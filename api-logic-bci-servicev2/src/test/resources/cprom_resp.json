{"data": {"resultType": "vector", "result": [{"value": [1669728127, "0.016279086"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "e3977d9fd988a5ccc06c1d89b6a75affdfcafd825b8bd193145b0922ad6d42a1", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "kubernetes_io_arch": "amd64", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cpu": "total", "container": "c01", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_cpu_usage_seconds_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/e3977d9fd988a5ccc06c1d89b6a75affdfcafd825b8bd193145b0922ad6d42a1", "image": "hub.baidubce.com/cce/busybox:latest", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor"}}, {"value": [1669728127, "0.304508245"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cpu": "total", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_cpu_usage_seconds_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************"}}, {"value": [1669728127, "0.033148784"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cpu": "total", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_cpu_usage_seconds_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "0.014232364"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "8c3661a65fe5b0df3b5645560e0bfd63f82b4b9366e0ae805c7244f4f8d8b3cd", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "kubernetes_io_arch": "amd64", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cpu": "total", "container": "c01", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_cpu_usage_seconds_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/8c3661a65fe5b0df3b5645560e0bfd63f82b4b9366e0ae805c7244f4f8d8b3cd", "image": "hub.baidubce.com/cce/busybox:latest", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor"}}, {"value": [1669728127, "1"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "8c3661a65fe5b0df3b5645560e0bfd63f82b4b9366e0ae805c7244f4f8d8b3cd", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "kubernetes_io_arch": "amd64", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cpu": "total", "container": "c01", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_memory_failures_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/8c3661a65fe5b0df3b5645560e0bfd63f82b4b9366e0ae805c7244f4f8d8b3cd", "image": "hub.baidubce.com/cce/busybox:latest", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor"}}, {"value": [1669728127, "0.347802024"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cpu": "total", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_cpu_usage_seconds_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************"}}, {"value": [1669728127, "0.031805991"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cpu": "total", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_cpu_usage_seconds_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "0"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "device": "/dev/vda", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_fs_reads_bytes_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************"}}, {"value": [1669728127, "0"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "device": "/dev/vda", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_fs_writes_bytes_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************"}}, {"value": [1669728127, "32768"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "e3977d9fd988a5ccc06c1d89b6a75affdfcafd825b8bd193145b0922ad6d42a1", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "container": "c01", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_memory_working_set_bytes", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/e3977d9fd988a5ccc06c1d89b6a75affdfcafd825b8bd193145b0922ad6d42a1", "image": "hub.baidubce.com/cce/busybox:latest", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "90112"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_memory_working_set_bytes", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************", "kubernetes_io_os": "linux"}}, {"value": [1669728127, "49152"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_memory_working_set_bytes", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************"}}, {"value": [1669728127, "32768"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "8c3661a65fe5b0df3b5645560e0bfd63f82b4b9366e0ae805c7244f4f8d8b3cd", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "container": "c01", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_memory_working_set_bytes", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/8c3661a65fe5b0df3b5645560e0bfd63f82b4b9366e0ae805c7244f4f8d8b3cd", "image": "hub.baidubce.com/cce/busybox:latest", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "184320"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_memory_working_set_bytes", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************", "kubernetes_io_os": "linux"}}, {"value": [1669728127, "45056"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_memory_working_set_bytes", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "job": "cadvisor", "kubernetes_io_arch": "amd64", "kubernetes_io_hostname": "************"}}, {"value": [1669728127, "656"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_receive_bytes_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "794564"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_receive_bytes_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "8"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_receive_packets_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "132"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_receive_packets_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "566"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_transmit_bytes_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "12091"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_transmit_bytes_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "7"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-cprnnozz", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "kubernetes_io_tor": "sw-LEJ9PWlqJvMirSaQbhuzLw", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_transmit_packets_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/pod51f4f9cf-5ed7-4ebe-ae44-9c662d75cde3/3624ce659f72fd71bfe3383a5ead1694f9b37cd027b81cf934bda26fcffe66ee", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}, {"value": [1669728127, "103"], "metric": {"topology_kubernetes_io_zone": "zoneC", "topology_kubernetes_io_region": "bd", "tenant_lock": "2e1be1eb99e946c3a543ec5a4eaa7d39", "region": "bd", "pod": "p-5y9bnjxa", "node_kubernetes_io_instance_type": "BCC", "node": "************", "namespace": "2e1be1eb99e946c3a543ec5a4eaa7d39", "name": "d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "kubernetes_io_tor": "sw-cAQEvler0nowarp22q8TA", "kubernetes_io_os": "linux", "kubernetes_io_hostname": "************", "feature_node_kubernetes_io_cpu_cpuid_FXSR": "true", "feature_node_kubernetes_io_cpu_cpuid_FMA3": "true", "feature_node_kubernetes_io_cpu_cpuid_CMPXCHG8": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VNNI": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512VL": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512F": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512DQ": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512CD": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX512BW": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX2": "true", "feature_node_kubernetes_io_cpu_cpuid_AVX": "true", "feature_node_kubernetes_io_cpu_cpuid_AESNI": "true", "feature_node_kubernetes_io_cpu_cpuid_ADX": "true", "failure_domain_beta_kubernetes_io_zone": "zoneC", "failure_domain_beta_kubernetes_io_region": "bd", "cluster_role": "node", "beta_kubernetes_io_os": "linux", "beta_kubernetes_io_instance_type": "BCC", "beta_kubernetes_io_instance_gpu": "false", "beta_kubernetes_io_arch": "amd64", "bci_node_state_machine": "runPod", "bci_node_name": "************", "agentID": "agent-6t2ltya86", "__name__": "container_network_transmit_packets_total", "buffer_node": "1", "cce_baidubce_com_baidu_cgpu_priority": "false", "cce_baidubce_com_gpu_share_device_plugin": "disable", "cce_baidubce_com_image_accelerate": "true", "cce_baidubce_com_image_accelerate_installer": "installer", "cce_baidubce_com_kubelet_dir": "2f7661722f6c69622f6b7562656c6574", "clusterID": "cce-q97ser83", "cluster_id": "cce-q97ser83", "feature_node_kubernetes_io_cpu_cpuid_FXSROPT": "true", "feature_node_kubernetes_io_cpu_cpuid_HYPERVISOR": "true", "feature_node_kubernetes_io_cpu_cpuid_IBPB": "true", "feature_node_kubernetes_io_cpu_cpuid_LAHF": "true", "feature_node_kubernetes_io_cpu_cpuid_MOVBE": "true", "feature_node_kubernetes_io_cpu_cpuid_MPX": "true", "feature_node_kubernetes_io_cpu_cpuid_OSXSAVE": "true", "feature_node_kubernetes_io_cpu_cpuid_SCE": "true", "feature_node_kubernetes_io_cpu_cpuid_X87": "true", "feature_node_kubernetes_io_cpu_cpuid_XSAVE": "true", "feature_node_kubernetes_io_cpu_cstate_enabled": "true", "feature_node_kubernetes_io_cpu_hardware_multithreading": "true", "feature_node_kubernetes_io_cpu_model_family": "6", "feature_node_kubernetes_io_cpu_model_id": "85", "feature_node_kubernetes_io_cpu_model_vendor_id": "Intel", "feature_node_kubernetes_io_kernel_config_NO_HZ": "true", "feature_node_kubernetes_io_kernel_config_NO_HZ_FULL": "true", "feature_node_kubernetes_io_kernel_version_full": "3.10.0-1160.76.1.el7.x86_64", "feature_node_kubernetes_io_kernel_version_major": "3", "feature_node_kubernetes_io_kernel_version_minor": "10", "feature_node_kubernetes_io_kernel_version_revision": "0", "feature_node_kubernetes_io_pci_0300_1013_present": "true", "feature_node_kubernetes_io_system_os_release_ID": "centos", "feature_node_kubernetes_io_system_os_release_VERSION_ID": "7", "feature_node_kubernetes_io_system_os_release_VERSION_ID_major": "7", "id": "/kubepods/burstable/podcee5b9fb-6424-49a5-9257-f7dcc0b23336/d422aa3fc9c0d5b8d446b7051fc074cf49bc048a2c25b3f10cd2bf4bf32b8c5a", "image": "registry.baidubce.com/cce-public/pause:3.1", "instance": "************", "instance_group_id": "cce-ig-0uxxdkd3", "interface": "eth0", "job": "cadvisor", "kubernetes_io_arch": "amd64"}}]}, "status": "success"}