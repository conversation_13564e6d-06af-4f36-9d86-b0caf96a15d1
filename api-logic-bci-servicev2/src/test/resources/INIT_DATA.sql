INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `gpu_type`, `gpu_count`, `product_type`, `eip_uuid`, `public_ip`, `cce_uuid`,
`internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtUI4D', 'ffcdfa25-6edc-4b7c-943d-d5225050086a', 'test-event', 'p-4WRtUI4D', 'running', 1, 1, 'KUNLUN-R200', 4.2, 'bidding', '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', 'ecgttrscxrzrryrtvsxz', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtfwef', 'ffcdfa25-6edc-4b7c-943d-d52250500few', 'test-event', 'p-4WRtfwef', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtfwef', 'ffcdfa25-6edc-4b7c-943d-d52250500123', 'test-event', 'p-4WRtfwef', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtfvevf', 'ffcdfa25-6edc-4b7c-943d-d522505012e1e', 'test-event', 'p-4WRtfvevf',  'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`,
`internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('pv2-4WRtUI4D', 'ffcdfav1-6edc-4b7c-943d-d5225050086a', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', 'ecgttrscxrzrryrtvsxz', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('pv2-4WRtfwef', 'ffcdfav1-6edc-4b7c-943d-d52250500few', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_container`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfav1-6edc-4b7c-943d-d5225050086a', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);

INSERT INTO `t_container_v2`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `gpu_type`, `gpu_count`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d5225050086a', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'KUNLUN-R200', 4.2, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);


INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-delete', 'ffcdfa25-6edc-4b7c-943d-d52250qqqqqq', 'test-event', 'p-delete', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');


INSERT INTO `t_container_v2`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d52250500123', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);

INSERT INTO `t_container_v2`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d522505012e1e', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);


INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-pending', 'ffcdfa25-6edc-4b7c-943d-d5fwwf', 'test-event', 'p-pending', 'Pending', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-3reeev3', 'ffcdfa25-6edc-4b7c-943d-4tgsfe', 'test-event', 'p-3reeev3', 'Pending', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');


INSERT INTO `t_container_v2`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d5225011111', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);

INSERT INTO `t_pod_charge_record_v2`(`id`, `msg_id`, `push_time`, `charge_time`, `last_charge_time`, `lock_id`, `lock_time`, `push_status`)
VALUES (96095, '2019-10-22-00-28', '2019-10-22 00:00:00', '2019-10-21 23:58:40', '2019-10-21 23:57:40', '', '1971-01-01 08:00:01', '');

INSERT INTO `t_cce_cluster`(`name`, `cce_id`, `cce_kube_config`,`description`)VALUES ('cce-cluster1','cce-cluster1-id',
'kubeconfig1','cce1');
INSERT INTO `t_cce_cluster`(`name`, `cce_id`, `cce_kube_config`,`description`)VALUES ('cce-cluster2','cce-cluster2-id',
'kubeconfig2','cce2');
INSERT INTO `t_cce_cluster`(`name`, `cce_id`, `cce_kube_config`,`description`)VALUES ('cce-cluster3','cce-cluster3-id',
'kubeconfig3','cce3');

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`, `name`, `client_token`, `status`, `internal_status`, `v_cpu`, `memory`, `gpu_type`, `gpu_count`, `product_type`, `eip_uuid`, `eip_id`, `public_ip`, `bandwidth_in_mbps`, `eip_status`, `cce_uuid`, `internal_ip`, `subnet_uuid`, `security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `flex_volume`, `pod_volumes`, `created_bls_tasks_id`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `enable_log`, `application`, `charge_source`, `node_name`, `bcc_instance_id`, `preempt_status`, `preempt_status_update_time`, `conditions`, `extra`, `cpu_type`, `resource_recycle_timestamp`, `resource_recycle_reason`, `resource_recycle_complete`, `eip_pay_method`, `delay_release_duration_minute`, `resource_version`, `eip_route_type`, `delay_release_succeeded`, `eip_actual_status`, `commit_deleted_time`, `zone_subnets`, `cpt1`, `is_tidal`, `internal_ipv6`)
VALUES ('p-beoisehkqq', '6ac50ddc-dd8a-41e2-8e05-54f6debb9e3b', 'test-autocase-gpu-16813520426627-1', 'p-beoisehkqq', 'deleted', 'synced', 8, 36, 'Nvidia A10 PCIE', 1, 'PostPay', '', '1', '', 0, 0, '', '*************', '5628b44e-efff-4728-8183-71d63f1e95d0', 'g-nuk8yx6p4mmt', 'OnFailure', 'd7a954a2ea32496f92177903fb5b4dd7', '[{\"labelKey\":\"test-case\",\"labelValue\":\"case\"}]', '', 'bb45087dee674fcaa21d75b53a35f7fc', 'z-tuC96YEA', '8919560f-b165-459d-8cfe-91b5775da8e0', '', '[]', '[]', '[]', '[]', '[{\"awsElasticBlockStore\":null,\"azureDisk\":null,\"azureFile\":null,\"cephfs\":null,\"cinder\":null,\"configMap\":null,\"csi\":null,\"downwardAPI\":null,\"emptyDir\":null,\"ephemeral\":null,\"fc\":null,\"flexVolume\":null,\"flocker\":null,\"gcePersistentDisk\":null,\"gitRepo\":null,\"glusterfs\":null,\"hostPath\":{\"path\":\"/home/<USER>/coredump/p-beoisehk\",\"type\":\"DirectoryOrCreate\"},\"iscsi\":null,\"name\":\"bci-internal-coredump\",\"nfs\":null,\"persistentVolumeClaim\":null,\"photonPersistentDisk\":null,\"portworxVolume\":null,\"projected\":null,\"quobyte\":null,\"rbd\":null,\"scaleIO\":null,\"secret\":null,\"storageos\":null,\"vsphereVolume\":null},{\"awsElasticBlockStore\":null,\"azureDisk\":null,\"azureFile\":null,\"cephfs\":null,\"cinder\":null,\"configMap\":null,\"csi\":null,\"downwardAPI\":null,\"emptyDir\":null,\"ephemeral\":null,\"fc\":null,\"flexVolume\":null,\"flocker\":null,\"gcePersistentDisk\":null,\"gitRepo\":null,\"glusterfs\":null,\"hostPath\":null,\"iscsi\":null,\"name\":\"default-token-76nj2\",\"nfs\":null,\"persistentVolumeClaim\":null,\"photonPersistentDisk\":null,\"portworxVolume\":null,\"projected\":null,\"quobyte\":null,\"rbd\":null,\"scaleIO\":null,\"secret\":{\"defaultMode\":420,\"items\":null,\"optional\":null,\"secretName\":\"default-token-76nj2\"},\"storageos\":null,\"vsphereVolume\":null}]', '', '2023-04-13 02:14:03', '2023-04-13 02:14:10', '2023-04-13 02:19:47', 1, 0, 'default', 'user', '**********', '', 'RUNNING', '2023-04-13 10:14:03', '[{\"lastTransitionTime\":\"2023-04-13T02:14:05Z\",\"status\":\"True\",\"type\":\"Initialized\"},{\"lastTransitionTime\":\"2023-04-13T02:14:07Z\",\"status\":\"True\",\"type\":\"Ready\"},{\"lastTransitionTime\":\"2023-04-13T02:14:07Z\",\"status\":\"True\",\"type\":\"ContainersReady\"},{\"lastTransitionTime\":\"2023-04-13T02:14:05Z\",\"status\":\"True\",\"type\":\"PodScheduled\"}]', '1', '', 0, '', 1, '1', 0, 120696282, '1', 0, '1', '2023-04-13 02:19:46', '', 1, '1', '');

INSERT INTO `t_pod_charge_status_v2`(`pod_uuid`, `previous_state`, `current_state`, `charge_state`, `resource_version`, `cpt1_sync_state`, `created_time`, `update_time`) VALUES ('6ac50ddc-dd8a-41e2-8e05-54f6debb9e3b', 'Pending', 'Running', 'charge', 0, 0, '2023-04-13 02:14:03', '2023-04-13 02:14:03');
INSERT INTO `t_pod_charge_status_v2`(`pod_uuid`, `previous_state`, `current_state`, `charge_state`, `resource_version`, `cpt1_sync_state`, `created_time`, `update_time`) VALUES ('6ac50ddc-dd8a-41e2-8e05-54f6debb9e3b', 'Running', 'deleted', 'noCharge', 0, 0, '2023-04-13 02:19:47', '2023-04-13 02:19:47');
INSERT INTO `t_cce_user_map`(`user_id`, `cce_ids`, `cpt1`)VALUES ('userid1','cce-cluster1-id,cce-cluster2-id',1);

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`, `name`, `client_token`, `status`, `internal_status`, `v_cpu`, `memory`, `gpu_type`, `gpu_count`, `product_type`, `eip_uuid`, `eip_id`, `public_ip`, `bandwidth_in_mbps`, `eip_status`, `cce_uuid`, `internal_ip`, `subnet_uuid`, `security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `flex_volume`, `pod_volumes`, `created_bls_tasks_id`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `enable_log`, `application`, `charge_source`, `node_name`, `bcc_instance_id`, `preempt_status`, `preempt_status_update_time`, `conditions`, `extra`, `cpu_type`, `resource_recycle_timestamp`, `resource_recycle_reason`, `resource_recycle_complete`, `eip_pay_method`, `delay_release_duration_minute`, `resource_version`, `eip_route_type`, `delay_release_succeeded`, `eip_actual_status`, `commit_deleted_time`, `zone_subnets`, `cpt1`, `is_tidal`, `internal_ipv6`)
VALUES ('p-beoisehkqq-1', '6ac50ddc-dd8a-41e2-8e05-54f6debb9e3b', 'test-autocase-gpu-16813520426627-1', 'p-beoisehkqq-1', 'Running', 'synced', 8, 36, 'Nvidia A10 PCIE', 1, 'PostPay', '', '1', '', 0, 0, '', '*************', '5628b44e-efff-4728-8183-71d63f1e95d0', 'g-nuk8yx6p4mmt', 'OnFailure', 'd7a954a2ea32496f92177903fb5b4dd7', '[{\"labelKey\":\"test-case\",\"labelValue\":\"case\"}]', '', 'userid1', 'z-tuC96YEA', '8919560f-b165-459d-8cfe-91b5775da8e0', '', '[]', '[]', '[]', '[]', '[{\"awsElasticBlockStore\":null,\"azureDisk\":null,\"azureFile\":null,\"cephfs\":null,\"cinder\":null,\"configMap\":null,\"csi\":null,\"downwardAPI\":null,\"emptyDir\":null,\"ephemeral\":null,\"fc\":null,\"flexVolume\":null,\"flocker\":null,\"gcePersistentDisk\":null,\"gitRepo\":null,\"glusterfs\":null,\"hostPath\":{\"path\":\"/home/<USER>/coredump/p-beoisehk\",\"type\":\"DirectoryOrCreate\"},\"iscsi\":null,\"name\":\"bci-internal-coredump\",\"nfs\":null,\"persistentVolumeClaim\":null,\"photonPersistentDisk\":null,\"portworxVolume\":null,\"projected\":null,\"quobyte\":null,\"rbd\":null,\"scaleIO\":null,\"secret\":null,\"storageos\":null,\"vsphereVolume\":null},{\"awsElasticBlockStore\":null,\"azureDisk\":null,\"azureFile\":null,\"cephfs\":null,\"cinder\":null,\"configMap\":null,\"csi\":null,\"downwardAPI\":null,\"emptyDir\":null,\"ephemeral\":null,\"fc\":null,\"flexVolume\":null,\"flocker\":null,\"gcePersistentDisk\":null,\"gitRepo\":null,\"glusterfs\":null,\"hostPath\":null,\"iscsi\":null,\"name\":\"default-token-76nj2\",\"nfs\":null,\"persistentVolumeClaim\":null,\"photonPersistentDisk\":null,\"portworxVolume\":null,\"projected\":null,\"quobyte\":null,\"rbd\":null,\"scaleIO\":null,\"secret\":{\"defaultMode\":420,\"items\":null,\"optional\":null,\"secretName\":\"default-token-76nj2\"},\"storageos\":null,\"vsphereVolume\":null}]', '', '2023-04-13 02:14:03', '2023-04-13 02:14:10', '2023-04-13 02:19:47', 0, 0, 'default', 'user', '**********', '', 'RUNNING', '2023-04-13 10:14:03', '[{\"lastTransitionTime\":\"2023-04-13T02:14:05Z\",\"status\":\"True\",\"type\":\"Initialized\"},{\"lastTransitionTime\":\"2023-04-13T02:14:07Z\",\"status\":\"True\",\"type\":\"Ready\"},{\"lastTransitionTime\":\"2023-04-13T02:14:07Z\",\"status\":\"True\",\"type\":\"ContainersReady\"},{\"lastTransitionTime\":\"2023-04-13T02:14:05Z\",\"status\":\"True\",\"type\":\"PodScheduled\"}]', '1', '', 0, '', 1, '1', 0, 120696282, '1', 0, '1', '2023-04-13 02:19:46', '', 1, '1', '');
INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`, `name`, `client_token`, `status`, `internal_status`, `v_cpu`, `memory`, `gpu_type`, `gpu_count`, `product_type`, `eip_uuid`, `eip_id`, `public_ip`, `bandwidth_in_mbps`, `eip_status`, `cce_uuid`, `internal_ip`, `subnet_uuid`, `security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `flex_volume`, `pod_volumes`, `created_bls_tasks_id`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `enable_log`, `application`, `charge_source`, `node_name`, `bcc_instance_id`, `preempt_status`, `preempt_status_update_time`, `conditions`, `extra`, `cpu_type`, `resource_recycle_timestamp`, `resource_recycle_reason`, `resource_recycle_complete`, `eip_pay_method`, `delay_release_duration_minute`, `resource_version`, `eip_route_type`, `delay_release_succeeded`, `eip_actual_status`, `commit_deleted_time`, `zone_subnets`, `cpt1`, `is_tidal`, `internal_ipv6`)
VALUES ('p-beoisehkqq-2', '7ac50ddc-dd8a-41e2-8e05-54f6debb9e3b', 'test-autocase-gpu-16813520426627-2', 'p-beoisehkqq-2', 'Succeeded', 'synced', 8, 36, 'Nvidia A10 PCIE', 1, 'PostPay', '', '1', '', 0, 0, '', '*************', '5628b44e-efff-4728-8183-71d63f1e95d0', 'g-nuk8yx6p4mmt', 'OnFailure', 'd7a954a2ea32496f92177903fb5b4dd7', '[{\"labelKey\":\"test-case\",\"labelValue\":\"case\"}]', '', 'userid1', 'z-tuC96YEA', '8919560f-b165-459d-8cfe-91b5775da8e0', '', '[]', '[]', '[]', '[]', '[{\"awsElasticBlockStore\":null,\"azureDisk\":null,\"azureFile\":null,\"cephfs\":null,\"cinder\":null,\"configMap\":null,\"csi\":null,\"downwardAPI\":null,\"emptyDir\":null,\"ephemeral\":null,\"fc\":null,\"flexVolume\":null,\"flocker\":null,\"gcePersistentDisk\":null,\"gitRepo\":null,\"glusterfs\":null,\"hostPath\":{\"path\":\"/home/<USER>/coredump/p-beoisehk\",\"type\":\"DirectoryOrCreate\"},\"iscsi\":null,\"name\":\"bci-internal-coredump\",\"nfs\":null,\"persistentVolumeClaim\":null,\"photonPersistentDisk\":null,\"portworxVolume\":null,\"projected\":null,\"quobyte\":null,\"rbd\":null,\"scaleIO\":null,\"secret\":null,\"storageos\":null,\"vsphereVolume\":null},{\"awsElasticBlockStore\":null,\"azureDisk\":null,\"azureFile\":null,\"cephfs\":null,\"cinder\":null,\"configMap\":null,\"csi\":null,\"downwardAPI\":null,\"emptyDir\":null,\"ephemeral\":null,\"fc\":null,\"flexVolume\":null,\"flocker\":null,\"gcePersistentDisk\":null,\"gitRepo\":null,\"glusterfs\":null,\"hostPath\":null,\"iscsi\":null,\"name\":\"default-token-76nj2\",\"nfs\":null,\"persistentVolumeClaim\":null,\"photonPersistentDisk\":null,\"portworxVolume\":null,\"projected\":null,\"quobyte\":null,\"rbd\":null,\"scaleIO\":null,\"secret\":{\"defaultMode\":420,\"items\":null,\"optional\":null,\"secretName\":\"default-token-76nj2\"},\"storageos\":null,\"vsphereVolume\":null}]', '', '2023-04-13 02:14:03', '2023-04-13 02:14:10', '2023-04-13 02:19:47', 0, 0, 'default', 'user', '**********', '', 'RUNNING', '2023-04-13 10:14:03', '[{\"lastTransitionTime\":\"2023-04-13T02:14:05Z\",\"status\":\"True\",\"type\":\"Initialized\"},{\"lastTransitionTime\":\"2023-04-13T02:14:07Z\",\"status\":\"True\",\"type\":\"Ready\"},{\"lastTransitionTime\":\"2023-04-13T02:14:07Z\",\"status\":\"True\",\"type\":\"ContainersReady\"},{\"lastTransitionTime\":\"2023-04-13T02:14:05Z\",\"status\":\"True\",\"type\":\"PodScheduled\"}]', '1', '', 0, '', 1, '1', 0, 120696282, '1', 0, '1', '2023-04-13 02:19:46', '', 1, '1', '');

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `client_token`, `status`, `v_cpu`, `memory`, `gpu_type`, `gpu_count`, `product_type`, `eip_uuid`, `public_ip`, `cce_uuid`,
`internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-test', 'test', 'test-event', 'p-test', 'deleted', 1, 1, 'KUNLUN-R200', 4.2, 'bidding', '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','order', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', 'ecgttrscxrzrryrtvsxz', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 1, '');
INSERT INTO `t_reserved_instance`(`user_id`, `account_id`, `name`, `resource_uuid`, `reserved_instance_id`, `reserved_instance_uuid`, `scope`, `physical_zone`, `order_id`, `reserve_resource`, `purchase_mode`, `status`, `deleted`, `reserved_spec`, `reserved_instance_count`, `reserved_time_unit`, `reserved_time_period`, `auto_renew`, `auto_renew_time_unit`, `auto_renew_time_period`, `effective_time`, `expire_time`, `created_time`, `updated_time`, `deleted_time`) VALUES ('bb45087dee674fcaa21d75b53a35f7fc', 'bb45087dee674fcaa21d75b53a35f7fc', 'test-for-gpu2', 'a', 'r-skjtxrtl', '', 'AZ', 'AZONE-gzns', '', 1, 'FullyPrepay', 'CREATE_FAILED', 0, 'bci.gna2.c18m78.1a10', 1, 'MONTH', 1, 1, 'MONTH', 1, '2023-04-10 00:00:00', '2023-05-10 00:00:00', '2023-04-17 17:57:42', '2023-04-19 19:50:42', NULL);
INSERT INTO `t_reserved_instance_spec`(`name`, `scope`, `physical_zone`, `deduct_instance_family`, `vcpu_num`, `mem_gb`, `gpu_name`, `gpu_num`) values ( 'bci.gna2.c8m36.1a10', 'AZ', 'AZONE-gzns', 'GPU-generic', 8, 36, 'Nvidia A10 PCIE', 1);
INSERT INTO `t_reserved_instance_spec`(`name`, `scope`, `physical_zone`, `deduct_instance_family`, `vcpu_num`, `mem_gb`, `gpu_name`, `gpu_num`) values ( 'bci.gna2.c18m78.1a10', 'AZ', 'AZONE-gzns', 'GPU-generic', 18, 78, 'Nvidia A10 PCIE', 1);
INSERT INTO `t_reserved_instance_spec`(`name`, `scope`, `physical_zone`, `deduct_instance_family`, `vcpu_num`, `mem_gb`, `gpu_name`, `gpu_num`) values ( 'bci.0.25c0.5g', '', '', 'CPU-generic', 0.25, 0.5, '', 0);
