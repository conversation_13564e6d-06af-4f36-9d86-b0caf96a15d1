server-host=qasandbox.bcetest.baidu.com
server.port=8784

bce.logical.region=bj
usersettings.default.region=bj
region.defaultRegion=bj
region.currentRegion=bj

swagger.start:false
swagger.app.docs: http://${server-host}:${server.port}

error_page.404:/bcc/404.html

#=================== login & access ===================#
login.url:https://login.bcetest.baidu.com/?redirect={referredUrl}
cookie.domain:.bcetest.baidu.com
login.cookie.md5.key:bcetest
login.urls.not.need.auth:/bcc/index.html;/bcc/asset/**;/bcc/dep/**;/bcc/esl.js;/swagger/**;/api-docs;/api-docs/**;/bcc/version.txt
login.urls.need.auth:/**
passport.appid=1240
passport.session.endpoint:http://***********:7801
uc.app.hypervisorId:285
uc.server:************:8880

iam.access.failed.jump.url:https://${server-host}/iam/access?redirect={referredUrl}
iam.console.username=console
iam.console.password=console
iam.access.paths:/**
iam.access.exclude.paths:/api-docs;/api-docs/**
iam.sts.rolename=BceServiceRole_bci
pod.sts.policy.id=9b468c32be2e405ba60380e2d2389ee0
pod.sts.service.account.id=c3dfeaf0a5234f4fa11f995bfc1005d7

iam.csrf.paths:/api/**
iam.csrf.exclude.paths:/api/bcc/instance/vnc;/api-docs;/api-docs/**;/api/bcc/order/confirm;/api/bcc/cds/order/confirm
iam.csrf.is.need.check.valid=true
iam.permission.is.need.check.valid=true
#======================================================#

#=================== logging config ===================#
logging.requestId_urlPattern:/bcc/;/api/*
logging.has_console_appender: true

logging.has_web_debug_appender: true
logging.web_debug_path: /bcc/debug
logging.web_debug.level: INFO

### logback rolling log, uncomment to open appender ###
logging.info_log_file_path:  ../log/info/bce-console-bcc.info.log
logging.error_log_file_path: ../log/error/bce-console-bcc.error.log
logging.warn_log_file_path:  ../log/warn/bce-console-bcc.warn.log
logging.debug_log_file_path: ../log/debug/bce-console-bcc.debug.log

### access log, uncomment to open appender ###
logging.access_debug_uri_prefix:/api
logging.access_log_file_path: ../log/access/bce-console-bcc.access.log
logging.access_debug_log_file_path: ../log/access_debug/bce-console-bcc.access_debug.log
#======================================================#

#=================== show floating ip ===================#
bcc.list.whetherShowFloatingIp=true
#======================================================#

#=================== sms ===================#
sms.bcc.rebuild.tpl.hypervisorId=smsTpl:0a09f75624f741fdb2b57ac620961afd
#======================================================#

#=================== finance ===================#
finance.account.type.available:100
finance.account.type.freeze:101
#======================================================#

#=================== bcc quota ===================#
bcc.prepay.quota:100
bcc.postpay.quota:20
cds.to.bcc.ratio:5
template.quota:20
snapshot.to.cds.ratio:8
#======================================================#

institution_id=1
secret_code=d21taUQ0N0tNZ0o2RllvbElObkV6VzFZdFhGd1dK
secret_code_institution_id_1=mUphTrGslz6JZtgAj0eqEKRBI9oxa4bi1M3wHcLF
secret_code_institution_id_2=d21taUQ0N0tNZ0o2RllvbElObkV6VzFZdFhGd1dK

finance.pay.url.prefix=http://cp01-testing-fengkong04.cp01.baidu.com:8118/process/pay_order_bce
finance.pay.pay_order.forward_url.prefix=https://${server-host}/billing/#/order/success
finance.pay.pay_order.feedback_url.prefix=http://nmg02-bce-test6.nmg02.baidu.com:8003/orderPurchaseCallback
finance.pay.recharge.forward_url=https://${server-host}/billing/#/account/history

#=================== trail config ===================#
trail.event.enable:true
trail.event.table_name:event
trail.event.agent_data_dir:../../../tidedb/agent/bce-console/

usersettings.region.enable:true
endpoint.default.regionName:bj

bae.apis_host:openbaev3.offlinea.bae.baidu.com
qualify.is.need.check.valid:true

#================ passport modify ================#
passport.passgateEndpoint:http://***********:8300/passgate
passport.app.username:bceplat
passport.app.password:bceplat

order.operate.enable:true

#================ global quota ================#
instance.postpay.gz.quota=2000
instance.postpay.bj.quota=1000

#============== new logical white user ============#
bce.logical.full.flow:true
bce.logical.white.enable:true
bce.logical.white.user:1a58d71202a14c4fbd077f9d027d5b1a,c254400af22947c3a3a1bea7ee1dee1e,\
  c3b1fab46b2a448a9d326931dbef06fa,c30d8e9de5204ca6a13c548d7f8acacf,\
  f1f62daf2ea34a7ba67b8aae9e398ef3,8a8fc7ed05eb45a7811c2c33e02b2587,36327b8052cc4a46afc0b13ba35907fc,\
  e91992a18cc74eb5859a97f95f363a30,5b785d8cb32c418e8a4a61cdb85e50a8

#================log monitor=================#
monitor.latencyRecorder.enable:true

database.isEmbedded:false
db.logical.bcc.url:***********************************************************************************
db.logical.bcc.username:root
db.logical.bcc.password:123456
#==================== database config begin ====================#
database.enable:false
database.mybatis.enable:true
database.logic.enable:true
#database.isEmbedded:false
database.logic.url:***********************************************************************************
database.logic.username:root
database.logic.password:123456
#==================== database config end ====================#

#==================== download image begin ====================#
download.image.address=registry.baidubce.com/wenzt-test/thirdparty-registry:latest
image.initContainer.download.enable=true
image.accelerate.enable=false
image.accelerate.gc.minute.interval=43200
image.accelerate.failed.record.gc.minute.interval=1440
image.accelerate.crd.apiVersion=image.bci.cloud.baidu.com/v1
image.accelerate.crd.kind=ImageCache

#==================== job pod recycle ====================#
job.pod.resource.recycle.minute.interval=2

spring.velocity.checkTemplateLocation=false

bce.asyncwork.enable:true

bci.eni.hexkey=WG8ugAEcU726q90B
bci.resource.account.id=b9ca12ddf6064c5eab7961d32496d564

#====================iam authorization config====================#
bce.logical.iamauthorization.enabled=true

#==================== monitor metric config ===============================#
bce.logical.cprom.metrics=container_cpu_cfs_periods_total,container_cpu_cfs_throttled_periods_total,container_cpu_cfs_throttled_seconds_total,container_cpu_load_average_10s,container_cpu_system_seconds_total,container_cpu_usage_seconds_total,container_cpu_user_seconds_total,container_file_descriptors,container_fs_inodes_free,container_fs_inodes_total,container_fs_io_current,container_fs_io_time_seconds_total,container_fs_io_time_weighted_seconds_total,container_fs_limit_bytes,container_fs_read_seconds_total,container_fs_reads_bytes_total,container_fs_reads_merged_total,container_fs_reads_total,container_fs_sector_reads_total,container_fs_sector_writes_total,container_fs_usage_bytes,container_fs_write_seconds_total,container_fs_writes_bytes_total,container_fs_writes_merged_total,container_fs_writes_total,container_last_seen,container_memory_cache,container_memory_failcnt,container_memory_failures_total,container_memory_mapped_file,container_memory_max_usage_bytes,container_memory_rss,container_memory_swap,container_memory_usage_bytes,container_memory_working_set_bytes,container_network_receive_bytes_total,container_network_receive_errors_total,container_network_receive_packets_dropped_total,container_network_receive_packets_total,container_network_transmit_bytes_total,container_network_transmit_errors_total,container_network_transmit_packets_dropped_total,container_network_transmit_packets_total,container_processes,container_scrape_error,container_sockets,container_spec_cpu_period,container_spec_cpu_quota,container_spec_cpu_shares,container_spec_memory_limit_bytes,container_spec_memory_reservation_limit_bytes,container_spec_memory_swap_limit_bytes,container_start_time_seconds,container_tasks_state,container_threads,container_threads_max,container_ulimits_soft
bce.logical.cprom.host=https://cprom.bd.baidubce.com
bce.logical.cprom.uri=select/prometheus/api/v1/query
bce.logical.cprom.instanceid=cprom-xxxx
bce.logical.cprom.token=xxxx
bce.logical.cprom.maxPodsPerRequest=100

# TO DO,智星云临时支持,支持手动管理镜像缓存后,需去除本部分代码
#============================image not ready then create pod fail white list========================#
image.accelerate.checkImageAccelerateReadyOrNotForTheseAccounts=b0cf8fadf1cb4a1191c16e94303ed759
image.accelerate.userDefineMaxImcCnt={}
#=================== bci cce vpc info ========================#
bci.cce.vpc.cidr=**********/16
bci.cce.clusterIP.cidr=***********/16

#=================== bci hostpath whitelist ========================#
bci.hostpath.whitelist=/var/log/pods,/noah,/home/<USER>/opt/compiler,/usr/lib64,/etc/localtime,/etc/nsswitch.conf,/var/run/kubelet-proxy
