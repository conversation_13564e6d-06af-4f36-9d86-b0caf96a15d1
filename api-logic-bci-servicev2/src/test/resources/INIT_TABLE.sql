CREATE TABLE `t_pod_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_id` varchar(255) NOT NULL,
  `pod_uuid` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `status` varchar(40) NOT NULL,
  `sub_status` varchar(64) NOT NULL DEFAULT '' COMMENT 'sub status',
  `internal_status` varchar(40),
  `v_cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `gpu_type` varchar(255) DEFAULT '',
  `gpu_count` float DEFAULT '0.0',
  `product_type` varchar(255) DEFAULT 'Postpay',
  `eip_uuid` varchar(255) NOT NULL,
  `eip_id` varchar(40) NOT NULL DEFAULT '',
  `public_ip` varchar(100) NOT NULL,
  `bandwidth_in_mbps` int(11) NOT NULL DEFAULT 0,
  `eip_route_type` varchar(40) NOT NULL DEFAULT '',
  `eip_pay_method` varchar(40) NOT NULL DEFAULT '',
  `eip_actual_status` varchar(40) NOT NULL DEFAULT '',
  `eip_status` int(11) NOT NULL DEFAULT 0,
  `cce_uuid` varchar(255) NOT NULL,
  `internal_ip` varchar(100) NOT NULL,
  `subnet_uuid` varchar(100) NOT NULL,
  `security_group_uuid` varchar(1000) NOT NULL,
  `restart_policy` varchar(40) NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `tags` text,
  `description` varchar(1000) NOT NULL,
  `user_id` varchar(255) NOT NULL,
  `zone_id` varchar(255) NOT NULL,
  `logical_zone` varchar(255) NOT NULL DEFAULT '' COMMENT '用户逻辑可用区',
  `resource_uuid` varchar(1000) NOT NULL,
  `task_status` varchar(40) NOT NULL,
  `nfs` text NOT NULL,
  `empty_dir` text NOT NULL,
  `config_file` text NOT NULL,
  `flex_volume` text,
  `pfs` text,
  `bos` text,
  `host_path` text,
  `pod_volumes` varchar(10000) NOT NULL,
  `zone_subnets` text,
  `created_bls_tasks_id` varchar(1024) NOT NULL DEFAULT '' COMMENT '创建的BLS任务id',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(11) NOT NULL DEFAULT 0,
  `enable_log` int(11) NOT NULL DEFAULT '0',
  `application` varchar(255) NOT NULL,
  `charge_source` varchar(32) NOT NULL DEFAULT 'user',
  `charge_account_id` varchar(255) NOT NULL DEFAULT '',
  `conditions` text NOT NULL DEFAULT '' COMMENT 'pod conditions 字段',
  `node_name` varchar(40) DEFAULT '',
  `bcc_instance_id` varchar(40) DEFAULT '',
  `preempt_status` varchar(40) DEFAULT '',
  `preempt_status_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '抢占状态变更时间',
  `extra` text COMMENT '额外信息',
  `cpu_type` varchar(255) DEFAULT '',
  `resource_recycle_timestamp` bigint(20) NOT NULL DEFAULT 0 COMMENT '开始资源回收时间',
  `resource_recycle_reason` varchar(40) DEFAULT '' COMMENT '资源回收原因',
  `resource_recycle_complete` int(11) NOT NULL DEFAULT 0 COMMENT '资源回收完成状态',
  `resource_recycle_complete_timestamp` bigint(20) NOT NULL DEFAULT 0 COMMENT '完成资源回收时间',
  `delay_release_duration_minute` int(11) NOT NULL DEFAULT 0,
  `delay_release_succeeded` boolean NOT NULL DEFAULT 0,
  `commit_deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '用户提交删除Pod的时间',
  `resource_version` bigint(20) NOT NULL DEFAULT 0 COMMENT 'k8s Pod资源版本号',
  `bci_resource_version` bigint(20) NOT NULL DEFAULT 0 COMMENT 'bci Pod资源版本号',
  `is_tidal` boolean NOT NULL DEFAULT 0,
  `cpt1` tinyint(1) DEFAULT 0 COMMENT 'CPT1计费',
  `ds_containers_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '每次有ds容器发生变化，ds容器版本加一',
  `ds_containers_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '记录ds容器数量',
  `ds_containers_synced_to_k8s` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'ds容器是否已经同步到K8S',
  `client_token` varchar(255) NOT NULL DEFAULT '' COMMENT 'create pod client token',
  `internal_ipv6` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT='pod信息表';

CREATE TABLE `t_container_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_uuid` varchar(255) NOT NULL DEFAULT '',
  `name` varchar(40) NOT NULL DEFAULT '',
  `container_type` varchar(40) NOT NULL DEFAULT 'workload' COMMENT '容器类型: init、workload、ds-workload',
  `container_uuid` varchar(255) DEFAULT '' COMMENT '容器底层id',
  `image_name` varchar(40) NOT NULL DEFAULT '',
  `image_version` varchar(40) NOT NULL DEFAULT '',
  `image_address` varchar(255) NOT NULL DEFAULT '',
  `image_id` varchar(255) DEFAULT '',
  `cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `gpu_type` varchar(255) DEFAULT '',
  `gpu_count` float DEFAULT '0.0',
  `working_dir` varchar(255) NOT NULL DEFAULT '',
  `image_pull_policy` varchar(255) NOT NULL DEFAULT '',
  `commands` varchar(255) NOT NULL DEFAULT '',
  `args` text NOT NULL DEFAULT '',
  `ports` text NOT NULL DEFAULT '',
  `volume_mounts` text NOT NULL DEFAULT '',
  `envs` text NOT NULL DEFAULT '',
  `liveness_probe` text NOT NULL DEFAULT '' COMMENT '容器是否存活探针',
  `readiness_probe` text NOT NULL DEFAULT '' COMMENT '容器是否就绪探针',
  `startup_probe` text NOT NULL DEFAULT '' COMMENT '容器是否启动成功探针',
  `extend_field` text NOT NULL DEFAULT '' COMMENT '扩展资源字段',
  `user_id` varchar(255) NOT NULL DEFAULT '',
  `previous_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器上一个状态',
  `current_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器当前状态',
  `restart_count` int(11) NOT NULL DEFAULT '0' COMMENT '容器重启次数',
  `ready` varchar(25) NOT NULL DEFAULT '' COMMENT '容器状态',
  `started` varchar(25) NOT NULL DEFAULT '' COMMENT '容器是否启动成功',
  `lifecycle` text COMMENT '容器生命周期',
  `stdin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否打开标准输入流',
  `stdin_once` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否单次开启标准输入流',
  `tty` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启终端',
  `extra` text NOT NULL DEFAULT '' COMMENT '额外信息',
  `security_context` text COMMENT '容器安全上下文',
  `ds_container_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前ds容器的版本号',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) COMMENT='container信息表';

CREATE TABLE `t_zone_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键（自然主键）',
  `zone_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户zone的uuid',
  `account_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户id',
  `logical_zone` varchar(32) NOT NULL DEFAULT '' COMMENT '逻辑可用区',
  `physical_zone` varchar(32) NOT NULL DEFAULT '' COMMENT '物理可用区',
  `subnet_id` varchar(64) NOT NULL DEFAULT '' COMMENT '默认子网的ID',
  `type` varchar(32) NOT NULL DEFAULT 'available' COMMENT '映射类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `zone_id` (`zone_id`),
  UNIQUE KEY `user_logical` (`account_id`,`logical_zone`)
);

CREATE TABLE `t_pod_charge_status_v2` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
`pod_uuid` varchar(255) NOT NULL,
`previous_state` varchar(40) NOT NULL,
`current_state` varchar(40) NOT NULL,
`charge_state` varchar(40) NOT NULL,
`resource_version` bigint(20) NOT NULL COMMENT '对应k8s的resource version',
`cpt1_sync_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'Cpt1同步状态，0非cpt1，1同步未完成，2同步完成',
`created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`),
UNIQUE KEY `pod_uuid` (`pod_uuid`,`charge_state`,`created_time`)
);

CREATE TABLE `t_pod_charge_record_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `msg_id` varchar(255) NOT NULL,
  `push_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '推送时间',
  `charge_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '计费截止时间',
  `last_charge_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01',
  `lock_id` varchar(255) NOT NULL,
  `lock_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '加锁时间',
  `push_status` varchar(40) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `msg_id` (`msg_id`)
);

CREATE TABLE `t_cce_cluster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) NOT NULL,
  `cce_id` varchar(255) NOT NULL,
  `cce_kube_config` text NOT NULL,
  `is_default` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否为默认集群',
  `description` varchar(255),
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `cce_id` (`cce_id`)
);

CREATE TABLE `t_cce_user_map` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` varchar(255) NOT NULL,
  `cce_ids` varchar(2048) NOT NULL,
  `bls_user_token` varchar(64) NOT NULL DEFAULT '' COMMENT 'BLS收集器用户token',
  `cpt1` tinyint(1) DEFAULT 0 COMMENT 'CPT1计费',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
);

CREATE TABLE `image_accelerate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `images` text NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `description` varchar(1000) NOT NULL,
  `account_id` varchar(255) NOT NULL,
  `zone_id` varchar(255) NOT NULL,
  `logical_zone` varchar(255) NOT NULL,
  `physical_zone` varchar(255) NOT NULL,
  `enable_eni` int(11) NOT NULL DEFAULT '0',
  `need_eip` int(11) NOT NULL DEFAULT '0',
  `user_id` varchar(255) NOT NULL DEFAULT '',
  `subnet_id` varchar(255) NOT NULL DEFAULT '',
  `security_group_ids` varchar(255) NOT NULL DEFAULT '',
  `vpc_cidr` varchar(255) NOT NULL DEFAULT '',
  `zone_subnets` text,
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01',
  `deleted` int(11) NOT NULL DEFAULT '0',
  `image_secrets` varchar(255) NOT NULL,
  `used_num` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ;

CREATE TABLE `t_webshell_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(255) NOT NULL,
  `cce_id` varchar(255) NOT NULL,
  `pod_id` varchar(255) NOT NULL,
  `container_name` varchar(255) NOT NULL,
  `ws_url` text NOT NULL,
  `token` varchar(255) NOT NULL,
  `version` int(20) NOT NULL DEFAULT '0',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01',
  `deleted` int(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ;

CREATE TABLE `t_pod` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_id` varchar(255) NOT NULL,
  `pod_uuid` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `status` varchar(40) NOT NULL,
  `v_cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `eip_uuid` varchar(255) NOT NULL,
  `public_ip` varchar(100) NOT NULL,
  `cce_uuid` varchar(255) NOT NULL,
  `internal_ip` varchar(100) NOT NULL,
  `subnet_uuid` varchar(100) NOT NULL,
  `security_group_uuid` varchar(1000) NOT NULL,
  `restart_policy` varchar(40) NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `tags` varchar(1000) NOT NULL,
  `description` varchar(1000) NOT NULL,
  `user_id` varchar(255) NOT NULL,
  `zone_id` varchar(255) NOT NULL,
  `resource_uuid` varchar(1000) NOT NULL,
  `task_status` varchar(40) NOT NULL,
  `nfs` text NOT NULL,
  `empty_dir` text NOT NULL,
  `config_file` text NOT NULL,
  `pfs` text,
  `bos` text,
  `host_path` text,
  `pod_volumes` varchar(1000) NOT NULL,
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(11) NOT NULL,
  `enable_log` int(11) NOT NULL,
  `application` varchar(255) NOT NULL,
  `charge_source` varchar(32) NOT NULL DEFAULT 'user',
  `charge_account_id` varchar(255) NOT NULL DEFAULT '',
  `internal_ipv6` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT='pod信息表';

CREATE TABLE `t_container` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_uuid` varchar(255) NOT NULL DEFAULT '',
  `name` varchar(40) NOT NULL DEFAULT '',
  `container_uuid` varchar(255) DEFAULT '' COMMENT '容器底层id',
  `image_name` varchar(40) NOT NULL DEFAULT '',
  `image_version` varchar(40) NOT NULL DEFAULT '',
  `image_address` varchar(40) NOT NULL DEFAULT '',
  `cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `working_dir` varchar(255) NOT NULL DEFAULT '',
  `image_pull_policy` varchar(255) NOT NULL DEFAULT '',
  `commands` varchar(255) NOT NULL DEFAULT '',
  `args` varchar(255) NOT NULL DEFAULT '',
  `ports` text NOT NULL DEFAULT '',
  `volume_mounts` text NOT NULL DEFAULT '',
  `envs` text NOT NULL DEFAULT '',
  `user_id` varchar(255) NOT NULL DEFAULT '',
  `previous_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器上一个状态',
  `current_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器当前状态',
  `restart_count` int(11) NOT NULL DEFAULT '0' COMMENT '容器重启次数',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) COMMENT='container信息表';

CREATE TABLE `t_ops_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` varchar(255) NOT NULL COMMENT 'accountID',
  `cce_id` varchar(255) NOT NULL COMMENT 'cce 集群id,对应t_cce_cluster cce_id',
  `pod_id` varchar(255) NOT NULL COMMENT 'pod 表对应的pod_id 字段',
  `uuid` varchar(255) NOT NULL COMMENT '一次请求对应的uuid',
  `ops_type` varchar(20) NOT NULL COMMENT 'opsType,取值 coredump、tcpdump',
  `ops_value` text NOT NULL COMMENT 'opsValue',
  `storage_type` varchar(20) NOT NULL COMMENT '存储类型，bos 或 oss',
  `storage_contents` text NOT NULL COMMENT 'jsonlist 存储，共享存储url&失败原因&ops_status ',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(20) NOT NULL DEFAULT '0',
  `completed` int(20) NOT NULL DEFAULT '0' COMMENT '0 未完成 1 已完成',
  PRIMARY KEY (`id`),
  INDEX `podid` (`pod_id`)
);

CREATE TABLE `t_reserved_instance` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` CHAR(32) NOT NULL COMMENT '用户ID',
    `account_id` CHAR(32) NOT NULL COMMENT '用户账户ID',
    `name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '预留实例券名称',
    `resource_uuid` CHAR(32) DEFAULT '' COMMENT 'billing中资源ID',
    `reserved_instance_id` CHAR(32) NOT NULL DEFAULT '' COMMENT '预留实例券短ID，bci生成',
    `reserved_instance_uuid` CHAR(64) NOT NULL DEFAULT '' COMMENT '预留实例券长ID',
    `scope` CHAR(8) NOT NULL DEFAULT 'AZ' COMMENT '预留实例券的可用范围',
    `physical_zone` CHAR(32) NOT NULL DEFAULT '' COMMENT '物理可用区，仅在scope为AZ时有效',
    `logical_zone` CHAR(32) NOT NULL DEFAULT '' COMMENT '逻辑可用区，仅在scope为AZ时有效',
    `order_id` CHAR(32) NOT NULL DEFAULT '' COMMENT '预留实例券订单ID',
    `reserve_resource` BOOL NOT NULL DEFAULT 1 COMMENT '预留类型，有预留为1，无预留为0',
    `purchase_mode` CHAR(16) NOT NULL COMMENT '付费模式，可选范围FullyPrepay, PartPrepay, Postpay',
    `status` CHAR(16) NOT NULL DEFAULT '' COMMENT '预留实例券状态',
    `deleted` BOOL NOT NULL DEFAULT 0,
    `reserved_spec` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '预留实例规格',
    `reserved_instance_count` INT NOT NULL DEFAULT 0 COMMENT '预留实例数量',
    `reserved_time_unit` CHAR(8) NOT NULL COMMENT '预留时长单位',
    `reserved_time_period` INT NOT NULL COMMENT '预留时长量',
    `auto_renew` BOOL NOT NULL DEFAULT 0 COMMENT '自动续费，1为开启，0为关闭',
    `auto_renew_time_unit` CHAR(8) NOT NULL DEFAULT '' COMMENT '自动续费时长单位',
    `auto_renew_time_period` INT NOT NULL DEFAULT 0 COMMENT '自动续费时长量',
    `effective_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    `expire_time` DATETIME NOT NULL COMMENT '过期时间',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_time` TIMESTAMP,
    `deleted_time` DATETIME,
    PRIMARY KEY (`id`)
) COMMENT='预留实例券表';

CREATE TABLE `t_reserved_instance_spec` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `scope` CHAR(16) NOT NULL COMMENT '预留实例券类型，地域级REGION或可用区级AZ',
    `physical_zone` CHAR(32) NOT NULL DEFAULT '' COMMENT '物理可用区，e.g. Azone-gzns',
    `name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '预留实例券规格名称',
    `deduct_instance_family` CHAR(32) NOT NULL COMMENT '抵扣实例族，可选 CPU-generic、GPU-generic',
    `vcpu_num` DOUBLE NOT NULL DEFAULT 0 COMMENT 'vcpu数量',
    `mem_gb` DOUBLE NOT NULL DEFAULT 0 COMMENT '内存GB',
    `gpu_name` VARCHAR(255) DEFAULT '' COMMENT 'GPU型号',
    `gpu_num` INT DEFAULT 0 COMMENT 'GPU卡数',
    `deleted` BOOL NOT NULL DEFAULT 0,
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_time` TIMESTAMP,
    `deleted_time` DATETIME,
    PRIMARY KEY (`id`)
) COMMENT='预留实例券规格表';

CREATE TABLE `t_pod_extra_v2` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT ,
    `pod_id` varchar(255) NOT NULL DEFAULT '' ,
    `user_id` varchar(255) NOT NULL DEFAULT '' ,
    `order_id` varchar(255) NOT NULL DEFAULT '' ,
    `resource_uuid` varchar(1000) NOT NULL DEFAULT '' ,
    `order_extra` longtext NOT NULL ,
    `deleted` tinyint(1) NOT NULL DEFAULT '0' ,
    `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' ,
    PRIMARY KEY (`id`)
) COMMENT='pod扩展信息表';

CREATE ALIAS IF NOT EXISTS UTC_TIMESTAMP FOR "com.baidu.bce.logic.bci.servicev2.util.FunctionsMySQL.utcTimestamp";

