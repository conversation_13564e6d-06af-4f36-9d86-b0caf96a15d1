{"items": [{"config": {"productType": "postpay", "region": "bj", "restartPolicy": "Always", "vpcId": "ed82e80f-8e2a-40e3-804c-96d5eb040a53", "logicalZone": "zoneC", "securityGroupId": "g-2ya3sszj7km2", "subnetId": "sub_1", "name": "param", "vpcUuid": "ed82e80f-8e2a-40e3-804c-96d5eb040a53", "volumes": {"nfs": [{"name": "nfs", "server": "cfs.baidu.com", "path": "/data/container", "readOnly": true}], "emptyDir": [{"name": "emptydir"}], "configFile": [{"name": "config", "configFiles": [{"path": "pod/config", "file": "在平台组Java架构体系中, bce-plat-web-framework处于最基础位置"}]}]}, "securityContext": {"runAsUser": 1000, "runAsGroup": 1000, "runAsNonRoot": true, "sysctls": [{"name": "key", "value": "value"}]}, "containers": [{"name": "container01", "cpu": 0.25, "memory": 0.5, "imageName": "busybox", "imageAddress": "hub.baidubce.com/liqilong/busybox", "imageVersion": "musl", "envs": [{"key": "java_home", "value": "/usr/local/java"}], "ports": [{"protocol": "TCP", "port": "8090"}], "securityContext": {"runAsUser": 1000, "runAsGroup": 1000, "runAsNonRoot": true}, "workingDir": "working", "commands": ["sleep"], "args": ["200"], "volumeMounts": [{"readOnly": false, "name": "nfs", "mountPath": "/usr/local/share"}, {"readOnly": false, "name": "emptydir", "mountPath": "/usr/local/share2"}, {"readOnly": false, "name": "config", "mountPath": "/usr/local/share3"}]}], "imageRegistrySecret": [{"server": "http://hub.baidubce.com/liqilong/", "userName": "", "password": ""}], "tags": [{"tagKey": "key", "tagValue": "tag"}], "serviceType": "BCI"}, "paymentMethod": []}], "paymentMethod": []}