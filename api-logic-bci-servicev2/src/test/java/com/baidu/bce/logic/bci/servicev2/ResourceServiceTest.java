package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.sql.SQLException;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.ACCOUNT_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_UUID;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ResourceServiceTest {

    @Autowired
    private LogicalResourceServiceV2 resourceService;

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
    }

    @Test
    public void queryResourceTest() {
        Resource resource = resourceService.queryResource(ServiceType.BCC, POD_UUID);
        Assert.assertNotNull(resource);
    }

    @Test
    public void deleteResourceByNameV2Test() {
        resourceService.deleteResourceByName(ACCOUNT_ID, POD_UUID, PodConstants.SERVICE_TYPE);
    }

    @Test
    public void deleteResourceByNameTest() {
        resourceService.deleteResourceByName(ACCOUNT_ID, POD_UUID, PodConstants.SERVICE_TYPE);
    }

    @Test
    public void getResourceTest() {
        Resource resource = resourceService.getResource(ACCOUNT_ID, POD_UUID, PodConstants.SERVICE_TYPE);
        Assert.assertNotNull(resource);
    }

    @Test(expected = PodExceptions.ResourceNotExistException.class)
    public void getResourceNotExistTest() {
        Resource resource = resourceService.getResource(ACCOUNT_ID, "name", PodConstants.SERVICE_TYPE);
        Assert.assertNotNull(resource);
    }
}
