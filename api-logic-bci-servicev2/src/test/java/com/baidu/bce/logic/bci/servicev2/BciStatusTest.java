package com.baidu.bce.logic.bci.servicev2;


import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.billing.auditing.sdk.domain.ResourceStatus;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class BciStatusTest {
    @Before
    public void setUp() throws IllegalAccessException {        
    }

    @Test
    public void testConvertToBillingStatus() {
        ResourceStatus status;
        status = BciStatus.convertToBillingStatus(BciStatus.RUNNING.getStatus());
        Assert.assertEquals(status, ResourceStatus.RUNNING);

        status = BciStatus.convertToBillingStatus(BciStatus.FAILED.getStatus());
        Assert.assertEquals(status, ResourceStatus.DESTROYED);

        status = BciStatus.convertToBillingStatus(BciStatus.PENDING.getStatus());
        Assert.assertEquals(status, ResourceStatus.RUNNING);

        status = BciStatus.convertToBillingStatus(BciStatus.DELETED.getStatus());
        Assert.assertEquals(status, ResourceStatus.DESTROYED);

        status = BciStatus.convertToBillingStatus(BciStatus.EXPIRED.getStatus());
        Assert.assertEquals(status, ResourceStatus.DESTROYED);

        status = BciStatus.convertToBillingStatus(BciStatus.UNKNOWN.getStatus());
        Assert.assertEquals(status, ResourceStatus.RUNNING);
    }
}
