package com.baidu.bce.logic.bci.servicev2.k8s;

import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class K8sServiceTest {

    @Autowired
    private K8sService k8sService;
    @Test
    public void tryGetPodTest () {
        String nameSpace = "userid1";
        String podName = "p-uob3l2xj";
        V1Pod pod = k8sService.tryGetPod(nameSpace, podName);
        Assert.assertNull(pod);
    }

    @Test
    public void testCreateConfigMap() throws Exception {
        V1ConfigMap configMap = new V1ConfigMap();
        configMap.setMetadata(new V1ObjectMeta().namespace("default"));
        k8sService.createConfigMap(configMap);
    }
}