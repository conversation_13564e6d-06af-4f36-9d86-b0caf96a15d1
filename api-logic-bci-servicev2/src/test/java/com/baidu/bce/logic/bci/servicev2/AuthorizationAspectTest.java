package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationAspect;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationException;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.mock.ControllerMock;
import com.baidu.bce.logic.bci.servicev2.model.LeakagePodDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.BCMPodDetailRequest;
import com.baidu.bce.logic.bci.servicev2.model.DeletePod;
import com.baidu.bce.logic.bcc.sdk.model.common.IDListRequest;
import com.baidu.bce.logic.bci.daov2.common.model.WebShell;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.mock.web.MockHttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class AuthorizationAspectTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthorizationAspectTest.class);

    @Autowired
    private ControllerMock controller;

    @Autowired
    private AuthorizationAspect aspect;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogicUserService.class);
        PowerMockito.when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
        PowerMockito.when(LogicUserService.getUserId()).thenReturn("zhangsan");

        Token token = new Token();
        token.setId("token-id");
        IamLogicService mockIamLogicService = Mockito.mock(IamLogicService.class);
        aspect.setIamLogicService(mockIamLogicService);
        Mockito.when(mockIamLogicService.getConsoleToken()).thenReturn(token);

        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("x-bce-security-token", "xxx");
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
        controller.pods = new HashMap<String, String>();
        controller.pods.put("ffcdfa25-6edc-4b7c-943d-d5225050086a", "pod-1");
        controller.pods.put("ffcdfa25-6edc-4b7c-943d-d52250500few", "pod-2");
    }

    private List<BatchVerifyResults.Result> getVerifyResult(boolean allow) {
        VerifyResult verifyResult = new VerifyResult();
        verifyResult.setEffect(AuthorizationConstant.DENY_PERMISSION);
        if (allow) {
            verifyResult.setEffect(AuthorizationConstant.ALLOW_PERMISSION);
        }
        List<VerifyResult> verifyResults = new ArrayList<VerifyResult>();
        verifyResults.add(verifyResult);
        BatchVerifyResults.Result result = new BatchVerifyResults.Result();
        result.setResult(verifyResults);
        List<BatchVerifyResults.Result> results = new ArrayList<BatchVerifyResults.Result>();
        results.add(result);
        return results;
    }

    @Test
    public void enableTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();
        aspect.setEnable(false);

        // 没有list权限，不会抛异常
        iamClient.result.setVerifyResults(getVerifyResult(false));
        try {
            controller.list();
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run fail, exception is " + e.toString());
            Assert.fail();
        }
        aspect.setEnable(true);
    }

    @Test
    public void listTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();

        // 没有list权限
        iamClient.result.setVerifyResults(getVerifyResult(false));
        try {
            controller.list();
            Assert.fail();
        }
        catch (AuthorizationException e) {
            // 符合预期
            LOGGER.info("Test run success, exception is " + e.toString());
        }

        // 有list权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.list();
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }
    }

    @Test
    public void batchDeleteTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();
        PodBatchDeleteRequest req = new PodBatchDeleteRequest();
        DeletePod pod = new DeletePod();
        pod.setPodId("ffcdfa25-6edc-4b7c-943d-d5225050086a");
        List<DeletePod> pods = new ArrayList<DeletePod>();
        pods.add(pod);
        req.setDeletePods(pods);

        // 有 delete 权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.batchDelete(req);
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }
    }

    @Test
    public void deleteLeakageTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();
        LeakagePodDeleteRequest req = new LeakagePodDeleteRequest();
        req.setPodId("ffcdfa25-6edc-4b7c-943d-d5225050086a");

        // 有 delete 权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.deleteLeakage(req);
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }
    }

    @Test
    public void getBCMPodTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();
        BCMPodDetailRequest req = new BCMPodDetailRequest();
        req.setId("ffcdfa25-6edc-4b7c-943d-d5225050086a");

        // 有 get 权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.getBcmPod(req);
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }
    }

    @Test
    public void webshellTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();
        WebShell shell = new WebShell();
        shell.setPodId("ffcdfa25-6edc-4b7c-943d-d5225050086a");

        // 没有 webshell 权限
        iamClient.result.setVerifyResults(getVerifyResult(false));
        try {
            controller.webshell(shell, "updated-pod-1");
            Assert.fail();
        }
        catch (AuthorizationException e) {
            // 符合预期
            LOGGER.info("Test run success, exception is " + e.toString());
        }
        Assert.assertEquals("pod-1", controller.pods.get("ffcdfa25-6edc-4b7c-943d-d5225050086a"));

        // 有 update 权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.webshell(shell, "updated-pod-1");
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }
    }

    @Test
    public void updateTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();

        // 没有 update 权限
        iamClient.result.setVerifyResults(getVerifyResult(false));
        try {
            controller.update("ffcdfa25-6edc-4b7c-943d-d5225050086a", "updated-pod-1");
            Assert.fail();
        }
        catch (AuthorizationException e) {
            // 符合预期
            LOGGER.info("Test run success, exception is " + e.toString());
        }
        Assert.assertEquals("pod-1", controller.pods.get("ffcdfa25-6edc-4b7c-943d-d5225050086a"));

        // 有 update 权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.update("ffcdfa25-6edc-4b7c-943d-d5225050086a", "updated-pod-1");
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }
    }

    @Test
    public void batchUpdateTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();
        List<String> podIDs = Arrays.asList("ffcdfa25-6edc-4b7c-943d-d5225050086a", 
                                            "ffcdfa25-6edc-4b7c-943d-d52250500few");
        IDListRequest req = new IDListRequest();
        req.setIds(podIDs);                      
        List<String> podValues = Arrays.asList("updated-pod-1",                                   
                                               "updated-pod-2");   

        // 没有 update 权限
        iamClient.result.setVerifyResults(getVerifyResult(false));
        try {                     
            controller.batchUpdate(req, podValues);
            Assert.fail();
        }
        catch (AuthorizationException e) {
            // 符合预期
            Assert.assertEquals(AuthorizationException.AuthorizationErrorCode.IAM_PERMISSION_DENY, e.getCode());
            LOGGER.info("Test run success, exception is " + e.toString());
        }
        Assert.assertEquals("pod-1", controller.pods.get("ffcdfa25-6edc-4b7c-943d-d5225050086a"));

        // 有 update 权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.batchUpdate(req, podValues);
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }

        // 有 update 权限，但是资源不存在
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.update("none-existed-pod", "updated-pod-1");
            Assert.fail();
        }
        catch (AuthorizationException e) {
            // 符合预期
            Assert.assertEquals(AuthorizationException.AuthorizationErrorCode.BCI_ACCESS_NON_OWNED_RESOURCE, 
                                e.getCode());
            LOGGER.info("Test run failed, exception is " + e.toString());
        }
    }

    @Test
    public void deleteTest() {
        MockIAMClient iamClient = new MockIAMClient();
        aspect.setIamClient(iamClient);
        iamClient.result = new BatchVerifyResults();
        ControllerMock.Identify id = controller.new Identify();
        id.id =  "ffcdfa25-6edc-4b7c-943d-d5225050086a";

        // 没有delete权限
        iamClient.result.setVerifyResults(getVerifyResult(false));
        try {
            controller.delete(id);
            Assert.fail();
        }
        catch (AuthorizationException e) {
            // 符合预期
            LOGGER.info("Test run success, exception is " + e.toString());
        }
        Assert.assertEquals(2, controller.pods.size());

        // 有delete权限
        iamClient.result.setVerifyResults(getVerifyResult(true));
        try {
            controller.delete(id);
        }
        catch (AuthorizationException e) {
            // 不符合预期
            LOGGER.info("Test run failed, exception is " + e.toString());
            Assert.fail();
        }
    }

    private class MockIAMClient extends IAMClient {
        public BatchVerifyResults result;
        @Override
        public BatchVerifyResults batchVerify(String userId, BatchPermissionRequest batchPermissionRequest) {
            return result;
        }
    }
}
