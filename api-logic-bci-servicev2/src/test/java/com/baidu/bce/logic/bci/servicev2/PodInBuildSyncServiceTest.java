package com.baidu.bce.logic.bci.servicev2;


import com.baidu.bce.logic.bci.servicev2.sync.service.PodInBuildSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import endpoint.EndpointManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import com.baidu.bce.logic.bci.servicev2.mock.BillingResourceSyncManagerV2Mock;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class, EndpointManager.class})
public class PodInBuildSyncServiceTest {

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Autowired
    private PodInBuildSyncServiceV2 podInBuildSyncService;

    @Before
    public void setUp() throws IllegalAccessException, SQLException {
        testUtil.setUp();
    }

    @Test
    public void schedulerTest() {
        podInBuildSyncService.syncPodInBuild();
    }

    @Test
    public void chargeStatusTest() {
        BillingResourceSyncManagerV2Mock billingResourceSyncManager = new BillingResourceSyncManagerV2Mock();
        podInBuildSyncService.setBillingResourceSyncManager(billingResourceSyncManager);
        PodPO podPO = new PodPO();
        podPO.setCpt1(true);
        podPO.setUserId("user-1");
        podPO.setPodUuid("UUID");
        podPO.setStatus("Running");
        podPO.setResourceVersion(111);
        podPO.setDelayReleaseDurationMinute(0);
        podPO.setResourceRecycleTimestamp(10);
        podPO.setResourceRecycleComplete(1);
        podPO.setResourceRecycleReason("JOB_POD_COMPLETE");
        podInBuildSyncService.chargeStatus(podPO.getStatus(), podPO, new Timestamp(new Date().getTime()), false);
    }
}
