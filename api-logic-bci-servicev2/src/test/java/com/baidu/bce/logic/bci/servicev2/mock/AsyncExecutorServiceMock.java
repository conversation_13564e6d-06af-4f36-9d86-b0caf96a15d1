package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthority;
import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthorityList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class AsyncExecutorServiceMock extends AsyncExecutorService {

    public Object getAsyncResult(String workKeyPrefix) {
        if (workKeyPrefix.contains("getZoneResourceDetail")) {
            ZoneAndAuthorityList zoneAndAuthorityList = new ZoneAndAuthorityList();
            zoneAndAuthorityList.setZoneAndAuthorities(new ArrayList<ZoneAndAuthority>());
            return zoneAndAuthorityList;
        } else if (workKeyPrefix.contains("getEipInstanceMapAsync")) {
            EipInstance eipInstance = new EipInstance();
            eipInstance.setEip("***********");
            Map<String, EipInstance> map = new HashMap<>();
            map.put("p-delete", eipInstance);
            return map;
        } else {
            return new Object();
        }

    }
}
