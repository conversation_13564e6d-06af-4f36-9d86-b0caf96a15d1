package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.mock.K8sServiceMock;
import com.baidu.bce.logic.bci.servicev2.mock.PodNewOrderExecutorServiceMock;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.PodExtra;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.Util;
import com.baidu.bce.logic.bci.servicev2.util.UtilException;
import com.baidu.bce.logic.bci.servicev2.util.UuidUtil;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1ConfigMapVolumeSource;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1Volume;
import io.kubernetes.client.openapi.models.V1VolumeMount;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class DsContainerSyncServiceV2Test{
    @Autowired
    private DsContainerSyncServiceV2 service;

    @Autowired
    PodNewOrderExecutorServiceMock orderMock;

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    private K8sServiceMock k8sMock;

    @Autowired
    private ContainerDaoV2 containerDao;

    @Autowired
    private PodExtraDaoV2 podExtraDaoV2;

    private String getPodExtraForSyncingDsContainers() {
        PodExtra podExtra = new PodExtra();
        Label label = new Label();
        label.setLabelKey("bci3");
        label.setLabelValue("true");
        podExtra.setMetadataLabels(Arrays.asList(label));
        return JsonUtil.toJSON(podExtra);
    }


    @Test
    public void testSyncDsContainersNew() throws UtilException {
        String userId = "user-id";
        PodPO nonSyncedPod = new PodPO();
        nonSyncedPod.setDsContainersSyncedToK8S(false);
        nonSyncedPod.setDsContainersVersion(4);
        nonSyncedPod.setDsContainersCount(3);
        nonSyncedPod.setPodUuid("sync-ds-containers-nonsynced-pod-uuid");
        nonSyncedPod.setPodId("p-nonsyncedpod");
        nonSyncedPod.setUserId(userId);
        nonSyncedPod.setStatus("Running");
        nonSyncedPod.setExtra(getPodExtraForSyncingDsContainers());
        nonSyncedPod.setClientToken(UuidUtil.generateUuid());
        nonSyncedPod.setConditions("");
        PodExtraPO podExtraPO = new PodExtraPO();
        podExtraPO.setPodId("p-nonsyncedpod");
        podExtraDaoV2.insertPodExtra(podExtraPO);

        // nonSyncedPod2 预期有3个ds容器（2、3、4），一个普通容器，一个configmap和emptyDir、hostPath被ds容器使用
        ContainerPO dsContainer2 = new ContainerPO();
        dsContainer2.setContainerType(ContainerType.DS_WORKLOAD.getType());
        dsContainer2.setContainerUuid("ds-workload-2");
        dsContainer2.setUserId(userId);
        dsContainer2.setPodUuid("sync-ds-containers-nonsynced-pod-uuid");
        dsContainer2.setName("ds-workload-2");
        dsContainer2.setDsContainerVersion(0);
        dsContainer2.setImageAddress("image-new");

        ContainerPO dsContainer3 = new ContainerPO();
        dsContainer3.setContainerType(ContainerType.DS_WORKLOAD.getType());
        dsContainer3.setContainerUuid("ds-workload-3");
        dsContainer3.setUserId(userId);
        dsContainer3.setPodUuid("sync-ds-containers-nonsynced-pod-uuid");
        dsContainer3.setName("ds-workload-3");
        dsContainer3.setDsContainerVersion(1);
        dsContainer3.setImageAddress("image-new");

        VolumeMounts configMounts4 = new VolumeMounts();
        configMounts4.setName("ds-config-4");
        configMounts4.setMountPath("/dev/ds-config-4");
        VolumeMounts dirMounts4 = new VolumeMounts();
        dirMounts4.setName("ds-emptydir-4");
        dirMounts4.setMountPath("/dev/ds-emptydir-4");
        VolumeMounts hostPathMounts4 = new VolumeMounts();
        hostPathMounts4.setName("ds-hostpath-4");
        hostPathMounts4.setMountPath("/dev/ds-hostpath-4");
        ContainerPO dsContainer4 = new ContainerPO();
        dsContainer4.setContainerType(ContainerType.DS_WORKLOAD.getType());
        dsContainer4.setContainerUuid("ds-workload-4");
        dsContainer4.setUserId(userId);
        dsContainer4.setPodUuid("sync-ds-containers-nonsynced-pod-uuid");
        dsContainer4.setName("ds-workload-4");
        dsContainer4.setVolumeMounts(JsonUtil.toJSON(Arrays.asList(configMounts4, dirMounts4, hostPathMounts4)));
        dsContainer4.setDsContainerVersion(0);

        ContainerPO container1 = new ContainerPO();
        container1.setContainerType(ContainerType.WORKLOAD.getType());
        container1.setContainerUuid("workload-1");
        container1.setUserId(userId);
        container1.setPodUuid("sync-ds-containers-nonsynced-pod-uuid-2");
        container1.setName("workload-1");

        ConfigFile config4 = new ConfigFile();
        config4.setName("ds-config-4");
        config4.setConfigFiles(new ArrayList<ConfigFileDetail>());
        config4.setDsVolume(true);
        nonSyncedPod.setConfigFile(JsonUtil.toJSON(Arrays.asList(config4)));
        EmptyDir emptyDir4 = new EmptyDir();
        emptyDir4.setName("ds-emptydir-4");
        emptyDir4.setDsVolume(true);
        emptyDir4.setMedium("hdd");
        nonSyncedPod.setEmptyDir(JsonUtil.toJSON(Arrays.asList(emptyDir4)));
        HostPathVolume hostPath4 = new HostPathVolume();
        hostPath4.setName("ds-hostpath-4");
        hostPath4.setDsVolume(true);
        hostPath4.setPath("/var/log/pods");
        nonSyncedPod.setHostPath(JsonUtil.toJSON(Arrays.asList(hostPath4)));
        podDao.batchInsertPods(Arrays.asList(nonSyncedPod));
        containerDao.batchInsert(Arrays.asList(dsContainer2, dsContainer3, dsContainer4, container1));


        service.setK8sServiceOnlyUsedInUT(k8sMock);
        V1Pod nonSyncedK8sPod = new V1Pod();
        nonSyncedK8sPod.setMetadata(new V1ObjectMeta());
        nonSyncedK8sPod.setSpec(new V1PodSpec());
        nonSyncedK8sPod.getMetadata().setNamespace(nonSyncedPod.getUserId());
        nonSyncedK8sPod.getMetadata().setName(nonSyncedPod.getPodId());
        nonSyncedK8sPod.getMetadata().setAnnotations(new HashMap<String, String>());
        Map<String, String> anno = nonSyncedK8sPod.getMetadata().getAnnotations();
        anno.put(PodConstants.BCI_DS_CONTAINERS_VERSION, "3");
        anno.put(PodConstants.BCI_DS_CONTAINER_NAMES, "ds-workload-1,ds-workload-2,ds-workload-3");
        String imageContainerNamePrefix = "bci-internal-image-download-container-image-ds-";
        anno.put(PodConstants.BCI_CHANGED_DS_CONTAINER_2_IMAGE_CONTAINER, "ds-workload-1:"
                + imageContainerNamePrefix + "0,ds-workload-2:" + imageContainerNamePrefix
                + "1,ds-workload-3:" + imageContainerNamePrefix + "2");
        anno.put(PodConstants.BCI_DS_VOLUME_NAME_2_TYPE, "ds-config-1:config_file");
        anno.put(PodConstants.BCI_DS_CONTAINER_VERSION_MAP, "ds-workload-1:0,ds-workload-2:0,ds-workload-3:0");
        // 初始化 containers
        nonSyncedK8sPod.getSpec().setContainers(new ArrayList<V1Container>());
        V1VolumeMount mount = new V1VolumeMount();
        mount.setName("ds-config-1");
        mount.setMountPath("/dev/ds-config-1");
        V1Container dsK8sContainer1 = new V1Container();
        dsK8sContainer1.setName("ds-workload-1");
        dsK8sContainer1.setVolumeMounts(Arrays.asList(mount));
        V1Container dsK8sContainer2 = new V1Container();
        dsK8sContainer2.setName("ds-workload-2");
        dsK8sContainer2.setImage("image-old");
        V1Container dsK8sContainer3 = new V1Container();
        dsK8sContainer3.setName("ds-workload-1");
        dsK8sContainer3.setImage("image-old");
        V1Container k8sContainer1 = new V1Container();
        k8sContainer1.setName("workload-1");
        k8sContainer1.setImage("image-old");

        nonSyncedK8sPod.getSpec().getContainers().add(dsK8sContainer1);
        nonSyncedK8sPod.getSpec().getContainers().add(dsK8sContainer2);
        nonSyncedK8sPod.getSpec().getContainers().add(dsK8sContainer3);
        nonSyncedK8sPod.getSpec().getContainers().add(k8sContainer1);
        // 初始化volume
        nonSyncedK8sPod.getSpec().setVolumes(new ArrayList<V1Volume>());
        V1Volume v1Volume = new V1Volume();
        v1Volume.setName("ds-config-1");
        V1ConfigMapVolumeSource configSource = new V1ConfigMapVolumeSource();
        configSource.setName(nonSyncedPod.getPodId() + "-ds-config-1");
        v1Volume.setConfigMap(configSource);
        nonSyncedK8sPod.getSpec().getVolumes().add(v1Volume);
        k8sMock.pods.put(nonSyncedPod.getUserId() + "-" + nonSyncedPod.getPodId(), nonSyncedK8sPod);
        // 初始化 configmap
        V1ConfigMap configMap = new V1ConfigMap();
        configMap.setMetadata(new V1ObjectMeta());
        configMap.getMetadata().setNamespace(nonSyncedPod.getUserId());
        configMap.getMetadata().setName(nonSyncedPod.getPodId() + "-ds-config-1");
        k8sMock.configMaps.put(nonSyncedPod.getUserId() + "-" + nonSyncedPod.getPodId() + "-ds-config-1", configMap);

        service.syncDsContainers();
    }

    @Test
    public void testSyncDsContainers() throws UtilException {
        // 构造测试环境，一共3个pod，一个pod已经同步完成，一个pod实际已同步，但是还未标记同步完成，一个pod未同步
        String userId = "user-id";
        PodPO syncedPod = new PodPO();
        syncedPod.setDsContainersSyncedToK8S(true);
        syncedPod.setDsContainersVersion(3);
        syncedPod.setPodUuid("sync-ds-containers-synced-pod-uuid");
        syncedPod.setPodId("p-syncedpod");
        syncedPod.setUserId(userId);
        syncedPod.setStatus("Running");
        syncedPod.setExtra(getPodExtraForSyncingDsContainers());
        syncedPod.setClientToken(UuidUtil.generateUuid());
        syncedPod.setConditions("");
        PodPO nonSyncedPod1 = new PodPO();
        nonSyncedPod1.setDsContainersSyncedToK8S(false);
        nonSyncedPod1.setDsContainersVersion(3);
        nonSyncedPod1.setPodUuid("sync-ds-containers-nonsynced-pod-uuid-1");
        nonSyncedPod1.setPodId("p-nonsyncedpod-1");
        nonSyncedPod1.setUserId(userId);
        nonSyncedPod1.setStatus("Running");
        nonSyncedPod1.setExtra(getPodExtraForSyncingDsContainers());
        nonSyncedPod1.setClientToken(UuidUtil.generateUuid());
        nonSyncedPod1.setConditions("");

        PodPO nonSyncedPod2 = new PodPO();
        nonSyncedPod2.setDsContainersSyncedToK8S(false);
        nonSyncedPod2.setDsContainersVersion(4);
        nonSyncedPod2.setDsContainersCount(3);
        nonSyncedPod2.setPodUuid("sync-ds-containers-nonsynced-pod-uuid-2");
        nonSyncedPod2.setPodId("p-nonsyncedpod-2");
        nonSyncedPod2.setUserId(userId);
        nonSyncedPod2.setStatus("Running");
        nonSyncedPod2.setExtra(getPodExtraForSyncingDsContainers());
        nonSyncedPod2.setClientToken(UuidUtil.generateUuid());
        nonSyncedPod2.setConditions("");
        PodExtraPO podExtraPO2 = new PodExtraPO();
        podExtraPO2.setPodId("p-nonsyncedpod-2");
        BciOrderExtra bciOrderExtra2 = new BciOrderExtra();
        bciOrderExtra2.setPodId("p-nonsyncedpod-2");
        podExtraPO2.setOrderExtra(JsonUtil.toJSON(bciOrderExtra2));
        podExtraDaoV2.insertPodExtra(podExtraPO2);


        // nonSyncedPod2 预期有3个ds容器（2、3、4），一个普通容器，一个configmap和emptyDir、hostPath被ds容器使用
        ContainerPO dsContainer2 = new ContainerPO();
        dsContainer2.setContainerType(ContainerType.DS_WORKLOAD.getType());
        dsContainer2.setContainerUuid("ds-workload-2");
        dsContainer2.setUserId(userId);
        dsContainer2.setPodUuid("sync-ds-containers-nonsynced-pod-uuid-2");
        dsContainer2.setName("ds-workload-2");
        dsContainer2.setDsContainerVersion(0);
        dsContainer2.setImageAddress("image-new");

        ContainerPO dsContainer3 = new ContainerPO();
        dsContainer3.setContainerType(ContainerType.DS_WORKLOAD.getType());
        dsContainer3.setContainerUuid("ds-workload-3");
        dsContainer3.setUserId(userId);
        dsContainer3.setPodUuid("sync-ds-containers-nonsynced-pod-uuid-2");
        dsContainer3.setName("ds-workload-3");
        dsContainer3.setDsContainerVersion(1);
        dsContainer3.setImageAddress("image-new");

        VolumeMounts configMounts4 = new VolumeMounts();
        configMounts4.setName("ds-config-4");
        configMounts4.setMountPath("/dev/ds-config-4");
        VolumeMounts dirMounts4 = new VolumeMounts();
        dirMounts4.setName("ds-emptydir-4");
        dirMounts4.setMountPath("/dev/ds-emptydir-4");
        VolumeMounts hostPathMounts4 = new VolumeMounts();
        hostPathMounts4.setName("ds-hostpath-4");
        hostPathMounts4.setMountPath("/dev/ds-hostpath-4");
        ContainerPO dsContainer4 = new ContainerPO();
        dsContainer4.setContainerType(ContainerType.DS_WORKLOAD.getType());
        dsContainer4.setContainerUuid("ds-workload-4");
        dsContainer4.setUserId(userId);
        dsContainer4.setPodUuid("sync-ds-containers-nonsynced-pod-uuid-2");
        dsContainer4.setName("ds-workload-4");
        dsContainer4.setVolumeMounts(JsonUtil.toJSON(Arrays.asList(configMounts4, dirMounts4, hostPathMounts4)));
        dsContainer4.setDsContainerVersion(0);

        ContainerPO container1 = new ContainerPO();
        container1.setContainerType(ContainerType.WORKLOAD.getType());
        container1.setContainerUuid("workload-1");
        container1.setUserId(userId);
        container1.setPodUuid("sync-ds-containers-nonsynced-pod-uuid-2");
        container1.setName("workload-1");

        ConfigFile config4 = new ConfigFile();
        config4.setName("ds-config-4");
        config4.setConfigFiles(new ArrayList<ConfigFileDetail>());
        config4.setDsVolume(true);
        nonSyncedPod2.setConfigFile(JsonUtil.toJSON(Arrays.asList(config4)));
        EmptyDir emptyDir4 = new EmptyDir();
        emptyDir4.setName("ds-emptydir-4");
        emptyDir4.setDsVolume(true);
        emptyDir4.setMedium("hdd");
        nonSyncedPod2.setEmptyDir(JsonUtil.toJSON(Arrays.asList(emptyDir4)));
        HostPathVolume hostPath4 = new HostPathVolume();
        hostPath4.setName("ds-hostpath-4");
        hostPath4.setDsVolume(true);
        hostPath4.setPath("/var/log/pods");
        nonSyncedPod2.setHostPath(JsonUtil.toJSON(Arrays.asList(hostPath4)));
        podDao.batchInsertPods(Arrays.asList(syncedPod, nonSyncedPod1, nonSyncedPod2));
        containerDao.batchInsert(Arrays.asList(dsContainer2, dsContainer3, dsContainer4, container1));

        // 初始化订单环境
        orderMock.setK8sServiceInUT(k8sMock);
        orderMock.orderExtra = new BciOrderExtra();
        orderMock.orderExtra.setV3(true);
        service.setOrderExecutorServiceOnlyUsedInUT(orderMock);

        // 初始化k8s环境，有两个pod，一个pod包含3个ds容器（1、2、3），一个普通容器，一个configmap
        service.setK8sServiceOnlyUsedInUT(k8sMock);
        V1Pod nonSyncedK8sPod1 = new V1Pod();
        nonSyncedK8sPod1.setMetadata(new V1ObjectMeta());
        nonSyncedK8sPod1.setSpec(new V1PodSpec());
        nonSyncedK8sPod1.getMetadata().setNamespace(nonSyncedPod1.getUserId());
        nonSyncedK8sPod1.getMetadata().setName(nonSyncedPod1.getPodId());
        nonSyncedK8sPod1.getMetadata().setAnnotations(new HashMap<String, String>());
        nonSyncedK8sPod1.getMetadata().getAnnotations().put(PodConstants.BCI_DS_CONTAINERS_VERSION, "3");
        k8sMock.pods.put(nonSyncedPod1.getUserId() + "-" + nonSyncedPod1.getPodId(), nonSyncedK8sPod1);

        V1Pod nonSyncedK8sPod2 = new V1Pod();
        nonSyncedK8sPod2.setMetadata(new V1ObjectMeta());
        nonSyncedK8sPod2.setSpec(new V1PodSpec());
        nonSyncedK8sPod2.getMetadata().setNamespace(nonSyncedPod2.getUserId());
        nonSyncedK8sPod2.getMetadata().setName(nonSyncedPod2.getPodId());
        nonSyncedK8sPod2.getMetadata().setAnnotations(new HashMap<String, String>());
        Map<String, String> anno = nonSyncedK8sPod2.getMetadata().getAnnotations();
        // 初始化 annotations
        anno.put(PodConstants.BCI_DS_CONTAINERS_VERSION, "3");
        anno.put(PodConstants.BCI_DS_CONTAINER_NAMES, "ds-workload-1,ds-workload-2,ds-workload-3");
        String imageContainerNamePrefix = "bci-internal-image-download-container-image-ds-";
        anno.put(PodConstants.BCI_CHANGED_DS_CONTAINER_2_IMAGE_CONTAINER, "ds-workload-1:"
               + imageContainerNamePrefix + "0,ds-workload-2:" + imageContainerNamePrefix
               + "1,ds-workload-3:" + imageContainerNamePrefix + "2");
        anno.put(PodConstants.BCI_DS_VOLUME_NAME_2_TYPE, "ds-config-1:config_file");
        anno.put(PodConstants.BCI_DS_CONTAINER_VERSION_MAP, "ds-workload-1:0,ds-workload-2:0,ds-workload-3:0");
        // 初始化 containers
        nonSyncedK8sPod2.getSpec().setContainers(new ArrayList<V1Container>());
        V1VolumeMount mount = new V1VolumeMount();
        mount.setName("ds-config-1");
        mount.setMountPath("/dev/ds-config-1");
        V1Container dsK8sContainer1 = new V1Container();
        dsK8sContainer1.setName("ds-workload-1");
        dsK8sContainer1.setVolumeMounts(Arrays.asList(mount));
        V1Container dsK8sContainer2 = new V1Container();
        dsK8sContainer2.setName("ds-workload-2");
        dsK8sContainer2.setImage("image-old");
        V1Container dsK8sContainer3 = new V1Container();
        dsK8sContainer3.setName("ds-workload-1");
        dsK8sContainer3.setImage("image-old");
        V1Container k8sContainer1 = new V1Container();
        k8sContainer1.setName("workload-1");
        k8sContainer1.setImage("image-old");

        nonSyncedK8sPod2.getSpec().getContainers().add(dsK8sContainer1);
        nonSyncedK8sPod2.getSpec().getContainers().add(dsK8sContainer2);
        nonSyncedK8sPod2.getSpec().getContainers().add(dsK8sContainer3);
        nonSyncedK8sPod2.getSpec().getContainers().add(k8sContainer1);
        // 初始化volume
        nonSyncedK8sPod2.getSpec().setVolumes(new ArrayList<V1Volume>());
        V1Volume v1Volume = new V1Volume();
        v1Volume.setName("ds-config-1");
        V1ConfigMapVolumeSource configSource = new V1ConfigMapVolumeSource();
        configSource.setName(nonSyncedPod2.getPodId() + "-ds-config-1");
        v1Volume.setConfigMap(configSource);
        nonSyncedK8sPod2.getSpec().getVolumes().add(v1Volume);
        k8sMock.pods.put(nonSyncedPod2.getUserId() + "-" + nonSyncedPod2.getPodId(), nonSyncedK8sPod2);
        // 初始化 configmap
        V1ConfigMap configMap = new V1ConfigMap();
        configMap.setMetadata(new V1ObjectMeta());
        configMap.getMetadata().setNamespace(nonSyncedPod2.getUserId());
        configMap.getMetadata().setName(nonSyncedPod2.getPodId() + "-ds-config-1");
        k8sMock.configMaps.put(nonSyncedPod2.getUserId() + "-" + nonSyncedPod2.getPodId() + "-ds-config-1", configMap);

        // 触发同步前校验
        PodPO temp;
        temp = podDao.getPodById(syncedPod.getPodId());
        Assert.assertEquals(3, temp.getDsContainersVersion());
        Assert.assertEquals(true, temp.isDsContainersSyncedToK8S());
        temp = podDao.getPodById(nonSyncedPod1.getPodId());
        Assert.assertEquals(3, temp.getDsContainersVersion());
        Assert.assertEquals(false, temp.isDsContainersSyncedToK8S());
        temp = podDao.getPodById(nonSyncedPod2.getPodId());
        Assert.assertEquals(4, temp.getDsContainersVersion());
        Assert.assertEquals(false, temp.isDsContainersSyncedToK8S());

        // 触发一次同步
        service.syncDsContainers();

        // 数据库中，syncedPod 保持不变；nonSyncedPod1 的 dsContainersSyncedToK8s 变为 true；nonSyncedPod2 的 dsContainersSyncedToK8s 还是 false
        // k8s 侧，nonSyncedPod2 的 dsContainersVersion 和数据库一样。dscontainers 只有1和2两个容器，3被删除；
        // k8s侧，1使用的 configfile 和 configmap 都存在，3使用的configfile被删除，configmap被删除
        temp = podDao.getPodById(syncedPod.getPodId());
        Assert.assertEquals(3, temp.getDsContainersVersion());
        Assert.assertEquals(true, temp.isDsContainersSyncedToK8S());
        temp = podDao.getPodById(nonSyncedPod1.getPodId());
        Assert.assertEquals(3, temp.getDsContainersVersion());
        Assert.assertEquals(true, temp.isDsContainersSyncedToK8S());
        temp = podDao.getPodById(nonSyncedPod2.getPodId());
        Assert.assertEquals(4, temp.getDsContainersVersion());
        Assert.assertEquals(false, temp.isDsContainersSyncedToK8S());

        V1Pod tempPod = k8sMock.pods.get(nonSyncedPod2.getUserId() + "-" + nonSyncedPod2.getPodId());
        Map<String, String> tempAnno = tempPod.getMetadata().getAnnotations();
        Assert.assertEquals("4", tempAnno.get(PodConstants.BCI_DS_CONTAINERS_VERSION));
        Set<String> actualDsContainerNames = new HashSet<>(Util.getListFromString(tempAnno.get(PodConstants.BCI_DS_CONTAINER_NAMES)));
        Assert.assertEquals(new HashSet<String>(Arrays.asList("ds-workload-2", "ds-workload-3", "ds-workload-4")),
                            actualDsContainerNames);
        Map<String, String> actualMap =
            Util.getMapFromString(tempAnno.get(PodConstants.BCI_CHANGED_DS_CONTAINER_2_IMAGE_CONTAINER));
        Assert.assertEquals(2, actualMap.size());
        Assert.assertEquals(new HashSet<String>(Arrays.asList("ds-workload-3", "ds-workload-4")), actualMap.keySet());
        Assert.assertEquals(new HashSet<String>(Arrays.asList(imageContainerNamePrefix + "3", imageContainerNamePrefix + "4")),
                            new HashSet<String>(actualMap.values()));
        Map<String, String> expectDsVolume2Type = new HashMap<>();
        expectDsVolume2Type.put("ds-config-4", "config_file");
        expectDsVolume2Type.put("ds-emptydir-4", "empty_dir");
        expectDsVolume2Type.put("ds-hostpath-4", "host_path");
        Assert.assertEquals(expectDsVolume2Type, Util.getMapFromString(tempAnno.get(PodConstants.BCI_DS_VOLUME_NAME_2_TYPE)));
        Map<String, String> expectDsConatinerVersionMap = new HashMap<>();
        expectDsConatinerVersionMap.put("ds-workload-2", "0");
        expectDsConatinerVersionMap.put("ds-workload-3", "1");
        expectDsConatinerVersionMap.put("ds-workload-4", "0");
        Assert.assertEquals(expectDsConatinerVersionMap,
                            Util.getMapFromString(tempAnno.get(PodConstants.BCI_DS_CONTAINER_VERSION_MAP)));
        Assert.assertEquals(6, tempPod.getSpec().getContainers().size());
        for (V1Container tempContainer : tempPod.getSpec().getContainers()) {
            if ("ds-workload-2".equals(tempContainer.getName())
                || "ds-workload-3".equals(tempContainer.getName())
                || "ds-workload-4".equals(tempContainer.getName())
                || "workload-1".equals(tempContainer.getName())
                || (imageContainerNamePrefix + "3").equals(tempContainer.getName())
                || (imageContainerNamePrefix + "4").equals(tempContainer.getName())) {
                continue;
            } else {
                Assert.assertEquals("ds-workload-1",
                                    tempContainer.getName());
            }
            if ("ds-workload-2".equals(tempContainer.getName())) {
                // 2号ds容器的镜像没变
                Assert.assertEquals("image-old", tempContainer.getImage());
            }
            if ("ds-workload-3".equals(tempContainer.getName())) {
                // 3号ds容器的镜像变了，因为 dsContainerVersion 变了
                Assert.assertEquals("image-new", tempContainer.getImage());
            }
        }

        Assert.assertEquals(5, tempPod.getSpec().getVolumes().size());
        for (V1Volume tempVo : tempPod.getSpec().getVolumes()) {
            if ("ds-config-4".equals(tempVo.getName())
                || "ds-emptydir-4".equals(tempVo.getName())
                || "ds-hostpath-4".equals(tempVo.getName())
                || "katadata".equals(tempVo.getName())
                || "sidecar-cm-ref".equals(tempVo.getName())) {
                continue;
            } else {
                Assert.assertEquals("containerd-socket",
                                    tempVo.getName());
            }
        }
        String tempPrefix = nonSyncedPod2.getUserId() + "-" + nonSyncedPod2.getPodId();
        Assert.assertEquals(null, k8sMock.configMaps.get(tempPrefix + "-ds-config-1"));
        Assert.assertEquals(nonSyncedPod2.getPodId() + "-ds-config-4",
                            k8sMock.configMaps.get(tempPrefix + "-ds-config-4").getMetadata().getName());
    }
}