package com.baidu.bce.logic.bci.servicev2.util;

import org.junit.Assert;
import org.junit.Test;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class UtilTest{

    @Test
    public void testGetListFromString() {
        Assert.assertEquals(new HashSet<>(Arrays.asList("a", "b", "c")), Util.getListFromString("a,b,c"));
        Assert.assertEquals(new HashSet<>(Arrays.asList("a", "b", "c")), Util.getListFromString(" a,b,c "));
        Assert.assertEquals(new HashSet<>(Arrays.asList("a", " b", "c")), Util.getListFromString("a, b,c"));
        Assert.assertEquals(new HashSet<>(), Util.getListFromString(""));
        Assert.assertEquals(new HashSet<>(), Util.getListFromString(null));
    }

    @Test
    public void testSaveListToString() {
        Assert.assertEquals(Util.saveListToString(new HashSet<>(Arrays.asList("a", "b", "c"))), "a,b,c");
        Assert.assertEquals(Util.saveListToString(new HashSet<>(Arrays.asList("a", " b", "c"))), "a, b,c");
        Assert.assertEquals(Util.saveListToString(new HashSet<>()), "");
    }

    @Test
    public void testGetMapFromString() throws UtilException {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        map.put("key2", "value2");
        String input = "key:value,key2:value2";
        Assert.assertEquals(Util.getMapFromString(input), map);
    }

    @Test
    public void testSaveMapToString() {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        map.put("key2", "value2");
        List<String> possibleInput = Arrays.asList("key:value,key2:value2", "key2:value2,key:value");
        Assert.assertTrue(possibleInput.contains(Util.saveMapToString(map)));
    }

    @Test
    public void testconvertListToMap() {
        List<String> input = Arrays.asList("a", "b", "c");
        Map<String, String> actual = Util.convertListToMap(input, "1");
        Map<String, String> expect = new HashMap<>();
        expect.put("a", "1");
        expect.put("b", "1");
        expect.put("c", "1");
        Assert.assertEquals(expect, actual);
    }

    @Test
    public void testConvertOffsetDateTimeToDate() {
        // 创建一个OffsetDateTime对象
        OffsetDateTime offsetDateTime = OffsetDateTime.now();
    
        // 将OffsetDateTime转换为Date
        Date date = Util.convertOffsetDateTimeToDate(offsetDateTime);
    
        // 验证转换结果
        assertEquals(offsetDateTime.toInstant().toEpochMilli(), date.getTime());
    }
}