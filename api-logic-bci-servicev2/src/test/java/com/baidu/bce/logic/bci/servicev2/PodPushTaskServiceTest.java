package com.baidu.bce.logic.bci.servicev2;


import com.baidu.bce.logic.bci.daov2.chargerecord.PodChargeRecordDaoV2;
import com.baidu.bce.logic.bci.servicev2.charge.service.PodPushTaskServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodPushTaskServiceTest {

    @Autowired
    private PodPushTaskServiceV2 podPushTaskService;

    private PodChargeRecordDaoV2 podChargeRecordDao;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
        podChargeRecordDao = PowerMockito.mock(PodChargeRecordDaoV2.class);
    }

    @Test
    public void deleteRecordTest() {
        podPushTaskService.deleteRecord();
    }

    @Test
    public void createPushTaskTest() {
        podPushTaskService.createPushTask();
    }
}
