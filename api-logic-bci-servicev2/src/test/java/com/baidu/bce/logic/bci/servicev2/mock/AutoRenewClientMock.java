package com.baidu.bce.logic.bci.servicev2.mock;

import org.springframework.stereotype.Component;
import org.springframework.test.context.ContextConfiguration;

import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.sdk.renew.model.AutoRenewCreateRequest;
import com.baidu.bce.sdk.renew.model.AutoRenewRules;
import com.baidu.bce.sdk.renew.model.ListAutoRenewRequest;

@Component
@ContextConfiguration(classes = TestConfiguration.class)
public class AutoRenewClientMock extends AutoRenewClient implements ThrowExceptionMock {

    public AutoRenewClientMock() {
        super(null, null);
    }

    public AutoRenewClientMock(String accessKey, String secretKey) {
        super(accessKey, secretKey);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        return;
    }
    
    @Override
    public AutoRenewRules getAutoRenewRules(ListAutoRenewRequest listAutoRenewRequest) {
        return new AutoRenewRules();
    }

    @Override
    public void createAutoRenewRule(AutoRenewCreateRequest autoRenewCreateRequest) {
        return;
    }
}
