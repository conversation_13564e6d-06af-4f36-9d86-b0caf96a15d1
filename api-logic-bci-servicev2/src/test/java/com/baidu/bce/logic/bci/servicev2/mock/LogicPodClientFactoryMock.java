package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.service.UsagePackageService;
import com.baidu.bce.externalsdk.logical.network.securitygroup.SecurityGroupClient;
import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.internalsdk.bci.*;
import com.baidu.bce.internalsdk.eip.EipLogicalClient;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.OrderClientV2;
import com.baidu.bce.internalsdk.order.PricingClientV3;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.ResourceClientV2;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import org.springframework.beans.factory.annotation.Autowired;

public class LogicPodClientFactoryMock extends LogicPodClientFactoryV2 {

    @Autowired
    private ZoneClientMock zoneClient;

    @Autowired
    private IAMClientMock iamClient;

    @Autowired
    private LogicalTagClientMock logicalTagClient;

    @Autowired
    private ExternalSubnetClientMock externalSubnetClient;

    @Autowired
    private ExternalVpcClientMock externalVpcClient;

    @Autowired
    private SecurityGroupClientMock securityGroupClient;

    @Autowired
    private PodClientMock podClient;

    @Autowired
    private EipLogicalClientMock eipLogicalClient;

    @Autowired
    private UserSettingsClientMock userSettingsClient;

    @Autowired
    private LogicEipClientMock logicEipClient;

    @Autowired
    private DockerHubrClientMock dockerHubrClient;

    @Autowired
    private CceImageClientMock cceImageClient;

    @Autowired
    private ResourceClientMock resourceClient;

    @Autowired
    private OrderClientMock orderClient;

    @Autowired
    private ResourceClientV2Mock resourceClientV2;

    @Autowired
    private ServiceCatalogOrderClientMock serviceCatalogOrderClient;

    @Autowired
    private ChargeResourceServiceMock chargeResourceService;

    @Autowired
    private PricingClientV3Mock pricingClientV3;

    @Autowired
    private EipTimeOutV2ClientMock eipTimeOutV2Client;

    @Autowired
    private BCCClientMock bccClientMock;

    @Autowired
    private AutoRenewClientMock autoRenewClientMock;

    @Autowired
    private OrderClientV2Mock orderClientV2Mock;

    @Autowired
    private UsagePackageServiceMock usagePackageService;
    
    public AutoRenewClient createAutoRenewClient(String accountId) {
        return autoRenewClientMock;
    }

    public OrderClientV2 createOrderClientV2AsBceService() {
        return orderClientV2Mock;
    }
    public ZoneClient createZoneClient(String accountId) {
        return zoneClient;
    }

    public IAMClient createIAMClientWithConsoleToken() {
        return iamClient;
    }

    public LogicalTagClient createLogicalTagClient() {
        return logicalTagClient;
    }

    public LogicalTagClient createLogicalTagClient(String account) {
        return logicalTagClient;
    }

    public LogicEipClient createLogicEipClient(String accountId) {
        return logicEipClient;
    }

    public ExternalSubnetClient createExternalSubnetClient(String accountId) {
        return externalSubnetClient;
    }

    public ExternalVpcClient createExternalVpcClient(String accountId) {
        return externalVpcClient;
    }

    public SecurityGroupClient createSecurityGroupClient(String accountId) {
        return securityGroupClient;
    }

    public PodClient createPodClient(String accountId) {
        return podClient;
    }

    public UserSettingsClient createUserSettingsClient(String accountId) {
        return userSettingsClient;
    }

    public PodClient createPodClientByAccountId(String accountId) {
        return podClient;
    }

    public EipLogicalClient createEipClient(String accountId) {
        return eipLogicalClient;
    }

    @Override
    public DockerHubClient createDockerHubrClient() {
        return dockerHubrClient;
    }

    public CceImageClient createCceImageClient(String accountId) {
        return cceImageClient;
    }

    public ResourceClient createResourceClient() {
        return resourceClient;
    }

    public PodClient createAdminPodClient() {
        return podClient;
    }

    public OrderClient createOrderClient() {
        return orderClient;
    }

    public ResourceClientV2 createResourceClientV2() {
        return resourceClientV2;
    }

    public ServiceCatalogOrderClient createServiceCatalogOrderClient(String accountId) {
        return serviceCatalogOrderClient;
    }

    public ChargeResourceService createChargeResourceService(String accoundId) {
        return chargeResourceService;
    }

    public PricingClientV3 createGlobalPriceClient() {
        return pricingClientV3;
    }

    public EipTimeOutV2Client createEipTimeOutClientV2(String accountId, Integer eipCreateTimeOut) {
        return eipTimeOutV2Client;
    }

    public UsagePackageService createUsagePackageService(String accountId) {
        return usagePackageService;
    }
}