package com.baidu.bce.logic.bci;

import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;
import com.baidu.bce.logic.bci.servicev2.orderresource.service.ResourceSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;

import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;


@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore({"javax.management.*","javax.net.ssl.*"})
@PrepareForTest({LogicUserService.class})
public class BillingResourceSyncTest {

    @Autowired
    private BillingResourceSyncManagerV2 billingResourceSyncManager;

    @Autowired
    private LogicalResourceServiceV2 resourceService;

    @Autowired
    private ResourceSyncServiceV2 resourceSyncServiceV2;


    @Test
    public void syncStatusToBillingTest() {
        String uuid = "123";
        String uuid1 = "234";
        BillingResourceSyncManagerV2.SyncPodChargeStatus syncPodChargeStatus =
                billingResourceSyncManager.new SyncPodChargeStatus(null, 0, ServiceTestConstants.ACCOUNT_ID, uuid,
                        0, PodConstants.CHARGE, 0, true);
        syncPodChargeStatus.syncStatusToBilling(null,0, ServiceTestConstants.ACCOUNT_ID, uuid,
                0, PodConstants.CHARGE, 0, true);
        syncPodChargeStatus.syncStatusToBilling(null,0, ServiceTestConstants.ACCOUNT_ID, uuid1,
                0, PodConstants.NO_CHARGE, 0, true);

        Set<String> pauseSet = resourceService.queryCpt1PauseResourceStatusInfos(ServiceTestConstants.ACCOUNT_ID,
                Arrays.asList(uuid, uuid1), PodConstants.SERVICE_TYPE);
        List<String> pauseList = new ArrayList<>(pauseSet);
        Assert.assertEquals(pauseList.size(), 1);
        Assert.assertEquals(pauseList.get(0), uuid1);
    }

    @Test
    public void syncInstanceStatusToBillingTest() throws InterruptedException {
        // 数据库插入2条pod数据，uuid对应running状态，uuid1对应failed状态
        String accountId = "userid1";
        String uuid = "6ac50ddc-dd8a-41e2-8e05-54f6debb9e3b";
        String uuid1 = "7ac50ddc-dd8a-41e2-8e05-54f6debb9e3b";
        // 让billing计费状态与pod状态不一致
        BillingResourceSyncManagerV2.SyncPodChargeStatus syncPodChargeStatus =
                billingResourceSyncManager.new SyncPodChargeStatus(null,0, "", "",
                        0, "", 0, true);
        Thread.sleep(5000);
        syncPodChargeStatus.syncStatusToBilling(null,0, accountId, uuid,
                0, PodConstants.NO_CHARGE, 0, true);
        syncPodChargeStatus.syncStatusToBilling(null, 0, accountId, uuid1,
                0, PodConstants.CHARGE, 0, true);

        Set<String> pauseSet = resourceService.queryCpt1PauseResourceStatusInfos(accountId,
                Arrays.asList(uuid, uuid1), PodConstants.SERVICE_TYPE);
        List<String> pauseList = new ArrayList<>(pauseSet);
        Assert.assertEquals(pauseList.size(), 1);
        Assert.assertEquals(pauseList.get(0), uuid);

        // 预期该方法纠正pause和start
        resourceSyncServiceV2.syncInstanceStatusToBilling();
        Thread.sleep(5000);
        Set<String> pauseSet1 = resourceService.queryCpt1PauseResourceStatusInfos(accountId,
                Arrays.asList(uuid, uuid1), PodConstants.SERVICE_TYPE);
        List<String> pauseList1 = new ArrayList<>(pauseSet1);
        Assert.assertEquals(pauseSet1.size(), 1);
        Assert.assertEquals(pauseList1.get(0), uuid1);
    }
}
