package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstanceSpec;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.model.DeleteReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.reservedinstance.ReservedInstanceService;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import endpoint.EndpointManager;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.mockito.*;
import java.io.IOException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class, EndpointManager.class})
public class ReservedInstanceServiceTest {
    @Autowired
    private ReservedInstanceService reservedInstanceService;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    DatabaseUtil databaseUtil;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    private ReservedInstanceDao reservedInstanceDao;

    @Mock
    protected RegionConfiguration regionConfiguration;

    private static final String CREATE_RESERVED_INSTANCE_LOCATION = "classpath*:create_reserved_instance.json";

    private BaseCreateOrderRequestVo<CreateReservedInstanceRequest> request;

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        Mockito.when(regionConfiguration.getCurrentRegion()).thenReturn("bj");
        testUtil.setUp();
        request = testUtil.orderRequest(
                databaseUtil.getResource(CREATE_RESERVED_INSTANCE_LOCATION).getInputStream(), 
                CreateReservedInstanceRequest.class);
    }

    @Test
    public void testListReservedInstanceSpecs() {
        LogicPageResultResponse<ReservedInstanceSpec> resp = reservedInstanceService.listReservedInstanceSpecs();
        Assert.assertEquals(3, resp.getTotalCount());
        Assert.assertEquals(3, resp.getResult().size());
    }

    @Test
    public void testListReservedInstancesWithPageByMultiKey() {
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        reservedInstancePO.setName("select_test");
        reservedInstancePO.setReservedInstanceUuid("select_uuid");
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        ListRequest listRequest = new ListRequest();
        listRequest.setPageNo(1);
        listRequest.setPageSize(10);
        Map<String, String> filterMap = new HashMap<String, String>();
        filterMap.put("name", "select_test");
        listRequest.setFilterMap(filterMap);
        LogicPageResultResponse<ReservedInstancePO> resp =
                reservedInstanceService.listReservedInstancesWithPageByMultiKey(listRequest);
        Assert.assertEquals(resp.getTotalCount(), 1);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testCreateReservedInstance() {
        reservedInstanceService.createReservedInstance(request);
        request.getItems().get(0).getConfig().setPurchaseMode("PostPay");
        reservedInstanceService.createReservedInstance(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testCreateReservedInstanceWithInvalidSupply() {
        // 创建预留实例券请求数量超出供需
        String nowTime = "2025-03-19 00:00:00";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 预留时长单位moth
        Timestamp timestamp = null;
        try {
            timestamp = new Timestamp(format.parse(nowTime).getTime());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        request.getItems().get(0).getConfig().setReservedSubServiceType("GPU-generic");
        request.getItems().get(0).getConfig().setPhysicalZone("AZONE-gzns");
        request.getItems().get(0).getConfig().setReservedSpec("bci.gna2.c18m78.1a10");
        request.getItems().get(0).getConfig().setEffectiveTime(timestamp);
        request.getItems().get(0).getConfig().setReservedTimeUnit("MONTH");
        request.getItems().get(0).getConfig().setReservedTimePeriod(1);
        request.getItems().get(0).getConfig().setReservedInstanceCount(1000);
        request.getItems().get(0).getConfig().setScope("AZ");
        reservedInstanceService.createReservedInstance(request);
    }

    @Test
    public void testDeleteReservedInstance() {
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        DeleteReservedInstanceRequest req = new DeleteReservedInstanceRequest();
        req.setReservedInstanceUuid(reservedInstancePO.getReservedInstanceUuid());
        reservedInstanceService.deleteReservedInstance(req);
    }

    private ReservedInstancePO initReservedInstancePO() {
        ReservedInstancePO reservedInstancePO = new ReservedInstancePO();
        reservedInstancePO.setReservedInstanceId(commonUtils.createExternalId("r"));
        reservedInstancePO.setUserId("bb45087dee674fcaa21d75b53a35f7fc");
        reservedInstancePO.setAccountId("bb45087dee674fcaa21d75b53a35f7fc");
        reservedInstancePO.setName("name");
        reservedInstancePO.setStatus(ReservedInstancePO.Status.CREATING);
        reservedInstancePO.setResourceUuid("acfef5fe-c3ab-425d-bf4f-f5c7b8b99bc0");
        reservedInstancePO.setReservedInstanceUuid("5628b44e-efff-4728-8183-71d63f1e95d0");
        reservedInstancePO.setScope("AZ");
        reservedInstancePO.setPhysicalZone("AZONE-gzns");
        reservedInstancePO.setOrderId("080ac425c0864acea046af31f4f7ead3");
        reservedInstancePO.setPurchaseMode("FullyPrepay");
        reservedInstancePO.setReservedSpec("bci.gna2.c8m36.1a10");
        reservedInstancePO.setReservedInstanceCount(1);
        reservedInstancePO.setReservedTimeUnit("MONTH");
        reservedInstancePO.setReservedTimePeriod(1);
        reservedInstancePO.setAutoRenew(true);
        reservedInstancePO.setAutoRenewTimeUnit("MONTH");
        reservedInstancePO.setAutoRenewTimePeriod(1);
        reservedInstancePO.setEffectiveTime(new Timestamp(System.currentTimeMillis()));
        reservedInstancePO.setExpireTime(ReservedInstancePO.calcExpireTime(reservedInstancePO.getEffectiveTime(), 
                reservedInstancePO.getReservedTimeUnit(), reservedInstancePO.getReservedTimePeriod()));
        return reservedInstancePO;
    }

    @Test
    public void testCheckSupplyAndDemand() {
        try {
            String nowTime = "2023-04-10 00:00:00";
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 预留时长单位moth
            Timestamp timestamp = new Timestamp(format.parse(nowTime).getTime());
            CreateReservedInstanceRequest request1 = new CreateReservedInstanceRequest();
            request1.setReservedSubServiceType("GPU-generic");
            request1.setPhysicalZone("AZONE-gzns");
            request1.setReservedSpec("bci.gna2.c18m78.1a10");
            request1.setEffectiveTime(timestamp);
            request1.setReservedTimeUnit("MONTH");
            request1.setReservedTimePeriod(1);
            request1.setReservedInstanceCount(2);
            request1.setScope("AZ");
            Assert.assertEquals(true, reservedInstanceService.checkSupplyAndDemand(request1, ""));

            // 预留时长单位year
            CreateReservedInstanceRequest request2 = new CreateReservedInstanceRequest();
            request2.setReservedSubServiceType("GPU-generic");
            request2.setPhysicalZone("AZONE-gzns");
            request2.setReservedSpec("bci.gna2.c18m78.1a10");
            request2.setEffectiveTime(timestamp);
            request2.setReservedTimeUnit("YEAR");
            request2.setReservedTimePeriod(1);
            request2.setReservedInstanceCount(2);
            request2.setScope("AZ");
            Assert.assertEquals(true, reservedInstanceService.checkSupplyAndDemand(request2, ""));

            // cpu 地域级预留
            CreateReservedInstanceRequest request4 = new CreateReservedInstanceRequest();
            request4.setReservedSubServiceType("CPU-generic");
            request4.setPhysicalZone("");
            request4.setReservedSpec("bci.0.25c0.5g");
            request4.setEffectiveTime(timestamp);
            request4.setReservedTimeUnit("MONTH");
            request4.setReservedTimePeriod(1);
            request4.setReservedInstanceCount(2);
            request4.setScope("REGION");
            Assert.assertEquals(true, reservedInstanceService.checkSupplyAndDemand(request2, ""));

            // 创建预留实例券请求数量超出供需
            CreateReservedInstanceRequest request3 = new CreateReservedInstanceRequest();
            request3.setReservedSubServiceType("GPU-generic");
            request3.setPhysicalZone("AZONE-gzns");
            request3.setReservedSpec("bci.gna2.c18m78.1a10");
            request3.setEffectiveTime(timestamp);
            request3.setReservedTimeUnit("MONTH");
            request3.setReservedTimePeriod(1);
            request3.setReservedInstanceCount(1000);
            request3.setScope("AZ");

            Assert.assertEquals(false, reservedInstanceService.checkSupplyAndDemand(request3, ""));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void getPodByReservedInstanceIdTest() {
        PodListRequest listRequest =
                new PodListRequest("", "", "", "", 1, 10, "");
        LogicPageResultResponse<PodPO> resultResponse =
                reservedInstanceService.getPodByReservedInstanceId("r-skjtxrtl", listRequest);
        Assert.assertEquals(0, resultResponse.getTotalCount());
    }
}