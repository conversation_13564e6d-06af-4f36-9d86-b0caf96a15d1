package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.request.SubnetMapRequest;
import com.baidu.bce.externalsdk.logical.network.subnet.model.response.SubnetMapResponse;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.LOGICAL_ZONE;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.SUBNET_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.VPC_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.SUBNET_SHORT_ID;

@Component
public class ExternalSubnetClientMock extends ExternalSubnetClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public ExternalSubnetClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }


    public SubnetVo findBySubnetId(String subnetId) {
        if (throwException != null) {
            throw throwException;
        }
        if (StringUtils.isEmpty(subnetId)) {
            return null;
        }
        SubnetVo subnet = new SubnetVo();
        subnet.setAz(LOGICAL_ZONE);
        subnet.setSubnetId(SUBNET_ID);
        subnet.setSubnetUuid(SUBNET_ID);
        subnet.setVpcId(VPC_ID);
        subnet.setShortId(SUBNET_SHORT_ID);
        return subnet;
    }

    public SubnetVo findSubnetWithIpUsage(String subnetId) {
        if (throwException != null) {
            throw throwException;
        }
        if (StringUtils.isEmpty(subnetId)) {
            return null;
        }
        SubnetVo subnet = new SubnetVo();
        subnet.setAz(LOGICAL_ZONE);
        subnet.setSubnetId(SUBNET_ID);
        subnet.setVpcId(VPC_ID);
        subnet.setShortId(SUBNET_SHORT_ID);
        subnet.setTotalIps(100);
        subnet.setUsedIps(10);
        return subnet;
    }

    public SubnetMapResponse getSubnetMap(SubnetMapRequest request) {
        Map<String, SubnetVo> subnetMap = new HashMap<>();
        SubnetVo subnetVo = new SubnetVo();
        subnetVo.setSubnetId(SUBNET_ID);
        subnetVo.setSubnetUuid(SUBNET_ID);
        subnetVo.setVpcId(VPC_ID);
        subnetVo.setShortId(SUBNET_SHORT_ID);
        subnetVo.setAz(LOGICAL_ZONE);
        subnetMap.put(SUBNET_ID, subnetVo);

        Map<String, VpcVo> vpcVoMap = new HashMap<>();
        VpcVo vpcVo = new VpcVo();
        vpcVo.setVpcId(VPC_ID);
        vpcVoMap.put(VPC_ID, vpcVo);

        SubnetMapResponse subnetMapResponse = new SubnetMapResponse();
        subnetMapResponse.setSubnetMap(subnetMap);
        subnetMapResponse.setVpcMap(vpcVoMap);

        return subnetMapResponse;
    }

    public SubnetMapResponse findSubnetMap(SubnetMapRequest request) {
        Map<String, SubnetVo> subnetMap = new HashMap<>();
        SubnetVo subnetVo = new SubnetVo();
        subnetVo.setSubnetId(SUBNET_ID);
        subnetVo.setSubnetUuid(SUBNET_ID);
        subnetVo.setVpcId(VPC_ID);
        subnetVo.setShortId(SUBNET_SHORT_ID);
        subnetMap.put(SUBNET_ID, subnetVo);

        Map<String, VpcVo> vpcVoMap = new HashMap<>();
        VpcVo vpcVo = new VpcVo();
        vpcVo.setVpcId(VPC_ID);
        vpcVoMap.put(VPC_ID, vpcVo);

        SubnetMapResponse subnetMapResponse = new SubnetMapResponse();
        subnetMapResponse.setSubnetMap(subnetMap);
        subnetMapResponse.setVpcMap(vpcVoMap);

        return subnetMapResponse;
    }
}
