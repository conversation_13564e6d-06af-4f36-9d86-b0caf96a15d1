package com.baidu.bce.logic.bci.servicev2.orderexecute;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Volume;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodNewOrderExecutorServiceV2Test {

    @InjectMocks
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Mock
    private BciOrderExtra bciOrderExtra;

    @Mock
    private V1ObjectMeta v1ObjectMeta;

    @Mock
    private V1ObjectMeta metadata;

    @Mock
    private PodConfiguration podConfiguration;

    Order order;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        podNewOrderExecutorServiceV2 = PowerMockito.spy(new PodNewOrderExecutorServiceV2());
        when(v1ObjectMeta.getAnnotations()).thenReturn(new HashMap<>());

        // 模拟 PodConfiguration 的 getGpuSpecK8sResourceMap 方法
        Map<String, String> gpuSpecK8sResourceMap = new HashMap<>();
        gpuSpecK8sResourceMap.put("Nvidia A10 PCIE", "baidu.com/a10_24g_cgpu");
        gpuSpecK8sResourceMap.put("Nvidia A100 Nvswitch-80g", "baidu.com/a100_80g_cgpu");
        gpuSpecK8sResourceMap.put("", ""); // 处理空字符串的情况
        when(podConfiguration.getGpuSpecK8sResourceMap()).thenReturn(gpuSpecK8sResourceMap);

        // 通过反射设置 podConfiguration 字段
        java.lang.reflect.Field field = PodNewOrderExecutorServiceV2.class.getDeclaredField("podConfiguration");
        field.setAccessible(true);
        field.set(podNewOrderExecutorServiceV2, podConfiguration);
    }

    @Test
    public void testForNTPWithValidAnnotations() throws Exception {
        String annotations = "{\"bci.virtual-kubelet.io/bci-ntp-server\": \"***********\"}";
        when(bciOrderExtra.getAnnotations()).thenReturn(annotations);

        podNewOrderExecutorServiceV2.forNTP(bciOrderExtra, v1ObjectMeta);

        Map<String, String> expectedAnnotations = new HashMap<>();
        expectedAnnotations.put("bci.virtual-kubelet.io/bci-ntp-server", "***********");
        assertEquals(expectedAnnotations, v1ObjectMeta.getAnnotations());
    }

    @Test
    public void testForNTPWithEmptyAnnotations() {
        when(bciOrderExtra.getAnnotations()).thenReturn("");

        podNewOrderExecutorServiceV2.forNTP(bciOrderExtra, v1ObjectMeta);

        assertTrue(v1ObjectMeta.getAnnotations().isEmpty());
    }

    @Test(expected = Exception.class)
    public void testForNTPWithInvalidAnnotations() {
        String invalidAnnotations = "invalid-json";
        when(bciOrderExtra.getAnnotations()).thenReturn(invalidAnnotations);
        podNewOrderExecutorServiceV2.forNTP(bciOrderExtra, v1ObjectMeta);
    }

    @Test(expected = PodExceptions.PodAnnotationInvalid.class)
    public void testForNTPWithInvalidIPAddress() {
        String annotations = "{\"bci.virtual-kubelet.io/bci-ntp-server\": \"abcdefg\"}";
        when(bciOrderExtra.getAnnotations()).thenReturn(annotations);
        podNewOrderExecutorServiceV2.forNTP(bciOrderExtra, v1ObjectMeta);
    }

    @Test(expected = PodExceptions.PodAnnotationInvalid.class)
    public void testForNTPWithEmptyIPAddress() {
        String annotations = "{\"bci.virtual-kubelet.io/bci-ntp-server\": \"\"}";
        when(bciOrderExtra.getAnnotations()).thenReturn(annotations);
        podNewOrderExecutorServiceV2.forNTP(bciOrderExtra, v1ObjectMeta);
    }


    @Test
    public void testGetNTPSidecarContainers_withValidNtpServerIP() throws IOException {
        // Arrange
        Map<String, String> annotations = new HashMap<>();
        annotations.put("bci.virtual-kubelet.io/bci-ntp-server", "***********");
        when(metadata.getAnnotations()).thenReturn(annotations);

        // Act
        List<V1Container> containers = podNewOrderExecutorServiceV2.getNTPSidecarContainers(metadata);

        // Assert
        assertTrue(containers.size() > 0);
        V1Container ntpSidecar = containers.get(0);
        assertEquals("bci-internal-ntp-sidecar-container-0", ntpSidecar.getName());
        assertEquals("[/bin/start.sh]", ntpSidecar.getCommand().toString());
        assertEquals(1, ntpSidecar.getPorts().size());
        assertEquals("123", ntpSidecar.getPorts().get(0).getContainerPort().toString());
        assertEquals("UDP", ntpSidecar.getPorts().get(0).getProtocol());
        assertEquals("***********", ntpSidecar.getEnv().get(0).getValue());
    }

    @Test
    public void testGetNTPSidecarContainers_withEmptyAnnotations() throws IOException {
        // Arrange
        when(metadata.getAnnotations()).thenReturn(new HashMap<>());

        // Act
        List<V1Container> containers = podNewOrderExecutorServiceV2.getNTPSidecarContainers(metadata);

        // Assert
        assertTrue(containers.isEmpty());
    }

    @Test
    public void testGetNTPSidecarContainers_withNullAnnotations() throws IOException {
        // Arrange
        when(metadata.getAnnotations()).thenReturn(null);

        // Act
        List<V1Container> containers = podNewOrderExecutorServiceV2.getNTPSidecarContainers(metadata);

        // Assert
        assertTrue(containers.isEmpty());
    }

    @Test
    public void testGetNTPSidecarContainers_withNullNtpServerIP() throws IOException {
        // Arrange
        Map<String, String> annotations = new HashMap<>();
        annotations.put("bci.virtual-kubelet.io/bci-ntp-server", null);
        when(metadata.getAnnotations()).thenReturn(annotations);

        // Act
        List<V1Container> containers = podNewOrderExecutorServiceV2.getNTPSidecarContainers(metadata);

        // Assert
        assertTrue(containers.isEmpty());
    }

    @Test
    public void testGetNTPSidecarContainers_withEmptyNtpServerIP() throws IOException {
        // Arrange
        Map<String, String> annotations = new HashMap<>();
        annotations.put("bci.virtual-kubelet.io/bci-ntp-server", "");
        when(metadata.getAnnotations()).thenReturn(annotations);

        // Act
        List<V1Container> containers = podNewOrderExecutorServiceV2.getNTPSidecarContainers(metadata);

        // Assert
        assertTrue(containers.isEmpty());
    }

    /**
     * 测试geneContainers方法 - workload类型容器的启动和退出优先级场景
     * 验证容器按照启动优先级排序，以及退出优先级逻辑的处理
     *
     * 测试场景:
     * 1. 容器启动优先级排序：高优先级容器排在前面
     * 2. 退出优先级逻辑：确保容器配置了正确的生命周期hooks
     * 3. 容器基本属性正确设置
     */
    @Test
    public void testGeneContainersWithPriority() throws IOException {
        // 创建具有不同启动和退出优先级的workload容器
        List<ContainerPurchase> containers = new ArrayList<>();

        // 容器1：启动优先级=5，退出优先级=3
        ContainerPurchase container1 = createContainerWithPriority("workload-1",
                ContainerType.WORKLOAD, "5", "3");
        containers.add(container1);

        // 容器2：启动优先级=10，退出优先级=8
        ContainerPurchase container2 = createContainerWithPriority("workload-2",
                ContainerType.WORKLOAD, "10", "8");
        containers.add(container2);

        // 容器3：启动优先级=1，退出优先级=1
        ContainerPurchase container3 = createContainerWithPriority("workload-3",
                ContainerType.WORKLOAD, "1", "1");
        containers.add(container3);

        // 执行方法
        List<V1Container> result = podNewOrderExecutorServiceV2.geneContainers(
                containers, ContainerType.WORKLOAD, new ArrayList<V1Volume>(), false);

        // 验证结果
        assertEquals(3, result.size());

        // 验证容器按启动优先级降序排列 (10 > 5 > 1)
        assertEquals("workload-2", result.get(0).getName());
        assertEquals("workload-1", result.get(1).getName());
        assertEquals("workload-3", result.get(2).getName());

        // 验证每个容器的基本属性
        for (V1Container container : result) {
            assertNotNull(container.getName());
            assertNotNull(container.getImage());
            assertNotNull(container.getResources());
            assertNotNull(container.getEnv());

            // 验证资源配置
            assertNotNull(container.getResources().getLimits().get("cpu"));
            assertNotNull(container.getResources().getLimits().get("memory"));

            // 验证优先级环境变量存在
            boolean hasLaunchPriority = container.getEnv().stream()
                    .anyMatch(env -> "BCI_CONTAINER_LAUNCH_PRIORITY".equals(env.getName()));
            boolean hasExitPriority = container.getEnv().stream()
                    .anyMatch(env -> "BCI_CONTAINER_EXIT_PRIORITY".equals(env.getName()));

            assertTrue(hasLaunchPriority);
            assertTrue(hasExitPriority);
        }

        // 验证高优先级容器的生命周期配置
        V1Container highPriorityContainer = result.get(0); // workload-2, 优先级=10
        if (highPriorityContainer.getLifecycle() != null) {
            // 高优先级容器可能有postStart hook
            if (highPriorityContainer.getLifecycle().getPostStart() != null) {
                assertNotNull(highPriorityContainer.getLifecycle().getPostStart());
            }
        }

        // 验证低优先级容器的生命周期配置
        V1Container lowPriorityContainer = result.get(2); // workload-3, 优先级=1
        if (lowPriorityContainer.getLifecycle() != null) {
            // 低优先级容器可能有preStop hook用于等待高优先级容器
            if (lowPriorityContainer.getLifecycle().getPreStop() != null) {
                assertNotNull(lowPriorityContainer.getLifecycle().getPreStop());
            }
        }
    }

    /**
     * 测试geneContainers方法：容器优先级相同的场景
     * 验证当所有容器优先级相同时，不会进行特殊排序
     */
    @Test
    public void testGeneContainersWithSamePriority() throws IOException {
        List<ContainerPurchase> containers = new ArrayList<>();

        // 创建3个具有相同优先级的workload容器
        containers.add(createContainerWithPriority("workload-a", ContainerType.WORKLOAD, "5", "5"));
        containers.add(createContainerWithPriority("workload-b", ContainerType.WORKLOAD, "5", "5"));
        containers.add(createContainerWithPriority("workload-c", ContainerType.WORKLOAD, "5", "5"));

        List<V1Container> result = podNewOrderExecutorServiceV2.geneContainers(
                containers, ContainerType.WORKLOAD, new ArrayList<V1Volume>(), false);

        assertEquals(3, result.size());

        // 验证所有容器都被正确处理
        for (V1Container container : result) {
            assertNotNull(container.getName());
            assertTrue(container.getName().startsWith("workload-"));
        }
    }

    /**
     * 测试geneContainers方法：混合容器类型过滤场景
     * 验证只返回指定类型的容器，其他类型被过滤
     */
    @Test
    public void testGeneContainersWithMixedTypesFiltering() throws IOException {
        List<ContainerPurchase> containers = new ArrayList<>();

        // 混合不同类型的容器
        containers.add(createContainerWithPriority("init-1", ContainerType.INIT, "5", "5"));
        containers.add(createContainerWithPriority("workload-1", ContainerType.WORKLOAD, "10", "8"));
        containers.add(createContainerWithPriority("ds-workload-1", ContainerType.DS_WORKLOAD, "3", "3"));
        containers.add(createContainerWithPriority("workload-2", ContainerType.WORKLOAD, "8", "6"));

        // 只获取workload类型容器
        List<V1Container> result = podNewOrderExecutorServiceV2.geneContainers(
                containers, ContainerType.WORKLOAD, new ArrayList<V1Volume>(), false);

        assertEquals(2, result.size());

        // 验证返回的都是workload容器，且按优先级排序
        assertEquals("workload-1", result.get(0).getName());
        assertEquals("workload-2", result.get(1).getName());
    }

    /**
     * 创建具有指定优先级的测试容器
     * @param name 容器名称
     * @param type 容器类型
     * @param launchPriority 启动优先级
     * @param exitPriority 退出优先级
     * @return 配置好的ContainerPurchase对象
     */
    private ContainerPurchase createContainerWithPriority(String name, ContainerType type,
                                                          String launchPriority, String exitPriority) {
        ContainerPurchase container = new ContainerPurchase();
        container.setName(name);
        container.setContainerType(type.getType());
        container.setImageName("test-image");
        container.setImageAddress("registry.example.com/test");
        container.setImageVersion("1.0");
        container.setImagePullPolicy("IfNotPresent");
        container.setCpu(1.0f);
        container.setMemory(1.0f);
        container.setWorkingDir("/app");

        // 关键修复：设置GPU相关字段避免空指针异常
        container.setGpuType(""); // 设置为空字符串而不是null
        container.setGpuCount(0.0f); // 设置为0而不是null

        // 设置启动和退出优先级环境变量
        List<Environment> envs = new ArrayList<>();

        Environment launchPriorityEnv = new Environment();
        launchPriorityEnv.setKey("BCI_CONTAINER_LAUNCH_PRIORITY");
        launchPriorityEnv.setValue(launchPriority);
        envs.add(launchPriorityEnv);

        Environment exitPriorityEnv = new Environment();
        exitPriorityEnv.setKey("BCI_CONTAINER_EXIT_PRIORITY");
        exitPriorityEnv.setValue(exitPriority);
        envs.add(exitPriorityEnv);

        // 添加一些其他测试环境变量
        Environment testEnv = new Environment();
        testEnv.setKey("TEST_ENV");
        testEnv.setValue("test-value");
        envs.add(testEnv);

        container.setEnvs(envs);

        // 设置基本的命令参数
        container.setCommands(Arrays.asList("/bin/sh", "-c"));
        container.setArgs(Arrays.asList("echo 'Container " + name + " started'"));

        // 设置其他可能导致空指针的字段
        container.setPorts(new ArrayList<>());
        container.setVolumeMounts(new ArrayList<>());
        container.setLogCollections(new ArrayList<>());

        return container;
    }

    @Test
    public void testEncapsulatePodForCreate() {
        order = new Order();
        order.setItems(new ArrayList<>());
        order.getItems().add(new Order.Item());
        order.getItems().get(0).setServiceType("BCI");


    }
}