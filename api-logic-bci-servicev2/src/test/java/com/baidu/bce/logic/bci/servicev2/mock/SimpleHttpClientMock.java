package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.logic.bci.servicev2.monitor.SimpleHttpClient;
import java.io.IOException;
import java.util.Map;

public class SimpleHttpClientMock extends SimpleHttpClient {
    private String result = "";

    @Override
    public String callPostWithFormUrlEncoded(String url, Map<String, String> headerParams, 
                                             Map<String, String> bodyParams, int timeoutMS) throws IOException {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}