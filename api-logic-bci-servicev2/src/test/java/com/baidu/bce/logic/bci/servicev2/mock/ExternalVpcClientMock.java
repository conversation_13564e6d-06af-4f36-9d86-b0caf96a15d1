package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.BelongSameVpcRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.VpcIdsRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.response.SimpleVpcMapResponse;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ExternalVpcClientMock extends ExternalVpcClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public ExternalVpcClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public boolean validateBelongSameVpc(BelongSameVpcRequest request) {
        return true;
    }

    public Map<String, SimpleVpcVo> get(VpcIdsRequest request) {
        Map<String, SimpleVpcVo> vpcVoMap = new HashMap<>();
        for (String vpcId : request.getVpcIds()) {
            SimpleVpcVo vpc = new SimpleVpcVo();
            vpc.setVpcId(vpcId);
            vpc.setStatus(0);
            vpc.setCidr("192.168.0.0/16");
            vpcVoMap.put(vpcId, vpc);
        }
        return vpcVoMap;
    }
}
