package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciFailStrategyConstant;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ContainerStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConditionDetail;
import com.baidu.bce.logic.bci.servicev2.constant.PodConditionTypeConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PodRestartPolicyConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodStatusReasonConstant;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.PodCondition;
import com.baidu.bce.logic.bci.servicev2.model.PodStatus;
import com.baidu.bce.logic.bci.servicev2.sync.service.PodContainerSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.Util;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ContainerState;
import io.kubernetes.client.openapi.models.V1ContainerStateRunning;
import io.kubernetes.client.openapi.models.V1ContainerStateTerminated;
import io.kubernetes.client.openapi.models.V1ContainerStateWaiting;
import io.kubernetes.client.openapi.models.V1ContainerStatus;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodCondition;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1PodStatus;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodContainerSyncServiceTest {

    @Autowired
    private PodContainerSyncServiceV2 podContainerSyncService;

    protected static final Logger LOGGER = LoggerFactory.getLogger(PodContainerSyncServiceTest.class);


    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
    }

    @Test
    public void hasFailedSidecarTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();

        Map<String, String> annotations = new HashMap<String, String>();
        annotations.put("bci_internal_sidecarStartFailed", "sidecar-1");
        V1Pod pod = new V1Pod().metadata(new V1ObjectMeta().annotations(annotations));
        Assert.assertEquals(true, syncPod.hasFailedSidecar(pod));

        annotations.remove("bci_internal_sidecarStartFailed");
        Assert.assertEquals(false, syncPod.hasFailedSidecar(pod));
    }

    @Test
    public void isFailedTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();

        V1ContainerState state = new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1));
        Assert.assertEquals(true, syncPod.isFailed(state));

        state = state.terminated(new V1ContainerStateTerminated().exitCode(0));
        Assert.assertEquals(false, syncPod.isFailed(state));

        state = state.terminated(new V1ContainerStateTerminated().exitCode(null));
        Assert.assertEquals(false, syncPod.isFailed(state));

        state = state.terminated(null);
        Assert.assertEquals(false, syncPod.isFailed(state));
    }

    @Test
    public void getFailedContainerNameTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        Map<String, String> annotations = new HashMap<>();
        annotations.put("bci-internal-image-download-init-container-image-workload-0", "cxx-xx");
        annotations.put("bci-internal-image-download-init-container-image-init-0", "cxx-init-0");

        Assert.assertEquals("cxx-xx",
            syncPod.getFailedContainerName(
                    "bci-internal-image-download-init-container-image-workload-0", annotations));

        Assert.assertEquals("cxx-init-0",
            syncPod.getFailedContainerName(
                    "bci-internal-image-download-init-container-image-init-0", annotations));
    }

    @Test
    public void optimizeContainerStateTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        PodContainerSyncServiceV2.ImagePullStatus pulling = PodContainerSyncServiceV2.ImagePullStatus.PULLING;
        PodContainerSyncServiceV2.ImagePullStatus success = PodContainerSyncServiceV2.ImagePullStatus.PULL_SUCCESS;
        PodContainerSyncServiceV2.ImagePullStatus error = PodContainerSyncServiceV2.ImagePullStatus.PULL_ERROR;
        V1ContainerState cState = new V1ContainerState();

        // 当前容器镜像拉取失败，预期是 waiting，原因是 PullImageErr
        V1ContainerState actual = syncPod.optimizeContainerState(error, false, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c01",null);
        Assert.assertNotEquals(cState, actual);
        Assert.assertEquals("PullImageErr", actual.getWaiting().getReason());
        Assert.assertEquals("failed to pull image", actual.getWaiting().getMessage());

        // 其他容器镜像拉取失败，预期是 waiting，原因是 PullingImage
        actual = syncPod.optimizeContainerState(error, false, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c02",null);
        Assert.assertNotEquals(cState, actual);
        Assert.assertEquals("PullingImage", actual.getWaiting().getReason());

        // 当前容器镜像拉取失败，用户有 init 容器，原因是 PullImageErr
        actual = syncPod.optimizeContainerState(error, true, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c01",null);
        Assert.assertNotEquals(cState, actual);
        Assert.assertEquals("PullImageErr", actual.getWaiting().getReason());

        // 其他容器镜像拉取失败，用户有 init 容器，state 保持不变
        actual = syncPod.optimizeContainerState(error, true, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c02",null);
        Assert.assertEquals(cState, actual);

        // 当前容器镜像拉取中，预期是 waiting，原因是 PullingImage
        actual = syncPod.optimizeContainerState(pulling, false, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c01",null);
        Assert.assertNotEquals(cState, actual);
        Assert.assertEquals("PullingImage", actual.getWaiting().getReason());

        // 其他容器镜像拉取中，预期是 waiting，原因是 PullingImage
        actual = syncPod.optimizeContainerState(pulling, false, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c02",null);
        Assert.assertNotEquals(cState, actual);
        Assert.assertEquals("PullingImage", actual.getWaiting().getReason());

        // 当前容器镜像拉取中，用户有 init 容器，state 保持不变
        actual = syncPod.optimizeContainerState(pulling, true, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c01",null);
        Assert.assertEquals(cState, actual);

        // 其他容器镜像拉取中，用户有 init 容器，state 保持不变
        actual = syncPod.optimizeContainerState(pulling, true, "c01", false,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c02",null);
        Assert.assertEquals(cState, actual);

        // 容器镜像拉取成功，state 保持不变
        actual = syncPod.optimizeContainerState(success, false, "c01", true,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c01",null);
        Assert.assertEquals(cState, actual);

        // 内置边车容器失败，显示xx功能初始化失败
        actual = syncPod.optimizeContainerState(success, false, "c01", true,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_FAILED, cState, "c01",null);
        Assert.assertNotEquals(cState, actual);
        Assert.assertEquals("InternalError", actual.getWaiting().getReason());
        Assert.assertEquals("bci internal component init failed", actual.getWaiting().getMessage());

        // 内置边车容器成功，state 保持不变
        actual = syncPod.optimizeContainerState(success, false, "c01", true,
        PodContainerSyncServiceV2.SidecarStatus.INITIALIZE_SUCCESS, cState, "c01",null);
        Assert.assertEquals(cState, actual);
    }

    @Test
    public void syncContainerEmptyTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();

        String podId = "p-podempty";
        String podUuid = "containerempty-6edc-4b7c-943d-d522505012e1e";

        PodPO podPO = new PodPO();
        podPO.setPodId(podId);
        podPO.setStatus(BciStatus.RUNNING.getStatus());
        // 该 pod 有一个容器 container01
        podPO.setPodUuid(podUuid);
        V1Pod pod = new V1Pod();
        pod.metadata(new V1ObjectMeta().name(podId).uid(podUuid));
        Map<String, String> annotations = new HashMap<String, String>();
        pod.getMetadata().annotations(annotations);
        List<ContainerPO> containers = syncPod.syncContainer(podPO, pod, PodContainerSyncServiceV2.PodEventType.UPDATE);
        Assert.assertNotNull(containers);
        Assert.assertTrue(containers.isEmpty());
    }

    @Test
    public void syncContainerTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();

        // init status state 不为空，依靠 state 判断镜像下载的状态
        PodPO podPO = new PodPO();
        podPO.setPodId("p-abcedef");
        // 该 pod 有一个容器 container01
        podPO.setPodUuid("ffcdfa25-6edc-4b7c-943d-d522505012e1e");
        V1Pod pod = new V1Pod();
        pod.metadata(new V1ObjectMeta().name("p-abcedef").uid("ffcdfa25-6edc-4b7c-943d-d522505012e1e"));
        Map<String, String> annotations = new HashMap<String, String>();
        annotations.put("bci-internal-image-download-init-container-image-workload-0", "container01");
        pod.getMetadata().annotations(annotations);
        List<V1ContainerStatus> initStatusList = new ArrayList<>();
        V1ContainerStatus initStatus = new V1ContainerStatus();
        initStatus.name("bci-internal-image-download-init-container-image-workload-0");
        initStatus.ready(false).started(true).restartCount(5);
        initStatus.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        initStatus.lastState(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        initStatusList.add(initStatus);
        List<V1ContainerStatus> containerStatusList = new ArrayList<>();
        V1ContainerStatus sidecarStatus = new V1ContainerStatus();
        sidecarStatus.name("bci-internal-log-sidecar-container-container01");
        sidecarStatus.ready(false).started(true).restartCount(5);
        sidecarStatus.lastState(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        containerStatusList.add(sidecarStatus);
        V1ContainerStatus containerStatus = new V1ContainerStatus();
        containerStatus.name("container01");
        containerStatus.ready(false).started(false).restartCount(0);
        containerStatus.state(new V1ContainerState().waiting(new V1ContainerStateWaiting().reason("PodInitializing")
            .message("pod is in initializing")));
        containerStatus.lastState(new V1ContainerState());
        containerStatusList.add(containerStatus);
        pod.status(new V1PodStatus().initContainerStatuses(initStatusList).containerStatuses(containerStatusList));

        List<ContainerPO> containers = syncPod.syncContainer(podPO, pod, PodContainerSyncServiceV2.PodEventType.UPDATE);
        Assert.assertEquals(1, containers.size());
        ContainerCurrentState curState = JsonUtil.fromJSON(containers.get(0).getCurrentState(),
                                                           ContainerCurrentState.class);
        Assert.assertEquals("PullImageErr;;failed to pull image", curState.getDetailStatus());

        // init status state 为空，依靠 laststate 判断镜像下载的状态
        initStatus.state(new V1ContainerState().waiting(new V1ContainerStateWaiting()));
        containers = syncPod.syncContainer(podPO, pod, PodContainerSyncServiceV2.PodEventType.UPDATE);
        Assert.assertEquals(1, containers.size());
        curState = JsonUtil.fromJSON(containers.get(0).getCurrentState(),
                                     ContainerCurrentState.class);
        Assert.assertEquals("PullImageErr;;failed to pull image", curState.getDetailStatus());

        // 镜像下载没问题，sidecar 失败
        initStatus.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(0)));
        initStatus.lastState(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(0)));
        pod.getMetadata().getAnnotations().put("bci_internal_sidecarStartFailed", "sidecar-1");

        containers = syncPod.syncContainer(podPO, pod, PodContainerSyncServiceV2.PodEventType.UPDATE);
        Assert.assertEquals(1, containers.size());
        curState = JsonUtil.fromJSON(containers.get(0).getCurrentState(),
                                     ContainerCurrentState.class);
        Assert.assertEquals("InternalError;;bci internal component init failed", curState.getDetailStatus());
    }

    @Test
    public void podDeletedSyncContainerTest() {
        PodContainerSyncServiceV2.SyncDeletePod syncPod = podContainerSyncService.new SyncDeletePod();

        // init status state 不为空，依靠 state 判断镜像下载的状态
        PodPO podPO = new PodPO();
        podPO.setPodId("p-abcedef");
        // 该 pod 有一个容器 container01
        podPO.setPodUuid("ffcdfa25-6edc-4b7c-943d-d522505012e1e");
        V1Pod pod = new V1Pod();
        pod.metadata(new V1ObjectMeta().name("p-abcedef").uid("ffcdfa25-6edc-4b7c-943d-d522505012e1e"));
        Map<String, String> annotations = new HashMap<String, String>();
        annotations.put("bci-internal-image-download-init-container-image-workload-0", "container01");
        pod.getMetadata().annotations(annotations);
        List<V1ContainerStatus> initStatusList = new ArrayList<>();
        V1ContainerStatus initStatus = new V1ContainerStatus();
        initStatus.name("bci-internal-image-download-init-container-image-workload-0");
        initStatus.ready(false).started(true).restartCount(5);
        initStatus.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        initStatus.lastState(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        initStatusList.add(initStatus);
        List<V1ContainerStatus> containerStatusList = new ArrayList<>();
        V1ContainerStatus sidecarStatus = new V1ContainerStatus();
        sidecarStatus.name("bci-internal-log-sidecar-container-container01");
        sidecarStatus.ready(false).started(true).restartCount(5);
        sidecarStatus.lastState(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        containerStatusList.add(sidecarStatus);
        V1ContainerStatus containerStatus = new V1ContainerStatus();
        containerStatus.name("container01");
        containerStatus.ready(false).started(false).restartCount(0);
        containerStatus.state(new V1ContainerState().waiting(new V1ContainerStateWaiting().reason("PodInitializing")
                .message("pod is in initializing")));
        containerStatus.lastState(new V1ContainerState());
        containerStatusList.add(containerStatus);
        pod.status(new V1PodStatus().initContainerStatuses(initStatusList).containerStatuses(containerStatusList));
        boolean deleteResult = syncPod.updateDeletePodContainer(pod);
        Assert.assertTrue(deleteResult);
    }

    @Test
    public void filterBciInternalContainersTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        List<PodCondition> conditions = new ArrayList<>();
        PodCondition condition1 = new PodCondition();
        condition1.setStatus("False");
        String msg1 = String.format("containers with incomplete status: [%s %s %s %s]",
                                    "bci-internal-image-download-init-container-image-init-0",
                                    "bci-internal-image-download-init-container-image-workload-0",
                                    "init", "container0");
        condition1.setMessage(msg1);
        conditions.add(condition1);

        PodCondition condition2 = new PodCondition();
        condition2.setStatus("True");
        String msg2 = String.format("containers with incomplete status: [%s %s %s %s]",
                                    "bci-internal-image-download-init-container-image-init-0",
                                    "bci-internal-image-download-init-container-image-workload-0",
                                    "init", "container0");
        condition2.setMessage(msg2);
        conditions.add(condition2);

        PodCondition condition3 = new PodCondition();
        condition3.setStatus("False");
        String msg3 = String.format("containers status: [%s %s %s %s]",
                                   "bci-internal-image-download-init-container-image-init-0",
                                   "bci-internal-image-download-init-container-image-workload-0",
                                   "init", "container0");
        condition3.setMessage(msg3);
        conditions.add(condition3);

        PodCondition condition4 = new PodCondition();
        condition4.setStatus("False");
        String msg4 = String.format("containers with unready status: [%s %s %s %s]",
                                    "bci-internal-image-download-init-container-image-init-0",
                                    "bci-internal-image-download-init-container-image-workload-0",
                                    "init", "container0");
        condition4.setMessage(msg4);
        conditions.add(condition4);

        PodCondition condition5 = new PodCondition();
        condition5.setStatus("False");
        String msg5 = String.format("containers with unknown status: [%s %s %s %s]",
                                    "bci-internal-image-download-init-container-image-init-0",
                                    "bci-internal-image-download-init-container-image-workload-0",
                                    "init", "container0");
        condition5.setMessage(msg5);
        conditions.add(condition5);

        syncPod.filterBciInternalContainers(conditions);
        Assert.assertEquals("containers with incomplete status: [init container0]", conditions.get(0).getMessage());
        Assert.assertEquals(msg2, conditions.get(1).getMessage());
        Assert.assertEquals(msg3, conditions.get(2).getMessage());
        Assert.assertEquals("containers with unready status: [init container0]", conditions.get(3).getMessage());
        Assert.assertEquals("containers with unknown status: [init container0]", conditions.get(4).getMessage());
    }

    @Test
    public void getPodPhaseAfterRemovingBciInternalContainerTest() {
        String podPhase = "running";
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        PodPO podPO = new PodPO();
        podPO.setRestartPolicy("Never");
        V1Pod pod = new V1Pod();
        List<V1ContainerStatus> containerStatusList = new ArrayList<>();
        V1ContainerStatus sidecarStatus = new V1ContainerStatus();
        sidecarStatus.name("bci-internal-log-sidecar-container-container01");
        sidecarStatus.state(new V1ContainerState().running(new V1ContainerStateRunning()));
        V1ContainerStatus container1status = new V1ContainerStatus();
        container1status.name("c1");
        container1status.state(new V1ContainerState().running(new V1ContainerStateRunning()));
        V1ContainerStatus container2status = new V1ContainerStatus();
        container2status.name("c2");
        container2status.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        containerStatusList.add(sidecarStatus);
        containerStatusList.add(container1status);
        containerStatusList.add(container2status);
        pod.status(new V1PodStatus().containerStatuses(containerStatusList));
        Map<String, String> annotations = new HashMap<String, String>();
        annotations.put("bci_internal_sidecarContainer", "kube-proxy-xxx");
        pod.metadata(new V1ObjectMeta().annotations(annotations));

        // 还有 user container running
        podPhase = syncPod.getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, "running");
        Assert.assertEquals("running", podPhase);

        // userContainer 全都结束了，重启策略是 Always
        container1status.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(0)));
        podPO.setRestartPolicy("Always");
        podPhase = syncPod.getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, "running");
        Assert.assertEquals("running", podPhase);

        // userContainer 全都结束了，有失败的，重启策略是 OnFailure
        podPO.setRestartPolicy("OnFailure");
        podPhase = syncPod.getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, "running");
        Assert.assertEquals("running", podPhase);

        // userContainer 全都结束了，没有失败的，重启策略是 OnFailure
        annotations.put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY, "c3,c4");
        podPO.setRestartPolicy("OnFailure");
        container2status.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(0)));
        podPhase = syncPod.getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, "running");
        Assert.assertEquals("succeeded", podPhase);

        // userContainer 全都结束了，有失败的，重启策略是 Never
        podPO.setRestartPolicy("Never");
        container2status.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        podPhase = syncPod.getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, "running");
        Assert.assertEquals("failed", podPhase);

        // userContainer 全都结束了，没有失败的，重启策略是 Never
        annotations.put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY, "c3,c4");
        podPO.setRestartPolicy("Never");
        container2status.state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(0)));
        podPhase = syncPod.getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, "running");
        Assert.assertEquals("succeeded", podPhase);

        // userContainer 全都结束了，没有失败的，重启策略是 Never
        annotations.put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY, "c2");
        podPO.setRestartPolicy("Never");
        container2status.state(new V1ContainerState().running(new V1ContainerStateRunning()));
        podPhase = syncPod.getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, "running");
        Assert.assertEquals("succeeded", podPhase);
    }

    @Test
    public void getPodPhaseAfterRemovingBciDsAndDsFunctionalContainerTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();

        PodPO podPO = new PodPO();
        podPO.setRestartPolicy("Always");

        V1Pod pod = new V1Pod();
        List<V1Container> containerList = new ArrayList<>();
        List<V1ContainerStatus> containerStatusList = new ArrayList<>();
        V1Container sidecar = new V1Container();
        V1ContainerStatus sidecarStatus = new V1ContainerStatus();
        sidecar.setName("bci-internal-log-container1");
        sidecarStatus.name("bci-internal-log-container1").state(new V1ContainerState().running(new V1ContainerStateRunning()));
        V1Container container1 = new V1Container();
        V1ContainerStatus container1status = new V1ContainerStatus();
        container1.setName("c1");
        container1status.name("c1").state(new V1ContainerState().running(new V1ContainerStateRunning()));
        V1Container container2 = new V1Container();
        V1ContainerStatus container2status = new V1ContainerStatus();
        container2.setName("c2");
        container2status.name("c2").state(new V1ContainerState().terminated(new V1ContainerStateTerminated().exitCode(1)));
        V1Container dsContainer = new V1Container();
        V1ContainerStatus dsContainerStatus = new V1ContainerStatus();
        dsContainer.setName("ds1");
        dsContainerStatus.name("ds1").state(new V1ContainerState().waiting(new V1ContainerStateWaiting()));
        V1Container dsImageContainer = new V1Container();
        V1ContainerStatus dsImageContainerStatus = new V1ContainerStatus();
        dsImageContainer.setName("bci-internal-image-ds1");
        dsImageContainerStatus.name("bci-internal-image-ds1").state(new V1ContainerState().waiting(new V1ContainerStateWaiting()));
        containerList.addAll(Arrays.asList(sidecar, container1, container2, dsContainer, dsImageContainer));
        containerStatusList.addAll(Arrays.asList(sidecarStatus, container1status, container2status, dsContainerStatus, dsImageContainerStatus));
        pod.setSpec(new V1PodSpec().containers(containerList));
        pod.setStatus(new V1PodStatus().containerStatuses(containerStatusList));

        List<V1PodCondition> conditions = new ArrayList<>();
        V1PodCondition condition = new V1PodCondition().type("Initialized").status("True");
        conditions.add(condition);
        pod.getStatus().setConditions(conditions);
        
        Map<String, String> annotations = new HashMap<String, String>();
        annotations.put(PodConstants.BCI_DS_CONTAINER_NAMES, "ds1");
        annotations.put(PodConstants.BCI_DS_FUNCTIONAL_CONTAINER_NAMES, "bci-internal-image-ds1");
        pod.setMetadata(new V1ObjectMeta().annotations(annotations));

        // pod phase是pending，去除ds容器后应该是running
        String podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("running", podPhase);

        // pod phase是pending，去除ds容器后仍然是pending
        container1status.setState(new V1ContainerState().waiting(new V1ContainerStateWaiting()));
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("pending", podPhase);
        container1status.setState(new V1ContainerState().running(new V1ContainerStateRunning()));

        // pod phase 是 running、terminated，不需要转换
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "running");
        Assert.assertEquals("running", podPhase);
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "succeeded");
        Assert.assertEquals("succeeded", podPhase);

        // pod restartPolicy 是 never，不需要转换
        podPO.setRestartPolicy("Never");
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("pending", podPhase);
        podPO.setRestartPolicy("Always");

        // pod 没有 initilized，不需要转换
        condition.setStatus("False");
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("pending", podPhase);
        condition.setStatus("True");
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("running", podPhase);

        // pod 的 status 不存在 containerstatus，不需要转换
        pod.getStatus().setContainerStatuses(null);
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("pending", podPhase);
        pod.getStatus().setContainerStatuses(containerStatusList);

        // pod 的 status 中容器数和spec中不一致，不需要转换
        containerStatusList.remove(1);
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("pending", podPhase);
        containerStatusList.add(container1status);
        podPhase = syncPod.getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, "pending");
        Assert.assertEquals("running", podPhase);
    }

    @Test
    public void getContainerCurrentStateNullTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        ContainerCurrentState containerCurrentState = syncPod.getContainerCurrentState(null);
        Assert.assertNotNull(containerCurrentState);
        Assert.assertEquals(ContainerStatus.CONTAINER_STATE_CREATING, containerCurrentState.getState());
    }

    @Test
    public void processPodIgnoreExitCodeContainersFailedTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        PodPO podPO = new PodPO();
        podPO.setPodId("p-abcedef");
        ContainerPO containerPO = new ContainerPO();
        String containerName1 = "c1";
        String containerName2 = "c2";
        containerPO.setName(containerName1);
        ContainerCurrentState currentState = new ContainerCurrentState();
        V1Pod pod = new V1Pod();
        V1ObjectMeta metadata = new V1ObjectMeta();
        // 创建一个注解 Map
        Map<String, String> annotations = new HashMap<>();
        // 将注解设置到元数据中
        metadata.setAnnotations(annotations);
        pod.setMetadata(metadata);
        {
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            annotations.put("key", "value");
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            annotations.put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY, containerName2);
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            annotations.put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY, containerName1 + "," + containerName2);
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            podPO.setRestartPolicy(PodRestartPolicyConstant.ALWAYS);
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            podPO.setRestartPolicy(PodRestartPolicyConstant.ON_FAILURE);
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            annotations.put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY, containerName1);
            podPO.setRestartPolicy(PodRestartPolicyConstant.NEVER);
            podPO.setStatus(BciStatus.RUNNING.getStatus());
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            podPO.setStatus(BciStatus.SUCCEEDED.getStatus());
            currentState.setState(ContainerStatus.CONTAINER_STATE_SUCCEEDED);
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
        {
            podPO.setStatus(BciStatus.SUCCEEDED.getStatus());
            currentState.setState(ContainerStatus.CONTAINER_STATE_FAILED);
            boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
            Assert.assertFalse(result);
        }
    }

    @Test
    public void processPodIgnoreExitCodeContainersSucceedTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        PodPO podPO = new PodPO();
        podPO.setPodId("p-abcedef");
        ContainerPO containerPO = new ContainerPO();
        String containerName1 = "c1";
        String containerName2 = "c2";
        containerPO.setName(containerName1);
        ContainerCurrentState currentState = new ContainerCurrentState();
        V1Pod pod = new V1Pod();
        V1ObjectMeta metadata = new V1ObjectMeta();
        // 创建一个注解 Map
        Map<String, String> annotations = new HashMap<>();
        // 将注解设置到元数据中
        annotations.put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY, containerName1 + "," + containerName2);
        metadata.setAnnotations(annotations);
        pod.setMetadata(metadata);
        podPO.setRestartPolicy(PodRestartPolicyConstant.NEVER);
        podPO.setStatus(BciStatus.SUCCEEDED.getStatus());
        currentState.setState(ContainerStatus.CONTAINER_STATE_RUNNING);
        Date containerStartTime = new Date();
        currentState.setContainerStartTime(containerStartTime);
        // 暂停 2 秒（2000 毫秒）
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        boolean result = syncPod.processPodIgnoreExitCodeContainers(podPO, containerPO, pod, currentState);
        Assert.assertTrue(result);
        Assert.assertEquals(currentState.getExitCode(), 0);
        Assert.assertEquals(currentState.getState(), ContainerStatus.CONTAINER_STATE_SUCCEEDED);
        String expectDetailStatus =
                PodContainerSyncServiceV2.BCI_IGNORE_EXIT_CODE_CONTAINER_REASON + PodContainerSyncServiceV2.reasonAndMessageDelimiter + PodContainerSyncServiceV2.BCI_IGNORE_EXIT_CODE_CONTAINER_MESSAGE;
        Assert.assertEquals(expectDetailStatus, currentState.getDetailStatus());
        Assert.assertEquals(containerStartTime, currentState.getContainerStartTime());

        // 1. 判断 containerFinishTime 晚于 containerStartTime
        Assert.assertTrue("containerFinishTime should be after containerStartTime",
                currentState.getContainerFinishTime().after(currentState.getContainerStartTime()));

        // 2. 判断 containerFinishTime 和 containerStartTime 之间的差异是否超过1秒
        long timeDifferenceInSeconds = (currentState.getContainerFinishTime().getTime() - currentState.getContainerStartTime().getTime()) / 1000;
        Assert.assertTrue("Difference between containerFinishTime and containerStartTime should be more than 1 second", timeDifferenceInSeconds > 1);
    }

    @Test
    public void isFinalStatePodTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        {
            String restartPolicy = PodRestartPolicyConstant.ALWAYS;
            String status = BciStatus.SUCCEEDED.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertFalse(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.ALWAYS;
            String status = BciStatus.FAILED.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertFalse(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.ALWAYS;
            String status = BciStatus.SUCCEEDED.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertFalse(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.NEVER;
            String status = BciStatus.SUCCEEDED.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertTrue(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.NEVER;
            String status = BciStatus.FAILED.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertTrue(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.NEVER;
            String status = BciStatus.RUNNING.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertFalse(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.ON_FAILURE;
            String status = BciStatus.SUCCEEDED.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertTrue(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.ON_FAILURE;
            String status = BciStatus.FAILED.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertTrue(result);
        }
        {
            String restartPolicy = PodRestartPolicyConstant.ON_FAILURE;
            String status = BciStatus.RUNNING.getStatus();
            boolean result = syncPod.isFinalStatePod(restartPolicy, status);
            Assert.assertFalse(result);
        }
    }

    @Test
    public void processPodFailStrategyTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        List<PodCondition> conditions = new ArrayList<>();
        PodPO podPO = new PodPO();
        String podId = "p-abcedef";
        podPO.setPodId(podId);
        ContainerPO containerPO = new ContainerPO();
        String containerName = "c1";
        containerPO.setName(containerName);
        List<ContainerPO> containers = new ArrayList<>();
        V1Pod pod = new V1Pod();
        pod.setSpec(new V1PodSpec());
        pod.metadata(new V1ObjectMeta().name(podId).uid("ffcdfa25-6edc-4b7c-943d-d522505012e1e"));
        // 创建一个注解 Map
        Map<String, String> annotations = new HashMap<String, String>();
        // 将注解设置到元数据中
        pod.getMetadata().setAnnotations(annotations);
        {
            pod.setStatus(null);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        pod.setStatus(new V1PodStatus());
        {
            pod.getStatus().setPhase(null);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        pod.getStatus().setPhase("Running");
        pod.getStatus().setPhase(BciStatus.STATUS_PENDING);
        podPO.setStatus(BciStatus.STATUS_PENDING);
        {
            pod.getMetadata().getAnnotations().put(PodConstants.BCI_FAIL_STRATEGY_ANNOTATION_KEY, BciFailStrategyConstant.FAIL_BACK);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
            pod.getMetadata().getAnnotations().put(PodConstants.BCI_FAIL_STRATEGY_ANNOTATION_KEY, BciFailStrategyConstant.FAIL_OVER);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
            pod.getMetadata().getAnnotations().put(PodConstants.BCI_FAIL_STRATEGY_ANNOTATION_KEY, "");
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        pod.getMetadata().getAnnotations().put(PodConstants.BCI_FAIL_STRATEGY_ANNOTATION_KEY, BciFailStrategyConstant.FAIL_FAST);
        {
            pod.getSpec().setNodeName("***********");
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
            pod.getSpec().setNodeName("");
            pod.getStatus().setPodIP("***********");
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
            pod.getStatus().setConditions(new ArrayList<V1PodCondition>());
            pod.getStatus().setPodIP("");
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        {
            V1PodCondition condition = new V1PodCondition();
            condition.setType("Initialized");
            condition.setStatus("True");
            condition.setLastTransitionTime(OffsetDateTime.now());
            pod.getStatus().getConditions().add(condition);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        {
            V1PodCondition condition = new V1PodCondition();
            condition.setType("ContainersReady");
            condition.setStatus("True");
            condition.setLastTransitionTime(OffsetDateTime.now());
            pod.getStatus().getConditions().add(condition);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        {
            V1PodCondition condition = new V1PodCondition();
            condition.setType("PodScheduled");
            condition.setStatus("True");
            condition.setLastTransitionTime(OffsetDateTime.now());
            pod.getStatus().getConditions().add(condition);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        {
            V1PodCondition condition = new V1PodCondition();
            condition.setType("PodScheduled");
            condition.setStatus("False");
            condition.setReason("Schedulable");
            condition.setLastTransitionTime(OffsetDateTime.now());
            pod.getStatus().getConditions().add(condition);
            Assert.assertFalse(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
        }
        {
            pod.getStatus().getConditions().clear();
            V1PodCondition condition = new V1PodCondition();
            condition.setType("PodScheduled");
            condition.setStatus("False");
            condition.setReason("Unschedulable");
            condition.setLastTransitionTime(OffsetDateTime.now());
            pod.getStatus().getConditions().add(condition);
            containers.add(containerPO);
            containerPO.setContainerType(ContainerType.WORKLOAD.getType());
            Assert.assertTrue(syncPod.processPodFailStrategy(podPO, pod, conditions, containers));
            LOGGER.debug("processPodFailStrategyTest conditions: {}", JsonUtil.toJSONString(conditions));
            Assert.assertEquals(1, conditions.size());
            PodCondition podCondition = conditions.get(0);
            Assert.assertEquals(PodConditionTypeConstant.CONTAINER_INSTANCE_CREATED, podCondition.getType());
            Assert.assertEquals("False", podCondition.getStatus());
            Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getReason(), podCondition.getReason());
            Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getMessage(), podCondition.getMessage());
            Assert.assertNotNull(podCondition.getLastTransitionTime());
            Assert.assertNotNull(podCondition.getLastProbeTime());
            LOGGER.debug("processPodFailStrategyTest containerName:{}, containerCurrentState:{}", containerPO.getName(),
                    containerPO.getCurrentState());
            Assert.assertTrue(StringUtils.isNotEmpty(containerPO.getCurrentState()));
            ContainerCurrentState currentState = JsonUtil.fromJSON(containerPO.getCurrentState(), ContainerCurrentState.class);
            Assert.assertNotNull(currentState);
            Assert.assertTrue(StringUtils.isNotEmpty(currentState.getDetailStatus()));
            PodStatus podStatus = JsonUtil.fromJSON(currentState.getDetailStatus(), PodStatus.class);
            Assert.assertNotNull(podStatus);
            Assert.assertEquals(BciStatus.STATUS_PENDING, podStatus.getPhase());
            Assert.assertEquals(PodStatusReasonConstant.CONTAINER_INSTANCE_SCHEDULE_FAILED, podStatus.getReason());
            Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getMessage(), podStatus.getMessage());
        }
    }

    @Test
    public void buildBciPodConditionsTest() {
        V1Pod pod = new V1Pod();
        String podId = "p-abcedef";
        pod.setSpec(new V1PodSpec());
        pod.metadata(new V1ObjectMeta().name(podId).uid("ffcdfa25-6edc-4b7c-943d-d522505012e1e"));
        pod.setStatus(new V1PodStatus());
        pod.getStatus().setConditions(new ArrayList<V1PodCondition>());
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        List<ContainerPO> containerPOS = new ArrayList<>();
        PodPO podPO = new PodPO();
        podPO.setConditions("");
        List<PodCondition> conditions = syncPod.buildBciPodConditions(pod, podPO, containerPOS);
        Assert.assertTrue(conditions.isEmpty());
    }

    @Test
    public void genPodFailStrategyConditionTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        {
            V1PodCondition condition = new V1PodCondition();
            condition.setLastTransitionTime(OffsetDateTime.now());
            condition.setLastProbeTime(OffsetDateTime.now());
            {
                PodCondition podCondition = syncPod.genPodFailStrategyCondition(condition);
                Assert.assertNotNull(podCondition);
                Assert.assertEquals(Util.convertOffsetDateTimeToDate(condition.getLastTransitionTime()),
                        podCondition.getLastTransitionTime());
                Assert.assertEquals(Util.convertOffsetDateTimeToDate(condition.getLastProbeTime()),
                        podCondition.getLastProbeTime());
                Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getMessage(), podCondition.getMessage());
                Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getReason(), podCondition.getReason());
                Assert.assertEquals("False", podCondition.getStatus());
                Assert.assertEquals(PodConditionTypeConstant.CONTAINER_INSTANCE_CREATED, podCondition.getType());
            }
            {
                condition.setLastTransitionTime(null);
                PodCondition podCondition = syncPod.genPodFailStrategyCondition(condition);
                Assert.assertNotNull(podCondition);
                Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getMessage(), podCondition.getMessage());
                Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getReason(), podCondition.getReason());
                Assert.assertEquals("False", podCondition.getStatus());
                Assert.assertEquals(PodConditionTypeConstant.CONTAINER_INSTANCE_CREATED, podCondition.getType());
            }
            {
                condition.setLastProbeTime(null);
                PodCondition podCondition = syncPod.genPodFailStrategyCondition(condition);
                Assert.assertNotNull(podCondition);
                Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getMessage(), podCondition.getMessage());
                Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getReason(), podCondition.getReason());
                Assert.assertEquals("False", podCondition.getStatus());
                Assert.assertEquals(PodConditionTypeConstant.CONTAINER_INSTANCE_CREATED, podCondition.getType());
            }
        }
    }

    @Test
    public void addPodFailStrategyPhaseAndReasonToPodStatusTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        PodPO podPO = new PodPO();
        String podId = "p-abcedef";
        podPO.setPodId(podId);
        V1Pod pod = new V1Pod();
        pod.setSpec(new V1PodSpec());
        List<ContainerPO> containerPOS = new ArrayList<>();
        boolean result = syncPod.addPodFailStrategyPhaseAndReasonToPodStatus(podPO, pod, containerPOS);
        Assert.assertFalse(result);

        ContainerPO containerPO = new ContainerPO();
        containerPO.setName("c1");
        containerPO.setContainerType(ContainerType.INIT.getType());
        containerPOS.add(containerPO);
        result = syncPod.addPodFailStrategyPhaseAndReasonToPodStatus(podPO, pod, containerPOS);
        Assert.assertFalse(result);
    }

    @Test
    public void genNewContainerCurrentStateTest() {
        PodContainerSyncServiceV2.SyncPod syncPod = podContainerSyncService.new SyncPod();
        PodPO podPO = new PodPO();
        String podId = "p-abcedef";
        podPO.setPodId(podId);
        podPO.setStatus(BciStatus.STATUS_PENDING);
        V1Pod pod = new V1Pod();
        pod.setSpec(new V1PodSpec());
        ContainerPO containerPO = new ContainerPO();
        containerPO.setName("c1");
        {
            containerPO.setCurrentState("");
            String newCurrentState = syncPod.genNewContainerCurrentState(podPO, pod, containerPO);
            Assert.assertNotNull(newCurrentState);
            Assert.assertTrue(StringUtils.isNotEmpty(newCurrentState));
            ContainerCurrentState newContainerCurrentState = JsonUtil.fromJSON(newCurrentState, ContainerCurrentState.class);
            Assert.assertNotNull(newContainerCurrentState);
            String podStatusString = newContainerCurrentState.getDetailStatus();
            Assert.assertNotNull(podStatusString);
            PodStatus podStatus = JsonUtil.fromJSON(podStatusString, PodStatus.class);
            Assert.assertNotNull(podStatus);
            Assert.assertEquals(BciStatus.STATUS_PENDING, podStatus.getPhase());
            Assert.assertEquals(PodStatusReasonConstant.CONTAINER_INSTANCE_SCHEDULE_FAILED, podStatus.getReason());
            Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getMessage(), podStatus.getMessage());
        }
        {
            ContainerCurrentState currentState = new ContainerCurrentState();
            String detailStatus = "Completed;;container completed";
            currentState.setState(BciStatus.STATUS_PENDING);
            Date now = new Date();
            currentState.setContainerStartTime(now);
            currentState.setDetailStatus(detailStatus);
            currentState.setExitCode(0);
            currentState.setContainerFinishTime(now);
            containerPO.setCurrentState(JsonUtil.toJSONString(currentState));
            String newCurrentState = syncPod.genNewContainerCurrentState(podPO, pod, containerPO);
            Assert.assertNotNull(newCurrentState);
            ContainerCurrentState newContainerCurrentState = JsonUtil.fromJSON(newCurrentState, ContainerCurrentState.class);
            LOGGER.debug("genNewContainerCurrentState result currentState:{} newCurrentState::{}, " +
                            "newContainerCurrentStateJson:{}",
                    JsonUtil.toJSON(currentState), newCurrentState, JsonUtil.toJSON(newContainerCurrentState));
            Assert.assertNotNull(newContainerCurrentState);
            Assert.assertEquals(currentState.getState(), newContainerCurrentState.getState());
            Assert.assertEquals(currentState.getExitCode(), newContainerCurrentState.getExitCode());
            String podStatusString = newContainerCurrentState.getDetailStatus();
            Assert.assertNotNull(podStatusString);
            PodStatus podStatus = JsonUtil.fromJSON(podStatusString, PodStatus.class);
            Assert.assertNotNull(podStatus);
            Assert.assertEquals(BciStatus.STATUS_PENDING, podStatus.getPhase());
            Assert.assertEquals(PodStatusReasonConstant.CONTAINER_INSTANCE_SCHEDULE_FAILED, podStatus.getReason());
            Assert.assertEquals(PodConditionDetail.NO_STOCK_CONDITION.getMessage(), podStatus.getMessage());
        }
    }
}
