package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.internalsdk.trail.util.JsonUtil;
import com.baidu.bce.logic.bci.daov2.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.daov2.common.model.PodListModel;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PreemptStatus;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.request.OrderModel;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.ACCOUNT_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_UUID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.TRANSACTION_ID;

@SuppressWarnings("SpringJavaAutowiringInspection")
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class PodDaoTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodDaoTest.class);

    @Autowired
    DatabaseUtil databaseUtil;

    @Autowired
    PodDaoV2 podDao;

    String accountId = "account_id";
    String orderId = "order_id";
    String podId = "pod_id";
    String podUuid = "pod_uuid";
    String status = "error";
    private static final float FLOATPRECISION = (float) 0.00001;

    PodListModel listModel;

    @Autowired
    PodConfiguration podConfiguration;

    @Before
    public void resetDatabase() throws Exception {
//        databaseUtil.resetDatabase();

        String str = "{\"keywordType\":\"podId\",\"keyword\":\"pod_id\",\"pageNo\":1,\"pageSize\":10," +
                "\"orders\":[{\"order\":\"desc\",\"orderBy\":\"createTime\"}],\"marker\":null,\"maxKeys\":1000," +
                "\"filterMap\":{\"keywordType\":\"keyword\"},\"logicalZone\":null,\"includedUuids\":null," +
                "\"tagOrderBy\":null,\"tagOrder\":null}";
        listModel = JsonUtil.fromJson(str, PodListModel.class);
    }

    @Test
    public void testUpdatePod() {
        PodPO podPO = new PodPO();
        podPO.setPodId("p-" + podId + "_insertMMM111_update_pod");
        podPO.setUserId("*********111");
        podPO.setPodUuid("pod_uuid_update_pod");
        podPO.setStatus("Running");
        podPO.setDeleted(0);
        podPO.setClientToken(podPO.getPodId());
        List<PodPO> list = new ArrayList<>();
        list.add(podPO);

        PodPO podPO1 = new PodPO();
        podPO1.setPodId("p-" + podId + "_insertMMM333_update_pod");
        podPO1.setUserId("*********111");
        podPO1.setPodUuid("pod_uuid_update_pod");
        podPO1.setStatus("Running");
        podPO1.setPreemptStatus("PREEMPTED");
        podPO1.setDeleted(0);
        podPO1.setClientToken(podPO1.getPodId());
        podPO1.setConditions("");
        list.add(podPO1);

        podDao.batchInsertPods(list);
        PodPO pod = podDao.getPodDetail("*********111", "p-" + podId + "_insertMMM111_update_pod");
        Assert.assertEquals("Running", pod.getStatus());
        Assert.assertEquals("pod_uuid_update_pod", pod.getPodUuid());
        Assert.assertEquals("RUNNING", pod.getPreemptStatus());
        pod.setPodUuid("pod_uuid1_update_pod");
        pod.setNfs("nfs");
        pod.setPfs("pfs");
        pod.setBos("bos");
        pod.setEmptyDir("emptydir");
        pod.setConfigFile("configfile");
        pod.setFlexVolume("flexvolume");
        pod.setPodVolumes("podvolumes");
        pod.setHostPath("hostPath");
        pod.setDsContainersVersion(101);
        pod.setDsContainersCount(5);
        podDao.updatePod(pod);
        pod = podDao.getPodDetail("*********111", "p-" + podId + "_insertMMM111_update_pod");
        Assert.assertEquals("Running", pod.getStatus());
        Assert.assertEquals("pod_uuid1_update_pod", pod.getPodUuid());
        Assert.assertEquals("nfs", pod.getNfs());
        Assert.assertEquals("pfs", pod.getPfs());
        Assert.assertEquals("bos", pod.getBos());
        Assert.assertEquals("emptydir", pod.getEmptyDir());
        Assert.assertEquals("configfile", pod.getConfigFile());
        Assert.assertEquals("flexvolume", pod.getFlexVolume());
        Assert.assertEquals("podvolumes", pod.getPodVolumes());
        Assert.assertEquals("hostPath", pod.getHostPath());
        Assert.assertEquals(101, pod.getDsContainersVersion());
        Assert.assertEquals(5, pod.getDsContainersCount());

        pod.setNfs("nfs-2");
        pod.setPfs("pfs-2");
        pod.setBos("bos-2");
        pod.setEmptyDir("emptydir-2");
        pod.setConfigFile("configfile-2");
        pod.setFlexVolume("flexvolume-2");
        pod.setPodVolumes("podvolumes-2");
        pod.setHostPath("hostpath-2");
        pod.setDsContainersVersion(109);
        pod.setDsContainersCount(15);
        podDao.updatePod(pod);
        pod = podDao.getPodDetail("*********111", "p-" + podId + "_insertMMM111_update_pod");
        Assert.assertEquals("nfs-2", pod.getNfs());
        Assert.assertEquals("pfs-2", pod.getPfs());
        Assert.assertEquals("bos-2", pod.getBos());
        Assert.assertEquals("emptydir-2", pod.getEmptyDir());
        Assert.assertEquals("configfile-2", pod.getConfigFile());
        Assert.assertEquals("flexvolume-2", pod.getFlexVolume());
        Assert.assertEquals("podvolumes-2", pod.getPodVolumes());
        Assert.assertEquals("hostpath-2", pod.getHostPath());
        Assert.assertEquals(109, pod.getDsContainersVersion());
        Assert.assertEquals(15, pod.getDsContainersCount());

        pod = podDao.getPodDetail("*********111", "p-" + podId + "_insertMMM333_update_pod");
        Assert.assertEquals("Running", pod.getStatus());
        Assert.assertEquals("pod_uuid_update_pod", pod.getPodUuid());
        Assert.assertEquals("PREEMPTED", pod.getPreemptStatus());
        pod.setStatus("Succeed");
        pod.setPodUuid("pod_uuid1_update_pod");
        podDao.updatePod(pod);
        pod = podDao.getPodDetail("*********111", "p-" + podId + "_insertMMM333_update_pod");
        Assert.assertEquals("Running", pod.getStatus());
        Assert.assertEquals("pod_uuid_update_pod", pod.getPodUuid());
    }

    @Test
    public void testUpdateNodeInfo() {
        PodPO podPO = new PodPO();
        podPO.setPodId("p-" + podId + "_insertMMM");
        podPO.setUserId("*********");
        podPO.setPodUuid(POD_UUID);
        podPO.setStatus("Running");
        podPO.setProductType(podConfiguration.getBidProductType());
        podPO.setBccInstanceId("111");
        podPO.setDeleted(0);
        podPO.setClientToken(podPO.getPodId());
        podPO.setConditions("");
        List<PodPO> list = new ArrayList<>();
        list.add(podPO);
        podDao.batchInsertPods(list);

        PodPO pod = podDao.getPodDetail("*********", "p-" + podId + "_insertMMM");
        Assert.assertEquals("", pod.getNodeName());
        Assert.assertEquals("", pod.getBccInstanceId());
        // test batchUpdateStatus --update nodename
        pod.setNodeName("nodeName11");
        pod.setBccInstanceId("bccInstanceId");
        pod.setStatus("Succeed");
        List<PodPO> pods = new ArrayList<>();
        pods.add(pod);

        podDao.batchUpdateStatus(pods);

        pod = podDao.getPodDetail("*********", "p-" + podId + "_insertMMM");
        Assert.assertEquals("nodeName11", pod.getNodeName());
        Assert.assertEquals("bccInstanceId", pod.getBccInstanceId());
        Assert.assertEquals("Running", pod.getStatus());
    }

    @Test 
    public void testListBidSucceedPods() throws InterruptedException {
        PodPO podPO = new PodPO();
        podPO.setPodId("p-" + podId + "_insert111111");
        podPO.setUserId("*********");
        podPO.setPodUuid(POD_UUID);
        podPO.setStatus("Succeeded");
        // gpu
        podPO.setGpuCount(1);
        podPO.setGpuType("gpuType");
        podPO.setProductType(podConfiguration.getBidProductType());
        // node && bcc
        podPO.setBccInstanceId("111");
        podPO.setNodeName("nodeName");
        podPO.setDeleted(0);
        podPO.setClientToken(podPO.getPodId());
        podPO.setConditions("");
        List<PodPO> list = new ArrayList<>();
        list.add(podPO);

        // test batchInsertPods
        podDao.batchInsertPods(list);
        podDao.batchUpdateStatus(list);
        PodPO pod = podDao.getPodDetail("*********", "p-" + podId + "_insert111111");
        Assert.assertEquals("gpuType", pod.getGpuType());
        Assert.assertEquals(podConfiguration.getBidProductType(), pod.getProductType());
        Assert.assertEquals("111", pod.getBccInstanceId());
        Assert.assertEquals("nodeName", pod.getNodeName());
        Assert.assertEquals("RUNNING", pod.getPreemptStatus());
        Assert.assertEquals("Succeeded", pod.getStatus());
        Assert.assertEquals(0, pod.getDeleted());

        // test list
        List<PodPO> podPOs = podDao.listBidRunningPods(PreemptStatus.RUNNING.toString(), 
                                                        podConfiguration.getBidProductType(), 
                                                        -podConfiguration.getBidProtectedPeriodMin());
        Assert.assertEquals(0, podPOs.size());
    }

    @Test 
    public void testListBidRunningPods() throws InterruptedException {
        PodPO podPO = new PodPO();
        podPO.setPodId("p-" + podId + "_insert");
        podPO.setUserId("*********");
        podPO.setPodUuid(POD_UUID);
        podPO.setStatus("Succeed");
        // gpu
        podPO.setGpuCount(1);
        podPO.setGpuType("gpuType");
        podPO.setProductType(podConfiguration.getBidProductType());
        // node && bcc
        podPO.setBccInstanceId("111");
        podPO.setNodeName("nodeName");
        podPO.setDeleted(0);
        podPO.setClientToken(podPO.getPodId());
        podPO.setConditions("");
        List<PodPO> list = new ArrayList<>();
        list.add(podPO);

        // test batchInsertPods
        podDao.batchInsertPods(list);
        PodPO pod = podDao.getPodDetail("*********", "p-" + podId + "_insert");
        Assert.assertTrue((pod.getGpuCount() - 1) < FLOATPRECISION);
        Assert.assertEquals("gpuType", pod.getGpuType());
        Assert.assertEquals(podConfiguration.getBidProductType(), pod.getProductType());
        Assert.assertEquals("", pod.getBccInstanceId());
        Assert.assertEquals("", pod.getNodeName());
        Assert.assertEquals("RUNNING", pod.getPreemptStatus());
        Assert.assertEquals("Succeed", pod.getStatus());

        // test batchupdateStatus
        podPO.setStatus("Running");
        podDao.batchUpdateStatus(list);
        pod = podDao.getPodDetail("*********", "p-" + podId + "_insert");
        Assert.assertTrue((pod.getGpuCount() - 1) < FLOATPRECISION);
        Assert.assertEquals("gpuType", pod.getGpuType());
        Assert.assertEquals(podConfiguration.getBidProductType(), pod.getProductType());
        Assert.assertEquals("111", pod.getBccInstanceId());
        Assert.assertEquals("nodeName", pod.getNodeName());
        Assert.assertEquals("RUNNING", pod.getPreemptStatus());
        Assert.assertEquals("Succeed", pod.getStatus());

        // test listBidRunningPods
        List<PodPO> podPOs = podDao.listBidRunningPods(PreemptStatus.RUNNING.toString(), 
                                                        podConfiguration.getBidProductType(), 
                                                        -podConfiguration.getBidProtectedPeriodMin());
        List<String> bidPodList = new ArrayList<>();
        for (PodPO p : podPOs) {
            bidPodList.add(p.getBccInstanceId());
        }
        Assert.assertEquals("111", bidPodList.get(0));

        // test listPreemptBidPods
        List<PodPO> pods = podDao.listPreemptBidPods(bidPodList, PreemptStatus.RUNNING.toString());
        Assert.assertEquals(PreemptStatus.RUNNING.toString(), pods.get(0).getPreemptStatus());

        // test updatePreemptedStatusByInstanceId
        TimeUnit.SECONDS.sleep(5);
        podDao.updatePreemptedStatusByInstanceId(bidPodList, PreemptStatus.RUNNING.toString(), 
                                                PreemptStatus.TO_BE_PREEMPTED.toString());
        pods = podDao.listPreemptBidPods(bidPodList, PreemptStatus.TO_BE_PREEMPTED.toString());
        Assert.assertEquals(PreemptStatus.TO_BE_PREEMPTED.toString(), pods.get(0).getPreemptStatus());

        // test listToBePreemptBidPods
        pods = podDao.listToBePreemptBidPods(PreemptStatus.TO_BE_PREEMPTED.toString(), 
                                                -podConfiguration.getBidPreemtWaitSec());
        Assert.assertEquals(PreemptStatus.TO_BE_PREEMPTED.toString(), pods.get(0).getPreemptStatus());
        Assert.assertEquals("*********", pods.get(0).getUserId());

        // test batchUpdatePreemptedPod
        List<String> podIds = new ArrayList<>();
        podIds.add(pods.get(0).getPodId());
        podDao.batchUpdatePreemptedPod(podIds, BciStatus.SUCCEEDED.getStatus(), PreemptStatus.PREEMPTED.toString());

        // test listPreemtBidPods
        pods = podDao.listPreemptedBidPods(BciStatus.SUCCEEDED.getStatus(),
                                            PreemptStatus.PREEMPTED.toString(), 
                                            -podConfiguration.getBidPreemptedClearHour());
        Assert.assertEquals("*********", pods.get(0).getUserId());
        Assert.assertEquals(PreemptStatus.PREEMPTED.toString(), pods.get(0).getPreemptStatus());
        Assert.assertEquals(BciStatus.SUCCEEDED.getStatus(), pods.get(0).getStatus());
    }

    @Test
    public void listPodTest() {
        Assert.assertTrue(podDao.listAllPodUuids(ACCOUNT_ID).size() > 0);
    }

    @Test
    public void mapPodTest() {
        Map<String, String> map = podDao.podIdMap(ACCOUNT_ID);
        Assert.assertTrue(map.size() > 0);
    }


    @Test
    public void listPodByOrderTest() {
        List<PodPO> list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void updateStatusTest() {
        List<PodPO> list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
        PodPO podPO = list.get(0);
        podPO.setStatus(status);
        podPO.setInternalIp("************");
        podDao.batchUpdateStatus(list);
    }

    @Test
    public void testListPodsWhichNotSyncDsContainersToK8S() {
        String userid= "*********";
        PodPO podPO1 = new PodPO();
        podPO1.setPodId("p-daemonset-test-list-1");
        podPO1.setUserId(userid);
        podPO1.setPodUuid("p-daemonset-test-list-1");
        podPO1.setStatus("Succeed");
        podPO1.setDsContainersVersion(1L);
        podPO1.setDsContainersSyncedToK8S(false);
        podPO1.setClientToken(podPO1.getPodId());
        podPO1.setConditions("");
        PodPO podPO2 = new PodPO();
        podPO2.setPodId("p-daemonset-test-list-2");
        podPO2.setUserId(userid);
        podPO2.setPodUuid("p-daemonset-test-list-2");
        podPO2.setStatus("Running");
        podPO2.setDsContainersVersion(1L);
        podPO2.setDsContainersSyncedToK8S(true);
        podPO2.setClientToken(podPO2.getPodId());
        podPO2.setConditions("");
        PodPO podPO3 = new PodPO();
        podPO3.setPodId("p-daemonset-test-list-3");
        podPO3.setUserId(userid);
        podPO3.setPodUuid("p-daemonset-test-list-3");
        podPO3.setStatus("Pending");
        podPO3.setDsContainersVersion(1L);
        podPO3.setDsContainersSyncedToK8S(false);
        podPO3.setClientToken(podPO3.getPodId());
        podPO3.setConditions("");
        List<PodPO> list = new ArrayList<>();
        list.add(podPO1);
        list.add(podPO2);
        list.add(podPO3);
        podDao.batchInsertPods(list);
    
        List<PodPO> result1 = podDao.listPodsWhichNotSyncDsContainersToK8S();
        Assert.assertEquals(result1.size(), 1);
        Assert.assertEquals(result1.get(0).getPodId(), "p-daemonset-test-list-3");
    }

    @Test
    public void testBatchMarkDsConatinersSynced() {
        String userid= "*********";
        PodPO podPO1 = new PodPO();
        podPO1.setPodId("p-daemonset-test-mark-1");
        podPO1.setUserId(userid);
        podPO1.setPodUuid("p-daemonset-test-mark-1");
        podPO1.setStatus("Succeed");
        podPO1.setClientToken(podPO1.getPodId());
        podPO1.setConditions("");
        PodPO podPO2 = new PodPO();
        podPO2.setPodId("p-daemonset-test-mark-2");
        podPO2.setUserId(userid);
        podPO2.setPodUuid("p-daemonset-test-mark-2");
        podPO2.setStatus("Running");
        podPO2.setClientToken(podPO2.getPodId());
        podPO2.setConditions("");
        PodPO podPO3 = new PodPO();
        podPO3.setPodId("p-daemonset-test-mark-3");
        podPO3.setUserId(userid);
        podPO3.setPodUuid("p-daemonset-test-mark-3");
        podPO3.setStatus("Pending");
        podPO3.setClientToken(podPO3.getPodId());
        podPO3.setConditions("");
        List<PodPO> list = new ArrayList<>();
        list.add(podPO1);
        list.add(podPO2);
        list.add(podPO3);
        podDao.batchInsertPods(list);

        // 新插入的 podPO，dsContainersSyncedToK8S全是true
        Assert.assertTrue(podDao.getPodDetail(userid, "p-daemonset-test-mark-1").isDsContainersSyncedToK8S());
        Assert.assertTrue(podDao.getPodDetail(userid, "p-daemonset-test-mark-2").isDsContainersSyncedToK8S());
        Assert.assertTrue(podDao.getPodDetail(userid, "p-daemonset-test-mark-3").isDsContainersSyncedToK8S());

        // dsContainersSyncedToK8S全部改成false
        List<PodPO> pods = podDao.listAllPods();
        for (PodPO pod : pods) {
            if (pod.getPodId().startsWith("p-daemonset-test-mark-")) {
                pod.setDsContainersSyncedToK8S(false);
                podDao.updatePod(pod);
            }
        }
        Assert.assertFalse(podDao.getPodDetail(userid, "p-daemonset-test-mark-1").isDsContainersSyncedToK8S());
        Assert.assertFalse(podDao.getPodDetail(userid, "p-daemonset-test-mark-2").isDsContainersSyncedToK8S());
        Assert.assertFalse(podDao.getPodDetail(userid, "p-daemonset-test-mark-3").isDsContainersSyncedToK8S());

        // 批量更新 dsContainersSyncedToK8S 为 true
        podDao.batchMarkDsConatinersSynced(Arrays.asList(podPO1, podPO3));
        Assert.assertTrue(podDao.getPodDetail(userid, "p-daemonset-test-mark-1").isDsContainersSyncedToK8S());
        Assert.assertFalse(podDao.getPodDetail(userid, "p-daemonset-test-mark-2").isDsContainersSyncedToK8S());
        Assert.assertTrue(podDao.getPodDetail(userid, "p-daemonset-test-mark-3").isDsContainersSyncedToK8S());

        podDao.batchMarkDsConatinersSynced(new ArrayList<PodPO>());
        Assert.assertTrue(podDao.getPodDetail(userid, "p-daemonset-test-mark-1").isDsContainersSyncedToK8S());
        Assert.assertFalse(podDao.getPodDetail(userid, "p-daemonset-test-mark-2").isDsContainersSyncedToK8S());
        Assert.assertTrue(podDao.getPodDetail(userid, "p-daemonset-test-mark-3").isDsContainersSyncedToK8S());
    }

    @Test
    public void insertTest() {
        PodPO podPO = new PodPO();
        podPO.setPodId("p-" + podId + "_insert111");
        podPO.setUserId(ACCOUNT_ID);
        podPO.setPodUuid(POD_UUID);
        podPO.setStatus("Pending");
        podPO.setConfigFile("");
        podPO.setGpuCount(10);
        podPO.setGpuType("gpuType");
        podPO.setCpuType("cpuType");
        podPO.setClientToken(podPO.getPodId());
        podPO.setConditions("");
        List<PodPO> list = new ArrayList<>();
        list.add(podPO);
        podDao.batchInsertPods(list);

        List<String> uuids = podDao.listAllPodUuids(ACCOUNT_ID);

        PodPO podPOQueryRes = podDao.getPodById("p-" + podId + "_insert111");
        Assert.assertEquals(podPOQueryRes.getGpuType(), "gpuType");
        Assert.assertTrue(Math.abs(podPOQueryRes.getGpuCount() - 10) < FLOATPRECISION);

        podPOQueryRes = podDao.getPodDetail(ACCOUNT_ID, "p-" + podId + "_insert111");
        Assert.assertEquals(podPOQueryRes.getGpuType(), "gpuType");
        Assert.assertTrue(Math.abs(podPOQueryRes.getGpuCount() - 10) < FLOATPRECISION);
    }

    @Test
    public void getPodDetailTest() {
        PodPO podPO = podDao.getPodDetail(ACCOUNT_ID, POD_ID);
        Assert.assertTrue(podPO != null);
    }

    @Test
    public void listPodsByMultiKeyTest() {
        PodFilterQueryModel podFilterQueryModel = new PodFilterQueryModel("podId", "123");
        List<PodFilterQueryModel> list = new ArrayList<>();
        list.add(podFilterQueryModel);
        Assert.assertTrue(
                podDao.listPodsByMultiKey(ACCOUNT_ID, listModel, list).size() ==  0);
    }

    @Test
    public void listPodsByMultiKeyAndIDTest() {
        PodFilterQueryModel podFilterQueryModel1 = new PodFilterQueryModel("podId", "123");
        PodFilterQueryModel podFilterQueryModel2 = new PodFilterQueryModel("test", "123");
        PodFilterQueryModel podFilterQueryModel3 = new PodFilterQueryModel("cceId", "cce-test");
        PodFilterQueryModel podFilterQueryModel4 = new PodFilterQueryModel("name", "test-pod-name");
        List<PodFilterQueryModel> list = new ArrayList<>();
        BigInteger id = new BigInteger("0");
        Assert.assertTrue(podDao.listPodsByMultiKeyAndID(ACCOUNT_ID, listModel,  list, id).size() != 0);
        list.add(podFilterQueryModel1);
        list.add(podFilterQueryModel2);
        list.add(podFilterQueryModel3);
        list.add(podFilterQueryModel4);
        OrderModel orderModel = new OrderModel();
        orderModel.setOrder("desc");
        orderModel.setOrderBy("name");
        List<OrderModel> orderModels = new ArrayList<>();
        orderModels.add(orderModel);
        PodListModel myListModel = new PodListModel();
        myListModel.setOrders(orderModels);
        Assert.assertTrue(podDao.listPodsByMultiKeyAndID(ACCOUNT_ID, myListModel, list, id).size() ==  0);
    }

    @Test
    public void queryPodCountByMultiKeyTest() {

        PodFilterQueryModel podFilterQueryModel = new PodFilterQueryModel("podId", POD_ID);
        List<PodFilterQueryModel> list = new ArrayList<>();
        list.add(podFilterQueryModel);
        Assert.assertTrue(
                podDao.queryPodCountByMultiKey(ACCOUNT_ID, listModel,  list) > 0);
    }

    @Test
    public void queryPodUuidTest() {
        Assert.assertTrue(podDao.queryPodUuid(POD_ID) != null);
    }

    @Test
    public void deletePodTest() {
        podDao.deletePod(ACCOUNT_ID, POD_ID);
    }

    @Test
    public void listByStatusTest() {
        Assert.assertTrue(podDao.listByStatus("Pending").size() > 0);
    }

    @Test
    public void updateTest() {
        List<PodPO> list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
//        PodPO podPO = list.get(0);
//        podPO.setStatus(status);
//        podPO.setInternalIp("************");
//        podPO.setUpdatedTime(new Timestamp(1557264980L * 1000));
        podDao.batchUpdateStatus(list);

        list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
    }

    @Test
    public void updateSinceTest() {
        Timestamp timestamp = podDao.updateSince();
        LOGGER.info(timestamp.toString());
    }

}
