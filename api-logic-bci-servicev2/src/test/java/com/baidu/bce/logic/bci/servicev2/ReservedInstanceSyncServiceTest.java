package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.sync.service.ReservedInstanceSyncService;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.internalsdk.order.OrderClient;
import endpoint.EndpointManager;
import mockit.Mock;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class, EndpointManager.class})
public class ReservedInstanceSyncServiceTest {
    
    @Autowired
    private TestUtil testUtil;

    @Autowired
    DatabaseUtil databaseUtil;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    ReservedInstanceDao reservedInstanceDao;

    @Autowired
    private ReservedInstanceSyncService reservedInstanceSyncService;

    @Before
    public void setUp() throws IllegalAccessException, SQLException {
        testUtil.setUp();
    }

    @Test
    public void schedulerTest() {
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        reservedInstancePO.setOrderId("080ac425c0864acea046af31f4f7ead4");
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        reservedInstancePO.setOrderId("080ac425c0864acea046af31f4f7ead5");
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        reservedInstanceSyncService.syncReservedInstanceInBuild();
        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        
        reservedInstancePO = initReservedInstancePO();
        reservedInstancePO.setStatus(ReservedInstancePO.Status.INACTIVE);
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        reservedInstancePO = initReservedInstancePO();
        reservedInstancePO.setStatus(ReservedInstancePO.Status.ACTIVE);
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        reservedInstanceSyncService.syncCreatedReservedInstance();
    }

    private ReservedInstancePO initReservedInstancePO() {
        ReservedInstancePO reservedInstancePO = new ReservedInstancePO();
        reservedInstancePO.setReservedInstanceId(commonUtils.createExternalId("r"));
        reservedInstancePO.setUserId("2e1be1eb99e946c3a543ec5a4eaa7d39");
        reservedInstancePO.setAccountId("2e1be1eb99e946c3a543ec5a4eaa7d39");
        reservedInstancePO.setName("name");
        reservedInstancePO.setStatus(ReservedInstancePO.Status.CREATING);
        reservedInstancePO.setResourceUuid("acfef5fe-c3ab-425d-bf4f-f5c7b8b99bc0");
        reservedInstancePO.setReservedInstanceUuid("5628b44e-efff-4728-8183-71d63f1e95d0");
        reservedInstancePO.setScope("AZ");
        reservedInstancePO.setPhysicalZone("Azone-gzdt");
        reservedInstancePO.setOrderId("080ac425c0864acea046af31f4f7ead3");
        reservedInstancePO.setPurchaseMode("FullyPrepay");
        reservedInstancePO.setReservedSpec("bci.gna2.c8m36.1a10");
        reservedInstancePO.setReservedInstanceCount(1);
        reservedInstancePO.setReservedTimeUnit("MONTH");
        reservedInstancePO.setReservedTimePeriod(1);
        reservedInstancePO.setAutoRenew(true);
        reservedInstancePO.setAutoRenewTimeUnit("MONTH");
        reservedInstancePO.setAutoRenewTimePeriod(1);
        reservedInstancePO.setEffectiveTime(new Timestamp(System.currentTimeMillis()));
        reservedInstancePO.setExpireTime(ReservedInstancePO.calcExpireTime(reservedInstancePO.getEffectiveTime(), 
                reservedInstancePO.getReservedTimeUnit(), reservedInstancePO.getReservedTimePeriod()));
        return reservedInstancePO;
    }
}
