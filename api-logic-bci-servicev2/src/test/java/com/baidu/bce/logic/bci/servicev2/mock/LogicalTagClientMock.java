package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.DeleteTagAssociationRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.logical.tag.sdk.model.request.CreateTagsRequest;
import org.springframework.stereotype.Component;

import java.util.Arrays;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_UUID;

@Component
public class LogicalTagClientMock extends LogicalTagClient implements ThrowExceptionMock {


    private RuntimeException throwException;

    public LogicalTagClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public void checkTags(CreateTagsRequest request) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public void deleteTagAssociation(DeleteTagAssociationRequest request) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public TagAssociationFulls listFullTags (FullTagListRequest request) {
        if (throwException != null) {
            throw throwException;
        }
        TagAssociationFulls tagAssociationFulls = new TagAssociationFulls();
        TagAssociationFull tagAssociationFull = new TagAssociationFull();
        tagAssociationFull.setTagKey("tag");
        tagAssociationFull.setTagValue("tt");
        tagAssociationFull.setServiceType(PodConstants.SERVICE_TYPE);
        tagAssociationFull.setResourceUuid(POD_UUID);
        tagAssociationFulls.setTagAssociationFulls(Arrays.asList(tagAssociationFull));

        return tagAssociationFulls;
    }

    public void createAndAssignTag(CreateAndAssignTagRequest request) {
    }
}
