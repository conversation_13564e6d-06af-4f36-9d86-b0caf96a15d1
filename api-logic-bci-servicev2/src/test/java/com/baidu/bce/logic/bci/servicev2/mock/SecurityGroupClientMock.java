package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.externalsdk.logical.network.securitygroup.SecurityGroupClient;
import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SecurityGroupSimpleInstancesVO;
import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.SECURITY_GROUP;

@Component
public class SecurityGroupClientMock extends SecurityGroupClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public SecurityGroupClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public SecurityGroupSimpleInstancesVO getSimpleSecurityGroupListByIds(List<String> ids) {
        if (throwException != null) {
            throw throwException;
        }
        SecurityGroupSimpleInstancesVO securityGroupSimpleInstancesVO = new SecurityGroupSimpleInstancesVO();
        SimpleSecurityGroupVO simpleSecurityGroupVO = new SimpleSecurityGroupVO();
        simpleSecurityGroupVO.setSecurityGroupId(SECURITY_GROUP);
        List<SimpleSecurityGroupVO> list = new ArrayList<>();
        list.add(simpleSecurityGroupVO);
        securityGroupSimpleInstancesVO.setList(list);

        return securityGroupSimpleInstancesVO;
    }
}
