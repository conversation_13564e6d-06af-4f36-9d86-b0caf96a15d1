package com.baidu.bce.logic.bci.servicev2.util;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.lang.reflect.Field;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCheck;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCheckAspect;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache.Version;
import com.baidu.bce.logic.core.user.LogicUserService;


@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class VersionCheckAspectTest {

    @InjectMocks
    private VersionCheckAspect versionCheckAspect;

    @Mock
    private PodServiceV2 podService;

    @Mock
    private VersionCache versionCache;
    
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public  void testVersionCheckEnable() throws Throwable {
        // 允许递归mock
        ProceedingJoinPoint mockPoint = mock(ProceedingJoinPoint.class, Mockito.RETURNS_DEEP_STUBS);
        Object expected = new Object();
        when(mockPoint.proceed(mockPoint.getArgs())).thenReturn(expected);
        // 递归mock
        when(mockPoint.getTarget().getClass().getSimpleName()).thenReturn("MockedClass");
        when(mockPoint.getSignature().getName()).thenReturn("MockedMethod");
        // 强改私有变量
        Field field = VersionCheckAspect.class.getDeclaredField("enable"); // 获取私有变量
        field.setAccessible(true); // 设置为可访问
        field.set(versionCheckAspect, false); // 将target对象中的myField字段设置为newValue

        VersionCheck mockCheck = mock(VersionCheck.class);

        when(podService.getAccountId()).thenReturn("user");
        try {
            Object result = versionCheckAspect.versionCheck(mockPoint, mockCheck);
            assertEquals(result, expected);
        } catch (Exception e){
            assertEquals(true, false);
        }
    }

    @Test
    public void testVersionCheckWhenUserIsEmpty() throws Throwable {
        // 允许递归mock
        ProceedingJoinPoint mockPoint = mock(ProceedingJoinPoint.class, Mockito.RETURNS_DEEP_STUBS);
        Object expected = new Object();
        when(mockPoint.proceed(mockPoint.getArgs())).thenReturn(expected);
        // 递归mock
        when(mockPoint.getTarget().getClass().getSimpleName()).thenReturn("MockedClass");
        when(mockPoint.getSignature().getName()).thenReturn("MockedMethod");
        // 强改私有变量
        Field field = VersionCheckAspect.class.getDeclaredField("enable"); // 获取私有变量
        field.setAccessible(true); // 设置为可访问
        field.set(versionCheckAspect, true); // 将target对象中的myField字段设置为newValue

        VersionCheck mockCheck = mock(VersionCheck.class);

        // 1.user为"", 则抛异常
        when(podService.getAccountId()).thenReturn("");
        try {
            Object result = versionCheckAspect.versionCheck(mockPoint, mockCheck);
            assertEquals(false, true);
        } catch (Exception e){
            assertEquals(e.getMessage(), "version check error, please try again later.");
        }
    }

    @Test
    public void testVersionCheckWhenV1() throws Throwable {
        // 允许递归mock
        ProceedingJoinPoint mockPoint = mock(ProceedingJoinPoint.class, Mockito.RETURNS_DEEP_STUBS);
        Object expected = new Object();
        expected = new String("test");
        when(mockPoint.proceed(mockPoint.getArgs())).thenReturn(expected);
        // 递归mock
        when(mockPoint.getTarget().getClass().getSimpleName()).thenReturn("MockedClass");
        when(mockPoint.getSignature().getName()).thenReturn("MockedMethod");
        // 强改私有变量
        Field field = VersionCheckAspect.class.getDeclaredField("enable"); // 获取私有变量
        field.setAccessible(true); // 设置为可访问
        field.set(versionCheckAspect, true); // 将target对象中的myField字段设置为newValue

        VersionCheck mockCheck = mock(VersionCheck.class);
        when(mockCheck.Version()).thenReturn(Version.V1);

        // 1.user为"user"
        when(podService.getAccountId()).thenReturn("user");
        when(versionCache.getUserVersion("user")).thenReturn(Version.V1);
        try {
            Object result = versionCheckAspect.versionCheck(mockPoint, mockCheck);
            assertEquals(result, expected);
        } catch (Exception e){
            assertEquals(true, false);
            assertEquals(e.getMessage(), "");
        }
    }

    @Test
    public void testVersionCheckWhenV2() throws Throwable {
        // 允许递归mock
        ProceedingJoinPoint mockPoint = mock(ProceedingJoinPoint.class, Mockito.RETURNS_DEEP_STUBS);
        Object expected = new Object();
        expected = new String("test");
        when(mockPoint.proceed(mockPoint.getArgs())).thenReturn(expected);
        // 递归mock
        when(mockPoint.getTarget().getClass().getSimpleName()).thenReturn("MockedClass");
        when(mockPoint.getSignature().getName()).thenReturn("MockedMethod");
        // 强改私有变量
        Field field = VersionCheckAspect.class.getDeclaredField("enable"); // 获取私有变量
        field.setAccessible(true); // 设置为可访问
        field.set(versionCheckAspect, true); // 将target对象中的myField字段设置为newValue

        VersionCheck mockCheck = mock(VersionCheck.class);
        when(mockCheck.Version()).thenReturn(Version.V2);

        // 1.user为"user"
        when(podService.getAccountId()).thenReturn("user");
        when(versionCache.getUserVersion("user")).thenReturn(Version.V2);
        try {
            Object result = versionCheckAspect.versionCheck(mockPoint, mockCheck);
            assertEquals(result, expected);
        } catch (Exception e){
            assertEquals(true, false);
            assertEquals(e.getMessage(), "");
        }
    }

    @Test
    public void testVersionCheckWhenAbnormal() throws Throwable {
        // 允许递归mock
        ProceedingJoinPoint mockPoint = mock(ProceedingJoinPoint.class, Mockito.RETURNS_DEEP_STUBS);
        Object expected = new Object();
        expected = new String("test");
        when(mockPoint.proceed(mockPoint.getArgs())).thenReturn(expected);
        // 递归mock
        when(mockPoint.getTarget().getClass().getSimpleName()).thenReturn("MockedClass");
        when(mockPoint.getSignature().getName()).thenReturn("MockedMethod");
        // 强改私有变量
        Field field = VersionCheckAspect.class.getDeclaredField("enable"); // 获取私有变量
        field.setAccessible(true); // 设置为可访问
        field.set(versionCheckAspect, true); // 将target对象中的myField字段设置为newValue

        VersionCheck mockCheck = mock(VersionCheck.class);
        when(mockCheck.Version()).thenReturn(Version.V2);

        // 1.user为"user"
        when(podService.getAccountId()).thenReturn("user");
        when(versionCache.getUserVersion("user")).thenReturn(Version.ABNORMAL);
        try {
            Object result = versionCheckAspect.versionCheck(mockPoint, mockCheck);
            assertEquals(false, true);
        } catch (Exception e){
            assertEquals(e.getMessage(), "version permission deny, version check fail");
        }
    }

    @Test
    public void testVersionCheckWhenNotEqual() throws Throwable {
        // 允许递归mock
        ProceedingJoinPoint mockPoint = mock(ProceedingJoinPoint.class, Mockito.RETURNS_DEEP_STUBS);
        Object expected = new Object();
        expected = new String("test");
        when(mockPoint.proceed(mockPoint.getArgs())).thenReturn(expected);
        // 递归mock
        when(mockPoint.getTarget().getClass().getSimpleName()).thenReturn("MockedClass");
        when(mockPoint.getSignature().getName()).thenReturn("MockedMethod");
        // 强改私有变量
        Field field = VersionCheckAspect.class.getDeclaredField("enable"); // 获取私有变量
        field.setAccessible(true); // 设置为可访问
        field.set(versionCheckAspect, true); // 将target对象中的myField字段设置为newValue

        VersionCheck mockCheck = mock(VersionCheck.class);
        when(mockCheck.Version()).thenReturn(Version.V1);

        // 1.user为"user"
        when(podService.getAccountId()).thenReturn("user");
        when(versionCache.getUserVersion("user")).thenReturn(Version.V2);
        try {
            Object result = versionCheckAspect.versionCheck(mockPoint, mockCheck);
            assertEquals(false, true);
        } catch (Exception e){
            assertEquals(e.getMessage(), "version permission deny, version check fail");
        }
    }
}