package com.baidu.bce.logic.bci.servicev2.orderexecute;

import static org.junit.Assert.*;

import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.ExecAction;
import com.baidu.bce.logic.bci.servicev2.model.HTTPGetAction;
import com.baidu.bce.logic.bci.servicev2.model.Lifecycle;
import com.baidu.bce.logic.bci.servicev2.model.LifecycleHandler;
import com.baidu.bce.logic.bci.servicev2.model.Probe;
import com.baidu.bce.logic.bci.servicev2.model.TCPSocketAction;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1Container;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

/**
 * HandleContainerLifecycle函数的单元测试类
 * 测试PodNewOrderExecutorServiceV2中的handleContainerLifecycle方法
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class HandleContainerLifecycleTest {

    @InjectMocks
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        podNewOrderExecutorServiceV2 = PowerMockito.spy(new PodNewOrderExecutorServiceV2());

        // 设置配置参数
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckMaxRetries", 20);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckRetryInterval", 3);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckInitialWait", 5);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartReadinessMaxRetries", 12);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartReadinessRetryInterval", 2);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartBasicWait", 5);
    }

    /**
     * 测试handleContainerLifecycle方法 - 基本功能测试
     * 当容器没有优先级设置时，不应该设置特殊的lifecycle配置
     */
    @Test
    public void testHandleContainerLifecycle_NoPriority() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, null, null, false);

        // Assert
        assertNotNull(container.getLifecycle());
        // 没有优先级时，不应该有postStart hook
        assertNull(container.getLifecycle().getPostStart());
    }

    /**
     * 测试handleContainerLifecycle方法 - 有下一个优先启动容器
     * 应该配置postStart hook
     */
    @Test
    public void testHandleContainerLifecycle_WithNextPriorityContainer() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, null, null, true);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPostStart());
        assertNotNull(container.getLifecycle().getPostStart().getExec());

        List<String> command = container.getLifecycle().getPostStart().getExec().getCommand();
        assertNotNull(command);
        assertEquals(3, command.size());
        assertEquals("/usr/execbin/exec", command.get(0));
        assertEquals("-command", command.get(1));
        assertTrue(command.get(2).contains("sleep 5")); // 包含基础等待时间
    }

    /**
     * 测试handleContainerLifecycle方法 - 带ReadinessProbe的postStart配置
     */
    @Test
    public void testHandleContainerLifecycle_WithReadinessProbe() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // 设置HTTP readiness probe
        Probe readinessProbe = new Probe();
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setPort(8080);
        httpGet.setPath("/health");
        readinessProbe.setHttpGet(httpGet);
        containerPurchase.setReadinessProbe(readinessProbe);

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, null, null, true);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPostStart());

        List<String> command = container.getLifecycle().getPostStart().getExec().getCommand();
        String commandStr = command.get(2);
        assertTrue(commandStr.contains("sleep 5")); // 基础等待
        assertTrue(commandStr.contains("curl")); // readiness检查命令
        assertTrue(commandStr.contains("8080")); // 端口
        assertTrue(commandStr.contains("/health")); // 路径
    }

    /**
     * 测试handleContainerLifecycle方法 - 带用户自定义postStart命令
     */
    @Test
    public void testHandleContainerLifecycle_WithUserPostStartCommand() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // 设置用户自定义的lifecycle
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler postStart = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'user poststart command'"));
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);
        containerPurchase.setLifecycle(lifecycle);

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, null, null, true);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPostStart());

        List<String> command = container.getLifecycle().getPostStart().getExec().getCommand();
        String commandStr = command.get(2);
        assertTrue(commandStr.contains("echo 'user poststart command'"));
    }

    /**
     * 测试handleContainerLifecycle方法 - 需要退出优先级控制
     */
    @Test
    public void testHandleContainerLifecycle_WithExitPriority() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");
        ContainerPurchase nextContainer = createTestContainerPurchase("next-container");

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, preContainer, nextContainer, false);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPreStop());
        assertNotNull(container.getLifecycle().getPreStop().getExec());

        List<String> command = container.getLifecycle().getPreStop().getExec().getCommand();
        assertNotNull(command);
        assertEquals(3, command.size());
        assertEquals("/usr/execbin/exec", command.get(0));
        assertEquals("-command", command.get(1));
        // preStop命令应该包含退出优先级逻辑
        assertTrue(command.get(2).length() > 0);
    }

    /**
     * 测试handleContainerLifecycle方法 - 带用户自定义preStop命令
     */
    @Test
    public void testHandleContainerLifecycle_WithUserPreStopCommand() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // 设置用户自定义的preStop
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'user prestop command'"));
        preStop.setExec(exec);
        lifecycle.setPreStop(preStop);
        containerPurchase.setLifecycle(lifecycle);

        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, preContainer, null, false);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPreStop());

        List<String> command = container.getLifecycle().getPreStop().getExec().getCommand();
        String commandStr = command.get(2);
        assertTrue(commandStr.contains("echo 'user prestop command'"));
    }

    /**
     * 测试handleContainerLifecycle方法 - 保持用户preStop配置
     */
    @Test
    public void testHandleContainerLifecycle_PreserveUserPreStop() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // 设置用户自定义的preStop，但没有退出优先级需求
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'preserve this command'"));
        preStop.setExec(exec);
        lifecycle.setPreStop(preStop);
        containerPurchase.setLifecycle(lifecycle);

        // Act - 没有前置和后续容器，不需要退出优先级
        invokeHandleContainerLifecycle(container, containerPurchase, null, null, false);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPreStop());

        List<String> command = container.getLifecycle().getPreStop().getExec().getCommand();
        assertEquals(Arrays.asList("/bin/sh", "-c", "echo 'preserve this command'"), command);
    }

    /**
     * 测试handleContainerLifecycle方法 - 异常处理
     */
    @Test
    public void testHandleContainerLifecycle_ExceptionHandling() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = null; // 故意传入null

        // Act & Assert
        try {
            invokeHandleContainerLifecycle(container, containerPurchase, null, null, false);
            // 如果没有抛出异常，说明方法能正确处理null输入
        } catch (Exception e) {
            // 预期可能会有异常，这是正常的
            assertNotNull(e);
        }
    }

    /**
     * 测试handleContainerLifecycle方法 - 带TCP Socket探针
     */
    @Test
    public void testHandleContainerLifecycle_WithTCPSocketProbe() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // 设置TCP socket readiness probe
        Probe readinessProbe = new Probe();
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(3306);
        readinessProbe.setTcpSocket(tcpSocket);
        containerPurchase.setReadinessProbe(readinessProbe);

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, null, null, true);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPostStart());

        List<String> command = container.getLifecycle().getPostStart().getExec().getCommand();
        String commandStr = command.get(2);
        assertTrue(commandStr.contains("nc")); // netcat命令
        assertTrue(commandStr.contains("3306")); // 端口
    }

    /**
     * 测试handleContainerLifecycle方法 - 带Exec探针
     */
    @Test
    public void testHandleContainerLifecycle_WithExecProbe() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // 设置Exec readiness probe
        Probe readinessProbe = new Probe();
        ExecAction execAction = new ExecAction();
        execAction.setCommand(Arrays.asList("/bin/sh", "-c", "test -f /ready"));
        readinessProbe.setExec(execAction);
        containerPurchase.setReadinessProbe(readinessProbe);

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, null, null, true);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPostStart());

        List<String> command = container.getLifecycle().getPostStart().getExec().getCommand();
        String commandStr = command.get(2);
        assertTrue(commandStr.contains("test -f /ready")); // exec命令
    }

    /**
     * 测试handleContainerLifecycle方法 - 同时有postStart和preStop
     */
    @Test
    public void testHandleContainerLifecycle_BothPostStartAndPreStop() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        container.setName("test-container");

        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // 设置用户自定义的lifecycle
        Lifecycle lifecycle = new Lifecycle();

        LifecycleHandler postStart = new LifecycleHandler();
        ExecAction postStartExec = new ExecAction();
        postStartExec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'user poststart'"));
        postStart.setExec(postStartExec);
        lifecycle.setPostStart(postStart);

        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction preStopExec = new ExecAction();
        preStopExec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'user prestop'"));
        preStop.setExec(preStopExec);
        lifecycle.setPreStop(preStop);

        containerPurchase.setLifecycle(lifecycle);

        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");

        // Act
        invokeHandleContainerLifecycle(container, containerPurchase, preContainer, null, true);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPostStart());
        assertNotNull(container.getLifecycle().getPreStop());

        // 检查postStart命令
        List<String> postStartCommand = container.getLifecycle().getPostStart().getExec().getCommand();
        String postStartCommandStr = postStartCommand.get(2);
        assertTrue(postStartCommandStr.contains("echo 'user poststart'"));

        // 检查preStop命令
        List<String> preStopCommand = container.getLifecycle().getPreStop().getExec().getCommand();
        String preStopCommandStr = preStopCommand.get(2);
        assertTrue(preStopCommandStr.contains("echo 'user prestop'"));
    }

    /**
     * 创建测试用的ContainerPurchase对象
     */
    private ContainerPurchase createTestContainerPurchase(String name) {
        ContainerPurchase containerPurchase = new ContainerPurchase();
        containerPurchase.setName(name);
        containerPurchase.setContainerType(ContainerType.WORKLOAD.getType());
        return containerPurchase;
    }

    /**
     * 通过反射调用handleContainerLifecycle私有方法
     */
    private void invokeHandleContainerLifecycle(V1Container container, ContainerPurchase containerPurchase, ContainerPurchase preContainer, ContainerPurchase nextContainer, Boolean hasNextPriorityContainer) throws Exception {
        Method method = PodNewOrderExecutorServiceV2.class.getDeclaredMethod("handleContainerLifecycle", V1Container.class, ContainerPurchase.class, ContainerPurchase.class, ContainerPurchase.class, Boolean.class);
        method.setAccessible(true);
        method.invoke(podNewOrderExecutorServiceV2, container, containerPurchase, preContainer, nextContainer, hasNextPriorityContainer);
    }
} 