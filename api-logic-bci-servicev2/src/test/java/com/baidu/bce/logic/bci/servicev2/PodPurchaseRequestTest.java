package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageRegistryCredential;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodPurchaseRequestTest {
    @Test
    public void podPurchaseRequestTest() {
        CreateContainerGroupRequest request = new CreateContainerGroupRequest();
        request.setName("test");
        request.setZoneName(null);
        request.setSecurityGroupIds(null);
        request.setSubnetIds(null);
        // request.setImageRegistryCredentials(new ArrayList<ImageRegistryCredential>());
        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest(request);
        Assert.assertEquals("", podPurchaseRequest.getSubnetIds());
        Assert.assertEquals("", podPurchaseRequest.getSubnetId());
    }
}
