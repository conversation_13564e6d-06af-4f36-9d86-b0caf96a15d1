package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeRollbackDbRequest;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeUpdateDbRequest;
import org.springframework.stereotype.Component;


@Component
public class BCCClientMock extends BCCClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public BCCClientMock() {
        super("endpoint", "accessKey", "secretKey");
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        if (throwException != null) {
            throw throwException;

        }
    }

    public void attachVolume(AttachVolumeUpdateDbRequest attachVolumeRequest) {

    }

    public void rollbackVolume(AttachVolumeRollbackDbRequest rollbackRequest) {

    }
}