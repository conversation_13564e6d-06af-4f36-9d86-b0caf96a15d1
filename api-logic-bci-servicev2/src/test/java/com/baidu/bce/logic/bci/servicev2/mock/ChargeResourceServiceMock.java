package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.billing.resourcemanager.model.ChargeResourceInfo;
import com.baidu.bce.billing.resourcemanager.model.ResourceDetail;
import com.baidu.bce.billing.resourcemanager.model.ResourceIdPair;
import com.baidu.bce.billing.resourcemanager.model.ResourceUseStatusInfo;
import com.baidu.bce.billing.resourcemanager.model.ResourceUsingStatusTimeSpan;
import com.baidu.bce.billing.resourcemanager.model.ResultStatus;
import com.baidu.bce.billing.resourcemanager.model.SubProductTypeInfo;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.request.BatchDestroyRequest;
import com.baidu.bce.billing.resourcemanager.service.request.BatchReleaseRequest;
import com.baidu.bce.billing.resourcemanager.service.request.BatchResRequest;
import com.baidu.bce.billing.resourcemanager.service.request.BatchResRequestV2;
import com.baidu.bce.billing.resourcemanager.service.request.ChargeResourceRequest;
import com.baidu.bce.billing.resourcemanager.service.request.DeleteRequest;
import com.baidu.bce.billing.resourcemanager.service.request.FilterPauseResRequest;
import com.baidu.bce.billing.resourcemanager.service.request.GetResourceIdPairRequest;
import com.baidu.bce.billing.resourcemanager.service.request.GetUsingServiceRequest;
import com.baidu.bce.billing.resourcemanager.service.request.PauseResAfterGivenTimeRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ReleaseRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ResourceDetailListQueryRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ResourceListRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ResourceRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ResourceUseStatusTimeSpanRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ResUuidRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ServiceRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ServiceUpdatedRequest;
import com.baidu.bce.billing.resourcemanager.service.request.StopRequest;
import com.baidu.bce.billing.resourcemanager.service.request.SubProductTypeQueryRequest;
import com.baidu.bce.billing.resourcemanager.service.request.UpdateExpireTimeRequest;
import com.baidu.bce.billing.resourcemanager.service.request.UseStatusBatchQueryRequest;
import com.baidu.bce.billing.resourcemanager.service.request.UseStatusBatchRequest;
import com.baidu.bce.billing.resourcemanager.service.request.UseStatusRequest;
import com.baidu.bce.billing.resourcemanager.service.response.BatchChangeUseStatusResponse;
import com.baidu.bce.billing.resourcemanager.service.response.BatchCheckResponse;
import com.baidu.bce.billing.resourcemanager.service.response.BatchResourceIdPairResponse;
import com.baidu.bce.billing.resourcemanager.service.response.BiddingResResponse;
import com.baidu.bce.billing.resourcemanager.service.response.CheckResponse;
import com.baidu.bce.billing.resourcemanager.service.response.CommonResponse;
import com.baidu.bce.billing.resourcemanager.service.response.GetUsingServiceResponse;
import com.baidu.bce.billing.resourcemanager.service.response.PauseResResponse;
import com.baidu.bce.billing.resourcemanager.service.response.ReleaseResponse;
import com.baidu.bce.billing.resourcemanager.service.response.ResourceDetailListQueryResponse;
import com.baidu.bce.billing.resourcemanager.service.response.ResourceQueryResponse;
import com.baidu.bce.billing.resourcemanager.service.response.Response;
import com.baidu.bce.billing.resourcemanager.service.response.SimpleInfo;
import com.baidu.bce.billing.resourcemanager.service.response.SimpleInfoResponse;
import com.baidu.bce.fbi.common.PageResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.stereotype.Component;

@Component
public class ChargeResourceServiceMock implements ChargeResourceService {

    private static final String start = "start";

    private static final String pause = "pause";

    private final Map<String, String> resourceStartPauseMap = new HashMap<>();

    @Override
    public Response destroy(DeleteRequest deleteRequest) {
        return null;
    }

    @Override
    public Response batchDestroy(BatchDestroyRequest batchDestroyRequest) {
        return null;
    }

    @Override
    public Response stop(StopRequest stopRequest) {
        return null;
    }

    @Override
    public ReleaseResponse release(ReleaseRequest releaseRequest) {
        return null;
    }

    @Override
    public ReleaseResponse batchRelease(BatchReleaseRequest batchReleaseRequest) {
        return null;
    }

    @Override
    public Response cancelRelease(ResourceRequest resourceRequest) {
        return null;
    }

    @Override
    public ResourceDetailListQueryResponse getAliveResourceDetailList(ResourceDetailListQueryRequest resourceDetailListQueryRequest) {
        return null;
    }

    @Override
    public List<String> getUsingServiceList(String s) {
        return null;
    }

    @Override
    public GetUsingServiceResponse getUsingServiceList(GetUsingServiceRequest getUsingServiceRequest) {
        return null;
    }

    @Override
    public Response pause(UseStatusRequest useStatusRequest) {
        resourceStartPauseMap.put(useStatusRequest.getIdentify().getName(), pause);
        Response response = new Response();
        response.setStatus(ResultStatus.SUCCESS);
        return response;
    }

    @Override
    public Response start(UseStatusRequest useStatusRequest) {
        resourceStartPauseMap.put(useStatusRequest.getIdentify().getName(), start);
        Response response = new Response();
        response.setStatus(ResultStatus.SUCCESS);
        return response;
    }

    @Override
    public CheckResponse allowChangeUseStatus(UseStatusRequest useStatusRequest) {
        return null;
    }

    @Override
    public BatchChangeUseStatusResponse batchPause(UseStatusBatchRequest useStatusBatchRequest) {
        return null;
    }

    @Override
    public BatchCheckResponse batchAllowChangeUseStatusCheck(UseStatusBatchRequest useStatusBatchRequest) {
        return null;
    }

    @Override
    public PauseResResponse getPauseResAfterGivenTime(PauseResAfterGivenTimeRequest pauseResAfterGivenTimeRequest) {
        return null;
    }

    @Override
    public List<String> filterPauseRes(FilterPauseResRequest filterPauseResRequest) {
        List<String> resList = new ArrayList<>();
        List<String> nameList = filterPauseResRequest.getNameList();
        for (String name : nameList) {
            if (pause.equals(resourceStartPauseMap.get(name))) {
                resList.add(name);
            }
        }
        return resList;
    }

    @Override
    public List<ResourceUseStatusInfo> getUseStatusList(UseStatusBatchQueryRequest useStatusBatchQueryRequest) {
        return null;
    }

    @Override
    public List<ResourceIdPair> getNameAndShortIdList(String s, String s1, String s2, String s3) {
        return null;
    }

    @Override
    public BatchResourceIdPairResponse batchGetNameAndShortIdList(GetResourceIdPairRequest getResourceIdPairRequest) {
        return null;
    }

    @Override
    public BatchCheckResponse batchBiddingCheck(BatchResRequest batchResRequest) {
        return null;
    }

    @Override
    public SimpleInfoResponse getAliveResSimpleInfo(BatchResRequest batchResRequest) {
        return null;
    }

    @Override
    public PageResult<SimpleInfo> getAliveResSimpleInfoV2(BatchResRequestV2 batchResRequestV2) {
        return null;
    }
    
    @Override
    public BiddingResResponse getAliveBiddingResList(ServiceRequest arg0) {
        return null;
    }

    @Override
    public List<String> getAliveResUuidList(ResUuidRequest resUuidRequest) {
        return null;
    }

    @Override
    public List<ChargeResourceInfo> getChargeResource(ChargeResourceRequest chargeResourceRequest) {
        return null;
    }

    @Override
    public ResourceDetail getAliveResourceDetail(ResourceRequest arg0) {
        ResourceDetail detail = new ResourceDetail();
        detail.setServiceType("BCI");
        detail.setAccountId("2e1be1eb99e946c3a543ec5a4eaa7d39");

        if (arg0.getName() == "ReservedPackage") {
            detail.setSubProductType("ReservedPackage");
        } else {
            detail.setSubProductType("");
        }
        return detail;
    }

    @Override
    public CommonResponse<Boolean> updateExpireTimeByIdentity(UpdateExpireTimeRequest updateExpireTimeRequest) {
        return null;
    }

    @Override
    public Map<String, List<ResourceUsingStatusTimeSpan>> getResourceUseStatusTimeSpan(ResourceUseStatusTimeSpanRequest resourceUseStatusTimeSpanRequest) {
        return null;
    }

    @Override
    public List<SubProductTypeInfo> getServiceSubProductTypeHistory(SubProductTypeQueryRequest arg0) {
        return null;
    }

    @Override
    public BiddingResResponse getUpdatedBiddingResList(ServiceUpdatedRequest arg0) {
        return null;
    }

    @Override
    public ResourceQueryResponse queryList(ResourceListRequest arg0) {
        return null;
    }

    @Override
    public ResourceQueryResponse queryListByUuids(List<String> arg0) {
        return null;
    }
}
