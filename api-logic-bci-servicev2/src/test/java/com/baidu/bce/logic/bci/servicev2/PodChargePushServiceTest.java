package com.baidu.bce.logic.bci.servicev2;


import com.baidu.bce.billing.proxy.model.v1.LegacyMetricData;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.charge.service.PodChargePushServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodChargePushServiceTest {

    @Autowired
    private PodChargePushServiceV2 podChargePushService;


    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
    }

    @Test
    public void pushChargeTest() {
        podChargePushService.pushChargeTask();
    }

    @Test
    public void generateMetricDatasTest() {
        List<PodPO> podPOS = new ArrayList<>();
        PodPO podPO = new PodPO();
        podPO.setvCpu(14);
        podPO.setMemory(120);
        podPO.setGpuCount(1);
        podPO.setGpuType("Nvidia A100 Nvswitch-80g");
        podPO.setPodUuid("askdjlkasjdl");
        podPOS.add(podPO);

        PodPO podPO1 = new PodPO();
        podPO1.setvCpu(14);
        podPO1.setMemory(120);
        podPO1.setGpuCount(0);
        podPO1.setGpuType("Nvidia A100 Nvswitch-80g");
        podPO1.setPodUuid("askdjlkasjal");
        podPOS.add(podPO1);

        PodPO podPO2 = new PodPO();
        podPO2.setvCpu(14);
        podPO2.setMemory(120);
        podPO2.setGpuCount(1);
        podPO2.setGpuType("Nvidia A10 Nvswitch-80g");
        podPO2.setPodUuid("askdjlkasjal");
        podPOS.add(podPO2);

        Timestamp chargeTime = new Timestamp(60000l);
        Map<PodPO, Long> podUsage = new HashMap<>();
        podUsage.put(podPO, 60000l);
        List<LegacyMetricData> legacyMetricData =
                podChargePushService.generateMetricDatas(podPOS, chargeTime, podUsage);
    }
}
