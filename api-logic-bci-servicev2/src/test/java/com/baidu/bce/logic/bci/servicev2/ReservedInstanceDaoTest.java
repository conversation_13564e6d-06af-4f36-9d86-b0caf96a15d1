package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.request.ListRequest;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("SpringJavaAutowiringInspection")
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class ReservedInstanceDaoTest {
    @Autowired
    DatabaseUtil databaseUtil;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    ReservedInstanceDao reservedInstanceDao;

    @Before
    public void resetDatabase() throws Exception {
    }

    @Test
    public void batchInsertReservedInstanceTest() {
        List<ReservedInstancePO> reservedInstancePOList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            ReservedInstancePO reservedInstancePO = initReservedInstancePO();
            reservedInstancePO.setReservedInstanceId(commonUtils.createExternalId("r"));
            reservedInstancePOList.add(reservedInstancePO);
        }
        reservedInstanceDao.batchInsertReservedInstance(reservedInstancePOList);

        ListRequest listRequest = new ListRequest();
        listRequest.setPageNo(0);
        listRequest.setPageSize(5);
        List<ReservedInstancePO> reservedInstancePOs = reservedInstanceDao.listReservedInstancesByMultiKey(
                reservedInstancePOList.get(0).getAccountId(), listRequest);
        Assert.assertEquals(reservedInstancePOs.size(), 5);
        int reservedInstanceCount = reservedInstanceDao.getReservedInstanceNumByMultiKey(
                reservedInstancePOList.get(0).getAccountId(), listRequest);
        Assert.assertEquals(reservedInstanceCount, 10);

        List<String> reservedInstanceIds = new ArrayList<>();
        String reservedInstanceId = reservedInstancePOList.get(0).getReservedInstanceId();
        String accountId = reservedInstancePOList.get(0).getAccountId();
        String orderId = "test-orderid";
        String status = ReservedInstancePO.Status.INACTIVE;
        reservedInstanceIds.add(reservedInstanceId);
        reservedInstanceDao.updateOrderId(reservedInstanceIds, accountId, orderId);
        reservedInstanceDao.updateStatus(accountId, reservedInstanceId, status);
        reservedInstancePOs = reservedInstanceDao.listCreatedReservedInstances();
        Assert.assertEquals(reservedInstancePOs.size(), 1);
        Assert.assertEquals(reservedInstancePOs.get(0).getStatus(), status);
        reservedInstancePOs = reservedInstanceDao.listReservedInstancesByOrderId(orderId);
        Assert.assertEquals(reservedInstancePOs.size(), 1);
        Assert.assertEquals(reservedInstancePOs.get(0).getOrderId(), orderId);

        status = ReservedInstancePO.Status.ACTIVE;
        String resourceUuid = "resource_uuid";
        String reservedInstanceUuid = "reserved_instance_uuid";
        orderId = "test-orderid2";
        List<String> reservedInstanceUuids = new ArrayList<>();
        reservedInstanceUuids.add(reservedInstanceUuid);
        reservedInstancePOs.get(0).setStatus(status);
        reservedInstancePOs.get(0).setResourceUuid(resourceUuid);
        reservedInstancePOs.get(0).setReservedInstanceUuid(reservedInstanceUuid);
        reservedInstanceDao.batchUpdateUuid(reservedInstancePOs);
        reservedInstanceDao.batchUpdateStatus(reservedInstancePOs);
        reservedInstanceDao.batchUpdateResourceUuid(reservedInstancePOs);
        reservedInstanceDao.updateOrderIdByUuid(reservedInstanceUuids, orderId);
        reservedInstancePOs = reservedInstanceDao.listCreatedReservedInstances();
        Assert.assertEquals(reservedInstancePOs.size(), 1);
        Assert.assertEquals(reservedInstancePOs.get(0).getReservedInstanceUuid(), reservedInstanceUuid);
        Assert.assertEquals(reservedInstancePOs.get(0).getResourceUuid(), resourceUuid);
        Assert.assertEquals(reservedInstancePOs.get(0).getStatus(), status);
        Assert.assertEquals(reservedInstancePOs.get(0).getOrderId(), orderId);
        reservedInstanceDao.updateOrderIdByResourceUuid(reservedInstanceUuids, orderId);
        reservedInstanceDao.deleteByReservedInstanceUuid(reservedInstanceUuid);
        
        listRequest.setPageSize(100);
        reservedInstancePOs = reservedInstanceDao.listReservedInstancesByMultiKey(
                reservedInstancePOList.get(0).getAccountId(), listRequest);
        for (ReservedInstancePO reservedInstancePO : reservedInstancePOs) {
            reservedInstanceDao.delete(reservedInstancePO.getId());
        }
    }

    @Test 
    public void insertReservedInstanceTest() {
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        List<ReservedInstancePO> reservedInstancePOs = reservedInstanceDao.listReservedInstancesInBuild();
        Assert.assertEquals(reservedInstancePOs.size(), 1);
        Assert.assertEquals(reservedInstancePOs.get(0).getReservedInstanceId(), 
                reservedInstancePO.getReservedInstanceId());
        reservedInstanceDao.delete(reservedInstancePOs.get(0).getId());
    }

    // @Test
    // public void calcExpireTimeTest() {
    //     Timestamp ts = new Timestamp(System.currentTimeMillis());
    //     // 1/30 add one month is 2/28，闰年是2/29
    //     ts.setMonth(0);
    //     ts.setDate(30);
    //     Timestamp expireTime = ReservedInstancePO.calcExpireTime(ts, "MONTH", 1);
    //     Assert.assertEquals(1, expireTime.getMonth());
    //     Assert.assertEquals(29, expireTime.getDate());

    //     // 5/1 add one month is 6/1
    //     ts.setMonth(4);
    //     ts.setDate(1);
    //     expireTime = ReservedInstancePO.calcExpireTime(ts, "MONTH", 1);
    //     Assert.assertEquals(5, expireTime.getMonth());
    //     Assert.assertEquals(1, expireTime.getDate());
    // }

    @Test
    public void testGetBciStockDemand() {
        List<ReservedInstancePO> reservedInstancePOList = new ArrayList<>();
        for (int i = 1; i != 4; i++) {
            ReservedInstancePO reservedInstancePO = initReservedInstancePO();
            reservedInstancePO.setAccountId("demand-test");
            reservedInstancePO.setUserId("demand-test");
            reservedInstancePO.setEffectiveTime(new Timestamp(1685548800000L + 8 * 3600000L + 3600000L * i));
            reservedInstancePO.setExpireTime(ReservedInstancePO.calcExpireTime(reservedInstancePO.getEffectiveTime(), 
                reservedInstancePO.getReservedTimeUnit(), reservedInstancePO.getReservedTimePeriod()));
            reservedInstancePO.setReservedInstanceCount(i);
            reservedInstancePOList.add(reservedInstancePO);
        }
        reservedInstanceDao.batchInsertReservedInstance(reservedInstancePOList);
        Integer count = reservedInstanceDao.getBciStockDemand("bci.gna2.c8m36.1a10", "Azone-gzdt", "demand-test",
            "2023-06-01 00:00:00", "2023-07-01 00:00:00");
        Assert.assertEquals(6, count.intValue());

    }
    
    private ReservedInstancePO initReservedInstancePO() {
        ReservedInstancePO reservedInstancePO = new ReservedInstancePO();
        reservedInstancePO.setReservedInstanceId(commonUtils.createExternalId("r"));
        reservedInstancePO.setUserId("2e1be1eb99e946c3a543ec5a4eaa7d39");
        reservedInstancePO.setAccountId("2e1be1eb99e946c3a543ec5a4eaa7d39");
        reservedInstancePO.setName("name");
        reservedInstancePO.setStatus(ReservedInstancePO.Status.CREATING);
        reservedInstancePO.setResourceUuid("acfef5fe-c3ab-425d-bf4f-f5c7b8b99bc0");
        reservedInstancePO.setReservedInstanceUuid("5628b44e-efff-4728-8183-71d63f1e95d0");
        reservedInstancePO.setScope("AZ");
        reservedInstancePO.setPhysicalZone("Azone-gzdt");
        reservedInstancePO.setOrderId("080ac425c0864acea046af31f4f7ead3");
        reservedInstancePO.setPurchaseMode("FullyPrepay");
        reservedInstancePO.setReservedSpec("bci.gna2.c8m36.1a10");
        reservedInstancePO.setReservedInstanceCount(1);
        reservedInstancePO.setReservedTimeUnit("MONTH");
        reservedInstancePO.setReservedTimePeriod(1);
        reservedInstancePO.setAutoRenew(true);
        reservedInstancePO.setAutoRenewTimeUnit("MONTH");
        reservedInstancePO.setAutoRenewTimePeriod(1);
        reservedInstancePO.setEffectiveTime(new Timestamp(System.currentTimeMillis()));
        reservedInstancePO.setExpireTime(ReservedInstancePO.calcExpireTime(reservedInstancePO.getEffectiveTime(), 
                reservedInstancePO.getReservedTimeUnit(), reservedInstancePO.getReservedTimePeriod()));
        return reservedInstancePO;
    }

    @Test
    public void getReservedInstanceByResourceUuidTest() {
        ReservedInstancePO reservedInstancePO = reservedInstanceDao.getReservedInstanceByResourceUuid("a");
        Assert.assertEquals(reservedInstancePO.getUserId(), "bb45087dee674fcaa21d75b53a35f7fc");
    }



}
