package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_UUID;

@SuppressWarnings("SpringJavaAutowiringInspection")
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class ContainerDaoTest {

    @Autowired
    DatabaseUtil databaseUtil;

    @Autowired
    ContainerDaoV2 containerDao;

    String name = "container_name_new";
    String containerUuid = "16151de01cefd87e78322709ac42b12e20254439d0679d1282a";
    String imageName = "busybox";
    String imageVersion = "latest";
    String imageAddress = "docker.io/busybox";
    float cpu = 0.25f;
    float memory = 0.5f;
    String workingDir = "workingdir";
    String commands = "sleep";
    String args = "10";
    String ports = "[{\"port\":8080,\"protocol\":\"TCP\"}]";
    String mounts = "[{\"mountPath\":\"/usr\",\"readOnly\":false,\"name\":\"nfs\"}]";
    String envs = "[{\"key\":\"envs\",\"value\":\"envs\"}]";
    String userId = "account_id";
    String previousState = "{\"state\":\"Failed\",\"startTime\":\"2019-05-14T16:33:48Z\",\"exitCode\":255," +
            "\"finishTime\":\"2019-05-14T16:33:58Z\",\"detailStatus\":\"container is dead\"}";
    String currentState = "{\"state\":\"Running\",\"startTime\":\"2019-05-14T16:34:08Z\"," +
            "\"detailStatus\":\"container is running\"}";
    int restartCount = 1;
    private static final float FLOATPRECISION = (float) 0.00001;


    @Before
    public void resetDatabase() throws Exception {
    }

    @Test
    public void listByPodIdTest() {
        Assert.assertTrue(containerDao.listByPodId(POD_UUID).size() > 0);
    }

    @Test
    public void batchInsertTest() {
        List<ContainerPO> list = new ArrayList<>();
        list.add(initContainer(name + "_2"));
        list.add(initContainer(name + "_3"));
        containerDao.batchInsert(list);
        List<ContainerPO> container = containerDao.listByPodId(POD_UUID);
    }

    @Test
    public void batchGpuInsertTest() {
        List<ContainerPO> list = new ArrayList<>();
        ContainerPO containerPo = initContainer(name + "_4");
        containerPo.setGpuCount(10);
        containerPo.setGpuType("gpuType");
        list.add(containerPo);
        containerDao.batchInsert(list);
        List<ContainerPO> containers = containerDao.listByPodId(POD_UUID);
        for (ContainerPO container : containers) {
            if (container.getName().equals(name + "_4")){
                Assert.assertEquals(container.getGpuType(), "gpuType");
                Assert.assertTrue(Math.abs(container.getGpuCount() - 10) < FLOATPRECISION);
            }
        }
    }

    @Test
    public void batchUpdateTest() {
        List<ContainerPO> list = new ArrayList<>();
        list.add(initContainer(name + "_2"));
        list.add(initContainer(name + "_3"));
        containerDao.batchInsert(list);
        List<ContainerPO> containers = containerDao.listByPodId(POD_UUID);
        for (ContainerPO containerPO : containers) {
            containerPO.setPodUuid(POD_UUID + "_new");
        }
        containerDao.batchUpdate(containers);
        List<ContainerPO> container = containerDao.listByPodId(POD_UUID + "_new");
        Assert.assertTrue(container.size() > 0);
    }


    @Test
    public void testBatchDelete() {
        List<ContainerPO> list = new ArrayList<>();
        list.add(initContainer(name + "_delete_1"));
        list.add(initContainer(name + "_delete_2"));
        list.add(initContainer(name + "_delete_3"));
        containerDao.batchInsert(list);

        List<Long> toBeDeletedContainerIds = new ArrayList<>();
        List<ContainerPO> containers = containerDao.listByPodId(POD_UUID);
        for (ContainerPO container : containers) {
            if (container.getName().equals(name + "_delete_1") 
                || container.getName().equals(name + "_delete_3") ){
                toBeDeletedContainerIds.add(container.getId());
            }
        }
        containerDao.batchDelete(userId, toBeDeletedContainerIds);

        containers = containerDao.listByPodId(POD_UUID);
        boolean container2Existed = false;
        boolean conatiner13Existed = false;
        for (ContainerPO container : containers) {
            if (container.getName().equals(name + "_delete_1") 
                || container.getName().equals(name + "_delete_3") ){
                conatiner13Existed = true;
            }
            if (container.getName().equals(name + "_delete_2")){
                container2Existed = true;
            }
        }
        if (container2Existed == false || conatiner13Existed) {
            Assert.assertTrue(false);
        }

        // 测试删除一个空列表
        containerDao.batchDelete(userId, new ArrayList<Long>());
        containers = containerDao.listByPodId(POD_UUID);
        container2Existed = false;
        for (ContainerPO container : containers) {
            if (container.getName().equals(name + "_delete_2")){
                container2Existed = true;
            }
        }
        if (container2Existed == false) {
            Assert.assertTrue(false);
        }
    }

    @Test
    public void deleteContainerTest() {
        containerDao.deleteContainers(userId, POD_UUID);
        List<ContainerPO> container = containerDao.listByPodId(POD_UUID);
    }

    @Test
    public void updateContainersByPodIdTest() {
        containerDao.updateContainersPodId(POD_ID, POD_UUID);
    }


    private ContainerPO initContainer(String name) {
        ContainerPO container = new ContainerPO();
        container.setPodUuid(POD_UUID);
        container.setName(name);
        container.setContainerUuid(containerUuid);
        container.setImageName(imageName);
        container.setImageVersion(imageVersion);
        container.setImageAddress(imageAddress);
        container.setCpu(cpu);
        container.setMemory(memory);
        container.setWorkingDir(workingDir);
        container.setCommands(commands);
        container.setArgs(args);
        container.setPorts(ports);
        container.setVolumeMounts(mounts);
        container.setEnvs(envs);
        container.setUserId(userId);
        container.setPreviousState(previousState);
        container.setCurrentState(currentState);
        container.setRestartCount(restartCount);

        return container;
    }
}
