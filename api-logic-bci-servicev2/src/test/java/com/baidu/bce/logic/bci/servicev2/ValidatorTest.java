package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.ValidatedItem;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodValidator;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.bci.servicev2.util.Validator;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ValidatorTest {

    private static final String CREATE_POD_LOCATION = "classpath*:create_pod.json";

    private BaseCreateOrderRequestVo<IOrderItem> request;

    @Autowired
    private Validator validator;

    @Autowired
    private TestUtil testUtil;

    @Mock
    private PodValidator podValidator;

    @Mock
    private CommonUtilsV2 commonUtils;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        testUtil.setUp();
        // request = testUtil.orderRequest(
        //         databaseUtil.getResource(CREATE_POD_LOCATION).getInputStream(), IOrderItem.class);
    }

    // @Test
    public void getZoneSubnetsTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        try {
            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid(null);
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnetIds or subnetId or subnetUuid only one can be field");
        }

        try {
            podPurchaseRequest.setSubnetIds("test");
            podPurchaseRequest.setSubnetId("test");
            podPurchaseRequest.setSubnetUuid(null);
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnetIds or subnetId or subnetUuid only one can be field");
        }

        try {
            podPurchaseRequest.setSubnetIds("test");
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid("test");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnetIds or subnetId or subnetUuid only one can be field");
        }

        try {
            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId("test");
            podPurchaseRequest.setSubnetUuid("test");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnetIds or subnetId or subnetUuid only one can be field");
        }

        try {
            podPurchaseRequest.setSubnetIds("test");
            podPurchaseRequest.setSubnetId("test");
            podPurchaseRequest.setSubnetUuid("test");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnetIds or subnetId or subnetUuid only one can be field");
        }
        //
        try {
            // mock
            SubnetVo subnetVo1 = new SubnetVo();
            subnetVo1.setSubnetId("sub1");
            subnetVo1.setTotalIps(10);
            subnetVo1.setUsedIps(5);
            subnetVo1.setVpcId("vpc1");
            SubnetVo subnetVo2 = new SubnetVo();
            subnetVo2.setSubnetId("sub1");
            subnetVo2.setTotalIps(10);
            subnetVo2.setUsedIps(5);
            subnetVo2.setVpcId("vpc1");
            when(podValidator.getSubnet("sub1")).thenReturn(subnetVo1);
            when(podValidator.getSubnet("sub2")).thenReturn(subnetVo2);
            ReflectionTestUtils.setField(podValidator, "podValidator", podValidator);

            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid("sub1,sub2");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnets need belong to the same vpc");
        }

        try {
            // mock
            SubnetVo subnetVo1 = new SubnetVo();
            subnetVo1.setSubnetId("sub1");
            subnetVo1.setTotalIps(10);
            subnetVo1.setUsedIps(5);
            subnetVo1.setVpcId("vpc1");
            SubnetVo subnetVo2 = new SubnetVo();
            subnetVo2.setSubnetId("sub1");
            subnetVo2.setTotalIps(10);
            subnetVo2.setUsedIps(5);
            subnetVo2.setVpcId("vpc1");
            when(podValidator.getSubnet("sub1")).thenReturn(subnetVo1);
            when(podValidator.getSubnet("sub2")).thenReturn(subnetVo2);

            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid("sub1,sub1");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnets have the same subnet");
        }

        try {
            // mock
            SubnetVo subnetVo1 = new SubnetVo();
            subnetVo1.setSubnetId("sub1");
            subnetVo1.setTotalIps(10);
            subnetVo1.setUsedIps(5);
            subnetVo1.setVpcId("vpc1");
            subnetVo1.setAz("az1");
            SubnetVo subnetVo2 = new SubnetVo();
            subnetVo2.setSubnetId("sub1");
            subnetVo2.setTotalIps(10);
            subnetVo2.setUsedIps(5);
            subnetVo2.setVpcId("vpc1");
            subnetVo1.setAz("az2");
            when(podValidator.getSubnet("sub1")).thenReturn(subnetVo1);
            when(podValidator.getSubnet("sub2")).thenReturn(subnetVo2);

            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid("sub1,sub1");
            podPurchaseRequest.setLogicalZone("az");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertFalse(true);
        } catch (CommonExceptions.RequestInvalidException e) {
            assertEquals(e.getMessage(), "subnets not belong to logicalZone");
        }

        try {
            // mock
            SubnetVo subnetVo1 = new SubnetVo();
            subnetVo1.setSubnetId("sub1");
            subnetVo1.setTotalIps(10);
            subnetVo1.setUsedIps(5);
            subnetVo1.setVpcId("vpc1");
            subnetVo1.setAz("az1");
            SubnetVo subnetVo2 = new SubnetVo();
            subnetVo2.setSubnetId("sub2");
            subnetVo2.setTotalIps(10);
            subnetVo2.setUsedIps(5);
            subnetVo2.setVpcId("vpc1");
            subnetVo1.setAz("az1");
            when(podValidator.getSubnet("sub1")).thenReturn(subnetVo1);
            when(podValidator.getSubnet("sub2")).thenReturn(subnetVo2);

            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid("sub1,sub2");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertTrue(true);
            assertEquals(2, validatedItem.getSubnetVoMap());
            assertEquals(1, validatedItem.getZoneMap());
        } catch (CommonExceptions.RequestInvalidException e) {
            Assert.assertTrue(false);
        }

        try {
            // mock
            SubnetVo subnetVo1 = new SubnetVo();
            subnetVo1.setSubnetId("sub1");
            subnetVo1.setTotalIps(10);
            subnetVo1.setUsedIps(5);
            subnetVo1.setVpcId("vpc1");
            subnetVo1.setAz("az1");
            SubnetVo subnetVo2 = new SubnetVo();
            subnetVo2.setSubnetId("sub2");
            subnetVo2.setTotalIps(10);
            subnetVo2.setUsedIps(5);
            subnetVo2.setVpcId("vpc1");
            subnetVo1.setAz("az2");
            when(podValidator.getSubnet("sub1")).thenReturn(subnetVo1);
            when(podValidator.getSubnet("sub2")).thenReturn(subnetVo2);

            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid("sub1,sub2");
            ValidatedItem validatedItem = new ValidatedItem();
            validator.getZoneSubnets(podPurchaseRequest, validatedItem);
            Assert.assertTrue(true);
            assertEquals(2, validatedItem.getSubnetVoMap());
            assertEquals(2, validatedItem.getZoneMap());
        } catch (CommonExceptions.RequestInvalidException e) {
            Assert.assertTrue(false);
        }
    }


    @Test
    public void getContainerCpuMemoryTest(){
        Map<String, Float> cpumems = validator.getCpuMemory(1, 1);
        Assert.assertEquals(cpumems.get(Validator.CPU), 1.0F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 1.0F, 1e-7);

        // 0.5core + 2mem ->0.25+1
        cpumems = validator.getCpuMemory(0.25F, 2F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 0.25F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 2F, 1e-7);

        // 0.25core + 0.8mem ->0.25+1
        cpumems = validator.getCpuMemory(0.25F, 0.8F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 0.25F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 1.0F, 1e-7);

        // 0.25core + 3.1mem ->0.25+2
        cpumems = validator.getCpuMemory(0.25F, 3.1F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 0.25F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 2.0F, 1e-7);

        // 1+3 ->1+4
        cpumems = validator.getCpuMemory(1F, 3F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 1F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 4F, 1e-7);

        // 1+9 ->1+8
        cpumems = validator.getCpuMemory(1F, 9F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 1F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 8F, 1e-7);

        // 1+32 ->1+8
        cpumems = validator.getCpuMemory(1F, 32F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 1F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 8F, 1e-7);

        // 1+100 ->1+8
        cpumems = validator.getCpuMemory(1F, 100F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 1F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 8F, 1e-7);

        // 2+1 ->2+4
        cpumems = validator.getCpuMemory(2F, 4F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 2F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 4F, 1e-7);

        // 100+1 -> exception
        try {
            validator.getCpuMemory(100F, 1F);
        }catch (Exception e){
            Assert.assertTrue(e instanceof PodExceptions.PodCpuMemSpecInvalid);
        }


        // 0+0->1+2
        cpumems = validator.getCpuMemory(0F, 0F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 2F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 4F, 1e-7);

        // 0+1.1->0.25+2
        cpumems = validator.getCpuMemory(0F, 1.1F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 0.25F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 2F, 1e-7);

        // 1+0->1+1
        cpumems = validator.getCpuMemory(1F, 0F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 1F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 1F, 1e-7);

        // 1.6+0->2+4
        cpumems = validator.getCpuMemory(1.6F, 0F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 2F, 1e-7);
        Assert.assertEquals(cpumems.get(Validator.MEMORY), 4F, 1e-7);

        // 0+1000->exception
        try {
            cpumems = validator.getCpuMemory(0F, 1000F);
        }catch (Exception e){
            Assert.assertTrue(e instanceof PodExceptions.PodCpuMemSpecInvalid);
        }

        try {
            validator.getCpuMemory(16, 256);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof PodExceptions.PodCpuMemSpecInvalid);
        }

        try {
            validator.getCpuMemory(48, 96);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof PodExceptions.PodCpuMemSpecInvalid);
        }

        try {
            validator.getCpuMemory(32, 256);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof PodExceptions.PodCpuMemSpecInvalid);
        }

        try {
            validator.getCpuMemory(64, 256);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof PodExceptions.PodCpuMemSpecInvalid);
        }
    }

    @Test
    public void cpuBiddingTest() {
        Map<String, Float> cpumems = validator.getBiddingCpuMemory(1F,1F);
        Assert.assertEquals(cpumems.get(Validator.CPU), 1F, 1e-7);
    }

    @Test
    public void genPodResourceTest() {
        when(commonUtils.checkWhiteList(eq(LogicalConstant.ENABLE_POD_RESOURCE_LIMIT), anyString(),
                anyString())).thenReturn(true);
        ReflectionTestUtils.setField(validator, "commonUtils", commonUtils);

        ContainerPurchase container1 = new ContainerPurchase();
        container1.setCpu(1);
        container1.setMemory(1);
        container1.setGpuCount(0);
        container1.setContainerType(ContainerType.WORKLOAD.getType());
        container1.setName("test");
        ContainerPurchase container2 = new ContainerPurchase();
        container2.setCpu(1);
        container2.setMemory(2);
        container2.setGpuCount(0);
        container2.setContainerType(ContainerType.INIT.getType());

        ContainerPurchase container3 = new ContainerPurchase();
        container3.setCpu(0);
        container3.setMemory(0);
        container3.setGpuCount(0);
        container3.setContainerType(ContainerType.WORKLOAD.getType());

        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
        List<Label> labels = new ArrayList<>();
        Label label = new Label();
        label.setLabelKey("bci.virtual-kubelet.io/kubeproxy-container");
        label.setLabelValue("test");
        labels.add(label);
        podPurchaseRequest.setLabels(labels);
        podPurchaseRequest.setContainerPurchases(new ArrayList<ContainerPurchase>());
        podPurchaseRequest.getContainerPurchases().add(container1);
        podPurchaseRequest.getContainerPurchases().add(container2);
        podPurchaseRequest.getContainerPurchases().add(container3);
        podPurchaseRequest.setProductType("Postpay");
        podPurchaseRequest.setAnnotations("{\"bci.virtual-kubelet.io/bci-pod-limits\":\"1-2Gi\"}");

        ValidatedItem validatedItem = new ValidatedItem();
        validator.testGenPodResource(podPurchaseRequest, validatedItem);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getCpu() - 1) < 0.00001);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getMemory() - 2) < 0.00001);
    }
}