package com.baidu.bce.logic.bci.servicev2;

import io.kubernetes.client.openapi.models.V1PodSecurityContext;
import io.kubernetes.client.openapi.models.V1SecurityContext;

import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.model.Capabilities;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerSecurityContext;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodSecurityContext;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.logic.core.user.LogicUserService;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ToK8SObjectTest {
    
    private static final String CREATE_POD_LOCATION = "classpath*:create_pod.json";

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    private BaseCreateOrderRequestVo<IOrderItem> request;

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        testUtil.setUp();
        request = testUtil.orderRequest(
                databaseUtil.getResource(CREATE_POD_LOCATION).getInputStream(), IOrderItem.class);
    }

    @Test
    public void podSecurityContextToK8SObject() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        PodSecurityContext podSecurityContext = podPurchaseRequest.getSecurityContext();
        V1PodSecurityContext v1PodSecurityContext = podSecurityContext.toV1PodSecurityContext();
        Assert.assertEquals(v1PodSecurityContext.getRunAsUser(), podSecurityContext.getRunAsUser());
        Assert.assertEquals(v1PodSecurityContext.getSysctls().size(), podSecurityContext.getSysctls().size());
    }

    @Test
    public void containerSecurityContextToK8SObject() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        ContainerPurchase containerPurchase = podPurchaseRequest.getContainerPurchases().get(0);
        Capabilities cap = new Capabilities();
        List<String> addCap = new ArrayList<>();
        addCap.add("NET_ADMIN");
        addCap.add("SYS_PTRACE");
        cap.setAdd(addCap);
        containerPurchase.getSecurityContext().setCapabilities(cap);
        ContainerSecurityContext containerSecurityContext = containerPurchase.getSecurityContext();
        V1SecurityContext v1SecurityContext = containerSecurityContext.toV1SecurityContext();
        Assert.assertEquals(v1SecurityContext.getRunAsUser(), containerSecurityContext.getRunAsUser());
        Assert.assertEquals(v1SecurityContext.getCapabilities().getAdd().size(), containerPurchase.getSecurityContext().getCapabilities().getAdd().size());
        
    }
}
