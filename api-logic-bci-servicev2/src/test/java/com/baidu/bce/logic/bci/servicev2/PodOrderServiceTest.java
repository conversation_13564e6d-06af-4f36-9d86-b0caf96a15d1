package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.externalsdk.logical.network.common.model.OrderDetailRequest;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.pod.PodOrderServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.powermock.api.mockito.PowerMockito.when;


@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodOrderServiceTest {
    @Autowired
    private PodOrderServiceV2 podOrderService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
    }

    @Test
    public void getBciOrderDetailTest() {
        OrderDetailRequest orderDetailRequest = new OrderDetailRequest();
        orderDetailRequest.setUuid("orderId");
        Order order = podOrderService.getBciOrderDetail(orderDetailRequest);
        Assert.assertNotNull(order);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void getBciOrderDetailRequestInvalidTest() {
        OrderDetailRequest orderDetailRequest = new OrderDetailRequest();
        orderDetailRequest.setKey("orderId");
        podOrderService.getBciOrderDetail(orderDetailRequest);
    }
}
