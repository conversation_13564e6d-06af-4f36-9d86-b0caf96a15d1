package com.baidu.bce.logic.bci.servicev2.constant;

import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class BciFailStrategyConstantTest{

    @Test
    public void isValidFailFastTest() {
        assertTrue(BciFailStrategyConstant.isValid(BciFailStrategyConstant.FAIL_FAST));
    }

    @Test
    public void isValidFailOverTest() {
        assertTrue(BciFailStrategyConstant.isValid(BciFailStrategyConstant.FAIL_OVER));
    }

    @Test
    public void isValidFailBackTest() {
        assertTrue(BciFailStrategyConstant.isValid(BciFailStrategyConstant.FAIL_BACK));
    }

    @Test
    public void isValidInvalidStrategyTetst() {
        assertFalse(BciFailStrategyConstant.isValid("invalid-strategy"));
    }

    @Test
    public void isValidEmptyStrategyTest() {
        assertFalse(BciFailStrategyConstant.isValid(""));
    }

}