package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.chargestatus.PodChargeStatusDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PreemptStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.mock.K8sServiceMock;
import com.baidu.bce.logic.bci.servicev2.model.PodCondition;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;

import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1ContainerStatus;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1PodCondition;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodStatus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PodContainerSyncServiceV2Test {

    @Mock
    private V1PodStatus podStatus;

    @Mock
    private K8sService k8sService;

    @Mock
    private PodDaoV2 podDao;

    @Mock
    private ContainerDaoV2 containerDao;

    @Mock
    private PodChargeStatusDaoV2 podChargeStatusDao;

    @Mock
    private BillingResourceSyncManagerV2 billingResourceSyncManager;

    @Mock
    private PodMigrateService podMigrateService;

    @Mock
    List<V1PodCondition> conditions;
    
    private PodContainerSyncServiceV2 service;
    private PodContainerSyncServiceV2.SyncPod syncPod;
    
    @Before
    public void setUp() {
        // Create a real service instance
        service = new PodContainerSyncServiceV2();
        
        // Create a real SyncPod instance
        syncPod = service.new SyncPod();
        
        // Use reflection to inject the mocked dependencies into the service instance
        try {
            java.lang.reflect.Field podDaoField = SyncServiceV2.class.getDeclaredField("podDao");
            podDaoField.setAccessible(true);
            podDaoField.set(service, podDao);
            
            java.lang.reflect.Field containerDaoField = SyncServiceV2.class.getDeclaredField("containerDao");
            containerDaoField.setAccessible(true);
            containerDaoField.set(service, containerDao);
            
            java.lang.reflect.Field podChargeStatusDaoField = SyncServiceV2.class.getDeclaredField("podChargeStatusDao");
            podChargeStatusDaoField.setAccessible(true);
            podChargeStatusDaoField.set(service, podChargeStatusDao);
            
            java.lang.reflect.Field billingResourceSyncManagerField = SyncServiceV2.class.getDeclaredField("billingResourceSyncManager");
            billingResourceSyncManagerField.setAccessible(true);
            billingResourceSyncManagerField.set(service, billingResourceSyncManager);
            
            java.lang.reflect.Field k8sServiceField = PodContainerSyncServiceV2.class.getDeclaredField("k8sService");
            k8sServiceField.setAccessible(true);
            k8sServiceField.set(service, k8sService);
            
            java.lang.reflect.Field podMigrateServiceField = PodContainerSyncServiceV2.class.getDeclaredField("podMigrateService");
            podMigrateServiceField.setAccessible(true);
            podMigrateServiceField.set(service, podMigrateService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
    }

    @Test
    public void testEmptyConditions() {
        V1Pod pod = mock(V1Pod.class);
        V1PodStatus podStatus = new V1PodStatus();
        when(pod.getStatus()).thenReturn(podStatus);
        PodPO podPO = new PodPO();

        List<PodCondition> podConditions = syncPod.buildBciPodConditions(pod, podPO, new ArrayList<>());
        assertEquals(0, podConditions.size());
    }

    @Test
    public void testVariousConditions() {
        V1Pod pod = mock(V1Pod.class);
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setName("test-pod");
        meta.setNamespace("test-namespace");
        meta.setAnnotations(new HashMap<>());
        meta.getAnnotations().put("a", "1");
        List<V1PodCondition> conditions = new ArrayList<>();
        conditions.add(new V1PodCondition().type("Ready").status("True"));
        conditions.add(new V1PodCondition().type("PodScheduled").status("True"));
        when(pod.getMetadata()).thenReturn(meta);
        when(pod.getStatus()).thenReturn(podStatus);
        when(podStatus.getConditions()).thenReturn(conditions);
        PodPO podPO = new PodPO();

        List<PodCondition> podConditions = syncPod.buildBciPodConditions(pod, podPO, new ArrayList<>());
        assertTrue(podConditions.size() == 2);
    }

    @Test
    public void testMergeConditions() {
        V1Pod pod = mock(V1Pod.class);
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setName("test-pod");
        meta.setNamespace("test-namespace");
        meta.setAnnotations(new HashMap<>());
        meta.getAnnotations().put("a", "1");
        List<V1PodCondition> conditions = new ArrayList<>();
        conditions.add(new V1PodCondition().type("Ready").status("True"));
        conditions.add(new V1PodCondition().type("PodScheduled").status("True"));
        when(pod.getMetadata()).thenReturn(meta);
        when(pod.getStatus()).thenReturn(podStatus);
        when(podStatus.getConditions()).thenReturn(conditions);
        PodPO podPO = new PodPO();
        List<V1PodCondition> POConditions = new ArrayList<>();
        POConditions.add(new V1PodCondition().type("Creating").status("True"));
        POConditions.add(new V1PodCondition().type("AutoInstanceTypeMatch").status("True"));
        podPO.setConditions(JsonUtil.toJSON(POConditions));

        List<PodCondition> podConditions = syncPod.buildBciPodConditions(pod, podPO, new ArrayList<>());
        assertEquals(4, podConditions.size());
    }

    @Test
    public void testProcessIgnoreNotReadyContainerConditionsInvalidInput() {
        List<PodCondition> podConditions = syncPod.processIgnoreNotReadyContainerConditions(null, null);
        assertNull(podConditions);
    }

    @Test
    public void testProcessIgnoreNotReadyContainerConditionsWhenNotSet() {
        V1Pod pod = mock(V1Pod.class);
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setName("test-pod");
        meta.setNamespace("test-namespace");
        meta.setAnnotations(new HashMap<>());
        List<V1PodCondition> conditions = new ArrayList<>();
        conditions.add(new V1PodCondition().type("Ready").status("True"));
        conditions.add(new V1PodCondition().type("ContainerReady").status("True"));
        conditions.add(new V1PodCondition().type("PodScheduled").status("True"));
        when(pod.getMetadata()).thenReturn(meta);
        when(pod.getStatus()).thenReturn(podStatus);
        when(podStatus.getConditions()).thenReturn(conditions);
        Map<String, String> k8sConditionTypeMap = new HashMap<>();
        k8sConditionTypeMap.put("ContainersReady", "1");
        k8sConditionTypeMap.put("Initialized", "1");
        k8sConditionTypeMap.put("Ready", "1");
        k8sConditionTypeMap.put("PodScheduled", "1");
        List<PodCondition> copyConditions = new ArrayList<>();
        for (V1PodCondition condition : conditions) {
            if (!k8sConditionTypeMap.containsKey(condition.getType())) {
                continue;
            }
            PodCondition copyCondition = new PodCondition();
            copyCondition.setReason(condition.getReason());
            copyCondition.setMessage(condition.getMessage());
            copyCondition.setType(condition.getType());
            if (condition.getLastProbeTime() != null) {
                copyCondition.setLastProbeTime(Date.from(condition.getLastProbeTime().toInstant()));
            }
            if (condition.getLastTransitionTime() != null) {
                copyCondition.setLastTransitionTime(Date.from(condition.getLastTransitionTime().toInstant()));
            }
            copyCondition.setStatus(condition.getStatus());
            copyConditions.add(copyCondition);
        }
        // not set
        List<PodCondition> podConditions = syncPod.processIgnoreNotReadyContainerConditions(copyConditions, pod);
        assertEquals(2, podConditions.size());
    }

    @Test
    public void testProcessIgnoreNotReadyContainerConditions() {
        V1Pod pod = mock(V1Pod.class);
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setName("test-pod");
        meta.setNamespace("test-namespace");
        meta.setAnnotations(new HashMap<>());
        meta.getAnnotations().put(PodConstants.BCI_IGNORE_NOT_READY_CONTAINERS_ANNOTATION_KEY, "c1");
        List<V1PodCondition> conditions = new ArrayList<>();
        conditions.add(new V1PodCondition().type("Ready").status("False"));
        conditions.add(new V1PodCondition().type("ContainersReady").status("False"));
        conditions.add(new V1PodCondition().type("PodScheduled").status("True"));

        List<V1ContainerStatus> cstatus = new ArrayList<>();
        cstatus.add(new V1ContainerStatus().name("c1").ready(false));
        cstatus.add(new V1ContainerStatus().name("c2").ready(true));

        when(pod.getMetadata()).thenReturn(meta);
        when(pod.getStatus()).thenReturn(podStatus);
        when(podStatus.getConditions()).thenReturn(conditions);
        when(podStatus.getContainerStatuses()).thenReturn(cstatus);

        Map<String, String> k8sConditionTypeMap = new HashMap<>();
        k8sConditionTypeMap.put("ContainersReady", "1");
        k8sConditionTypeMap.put("Initialized", "1");
        k8sConditionTypeMap.put("Ready", "1");
        k8sConditionTypeMap.put("PodScheduled", "1");
        List<PodCondition> copyConditions = new ArrayList<>();
        for (V1PodCondition condition : conditions) {
            if (!k8sConditionTypeMap.containsKey(condition.getType())) {
                continue;
            }
            PodCondition copyCondition = new PodCondition();
            copyCondition.setReason(condition.getReason());
            copyCondition.setMessage(condition.getMessage());
            copyCondition.setType(condition.getType());
            if (condition.getLastProbeTime() != null) {
                copyCondition.setLastProbeTime(Date.from(condition.getLastProbeTime().toInstant()));
            }
            if (condition.getLastTransitionTime() != null) {
                copyCondition.setLastTransitionTime(Date.from(condition.getLastTransitionTime().toInstant()));
            }
            copyCondition.setStatus(condition.getStatus());
            copyConditions.add(copyCondition);
        }

        // c1 is ignored because of annotation
        List<PodCondition> podConditions = syncPod.processIgnoreNotReadyContainerConditions(copyConditions, pod);
        for (PodCondition condition : podConditions) {
            if (condition.getType().equals("ContainersReady")) {
                assertEquals("True", condition.getStatus());
            }
            if (condition.getType().equals("Ready")) {
                assertEquals("True", condition.getStatus());
            }
        }
    }

    @Test
    public void testProcessIgnoreNotReadyContainerConditions2() {
        V1Pod pod = mock(V1Pod.class);
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setName("test-pod");
        meta.setNamespace("test-namespace");
        meta.setAnnotations(new HashMap<>());
        meta.getAnnotations().put(PodConstants.BCI_IGNORE_NOT_READY_CONTAINERS_ANNOTATION_KEY, "c1");
        List<V1PodCondition> conditions = new ArrayList<>();
        conditions.add(new V1PodCondition().type("Ready").status("False"));
        conditions.add(new V1PodCondition().type("ContainersReady").status("False"));
        conditions.add(new V1PodCondition().type("PodScheduled").status("True"));

        List<V1ContainerStatus> cstatus = new ArrayList<>();
        cstatus.add(new V1ContainerStatus().name("c1").ready(false));
        cstatus.add(new V1ContainerStatus().name("c2").ready(true));
        cstatus.add(new V1ContainerStatus().name("c3").ready(false));

        when(pod.getMetadata()).thenReturn(meta);
        when(pod.getStatus()).thenReturn(podStatus);
        when(podStatus.getConditions()).thenReturn(conditions);
        when(podStatus.getContainerStatuses()).thenReturn(cstatus);

        Map<String, String> k8sConditionTypeMap = new HashMap<>();
        k8sConditionTypeMap.put("ContainersReady", "1");
        k8sConditionTypeMap.put("Initialized", "1");
        k8sConditionTypeMap.put("Ready", "1");
        k8sConditionTypeMap.put("PodScheduled", "1");
        List<PodCondition> copyConditions = new ArrayList<>();
        for (V1PodCondition condition : conditions) {
            if (!k8sConditionTypeMap.containsKey(condition.getType())) {
                continue;
            }
            PodCondition copyCondition = new PodCondition();
            copyCondition.setReason(condition.getReason());
            copyCondition.setMessage(condition.getMessage());
            copyCondition.setType(condition.getType());
            if (condition.getLastProbeTime() != null) {
                copyCondition.setLastProbeTime(Date.from(condition.getLastProbeTime().toInstant()));
            }
            if (condition.getLastTransitionTime() != null) {
                copyCondition.setLastTransitionTime(Date.from(condition.getLastTransitionTime().toInstant()));
            }
            copyCondition.setStatus(condition.getStatus());
            copyConditions.add(copyCondition);
        }

        // c1 is ignored because of annotation
        List<PodCondition> podConditions = syncPod.processIgnoreNotReadyContainerConditions(copyConditions, pod);
        String exString = "containers with unready status: [c3]";

        for (PodCondition condition : podConditions) {
            if (condition.getType().equals("ContainersReady")) {
                assertEquals("False", condition.getStatus());
                assertEquals(exString, condition.getMessage());
            }
            if (condition.getType().equals("Ready")) {
                assertEquals("False", condition.getStatus());
                assertEquals(exString, condition.getMessage());
            }
        }
    }

    @Test
    public void testAddAndUpdatePodContainer() throws K8sServiceException, ApiException {
        // 准备测试数据
        // Use the real service instance created in setUp
        PodContainerSyncServiceV2 service = this.service;

        // 创建K8s Pod用于SyncPodEvent构造函数
        V1Pod podForEvent = new V1Pod();
        V1ObjectMeta metadataForEvent = new V1ObjectMeta();
        metadataForEvent.setName("test-pod");
        metadataForEvent.setNamespace("test-namespace");
        metadataForEvent.setUid("test-pod-uid");
        podForEvent.setMetadata(metadataForEvent);

        PodContainerSyncServiceV2.SyncPodEvent podEvent = service.new SyncPodEvent(podForEvent, PodContainerSyncServiceV2.PodEventType.ADD);
        // 创建PodPO
        PodPO podPO = new PodPO();
        podPO.setPodId("test-pod");
        podPO.setUserId("test-namespace");
        podPO.setStatus(BciStatus.PENDING.getStatus());
        podPO.setResourceVersion(5L);
        podPO.setPodUuid("test-pod");

        // 创建K8s Pod - 确保包含完整的metadata
        V1Pod k8sPod = new V1Pod();
        V1ObjectMeta metadata = new V1ObjectMeta();
        metadata.setName("test-pod");
        metadata.setNamespace("test-namespace");
        metadata.setResourceVersion("10");
        metadata.setUid("test-pod-uid"); // 确保设置uid字段
        Map<String, String> annotations = new HashMap<>();
        annotations.put("bci_internal_PodIPv6", "fe80::1");
        metadata.setAnnotations(annotations);
        k8sPod.setMetadata(metadata);

        V1PodStatus status = new V1PodStatus();
        status.setPhase("Running");
        status.setPodIP("***********");
        // 设置containerStatuses以避免NPE
        List<V1ContainerStatus> containerStatuses = new ArrayList<>();
        status.setContainerStatuses(containerStatuses);
        k8sPod.setStatus(status);

        when(podDao.getPodById("test-namespace", "test-pod")).thenReturn(podPO);
        when(k8sService.getPod("test-namespace", "test-pod")).thenReturn(k8sPod);
        when(podMigrateService.isMigratePod(k8sPod)).thenReturn(false);
        
        // Mock containerDao methods
        when(containerDao.listByPodId("test-pod")).thenReturn(new ArrayList<>());
        when(containerDao.listByPodId("test-pod-uid")).thenReturn(new ArrayList<>());
        
        // Mock podChargeStatusDao methods
        when(podChargeStatusDao.insert(any())).thenReturn(1);

        // Let the real methods execute - they will use the mocked dependencies
        boolean result = syncPod.addAndUpdatePodContainer(podEvent);
        // 由于podUuid与podId相同，应该返回true（重试）
        assertTrue(result);

    }
}