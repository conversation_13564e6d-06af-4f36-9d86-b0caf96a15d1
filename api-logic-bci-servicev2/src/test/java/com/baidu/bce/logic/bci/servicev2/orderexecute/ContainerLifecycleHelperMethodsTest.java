package com.baidu.bce.logic.bci.servicev2.orderexecute;

import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.ExecAction;
import com.baidu.bce.logic.bci.servicev2.model.HTTPGetAction;
import com.baidu.bce.logic.bci.servicev2.model.Lifecycle;
import com.baidu.bce.logic.bci.servicev2.model.LifecycleHandler;
import com.baidu.bce.logic.bci.servicev2.model.Probe;
import com.baidu.bce.logic.bci.servicev2.model.TCPSocketAction;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1Lifecycle;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * ContainerLifecycle辅助函数的单元测试类
 * 测试PodNewOrderExecutorServiceV2中handleContainerLifecycle使用的辅助函数
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ContainerLifecycleHelperMethodsTest {

    @InjectMocks
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        podNewOrderExecutorServiceV2 = PowerMockito.spy(new PodNewOrderExecutorServiceV2());

        // 设置配置参数
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckMaxRetries", 20);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckRetryInterval", 3);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckInitialWait", 5);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartReadinessMaxRetries", 12);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartReadinessRetryInterval", 2);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartBasicWait", 5);
    }

    // ===================================================================================
    // Tests for handlePostStartHook method
    // ===================================================================================

    @Test
    public void testHandlePostStartHook_WithoutNextPriority() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Boolean hasNextPriorityContainer = false;

        // Act
        invokePrivateMethod("handlePostStartHook", lifecycle, containerPurchase, hasNextPriorityContainer);

        // Assert
        assertNull(lifecycle.getPostStart());
    }

    @Test
    public void testHandlePostStartHook_WithNextPriority() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Boolean hasNextPriorityContainer = true;

        // Act
        invokePrivateMethod("handlePostStartHook", lifecycle, containerPurchase, hasNextPriorityContainer);

        // Assert
        assertNotNull(lifecycle.getPostStart());
        assertNotNull(lifecycle.getPostStart().getExec());
        assertNotNull(lifecycle.getPostStart().getExec().getCommand());
    }

    @Test
    public void testHandlePostStartHook_WithReadinessProbe() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // Add readiness probe
        Probe readinessProbe = new Probe();
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setPath("/health");
        httpGet.setPort(8080);
        readinessProbe.setHttpGet(httpGet);
        containerPurchase.setReadinessProbe(readinessProbe);

        Boolean hasNextPriorityContainer = true;

        // Act
        invokePrivateMethod("handlePostStartHook", lifecycle, containerPurchase, hasNextPriorityContainer);

        // Assert
        assertNotNull(lifecycle.getPostStart());
        assertNotNull(lifecycle.getPostStart().getExec());
        assertTrue(lifecycle.getPostStart().getExec().getCommand().get(2).contains("curl"));
    }

    @Test
    public void testHandlePreStopHook_WithoutExitPriority() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        ContainerPurchase preContainer = null;
        ContainerPurchase nextContainer = null;

        // Act
        invokePrivateMethod("handlePreStopHook", lifecycle, containerPurchase, preContainer, nextContainer);

        // Assert
        // Should preserve user preStop hook or have no preStop
        // The exact behavior depends on whether user has existing preStop
    }

    @Test
    public void testHandlePreStopHook_WithExitPriority() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");
        ContainerPurchase nextContainer = createTestContainerPurchase("next-container");

        // Act
        invokePrivateMethod("handlePreStopHook", lifecycle, containerPurchase, preContainer, nextContainer);

        // Assert
        assertNotNull(lifecycle.getPreStop());
        assertNotNull(lifecycle.getPreStop().getExec());
        assertNotNull(lifecycle.getPreStop().getExec().getCommand());
    }

    @Test
    public void testHandlePreStopHook_WithUserPreStopCommand() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // Add user preStop command
        Lifecycle userLifecycle = new Lifecycle();
        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'custom cleanup'"));
        preStop.setExec(exec);
        userLifecycle.setPreStop(preStop);
        containerPurchase.setLifecycle(userLifecycle);

        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");
        ContainerPurchase nextContainer = null;

        // Act
        invokePrivateMethod("handlePreStopHook", lifecycle, containerPurchase, preContainer, nextContainer);

        // Assert
        assertNotNull(lifecycle.getPreStop());
        assertTrue(lifecycle.getPreStop().getExec().getCommand().get(2).contains("custom cleanup"));
    }


    @Test
    public void testHandleLifecycleConfigurationFailure_NormalCase() throws Exception {
        // Arrange
        V1Container container = new V1Container();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Exception error = new RuntimeException("Test error");

        // Act
        invokePrivateMethod("handleLifecycleConfigurationFailure", container, containerPurchase, error);

        // Assert
        assertNotNull(container.getLifecycle());
        assertNotNull(container.getLifecycle().getPostStart());
        assertEquals("sleep 3", container.getLifecycle().getPostStart().getExec().getCommand().get(2));
    }

    @Test
    public void testHandleLifecycleConfigurationFailure_WithFallbackException() throws Exception {
        // Arrange
        V1Container container = null; // This will cause exception in fallback
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Exception error = new RuntimeException("Test error");

        // Act & Assert - Should not throw exception
        invokePrivateMethod("handleLifecycleConfigurationFailure", container, containerPurchase, error);
    }

    // ===================================================================================
    // Tests for handlePreStopConfigurationFailure method
    // ===================================================================================

    @Test
    public void testHandlePreStopConfigurationFailure_WithoutUserConfig() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");
        ContainerPurchase nextContainer = null;
        Exception error = new RuntimeException("Test error");

        // Act
        invokePrivateMethod("handlePreStopConfigurationFailure", lifecycle, containerPurchase, preContainer, nextContainer, error);

        // Assert
        assertNotNull(lifecycle.getPreStop());
        assertTrue(lifecycle.getPreStop().getExec().getCommand().get(2).contains("sleep 15"));
    }

    @Test
    public void testHandlePreStopConfigurationFailure_WithUserConfig() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = new V1Lifecycle();
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");

        // Add user preStop command
        Lifecycle userLifecycle = new Lifecycle();
        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'user cleanup'"));
        preStop.setExec(exec);
        userLifecycle.setPreStop(preStop);
        containerPurchase.setLifecycle(userLifecycle);

        ContainerPurchase preContainer = null;
        ContainerPurchase nextContainer = null;
        Exception error = new RuntimeException("Test error");

        // Act
        invokePrivateMethod("handlePreStopConfigurationFailure", lifecycle, containerPurchase, preContainer, nextContainer, error);

        // Assert - Should preserve user config
        // The exact assertion depends on implementation
    }

    @Test
    public void testHandlePreStopConfigurationFailure_WithFallbackException() throws Exception {
        // Arrange
        V1Lifecycle lifecycle = null; // This will cause exception in fallback
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        ContainerPurchase preContainer = null;
        ContainerPurchase nextContainer = null;
        Exception error = new RuntimeException("Test error");

        // Act & Assert - Should not throw exception
        invokePrivateMethod("handlePreStopConfigurationFailure", lifecycle, containerPurchase, preContainer, nextContainer, error);
    }

    // ===================================================================================
    // Tests for convertProbeToExecCommand method
    // ===================================================================================

    @Test
    public void testConvertProbeToExecCommand_WithNull() throws Exception {
        // Arrange
        Probe probe = null;

        // Act
        String result = (String) invokePrivateMethod("convertProbeToExecCommand", probe);

        // Assert
        assertNull(result);
    }

    @Test
    public void testConvertProbeToExecCommand_WithHttpGet() throws Exception {
        // Arrange
        Probe probe = new Probe();
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setPath("/health");
        httpGet.setPort(8080);
        httpGet.setHost("localhost");
        probe.setHttpGet(httpGet);

        // Act
        String result = (String) invokePrivateMethod("convertProbeToExecCommand", probe);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("localhost:8080/health"));
    }

    @Test
    public void testConvertProbeToExecCommand_WithTcpSocket() throws Exception {
        // Arrange
        Probe probe = new Probe();
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(8080);
        tcpSocket.setHost("localhost");
        probe.setTcpSocket(tcpSocket);

        // Act
        String result = (String) invokePrivateMethod("convertProbeToExecCommand", probe);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("8080"));
        assertTrue(result.contains("localhost"));
    }

    @Test
    public void testConvertProbeToExecCommand_WithExec() throws Exception {
        // Arrange
        Probe probe = new Probe();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "test -f /ready"));
        probe.setExec(exec);

        // Act
        String result = (String) invokePrivateMethod("convertProbeToExecCommand", probe);

        // Assert
        assertNotNull(result);
        assertEquals("test -f /ready", result);
    }

    @Test
    public void testConvertProbeToExecCommand_WithEmptyExec() throws Exception {
        // Arrange
        Probe probe = new Probe();
        ExecAction exec = new ExecAction();
        exec.setCommand(new ArrayList<>());
        probe.setExec(exec);

        // Act
        String result = (String) invokePrivateMethod("convertProbeToExecCommand", probe);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("empty command"));
    }

    @Test
    public void testConvertProbeToExecCommand_WithNoAction() throws Exception {
        // Arrange
        Probe probe = new Probe();
        // No action set

        // Act
        String result = (String) invokePrivateMethod("convertProbeToExecCommand", probe);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("no recognized action"));
    }

    // ===================================================================================
    // Tests for buildPostStartCommand method
    // ===================================================================================

    @Test
    public void testBuildPostStartCommand_WithBothCommands() throws Exception {
        // Arrange
        String readinessCommand = "curl -f http://localhost:8080/health";
        String userCommand = "echo 'Starting application'";

        // Act
        String result = (String) invokePrivateMethod("buildPostStartCommand", readinessCommand, userCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Starting application"));
        assertTrue(result.contains("Container basic startup phase"));
        assertTrue(result.contains("readiness verification"));
    }

    @Test
    public void testBuildPostStartCommand_WithOnlyReadiness() throws Exception {
        // Arrange
        String readinessCommand = "nc -z localhost 8080";
        String userCommand = null;

        // Act
        String result = (String) invokePrivateMethod("buildPostStartCommand", readinessCommand, userCommand);

        // Assert
        assertNotNull(result);
        assertFalse(result.contains("&&"));
        assertTrue(result.contains("Container basic startup phase"));
        assertTrue(result.contains("readiness verification"));
    }

    @Test
    public void testBuildPostStartCommand_WithOnlyUserCommand() throws Exception {
        // Arrange
        String readinessCommand = null;
        String userCommand = "echo 'Custom startup'";

        // Act
        String result = (String) invokePrivateMethod("buildPostStartCommand", readinessCommand, userCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Custom startup"));
        assertTrue(result.contains("Container basic startup phase"));
        assertTrue(result.contains("no readiness check required"));
    }

    @Test
    public void testBuildPostStartCommand_WithNeitherCommand() throws Exception {
        // Arrange
        String readinessCommand = null;
        String userCommand = null;

        // Act
        String result = (String) invokePrivateMethod("buildPostStartCommand", readinessCommand, userCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Container basic startup phase"));
        assertTrue(result.contains("no readiness check required"));
    }

    @Test
    public void testBuildPostStartCommand_WithSimpleReadinessCommand() throws Exception {
        // Arrange
        String readinessCommand = "sleep 5"; // This is a simple test command
        String userCommand = null;

        // Act
        String result = (String) invokePrivateMethod("buildPostStartCommand", readinessCommand, userCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Skipped test-only readiness check"));
        assertFalse(result.contains("readiness verification"));
    }

    @Test
    public void testBuildPostStartCommand_WithEmptyStrings() throws Exception {
        // Arrange
        String readinessCommand = "   ";
        String userCommand = "";

        // Act
        String result = (String) invokePrivateMethod("buildPostStartCommand", readinessCommand, userCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Container basic startup phase"));
        assertTrue(result.contains("no readiness check required"));
    }

    // ===================================================================================
    // Tests for isSimpleCommand method
    // ===================================================================================

    @Test
    public void testIsSimpleCommand_WithNull() throws Exception {
        // Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", (String) null));
    }

    @Test
    public void testIsSimpleCommand_WithEmpty() throws Exception {
        // Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", ""));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "   "));
    }

    @Test
    public void testIsSimpleCommand_WithBasicCommands() throws Exception {
        // Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "sleep"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "true"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "/bin/true"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "exit 0"));
    }

    @Test
    public void testIsSimpleCommand_WithSleepCommand() throws Exception {
        // Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "sleep 5"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "sleep 10"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "sleep 5; echo done")); // Contains semicolon
    }

    @Test
    public void testIsSimpleCommand_WithEchoCommand() throws Exception {
        // Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "echo hello"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "echo 'test message'"));
        assertFalse((Boolean) invokePrivateMethod("isSimpleCommand", "echo hello; ls")); // Contains semicolon
    }

    @Test
    public void testIsSimpleCommand_WithProbeKeywords() throws Exception {
        // Act & Assert
        assertFalse((Boolean) invokePrivateMethod("isSimpleCommand", "nc -z localhost 8080"));
        assertFalse((Boolean) invokePrivateMethod("isSimpleCommand", "curl -f http://localhost:8080/health"));
        assertFalse((Boolean) invokePrivateMethod("isSimpleCommand", "wget -q http://localhost/ready"));
        assertFalse((Boolean) invokePrivateMethod("isSimpleCommand", "python health_check.py"));
    }

    @Test
    public void testIsSimpleCommand_WithCompositeTestCommands() throws Exception {
        // Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "sleep 3 && true"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "echo hello; sleep 1"));
        assertFalse((Boolean) invokePrivateMethod("isSimpleCommand", "echo hello && nc -z localhost 8080")); // Contains probe keyword
    }

    @Test
    public void testIsSimpleCommand_CaseInsensitive() throws Exception {
        // Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "SLEEP"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "TRUE"));
        assertTrue((Boolean) invokePrivateMethod("isSimpleCommand", "EXIT 0"));
    }

    // ===================================================================================
    // Tests for extractUserPostStartCommand method
    // ===================================================================================

    @Test
    public void testExtractUserPostStartCommand_WithNull() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        containerPurchase.setLifecycle(null);

        // Act
        String result = (String) invokePrivateMethod("extractUserPostStartCommand", containerPurchase);

        // Assert
        assertNull(result);
    }

    @Test
    public void testExtractUserPostStartCommand_WithNoPostStart() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPostStartCommand", containerPurchase);

        // Assert
        assertNull(result);
    }

    @Test
    public void testExtractUserPostStartCommand_WithExecCommand() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler postStart = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'custom poststart'"));
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPostStartCommand", containerPurchase);

        // Assert
        assertNotNull(result);
        assertEquals("echo 'custom poststart'", result);
    }

    @Test
    public void testExtractUserPostStartCommand_WithEmptyCommand() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler postStart = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(new ArrayList<>());
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPostStartCommand", containerPurchase);

        // Assert
        assertNull(result);
    }

    @Test
    public void testExtractUserPostStartCommand_WithDirectCommand() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler postStart = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("echo", "direct command"));
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPostStartCommand", containerPurchase);

        // Assert
        assertNotNull(result);
        assertEquals("echo direct command", result);
    }

    // ===================================================================================
    // Tests for extractUserPreStopCommand method
    // ===================================================================================

    @Test
    public void testExtractUserPreStopCommand_WithNull() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        containerPurchase.setLifecycle(null);

        // Act
        String result = (String) invokePrivateMethod("extractUserPreStopCommand", containerPurchase);

        // Assert
        assertNull(result);
    }

    @Test
    public void testExtractUserPreStopCommand_WithNoPreStop() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPreStopCommand", containerPurchase);

        // Assert
        assertNull(result);
    }

    @Test
    public void testExtractUserPreStopCommand_WithExecCommand() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("/bin/sh", "-c", "echo 'custom prestop'"));
        preStop.setExec(exec);
        lifecycle.setPreStop(preStop);
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPreStopCommand", containerPurchase);

        // Assert
        assertNotNull(result);
        assertEquals("echo 'custom prestop'", result);
    }

    @Test
    public void testExtractUserPreStopCommand_WithEmptyCommand() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(new ArrayList<>());
        preStop.setExec(exec);
        lifecycle.setPreStop(preStop);
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPreStopCommand", containerPurchase);

        // Assert
        assertNull(result);
    }

    @Test
    public void testExtractUserPreStopCommand_WithDirectCommand() throws Exception {
        // Arrange
        ContainerPurchase containerPurchase = createTestContainerPurchase("test-container");
        Lifecycle lifecycle = new Lifecycle();
        LifecycleHandler preStop = new LifecycleHandler();
        ExecAction exec = new ExecAction();
        exec.setCommand(Arrays.asList("pkill", "-TERM", "java"));
        preStop.setExec(exec);
        lifecycle.setPreStop(preStop);
        containerPurchase.setLifecycle(lifecycle);

        // Act
        String result = (String) invokePrivateMethod("extractUserPreStopCommand", containerPurchase);

        // Assert
        assertNotNull(result);
        assertEquals("pkill -TERM java", result);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的ContainerPurchase对象
     */
    private ContainerPurchase createTestContainerPurchase(String name) {
        ContainerPurchase containerPurchase = new ContainerPurchase();
        containerPurchase.setName(name);
        containerPurchase.setContainerType(ContainerType.WORKLOAD.getType());
        return containerPurchase;
    }

    /**
     * 通过反射调用私有方法
     */
    private Object invokePrivateMethod(String methodName, Object... args) throws Exception {
        Class<?>[] paramTypes = new Class<?>[args.length];
        for (int i = 0; i < args.length; i++) {
            if (args[i] == null) {
                paramTypes[i] = Object.class; // 对于null参数，使用Object.class
            } else {
                paramTypes[i] = args[i].getClass();
                // 特殊处理基本类型的包装类
                if (paramTypes[i] == Boolean.class) {
                    paramTypes[i] = Boolean.class;
                } else if (paramTypes[i] == Integer.class) {
                    paramTypes[i] = Integer.class;
                }
            }
        }

        Method method = findMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(podNewOrderExecutorServiceV2, args);
    }

    /**
     * 查找匹配的方法
     */
    private Method findMethod(String methodName, Class<?>[] paramTypes) throws NoSuchMethodException {
        Method[] methods = PodNewOrderExecutorServiceV2.class.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                Class<?>[] methodParamTypes = method.getParameterTypes();
                if (methodParamTypes.length == paramTypes.length) {
                    boolean matches = true;
                    for (int i = 0; i < methodParamTypes.length; i++) {
                        if (paramTypes[i] == Object.class) {
                            // null参数，跳过类型检查
                            continue;
                        }
                        if (!isAssignableFrom(methodParamTypes[i], paramTypes[i])) {
                            matches = false;
                            break;
                        }
                    }
                    if (matches) {
                        return method;
                    }
                }
            }
        }
        throw new NoSuchMethodException("Method " + methodName + " not found");
    }

    /**
     * 检查类型兼容性
     */
    private boolean isAssignableFrom(Class<?> expected, Class<?> actual) {
        if (expected.isAssignableFrom(actual)) {
            return true;
        }
        // 处理基本类型
        if (expected == boolean.class && actual == Boolean.class) {
            return true;
        }
        if (expected == int.class && actual == Integer.class) {
            return true;
        }
        // 处理null值的情况
        if (!expected.isPrimitive() && actual == Object.class) {
            return true;
        }
        return false;
    }
} 