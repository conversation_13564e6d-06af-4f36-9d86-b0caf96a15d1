package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class CceClusterTest {

    @Autowired
    private CceClusterService cceClusterService;

    @Test
    public void createCceClusterTest() {
        CceCluster cceCluster = new CceCluster("name", "createCceClusterTestcceid", "kubeconfig", "cceid1 cluster");

        cceClusterService.createCceCluster(cceCluster);

    }

    @Test
    public void getCceClusterTest() {

        CceCluster cceCluster = cceClusterService.getCceClusterByCceId("cce-cluster1-id");
        Assert.assertEquals(cceCluster.getName(), "cce-cluster1");

    }

    @Test
    public void deleteCceClusterTest() {
        CceCluster cceCluster = new CceCluster("name", "deleteCceClusterTestcceid", "kubeconfig", "cceid1 cluster");

        cceClusterService.createCceCluster(cceCluster);

        cceCluster = cceClusterService.getCceClusterByCceId("deleteCceClusterTestcceid");
        Assert.assertEquals(cceCluster.getName(), "name");


        cceClusterService.deleteCceClusterByCceId("deleteCceClusterTestcceid");
        cceCluster = cceClusterService.getCceClusterByCceId("deleteCceClusterTestcceid");
        Assert.assertNull(cceCluster);
    }

    @Test
    public void createCceUserMapTest() {
        CceUserMap cceUserMap = new CceUserMap("createCceUserMapTestuserid1", "cceid1");

        cceClusterService.createCceUserMap(cceUserMap);

    }

    @Test
    public void getCceUserMapTest() {
        CceUserMap cceUserMap = new CceUserMap("getCceUserMapTestuserid1", "cceid1,cceid2");
        cceClusterService.createCceUserMap(cceUserMap);


         cceUserMap = cceClusterService.getCceUserMapByUserId("getCceUserMapTestuserid1");
        Assert.assertEquals(cceUserMap.getCceIds(), "cceid1,cceid2");

    }

    @Test
    public void deleteCceUserMapTest() {
        CceUserMap cceUserMap = new CceUserMap("deleteCceUserMapTestuserid1", "cceid1");

        cceClusterService.createCceUserMap(cceUserMap);

        cceUserMap = cceClusterService.getCceUserMapByUserId("deleteCceUserMapTestuserid1");
        Assert.assertEquals(cceUserMap.getCceIds(), "cceid1");


        cceClusterService.deleteCceUserMapByUserId("deleteCceUserMapTestuserid1");
        cceUserMap = cceClusterService.getCceUserMapByUserId("deleteCceUserMapTestuserid1");
        Assert.assertNull(cceUserMap);
    }

    @Test
    public void getCceClustersByUserIdTest() {

        CceCluster cceCluster1 = new CceCluster("name1", "getCceClustersByUserIdTestcceid1", "kubeconfig1", "cceid1 cluster");
        CceCluster cceCluster2 = new CceCluster("name2", "getCceClustersByUserIdTestcceid2", "kubeconfig2", "cceid2 cluster");
        CceCluster cceCluster3 = new CceCluster("name3", "getCceClustersByUserIdTestcceid3", "kubeconfig3", "cceid3 cluster");

        cceClusterService.createCceCluster(cceCluster1);
        cceClusterService.createCceCluster(cceCluster2);
        cceClusterService.createCceCluster(cceCluster3);

        CceUserMap cceUserMap = new CceUserMap("testuserid1", "getCceClustersByUserIdTestcceid1," +
                "getCceClustersByUserIdTestcceid2");
        cceClusterService.createCceUserMap(cceUserMap);

        List<CceCluster> cceClusters = cceClusterService.getCceClustersByUserId("testuserid1");

        Assert.assertEquals(cceClusters.size(), 2);
        Assert.assertEquals(cceClusters.get(0).getCceId(), "getCceClustersByUserIdTestcceid1");
        Assert.assertEquals(cceClusters.get(1).getCceId(), "getCceClustersByUserIdTestcceid2");

    }
}
