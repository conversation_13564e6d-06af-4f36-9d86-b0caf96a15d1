package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.sync.service.ResourceRecycleSyncServiceV2;
import org.springframework.beans.factory.annotation.Autowired;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ResourceRecycleSyncServiceTest {
    @Autowired
    private ResourceRecycleSyncServiceV2 resourceRecycleSyncServiceV2;

    @Test
    public void isResourceRecycleTimeoutTest() {
        PodPO podPO = new PodPO();
        // 2023年6月1日08时30分12秒961毫秒
        long timeStamp = 1685579412961L;
        podPO.setResourceRecycleTimestamp(timeStamp);

        podPO.setTidal(false);
        boolean ans1 = ReflectionTestUtils.invokeMethod(resourceRecycleSyncServiceV2, "isResourceRecycleTimeout", podPO);
        Assert.assertTrue(ans1);

        podPO.setTidal(true);
        boolean ans2 = ReflectionTestUtils.invokeMethod(resourceRecycleSyncServiceV2, "isResourceRecycleTimeout", podPO);
        Assert.assertTrue(ans2);
    }
}
