package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class OrderClientMock extends OrderClient implements ThrowExceptionMock {

    @Autowired
    DatabaseUtil databaseUtil;

    private RuntimeException throwException;

    public OrderClientMock() {
        super(null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public Order get(String uuid) {
        Order order = null;
        try {
            if (uuid == "080ac425c0864acea046af31f4f7ead3") {
                // 预留实例券订单，状态 ready_for_create
                order = TestUtil.fromJson(
                        databaseUtil.getResource("classpath*:reserved_instance_order.json").getInputStream(), 
                        Order.class);
            } else if (uuid == "080ac425c0864acea046af31f4f7ead4") {
                // 预留实例券订单，状态 created
                order = TestUtil.fromJson(
                        databaseUtil.getResource("classpath*:reserved_instance_order.json").getInputStream(), 
                        Order.class);
                order.setStatus(OrderStatus.CREATED);
            } else if (uuid == "080ac425c0864acea046af31f4f7ead5") {
                // 预留实例券订单，状态 create_failed
                order = TestUtil.fromJson(
                        databaseUtil.getResource("classpath*:reserved_instance_order.json").getInputStream(), 
                        Order.class);
                order.setStatus(OrderStatus.CREATE_FAILED);
            } else {
                order = TestUtil.fromJson(
                        databaseUtil.getResource("classpath*:order.json").getInputStream(), Order.class);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return order;
    }

    public Order update(String uuid, UpdateOrderRequest request) {
        return new Order();
    }

    @Override
    public boolean tryLock(String uuid, OrderStatus status, int timeoutInMinute) {
        return true;
    }

    @Override
    public void unlock(String uuid) {
    }
}
