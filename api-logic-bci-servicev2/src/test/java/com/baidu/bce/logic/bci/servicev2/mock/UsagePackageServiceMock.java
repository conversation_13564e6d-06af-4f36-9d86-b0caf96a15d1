package com.baidu.bce.logic.bci.servicev2.mock;

import java.util.LinkedList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.baidu.bce.billing.model.PackageDeductRequest;
import com.baidu.bce.billing.model.PackageEntity;
import com.baidu.bce.billing.model.PackageInfo;
import com.baidu.bce.billing.model.PackageRenewRequest;
import com.baidu.bce.billing.model.PackageSegVO;
import com.baidu.bce.billing.model.PackageShiftInstanceIdRequest;
import com.baidu.bce.billing.model.PackageUsageDetail;
import com.baidu.bce.billing.model.QueryPackageNameParam;
import com.baidu.bce.billing.model.QueryPackageProductNamesParam;
import com.baidu.bce.billing.model.QueryRefundUsagePackageParam;
import com.baidu.bce.billing.model.QuerySegPackageParam;
import com.baidu.bce.billing.model.QueryTimeSpanRunningUsagePackageParam;
import com.baidu.bce.billing.model.QueryUsagePackageParam;
import com.baidu.bce.billing.model.QueryUsagePackageParamV3;
import com.baidu.bce.billing.model.QueryUsageParamWithStatus;
import com.baidu.bce.billing.model.vo.PackageInfoVO;
import com.baidu.bce.billing.service.UsagePackageService;
import com.baidu.bce.fbi.common.PageResult;
import com.baidu.bce.internalsdk.order.model.order.Order;

@Component
public class UsagePackageServiceMock implements UsagePackageService {

    @Override
    public void checkExpire() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'checkExpire'");
    }

    @Override
    public void checkStatus() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'checkStatus'");
    }

    @Override
    public void clearPackageTicket() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'clearPackageTicket'");
    }

    @Override
    public List<PackageInfo> create(Order arg0) {
        return new LinkedList<PackageInfo>();
    }

    @Override
    public List<PackageUsageDetail> deduct(PackageDeductRequest arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'deduct'");
    }

    @Override
    public List<PackageUsageDetail> deductAll(List<PackageEntity> arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'deductAll'");
    }

    @Override
    public List<PackageUsageDetail> deductSegAll(PackageEntity arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'deductSegAll'");
    }

    @Override
    public List<PackageUsageDetail> destroyPackage(List<PackageEntity> arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'destroyPackage'");
    }

    @Override
    public PageResult<PackageInfo> getByServiceType(QueryUsagePackageParam arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getByServiceType'");
    }

    @Override
    public PageResult<PackageInfo> getByServiceTypeAndStatus(QueryUsageParamWithStatus arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getByServiceTypeAndStatus'");
    }

    @Override
    public PageResult<PackageInfo> getByServiceTypeForAll(QueryUsagePackageParamV3 arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getByServiceTypeForAll'");
    }

    @Override
    public PageResult<PackageInfo> getByServiceTypeV2(QueryUsagePackageParam arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getByServiceTypeV2'");
    }

    @Override
    public PageResult<PackageInfo> getByServiceTypeV3(QueryUsagePackageParamV3 arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getByServiceTypeV3'");
    }

    @Override
    public List<PackageInfo> getPackage(List<PackageEntity> arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getPackage'");
    }

    @Override
    public List<PackageInfo> queryPackageName(QueryPackageNameParam arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'queryPackageName'");
    }

    @Override
    public PageResult<PackageInfoVO> queryRefundUsagePackage(QueryRefundUsagePackageParam arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'queryRefundUsagePackage'");
    }

    @Override
    public PageResult<PackageSegVO> querySegPackage(QuerySegPackageParam arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'querySegPackage'");
    }

    @Override
    public PageResult<PackageInfo> queryTimeSpanRunningUsagePackageNoSeg(QueryTimeSpanRunningUsagePackageParam arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'queryTimeSpanRunningUsagePackageNoSeg'");
    }

    @Override
    public List<String> queryUsagePackageProductNames(QueryPackageProductNamesParam arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'queryUsagePackageProductNames'");
    }

    @Override
    public List<PackageInfo> renewPackage(PackageRenewRequest arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'renewPackage'");
    }

    @Override
    public List<PackageInfo> renewReservedPackage(PackageRenewRequest arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'renewReservedPackage'");
    }

    @Override
    public List<PackageInfo> shift(Order arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'shift'");
    }

    @Override
    public PackageInfo shiftBandingInstanceId(PackageShiftInstanceIdRequest arg0) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'shiftBandingInstanceId'");
    }

    
}
