package com.baidu.bce.logic.bci.servicev2.orderexecute;

import static org.junit.Assert.*;

import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.HTTPGetAction;
import com.baidu.bce.logic.bci.servicev2.model.HTTPHeader;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.model.TCPSocketAction;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import io.kubernetes.client.openapi.models.V1Volume;
import io.kubernetes.client.openapi.models.V1VolumeMount;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * ContainerLifecycle高级辅助函数的单元测试类
 * 测试PodNewOrderExecutorServiceV2中handleContainerLifecycle使用的高级辅助函数
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ContainerLifecycleAdvancedMethodsTest {

    @InjectMocks
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        podNewOrderExecutorServiceV2 = PowerMockito.spy(new PodNewOrderExecutorServiceV2());

        // 设置配置参数
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckMaxRetries", 20);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckRetryInterval", 3);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "readinessCheckInitialWait", 5);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartReadinessMaxRetries", 12);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartReadinessRetryInterval", 2);
        ReflectionTestUtils.setField(podNewOrderExecutorServiceV2, "postStartBasicWait", 5);
    }

    // ==================== convertProbHttpGetToExecCommand 测试 ====================

    /**
     * 测试convertProbHttpGetToExecCommand方法 - 基本HTTP GET
     */
    @Test
    public void testConvertProbHttpGetToExecCommand_BasicHttpGet() throws Exception {
        // Arrange
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setPort(8080);
        httpGet.setPath("/health");

        // Act
        String result = (String) invokePrivateMethod("convertProbHttpGetToExecCommand", httpGet);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("8080"));
        assertTrue(result.contains("/health"));
    }

    /**
     * 测试convertProbHttpGetToExecCommand方法 - 带自定义Host
     */
    @Test
    public void testConvertProbHttpGetToExecCommand_WithCustomHost() throws Exception {
        // Arrange
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setHost("custom-host");
        httpGet.setPort(9090);
        httpGet.setPath("/status");

        // Act
        String result = (String) invokePrivateMethod("convertProbHttpGetToExecCommand", httpGet);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("custom-host"));
        assertTrue(result.contains("9090"));
        assertTrue(result.contains("/status"));
    }

    /**
     * 测试convertProbHttpGetToExecCommand方法 - 带HTTP Headers
     */
    @Test
    public void testConvertProbHttpGetToExecCommand_WithHeaders() throws Exception {
        // Arrange
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setPort(8080);
        httpGet.setPath("/api/health");

        List<HTTPHeader> headers = new ArrayList<>();
        HTTPHeader header1 = new HTTPHeader();
        header1.setName("Authorization");
        header1.setValue("Bearer token");
        headers.add(header1);

        HTTPHeader header2 = new HTTPHeader();
        header2.setName("Content-Type");
        header2.setValue("application/json");
        headers.add(header2);

        httpGet.setHttpHeaders(headers);

        // Act
        String result = (String) invokePrivateMethod("convertProbHttpGetToExecCommand", httpGet);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("-H"));
        assertTrue(result.contains("Authorization: Bearer token"));
        assertTrue(result.contains("Content-Type: application/json"));
    }

    /**
     * 测试convertProbHttpGetToExecCommand方法 - HTTPS协议
     */
    @Test
    public void testConvertProbHttpGetToExecCommand_HttpsScheme() throws Exception {
        // Arrange
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setScheme("HTTPS");
        httpGet.setPort(8443);
        httpGet.setPath("/secure/health");

        // Act
        String result = (String) invokePrivateMethod("convertProbHttpGetToExecCommand", httpGet);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("https://"));
        assertTrue(result.contains("8443"));
        assertTrue(result.contains("/secure/health"));
    }

    // ==================== convertTcpSocketToExecCommand 测试 ====================

    /**
     * 测试convertTcpSocketToExecCommand方法 - 基本TCP Socket
     */
    @Test
    public void testConvertTcpSocketToExecCommand_BasicTcpSocket() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(3306);

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("nc"));
        assertTrue(result.contains("3306"));
        assertTrue(result.contains("-z")); // scan mode flag
    }

    /**
     * 测试convertTcpSocketToExecCommand方法 - 带自定义Host
     */
    @Test
    public void testConvertTcpSocketToExecCommand_WithCustomHost() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setHost("database-server");
        tcpSocket.setPort(5432);

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("nc"));
        assertTrue(result.contains("database-server"));
        assertTrue(result.contains("5432"));
    }

    /**
     * 测试convertTcpSocketToExecCommand方法 - 字符串端口
     */
    @Test
    public void testConvertTcpSocketToExecCommand_StringPort() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(80); // 服务名端口

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("nc"));
        assertTrue(result.contains("80"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_NormalCase() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(8080);
        tcpSocket.setHost("localhost");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("8080"));
        assertTrue(result.contains("localhost"));
        assertTrue(result.contains("BCI TCPSocket Probe"));
        assertTrue(result.contains("nc -z"));
        assertTrue(result.contains("/dev/tcp"));
        assertTrue(result.contains("telnet"));
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("python3"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithNullTcpSocket() throws Exception {
        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", (TCPSocketAction) null);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("TCPSocket probe is null"));
        assertTrue(result.contains("false"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithDefaultHost() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(3306);
        tcpSocket.setHost(null); // 应该默认为localhost

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("3306"));
        assertTrue(result.contains("localhost"));
        assertTrue(result.contains("BCI TCPSocket Probe"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithValidPortRange() throws Exception {
        // Arrange - 测试端口边界值
        TCPSocketAction tcpSocket1 = new TCPSocketAction();
        tcpSocket1.setPort(1); // 最小端口
        tcpSocket1.setHost("127.0.0.1");

        TCPSocketAction tcpSocket2 = new TCPSocketAction();
        tcpSocket2.setPort(65535); // 最大端口
        tcpSocket2.setHost("example.com");

        // Act
        String result1 = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket1);
        String result2 = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket2);

        // Assert
        assertNotNull(result1);
        assertTrue(result1.contains("1"));
        assertTrue(result1.contains("127.0.0.1"));
        assertFalse(result1.contains("Invalid port"));

        assertNotNull(result2);
        assertTrue(result2.contains("65535"));
        assertTrue(result2.contains("example.com"));
        assertFalse(result2.contains("Invalid port"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithInvalidPortTooLow() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(0); // 无效端口
        tcpSocket.setHost("localhost");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Invalid port number: 0"));
        assertTrue(result.contains("false"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithInvalidPortTooHigh() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(65536); // 无效端口
        tcpSocket.setHost("localhost");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Invalid port number: 65536"));
        assertTrue(result.contains("false"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithNegativePort() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(-1); // 负数端口
        tcpSocket.setHost("localhost");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Invalid port number: -1"));
        assertTrue(result.contains("false"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithEmptyHost() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(8080);
        tcpSocket.setHost(""); // 空主机名

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Host name is empty"));
        assertTrue(result.contains("false"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithWhitespaceHost() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(8080);
        tcpSocket.setHost("   "); // 只有空格的主机名

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Host name is empty"));
        assertTrue(result.contains("false"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithValidIpAddress() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(443);
        tcpSocket.setHost("*************");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("443"));
        assertTrue(result.contains("*************"));
        assertTrue(result.contains("BCI TCPSocket Probe"));
        // 验证包含多种检测方式
        assertTrue(result.contains("nc -z"));
        assertTrue(result.contains("/dev/tcp"));
        assertTrue(result.contains("telnet"));
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("python3"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithDomainName() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(22);
        tcpSocket.setHost("example.com");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("22"));
        assertTrue(result.contains("example.com"));
        assertTrue(result.contains("BCI TCPSocket Probe"));
        // 验证命令结构
        assertTrue(result.contains("timeout 3 nc -z example.com 22"));
        assertTrue(result.contains("timeout 3 bash -c 'exec 3<>/dev/tcp/example.com/22"));
        assertTrue(result.contains("timeout 3 bash -c 'echo \"\" | telnet example.com 22"));
        assertTrue(result.contains("timeout 3 curl -s --connect-timeout 2 --max-time 3 telnet://example.com:22"));
        assertTrue(result.contains("timeout 3 python3 -c \"import socket; s=socket.socket(); s.settimeout(2); s.connect(('example.com', 22))"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_CommandStructureValidation() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(9090);
        tcpSocket.setHost("test-host");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);

        // 验证命令的基本结构
        assertTrue(result.startsWith("echo 'BCI TCPSocket Probe: Testing connection to test-host:9090...'"));
        assertTrue(result.contains("("));
        assertTrue(result.endsWith(")"));

        // 验证所有备选方案都存在
        assertTrue(result.contains("nc -z test-host 9090"));
        assertTrue(result.contains("/dev/tcp/test-host/9090"));
        assertTrue(result.contains("telnet test-host 9090"));
        assertTrue(result.contains("telnet://test-host:9090"));
        assertTrue(result.contains("('test-host', 9090)"));

        // 验证成功消息
        assertTrue(result.contains("TCP connection successful via nc to test-host:9090"));
        assertTrue(result.contains("TCP connection successful via /dev/tcp to test-host:9090"));
        assertTrue(result.contains("TCP connection successful via telnet to test-host:9090"));
        assertTrue(result.contains("TCP connection successful via curl to test-host:9090"));
        assertTrue(result.contains("TCP connection successful via python to test-host:9090"));

        // 验证失败处理
        assertTrue(result.contains("All TCP connection methods failed for test-host:9090"));
        assertTrue(result.contains("Service may not be ready or network issue exists"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithHighPorts() throws Exception {
        // Arrange - 测试高端口号
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(32768);
        tcpSocket.setHost("service.local");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("32768"));
        assertTrue(result.contains("service.local"));
        assertFalse(result.contains("Invalid port"));
        assertTrue(result.contains("BCI TCPSocket Probe"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithCommonPorts() throws Exception {
        // Arrange - 测试常见端口
        int[] commonPorts = {80, 443, 22, 21, 25, 53, 110, 143, 993, 995, 3306, 5432, 6379, 27017};

        for (int port : commonPorts) {
            TCPSocketAction tcpSocket = new TCPSocketAction();
            tcpSocket.setPort(port);
            tcpSocket.setHost("service");

            // Act
            String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

            // Assert
            assertNotNull(result);
            assertTrue(result.contains(String.valueOf(port)));
            assertTrue(result.contains("service"));
            assertFalse(result.contains("Invalid port"));
            assertTrue(result.contains("BCI TCPSocket Probe"));
        }
    }

    @Test
    public void testConvertTcpSocketToExecCommand_ExceptionHandling() throws Exception {
        // 这个测试验证异常处理分支，虽然实际的异常情况在当前实现中很难直接触发
        // 但我们可以通过其他方式验证异常处理逻辑的存在

        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(8080);
        tcpSocket.setHost("localhost");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        // 正常情况下不应该包含异常处理的消息
        assertFalse(result.contains("TCPSocket probe conversion failed"));
        assertTrue(result.contains("8080"));
        assertTrue(result.contains("localhost"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_WithSpecialCharactersInHost() throws Exception {
        // Arrange - 测试包含特殊字符的主机名（虽然不常见，但需要处理）
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(8080);
        tcpSocket.setHost("my-service.namespace.svc.cluster.local");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("8080"));
        assertTrue(result.contains("my-service.namespace.svc.cluster.local"));
        assertFalse(result.contains("Host name is empty"));
        assertTrue(result.contains("BCI TCPSocket Probe"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_VerifyTimeoutSettings() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(5000);
        tcpSocket.setHost("timeout-test");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);

        // 验证所有命令都有超时设置
        assertTrue(result.contains("timeout 3 nc"));
        assertTrue(result.contains("timeout 3 bash"));
        assertTrue(result.contains("timeout 3 curl"));
        assertTrue(result.contains("timeout 3 python3"));

        // 验证curl的特定超时参数
        assertTrue(result.contains("--connect-timeout 2"));
        assertTrue(result.contains("--max-time 3"));

        // 验证Python socket的超时设置
        assertTrue(result.contains("s.settimeout(2)"));
    }

    @Test
    public void testConvertTcpSocketToExecCommand_VerifyErrorRedirection() throws Exception {
        // Arrange
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(7777);
        tcpSocket.setHost("error-test");

        // Act
        String result = (String) invokePrivateMethod("convertTcpSocketToExecCommand", tcpSocket);

        // Assert
        assertNotNull(result);

        // 验证错误输出重定向
        assertTrue(result.contains("2>/dev/null"));

        // 验证多个重定向存在
        int errorRedirectCount = result.split("2>/dev/null").length - 1;
        assertTrue(errorRedirectCount >= 4); // 至少4个命令有错误重定向
    }

    // ==================== extractActualCommand 测试 ====================

    /**
     * 测试extractActualCommand方法 - shell包装的命令
     */
    @Test
    public void testExtractActualCommand_ShellWrappedCommand() throws Exception {
        // Arrange
        List<String> command = Arrays.asList("/bin/sh", "-c", "echo 'Hello World'");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", command);

        // Assert
        assertEquals("echo 'Hello World'", result);
    }

    /**
     * 测试extractActualCommand方法 - 直接命令
     */
    @Test
    public void testExtractActualCommand_DirectCommand() throws Exception {
        // Arrange
        List<String> command = Arrays.asList("/usr/bin/healthcheck");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", command);

        // Assert
        assertEquals("/usr/bin/healthcheck", result);
    }

    /**
     * 测试extractActualCommand方法 - 多参数命令
     */
    @Test
    public void testExtractActualCommand_MultiArgumentCommand() throws Exception {
        // Arrange
        List<String> command = Arrays.asList("curl", "-f", "http://localhost:8080/health");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", command);

        // Assert
        assertEquals("curl -f http://localhost:8080/health", result);
    }

    /**
     * 测试extractActualCommand方法 - 空命令列表
     */
    @Test
    public void testExtractActualCommand_EmptyCommand() throws Exception {
        // Arrange
        List<String> command = new ArrayList<>();

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", command);

        // Assert
        assertEquals("", result);
    }

    // ===================================================================================
    // Tests for extractActualCommand method
    // ===================================================================================

    @Test
    public void testExtractActualCommand_WithEmpty() throws Exception {
        // Arrange
        List<String> existingCommand = new ArrayList<>();

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("", result);
    }

    @Test
    public void testExtractActualCommand_WithNull() throws Exception {
        // Arrange
        List<String> existingCommand = null;

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("", result);
    }

    @Test
    public void testExtractActualCommand_WithBinShCommand() throws Exception {
        // Arrange
        List<String> existingCommand = Arrays.asList("/bin/sh", "-c", "echo 'test command'");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("echo 'test command'", result);
    }

    @Test
    public void testExtractActualCommand_WithBinBashCommand() throws Exception {
        // Arrange
        List<String> existingCommand = Arrays.asList("/bin/bash", "-c", "sleep 10; echo 'done'");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("sleep 10; echo 'done'", result);
    }

    @Test
    public void testExtractActualCommand_WithShCommand() throws Exception {
        // Arrange
        List<String> existingCommand = Arrays.asList("sh", "-c", "curl -f http://localhost:8080/health");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("curl -f http://localhost:8080/health", result);
    }

    @Test
    public void testExtractActualCommand_WithBashCommand() throws Exception {
        // Arrange
        List<String> existingCommand = Arrays.asList("bash", "-c", "nc -z localhost 8080");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("nc -z localhost 8080", result);
    }

    @Test
    public void testExtractActualCommand_WithDirectCommand() throws Exception {
        // Arrange
        List<String> existingCommand = Arrays.asList("java", "-jar", "app.jar", "--port=8080");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("java -jar app.jar --port=8080", result);
    }

    @Test
    public void testExtractActualCommand_WithShortCommand() throws Exception {
        // Arrange
        List<String> existingCommand = Arrays.asList("sh", "-c");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("sh -c", result);
    }

    @Test
    public void testExtractActualCommand_WithSingleCommand() throws Exception {
        // Arrange
        List<String> existingCommand = Arrays.asList("echo");

        // Act
        String result = (String) invokePrivateMethod("extractActualCommand", existingCommand);

        // Assert
        assertEquals("echo", result);
    }

    // ==================== getProbeType 测试 ====================

    /**
     * 测试getProbeType方法 - 各种探针类型
     */
    @Test
    public void testGetProbeType_VariousProbeTypes() throws Exception {
        // HTTP探针
        com.baidu.bce.logic.bci.servicev2.model.Probe httpProbe = new com.baidu.bce.logic.bci.servicev2.model.Probe();
        HTTPGetAction httpGet = new HTTPGetAction();
        httpGet.setPort(8080);
        httpProbe.setHttpGet(httpGet);

        String result1 = (String) invokePrivateMethod("getProbeType", httpProbe);
        assertEquals("HTTP", result1);

        // TCP探针
        com.baidu.bce.logic.bci.servicev2.model.Probe tcpProbe = new com.baidu.bce.logic.bci.servicev2.model.Probe();
        TCPSocketAction tcpSocket = new TCPSocketAction();
        tcpSocket.setPort(3306);
        tcpProbe.setTcpSocket(tcpSocket);

        String result2 = (String) invokePrivateMethod("getProbeType", tcpProbe);
        assertEquals("TCPSocket", result2);

        // Exec探针
        com.baidu.bce.logic.bci.servicev2.model.Probe execProbe = new com.baidu.bce.logic.bci.servicev2.model.Probe();
        com.baidu.bce.logic.bci.servicev2.model.ExecAction execAction = new com.baidu.bce.logic.bci.servicev2.model.ExecAction();
        execAction.setCommand(Arrays.asList("test", "-f", "/ready"));
        execProbe.setExec(execAction);

        String result3 = (String) invokePrivateMethod("getProbeType", execProbe);
        assertEquals("Exec", result3);

        // 空探针
        String result4 = (String) invokePrivateMethod("getProbeType", (com.baidu.bce.logic.bci.servicev2.model.Probe) null);
        assertEquals("null", result4);

        // 无动作探针
        com.baidu.bce.logic.bci.servicev2.model.Probe emptyProbe = new com.baidu.bce.logic.bci.servicev2.model.Probe();
        String result5 = (String) invokePrivateMethod("getProbeType", emptyProbe);
        assertEquals("Unknown", result5);
    }

    // ==================== buildReadinessCheck 测试 ====================

    /**
     * 测试buildReadinessCheck方法 - postStart上下文
     */
    @Test
    public void testBuildReadinessCheck_PostStartContext() throws Exception {
        // Arrange
        String readinessCommand = "curl -f http://localhost:8080/health";
        boolean isPostStartContext = true;

        // Act
        String result = (String) invokePrivateMethod("buildReadinessCheck", readinessCommand, isPostStartContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("curl"));
        assertTrue(result.contains("12")); // postStartReadinessMaxRetries
        assertTrue(result.contains("2")); // postStartReadinessRetryInterval
    }

    /**
     * 测试buildReadinessCheck方法 - 非postStart上下文
     */
    @Test
    public void testBuildReadinessCheck_NonPostStartContext() throws Exception {
        // Arrange
        String readinessCommand = "nc -z localhost 3306";
        boolean isPostStartContext = false;

        // Act
        String result = (String) invokePrivateMethod("buildReadinessCheck", readinessCommand, isPostStartContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("nc"));
        assertTrue(result.contains("20")); // readinessCheckMaxRetries
        assertTrue(result.contains("3")); // readinessCheckRetryInterval
    }

    // ==================== isCompositeTestCommand 测试 ====================

    /**
     * 测试isCompositeTestCommand方法 - 组合测试命令
     */
    @Test
    public void testIsCompositeTestCommand_CompositeCommands() throws Exception {
        // Arrange & Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isCompositeTestCommand", "true && sleep 1"));
        assertTrue((Boolean) invokePrivateMethod("isCompositeTestCommand", "echo hello; /bin/true"));
        assertTrue((Boolean) invokePrivateMethod("isCompositeTestCommand", "sleep 5 && echo done && exit 0"));

        assertFalse((Boolean) invokePrivateMethod("isCompositeTestCommand", "curl -f http://localhost:8080"));
        assertFalse((Boolean) invokePrivateMethod("isCompositeTestCommand", "nc -z localhost 3306"));
    }

    // ==================== isSingleTestCommand 测试 ====================

    /**
     * 测试isSingleTestCommand方法 - 单个测试命令
     */
    @Test
    public void testIsSingleTestCommand_SingleCommands() throws Exception {
        // Arrange & Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "true"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "/bin/true"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "echo hello"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "sleep 10"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "exit 0"));

        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "curl localhost"));
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "test -f /ready"));
    }

    // ===================================================================================
    // Tests for isSingleTestCommand method
    // ===================================================================================

    @Test
    public void testIsSingleTestCommand_WithNull() throws Exception {
        // Arrange
        String command = null;

        // Act
        Boolean result = (Boolean) invokePrivateMethod("isSingleTestCommand", command);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsSingleTestCommand_WithEmpty() throws Exception {
        // Arrange
        String command = "";

        // Act
        Boolean result = (Boolean) invokePrivateMethod("isSingleTestCommand", command);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsSingleTestCommand_WithWhitespace() throws Exception {
        // Arrange
        String command = "   ";

        // Act
        Boolean result = (Boolean) invokePrivateMethod("isSingleTestCommand", command);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsSingleTestCommand_WithBasicTestCommands() throws Exception {
        // Arrange and Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "sleep"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "true"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "/bin/true"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "exit 0"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "false"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "/bin/false"));
    }

    @Test
    public void testIsSingleTestCommand_WithSleepWithNumber() throws Exception {
        // Arrange and Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "sleep 5"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "sleep 10"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "sleep 0"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "sleep 3600"));
    }

    @Test
    public void testIsSingleTestCommand_WithSimpleEcho() throws Exception {
        // Arrange and Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "echo hello"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "echo 'test message'"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "echo test"));
    }

    @Test
    public void testIsSingleTestCommand_WithComplexEcho() throws Exception {
        // Arrange and Act & Assert
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "echo hello >> output.txt"));
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "echo $(date)"));
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "echo `date`"));
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "echo hello 2> error.log"));
    }

    @Test
    public void testIsSingleTestCommand_WithRealCommands() throws Exception {
        // Arrange and Act & Assert
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "curl -f http://localhost:8080/health"));
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "nc -z localhost 8080"));
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "python3 -c \"import socket; socket.socket().connect(('localhost', 8080))\""));
        assertFalse((Boolean) invokePrivateMethod("isSingleTestCommand", "wget -q http://localhost:8080/health"));
    }

    @Test
    public void testIsSingleTestCommand_WithCaseInsensitive() throws Exception {
        // Arrange and Act & Assert
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "SLEEP"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "TRUE"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "EXIT 0"));
        assertTrue((Boolean) invokePrivateMethod("isSingleTestCommand", "ECHO hello"));
    }

    // ==================== genePreStopCommand 测试 ====================

    /**
     * 测试genePreStopCommand方法 - 基本preStop命令生成
     */
    @Test
    public void testGenePreStopCommand_BasicGeneration() throws Exception {
        // Arrange
        StringBuilder exitCommand = new StringBuilder();
        ContainerPurchase currContainer = createTestContainerPurchase("current-container");
        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");
        ContainerPurchase nextContainer = createTestContainerPurchase("next-container");

        // Act
        StringBuilder result = (StringBuilder) invokePrivateMethod("genePreStopCommand", exitCommand, currContainer, preContainer, nextContainer);

        // Assert
        assertNotNull(result);
        String commandStr = result.toString();
        assertTrue(commandStr.length() > 0);
        // 应该包含基本的退出逻辑
    }

    /**
     * 测试genePreStopCommand方法 - 带用户命令
     */
    @Test
    public void testGenePreStopCommand_WithUserCommand() throws Exception {
        // Arrange
        StringBuilder exitCommand = new StringBuilder("echo 'user cleanup' && ");
        ContainerPurchase currContainer = createTestContainerPurchase("current-container");
        ContainerPurchase preContainer = createTestContainerPurchase("pre-container");
        ContainerPurchase nextContainer = null;

        // Act
        StringBuilder result = (StringBuilder) invokePrivateMethod("genePreStopCommand", exitCommand, currContainer, preContainer, nextContainer);

        // Assert
        assertNotNull(result);
        String commandStr = result.toString();
        assertTrue(commandStr.contains("echo 'user cleanup'"));
    }

    // ==================== hasNextPriorityContainer 测试 ====================

    /**
     * 测试hasNextPriorityContainer方法 - 有下一个优先级容器
     */
    @Test
    public void testHasNextPriorityContainer_WithNextPriority() throws Exception {
        // Arrange
        ContainerPurchase container1 = createTestContainerPurchase("container-1");
        ContainerPurchase container2 = createTestContainerPurchase("container-2");

        List<ContainerPurchase> containers = Arrays.asList(container1, container2);

        // 设置优先级（假设container1优先级高于container2）
        // 这里需要根据实际的优先级设置逻辑调整

        // Act
        boolean result = (Boolean) invokePrivateMethod("hasNextPriorityContainer", container1, containers);

        // Assert - 具体的断言需要根据实际的优先级逻辑调整
        // 这里只验证方法能正常执行
        assertNotNull(result);
    }

    /**
     * 测试hasNextPriorityContainer方法 - 无下一个优先级容器
     */
    @Test
    public void testHasNextPriorityContainer_NoNextPriority() throws Exception {
        // Arrange
        ContainerPurchase container1 = createTestContainerPurchase("container-1");
        List<ContainerPurchase> containers = Arrays.asList(container1);

        // Act
        boolean result = (Boolean) invokePrivateMethod("hasNextPriorityContainer", container1, containers);

        // Assert
        assertFalse(result); // 单个容器列表中，不应该有下一个优先级容器
    }

    // ==================== getPreviousExitPriorityContainer 测试 ====================

    /**
     * 测试getPreviousExitPriorityContainer方法
     */
    @Test
    public void testGetPreviousExitPriorityContainer() throws Exception {
        // Arrange
        ContainerPurchase container1 = createTestContainerPurchase("container-1");
        ContainerPurchase container2 = createTestContainerPurchase("container-2");
        ContainerPurchase container3 = createTestContainerPurchase("container-3");

        List<ContainerPurchase> containers = Arrays.asList(container1, container2, container3);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", container2, containers);

        // Assert - 具体的断言需要根据实际的退出优先级逻辑调整
        // 这里只验证方法能正常执行且不抛异常
        // 实际的断言需要基于具体的优先级计算逻辑
    }

    // ===================================================================================
    // Tests for getPreviousExitPriorityContainer method
    // ===================================================================================

    @Test
    public void testGetPreviousExitPriorityContainer_WithNull() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = null;
        List<ContainerPurchase> containers = new ArrayList<>();

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetPreviousExitPriorityContainer_WithNullList() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        List<ContainerPurchase> containers = null;

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetPreviousExitPriorityContainer_WithEmptyList() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        List<ContainerPurchase> containers = new ArrayList<>();

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetPreviousExitPriorityContainer_WithHigherPriorityContainer() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        ContainerPurchase higherContainer = createMockContainerWithPriority("higher", "10");
        ContainerPurchase lowerContainer = createMockContainerWithPriority("lower", "3");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, higherContainer, lowerContainer);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNotNull(result);
        assertEquals("higher", result.getName());
    }

    @Test
    public void testGetPreviousExitPriorityContainer_WithMultipleHigherPriority() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        ContainerPurchase highest = createMockContainerWithPriority("highest", "15");
        ContainerPurchase higher = createMockContainerWithPriority("higher", "10");
        ContainerPurchase lower = createMockContainerWithPriority("lower", "3");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, highest, higher, lower);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNotNull(result);
        assertEquals("highest", result.getName());
    }

    @Test
    public void testGetPreviousExitPriorityContainer_WithNoHigherPriority() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "10");
        ContainerPurchase lower1 = createMockContainerWithPriority("lower1", "5");
        ContainerPurchase lower2 = createMockContainerWithPriority("lower2", "3");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, lower1, lower2);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetPreviousExitPriorityContainer_WithNegativePriorities() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "-5");
        ContainerPurchase higherContainer = createMockContainerWithPriority("higher", "5");
        ContainerPurchase lowerContainer = createMockContainerWithPriority("lower", "-10");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, higherContainer, lowerContainer);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNotNull(result);
        assertEquals("higher", result.getName());
    }

    @Test
    public void testGetPreviousExitPriorityContainer_WithSamePriority() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        ContainerPurchase sameContainer = createMockContainerWithPriority("same", "5");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, sameContainer);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getPreviousExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    // ===================================================================================
    // Tests for getNextExitPriorityContainer method
    // ===================================================================================

    @Test
    public void testGetNextExitPriorityContainer() throws Exception {
        // Arrange
        ContainerPurchase container1 = createTestContainerPurchase("container-1");
        ContainerPurchase container2 = createTestContainerPurchase("container-2");
        ContainerPurchase container3 = createTestContainerPurchase("container-3");

        List<ContainerPurchase> containers = Arrays.asList(container1, container2, container3);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", container2, containers);

        // Assert - 具体的断言需要根据实际的退出优先级逻辑调整
        // 这里只验证方法能正常执行且不抛异常
    }

    @Test
    public void testGetNextExitPriorityContainer_WithNull() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = null;
        List<ContainerPurchase> containers = new ArrayList<>();

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetNextExitPriorityContainer_WithNullList() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        List<ContainerPurchase> containers = null;

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetNextExitPriorityContainer_WithEmptyList() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        List<ContainerPurchase> containers = new ArrayList<>();

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetNextExitPriorityContainer_WithLowerPriorityContainer() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        ContainerPurchase higherContainer = createMockContainerWithPriority("higher", "10");
        ContainerPurchase lowerContainer = createMockContainerWithPriority("lower", "3");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, higherContainer, lowerContainer);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNotNull(result);
        assertEquals("lower", result.getName());
    }

    @Test
    public void testGetNextExitPriorityContainer_WithMultipleLowerPriority() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "10");
        ContainerPurchase higher = createMockContainerWithPriority("higher", "15");
        ContainerPurchase lower1 = createMockContainerWithPriority("lower1", "5");
        ContainerPurchase lowest = createMockContainerWithPriority("lowest", "1");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, higher, lower1, lowest);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNotNull(result);
        assertEquals("lowest", result.getName());
    }

    @Test
    public void testGetNextExitPriorityContainer_WithNoLowerPriority() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "1");
        ContainerPurchase higher1 = createMockContainerWithPriority("higher1", "5");
        ContainerPurchase higher2 = createMockContainerWithPriority("higher2", "10");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, higher1, higher2);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetNextExitPriorityContainer_WithNegativePriorities() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        ContainerPurchase higherContainer = createMockContainerWithPriority("higher", "10");
        ContainerPurchase lowerContainer = createMockContainerWithPriority("lower", "-5");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, higherContainer, lowerContainer);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNotNull(result);
        assertEquals("lower", result.getName());
    }

    @Test
    public void testGetNextExitPriorityContainer_WithSamePriority() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "5");
        ContainerPurchase sameContainer = createMockContainerWithPriority("same", "5");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, sameContainer);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetNextExitPriorityContainer_WithMixedPriorities() throws Exception {
        // Arrange
        ContainerPurchase currentContainer = createMockContainerWithPriority("current", "0");
        ContainerPurchase highest = createMockContainerWithPriority("highest", "10");
        ContainerPurchase negative = createMockContainerWithPriority("negative", "-5");
        ContainerPurchase lowest = createMockContainerWithPriority("lowest", "-10");

        List<ContainerPurchase> containers = Arrays.asList(currentContainer, highest, negative, lowest);

        // Act
        ContainerPurchase result = (ContainerPurchase) invokePrivateMethod("getNextExitPriorityContainer", currentContainer, containers);

        // Assert
        assertNotNull(result);
        assertEquals("lowest", result.getName());
    }

    // ==================== getPriority 方法测试 ====================

    /**
     * 测试getPriority方法 - 正常情况
     */
    @Test
    public void testGetPriority_WithValidPriority() throws Exception {
        // Arrange
        ContainerPurchase container = new ContainerPurchase();
        container.setName("test-container");

        List<Environment> envs = new ArrayList<>();
        Environment priorityEnv = new Environment();
        priorityEnv.setKey("BCI_CONTAINER_LAUNCH_PRIORITY");
        priorityEnv.setValue("10");
        envs.add(priorityEnv);

        container.setEnvs(envs);

        // Act
        String result = (String) invokePrivateMethod("getPriority", container, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertNotNull(result);
        assertEquals("10", result);
    }

    /**
     * 测试getPriority方法 - 容器为null
     */
    @Test
    public void testGetPriority_WithNullContainer() throws Exception {
        // Act
        String result = (String) invokePrivateMethod("getPriority", null, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertEquals("0", result);
    }

    /**
     * 测试getPriority方法 - 环境变量为null
     */
    @Test
    public void testGetPriority_WithNullEnvs() throws Exception {
        // Arrange
        ContainerPurchase container = new ContainerPurchase();
        container.setName("test-container");
        container.setEnvs(null);

        // Act
        String result = (String) invokePrivateMethod("getPriority", container, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertEquals("0", result);
    }

    /**
     * 测试getPriority方法 - 找不到匹配的环境变量
     */
    @Test
    public void testGetPriority_WithNoMatchingEnv() throws Exception {
        // Arrange
        ContainerPurchase container = new ContainerPurchase();
        container.setName("test-container");

        List<Environment> envs = new ArrayList<>();
        Environment otherEnv = new Environment();
        otherEnv.setKey("OTHER_ENV");
        otherEnv.setValue("value");
        envs.add(otherEnv);

        container.setEnvs(envs);

        // Act
        String result = (String) invokePrivateMethod("getPriority", container, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertEquals("0", result);
    }

    /**
     * 测试getPriority方法 - 空环境变量列表
     */
    @Test
    public void testGetPriority_WithEmptyEnvs() throws Exception {
        // Arrange
        ContainerPurchase container = new ContainerPurchase();
        container.setName("test-container");
        container.setEnvs(new ArrayList<>());

        // Act
        String result = (String) invokePrivateMethod("getPriority", container, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertEquals("0", result);
    }

    /**
     * 测试getPriority方法 - 多个环境变量中找到匹配的
     */
    @Test
    public void testGetPriority_WithMultipleEnvs() throws Exception {
        // Arrange
        ContainerPurchase container = new ContainerPurchase();
        container.setName("test-container");

        List<Environment> envs = new ArrayList<>();
        Environment env1 = new Environment();
        env1.setKey("OTHER_ENV");
        env1.setValue("other");
        envs.add(env1);

        Environment targetEnv = new Environment();
        targetEnv.setKey("BCI_CONTAINER_LAUNCH_PRIORITY");
        targetEnv.setValue("5");
        envs.add(targetEnv);

        Environment env3 = new Environment();
        env3.setKey("ANOTHER_ENV");
        env3.setValue("another");
        envs.add(env3);

        container.setEnvs(envs);

        // Act
        String result = (String) invokePrivateMethod("getPriority", container, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertEquals("5", result);
    }

    // ==================== sortByPriority 方法测试 ====================

    /**
     * 测试sortByPriority方法 - 正常排序（降序）
     */
    @Test
    public void testSortByPriority_NormalSort() throws Exception {
        // Arrange
        ContainerPurchase container1 = createMockContainerWithLaunchPriority("container1", "5");
        ContainerPurchase container2 = createMockContainerWithLaunchPriority("container2", "10");
        ContainerPurchase container3 = createMockContainerWithLaunchPriority("container3", "1");

        List<ContainerPurchase> containers = Arrays.asList(container1, container2, container3);

        // Act
        @SuppressWarnings("unchecked") List<ContainerPurchase> result = (List<ContainerPurchase>) invokePrivateMethod("sortByPriority", containers, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("container2", result.get(0).getName()); // 优先级10，最高
        assertEquals("container1", result.get(1).getName()); // 优先级5，中等
        assertEquals("container3", result.get(2).getName()); // 优先级1，最低
    }

    /**
     * 测试sortByPriority方法 - 相同优先级
     */
    @Test
    public void testSortByPriority_SamePriority() throws Exception {
        // Arrange
        ContainerPurchase container1 = createMockContainerWithLaunchPriority("container1", "5");
        ContainerPurchase container2 = createMockContainerWithLaunchPriority("container2", "5");

        List<ContainerPurchase> containers = Arrays.asList(container1, container2);

        // Act
        @SuppressWarnings("unchecked") List<ContainerPurchase> result = (List<ContainerPurchase>) invokePrivateMethod("sortByPriority", containers, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        // 相同优先级时，保持原始顺序
    }

    /**
     * 测试sortByPriority方法 - 负数优先级
     */
    @Test
    public void testSortByPriority_NegativePriority() throws Exception {
        // Arrange
        ContainerPurchase container1 = createMockContainerWithLaunchPriority("container1", "-5");
        ContainerPurchase container2 = createMockContainerWithLaunchPriority("container2", "5");
        ContainerPurchase container3 = createMockContainerWithLaunchPriority("container3", "-10");

        List<ContainerPurchase> containers = Arrays.asList(container1, container2, container3);

        // Act
        @SuppressWarnings("unchecked") List<ContainerPurchase> result = (List<ContainerPurchase>) invokePrivateMethod("sortByPriority", containers, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("container2", result.get(0).getName()); // 优先级5，最高
        assertEquals("container1", result.get(1).getName()); // 优先级-5，中等
        assertEquals("container3", result.get(2).getName()); // 优先级-10，最低
    }

    /**
     * 测试sortByPriority方法 - 空列表
     */
    @Test
    public void testSortByPriority_EmptyList() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();

        // Act
        @SuppressWarnings("unchecked") List<ContainerPurchase> result = (List<ContainerPurchase>) invokePrivateMethod("sortByPriority", containers, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * 测试sortByPriority方法 - 单个容器
     */
    @Test
    public void testSortByPriority_SingleContainer() throws Exception {
        // Arrange
        ContainerPurchase container = createMockContainerWithLaunchPriority("container", "5");
        List<ContainerPurchase> containers = Arrays.asList(container);

        // Act
        @SuppressWarnings("unchecked") List<ContainerPurchase> result = (List<ContainerPurchase>) invokePrivateMethod("sortByPriority", containers, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("container", result.get(0).getName());
    }

    /**
     * 测试sortByPriority方法 - 没有设置优先级的容器（使用默认值0）
     */
    @Test
    public void testSortByPriority_ContainersWithoutPriority() throws Exception {
        // Arrange
        ContainerPurchase container1 = createMockContainerWithLaunchPriority("container1", "5");
        ContainerPurchase container2 = new ContainerPurchase(); // 没有设置优先级，默认为0
        container2.setName("container2");
        container2.setEnvs(new ArrayList<>());

        List<ContainerPurchase> containers = Arrays.asList(container1, container2);

        // Act
        @SuppressWarnings("unchecked") List<ContainerPurchase> result = (List<ContainerPurchase>) invokePrivateMethod("sortByPriority", containers, "BCI_CONTAINER_LAUNCH_PRIORITY");

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("container1", result.get(0).getName()); // 优先级5，高于默认值0
        assertEquals("container2", result.get(1).getName()); // 默认优先级0
    }

    // ==================== parseContainerLifecycleMounts 方法测试 ====================

    /**
     * 测试parseContainerLifecycleMounts方法 - 正常情况
     */
    @Test
    public void testParseContainerLifecycleMounts_NormalCase() throws Exception {
        // Act
        V1VolumeMount result = (V1VolumeMount) invokePrivateMethod("parseContainerLifecycleMounts");

        // Assert
        assertNotNull(result);
        assertEquals("exec", result.getName());
        assertEquals("/usr/execbin/", result.getMountPath());
    }

    // ==================== getContainerLifecycleVolumes 方法测试 ====================

    /**
     * 测试getContainerLifecycleVolumes方法 - 有容器优先级
     */
    @Test
    public void testGetContainerLifecycleVolumes_WithContainerPriority() throws Exception {
        // Arrange
        BciOrderExtra orderExtra = new BciOrderExtra();
        List<ContainerPurchase> containers = new ArrayList<>();

        // 创建具有不同优先级的容器
        ContainerPurchase container1 = createMockContainerWithLaunchPriority("container1", "5");
        ContainerPurchase container2 = createMockContainerWithLaunchPriority("container2", "10");
        containers.add(container1);
        containers.add(container2);

        orderExtra.setContainers(containers);

        // Act
        @SuppressWarnings("unchecked") List<V1Volume> result = (List<V1Volume>) invokePrivateMethod("getContainerLifecycleVolumes", orderExtra);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        // 检查第一个volume（lifecycle mount）
        V1Volume lifecycleVolume = result.get(0);
        assertEquals("exec", lifecycleVolume.getName());
        assertNotNull(lifecycleVolume.getHostPath());
        assertEquals("/etc/bci/", lifecycleVolume.getHostPath().getPath());

        // 检查第二个volume（empty dir）
        V1Volume emptyDirVolume = result.get(1);
        assertEquals("workload-emptydir", emptyDirVolume.getName());
        assertNotNull(emptyDirVolume.getEmptyDir());
    }

    /**
     * 测试getContainerLifecycleVolumes方法 - 没有容器优先级
     */
    @Test
    public void testGetContainerLifecycleVolumes_WithoutContainerPriority() throws Exception {
        // Arrange
        BciOrderExtra orderExtra = new BciOrderExtra();
        List<ContainerPurchase> containers = new ArrayList<>();

        // 创建具有相同优先级的容器（退化为没有优先级）
        ContainerPurchase container1 = createMockContainerWithPriorities("container1", "5", "5");
        ContainerPurchase container2 = createMockContainerWithPriorities("container2", "5", "5");
        containers.add(container1);
        containers.add(container2);

        orderExtra.setContainers(containers);

        // Act
        @SuppressWarnings("unchecked") List<V1Volume> result = (List<V1Volume>) invokePrivateMethod("getContainerLifecycleVolumes", orderExtra);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size()); // 没有优先级差异，不需要生命周期卷
    }

    /**
     * 测试getContainerLifecycleVolumes方法 - 空容器列表
     */
    @Test
    public void testGetContainerLifecycleVolumes_EmptyContainers() throws Exception {
        // Arrange
        BciOrderExtra orderExtra = new BciOrderExtra();
        orderExtra.setContainers(new ArrayList<>());

        // Act
        @SuppressWarnings("unchecked") List<V1Volume> result = (List<V1Volume>) invokePrivateMethod("getContainerLifecycleVolumes", orderExtra);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    // ==================== hasContainerPriority 方法测试 ====================

    /**
     * 测试hasContainerPriority方法 - 有不同的启动优先级
     */
    @Test
    public void testHasContainerPriority_WithDifferentLaunchPriority() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();
        ContainerPurchase container1 = createMockContainerWithPriorities("container1", "5", "0");
        ContainerPurchase container2 = createMockContainerWithPriorities("container2", "10", "0");
        containers.add(container1);
        containers.add(container2);

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertTrue(result);
    }

    /**
     * 测试hasContainerPriority方法 - 有不同的退出优先级
     */
    @Test
    public void testHasContainerPriority_WithDifferentExitPriority() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();
        ContainerPurchase container1 = createMockContainerWithPriorities("container1", "0", "5");
        ContainerPurchase container2 = createMockContainerWithPriorities("container2", "0", "10");
        containers.add(container1);
        containers.add(container2);

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertTrue(result);
    }

    /**
     * 测试hasContainerPriority方法 - 所有容器相同优先级
     */
    @Test
    public void testHasContainerPriority_WithSamePriority() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();
        ContainerPurchase container1 = createMockContainerWithPriorities("container1", "5", "5");
        ContainerPurchase container2 = createMockContainerWithPriorities("container2", "5", "5");
        containers.add(container1);
        containers.add(container2);

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertFalse(result);
    }

    /**
     * 测试hasContainerPriority方法 - 空容器列表
     */
    @Test
    public void testHasContainerPriority_EmptyList() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertFalse(result);
    }

    /**
     * 测试hasContainerPriority方法 - null容器列表
     */
    @Test
    public void testHasContainerPriority_NullList() throws Exception {
        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", (Object) null);

        // Assert
        assertFalse(result);
    }

    /**
     * 测试hasContainerPriority方法 - 单个容器
     */
    @Test
    public void testHasContainerPriority_SingleContainer() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();
        ContainerPurchase container = createMockContainerWithPriorities("container", "5", "5");
        containers.add(container);

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertFalse(result); // 只有一个容器，没有对比，返回false
    }

    /**
     * 测试hasContainerPriority方法 - 包含null容器
     */
    @Test
    public void testHasContainerPriority_WithNullContainer() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();
        containers.add(null);
        ContainerPurchase container = createMockContainerWithPriorities("container", "5", "5");
        containers.add(container);

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertFalse(result); // null容器被跳过，实际只有一个有效容器
    }

    /**
     * 测试hasContainerPriority方法 - 启动和退出优先级都不同
     */
    @Test
    public void testHasContainerPriority_BothPrioritiesDifferent() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();
        ContainerPurchase container1 = createMockContainerWithPriorities("container1", "5", "10");
        ContainerPurchase container2 = createMockContainerWithPriorities("container2", "10", "5");
        containers.add(container1);
        containers.add(container2);

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertTrue(result); // 启动和退出优先级都不同
    }

    /**
     * 测试hasContainerPriority方法 - 三个容器混合情况
     */
    @Test
    public void testHasContainerPriority_ThreeContainersMixedCase() throws Exception {
        // Arrange
        List<ContainerPurchase> containers = new ArrayList<>();
        ContainerPurchase container1 = createMockContainerWithPriorities("container1", "5", "5");
        ContainerPurchase container2 = createMockContainerWithPriorities("container2", "5", "5");
        ContainerPurchase container3 = createMockContainerWithPriorities("container3", "10", "5"); // 启动优先级不同
        containers.add(container1);
        containers.add(container2);
        containers.add(container3);

        // Act
        Boolean result = (Boolean) invokePrivateMethod("hasContainerPriority", containers);

        // Assert
        assertTrue(result); // 有容器的启动优先级不同
    }

    // ==================== 辅助方法扩充 ====================

    /**
     * 创建具有指定启动优先级的测试容器
     */
    private ContainerPurchase createMockContainerWithLaunchPriority(String name, String launchPriority) {
        ContainerPurchase container = new ContainerPurchase();
        container.setName(name);

        List<Environment> envs = new ArrayList<>();
        Environment env = new Environment();
        env.setKey("BCI_CONTAINER_LAUNCH_PRIORITY");
        env.setValue(launchPriority);
        envs.add(env);

        container.setEnvs(envs);
        return container;
    }

    /**
     * 创建具有指定启动和退出优先级的测试容器
     */
    private ContainerPurchase createMockContainerWithPriorities(String name, String launchPriority, String exitPriority) {
        ContainerPurchase container = new ContainerPurchase();
        container.setName(name);

        List<Environment> envs = new ArrayList<>();

        Environment launchEnv = new Environment();
        launchEnv.setKey("BCI_CONTAINER_LAUNCH_PRIORITY");
        launchEnv.setValue(launchPriority);
        envs.add(launchEnv);

        Environment exitEnv = new Environment();
        exitEnv.setKey("BCI_CONTAINER_EXIT_PRIORITY");
        exitEnv.setValue(exitPriority);
        envs.add(exitEnv);

        container.setEnvs(envs);
        return container;
    }

    // Helper method to create mock containers with specific exit priorities
    private ContainerPurchase createMockContainerWithPriority(String name, String exitPriority) {
        ContainerPurchase container = new ContainerPurchase();
        container.setName(name);

        List<Environment> envs = new ArrayList<>();
        Environment env = new Environment();
        env.setKey("BCI_CONTAINER_EXIT_PRIORITY");
        env.setValue(exitPriority);
        envs.add(env);

        container.setEnvs(envs);
        return container;
    }

    /**
     * 创建测试用的ContainerPurchase对象
     */
    private ContainerPurchase createTestContainerPurchase(String name) {
        ContainerPurchase containerPurchase = new ContainerPurchase();
        containerPurchase.setName(name);
        containerPurchase.setContainerType(ContainerType.WORKLOAD.getType());
        return containerPurchase;
    }

    /**
     * 通过反射调用私有方法
     */
    private Object invokePrivateMethod(String methodName, Object... args) throws Exception {
        Class<?>[] paramTypes = new Class<?>[args.length];
        for (int i = 0; i < args.length; i++) {
            if (args[i] == null) {
                paramTypes[i] = Object.class;
            } else {
                paramTypes[i] = args[i].getClass();
                // 处理基本类型
                if (paramTypes[i] == Boolean.class) {
                    paramTypes[i] = Boolean.class;
                } else if (paramTypes[i] == Integer.class) {
                    paramTypes[i] = Integer.class;
                }
            }
        }

        Method method = findMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(podNewOrderExecutorServiceV2, args);
    }

    /**
     * 查找匹配的方法
     */
    private Method findMethod(String methodName, Class<?>[] paramTypes) throws NoSuchMethodException {
        Method[] methods = PodNewOrderExecutorServiceV2.class.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                Class<?>[] methodParamTypes = method.getParameterTypes();
                if (methodParamTypes.length == paramTypes.length) {
                    boolean matches = true;
                    for (int i = 0; i < methodParamTypes.length; i++) {
                        if (paramTypes[i] == Object.class) {
                            continue;
                        }
                        if (!isAssignableFrom(methodParamTypes[i], paramTypes[i])) {
                            matches = false;
                            break;
                        }
                    }
                    if (matches) {
                        return method;
                    }
                }
            }
        }
        throw new NoSuchMethodException("Method " + methodName + " not found");
    }

    /**
     * 检查类型兼容性
     */
    private boolean isAssignableFrom(Class<?> expected, Class<?> actual) {
        if (expected.isAssignableFrom(actual)) {
            return true;
        }
        if (expected == boolean.class && actual == Boolean.class) {
            return true;
        }
        if (expected == int.class && actual == Integer.class) {
            return true;
        }
        if (!expected.isPrimitive() && actual == Object.class) {
            return true;
        }
        return false;
    }
} 