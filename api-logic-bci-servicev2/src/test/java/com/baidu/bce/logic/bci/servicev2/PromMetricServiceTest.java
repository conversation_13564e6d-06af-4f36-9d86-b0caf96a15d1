package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.servicev2.model.MetricRspBody;
import com.baidu.bce.logic.bci.servicev2.model.MetricRspResult;
import com.baidu.bce.logic.bci.servicev2.monitor.PromMetricServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.mock.SimpleHttpClientMock;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Test;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.junit.Assert;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.io.InputStream;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*", "javax.security.*"})
@PrepareForTest({LogicUserService.class})
public class PromMetricServiceTest {
    protected static final Logger LOGGER = LoggerFactory.getLogger(PromMetricServiceTest.class);

    @Autowired
    private PromMetricServiceV2 promMetricService;

    @Autowired
    private DatabaseUtil databaseUtil;

    private static final String CPROM_RESP_LOCATION = "classpath*:cprom_resp.json";

    @Before
    public void setUp() {
    }

    @Test
    public void listMetricsByShortIdsTest() throws IOException {
        List<String> ids = new ArrayList<>(Arrays.asList("p-5y9bnjxa", "p-cprnnozz"));
        SimpleHttpClientMock mockClient = new SimpleHttpClientMock();
        mockClient.setResult(getStrFromFile(CPROM_RESP_LOCATION));
        promMetricService.getCPromClient().setHttpClient(mockClient);
        MetricRspBody res = promMetricService.getMetrics(ids);

        Assert.assertEquals(res.getResult().size(), 2);
        for (MetricRspResult result : res.getResult()) {
            if (StringUtils.equals("p-5y9bnjxa", result.getPodShortId())) {
                Assert.assertEquals(13, result.getMetrics().size());
            } else {
                Assert.assertEquals(10, result.getMetrics().size());
            }
        }
    }

    private String getStrFromFile(String location) throws IOException {
        InputStream input = databaseUtil.getResource(location).getInputStream();
        byte[] bytes = new byte[input.available()];
        input.read(bytes);
        return new String(bytes);
    }
}