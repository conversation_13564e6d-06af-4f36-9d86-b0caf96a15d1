package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.EipInfoSyncSchedulerV2;
import com.baidu.bce.logic.bci.servicev2.sync.service.EipInfoSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.verify;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class EipInfoSyncSchedulerV2Test {
    @Mock
    private SchedulerStatistics schedulerStatistics;

    @Mock
    private EipInfoSyncServiceV2 eipSyncService;

    @InjectMocks
    private EipInfoSyncSchedulerV2 eipInfoSyncSchedulerV2;

    @Test
    public void syncEipInfoTest() {
        // Act
        eipInfoSyncSchedulerV2.runScheduledTask();

        // Assert
        verify(schedulerStatistics).beforeSchedulerRun("EipInfoSyncSchedulerV2.runScheduledTask");
        verify(eipSyncService).syncEipInfo();
        verify(schedulerStatistics).afterSchedulerRun("EipInfoSyncSchedulerV2.runScheduledTask");
    }
}
