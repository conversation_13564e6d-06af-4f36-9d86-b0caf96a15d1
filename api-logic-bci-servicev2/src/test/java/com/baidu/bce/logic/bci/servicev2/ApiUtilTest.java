package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.common.ApiUtilV2;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ApiUtilTest {

    @Autowired
    ApiUtilV2 apiUtil;

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void apiZoneToLogicZoneNullTest() {
        apiUtil.apiZoneToLogicZone("api", null);
    }

    @Test
    public void apiZoneToLogicZoneApiTest() {
        Assert.assertNull(apiUtil.apiZoneToLogicZone("", ""));
    }

    @Test
    public void apiZoneToLogicZoneNormalTest() {
        Assert.assertNotNull(apiUtil.apiZoneToLogicZone("API", "cn-bj-a"));
    }

    @Test(expected = RuntimeException.class)
    public void getValueTest() {
        PodPO podPO = new PodPO();
        ApiUtilV2.getValue(podPO, "pod_id");
    }

    @Test
    public void getMethodNameTest() {
        ListRequest listRequest = new ListRequest();
        listRequest.setLogicZone("cn-bj-a");
        apiUtil.convertListRequest("api", listRequest);
    }
}
