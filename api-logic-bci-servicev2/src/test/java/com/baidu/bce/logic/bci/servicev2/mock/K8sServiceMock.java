package com.baidu.bce.logic.bci.servicev2.mock;

import java.util.HashMap;
import java.util.Map;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException.ErrorCode;

import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1Pod;


@Component
@Primary
public class K8sServiceMock extends K8sService implements ThrowExceptionMock {
    public Map<String, V1Pod> pods = new HashMap<>();
    public Map<String, V1ConfigMap> configMaps = new HashMap<>();

    public K8sServiceMock() {
        super();
    }

    @Override
    public synchronized void createConfigMap(V1ConfigMap configMap) {
        String keyName = configMap.getMetadata().getNamespace() + "-" + configMap.getMetadata().getName();
        configMaps.put(keyName, configMap);
    }

    public void deleteConfigMap(String namespace, String configmapName) throws K8sServiceException, ApiException {
        String keyName = namespace + "-" + configmapName;
        configMaps.remove(keyName);
    }

    @Override
    public V1Pod getPod(String namespace, String podName) {
        String keyName = namespace + "-" + podName;
        return pods.get(keyName);
    }
    
    public synchronized void updatePod(String namespace, String name, V1Pod v1Pod) throws K8sServiceException, ApiException {
        String keyName = namespace + "-" + name;
        if (!pods.containsKey(keyName)) {
            throw new K8sServiceException(ErrorCode.POD_NOT_EXISTED, "pod not existed.");
        }
        pods.put(keyName, v1Pod);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setThrowException'");
    }
}
