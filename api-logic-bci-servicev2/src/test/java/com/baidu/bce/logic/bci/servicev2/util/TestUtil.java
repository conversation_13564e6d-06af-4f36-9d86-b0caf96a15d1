package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.mock.ResettableMock;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.aop.framework.Advised;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;

import static org.powermock.api.mockito.PowerMockito.when;

@Component
@PrepareForTest({PodServiceV2.class})
public class TestUtil {

    @Autowired
    private DatabaseUtil databaseUtil;

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    private ContainerDaoV2 containerDao;

    @Autowired
    private ResettableMock[] resettableMocks;

    private static final ObjectMapper MAPPER = new ObjectMapper();

    public void setUp() throws IllegalAccessException, SQLException {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
        for (ResettableMock resettableMock : resettableMocks) {
            resettableMock.reset();
        }
    }

    public static <T> T getTargetObject(Object proxy) throws Exception {
        if ((AopUtils.isJdkDynamicProxy(proxy))) {
            return (T) getTargetObject(((Advised) proxy).getTargetSource().getTarget());
        }
        return (T) proxy;
    }

    public <T> BaseCreateOrderRequestVo<T> orderRequest(InputStream inputStream, Class<T> clazz) throws IOException {
        if (inputStream == null) {
            return null;
        }
        JavaType javaType = MAPPER.getTypeFactory().constructParametricType(BaseCreateOrderRequestVo.class, clazz);
        return MAPPER.readValue(inputStream, javaType);
    }

    public static <T> T fromJson(InputStream inputStream, Class<T> clazz) throws IOException {
        return MAPPER.readValue(inputStream, clazz);
    }
}
