package com.baidu.bce.logic.bci.servicev2.mock;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceID;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceIDGetter;
import com.baidu.bce.logic.bcc.sdk.model.common.IDListRequest;
import com.baidu.bce.logic.bci.daov2.common.model.WebShell;
import com.baidu.bce.logic.bci.servicev2.model.LeakagePodDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.BCMPodDetailRequest;

@Component
public class ControllerMock {
    public HashMap<String, String> pods = new HashMap<String, String>();

    public ControllerMock() {}

    @Authorization(resourceLocation=Authorization.ResourceLocation.NO_RESOURCE_ID, 
                    permissions={AuthorizationConstant.BCI_READ})
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public List<String> list() {
        List<String> result = new ArrayList<String>();
        result.addAll(pods.keySet());
        return result;
    }

    @Authorization(resourceLocation=Authorization.ResourceLocation.IN_CLASS_GETTER, 
    permissions={AuthorizationConstant.BCI_CONTROL})
    @RequestMapping(value = "/batchdelete", method = RequestMethod.DELETE)
    public void batchDelete(@AuthorizationResourceID PodBatchDeleteRequest podID) {
        for (String id : podID.getPodIDs()) {
            pods.remove(id);
        }
    }

    @Authorization(resourceLocation=Authorization.ResourceLocation.IN_CLASS_GETTER, 
    permissions={AuthorizationConstant.BCI_CONTROL})
    @RequestMapping(value = "/deleteleakage", method = RequestMethod.DELETE)
    public void deleteLeakage(@AuthorizationResourceID LeakagePodDeleteRequest podID) {
        for (String id : podID.getPodIDs()) {
            pods.remove(id);
        }
    }

    @Authorization(resourceLocation=Authorization.ResourceLocation.IN_CLASS_GETTER, 
                    permissions={AuthorizationConstant.BCI_READ})
    @RequestMapping(value = "/getBcmPod", method = RequestMethod.GET)
    public String getBcmPod(@AuthorizationResourceID BCMPodDetailRequest podID) {
        return pods.get(podID.getPodIDs().get(0));
    }

    @Authorization(resourceLocation=Authorization.ResourceLocation.IN_STRING, 
                    permissions={AuthorizationConstant.BCI_OPERATE})
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public void update(@AuthorizationResourceID String podID, String value) {
        pods.put(podID, value);
    }

    @Authorization(resourceLocation=Authorization.ResourceLocation.IN_WEBSHELL_REQ, 
                    permissions={AuthorizationConstant.BCI_OPERATE})
    @RequestMapping(value = "/webshell", method = RequestMethod.PUT)
    public void webshell(@AuthorizationResourceID WebShell webshell, String value) {
        pods.put(webshell.getPodId(), value);
    }

    @Authorization(resourceLocation=Authorization.ResourceLocation.IN_ID_LIST, 
                    permissions={AuthorizationConstant.BCI_OPERATE})
    @RequestMapping(value = "/batchupdate", method = RequestMethod.PUT)
    public void batchUpdate(@AuthorizationResourceID IDListRequest podIDs, List<String> values) {
        for (int idx = 0; idx < podIDs.getIds().size(); idx++) {
            pods.put(podIDs.getIds().get(idx), values.get(idx));
        }
    }

    @Authorization(resourceLocation=Authorization.ResourceLocation.IN_CLASS_GETTER, 
                    permissions={AuthorizationConstant.BCI_CONTROL})
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public void delete(@AuthorizationResourceID Identify pod) {
        pods.remove(pod.id);
    }

    public class Identify implements AuthorizationResourceIDGetter {
        public String id;
        public List<String> getPodIDs() {
            List<String> result = new ArrayList<String>();
            result.add(id);
            return result; 
        }
    }
}