package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.imagecachev2.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.ImageDetailDaoV2;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.model.ImageDetailPO;
import io.kubernetes.client.openapi.models.V1Container;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import com.baidu.bce.logic.bci.servicev2.pod.ImageCacheServiceV2;



@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class ImageCacheServiceV2Test {

    @Mock
    private ImageDetailDaoV2 imageDetailDaoV2;

    @Mock
    private ImageCacheDaoV2 imageCacheDaoV2;

    @InjectMocks
    private ImageCacheServiceV2 imageCacheServiceV2;

    private static final String ACCOUNT_ID = "test-account-123";
    private static final String IMAGE_ADDRESS = "registry.example.com/image";
    private static final String IMAGE_VERSION = "v1.0";
    private static final String FULL_IMAGE = IMAGE_ADDRESS + ":" + IMAGE_VERSION;
    private static final String ACCE_IMAGE_ADDRESS = "registry.cache.com/cached-image";
    private static final String ACCE_IMAGE_VERSION = "v1.0-cached";
    private static final String IMAGE_CACHE_ID = "cache-123";

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test the case where imageCachePO is null.
     * The method should still update the container image but not update the cache timestamp.
     */
    @Test
    public void testWhenImageCachePoIsNull() {
        // Create a container with a valid image and pull policy
        V1Container container = new V1Container()
            .image(FULL_IMAGE)
            .imagePullPolicy("IfNotPresent");
        
        List<V1Container> containers = Collections.singletonList(container);
        
        // Create an image detail PO that will be returned by the mock
        ImageDetailPO imageDetail = new ImageDetailPO();
        imageDetail.setImageCacheId(IMAGE_CACHE_ID);
        imageDetail.setAcceImageAddress(ACCE_IMAGE_ADDRESS);
        imageDetail.setAcceImageVersion(ACCE_IMAGE_VERSION);
        
        // Set up the mock to return the image detail when queried
        when(imageDetailDaoV2.getImageDetailByImageCacheTypeAndImageAddressTag(
            eq(IMAGE_ADDRESS), eq(IMAGE_VERSION), eq(ACCOUNT_ID)))
            .thenReturn(Collections.singletonList(imageDetail));
        
        // Set up the mock to return null for the image cache, simulating the scenario we want to test
        when(imageCacheDaoV2.getImageCacheById(eq(IMAGE_CACHE_ID), eq(ACCOUNT_ID)))
            .thenReturn(null);
        
        // Call the method we're testing
        List<V1Container> result = imageCacheServiceV2.getAndSetCanAcceContainersForCcr(containers, ACCOUNT_ID);
        
        // Verify the results
        assertEquals(1, result.size());
        assertEquals(ACCE_IMAGE_ADDRESS + ":" + ACCE_IMAGE_VERSION, container.getImage());
        assertEquals("Always", container.getImagePullPolicy());
        
        // Verify the mock interactions
        verify(imageDetailDaoV2, times(1)).getImageDetailByImageCacheTypeAndImageAddressTag(
            eq(IMAGE_ADDRESS), eq(IMAGE_VERSION), eq(ACCOUNT_ID));
        verify(imageCacheDaoV2, times(1)).getImageCacheById(eq(IMAGE_CACHE_ID), eq(ACCOUNT_ID));
        
        // Most importantly, verify that updateImageCache is never called since imageCachePO was null
        verify(imageCacheDaoV2, never()).updateImageCache(any());
    }
    
    /**
     * Test the scenario where there are multiple image details but all corresponding
     * image caches are null, so no updates should occur.
     */
    @Test
    public void testMultipleImageDetailsWithNullImageCaches() {
        // Create a container with a valid image and pull policy
        V1Container container = new V1Container()
            .image(FULL_IMAGE)
            .imagePullPolicy("IfNotPresent");
        
        List<V1Container> containers = Collections.singletonList(container);
        
        // Create multiple image details with different cache IDs
        ImageDetailPO imageDetail1 = new ImageDetailPO();
        imageDetail1.setImageCacheId("cache-123");
        imageDetail1.setAcceImageAddress(ACCE_IMAGE_ADDRESS);
        imageDetail1.setAcceImageVersion(ACCE_IMAGE_VERSION);
        
        ImageDetailPO imageDetail2 = new ImageDetailPO();
        imageDetail2.setImageCacheId("cache-456");
        imageDetail2.setAcceImageAddress(ACCE_IMAGE_ADDRESS);
        imageDetail2.setAcceImageVersion(ACCE_IMAGE_VERSION + "-alt");
        
        List<ImageDetailPO> imageDetails = new ArrayList<>();
        imageDetails.add(imageDetail1);
        imageDetails.add(imageDetail2);
        
        // Set up the mock to return multiple image details
        when(imageDetailDaoV2.getImageDetailByImageCacheTypeAndImageAddressTag(
            eq(IMAGE_ADDRESS), eq(IMAGE_VERSION), eq(ACCOUNT_ID)))
            .thenReturn(imageDetails);
        
        // All image caches are null
        when(imageCacheDaoV2.getImageCacheById(anyString(), eq(ACCOUNT_ID)))
            .thenReturn(null);
        
        // Call the method we're testing
        List<V1Container> result = imageCacheServiceV2.getAndSetCanAcceContainersForCcr(containers, ACCOUNT_ID);
        
        // Verify results
        assertEquals(1, result.size());
        // Should use the first image detail for setting the container image
        assertEquals(ACCE_IMAGE_ADDRESS + ":" + ACCE_IMAGE_VERSION, container.getImage());
        assertEquals("Always", container.getImagePullPolicy());
        
        // Verify mock interactions - getImageCacheById should be called once for each image detail
        verify(imageCacheDaoV2, times(2)).getImageCacheById(anyString(), eq(ACCOUNT_ID));
        // But updateImageCache should never be called
        verify(imageCacheDaoV2, never()).updateImageCache(any());
    }
}