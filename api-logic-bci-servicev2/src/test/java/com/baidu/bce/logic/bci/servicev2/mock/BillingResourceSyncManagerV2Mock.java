package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;

public class BillingResourceSyncManagerV2Mock extends BillingResourceSyncManagerV2 {
    @Override
    public void doSync(PodPO podPO, long chargeStatusId, String accountId, String podUuid, long resourceVersion,
                       String chargeStatus, int retriedCount) {
        // do nothing
    }
}