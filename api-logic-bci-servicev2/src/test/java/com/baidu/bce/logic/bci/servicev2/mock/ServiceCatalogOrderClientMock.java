package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;

import org.springframework.stereotype.Component;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.TRANSACTION_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.RESERVED_INSTANCE_TRANSACTION_ID;
@Component
public class ServiceCatalogOrderClientMock extends ServiceCatalogOrderClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public ServiceCatalogOrderClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public OrderUuidResult createOrder(CreateOrderRequest request) {
        OrderUuidResult orderUuidResult = new OrderUuidResult();
        orderUuidResult.setOrderId(TRANSACTION_ID);
        if (request.getItems().size() > 0) {
            if (((CreateNewTypeOrderItem)request.getItems().get(0)).getSubProductType() == "ReservedPackage") {
                orderUuidResult.setOrderId(RESERVED_INSTANCE_TRANSACTION_ID);
            }
        }
        return orderUuidResult;
    }
}
