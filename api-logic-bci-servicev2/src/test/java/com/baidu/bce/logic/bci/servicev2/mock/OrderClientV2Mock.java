package com.baidu.bce.logic.bci.servicev2.mock;

import org.springframework.stereotype.Component;

import com.baidu.bce.internalsdk.order.OrderClientV2;
import com.baidu.bce.internalsdk.order.model.OrderFlavorUpdateRequest;

@Component
public class OrderClientV2Mock extends OrderClientV2 implements ThrowExceptionMock {

    public OrderClientV2Mock() {
        super(null, null);
    }
    
    public OrderClientV2Mock(String accessKey, String secretKey) {
        super(accessKey, secretKey);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        return;
    }

    @Override
    public void updateOrderFlavors(OrderFlavorUpdateRequest request) {
        return;
    }

}
