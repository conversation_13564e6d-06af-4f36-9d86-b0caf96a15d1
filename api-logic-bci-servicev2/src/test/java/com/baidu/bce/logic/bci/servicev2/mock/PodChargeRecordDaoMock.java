package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.logic.bci.daov2.chargerecord.PodChargeRecordDaoV2;
import com.baidu.bce.logic.bci.daov2.chargerecord.model.PodChargeRecord;

import java.sql.Timestamp;
import java.util.Date;

public class PodChargeRecordDaoMock extends PodChargeRecordDaoV2 {

    @Override
    public Boolean tryToLockOneLine(Date lockDate, String lockId, Long id, int overTime) {
        return true;
    }

    @Override
    public int updateSucc(Long id) {
        throw new RuntimeException();
    }

    @Override
    public Boolean unLock(Long id) {
        return false;
    }

    @Override
    public PodChargeRecord getNeedToPush() {

        return new PodChargeRecord()
                .setChargeTime(new Timestamp(31536001000L))
                .setLastChargeTime(new Timestamp(31536001000L))
                .setMsgId("2019-11-22-12-18");
    }

    @Override
    public PodChargeRecord getLastPodChargeRecord() {
        return new PodChargeRecord()
                .setChargeTime(new Timestamp(31536001000L))
                .setLastChargeTime(new Timestamp(31536001000L))
                .setMsgId("2019-11-22-12-18");
    }
}
