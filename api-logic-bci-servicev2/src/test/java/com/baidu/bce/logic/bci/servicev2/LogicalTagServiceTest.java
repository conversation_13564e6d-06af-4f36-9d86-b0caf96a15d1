package com.baidu.bce.logic.bci.servicev2;

import org.junit.Test;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalTagServiceV2;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_UUID;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class LogicalTagServiceTest {
    @Autowired
    LogicalTagServiceV2 logicalTagService;

    @Test
    public void hasTagsTest() {
        boolean hasTags = logicalTagService.hasTags(POD_UUID, "test");
        Assert.assertTrue(hasTags);
    }
}
