package com.baidu.bce.logic.bci.servicev2.exception;

import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodAnnotationInvalidTest {

    // 模拟LogicUserService的getRequestId方法
    private static LogicUserService logicUserServiceMock;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        // 模拟静态方法LogicUserService.getRequestId
        mockStatic(LogicUserService.class);
        PowerMockito.when(LogicUserService.getRequestId()).thenReturn("mockRequestId");
    }

    @Test
    public void testPodAnnotationInvalidDefaultMessage() {
        PodExceptions.PodAnnotationInvalid ex = new PodExceptions.PodAnnotationInvalid();
        assertEquals("pod annotation param must in valid specification", ex.getMessage());
    }

    @Test
    public void testPodAnnotationInvalidCustomMessage() {
        String customMessage = "Custom error message";
        PodExceptions.PodAnnotationInvalid ex = new PodExceptions.PodAnnotationInvalid(customMessage);
        assertEquals(customMessage, ex.getMessage());
    }
}
