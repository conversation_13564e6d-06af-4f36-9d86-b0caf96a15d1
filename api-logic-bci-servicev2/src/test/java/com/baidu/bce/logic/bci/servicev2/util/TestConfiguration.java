package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.logic.bci.daov2.chargerecord.PodChargeRecordDaoV2;
import com.baidu.bce.logic.bci.daov2.chargestatus.PodChargeStatusDaoV2;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalZoneResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.mock.AsyncExecutorServiceMock;
import com.baidu.bce.logic.bci.servicev2.mock.LogicPodClientFactoryMock;
import com.baidu.bce.logic.bci.servicev2.mock.LogicResourceServiceMock;
import com.baidu.bce.logic.bci.servicev2.mock.LogicalZoneResourceServiceMock;
import com.baidu.bce.logic.bci.servicev2.mock.PodChargeRecordDaoMock;
import com.baidu.bce.logic.bci.servicev2.mock.PodChargeStatusDaoMock;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import endpoint.EndpointManager;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.context.annotation.Profile;

@Configuration
@EnableAutoConfiguration
@PropertySource("classpath:application-test.properties")
@ComponentScan(
        basePackages = {"com.baidu.bce.*"},
        basePackageClasses = {RegionConfiguration.class},
        excludeFilters =
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = {
                AsyncExecutorService.class,
                LogicPodClientFactoryV2.class,
                LogicalResourceServiceV2.class
        })
)
public class TestConfiguration {
    private DatabaseUtil databaseUtil = new DatabaseUtil();

    public TestConfiguration() throws Exception {
        EndpointManager.setEndpoint("OrderV2", "");
        EndpointManager.setEndpoint("auto-renew", "");
        EndpointManager.setEndpoint("UserSettings", "");
        EndpointManager.setEndpoint("Bus", "");
    }

    @Bean
    @Primary
    public SqlSessionFactoryBean logicSqlSessionFactoryBean() {
        return databaseUtil.getSqlSessionFactoryBean();
    }

    @Bean(name = "logicalJdbcTemplate")
    @Primary
    public JdbcTemplate logicalJdbcTemplate() {
        return databaseUtil.getJdbcTemplate();
    }

    @Bean
    @Primary
    public org.springframework.boot.autoconfigure.web.ServerProperties server() {
        return new org.springframework.boot.autoconfigure.web.ServerProperties();
    }

    @Bean
    public DatabaseUtil databaseUtil() {
        return databaseUtil;
    }

    @Bean
    public LogicPodClientFactoryV2 logicPodClientFactoryV2() {
        return new LogicPodClientFactoryMock();
    }

    @Bean
    public LogicalZoneResourceServiceV2 logicalZoneResourceServiceV2() {
        return new LogicalZoneResourceServiceMock();
    }

    @Bean
    public AsyncExecutorService asyncExecutorService() {
        return new AsyncExecutorServiceMock();
    }

    @Bean
    public LogicalResourceServiceV2 logicalResourceServiceV2() {
        return new LogicResourceServiceMock();
    }

    @Bean
    public ResourceLoader createResourceLoader() {
        return new DefaultResourceLoader();
    }

    @Bean
    public PodChargeRecordDaoV2 podChargeRecordDaoV2() {
        return new PodChargeRecordDaoMock();
    }

    @Bean
    public PodChargeStatusDaoV2 podChargeStatusDaoV2() {
        return new PodChargeStatusDaoMock();
    }
}
