package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.ContainerStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.model.*;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1Volume;
import okhttp3.MediaType;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@PrepareForTest({LogicUserService.class})
public class PodUtilsTest {
    protected static final Logger LOG = LoggerFactory.getLogger(PodUtilsTest.class);

    @Before
    public void setUp() throws IllegalAccessException {        
    }

    @Test 
    public void testRectifyContainerLogResponse() {
        String containerName = "c1";
        Response response = getMockResponse();
        Response newResponse = PodUtils.rectifyContainerLogResponse(containerName, null, response);
        Assert.assertEquals("body from bci cluster", getStringFromResp(newResponse));

        ContainerCurrentState state = new ContainerCurrentState();
        state.setState(ContainerStatus.CONTAINER_STATE_CREATING);
        state.setDetailStatus("");
        response = getMockResponse();
        newResponse = PodUtils.rectifyContainerLogResponse(containerName, state, response);
        Assert.assertEquals("body from bci cluster", getStringFromResp(newResponse));

        state.setDetailStatus(";;");
        response = getMockResponse();
        newResponse = PodUtils.rectifyContainerLogResponse(containerName, state, response);
        Assert.assertEquals("body from bci cluster", getStringFromResp(newResponse));

        state.setDetailStatus(PodConstants.CONTAINER_PULL_IMAGE_ERROR + ";;xxxxx;;xxxxxxxxxxx");
        response = getMockResponse();
        newResponse = PodUtils.rectifyContainerLogResponse(containerName, state, response);
        Assert.assertEquals("container \"c1\" is waiting to start: trying and failing to pull image", 
                            getStringFromResp(newResponse));

        state.setDetailStatus(PodConstants.CONTAINER_PULLING_IMAGE + ";;xxxxx;;xxxxxxxxxxx");
        response = getMockResponse();
        newResponse = PodUtils.rectifyContainerLogResponse(containerName, state, response);
        Assert.assertEquals("container \"c1\" is waiting to start: trying to pull image", 
                            getStringFromResp(newResponse));
    }

    private String getStringFromResp(Response resp) {
        try {
            return resp.body().string();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private Response getMockResponse() {
        MediaType plainText = MediaType.get("text/plain; charset=utf-8");
        RequestBody reqBody = RequestBody.create("body from user", plainText);
        Request request = new Request.Builder().url("http://xxxx").post(reqBody).build();
        ResponseBody respBody = ResponseBody.create("body from bci cluster", plainText);
        Response.Builder builder = new Response.Builder();
        builder = builder.request(request).protocol(Protocol.HTTP_1_1).body(respBody).code(200).message("message");
        return builder.build();
    }

    @Test
    public void isSubnetOverlapTest() {
        String cidr1 = "172.16.0.0/16";
        String cidr2 = "172.17.0.0/16";
        Assert.assertFalse(PodUtils.isSubnetOverlap(cidr1, cidr2));
        cidr2 = "172.16.0.0/14";
        Assert.assertTrue(PodUtils.isSubnetOverlap(cidr1, cidr2));
        cidr2 = "172.16.0.0/16";
        Assert.assertTrue(PodUtils.isSubnetOverlap(cidr1, cidr2));
    }

    @Test
    public void getChargeStatusOfCpt1InstanceTest() {
        String chargeStatus = PodUtils.getChargeStatusOfCpt1Instance("Running", 0, false, 0, 0, "");
        Assert.assertEquals(PodConstants.CHARGE, chargeStatus);

        chargeStatus = PodUtils.getChargeStatusOfCpt1Instance("Failed", 0, false, 0, 0, "");
        Assert.assertEquals(PodConstants.NO_CHARGE, chargeStatus);

        chargeStatus = PodUtils.getChargeStatusOfCpt1Instance("Failed", 10, false, 10, 1, "JOB_POD_COMPLETE");
        Assert.assertEquals(PodConstants.NO_CHARGE, chargeStatus);

        chargeStatus = PodUtils.getChargeStatusOfCpt1Instance("Succeeded", 10, false, 10, 0, "JOB_POD_COMPLETE");
        Assert.assertEquals(PodConstants.NO_CHARGE, chargeStatus);

        chargeStatus = PodUtils.getChargeStatusOfCpt1Instance("Succeeded", 10, true, 10, 0, "JOB_POD_COMPLETE");
        Assert.assertEquals(PodConstants.CHARGE, chargeStatus);

        chargeStatus = PodUtils.getChargeStatusOfCpt1Instance("Failed", 10, false, 10, 0, "JOB_POD_COMPLETE");
        Assert.assertEquals(PodConstants.CHARGE, chargeStatus);
    }

    @Test
    public void testRefreshVolumes() throws Exception {
        Pfs pfs1 = new Pfs();
        pfs1.setDsVolume(true);
        pfs1.setName("ds-1");
        Pfs pfs2 = new Pfs();
        pfs2.setDsVolume(true);
        pfs2.setName("ds-2");
        Pfs pfs3 = new Pfs();
        pfs3.setDsVolume(false);
        pfs3.setName("non-1");
        Pfs pfs4 = new Pfs();
        pfs4.setDsVolume(false);
        pfs4.setName("non-2");
        List<Pfs> oldVolumes = Arrays.asList(pfs1, pfs2, pfs3, pfs4);

        Pfs pfs5 = new Pfs();
        pfs5.setDsVolume(true);
        pfs5.setName("ds-2");
        Pfs pfs6 = new Pfs();
        pfs6.setDsVolume(true);
        pfs6.setName("ds-3");
        Pfs pfs7 = new Pfs();
        pfs7.setDsVolume(true);
        pfs7.setName("ds-4");
        List<Pfs> expectDsVolumes = Arrays.asList(pfs5, pfs6, pfs7);

        List<Pfs> newVolumes = PodUtils.refreshVolumes(oldVolumes, expectDsVolumes);
    
        Set<String> expectVolumeNames = new HashSet<>(Arrays.asList("ds-2", "ds-3", "ds-4", "non-1", "non-2"));
        Set<String> nonExpectVolumeNames = new HashSet<>(Arrays.asList("ds-1"));
        Assert.assertEquals(5, newVolumes.size());
        for (Pfs pfs : newVolumes) {
            if (!expectVolumeNames.contains(pfs.getName())) {
                Assert.assertTrue(false);
            } 
            if (nonExpectVolumeNames.contains(pfs.getName())) {
                Assert.assertTrue(false);
            } 
        }
    }

    @Test
    public void testGetVolumeFromPodPO() {
        Pfs pfs = new Pfs();
        pfs.setName("pfs");
        pfs.setDsVolume(false);
        Nfs nfs = new Nfs();
        nfs.setName("nfs");
        nfs.setDsVolume(true);
        
        PodPO podPO = new PodPO();
        podPO.setPfs(JsonUtil.toJSON(Arrays.asList(pfs)));
        podPO.setNfs(JsonUtil.toJSON(Arrays.asList(nfs)));
        
        Volume volume = PodUtils.getVolumeFromPodPO(podPO);
        Assert.assertEquals(1, volume.getNfs().size());
        Assert.assertEquals("nfs", volume.getNfs().get(0).getName());
        Assert.assertEquals(0, volume.getEmptyDir().size());
        Assert.assertEquals(0, volume.getConfigFile().size());
        Assert.assertEquals(0, volume.getFlexVolume().size());
        Assert.assertEquals(1, volume.getPfs().size());
        Assert.assertEquals("pfs", volume.getPfs().get(0).getName());
        Assert.assertEquals(0, volume.getBos().size());
        Assert.assertEquals(0, volume.getHostPath().size());
    }

    private Volume makeVolume() {
        Pfs pfs1 = new Pfs();
        pfs1.setDsVolume(true);
        pfs1.setName("pfs1");
        Pfs pfs2 = new Pfs();
        pfs2.setDsVolume(false);
        pfs2.setName("pfs2");
        Nfs nfs1 = new Nfs();
        nfs1.setDsVolume(false);
        nfs1.setName("nfs1");
        Nfs nfs2 = new Nfs();
        nfs2.setDsVolume(true);
        nfs2.setName("nfs2");
        EmptyDir emptyDir = new EmptyDir();
        emptyDir.setName("emptyDir");
        emptyDir.setDsVolume(true);
        ConfigFile configFile = new ConfigFile();
        configFile.setName("configFile");
        configFile.setDsVolume(false);

        Volume volume = new Volume();
        volume.setNfs(Arrays.asList(nfs1, nfs2));
        volume.setPfs(Arrays.asList(pfs1, pfs2));
        volume.setEmptyDir(Arrays.asList(emptyDir));
        volume.setConfigFile(Arrays.asList(configFile));
        return volume;
    }

    @Test
    public void testGetVolume2TypeMap() {
        Volume volume = makeVolume();
        Map<String, String> volumeMap = PodUtils.getVolume2TypeMap(volume, true);
        Assert.assertTrue(volumeMap.containsKey("pfs1"));
        Assert.assertTrue(volumeMap.containsKey("nfs2"));
        Assert.assertTrue(volumeMap.containsKey("emptyDir"));

        volumeMap = PodUtils.getVolume2TypeMap(volume, false);

        Assert.assertTrue(volumeMap.containsKey("pfs2"));
        Assert.assertTrue(volumeMap.containsKey("nfs1"));
        Assert.assertTrue(volumeMap.containsKey("configFile"));
    }

    @Test
    public void testFilterVolumes() {
        Volume volume = makeVolume();

        Volume filteredVolume = PodUtils.filterVolumes(volume, false);
        Assert.assertEquals(1, filteredVolume.getNfs().size());
        Assert.assertEquals(1, filteredVolume.getPfs().size());
        Assert.assertEquals(0, filteredVolume.getEmptyDir().size());
        Assert.assertEquals(1, filteredVolume.getConfigFile().size());

        filteredVolume = PodUtils.filterVolumes(volume, true);
        Assert.assertEquals(1, filteredVolume.getNfs().size());
        Assert.assertEquals(1, filteredVolume.getPfs().size());
        Assert.assertEquals(1, filteredVolume.getEmptyDir().size());
        Assert.assertEquals(0, filteredVolume.getConfigFile().size());
    }

    @Test
    public void testContains() {
        V1Volume v1Volume = new V1Volume();
        v1Volume.setName("vol1");
        List<V1Volume> volumes = Arrays.asList(v1Volume);
        
        Assert.assertTrue(PodUtils.contains(volumes, "vol1"));
        Assert.assertFalse(PodUtils.contains(volumes, "vol2"));
    }

    @Test
    public void testIsOrderV3() {
        Label bci3 = new Label();
        bci3.setLabelKey("bci3");
        Label other = new Label();
        other.setLabelKey("other");
        List<Label> metadataLabels = new ArrayList<>(Arrays.asList(other));
    
        Assert.assertFalse(PodUtils.isOrderV3(metadataLabels));
        metadataLabels.add(bci3);
        Assert.assertTrue(PodUtils.isOrderV3(metadataLabels));  
    }

    @Test
    public void testDeepCloneV1Pod() {
        V1Pod origin = new V1Pod();
        origin.setMetadata(new V1ObjectMeta());
        origin.setSpec(new V1PodSpec());
        origin.getMetadata().setName("pod-1");
        origin.getSpec().setHostNetwork(true);
        origin.getSpec().setNodeName("*******");
        V1Pod clone = PodUtils.deepCloneV1Pod(origin);
        Assert.assertEquals(clone, origin);
    }

    @Test
    public void getPodAnnotationValueByKeyTest() {
        String result = PodUtils.getPodAnnotationValueByKey(null, "key1");
        Assert.assertNull(result);

        V1Pod pod = new V1Pod();
        pod.setMetadata(null);
        Assert.assertNull(PodUtils.getPodAnnotationValueByKey(pod, "key1"));
        V1ObjectMeta metadata = new V1ObjectMeta();
        metadata.setAnnotations(null);
        Assert.assertNull(PodUtils.getPodAnnotationValueByKey(pod, "key1"));
        metadata.setAnnotations(new java.util.HashMap<>());
        metadata.getAnnotations().put("key1", "value1");
        metadata.getAnnotations().put("key2", "value2");
        pod.setMetadata(metadata);
        Assert.assertEquals("value1", PodUtils.getPodAnnotationValueByKey(pod, "key1"));
        Assert.assertEquals("value2", PodUtils.getPodAnnotationValueByKey(pod, "key2"));
        Assert.assertNull(PodUtils.getPodAnnotationValueByKey(pod, "key3"));
    }

    @Test
    public void testConvertPodPOToPodDetail() {
        PodPO podPO = new PodPO();
        podPO.setPodId("podId");
        podPO.setPodUuid("podUuid");
        podPO.setName("podName");
        podPO.setNodeName("nodeName");
        podPO.setvCpu(1);
        podPO.setMemory(1024);
        podPO.setProductType("productType");
        podPO.setGpuType("gpuType");
        podPO.setGpuCount(1);
        podPO.setPublicIp("publicIp");
        podPO.setCceUuid("cceUuid");
        podPO.setInternalIp("***********");
        podPO.setInternalIPv6("fe80::1");

        PodDetail podDetail = PodUtils.convertPodPOToPodDetail(podPO);
        Assert.assertEquals(podDetail.getPodId(), podPO.getPodId());
        Assert.assertEquals(podDetail.getPodUuid(), podPO.getPodUuid());
        Assert.assertEquals(podDetail.getName(), podPO.getName());
        Assert.assertEquals(podDetail.getNodeName(), podPO.getNodeName());
    }
}
