package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalTagServiceV2;
import com.baidu.bce.logic.bci.servicev2.mock.OrderClientMock;
import com.baidu.bce.logic.bci.servicev2.mock.ResourceClientMock;
import com.baidu.bce.logic.bci.servicev2.model.ReservedInstanceExtra;
import com.baidu.bce.logic.bci.servicev2.orderexecute.ReservedInstanceOrderExecutorService;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.when;
import static org.mockito.Matchers.anyString;


import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ReservedInstanceOrderExecutorServiceTest {
    @Autowired
    ReservedInstanceOrderExecutorService reservedInstanceOrderExecutorService;

    @Autowired
    DatabaseUtil databaseUtil;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    ReservedInstanceDao reservedInstanceDao;
    
    OrderClient orderClient;

    ResourceClient resourceClient;

    Order order;

    @Mock
    private LogicalTagServiceV2 logicalTagService;

    @Before
    public void init() throws IOException {
        orderClient = new OrderClientMock();
        resourceClient = new ResourceClientMock();
        order = TestUtil.fromJson(
                databaseUtil.getResource("classpath*:reserved_instance_order.json").getInputStream(), Order.class);
    }

    @Test
    public void testProcess() throws IOException {
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        reservedInstancePO.setEffectiveTime(new Timestamp(System.currentTimeMillis()-86400000));
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        reservedInstanceOrderExecutorService.execute(orderClient, resourceClient, order);
        reservedInstanceOrderExecutorService.check(orderClient, resourceClient, order);
    }

    @Test
    public void testIsReservedInstanceOrder() {
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        reservedInstancePO.setOrderId("testIsReservedInstanceOrder");
        reservedInstanceDao.insertReservedInstance(reservedInstancePO);
        order.setUuid("testIsReservedInstanceOrder");
        boolean isReservedInstanceOrder = reservedInstanceOrderExecutorService.isReservedInstanceOrder(order);
        Assert.assertEquals(true, isReservedInstanceOrder);
    }

    @Test
    public void testCreateAutoRenewRule() {
        List<ReservedInstancePO> reservedInstancePOList = new ArrayList<>();
        // empty case
        reservedInstanceOrderExecutorService.createAutoRenewRule(reservedInstancePOList, order);
        // normal case
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        reservedInstancePO.setReservedInstanceId("bci");
        reservedInstancePO.setResourceUuid("testCreateAutoRenewRule");
        reservedInstancePOList.add(reservedInstancePO);
        reservedInstanceOrderExecutorService.createAutoRenewRule(reservedInstancePOList, order);
    }

    @Test
    public void testIsOrderTimeout() {
        order.setUpdateTime(new DateTime().toDate());
        Assert.assertEquals(false, reservedInstanceOrderExecutorService.isOrderTimeout(order));
        order.setUpdateTime(new DateTime().minusHours(1).minusMinutes(1).toDate());
        Assert.assertEquals(true, reservedInstanceOrderExecutorService.isOrderTimeout(order));
    }

    @Test
    public void testCreateUsagePackage() {
        reservedInstanceOrderExecutorService.createUsagePackage(order);
    }
    
    private ReservedInstancePO initReservedInstancePO() {
        ReservedInstancePO reservedInstancePO = new ReservedInstancePO();
        reservedInstancePO.setReservedInstanceId("r-test123");
        reservedInstancePO.setUserId("2e1be1eb99e946c3a543ec5a4eaa7d39");
        reservedInstancePO.setAccountId("2e1be1eb99e946c3a543ec5a4eaa7d39");
        reservedInstancePO.setName("name");
        reservedInstancePO.setStatus(ReservedInstancePO.Status.CREATING);
        reservedInstancePO.setResourceUuid("acfef5fe-c3ab-425d-bf4f-f5c7b8b99bc0");
        reservedInstancePO.setReservedInstanceUuid("test123");
        reservedInstancePO.setScope("AZ");
        reservedInstancePO.setPhysicalZone("Azone-gzdt");
        reservedInstancePO.setOrderId("080ac425c0864acea046af31f4f7ead3");
        reservedInstancePO.setPurchaseMode("FullyPrepay");
        reservedInstancePO.setReservedSpec("bci.gna2.c8m36.1a10");
        reservedInstancePO.setReservedInstanceCount(1);
        reservedInstancePO.setReservedTimeUnit("MONTH");
        reservedInstancePO.setReservedTimePeriod(1);
        reservedInstancePO.setAutoRenew(true);
        reservedInstancePO.setAutoRenewTimeUnit("MONTH");
        reservedInstancePO.setAutoRenewTimePeriod(1);
        reservedInstancePO.setEffectiveTime(new Timestamp(System.currentTimeMillis()));
        reservedInstancePO.setExpireTime(ReservedInstancePO.calcExpireTime(reservedInstancePO.getEffectiveTime(), 
                reservedInstancePO.getReservedTimeUnit(), reservedInstancePO.getReservedTimePeriod()));
        return reservedInstancePO;
    }

    @Test
    public void bindTagsTest() {
        when(logicalTagService.hasTags(anyString(), anyString())).thenReturn(false);
        ReflectionTestUtils.setField(reservedInstanceOrderExecutorService, "logicalTagService", logicalTagService);
        ReservedInstancePO reservedInstancePO = initReservedInstancePO();
        List<ReservedInstancePO> reservedInstancePOList = new ArrayList<>();
        reservedInstancePOList.add(reservedInstancePO);
        order.getItems().get(0).setKey("r-test123");
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        tag.setTagKey("key");
        tag.setTagValue("value");
        tags.add(tag);
        ReservedInstanceExtra extra = new ReservedInstanceExtra();
        extra.setTags(tags);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            order.getItems().get(0).setExtra(objectMapper.writeValueAsString(extra));
        } catch (Exception e) {
            throw new BceException("gen order extra failed");
        }
        reservedInstanceOrderExecutorService.bindTags(reservedInstancePOList, order);
    }
}
