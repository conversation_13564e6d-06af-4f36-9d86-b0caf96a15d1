package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.core.user.LogicUserService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.baidu.bce.billing.auditing.sdk.domain.ResourceInfo;
import com.baidu.bce.logic.bci.servicev2.resource.BciBillingResourceService;
import com.baidu.bce.billing.auditing.sdk.domain.OperatorContext;

import java.io.IOException;
import java.sql.Timestamp;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class BciBillingResourceServiceTest {
    @Autowired
    BciBillingResourceService bciBillingResourceService;

    String accountId = "2e1be1eb99e946c3a543ec5a4eaa7d39";
    String service = "BCI";
    String region = "gz";
    String reservedPackageName = "ReservedPackage";
    String podName = "podName";

    @Test
    public void testGet() {
        bciBillingResourceService.get(accountId, service, reservedPackageName, region);
        bciBillingResourceService.get(accountId, service, podName, region);
    }

    @Test
    public void testStart() {
        ResourceInfo resourceInfo = new ResourceInfo();
        resourceInfo.setAccountId(accountId);
        resourceInfo.setService(service);
        resourceInfo.setRegion(region);
        resourceInfo.setResourceId(reservedPackageName);
        bciBillingResourceService.start(resourceInfo, new OperatorContext());
        resourceInfo.setResourceId(podName);
        bciBillingResourceService.start(resourceInfo, new OperatorContext());
    }

    @Test
    public void testStop() {
        ResourceInfo resourceInfo = new ResourceInfo();
        resourceInfo.setAccountId(accountId);
        resourceInfo.setService(service);
        resourceInfo.setRegion(region);
        resourceInfo.setResourceId(reservedPackageName);
        OperatorContext context = new OperatorContext("OVERDUE");
        bciBillingResourceService.stop(resourceInfo, context);
        resourceInfo.setResourceId(podName);
        context = new OperatorContext("EXPIRED");
        bciBillingResourceService.stop(resourceInfo, context);
    }

    @Test
    public void testDelete() {
        ResourceInfo resourceInfo = new ResourceInfo();
        resourceInfo.setAccountId(accountId);
        resourceInfo.setService(service);
        resourceInfo.setRegion(region);
        resourceInfo.setResourceId(reservedPackageName);
        bciBillingResourceService.delete(resourceInfo, new OperatorContext());
        resourceInfo.setResourceId(podName);
        bciBillingResourceService.delete(resourceInfo, new OperatorContext());
    }

    @Test
    public void testClear() {
        ResourceInfo resourceInfo = new ResourceInfo();
        resourceInfo.setAccountId(accountId);
        resourceInfo.setService(service);
        resourceInfo.setRegion(region);
        resourceInfo.setResourceId(reservedPackageName);
        bciBillingResourceService.clear(resourceInfo, new OperatorContext());
    }
}
