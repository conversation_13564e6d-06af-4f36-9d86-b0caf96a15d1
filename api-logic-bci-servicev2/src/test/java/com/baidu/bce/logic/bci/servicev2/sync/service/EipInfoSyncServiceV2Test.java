package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.model.QueryEipListResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEipResponse;
import com.baidu.bce.logic.bci.daov2.cceuser.CceUserMapDao;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPOWithId;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({EipInfoSyncServiceV2.class, LogicEipClient.class, CceUserMapDao.class, PodDaoV2.class,
        ContainerDaoV2.class})
public class EipInfoSyncServiceV2Test {
    @Mock
    public CceUserMapDao cceUserDao;

    @Mock
    public PodDaoV2 podDao;

    @Autowired
    public ContainerDaoV2 containerDao;

    @Mock
    public LogicPodClientFactory logicPodClientFactory;


    @InjectMocks
    public EipInfoSyncServiceV2 eipInfoSyncServiceV2;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSyncEipInfo() throws Exception {
        // Mock data
        List<CceUserMap> users = new ArrayList<>();
        CceUserMap user = new CceUserMap();
        user.setUserId("userId");
        users.add(user);

        List<PodPOWithId> podPOSActive = new ArrayList<>();
        PodPOWithId podPOWithId = new PodPOWithId();
        podPOWithId.setPublicIp("***********");
        podPOWithId.setInternalIp("***********");
        podPOWithId.setBandwidthInMbps(100);
        podPOWithId.setEipActualStatus("binded");
        podPOWithId.setUserId("userId");
        podPOSActive.add(podPOWithId);

        List<PodPO> podPOS = new ArrayList<>();
        PodPO podPO = new PodPO();
        podPO.setName("testPod");
        podPO.setPodId("p-abcd12345");
        podPO.setPodUuid("p-uuid-abcd12345");
        podPO.setStatus("Running");
        podPO.setSubStatus("default");
        podPO.setInternalStatus("sync");
        podPO.setCpuType("intel");
        podPO.setGpuType("");
        podPO.setProductType("user");
        podPO.setEipUuid("e-uuid-abcd12345");
        podPO.setEipId("e-abcd12345");
        podPO.setEipRouteType("");
        podPO.setEipPayMethod("");
        podPO.setCceUuid("");
        podPO.setSecurityGroupUuid("");
        podPO.setRestartPolicy("Always");
        podPO.setTags("");
        podPO.setOrderId("test-orderId");
        podPO.setCreatedTime(new Timestamp(System.currentTimeMillis()));
        podPO.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
        podPO.setDescription("");
        podPO.setRegion("bj");
        podPO.setResourceUuid("");
        podPO.setTaskStatus("");
        podPO.setNfs("");
        podPO.setEmptyDir("");
        podPO.setConfigFile("");
        podPO.setFlexVolume("");
        podPO.setPfs("");
        podPO.setBos("");
        podPO.setHostPath("");
        podPO.setPodVolumes("");
        podPO.setCephFS("");
        podPO.setResourceRecycleReason("");
        podPO.setCommitDeletedTime(new Timestamp(System.currentTimeMillis()));
        podPO.setPodTags(new ArrayList<>());
        podPO.setSubnetUuid("");
        podPO.setZoneId("");
        podPO.setLogicalZone("");
        podPO.setSubnetType("");
        podPO.setEipGroupId("");
        podPO.setLabels(new ArrayList<>());
        podPO.setZoneSubnets("");
        podPO.setApplication("default");
        podPO.setChargeSource("charge");
        podPO.setChargeAccountId("userId");
        podPO.setConditions("");
        podPO.setCreatedBLSTasksID("");
        podPO.setNodeName("");
        podPO.setBccInstanceId("");
        podPO.setPreemptStatusUpdateTime(new Timestamp(System.currentTimeMillis()));
        podPO.setExtra("");
        podPO.setClientToken("test-clientToken");
        podPO.setPublicIp("***********");
        podPO.setInternalIp("***********");
        podPO.setBandwidthInMbps(100);
        podPO.setEipActualStatus("binded");
        podPO.setUserId("userId");
        podPOS.add(podPO);

        QueryEipListResponse eipListResponse = new QueryEipListResponse();
        QueryEipResponse eipResponse = new QueryEipResponse();
        eipResponse.setEip("***********");
        eipResponse.setInstanceIp("***********");
        eipResponse.setBandwidthInMbps(100);
        eipResponse.setStatus("binded");
        eipListResponse.getEipList().add(eipResponse);

        Map<String, QueryEipResponse> eip2EipInfo = new HashMap<>();
        eip2EipInfo.put("***********", eipResponse);

        // 创建 CceUserMapDao 的 mock 实例
        CceUserMapDao cceUserDao = mock(CceUserMapDao.class);
        // 定义当调用 getActiveUsers("userId") 时返回 users
        when(cceUserDao.getActiveUsers()).thenReturn(users);

        // 创建 PodDaoV2 的 mock 实例
        PodDaoV2 podDao = mock(PodDaoV2.class);
        // 定义当调用 getActiveEipByUser("userId") 时返回 podPOSActive
        when(podDao.getActiveEipByUser("userId")).thenReturn(podPOSActive);

        LogicEipClient logicEipClient = mock(LogicEipClient.class);
        when(logicPodClientFactory.createLogicEipClient("userId")).thenReturn(logicEipClient);
        when(logicEipClient.queryEipBindedV2("ENI")).thenReturn(eipListResponse);

        // Call the method
        eipInfoSyncServiceV2.syncEipInfo();
    }
}
