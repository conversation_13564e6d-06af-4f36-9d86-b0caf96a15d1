package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.servicev2.model.TidalTime;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TidalValidator;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*", "javax.security.*"})
@PrepareForTest({LogicUserService.class})
public class TidalValidatorTest {

    @Test
    public void tidalTimeTest() {
        TidalValidator tidalValidator = new TidalValidator();
        TidalTime tidalTime = tidalValidator.buildCurrentTidalTime();
        tidalTime.setTidalStartSubmitTime("23:30");
        tidalTime.setTidalEndSubmitTime("6:30");

        // 2023 年节假日列表
        String holidayList = "2023-01-01 2023-01-02 2023-01-22 2023-01-23 2023-01-24 2023-01-25 2023-01-26 2023-01-27" +
                " 2023-04-05 2023-05-01 2023-05-02 2023-05-03 2023-06-22 2023-06-23 2023-09-29 2023-10-01 2023-10-02 " +
                "2023-10-03 2023-10-04 2023-10-05 2023-10-06";
        String[] holidaySplit = holidayList.split(" ");
        for (String str : holidaySplit) {
            tidalTime.getHolidayDayList().add(str);
        }

        String timeOffDayList = "2023-01-28 2023-01-29 2023-04-23 2023-05-06 2023-06-25 2023-10-07 2023-10-08";
        String[] timeOffSplit = timeOffDayList.split(" ");
        for (String str : timeOffSplit) {
            tidalTime.getTimeOffDayList().add(str);
        }

        // 非周末夜间
        tidalTime.setCurrentDay(1);
        tidalTime.setCurrentMonth(6);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(5);
        boolean b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // true

        Assert.assertEquals(true, b);
        // 非周末白天
        tidalTime.setCurrentDay(1);
        tidalTime.setCurrentMonth(6);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(12);
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // false
        Assert.assertEquals(false, b);
        // 周末调休，白天
        tidalTime.setCurrentDay(25);
        tidalTime.setCurrentMonth(6);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(12);
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // false
        Assert.assertEquals(false, b);

        // 周末调休，夜间
        tidalTime.setCurrentDay(25);
        tidalTime.setCurrentMonth(6);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(0);
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // true
        Assert.assertEquals(true, b);
        // 正常周末
        tidalTime.setCurrentDay(18);
        tidalTime.setCurrentMonth(6);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(12);
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // true
        Assert.assertEquals(true, b);
        // 节假日，工作日
        tidalTime.setCurrentDay(1);
        tidalTime.setCurrentMonth(5);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(12);
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // true
        Assert.assertEquals(true, b);
        // 潮汐夜间
        tidalTime.setCurrentDay(8);
        tidalTime.setCurrentMonth(5);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(0);
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // true
        Assert.assertEquals(true, b);
        // 更改潮汐时间段
        tidalTime.setCurrentDay(8);
        tidalTime.setCurrentMonth(5);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(0);
        tidalTime.setTidalStartSubmitTime("10:00");
        tidalTime.setTidalEndSubmitTime("23:00");
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // false
        Assert.assertEquals(false, b);
        // 更改潮汐时间段
        tidalTime.setCurrentDay(8);
        tidalTime.setCurrentMonth(5);
        tidalTime.setCurrentYear(2023);
        tidalTime.setCurrentMinute(3);
        tidalTime.setCurrentHour(11);
        tidalTime.setTidalStartSubmitTime("10:00");
        tidalTime.setTidalEndSubmitTime("23:00");
        b = tidalValidator.inTidalRunTime(tidalTime);
        System.out.println(b); // true
        Assert.assertEquals(true, b);
    }
}
