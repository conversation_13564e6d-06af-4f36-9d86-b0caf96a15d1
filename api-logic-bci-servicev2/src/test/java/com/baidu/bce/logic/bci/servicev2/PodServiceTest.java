package com.baidu.bce.logic.bci.servicev2;


import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.internalsdk.bci.model.BcmListEventsRequest;
import com.baidu.bce.internalsdk.bci.model.BcmListEventsResponse;
import com.baidu.bce.internalsdk.bci.model.DockerHubImage;
import com.baidu.bce.internalsdk.bci.model.OfficialImage;
import com.baidu.bce.internalsdk.bci.model.PodEventPO;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.PreemptStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.model.BciCreateResponse;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.BidOption;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.DeletePod;
import com.baidu.bce.logic.bci.servicev2.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.Nfs;
import com.baidu.bce.logic.bci.servicev2.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodDetail;
import com.baidu.bce.logic.bci.servicev2.model.PodExtra;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodVpcResponse;
import com.baidu.bce.logic.bci.servicev2.model.SyncDsContainersRequest;
import com.baidu.bce.logic.bci.servicev2.model.ValidatedItem;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.bci.servicev2.util.UuidUtil;
import com.baidu.bce.logic.bci.servicev2.util.Validator;
import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.exception.CommonExceptions.RequestInvalidException;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AdvisedSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAMESPACE_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAME_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAMESPACE_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.FROM_CONSOLE;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.DELETE_POD_ID;
import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_ID;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@PrepareForTest({LogicUserService.class})
public class PodServiceTest {

    protected static final Logger logger = LoggerFactory.getLogger(PodServiceTest.class);

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    private ContainerDaoV2 containerDao;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Mock
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private Validator validator;

    @Autowired
    private PodConfiguration podConfiguration;

    @Mock
    private CceClusterService cceClusterService;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    private static final String CREATE_POD_LOCATION = "classpath*:create_pod.json";
    private static final String BCM_RESP_LOCATION = "classpath*:bcm_resp.json";

    private static final float FLOATPRECISION = (float) 0.00001;

    private BaseCreateOrderRequestVo<IOrderItem> request;

    @Before
    public void setUp() throws IllegalArgumentException, IllegalAccessException, SQLException, IOException,
            NoSuchFieldException, Exception {
        testUtil.setUp();
        request = testUtil.orderRequest(
                databaseUtil.getResource(CREATE_POD_LOCATION).getInputStream(), IOrderItem.class);
        when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenReturn(new CceUserMap());
        List<CceCluster> cceClusters = new ArrayList<>();
        cceClusters.add(new CceCluster());
        when(cceClusterService.getCceClustersByUserId(ServiceTestConstants.ACCOUNT_ID)).thenReturn(cceClusters);

        // podService 内有一些方法被 @Transactional 修饰了，导致 podService 是一个 spring 代理对象，这里利用反射机制mock的时候，需要先获取 podService 的代理对象。
        Field h = podService.getClass().getDeclaredField("CGLIB$CALLBACK_0");
        h.setAccessible(true);
        Object dynamicAdvisedInterceptor = h.get(podService);
        Field advised = dynamicAdvisedInterceptor.getClass().getDeclaredField("advised");
        advised.setAccessible(true);
        Object target = ((AdvisedSupport)advised.get(dynamicAdvisedInterceptor)).getTargetSource().getTarget();

        ReflectionTestUtils.setField(validator, "cceClusterService", cceClusterService);
        ReflectionTestUtils.setField(target, "cceClusterService", cceClusterService);
        ReflectionTestUtils.setField(target, "validator", validator);
    }

    @Test
    public void downloadConfigFileTest() {
        podService.downloadConfigFile("ffcdfa25-6edc-4b7c-943d-d52250500123", "interceptor", "pod/interceptor");
    }

    /*
    @Test
    public void bindEipTest() {
        podService.bindEipToPod("**************", "ffcdfa25-6edc-4b7c-943d-d52250500123");
    }
    */

    @Test
    public void addCceClusterForUserTest() {
        // 1.account为null
        boolean result = podService.addCceClusterForUser(null);
        Assert.assertFalse(result);

        // 2.account为""
        result = podService.addCceClusterForUser("");
        Assert.assertFalse(result);

        // 3.cceUserMap不为null
        CceUserMap userMap = new CceUserMap();
        when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenReturn(userMap);
        result = podService.addCceClusterForUser(ServiceTestConstants.ACCOUNT_ID);
        Assert.assertTrue(result);

        // 4.第一次cceUserMap为null, lock后不为null
        Answer<CceUserMap> answer = new Answer<CceUserMap>() {
            private int callCount = 0;
            public CceUserMap answer(InvocationOnMock invocation) throws Throwable {
                // 在这里编写自定义逻辑来返回不同的值
                callCount++;
                // 返回不同的值根据调用次数
                if (callCount == 1) {
                    // 第一次调用返回 null
                    return null;
                } else if (callCount == 2) {
                    // 第二次调用返回一个特定的 Map 对象
                    return new CceUserMap();
                } else {
                    // 第三次及以后的调用返回其他值
                    // 可以根据需要进行更多的逻辑处理
                    return null;
                }
            }
        };
        when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenAnswer(answer);
        result = podService.addCceClusterForUser(ServiceTestConstants.ACCOUNT_ID);
        Assert.assertTrue(result);

        // 5.第一次cceUserMap为null, lock后为null && 没有默认cceCluster
        answer = new Answer<CceUserMap>() {
            private int callCount = 0;
            public CceUserMap answer(InvocationOnMock invocation) throws Throwable {
                // 在这里编写自定义逻辑来返回不同的值
                callCount++;
                // 返回不同的值根据调用次数
                if (callCount == 1) {
                    // 第一次调用返回 null
                    return null;
                } else if (callCount == 2) {
                    // 第二次调用返回 null
                    return null;
                } else {
                    // 第三次及以后的调用返回其他值
                    // 可以根据需要进行更多的逻辑处理
                    return null;
                }
            }
        };
        when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenAnswer(answer);
        List<CceCluster> cceClusters = new ArrayList<>();
        when(cceClusterService.getDefaultCceClusters()).thenReturn(cceClusters);
        result = podService.addCceClusterForUser(ServiceTestConstants.ACCOUNT_ID);
        Assert.assertFalse(result);

        // 6.第一次cceUserMap为null, lock后为null && 存在默认cceCluster && createCceUserMap失败
        answer = new Answer<CceUserMap>() {
            private int callCount = 0;
            public CceUserMap answer(InvocationOnMock invocation) throws Throwable {
                // 在这里编写自定义逻辑来返回不同的值
                callCount++;
                // 返回不同的值根据调用次数
                if (callCount == 1) {
                    // 第一次调用返回 null
                    return null;
                } else if (callCount == 2) {
                    // 第二次调用返回 null
                    return null;
                } else {
                    // 第三次及以后的调用返回其他值
                    // 可以根据需要进行更多的逻辑处理
                    return null;
                }
            }
        };
        when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenAnswer(answer);
        cceClusters.add(new CceCluster("","id1","",""));
        // CceUserMap newUserMap = new CceUserMap(ServiceTestConstants.ACCOUNT_ID, cceClusters.get(0).getCceId());
        when(cceClusterService.getDefaultCceClusters()).thenReturn(cceClusters);
        Mockito.when(cceClusterService.createCceUserMap(Mockito.any())).thenReturn(false);
        result = podService.addCceClusterForUser(ServiceTestConstants.ACCOUNT_ID);
        Assert.assertFalse(result);

        // 7.第一次cceUserMap为null, lock后为null && 存在默认cceCluster && createCceUserMap成功
        answer = new Answer<CceUserMap>() {
            private int callCount = 0;
            public CceUserMap answer(InvocationOnMock invocation) throws Throwable {
                // 在这里编写自定义逻辑来返回不同的值
                callCount++;
                // 返回不同的值根据调用次数
                if (callCount == 1) {
                    // 第一次调用返回 null
                    return null;
                } else if (callCount == 2) {
                    // 第二次调用返回 null
                    return null;
                } else {
                    // 第三次及以后的调用返回其他值
                    // 可以根据需要进行更多的逻辑处理
                    return null;
                }
            }
        };
        when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenAnswer(answer);
        // newUserMap = new CceUserMap(ServiceTestConstants.ACCOUNT_ID, cceClusters.get(0).getCceId());
        when(cceClusterService.getDefaultCceClusters()).thenReturn(cceClusters);
        when(cceClusterService.createCceUserMap(Mockito.any())).thenReturn(true);
        result = podService.addCceClusterForUser(ServiceTestConstants.ACCOUNT_ID);
//        Assert.assertTrue(result);
    }

    // @Test
    public void createPodTest() {
        BciCreateResponse response = podService.createPod(request, FROM_CONSOLE);
        Assert.assertNotNull(response.getOrderId());
    }

    // @Test
    public void createPodWithEipTest() {
        BaseCreateOrderRequestVo.Item<IOrderItem> item = new BaseCreateOrderRequestVo.Item<>();
        EipPurchaseRequest config = new EipPurchaseRequest();
        item.setConfig(config);

        config.setServiceType("EIP");
        config.setBandwidthInMbps(10);
        config.setName("eip");
        // config.setCount(1);
        config.setProductType("postpay");

        request.getItems().add(item);
        BciCreateResponse response = podService.createPod(request, FROM_CONSOLE);
        Assert.assertNotNull(response.getOrderId());
    }

    // @Test todo 需要修复
    // @Test(expected = CommonExceptions.ResourceInTaskException.class)
    public void deletePodStatusTest() throws K8sServiceException {
        PodBatchDeleteRequest request = new PodBatchDeleteRequest();
        List<DeletePod> deletePods = new ArrayList<>();
        DeletePod deletePod = new DeletePod();
        deletePod.setPodId("p-3reeev3");
        deletePods.add(deletePod);
        request.setDeletePods(deletePods);
        request.setRelatedReleaseFlag(true);
        podService.deletePod(request);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void createPodRequestInvalidExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        order.setItems(new ArrayList<BaseCreateOrderRequestVo.Item<IOrderItem>>());
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.RestartPolicyInvalidException.class)
    public void createPodRestartPolicyExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setRestartPolicy("wrong_policy");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.VolumeNameInvalidException.class)
    public void createPodVolumeNameExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            Volume volumes = podPurchaseRequest.getVolume();
            List<Nfs> nfs = volumes.getNfs();
            List<EmptyDir> emptyDirs = volumes.getEmptyDir();

            nfs.get(0).setName("same-name");
            emptyDirs.get(0).setName("same-name");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.NameInvalidException.class)
    public void createPodNameExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setName("_test");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.NameInvalidException.class)
    public void createPodNameLengthExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setName("tttttttttttttttttttttttttttttttttt" +
                    "ttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttt" +
                    "ttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttt" +
                    "ttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttt");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.LogicalZoneInvalidException.class)
    public void createPodLogicalZoneExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setLogicalZone("zoneA");
            podPurchaseRequest.setSubnetIds("sub_1");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.LogicalZoneInvalidException.class)
    public void createPodLogicalZoneException1Test() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setLogicalZone("zoneA");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SecurityGroupInvalidException.class)
    public void createPodSecurityGroupExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSecurityGroupId("not_exist");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SecurityGroupInvalidException.class)
    public void createPodSecurityGroupNumExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSecurityGroupId("1,2,3,4,5,6,7,8,9,10,11");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SubnetInvalidException.class)
    public void createPodSubnetExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSubnetId("1");
            podPurchaseRequest.setSubnetIds("1");
            podPurchaseRequest.setLogicalZone("");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SubnetInvalidException.class)
    public void createPodSubnetException1Test() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSubnetId("1");
            podPurchaseRequest.setSubnetUuid("1");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SubnetInvalidException.class)
    public void createPodSubnetException2Test() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSubnetId("");
            podPurchaseRequest.setSubnetIds("");
            podPurchaseRequest.setSubnetUuid("");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SubnetInvalidException.class)
    public void createPodSubnetException3Test() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSubnetId("");
            podPurchaseRequest.setSubnetIds("1,2,3,4,5,6,7,8,9,10,11");
            podPurchaseRequest.setSubnetUuid("");
            podPurchaseRequest.setLogicalZone("");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SubnetInvalidException.class)
    public void createPodSubnetException4Test() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSubnetId("");
            podPurchaseRequest.setSubnetIds("not_exist");
            podPurchaseRequest.setSubnetUuid("");
            podPurchaseRequest.setLogicalZone("");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.SubnetInvalidException.class)
    public void createPodSubnetException5Test() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setSubnetId("");
            podPurchaseRequest.setSubnetIds("sub1,sub1");
            podPurchaseRequest.setSubnetUuid("");
            podPurchaseRequest.setLogicalZone("");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.CpuTypePermissionDenyException.class)
    public void createPodCpuTypeExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setCpuType("test");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.TagInvalidException.class)
    public void createPodTagExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            List<Tag> tags = podPurchaseRequest.getTags();
            Tag tag2 = new Tag();
            tag2.setTagKey("key");
            tag2.setTagValue("tag2");
            tags.add(tag2);
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.TagInvalidException.class)
    public void createPodTagException1Test() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            List<Tag> tags = podPurchaseRequest.getTags();
            Tag tag2 = new Tag();
            tag2.setTagKey(",sss");
            tag2.setTagValue("tag2");
            tags.add(tag2);
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test
    public void listPodTest() {

        PodListRequest listRequest =
                new PodListRequest("", "name", "desc",
                        "name", 1, 10, "");

        LogicPageResultResponse<PodPO> list = podService.listPodsWithPageByMultiKey(listRequest);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list.getResult()));
    }

    @Test
    public void listPodsWithMarkerByMultiKey() {
        // 1.maxKeys错误
        PodListRequest listRequest =
                new PodListRequest("", "", "",
                        "", "", -1, "");
        try {
            LogicMarkerResultResponse<PodPO> list1 = podService.listPodsWithMarkerByMultiKey(listRequest);
            Assert.assertEquals(true, false);
        } catch (RequestInvalidException e) {
            Assert.assertEquals("BadRequest", e.getCode());
            Assert.assertEquals(HttpStatus.ERROR_INPUT_INVALID, e.getHttpStatus());
        } catch (Exception e) {
            Assert.assertEquals("", e.getMessage());
        }

        // 2.FilterMap字段不合法
        listRequest.setMaxKeys(1);
        Map<String, String> filter = new HashMap<>();
        filter.put("name", "/.&8");
        listRequest.setFilterMap(filter);
        LogicMarkerResultResponse<PodPO> list2 = podService.listPodsWithMarkerByMultiKey(listRequest);
        Assert.assertTrue(CollectionUtils.isEmpty(list2.getResult()));

        // 3.marker为""
        filter = new HashMap<>();
        filter.put("name", "test");
        listRequest.setFilterMap(filter);
        LogicMarkerResultResponse<PodPO> list3 = podService.listPodsWithMarkerByMultiKey(listRequest);
        Assert.assertEquals(list3.getMarker(), "");
        Assert.assertEquals(list3.getIsTruncated(), true);
        Assert.assertEquals(list3.getNextMarker(), "p-4WRtfwef");
        Assert.assertEquals(list3.getResult().size(), 1);
        Assert.assertEquals(list3.getResult().iterator().next().getPodId(), "p-4WRtUI4D");

        // 4.marker为"p-4WRtUI4D"
        listRequest.setMarker("p-4WRtUI4D");
        listRequest.setMaxKeys(3);
        LogicMarkerResultResponse<PodPO> list4 = podService.listPodsWithMarkerByMultiKey(listRequest);
        Assert.assertEquals(list4.getMarker(), "p-4WRtUI4D");
        Assert.assertEquals(list4.getIsTruncated(), true);
        Assert.assertEquals(list4.getNextMarker(), "p-4WRtfvevf");
        Assert.assertEquals(list4.getResult().size(), 3);
        Assert.assertEquals(list4.getResult().iterator().next().getPodId(), "p-4WRtUI4D");

        // 5.marker为"sss"非法
        /*
        listRequest.setMarker("sss");
        listRequest.setMaxKeys(3);
        LogicMarkerResultResponse<PodPO> list5 = podService.listPodsWithMarkerByMultiKey(listRequest);
        Assert.assertEquals(list5.getMarker(), "sss");
        Assert.assertEquals(list5.getIsTruncated(), false);
        Assert.assertEquals(list5.getNextMarker(), null);
        Assert.assertEquals(list5.getResult().size(), 0);
        */

        // 6.指定order
        PodListRequest listRequest1 =
                new PodListRequest("", "", "desc",
                        "name", "", 3, "");
        LogicMarkerResultResponse<PodPO> list6 = podService.listPodsWithMarkerByMultiKey(listRequest1);
        Assert.assertEquals(list6.getMarker(), "");
        Assert.assertEquals(list6.getIsTruncated(), true);
        Assert.assertEquals(list6.getNextMarker(), "p-4WRtfvevf");
        Assert.assertEquals(list6.getResult().size(), 3);
    }

    @Test
    public void listPodByTagTest() {
        PodListRequest listRequest =
                new PodListRequest("", "name", "desc",
                        "tag", 1, 10, "");
        Map<String, String> filter = new HashMap<>();
        filter.put("tag", "tt");
        listRequest.setFilterMap(filter);
        LogicPageResultResponse<PodPO> list = podService.listPodsWithPageByMultiKey(listRequest);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list.getResult()));
    }

    @Test
    public void listPodWithInvalidCharTest() {

        PodListRequest listRequest =
                new PodListRequest("", "name", "desc",
                        "name", 1, 10, "");
        Map<String, String> filter = new HashMap<>();
        filter.put("#@", "*^%");
        listRequest.setFilterMap(filter);
        LogicPageResultResponse<PodPO> list = podService.listPodsWithPageByMultiKey(listRequest);
        Assert.assertTrue(CollectionUtils.isEmpty(list.getResult()));
    }

    @Test
    public void listPodByUuid() {
        List<PodPO> podPOS = podService.getPods(Arrays.asList("ffcdfa25-6edc-4b7c-943d-d5225050086a"));
        Assert.assertTrue(CollectionUtils.isNotEmpty(podPOS));
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void listPodByNullUuid() {
        podService.getPods(null);
    }

    @Test
    public void listDockerHubImageTest() {
        LogicPageResultResponse<DockerHubImage> response = podService.listDockerHubImages("", "", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listDockerHubImageWithKeyWordTest() {
        LogicPageResultResponse<DockerHubImage> response = podService.listDockerHubImages("mysql", "name", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listDockerHubImageTagTest() {
        LogicPageResultResponse<String> response = podService.listDockerHubImageTags("mysql", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listUserImageTest() {
        LogicPageResultResponse<UserImage> response = podService.listUserImage("", "", "",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isEmpty(response.getResult()));
    }

    @Test
    public void listUserImageWithOrderTest() {
        LogicPageResultResponse<UserImage> response = podService.listUserImage("", "", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isEmpty(response.getResult()));
    }

    @Test
    public void listOfficialImageTest() {
        LogicPageResultResponse<OfficialImage> response = podService.listOfficialImage("baidu", "name", "",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listOfficialImageWithOrderTest() {
        LogicPageResultResponse<OfficialImage> response = podService.listOfficialImage("baidu", "name", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listOfficialImageWithNoKeyWordTest() {
        LogicPageResultResponse<OfficialImage> response = podService.listOfficialImage("", "", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }


    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void bindEipRequestInvalidTest() {
        podService.bindEipToPod("**************", "dw");
    }

    @Test
    public void unBindEipTest() {
        podService.unBindEipFromPod(POD_ID);
    }

    @Test
    public void bciQuotaTest() {
        podService.getBciQuota(true);
    }


    @Test
    public void podDetailTest() {
        PodDetail podDetail = podService.podDetail(POD_ID);
        Assert.assertNotNull(podDetail);
    }

    // @Test todo 需要修复
    public void deletePodRelatedReleaseTest() throws K8sServiceException {
        PodBatchDeleteRequest request = new PodBatchDeleteRequest();
        List<DeletePod> deletePods = new ArrayList<>();
        DeletePod deletePod = new DeletePod();
        deletePod.setPodId(DELETE_POD_ID);
        deletePods.add(deletePod);
        request.setDeletePods(deletePods);
        request.setRelatedReleaseFlag(true);

        podService.deletePod(request);
    }

    // @Test todo 需要修复
    public void deletePodTest() throws K8sServiceException {
        PodBatchDeleteRequest request = new PodBatchDeleteRequest();
        List<DeletePod> deletePods = new ArrayList<>();
        DeletePod deletePod = new DeletePod();
        deletePod.setPodId("p-4WRtfvevf");
        deletePods.add(deletePod);
        request.setDeletePods(deletePods);
        request.setRelatedReleaseFlag(false);

        podService.deletePod(request);
    }

    @Test(expected = PodExceptions.PodIdIsEmptyException.class)
    public void deletePodIdIsEmptyTest() throws K8sServiceException {
        podService.deletePod(null);
    }

    @Test
    public void downloadTest() {
        PodListRequest listRequest =
                new PodListRequest("keyword", "podId", "desc",
                        "name", 1, 10, "");
        podService.download(listRequest);
    }

    @Test
    public void podCreateTest() {
        BciCreateResponse response = podService.createPod(request, FROM_CONSOLE);
        Assert.assertNotNull(response.getOrderId());
    }

    @Test
    public void podValidator() {
        ValidatedItem item = validator.validate(request, "test", null);
        Assert.assertNotNull(item);
    }

    @Test
    public void handleInstanceCreateBciTest() {
        ValidatedItem item = validator.validate(request, "test", null);
        Map<String, PodPO> podId2PodPOMap = new HashMap<>();
        Map<String, List<ContainerPO>> podId2ContainerPOMap = new HashMap<>();
        Map<String, BciOrderExtra> podId2BciOrderExtraMap = new HashMap<>();
        List<CreateNewTypeOrderItem> items = podService.handleInstanceCreateBci(
                item,
                podId2PodPOMap,
                podId2ContainerPOMap,
                podId2BciOrderExtraMap);
        Assert.assertNotNull(items);
    }

    @Test
    public void testHandleInstanceCreateGpuBci() {
        ValidatedItem item = validator.validate(request, "test", null);
        item.getPodPurchaseRequest().setGpuCount(1);
        item.getPodPurchaseRequest().setGpuType("Nvidia A100 Nvswitch-80g");
        item.getPodPurchaseRequest().setCpu(14);
        item.getPodPurchaseRequest().setMemory(120);
        item.getPodPurchaseRequest().getContainerPurchases().get(0).setGpuCount(1);
        item.getPodPurchaseRequest().getContainerPurchases().get(0).setGpuType("Nvidia A100 Nvswitch-80g");
        item.getPodPurchaseRequest().getContainerPurchases().get(0).setCpu(14);
        item.getPodPurchaseRequest().getContainerPurchases().get(0).setMemory(120);
        item.getPodPurchaseRequest().setProductType(podConfiguration.getBidProductType());
        BidOption bidOption = new BidOption();
        bidOption.setBidModel(podConfiguration.getBidModelCustom());
        item.getPodPurchaseRequest().setBidOption(bidOption);
        Map<String, PodPO> podId2PodPOMap = new HashMap<>();
        Map<String, List<ContainerPO>> podId2ContainerPOMap = new HashMap<>();
        Map<String, BciOrderExtra> podId2BciOrderExtraMap = new HashMap<>();
        List<CreateNewTypeOrderItem> items = podService.handleInstanceCreateBci(
                item, podId2PodPOMap, podId2ContainerPOMap, podId2BciOrderExtraMap);
        Assert.assertEquals(items.get(0).getBidModel(), podConfiguration.getBidModelCustom());
        Assert.assertEquals(items.get(0).getBidPrice(), null);
        for (Map.Entry<String, PodPO> entry : podId2PodPOMap.entrySet()) {
            String podId = entry.getKey();
            PodPO pod = entry.getValue();
            List<ContainerPO> containers = podId2ContainerPOMap.get(podId);
            Assert.assertTrue(Math.abs(pod.getGpuCount() - 1) < FLOATPRECISION);
            Assert.assertEquals(pod.getGpuType(), "Nvidia A100 Nvswitch-80g");
            Assert.assertEquals(containers.get(0).getGpuType(), "Nvidia A100 Nvswitch-80g");
            Assert.assertTrue(Math.abs(containers.get(0).getGpuCount() - 1) < FLOATPRECISION);
        }
    }

    @Test
    public void testCheckUserId() {
        try {
            when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenReturn(null);
            validator.testCheckUserId();
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),
                    "userId is error, not find CceUserMap: bb45087dee674fcaa21d75b53a35f7fc");
        }
        try {
            when(cceClusterService.getCceUserMapByUserId(ServiceTestConstants.ACCOUNT_ID)).thenReturn(new CceUserMap());
            when(cceClusterService.getCceClustersByUserId(ServiceTestConstants.ACCOUNT_ID)).thenReturn(null);
            validator.testCheckUserId();
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),
                    "userId is error, not find cceCluster: bb45087dee674fcaa21d75b53a35f7fc");
        }
    }

    @Test
    public void testValidateBid() {
        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
        podPurchaseRequest.setProductType(podConfiguration.getBidProductType());
        try {
            validator.testValidateBid(podPurchaseRequest);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "productType=bidding, bidOption and bidModel is required");
        }

        BidOption bidOption = new BidOption();
        bidOption.setBidModel(podConfiguration.getBidModelCustom());
        podPurchaseRequest.setBidOption(bidOption);
        try {
            validator.testValidateBid(podPurchaseRequest);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "bidModel = CUSTOM_BID, bidPrice is required and must >= 0");
        }

        bidOption.setBidPrice(new BigDecimal(-10.0));
        podPurchaseRequest.setBidOption(bidOption);
        try {
            validator.testValidateBid(podPurchaseRequest);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "bidModel = CUSTOM_BID, bidPrice is required and must >= 0");
        }

        bidOption.setBidPrice(new BigDecimal(new Double(10.0)));
        podPurchaseRequest.setBidOption(bidOption);
        validator.testValidateBid(podPurchaseRequest);
    }

    @Test
    public void testGenPodResourceGPU() {
        ContainerPurchase container1 = new ContainerPurchase();
        container1.setCpu(1);
        container1.setMemory(1);
        container1.setGpuCount(0);
        container1.setContainerType(ContainerType.WORKLOAD.getType());
        ContainerPurchase container2 = new ContainerPurchase();
        container2.setCpu(2);
        container2.setMemory(2);
        container2.setGpuCount(0);
        container2.setContainerType(ContainerType.INIT.getType());

        ContainerPurchase container3 = new ContainerPurchase();
        container3.setCpu(0);
        container3.setMemory(0);
        container3.setGpuCount(0);
        container3.setContainerType(ContainerType.WORKLOAD.getType());

        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
        podPurchaseRequest.setContainerPurchases(new ArrayList<ContainerPurchase>());
        podPurchaseRequest.getContainerPurchases().add(container1);
        podPurchaseRequest.getContainerPurchases().add(container2);
        podPurchaseRequest.getContainerPurchases().add(container3);
        podPurchaseRequest.setProductType("Postpay");
        ValidatedItem validatedItem = new ValidatedItem();
        // no gpu type, cpu cac
        validator.testGenPodResource(podPurchaseRequest, validatedItem);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getCpu() - 2) < FLOATPRECISION);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getMemory() - 4) < FLOATPRECISION);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getGpuCount() - 0) < FLOATPRECISION);
        Assert.assertEquals(podPurchaseRequest.getGpuType(), "");

        // error gpu type
        container1.setGpuCount(2);
        container2.setGpuCount(3);
        container1.setGpuType("Nvidia A10 PCIE");
        container2.setGpuType("Nvidia A10 PCIE22");
        try {
            validator.testGenPodResource(podPurchaseRequest, validatedItem);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "The container's gpuType must be consistent.");
        }

        // gpu
        container2.setGpuType("Nvidia A10 PCIE");
        validator.testGenPodResource(podPurchaseRequest, validatedItem);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getCpu() - 62) < FLOATPRECISION);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getMemory() - 240) < FLOATPRECISION);
        Assert.assertTrue(Math.abs(podPurchaseRequest.getGpuCount() - 4) < FLOATPRECISION);
        Assert.assertTrue(podPurchaseRequest.getGpuType().equals("Nvidia A10 PCIE"));

        podConfiguration.getGpuBCCSpecMap().remove("Nvidia A10 PCIE|4|62|240");
        try {
            validator.testGenPodResource(podPurchaseRequest, validatedItem);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "Internal service occurs error.");
        }
        podConfiguration.getGpuBCCSpecMap().put("Nvidia A10 PCIE|4|62|240", "bci.gna2.c62m240.4a10");

        container3.setGpuType("Nvidia A10 PCIE");
        try {
            validator.testGenPodResource(podPurchaseRequest, validatedItem);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "The container's gpuCount should be greater than 0.");
        }

        container3.setGpuType("");
        // error gpu count
        container2.setGpuCount(10);
        try {
            validator.testGenPodResource(podPurchaseRequest, validatedItem);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "The pod cpu and memory should in valid specification.");
        }
        container2.setGpuCount(3);

        // error cpu
        container2.setCpu(10000);
        try {
            validator.testGenPodResource(podPurchaseRequest, validatedItem);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "The pod cpu and memory should in valid specification.");
        }
        container2.setCpu(1);
        // error mem
        container2.setMemory(10000);
        try {
            validator.testGenPodResource(podPurchaseRequest, validatedItem);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "The pod cpu and memory should in valid specification.");
        }

    }

    @Test
    public void testSpec() {
        Assert.assertEquals(PodConfiguration.gpuSpecGpuMemoryMap.get("xxx"), null);
        Assert.assertEquals(podConfiguration.getGpuSpecK8sResourceMap().get("Nvidia A10 PCIE"),
                "baidu.com/a10_24g_cgpu");
        Assert.assertEquals(podConfiguration.getGpuBCCSpecMap().get("Nvidia A100 Nvswitch-80g|1|14|120"),
                "bcc.gn5.c14m120.1A100-80g");
    }

    @Test
    public void biddingStatusConvertTest() {

        List<PodPO> podPOS1 = new ArrayList<>();
        PodPO podP1 = new PodPO();
        podP1.setProductType("bidding");
        podP1.setStatus("Pending");
        podPOS1.add(podP1);

        List<PodPO> podPOS2 = new ArrayList<>();
        PodPO podP2 = new PodPO();
        podP2.setProductType("PostPay");
        podP2.setStatus("Pending");
        podPOS2.add(podP2);

        List<PodPO> podPOS3 = new ArrayList<>();
        PodPO podP3 = new PodPO();
        podP3.setProductType("bidding");
        podP3.setStatus("Succeeded");
        podPOS3.add(podP3);

        List<PodPO> podPOS4 = new ArrayList<>();
        PodPO podP4 = new PodPO();
        podP4.setProductType("PostPay");
        podP4.setStatus("Succeeded");
        podPOS4.add(podP4);

        List<PodPO> podPOS5 = new ArrayList<>();
        PodPO podP5 = new PodPO();
        podP5.setProductType("bidding");
        podP5.setStatus("Running");
        podPOS5.add(podP5);

        List<PodPO> podPOS6 = new ArrayList<>();
        PodPO podP6 = new PodPO();
        podP6.setProductType("bidding");
        podP6.setStatus("Succeeded");
        podP6.setPreemptStatus(PreemptStatus.PREEMPTED.name());
        podPOS6.add(podP6);

        podService.biddingStatusConvert(podPOS1);
        podService.biddingStatusConvert(podPOS2);
        podService.biddingStatusConvert(podPOS3);
        podService.biddingStatusConvert(podPOS4);
        podService.biddingStatusConvert(podPOS5);
        podService.biddingStatusConvert(podPOS6);

        Assert.assertEquals(podPOS1.get(0).getStatus(), "Bidding");
        Assert.assertEquals(podPOS2.get(0).getStatus(), "Pending");
        Assert.assertEquals(podPOS3.get(0).getStatus(), "Succeeded");
        Assert.assertEquals(podPOS4.get(0).getStatus(), "Succeeded");
        Assert.assertEquals(podPOS5.get(0).getStatus(), "Running");
        Assert.assertEquals(podPOS6.get(0).getStatus(), "Recycled");
    }

    @Test
    public void testConvertToContainerCurrentState() {
        String rawState = "{\"state\": \"RUNNING\", \"detailStatus\": \"status\", \"exitCode\": 10}";
        ContainerCurrentState state = podService.convertToContainerCurrentState(rawState);
        Assert.assertEquals("RUNNING", state.getState());
        Assert.assertEquals("status", state.getDetailStatus());
        Assert.assertEquals(10, state.getExitCode());
    }

    @Test
    public void testGetVpcAndSubnetByPodId() {
        PodVpcResponse response = podService.getVpcAndSubnetByPodId(POD_ID);
        Assert.assertNotNull(response);
    }

    @Test
    public void testConsBcmListEventsRequest() {
        BcmListEventsRequest req = podService.consBcmListEventsRequest(1, 10, 1, "p-xcvdfe1");
        Assert.assertEquals(1, req.getPageNo());
        Assert.assertEquals(10, req.getPageSize());
        Assert.assertEquals(24*3600*1000, req.getEndTime().getTime()-req.getStartTime().getTime());
        Assert.assertEquals("p-xcvdfe1", req.getResourceId());
    }

    @Test
    public void testConvertBcmEventToEventPo() {
        BcmListEventsResponse.BcmEvent event = new BcmListEventsResponse.BcmEvent();
        event.setEventAlias("BCI");
        event.setContent("{\"info\":\"Started\",\"advice\":\"string\",\"reason\":\"Started\",\"message\":\"Started container job-test\",\"fieldPath\":\"spec.containers{job-test}\"}");
        PodEventPO eventP0 = podService.convertBcmEventToEventPo(event);
        Assert.assertEquals(event.getEventAlias(), eventP0.getEventName());
        Assert.assertEquals("Started container job-test", eventP0.getDescription());
    }

    @Test
    public void testInitContainerPO() {
        ContainerPurchase containerPurchase = new ContainerPurchase();
        containerPurchase.setName("init-test");
        containerPurchase.setImageName("init-test-image-name");
        containerPurchase.setImageVersion("init-test-image-version");
        containerPurchase.setImageAddress("init-test-image-address");
        containerPurchase.setGpuCount(1);
        containerPurchase.setMemory(1024);
        containerPurchase.setCpu(1);
        containerPurchase.setContainerType(ContainerType.DS_WORKLOAD.getType());
        ContainerPO containerPO = podService.initContainerPO("pod-uuid", containerPurchase);
        Assert.assertEquals(containerPurchase.getName(), containerPO.getName());
        Assert.assertEquals(containerPurchase.getImageName(), containerPO.getImageName());
        Assert.assertEquals(containerPurchase.getImageVersion(), containerPO.getImageVersion());
        Assert.assertEquals(containerPurchase.getImageAddress(), containerPO.getImageAddress());
        Assert.assertEquals(containerPurchase.getCpu(), containerPO.getCpu(), 0.01);
        Assert.assertEquals(containerPurchase.getContainerType(), containerPO.getContainerType());
    }

    private String getPodExtraForSyncingDsContainers() {
        PodExtra podExtra = new PodExtra();
        Label label = new Label();
        label.setLabelKey("bci3");
        label.setLabelValue("true");
        podExtra.setMetadataLabels(Arrays.asList(label));
        return JsonUtil.toJSON(podExtra);
    }

    @Test
    public void testSyncDsContainers() {
        // 初始化实例和容器数据库
        String userId = LogicUserService.getAccountId();
        PodPO podPO = new PodPO();
        podPO.setDsContainersSyncedToK8S(true);
        podPO.setDsContainersVersion(1);
        podPO.setDsContainersCount(1);
        podPO.setPodUuid("sync-ds-containers-pod-uuid");
        podPO.setPodId("p-syncdsid");
        podPO.setUserId(userId);
        podPO.setStatus("Running");
        podPO.setClientToken(UuidUtil.generateUuid());
        podPO.setConditions("");
        podPO.setExtra(getPodExtraForSyncingDsContainers());

        VolumeMounts mounts = new VolumeMounts();
        mounts.setName("emptydir");
        mounts.setMountPath("/dev/nondspath");
        ContainerPO containerPO = new ContainerPO();
        containerPO.setContainerType(ContainerType.WORKLOAD.getType());
        containerPO.setContainerUuid("workload-1-uuid");
        containerPO.setCpu(3.0F);
        containerPO.setMemory(12.0F);
        containerPO.setUserId(userId);
        containerPO.setPodUuid("sync-ds-containers-pod-uuid");
        containerPO.setName("workload-1-name");
        containerPO.setVolumeMounts(JsonUtil.toJSON(Arrays.asList(mounts)));

        ContainerPO dsContainerPO = new ContainerPO();
        dsContainerPO.setContainerType(ContainerType.DS_WORKLOAD.getType());
        dsContainerPO.setContainerUuid("ds-workload-1-uuid");
        dsContainerPO.setUserId(userId);
        dsContainerPO.setPodUuid("sync-ds-containers-pod-uuid");
        dsContainerPO.setName("ds-workload-1-name");

        EmptyDir emptyDir = new EmptyDir();
        emptyDir.setName("emptydir");
        emptyDir.setMedium("hdd");
        emptyDir.setDsVolume(false);
        podPO.setEmptyDir(JsonUtil.toJSON(Arrays.asList(emptyDir)));

        podDao.batchInsertPods(Arrays.asList(podPO));
        containerDao.batchInsert(Arrays.asList(containerPO, dsContainerPO));

        // 构造实例更新请求参数
        EmptyDir dsEmptyDir = new EmptyDir();
        dsEmptyDir.setName("ds-emptydir");
        dsEmptyDir.setMedium("hdd");
        dsEmptyDir.setDsVolume(true);
        Volume volume = new Volume();
        volume.setEmptyDir(Arrays.asList(dsEmptyDir));

        VolumeMounts dsMounts = new VolumeMounts();
        dsMounts.setName("ds-emptydir");
        dsMounts.setMountPath("/dev/hdd");
        ContainerPurchase container = new ContainerPurchase();
        container.setName("ds-workload-2-name");
        container.setImageName("image");
        container.setImageVersion("v1");
        container.setImageAddress("registry.baidubce.com/ds-workload");
        container.setContainerType(ContainerType.DS_WORKLOAD.getType());
        container.setVolumeMounts(Arrays.asList(dsMounts));

        SyncDsContainersRequest request = new SyncDsContainersRequest();
        request.setExpectDsContainers(Arrays.asList(container));
        request.setExpectDsVolumes(volume);
        String errorMsg = "";

        // 测试实例状态不允许调整
        podPO.setStatus("Failed");
        podDao.updatePod(podPO);
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (CommonExceptions.RequestInvalidException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("bci instance must be running"));
        podPO.setStatus("Running");
        podDao.updatePod(podPO);

        // 测试实例 uuid 还是 podId
        podPO.setPodUuid("p-syncdsid");
        podDao.updatePod(podPO);
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (CommonExceptions.ResourceInTaskException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("bci instance is creating"));
        podPO.setPodUuid("sync-ds-containers-pod-uuid");
        podDao.updatePod(podPO);

        // 必填字段没有填
        request.setExpectDsVolumes(null);
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (CommonExceptions.RequestInvalidException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("expectDsContainers and expectDsVolumes must be specified"));
        request.setExpectDsVolumes(volume);

        // 测试要使用的ds卷不存在
        dsEmptyDir.setName("emptydir");
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (PodExceptions.PodContainerMountInvalidException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("Volume mount name invalid"));

        // 测试使用了非ds卷
        dsMounts.setName("emptydir");
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (PodExceptions.PodContainerMountInvalidException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("Volume mount name invalid"));
        dsMounts.setName("ds-emptydir");
        dsEmptyDir.setName("ds-emptydir");

        // 测试容器名字冲突
        container.setName("workload-1-name");
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (PodExceptions.ContainerNameException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("is duplicated"));
        container.setName("ds-workload-2-name");

        // 测试容器不是ds容器
        container.setContainerType(ContainerType.WORKLOAD.getType());
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (CommonExceptions.RequestInvalidException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("containerType in ds container must be ds-workload"));
        container.setContainerType(ContainerType.DS_WORKLOAD.getType());

        // 测试 cpu、mem 不合法
        container.setCpu(1.0F);
        try {
            podService.syncDsContainers("p-syncdsid", request);
        } catch (CommonExceptions.RequestInvalidException e) {
            errorMsg = e.getMessage();
        }
        Assert.assertTrue(errorMsg.contains("cpu/memory in ds container is not supported"));
        container.setCpu(0.0F);

        // 创建成功
        podService.syncDsContainers("p-syncdsid", request);
        PodPO newPodPO = podDao.getPodById("p-syncdsid");
        Assert.assertEquals(2, newPodPO.getDsContainersVersion());
        Assert.assertEquals(false, newPodPO.isDsContainersSyncedToK8S());
        Assert.assertEquals(2, JsonUtil.toList(newPodPO.getEmptyDir(), EmptyDir.class).size());
        List<ContainerPO> newContainers = containerDao.listByPodId("sync-ds-containers-pod-uuid");
        Assert.assertEquals(2, newContainers.size());
        for (ContainerPO newContainer : newContainers) {
            if ("workload-1-name".equals(newContainer.getName())) {
                Assert.assertEquals("workload", newContainer.getContainerType());
            } else if ("ds-workload-2-name".equals(newContainer.getName())) {
                Assert.assertEquals("ds-workload", newContainer.getContainerType());
            } else {
                Assert.assertEquals("ds-workload-1-name not exist", "ds-workload-1-name exist");
            }
        }
    }

    @Test
    public void testValidatePod() {
        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
        ContainerPurchase container1 = new ContainerPurchase();
        podPurchaseRequest.setName("pod");
        container1.setName("pod1");
        container1.setCpu(1);
        container1.setMemory(1);
        container1.setGpuCount(0);
        container1.setContainerType(ContainerType.WORKLOAD.getType());
        podPurchaseRequest.setContainerPurchases(new ArrayList<ContainerPurchase>());
        podPurchaseRequest.getContainerPurchases().add(container1);

        Volume volume = new Volume();
        HostPathVolume hostPath = new HostPathVolume();
        hostPath.setName("test");
        hostPath.setPath("/var/log/pods");
        volume.setHostPath(Arrays.asList(hostPath));
        podPurchaseRequest.setVolume(volume);
//        try {
//            // BCI2.0不支持hostPath为/var/log/pods
//            validator.validatePod(podPurchaseRequest, false);
//            Assert.fail();
//        } catch (Exception e) {
//            Assert.assertEquals(e.getMessage(), "invalid host path:/var/log/pods");
//        }

        Label label = new Label();
        label.setLabelKey("bci3");
        podPurchaseRequest.setMetadataLabels(new ArrayList<Label>());
        podPurchaseRequest.getMetadataLabels().add(label);

        validator.validatePod(podPurchaseRequest, false);

        hostPath.setType("DirectoryOrCreate");
        volume.setHostPath(Arrays.asList(hostPath));
        podPurchaseRequest.setVolume(volume);
        validator.validatePod(podPurchaseRequest, false);
        Assert.assertEquals(podPurchaseRequest.getVolume().getHostPath().get(0).getType(), "Directory");

        hostPath.setPath("/etc/localtime");
        hostPath.setType("FileOrCreate");
        volume.setHostPath(Arrays.asList(hostPath));
        podPurchaseRequest.setVolume(volume);
        validator.validatePod(podPurchaseRequest, false);
        Assert.assertEquals(podPurchaseRequest.getVolume().getHostPath().get(0).getType(), "File");

        hostPath.setType("UnknownOrCreate");
        volume.setHostPath(Arrays.asList(hostPath));
        podPurchaseRequest.setVolume(volume);
        try {
            validator.validatePod(podPurchaseRequest, false);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "invalid hostpath type:UnknownOrCreate");
        }

        hostPath.setName("test2");
        hostPath.setPath("/var");
        volume.setHostPath(Arrays.asList(hostPath));
        podPurchaseRequest.setVolume(volume);
        try {
            validator.validatePod(podPurchaseRequest, false);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), "invalid host path:/var");
        }
    }

    @Test
    public void testGeneratePodAnnotations() {
        String podName = "testPod";
        String podNamespace = "default";
        {
            // Arrange
            PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
            Label label1 = new Label();
            label1.setLabelKey(BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY);
            label1.setLabelValue(podName);
            Label label2 = new Label();
            label2.setLabelKey(BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAMESPACE_KEY);
            label2.setLabelValue(podNamespace);
            List<Label> labels = Arrays.asList(label1, label2);
            podPurchaseRequest.setMetadataLabels(labels);

            // Act
            Map<String, String> podAnnotations = podService.generatePodAnnotations(podPurchaseRequest);

            // Assert
            Assert.assertTrue(podAnnotations.containsKey(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAME_KEY));
            Assert.assertEquals(podName, podAnnotations.get(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAME_KEY));
            Assert.assertTrue(podAnnotations.containsKey(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAMESPACE_KEY));
            Assert.assertEquals(podNamespace, podAnnotations.get(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAMESPACE_KEY));
        }
        {
            // Arrange
            PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
            Label label1 = new Label();
            label1.setLabelKey("pod-name");
            label1.setLabelValue("testPod");
            Label label2 = new Label();
            label2.setLabelKey("pod-namespace");
            label2.setLabelValue("default");
            List<Label> labels = Arrays.asList(label1, label2);
            podPurchaseRequest.setMetadataLabels(labels);

            // Act
            Map<String, String> podAnnotations = podService.generatePodAnnotations(podPurchaseRequest);

            // Assert
            Assert.assertFalse(podAnnotations.containsKey(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAME_KEY));
            Assert.assertFalse(podAnnotations.containsKey(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAMESPACE_KEY));
            Assert.assertEquals(0, podAnnotations.size());
        }
    }
}
