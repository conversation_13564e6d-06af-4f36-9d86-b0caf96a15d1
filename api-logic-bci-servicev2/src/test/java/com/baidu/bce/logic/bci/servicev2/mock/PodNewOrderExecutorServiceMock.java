package com.baidu.bce.logic.bci.servicev2.mock;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.core.exception.CommonExceptions;

@Service
@Primary
public class PodNewOrderExecutorServiceMock extends PodNewOrderExecutorServiceV2 implements ThrowExceptionMock {
    public BciOrderExtra orderExtra;
    public PodNewOrderExecutorServiceMock() {
        super();
    }

    @Override
    public BciOrderExtra getOrderExtra(String orderId, String podId) {
        if (orderExtra == null) {
            throw new CommonExceptions.ResourceNotExistException();
        }
        return orderExtra;
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setThrowException'");
    }
}
