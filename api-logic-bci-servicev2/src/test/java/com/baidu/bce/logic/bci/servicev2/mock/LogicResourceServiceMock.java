package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.billing.resourcemanager.model.ResourceDetail;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.request.FilterPauseResRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class LogicResourceServiceMock extends LogicalResourceServiceV2 implements ResettableMock {
    private boolean throwException = false;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Autowired
    private LogicPodClientFactoryMock logicPodClientFactory;

    private Map<String, Resource> resourceMap = new HashMap<>();

    public void setThrowException(boolean throwException) {
        this.throwException = throwException;
    }


    public void addResource(String uuid, Resource resource) {
        resourceMap.put(uuid, resource);
    }

    @Override
    public void reset() {
        resourceMap.clear();
    }

    @Override
    public Set<String> queryCpt1PauseResourceStatusInfos(String accountId, List<String> nameList,
                                                         String serviceType) {

        ChargeResourceService chargeResourceService =
                logicPodClientFactory.createChargeResourceService(accountId);

        Set<String> allPauseRes = new HashSet<>();

        // 批量获取
        List<List<String>> nameLists = Lists.partition(nameList, 10);

        for (List<String> names : nameLists) {
            FilterPauseResRequest filterPauseResRequest = new FilterPauseResRequest();
            filterPauseResRequest.setServiceType(serviceType);
            filterPauseResRequest.setRegion("gz");
            filterPauseResRequest.setNameList(names);

            try {
                List<String> pauseRes = chargeResourceService.filterPauseRes(filterPauseResRequest);
                if (null != pauseRes) {
                    allPauseRes.addAll(pauseRes);
                }
            } catch (BceInternalResponseException e) {
                throw new PodExceptions.InternalServerErrorException();
            }
        }
        return allPauseRes;
    }

    public List<ResourceDetail> queryCpt1ResourceListV2(String accountId) {
        List<ResourceDetail> result = new ArrayList<>();
        ResourceDetail resourceDetail1 = new ResourceDetail();
        resourceDetail1.setAccountId(accountId);
        resourceDetail1.setName("6ac50ddc-dd8a-41e2-8e05-54f6debb9e3b");
        resourceDetail1.setSubProductType(PodServiceV2.CPT1_SUB_PRODUCT_TYPE);
        result.add(resourceDetail1);

        ResourceDetail resourceDetail2 = new ResourceDetail();
        resourceDetail2.setAccountId(accountId);
        resourceDetail2.setName("7ac50ddc-dd8a-41e2-8e05-54f6debb9e3b");
        resourceDetail2.setSubProductType(PodServiceV2.CPT1_SUB_PRODUCT_TYPE);
        result.add(resourceDetail2);
        return result;
    }


//    @Override
//    public Resource getResource(String accountId, String name, String serviceType) {
//        assertEquals(PodConstants.SERVICE_TYPE, serviceType);
//        Resource resource = resourceMap.get(name);
//        if (resource == null) {
//            String podUuid;
//            try {
//                podUuid = databaseUtil.getPodMapper().queryPodUuid(name);
//                if (podUuid != null) {
//                    return resourceMap.get(podUuid);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        return resource;
//    }

    public Resource removeResource(String resourceUuid) {
        return resourceMap.remove(resourceUuid);
    }
}
