package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.plat.webframework.database.SqliPrevention;
import com.baidu.bce.plat.webframework.database.mybatis.ContextMapPropertyAccessor;
import com.baidu.bce.plat.webframework.database.mybatis.CustomizableSqlSessionFactoryBean;
import com.baidu.bce.plat.webframework.database.mybatis.DefaultSqlSessionConfigurationCustomizer;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.apache.ibatis.ognl.OgnlRuntime;
import org.apache.ibatis.scripting.xmltags.DynamicContext;
import org.apache.ibatis.session.LocalCacheScope;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import java.io.FileReader;
import java.io.IOException;
import java.io.Reader;
import java.sql.SQLException;


public class DatabaseUtil {

    private final DriverManagerDataSource dataSource;
    private CustomizableSqlSessionFactoryBean sqlSessionFactoryBean;
    private final SqlSessionFactory factory;
    private final PathMatchingResourcePatternResolver resolver;

    private void overrideContextMapPropertyAccessor() {
        try {
            // Initialize DynamicContext class first, so that it will set property accessor before we do.
            Class.forName(DynamicContext.class.getName());
            // ContextMap is a package private class, we cannot directly reference it here.
            Class<?> contextMapClass = Class.forName(DynamicContext.class.getName() + "$ContextMap");

            OgnlRuntime.setPropertyAccessor(contextMapClass, new ContextMapPropertyAccessor());
        } catch (Exception ignore) {
            // ignore
        }
    }

    public DatabaseUtil() throws Exception {
        dataSource = new DriverManagerDataSource(org.h2.Driver.class.getCanonicalName());
        dataSource.setUrl("jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;MODE=MySQL;DB_CLOSE_ON_EXIT=FALSE");
        dataSource.setUsername("");
        dataSource.setPassword("");

        TransactionFactory transactionFactory = new JdbcTransactionFactory();
        resolver = new PathMatchingResourcePatternResolver();

        sqlSessionFactoryBean = new CustomizableSqlSessionFactoryBean();
//        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath*:*.xml"));
        sqlSessionFactoryBean.setSqlSessionConfigurationCustomizer(
                new DefaultSqlSessionConfigurationCustomizer("velocity", SqliPrevention.IDENTIFIER_REGEX));
        overrideContextMapPropertyAccessor();

        sqlSessionFactoryBean.setTypeAliases(new Class[]{org.mybatis.scripting.velocity.Driver.class});
        sqlSessionFactoryBean.setTransactionFactory(transactionFactory);
        sqlSessionFactoryBean.setDataSource(dataSource);

        factory = sqlSessionFactoryBean.getObject();
        factory.getConfiguration().setMapUnderscoreToCamelCase(true);
        factory.getConfiguration().setCacheEnabled(false);
        factory.getConfiguration().setLocalCacheScope(LocalCacheScope.STATEMENT);

        // create tables
        SqlSession session = factory.openSession();
        ScriptRunner runner = new ScriptRunner(session.getConnection());
        runner.setSendFullScript(true);
        Reader schemaReader = new FileReader(
                resolver.getResources("classpath*:INIT_TABLE.sql")[0].getFile());
        Reader dataReader = new FileReader(
                resolver.getResources("classpath*:INIT_DATA.sql")[0].getFile());

        runner.runScript(schemaReader);
        runner.runScript(dataReader);
        runner.closeConnection();
        session.close();
    }

    public SqlSessionFactoryBean getSqlSessionFactoryBean() {
        return sqlSessionFactoryBean;
    }

//    public PodMapper getPodMapper() throws Exception {
//        return factory.openSession(true).getMapper(PodMapper.class);
//    }
//
//    public ContainerMapper getContainerMapper() throws Exception {
//        return factory.openSession(true).getMapper(ContainerMapper.class);
//    }

    public JdbcTemplate getJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    public void resetPod() throws SQLException {
        getJdbcTemplate().execute("DELETE FROM t_pod");
    }

    public void resetContainer() throws SQLException {
        getJdbcTemplate().execute("DELETE FROM t_container");
    }

    public void resetDatabase() throws SQLException {
        resetPod();
        resetContainer();
    }

    public void runScript(String path) throws Exception {
        SqlSession session = sqlSessionFactoryBean.getObject().openSession();
        ScriptRunner runner = new ScriptRunner(session.getConnection());
        runner.runScript(new FileReader(resolver.getResources(path)[0].getFile()));
        runner.closeConnection();
        session.close();
    }

    public Resource getResource(String location) throws IOException {
        return resolver.getResources(location)[0];
    }
}
