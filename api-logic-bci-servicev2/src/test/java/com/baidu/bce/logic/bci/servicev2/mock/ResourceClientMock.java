package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.ExtendedGetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.GetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceIds;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import org.springframework.stereotype.Component;

import static com.baidu.bce.logic.bci.servicev2.constant.ServiceTestConstants.POD_UUID;

@Component
public class ResourceClientMock extends ResourceClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public ResourceClientMock() {
        super(null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public Resources queryList(ExtendedGetResourcesRequest request) {
        if (throwException != null) {
            throw throwException;
        }
        Resources resources = new Resources();
        Resource resource = new Resource();
        resource.setName(POD_UUID);
        resources.add(resource);
        return resources;
    }

    public void delete(String uuid) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public Resource get(String uuid) {
        Resource resource = new Resource();
        resource.setName(POD_UUID);
        return resource;
    }

    public Resources getResourcesByIds(ResourceIds request) {
        Resources resources = new Resources();
        Resource resource = new Resource();
        resource.setName("ffcdfa25-6edc-4b7c-943d-d5225050086a");
        resource.setStatus(ResourceStatus.RUNNING);
        resource.setServiceType("BCI");
        resources.add(resource);
        return resources;
    }

    public Resources list(GetResourcesRequest getResourcesRequest) {
        Resources resources = new Resources();
        Resource resource = new Resource();
        resource.setName("ffcdfa25-6edc-4b7c-943d-d5225050086a");
        resource.setStatus(ResourceStatus.RUNNING);
        resources.add(resource);
        return resources;
    }
}
