package com.baidu.bce.logic.bci.servicev2.util;

import org.junit.Test;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.baidu.bce.logic.bci.servicev2.model.UserVersion;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache.Version;
import com.baidu.bce.logic.core.user.LogicUserService;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class VersionCacheTest {
    @InjectMocks
    private VersionCache versionCache;

    @Mock
    private PodServiceV2 podService;
    
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetUserVersionEmptyAccountId() {
        // 账号为空, 返回版本异常
        String accountId = "";
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.ABNORMAL, result);
    }

    @Test
    public void testGetUserVersionV1Cache() {
        // 在v1cache中, 返回v1版本
        String accountId = "user";
        versionCache.addUserToV1Cache("user", 1000000);
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.V1, result);
    }

    @Test
    public void testGetUserVersionV2Cache() {
        // 在v2cache中, 返回v2版本
        String accountId = "user";
        versionCache.addUserToV2Cache("user", 1000000);
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.V2, result);
    }

    @Test
    public void testGetUserVersionV1CacheAndV2Cache() {
        // 在v1cache && v2cache中, 返回版本异常
        String accountId = "user";
        versionCache.addUserToV1Cache("user", 1000000);
        versionCache.addUserToV2Cache("user", 1000000);
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.ABNORMAL, result);
    }

    @Test
    public void testGetUserVersionNotInCachesAndIsV1User() {
        // 不在v1cache && v2cache中, 且为v1用户, 返回v1版本
        String accountId = "user";
        UserVersion userVersion = new UserVersion();
        userVersion.setIsv1(true);
        Mockito.when(podService.isV1AccountId()).thenReturn(userVersion);
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.V1, result);
    }

    @Test
    public void testGetUserVersionNotInCachesAndIsV2User() {
        String accountId = "user";
        UserVersion userVersion = new UserVersion();
        userVersion.setIsv1(false);
        // mock 返回非v1版本
        when(podService.isV1AccountId()).thenReturn(userVersion);
        // mock 添加v2集群正常
        when(podService.addCceClusterForUser(accountId)).thenReturn(true);
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.V2, result);
    }

    @Test
    public void testGetUserVersionNotInCachesAndIsV2UserAddClusterFail() {
        String accountId = "user";
        UserVersion userVersion = new UserVersion();
        userVersion.setIsv1(false);
        // mock 返回非v1版本
        when(podService.isV1AccountId()).thenReturn(userVersion);
        // mock 添加v2集群正常
        when(podService.addCceClusterForUser(accountId)).thenReturn(false);
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.ABNORMAL, result);
    }

    @Test
    public void testGetUserVersionExceptionCaught() {
        String accountId = "user";
        UserVersion userVersion = new UserVersion();
        userVersion.setIsv1(false);
        // mock 返回非v1版本
        when(podService.isV1AccountId()).thenReturn(userVersion);
        // mock 抛出异常
        when(podService.addCceClusterForUser(accountId)).thenThrow(new RuntimeException("Error"));
        Version result = versionCache.getUserVersion(accountId);
        assertEquals(Version.ABNORMAL, result);
    }
}