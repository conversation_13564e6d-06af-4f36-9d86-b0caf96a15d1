package com.baidu.bce.logic.bci.servicev2.k8s;

import io.kubernetes.client.informer.ResourceEventHandler;
import io.kubernetes.client.informer.SharedIndexInformer;
import io.kubernetes.client.informer.SharedInformerFactory;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.*;
import io.kubernetes.client.util.CallGenerator;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;

//import static org.junit.Assert.assertEquals;


public class K8sClusterTest {
    @Mock
    private CoreV1Api coreV1Api;
    @InjectMocks
    private K8sCluster k8sCluster = new K8sCluster();
    @Mock
    private SharedInformerFactory factory;
    @Mock
    private SharedIndexInformer<V1Pod> podInformer;
    @Mock
    private SharedIndexInformer<V1ConfigMap> cmInformer;
    @Mock
    private SharedIndexInformer<V1Namespace> nsInformer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // @Test todo 需要修复
    public void registerPodEventHandlersTest() throws Exception {
        PowerMockito.when(factory, "getExistingSharedIndexInformer", V1Pod.class).thenReturn(null);
        PowerMockito.when(factory, "sharedIndexInformerFor", Matchers.<CallGenerator>anyObject(), 
                          V1Pod.class, V1PodList.class).thenReturn(podInformer);
        PowerMockito.when(podInformer, "addEventHandler", Mockito.anyObject()).thenAnswer(null);
        List<ResourceEventHandler<V1Pod>> handlers = new ArrayList<>();
        handlers.add(new EventHandlerImpl());
        handlers.add(new EventHandlerImpl());
        k8sCluster.registerPodEventHandlers(handlers);
    }

    // @Test todo 需要修复
    public void startTest() throws Exception {
        PowerMockito.when(factory, "getExistingSharedIndexInformer", Mockito.anyObject()).thenReturn(podInformer);
        PowerMockito.when(factory, "sharedIndexInformerFor", Matchers.<CallGenerator>anyObject(), 
                          V1ConfigMap.class, V1ConfigMapList.class).thenReturn(cmInformer);
        PowerMockito.when(factory, "sharedIndexInformerFor", Matchers.<CallGenerator>anyObject(), 
                          V1Namespace.class, V1NamespaceList.class).thenReturn(nsInformer);
        PowerMockito.when(factory, "startAllRegisteredInformers").thenReturn(null);
        PowerMockito.when(podInformer, "hasSynced").thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) {
                return true;
            }
        });
        k8sCluster.start();
    }

    private class EventHandlerImpl implements ResourceEventHandler<V1Pod> {
        public void onAdd(V1Pod obj) {}
        public void onUpdate(V1Pod oldObj, V1Pod newObj) {}
        public void onDelete(V1Pod obj, boolean deletedFinalStateUnknown) {}
    }

    @Test
    public void testCreateConfigMap_Success() throws K8sServiceException {
        V1ConfigMap configMap = new V1ConfigMap();
        configMap.setMetadata(new V1ObjectMeta().namespace("default"));
        k8sCluster.createConfigMap(configMap);
    }

    @Test(expected = K8sServiceException.class)
    public void testCreateConfigMap_Fail() throws Exception {
        V1ConfigMap configMap = new V1ConfigMap();
        configMap.setMetadata(new V1ObjectMeta().namespace("default"));


        // 模拟 ApiException 异常
        ApiException apiException = new ApiException(
                "Internal Server Error", // 错误消息
                500,                      // HTTP 响应码
                null,                     // HTTP 响应头
                "Internal Server Error"    // 响应体
        );
        Mockito.doThrow(apiException)
                .when(coreV1Api).createNamespacedConfigMap(eq("default"), any(V1ConfigMap.class), any(), any(), any(),
                        any());
        k8sCluster.createConfigMap(configMap);
    }
}