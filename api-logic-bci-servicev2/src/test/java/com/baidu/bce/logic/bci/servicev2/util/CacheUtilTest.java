package com.baidu.bce.logic.bci.servicev2.util;

import org.junit.Before;
import org.junit.Test;

import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class CacheUtilTest {
    private CacheUtil<String, String> cacheUtil;

    @Before
    public void setUp() {
        cacheUtil = new CacheUtil<>();
    }

    @Test
    public void testPutAndGet() {
        cacheUtil.put("key1", "value1");
        assertEquals("value1", cacheUtil.get("key1"));
    }

    @Test
    public void testExpiration() throws InterruptedException {
        cacheUtil.put("key2", "value2", 1000);
        TimeUnit.MILLISECONDS.sleep(1100);
        assertNull(cacheUtil.get("key2"));
    }

    @Test
    public void testGetNonexistentKey() {
        assertNull(cacheUtil.get("nonexistent"));
    }

    @Test
    public void testSize() {
        cacheUtil.put("key1", "value1");
        cacheUtil.put("key2", "value2");
        assertEquals(2, cacheUtil.size());
    }

    @Test
    public void testSizeAfterExpiration() throws InterruptedException {
        cacheUtil.put("key1", "value1");
        cacheUtil.put("key2", "value2", 1000);
        TimeUnit.MILLISECONDS.sleep(1100);
        assertEquals(null, cacheUtil.get("key2")); // Triggers removal of expired item
        assertEquals(1, cacheUtil.size());
    }
}