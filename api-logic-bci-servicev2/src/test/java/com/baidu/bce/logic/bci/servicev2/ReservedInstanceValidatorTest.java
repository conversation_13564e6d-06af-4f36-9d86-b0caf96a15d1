package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.ReservedInstanceValidator;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class ReservedInstanceValidatorTest {
    private static final String CREATE_RESERVED_INSTANCE_LOCATION = "classpath*:create_reserved_instance.json";

    private BaseCreateOrderRequestVo<CreateReservedInstanceRequest> request;

    @Autowired
    private ReservedInstanceValidator reservedInstanceValidator;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        testUtil.setUp();
        request = testUtil.orderRequest(
                databaseUtil.getResource(CREATE_RESERVED_INSTANCE_LOCATION).getInputStream(), 
                CreateReservedInstanceRequest.class);
    }

    @Test
    public void testValidateSearchId() {
        Map<String, String> filter = new HashMap<>();
        filter.put("searchId", "123Qa-_");
        boolean isValid = ReservedInstanceValidator.validateSearchId(filter);
        Assert.assertTrue(isValid);

        filter.clear();
        filter.put("searchId", "a!*");
        isValid = ReservedInstanceValidator.validateSearchId(filter);
        Assert.assertFalse(isValid);
    }

    @Test
    public void testValidate() {
        reservedInstanceValidator.validate(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidScope() {
        request.getItems().get(0).getConfig().setScope("test");
        reservedInstanceValidator.validate(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidName() {
        request.getItems().get(0).getConfig().setName("a_*b");
        reservedInstanceValidator.validate(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidPurchaseMode() {
        request.getItems().get(0).getConfig().setPurchaseMode("test");
        reservedInstanceValidator.validate(request);
    }
    
    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidReservedInstanceCount() {
        request.getItems().get(0).getConfig().setReservedInstanceCount(0);
        reservedInstanceValidator.validate(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidReservedTimeUnit() {
        request.getItems().get(0).getConfig().setReservedTimeUnit("test");
        reservedInstanceValidator.validate(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidReservedTimePeriod() {
        request.getItems().get(0).getConfig().setReservedTimePeriod(0);
        reservedInstanceValidator.validate(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidAutoRenewTimeUnit() {
        request.getItems().get(0).getConfig().setAutoRenewTimeUnit("test");
        reservedInstanceValidator.validate(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateWithInvalidAutoRenewTimePeriod() {
        request.getItems().get(0).getConfig().setAutoRenewTimePeriod(0);
        reservedInstanceValidator.validate(request);
    }

    @Test
    public void testValidateListReservedInstanceRequest() {
        ListRequest request = initListRequest();
        ListRequest validatedRequest = reservedInstanceValidator.validateListReservedInstanceRequest(request);
        Assert.assertEquals(1, validatedRequest.getOrders().size());
        request = initListRequest();
        request.setOrders(null);
        validatedRequest = reservedInstanceValidator.validateListReservedInstanceRequest(request);
        Assert.assertEquals(validatedRequest.getPageNo(), 1);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateListReservedInstanceRequestWithInvalidPageSize() {
        ListRequest request = new ListRequest();
        request.setPageSize(0);
        reservedInstanceValidator.validateListReservedInstanceRequest(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateListReservedInstanceRequestWithInvalidPageNo() {
        ListRequest request = initListRequest();
        request.setPageNo(0);
        reservedInstanceValidator.validateListReservedInstanceRequest(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateListReservedInstanceRequestWithInvalidOrderBy() {
        ListRequest request = initListRequest();
        request.getOrders().get(0).setOrderBy("test");
        reservedInstanceValidator.validateListReservedInstanceRequest(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateListReservedInstanceRequestWithInvalidOrder() {
        ListRequest request = initListRequest();
        request.getOrders().get(0).setOrder("test");
        reservedInstanceValidator.validateListReservedInstanceRequest(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateListReservedInstanceRequestWithInvalidFilterField() {
        ListRequest request = initListRequest();
        request.getFilterMap().put("test", "test");
        reservedInstanceValidator.validateListReservedInstanceRequest(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void testValidateListReservedInstanceRequestWithInvalidFilterValue() {
        ListRequest request = initListRequest();
        request.getFilterMap().put("reservedInstanceId", "*");
        reservedInstanceValidator.validateListReservedInstanceRequest(request);
    }

    public ListRequest initListRequest() {
        ListRequest request = new ListRequest();
        request.setPageNo(1);
        request.setPageSize(2);
        Map<String, String> filter = new HashMap<>();
        filter.put("reservedInstanceId", "123");
        request.setFilterMap(filter);
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderBy("createdTime");
        orderModel.setOrder("DESC");
        List<OrderModel> orderModelList = new ArrayList<>();
        orderModelList.add(orderModel);
        request.setOrders(orderModelList);
        return request;
    }
}
