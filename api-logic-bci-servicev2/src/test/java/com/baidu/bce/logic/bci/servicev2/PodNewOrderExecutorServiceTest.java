package com.baidu.bce.logic.bci.servicev2;


import com.baidu.bce.fbi.common.Pair;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.mock.K8sServiceMock;
import com.baidu.bce.logic.bci.servicev2.mock.OrderClientMock;
import com.baidu.bce.logic.bci.servicev2.mock.ResourceClientMock;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.Bos;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.Pfs;
import com.baidu.bce.logic.bci.servicev2.model.PodExtra;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ContainerBuilder;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1PodStatus;
import io.kubernetes.client.openapi.models.V1ResourceRequirements;
import io.kubernetes.client.openapi.models.V1Volume;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodNewOrderExecutorServiceTest {

    @Autowired
    PodNewOrderExecutorServiceV2 podNewOrderExecutorService;

    @Autowired
    DatabaseUtil databaseUtil;

    OrderClient orderClient;

    ResourceClient resourceClient;

    Order order;

    @Before
    public void init() throws IOException {
        orderClient = new OrderClientMock();
        resourceClient = new ResourceClientMock();
        order = TestUtil.fromJson(
                databaseUtil.getResource("classpath*:order.json").getInputStream(), Order.class);
    }

    @Test
    public void executeTest() {
        order.setUuid("response");
        podNewOrderExecutorService.executeWithoutLock(orderClient, resourceClient, order, "test");
    }

    @Test
    public void executeTestBceException() {
        order.setUuid("null-response");
        podNewOrderExecutorService.executeWithoutLock(orderClient, resourceClient, order, "test");
    }

    // @Test todo 需要修复
    @Test
    public void encapsulatePodForCreateTest() throws IOException, K8sServiceException, ApiException, InterruptedException {
        podNewOrderExecutorService.encapsulatePodForCreate(order, "");
    }


    @Test
    public void checkSuccTest() {
        order.setUuid("succ");
        podNewOrderExecutorService.checkWithoutLock(orderClient, resourceClient, order, "test");
    }

    @Test
    public void checkErrorTest() {
        order.setUuid("error");
        podNewOrderExecutorService.checkWithoutLock(orderClient, resourceClient, order, "test");
    }

    @Test
    public void checkOrderAndPodTest() {
        // 1.order不存在 && 没有超时
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        order.setUuid("not_exit");
        Date date = new Date();
        order.setCreateTime(date);
        boolean res = podNewOrderExecutorService.checkOrderAndPod(orderClient, order, executionResult, "");
        Assert.assertEquals(res, false);
        Assert.assertEquals(executionResult.getExecutionStatus(), ExecutionStatus.CREATING);

        // 1.order不存在 && 没有超时
        executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        order.setUuid("not_exit");
        Calendar c = new GregorianCalendar();
        date = new Date();
        // 设置参数时间
        c.setTime(date);
        // 把日期往后增加SECOND秒.整数往后推,负数往前移动
        c.add(Calendar.SECOND, -55);
        date = c.getTime();
        order.setCreateTime(date);
        res = podNewOrderExecutorService.checkOrderAndPod(orderClient, order, executionResult, "");
        Assert.assertEquals(res, false);
        Assert.assertEquals(executionResult.getExecutionStatus(), ExecutionStatus.CREATING);

        // 2.order不存在 && 超时
        executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        order.setUuid("not_exit");
        c = new GregorianCalendar();
        date = new Date();
        // 设置参数时间
        c.setTime(date);
        // 把日期往后增加SECOND秒.整数往后推,负数往前移动
        c.add(Calendar.SECOND, -60);
        date = c.getTime();
        order.setCreateTime(date);
        res = podNewOrderExecutorService.checkOrderAndPod(orderClient, order, executionResult, "");
        Assert.assertEquals(res, false);
        Assert.assertEquals(executionResult.getExecutionStatus(), ExecutionStatus.FAILURE);

        // 3.订单存在 && pod被删除
        executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        order.setUuid("order");
        date = new Date();
        order.setCreateTime(date);
        res = podNewOrderExecutorService.checkOrderAndPod(orderClient, order, executionResult, "");
        Assert.assertEquals(res, false);
        Assert.assertEquals(executionResult.getExecutionStatus(), ExecutionStatus.FAILURE);

        // 4.订单存在 && pod存在
        executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        order.setUuid("b5e691ab-6b7f-4263-a171-761b6c2a3e5d");
        res = podNewOrderExecutorService.checkOrderAndPod(orderClient, order, executionResult, "");
        Assert.assertEquals(res, true);
        Assert.assertEquals(executionResult.getExecutionStatus(), ExecutionStatus.CREATING);
    }

    @Test
    public void forBideTest() {
        V1ObjectMeta metadata = new V1ObjectMeta();
        metadata.setAnnotations(new HashMap<String, String>());
        podNewOrderExecutorService.forBid(order.getItems().get(0), metadata);
    }

    @Test
    public void getPfsToHostPathVolumesTest() throws IOException {
        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        Volume volume = new Volume();;
        Pfs pfs = new Pfs();
        pfs.setPath("/");
        pfs.setServer("********");
        pfs.setName("test-pfs");
        volume.setPfs(Arrays.asList(pfs));
        bciOrderExtra.setVolume(volume);
        bciOrderExtra.setPodId("123");

        List<V1Volume> volumes = podNewOrderExecutorService.getPfsToHostPathVolumes(bciOrderExtra);

        Assert.assertEquals(volumes.get(0).getName(), "test-pfs");
        Assert.assertEquals(volumes.get(0).getHostPath().getPath(), "/bci-pfs/test-pfs123");
        Assert.assertEquals(volumes.get(0).getHostPath().getType(), "DirectoryOrCreate");

        Assert.assertEquals(volumes.get(1).getName(), "pfs-base");
        Assert.assertEquals(volumes.get(1).getHostPath().getPath(), "/bci-pfs/");
        Assert.assertEquals(volumes.get(1).getHostPath().getType(), "DirectoryOrCreate");

    }

    @Test
    public void getBosToHostPathVolumesTest() throws IOException {
        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        Volume volume = new Volume();;
        Bos bos = new Bos();
        bos.setName("test-bos");
        bos.setBucket("bos-test-gz");
        bos.setUrl("gz.bcebos.com");
        bos.setAk("ak");
        bos.setSk("sk");
        bos.setReadOnly(false);

        volume.setBos(Arrays.asList(bos));
        bciOrderExtra.setVolume(volume);
        bciOrderExtra.setPodId("123");

        List<V1Volume> volumes = podNewOrderExecutorService.getBosToHostPathVolumes(bciOrderExtra);

        Assert.assertEquals(volumes.get(0).getName(), "test-bos");
        Assert.assertEquals(volumes.get(0).getHostPath().getPath(), "/bci-bos/test-bos123");
        Assert.assertEquals(volumes.get(0).getHostPath().getType(), "DirectoryOrCreate");

        Assert.assertEquals(volumes.get(1).getName(), "bos-base");
        Assert.assertEquals(volumes.get(1).getHostPath().getPath(), "/bci-bos/");
        Assert.assertEquals(volumes.get(1).getHostPath().getType(), "DirectoryOrCreate");

    }

    @Test
    public void set10255MetaInfoInAnnotationTest() throws IOException {
        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        V1ObjectMeta metadata = new V1ObjectMeta();
        metadata.setAnnotations(new HashMap<String, String>());

        List<Label> labels = new ArrayList<>();
        Label label = new Label();
        label.setLabelKey("bci.virtual-kubelet.io/pod-namespace-name");
        label.setLabelValue("default/testpod1");
        labels.add(label);
        bciOrderExtra.setLabels(labels);

        PodExtra podExtra = new PodExtra();
        List<Label> labels2 = new ArrayList<>();
        Label bci3 = new Label();
        bci3.setLabelKey("bci3");
        bci3.setLabelValue("true");
        labels2.add(bci3);
        podExtra.setMetadataLabels(labels2);
        bciOrderExtra.setPodExtra(podExtra);

        podNewOrderExecutorService.set10255MetaInfoInAnnotation(bciOrderExtra, metadata);
        // String ret = metadata.getAnnotations().get("bci.cloud.baidu.com/pod-meta-data");
        // Assert.assertEquals(ret, "{\"podName\":\"b5\",\"namespace\":\"default\",\"labels\":{\"bci3\":\"true\"}}");
    }

    @Test
    public void enableBCI3Test() {
        BciOrderExtra orderExtra = new BciOrderExtra();
        orderExtra.setPodCpu(0.5f);
        orderExtra.setPodMem(2);

        PodExtra podExtra = new PodExtra();
        
        List<Label> labels = new ArrayList<>();
        Label bci3 = new Label();
        bci3.setLabelKey("bci3");
        bci3.setLabelValue("true");
        labels.add(bci3);
        podExtra.setMetadataLabels(labels);
        orderExtra.setPodExtra(podExtra);
        podNewOrderExecutorService.setV3FlagByLabel(orderExtra);
        Assert.assertEquals(true, orderExtra.isV3());
    }

    @Test
    public void getPfsSidecarContainersTest() throws IOException {
        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        Volume volume = new Volume();;
        Pfs pfs = new Pfs();
        pfs.setPath("/");
        pfs.setServer("********");
        pfs.setName("test-pfs");
        volume.setPfs(Arrays.asList(pfs));
        bciOrderExtra.setVolume(volume);
        bciOrderExtra.setPodId("123");

        VolumeMounts volumeMount = new VolumeMounts();
        volumeMount.setMountPath("/test");
        volumeMount.setName("test-pfs");
        volumeMount.setType("PFS");
        ContainerPurchase containerPurchase = new ContainerPurchase();
        containerPurchase.setVolumeMounts(Arrays.asList(volumeMount));
        bciOrderExtra.setContainers(Arrays.asList(containerPurchase));

        List<V1Volume> volumes = podNewOrderExecutorService.getPfsToHostPathVolumes(bciOrderExtra);
        List<V1Container> containers = podNewOrderExecutorService.getPfsSidecarContainers(bciOrderExtra, volumes);

        Assert.assertEquals(containers.size(), 1);
        Assert.assertEquals(containers.get(0).getName(), "bci-internal-pfs-sidecar-container-0");
    }


    @Test
    public void getBosSidecarContainersTest() throws IOException {
        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        Volume volume = new Volume();;
        Bos bos = new Bos();
        bos.setName("test-bos");
        bos.setBucket("bos-test-gz");
        bos.setUrl("gz.bcebos.com");
        bos.setAk("ak");
        bos.setSk("sk");
        bos.setReadOnly(false);

        volume.setBos(Arrays.asList(bos));
        bciOrderExtra.setVolume(volume);
        bciOrderExtra.setPodId("123");

        VolumeMounts volumeMount = new VolumeMounts();
        volumeMount.setMountPath("/test");
        volumeMount.setName("test-bos");
        volumeMount.setType("BOS");
        ContainerPurchase containerPurchase = new ContainerPurchase();
        containerPurchase.setVolumeMounts(Arrays.asList(volumeMount));
        bciOrderExtra.setContainers(Arrays.asList(containerPurchase));

        List<V1Volume> volumes = podNewOrderExecutorService.getBosToHostPathVolumes(bciOrderExtra);
        List<V1Container> containers = podNewOrderExecutorService.getBosSidecarContainers(bciOrderExtra, volumes);

        Assert.assertEquals(containers.size(), 1);
        Assert.assertEquals(containers.get(0).getName(), "bci-internal-bos-sidecar-container-0");
    }

    @Test
    public void getPfsSidecarTest() throws IOException {
        V1Container sd = podNewOrderExecutorService.getPfsSidecar("test-volume", "********0", "/", "data", 1);
        Assert.assertEquals(sd.getVolumeMounts().get(0).getName(), "pfs-base");
        Assert.assertNotNull(sd.getLifecycle().getPostStart());
        Assert.assertNotNull(sd.getLifecycle().getPreStop());
        Assert.assertEquals(sd.getEnv().get(0).getValue(), "********0");
    }

    @Test
    public void getBosSidecarTest() throws IOException {
        V1Container sd = podNewOrderExecutorService.getBosSidecar("test-volume", "bos-test-gz", "", "gz.bcebos.com", "ak", "sk", false, "/bos-base/test-volume123", 1);
        Assert.assertEquals(sd.getVolumeMounts().get(0).getName(), "bos-base");
        Assert.assertNotNull(sd.getLifecycle().getPostStart());
        Assert.assertNotNull(sd.getLifecycle().getPreStop());
        Assert.assertEquals(sd.getEnv().get(0).getValue(), "bos-test-gz");
    }

    @Test
    public void getResourceStatusTest() {
        V1PodStatus podStatus = new V1PodStatus();
        podStatus.setPhase("Running");
        V1Pod k8sPod = new V1Pod();
        k8sPod.setStatus(podStatus);
        PodPO podPO = new PodPO();
        podPO.setCpt1(false);
        podPO.setDelayReleaseDurationMinute(0);
        podPO.setResourceRecycleTimestamp(10);
        podPO.setResourceRecycleComplete(1);
        podPO.setResourceRecycleReason("JOB_POD_COMPLETE");
        ResourceStatus status = podNewOrderExecutorService.getResourceStatus(k8sPod, podPO);
        Assert.assertEquals(ResourceStatus.RUNNING, status);

        podStatus.setPhase("Failed");
        status = podNewOrderExecutorService.getResourceStatus(k8sPod, podPO);
        Assert.assertEquals(ResourceStatus.INVALID, status);

        podStatus.setPhase("Succeeded");
        status = podNewOrderExecutorService.getResourceStatus(k8sPod, podPO);
        Assert.assertEquals(ResourceStatus.RUNNING, status);

        podPO.setCpt1(true);
        status = podNewOrderExecutorService.getResourceStatus(k8sPod, podPO);
        Assert.assertEquals(ResourceStatus.INVALID, status);

        podPO.setDelayReleaseDurationMinute(10);
        status = podNewOrderExecutorService.getResourceStatus(k8sPod, podPO);
        Assert.assertEquals(ResourceStatus.INVALID, status);
    }
    
    @Test
    public void testUpdateOrderFlavorByPodPhysicalZone() {
        Order order = new Order();
        order.setUuid("test-order");
        PodPO podPO = new PodPO();
        podPO.setPodId("podId");
        podPO.setUserId("userId");
        List<PodPO> podPOList = new ArrayList<>();
        podPOList.add(podPO);
        V1Pod pod = new V1Pod();
        V1ObjectMeta metadata = new V1ObjectMeta();
        Map<String,String> annotations = new HashMap<>();
        HashMap<Pair<String, String>, V1Pod> podMap = new HashMap<>();
        annotations.put("bci_internal_PhysicalZone", "zone");
        metadata.setAnnotations(annotations);
        pod.setMetadata(metadata);
        podMap.put(new Pair<>("userId", "podId"), pod);
        podNewOrderExecutorService.updateOrderFlavorByPodPhysicalZone(order, podPOList, podMap);
    }

    @Test
    public void testConvertToK8sEmptyDir() {
        EmptyDir emptyDir = new EmptyDir();
        emptyDir.setName("test-empty-dir");
        emptyDir.setDsVolume(true);
        emptyDir.setMedium("hdd");
        V1Volume volume = PodNewOrderExecutorServiceV2.convertToK8sEmptyDir(emptyDir);
        Assert.assertEquals("test-empty-dir", volume.getName());
        Assert.assertEquals("hdd", volume.getEmptyDir().getMedium());
    }

    @Test
    public void testConvertToK8sHostPath() {
        HostPathVolume hostPathVo = new HostPathVolume();
        hostPathVo.setName("test-host-path");
        hostPathVo.setDsVolume(true);
        hostPathVo.setPath("/var/logs");
        V1Volume volume = PodNewOrderExecutorServiceV2.convertToK8sHostPath(hostPathVo);
        Assert.assertEquals("test-host-path", volume.getName());
        Assert.assertEquals("/var/logs", volume.getHostPath().getPath());
        Assert.assertEquals(null, volume.getHostPath().getType());
    }

    private ContainerPurchase getContainerPurchase(String name, ContainerType type) {
        ContainerPurchase purchase = new ContainerPurchase();
        purchase.setName(name);
        purchase.setContainerType(type.getType());
        return purchase;
    }
    
    @Test
    public void testGeneContainers() throws IOException {
        List<ContainerPurchase> containers = new ArrayList<>();
        containers.add(getContainerPurchase("c-1", ContainerType.INIT));
        containers.add(getContainerPurchase("c-2", ContainerType.WORKLOAD));
        containers.add(getContainerPurchase("c-3", ContainerType.DS_WORKLOAD));
        List<V1Container> result;

        result = podNewOrderExecutorService.geneContainers(containers, ContainerType.INIT, 
                                                           new ArrayList<V1Volume>(), false);
        Assert.assertEquals(result.size(), 1);
        Assert.assertEquals("c-1", result.get(0).getName());

        result = podNewOrderExecutorService.geneContainers(containers, ContainerType.WORKLOAD, 
                                                           new ArrayList<V1Volume>(), false);
        Assert.assertEquals(result.size(), 1);
        Assert.assertEquals("c-2", result.get(0).getName());

        result = podNewOrderExecutorService.geneContainers(containers, ContainerType.DS_WORKLOAD, 
                                                           new ArrayList<V1Volume>(), false);
        Assert.assertEquals(result.size(), 1);
        Assert.assertEquals("c-3", result.get(0).getName());
    }

    @Test
    public void testGeneImageInitContainers() throws IOException {
        List<V1Container> initContainers = podNewOrderExecutorService.geneContainers(
            Arrays.asList(getContainerPurchase("c-1", ContainerType.INIT)), ContainerType.INIT, 
            new ArrayList<V1Volume>(), false);
        List<V1Container> workloadContainers = podNewOrderExecutorService.geneContainers(
            Arrays.asList(getContainerPurchase("c-2", ContainerType.WORKLOAD)), ContainerType.WORKLOAD, 
            new ArrayList<V1Volume>(), false);
        List<V1Container> dsContainers = podNewOrderExecutorService.geneContainers(
            Arrays.asList(getContainerPurchase("c-3", ContainerType.DS_WORKLOAD)), ContainerType.DS_WORKLOAD, 
            new ArrayList<V1Volume>(), false);

        List<V1Container>result = podNewOrderExecutorService.geneImageInitContainers(
            new BciOrderExtra() , initContainers, workloadContainers, dsContainers, 
            new ArrayList<V1Container>(), new HashMap<String, String>());
        Assert.assertEquals(result.size(), 3);
        Assert.assertEquals("bci-internal-image-download-init-container-image-init-0", result.get(0).getName());
        Assert.assertEquals("bci-internal-image-download-init-container-image-workload-0", result.get(1).getName());
        Assert.assertEquals("bci-internal-image-download-init-container-image-ds-0", result.get(2).getName());
    }

    @Test
    public void testCreateAndReturnConfigMapVolume() throws K8sServiceException, ApiException {
        ConfigFile configFile = new ConfigFile();
        configFile.setName("test-config");
        configFile.setConfigFiles(new ArrayList<ConfigFileDetail>());
        
        podNewOrderExecutorService.setK8sServiceInUT(new K8sServiceMock());
        V1Volume volume = podNewOrderExecutorService.createAndReturnConfigMapVolume(configFile, "", "p-1");
        Assert.assertEquals("p-1-test-config", volume.getConfigMap().getName());
    }

    @Test
    public void addPodResourceLimitTest() {
        // add pod Resource limit succeed
        V1ObjectMeta metadata = new V1ObjectMeta();
        podNewOrderExecutorService.addResourceLimitAnnotation(metadata);
        Assert.assertTrue(metadata.getAnnotations().containsKey("bci_internal_EnablePodLimits"));
        Assert.assertEquals(metadata.getAnnotations().get("bci_internal_EnablePodLimits"), "true");
    }

    @Test
    public void addResourceLimitToContainersTest() {
        // add pod Resource limit succeed
        List<V1Container> containers = new ArrayList<>();
        V1Container container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .withNewResources()
                .withLimits(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("1"));
                    put("memory", new Quantity("1Gi"));
                }})
                .withRequests(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("500m"));
                    put("memory", new Quantity("500Mi"));
                }})
            .endResources().build();
        containers.add(container);
        podNewOrderExecutorService.addResourceLimitToContainers(containers, 1F, 2F);
        Assert.assertEquals(1F, containers.get(0).getResources().getLimits().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(1*1024*1024*1024F, containers.get(0).getResources().getLimits().get("memory").getNumber().floatValue(), 0.01);

        containers.clear();
        container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .withNewResources()
                .withRequests(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("500m"));
                    put("memory", new Quantity("500Mi"));
                }})
            .endResources().build();
        containers.add(container);
        podNewOrderExecutorService.addResourceLimitToInitContainers(containers, 1F, 2F);
        Assert.assertEquals(1F, containers.get(0).getResources().getLimits().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(2*1024*1024*1024F, containers.get(0).getResources().getLimits().get("memory").getNumber().floatValue(), 0.01);

        containers.clear();
        podNewOrderExecutorService.addResourceLimitToInitContainers(containers, 1F, 2F);

        container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .build();
        containers.add(container);
        podNewOrderExecutorService.addResourceLimitToInitContainers(containers, 1F, 2F);
        Assert.assertEquals(1F, containers.get(0).getResources().getLimits().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(2*1024*1024*1024F, containers.get(0).getResources().getLimits().get("memory").getNumber().floatValue(), 0.01);

        containers.clear();
        container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .withNewResources()
                .withLimits(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("1"));
                }})
                .withRequests(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("500m"));
                    put("memory", new Quantity("500Mi"));
                }})
            .endResources().build();
        containers.add(container);
        podNewOrderExecutorService.addResourceLimitToInitContainers(containers, 1F, 2F);
        Assert.assertEquals(1F, containers.get(0).getResources().getLimits().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(2*1024*1024*1024F, containers.get(0).getResources().getLimits().get("memory").getNumber().floatValue(), 0.01);


        containers.clear();
        container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .withNewResources()
                .withLimits(new HashMap<String, Quantity>() {{
                    put("memory", new Quantity("1Gi"));
                }})
                .withRequests(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("500m"));
                    put("memory", new Quantity("500Mi"));
                }})
            .endResources().build();
        containers.add(container);
        podNewOrderExecutorService.addResourceLimitToInitContainers(containers, 1F, 2F);
        Assert.assertEquals(1F, containers.get(0).getResources().getLimits().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(1*1024*1024*1024F, containers.get(0).getResources().getLimits().get("memory").getNumber().floatValue(), 0.01);

        containers.clear();
        container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .withNewResources()
                .withRequests(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("500m"));
                    put("memory", new Quantity("500Mi"));
                }})
            .endResources().build();
        containers.add(container);
        podNewOrderExecutorService.configureResourceToSidecarContainers(containers, 1F, 2F);
        Assert.assertEquals(0F, containers.get(0).getResources().getRequests().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(0F, containers.get(0).getResources().getRequests().get("memory").getNumber().floatValue(), 0.01);

        containers.clear();
        podNewOrderExecutorService.configureResourceToSidecarContainers(containers, 1F, 2F);

        container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .build();
        containers.add(container);
        podNewOrderExecutorService.configureResourceToSidecarContainers(containers, 1F, 2F);
        Assert.assertEquals(0F, containers.get(0).getResources().getRequests().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(0F, containers.get(0).getResources().getRequests().get("memory").getNumber().floatValue(), 0.01);

        containers.clear();
        container = new V1ContainerBuilder()
            .withName("example-container")  // 容器名称
            .withNewResources()
                .withLimits(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("0"));
                    put("memory", new Quantity("0"));
                }})
                .withRequests(new HashMap<String, Quantity>() {{
                    put("cpu", new Quantity("500m"));
                    put("memory", new Quantity("500Mi"));
                }})
            .endResources().build();
        containers.add(container);
        podNewOrderExecutorService.configureResourceToSidecarContainers(containers, 1F, 2F);
        Assert.assertEquals(0F, containers.get(0).getResources().getRequests().get("cpu").getNumber().floatValue(), 0.01);
        Assert.assertEquals(0F, containers.get(0).getResources().getRequests().get("memory").getNumber().floatValue(), 0.01);


    }

    @Test
    public void addResourceLimitAnnotationTest() {
        podNewOrderExecutorService.addResourceLimitAnnotation(null);
    }

    @Test
    public void setHostPathIfPossibleTest() throws IOException {
        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        V1Pod k8sPod = new V1Pod();

        k8sPod.setSpec(new V1PodSpec());
        k8sPod.getSpec().setVolumes(new ArrayList<V1Volume>());

        Volume volume = new Volume();;
        HostPathVolume hostPath = new HostPathVolume();
        hostPath.setPath("/var/log/pods");
        hostPath.setName("test-pfs");
        volume.setHostPath(Arrays.asList(hostPath));
        bciOrderExtra.setVolume(volume);
        bciOrderExtra.setPodId("123");
        podNewOrderExecutorService.setHostPathIfPossible(bciOrderExtra, k8sPod);
    }

    @Test
    public void processPodResourceIgnoreContainersIfPossibleTest() {
        V1Container container1 = new V1Container();
        container1.setName("container1");
        V1ResourceRequirements resources1 = new V1ResourceRequirements();
        resources1.setRequests(new HashMap<>());
        resources1.getRequests().put("cpu", new Quantity("1"));
        resources1.getRequests().put("memory", new Quantity("2Gi"));
        container1.setResources(resources1);

        V1Container container2 = new V1Container();
        container2.setName("container2");
        V1ResourceRequirements resources2 = new V1ResourceRequirements();
        resources2.setRequests(new HashMap<>());
        resources2.getRequests().put("cpu", new Quantity("1"));
        resources2.getRequests().put("memory", new Quantity("2Gi"));
        container2.setResources(resources2);

        List<V1Container> containers = Arrays.asList(container1, container2);
        String podResourceIgnoreContainers = "container1";

        boolean result = podNewOrderExecutorService.processPodResourceIgnoreContainersIfPossible(containers, podResourceIgnoreContainers);

        Assert.assertEquals(true, result);
        Assert.assertEquals("0", container1.getResources().getRequests().get("cpu").getNumber().toString());
        Assert.assertEquals("0", container1.getResources().getRequests().get("memory").getNumber().toString());
        Assert.assertEquals("1", container2.getResources().getRequests().get("cpu").getNumber().toString());
        Assert.assertEquals(new Quantity("2Gi"), container2.getResources().getRequests().get("memory"));

        resources1.setRequests(new HashMap<>());
        resources1.getRequests().put("cpu", new Quantity("1"));
        resources1.getRequests().put("memory", new Quantity("2Gi"));
        container1.setResources(resources1);

        resources2.setRequests(new HashMap<>());
        resources2.getRequests().put("cpu", new Quantity("1"));
        resources2.getRequests().put("memory", new Quantity("2Gi"));
        container2.setResources(resources2);

        podResourceIgnoreContainers = "container3,container4";
        result = podNewOrderExecutorService.processPodResourceIgnoreContainersIfPossible(containers, podResourceIgnoreContainers);
        Assert.assertEquals(true, result);
        Assert.assertEquals("1", container1.getResources().getRequests().get("cpu").getNumber().toString());
        Assert.assertEquals(new Quantity("2Gi"), container1.getResources().getRequests().get("memory"));
        Assert.assertEquals("1", container2.getResources().getRequests().get("cpu").getNumber().toString());
        Assert.assertEquals(new Quantity("2Gi"), container2.getResources().getRequests().get("memory"));
    }

    public String repeat(String str, int n) {
        return new String(new char[n]).replace("\0", str);
    }

    @Test
    public void testValidatePodLabel() {
        String podId = "pod1";
        // label is null
        {
            boolean result = podNewOrderExecutorService.validatePodLabel(podId, null);
            Assert.assertFalse(result);
        }
        // label key is null
        {
            Label label = new Label();
            label.setLabelKey("");
            label.setLabelValue("value");
            boolean result = podNewOrderExecutorService.validatePodLabel(podId, label);
            Assert.assertFalse(result);
        }
        // label key is too long
        {
            Label label = new Label();
            label.setLabelKey(repeat("a", PodConstants.POD_LABEL_MAX_KEY_LENGTH + 1));
            label.setLabelValue("value");
            boolean result = podNewOrderExecutorService.validatePodLabel(podId, label);
            Assert.assertFalse(result);
        }
        // label key is too long
        {
            Label label = new Label();
            label.setLabelKey("key");
            label.setLabelValue(repeat("a", PodConstants.POD_LABEL_MAX_VALUE_LENGTH + 1));
            boolean result = podNewOrderExecutorService.validatePodLabel(podId, label);
            Assert.assertFalse(result);
        }
        // label key and value is valid
        {
            Label label = new Label();
            label.setLabelKey("key");
            label.setLabelValue("value");
            boolean result = podNewOrderExecutorService.validatePodLabel(podId, label);
            Assert.assertTrue(result);
        }
    }

    @Test
    public void testValidatePodAnnotation() {
        String podId = "pod1";
        // NullAnnotation
        {
            boolean result = podNewOrderExecutorService.validatePodAnnotation(podId, null);
            Assert.assertFalse(result);
        }
        // Annotation_EmptyKey
        {
            Map.Entry<String, String> annotation = new AbstractMap.SimpleEntry<>("", "value");
            boolean result = podNewOrderExecutorService.validatePodAnnotation(podId, annotation);
            Assert.assertFalse(result);
        }
        // Annotation_KeyTooLong
        {
            StringBuilder keyBuilder = new StringBuilder();
            for (int i = 0; i < PodConstants.POD_ANNOTATION_MAX_KEY_LENGTH + 1; i++) {
                keyBuilder.append('a');
            }
            Map.Entry<String, String> annotation = new AbstractMap.SimpleEntry(keyBuilder.toString(), "value");
            boolean result = podNewOrderExecutorService.validatePodAnnotation(podId, annotation);
            Assert.assertFalse(result);
        }
        // Annotation_ValueTooLong
        {
            StringBuilder valueBuilder = new StringBuilder();
            for (int i = 0; i < PodConstants.POD_ANNOTATION_MAX_VALUE_LENGTH + 1; i++) {
                valueBuilder.append('a');
            }
            Map.Entry<String, String> annotation = new AbstractMap.SimpleEntry<>("key", valueBuilder.toString());
            boolean result = podNewOrderExecutorService.validatePodAnnotation(podId, annotation);
            Assert.assertFalse(result);
        }
        // Annotation_ValidAnnotation
        {
            Map.Entry<String, String> annotation = new AbstractMap.SimpleEntry<>("key", "value");
            boolean result = podNewOrderExecutorService.validatePodAnnotation(podId, annotation);
            Assert.assertTrue(result);
        }
    }
}
