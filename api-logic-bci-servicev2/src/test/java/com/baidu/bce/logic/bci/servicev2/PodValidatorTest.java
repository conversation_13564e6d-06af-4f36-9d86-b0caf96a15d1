package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.WhiteListKey;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.BciQuota;
import com.baidu.bce.logic.bci.servicev2.model.Bos;
import com.baidu.bce.logic.bci.servicev2.model.Capabilities;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.ImageRegistrySecret;
import com.baidu.bce.logic.bci.servicev2.model.Pfs;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.DatabaseUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodValidator;
import com.baidu.bce.logic.bci.servicev2.util.TestConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.TestUtil;
import com.baidu.bce.logic.bci.servicev2.util.Validator;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodValidatorTest {

    private static final String CREATE_POD_LOCATION = "classpath*:create_pod.json";

    private BaseCreateOrderRequestVo<IOrderItem> request;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private Validator validator;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        testUtil.setUp();
        request = testUtil.orderRequest(
                databaseUtil.getResource(CREATE_POD_LOCATION).getInputStream(), IOrderItem.class);
    }

    @Test
    public void validateCreateBCIServiceTypeTest() {
        podValidator.validateCreateBCIServiceType(request);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void validateCreateBCIServiceTypeExceptionTest() {
        request.getItems().get(0).getConfig().setServiceType("BCC");
        podValidator.validateCreateBCIServiceType(request);
    }

    @Test(expected = PodExceptions.ImageRegistryException.class)
    public void validateImageSecretTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.getImageRegistrySecrets().get(0).setServer("httw:/evce.cwv");
        podValidator.validateImageRegistrySecret(podPurchaseRequest);
    }

    @Test
    public void validateImageRegistrySecretTest() {
        podValidator.validateImageRegistrySecret((PodPurchaseRequest) request.getItems().get(0).getConfig());
    }

    @Test
    public void validateRepositoryTest() {
        podValidator.validateRepository((PodPurchaseRequest) request.getItems().get(0).getConfig());

        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
        // imageName长度校验
        try {
            List<ContainerPurchase> containerPurchases = new ArrayList<>();
            containerPurchases.add(new ContainerPurchase());
            podPurchaseRequest.setContainerPurchases(containerPurchases);
            StringBuilder imageName = new StringBuilder();
            for (int i = 1; i <= 1025; i ++) {
                imageName.append(1);
            }
            podPurchaseRequest.getContainerPurchases().get(0).setImageName(imageName.toString());
            podValidator.validateRepository(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ContainerImageNameException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ContainerImageNameInvalid", e.getCode());
            Assert.assertEquals("The container imageName is invalid, " +
                    "a valid imageName length must be between 1 and 1024.", e.getMessage());
        }
        // imageVersion长度校验
        try {
            List<ContainerPurchase> containerPurchases = new ArrayList<>();
            containerPurchases.add(new ContainerPurchase());
            podPurchaseRequest.setContainerPurchases(containerPurchases);
            StringBuilder imageVersion = new StringBuilder();
            for (int i = 1; i <= 1025; i ++) {
                imageVersion.append(1);
            }
            podPurchaseRequest.getContainerPurchases().get(0).setImageVersion(imageVersion.toString());
            podValidator.validateRepository(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ContainerImageVersionException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ContainerImageVersionInvalid", e.getCode());
            Assert.assertEquals("The container imageVersion is invalid, " +
                    "a valid imageVersion length must be between 1 and 1024.", e.getMessage());
        }
        // imageAddress长度校验
        try {
            List<ContainerPurchase> containerPurchases = new ArrayList<>();
            containerPurchases.add(new ContainerPurchase());
            podPurchaseRequest.setContainerPurchases(containerPurchases);
            StringBuilder imageAddress = new StringBuilder();
            for (int i = 1; i <= 1025; i ++) {
                imageAddress.append(1);
            }
            podPurchaseRequest.getContainerPurchases().get(0).setImageAddress(imageAddress.toString());
            podValidator.validateRepository(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ContainerImageAddressException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ContainerImageAddressInvalid", e.getCode());
            Assert.assertEquals("The container imageAddress is invalid, " +
                    "a valid imageAddress length must be between 1 and 1024.", e.getMessage());
        }
        // workingDir长度校验
        try {
            List<ContainerPurchase> containerPurchases = new ArrayList<>();
            containerPurchases.add(new ContainerPurchase());
            podPurchaseRequest.setContainerPurchases(containerPurchases);
            StringBuilder workingDir = new StringBuilder();
            for (int i = 1; i <= 1025; i ++) {
                workingDir.append(1);
            }
            podPurchaseRequest.getContainerPurchases().get(0).setWorkingDir(workingDir.toString());
            podValidator.validateRepository(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ContainerWorkingDirException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ContainerWorkingDirInvalid", e.getCode());
            Assert.assertEquals("The container workingDir is invalid, " +
                    "a valid workingDir length must be between 1 and 1024.", e.getMessage());
        }
        // commands长度校验
        try {
            List<ContainerPurchase> containerPurchases = new ArrayList<>();
            containerPurchases.add(new ContainerPurchase());
            podPurchaseRequest.setContainerPurchases(containerPurchases);
            List<String> commands = new ArrayList<>();
            for (int i = 1; i <= 1024; i ++) {
                commands.add("1");
            }
            podPurchaseRequest.getContainerPurchases().get(0).setCommands(commands);
            podValidator.validateRepository(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ContainerCommandsException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ContainerCommandsInvalid", e.getCode());
            Assert.assertEquals("The container commands is invalid, " +
                    "a valid commands length must be between 1 and 4096.", e.getMessage());
        }
    }

    @Test(expected = PodExceptions.ImageRegistryException.class)
    public void validateImageRegistryTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        for (ContainerPurchase containerPurchase : podPurchaseRequest.getContainerPurchases()) {
            containerPurchase.setImageAddress("fwee.r3g5ht/fdq");
        }
        podValidator.validateImageRegistrySecret((PodPurchaseRequest) request.getItems().get(0).getConfig());
    }

    @Test
    public void validateNullImageRegistrySecretTest() {
        podValidator.validateImageRegistrySecret(
                ((PodPurchaseRequest) request.getItems().get(0).getConfig())
                        .setImageRegistrySecrets(new ArrayList<ImageRegistrySecret>()));
    }

    @Test
    public void validateAutoRenewTimeMouthTest() {
        podValidator.validateAutoRenewTime("month", 1);
    }

    @Test
    public void validateAutoRenewTimeYearTest() {
        podValidator.validateAutoRenewTime("year", 1);
    }

    @Test
    public void validateAutoRenewTimeInvalidExceptionTest() {
        try {
            podValidator.validateAutoRenewTime("year", -1);
            Assert.assertTrue(false);
        } catch (PodExceptions.InvalidAutoRenewTimeException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.EipAutoRenewTimeInvalid", e.getCode());
            Assert.assertEquals("Eip autoRenewTime is invalid.", e.getMessage());
        }
        try {
            podValidator.validateAutoRenewTime("month", -1);
            Assert.assertTrue(false);
        } catch (PodExceptions.InvalidAutoRenewTimeException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.EipAutoRenewTimeInvalid", e.getCode());
            Assert.assertEquals("Eip autoRenewTime is invalid.", e.getMessage());
        }

        podValidator.validateAutoRenewTime("year", 2);
        Assert.assertTrue(true);
        podValidator.validateAutoRenewTime("month", 3);
        Assert.assertTrue(true);
    }

    @Test
    public void validateTimeUnitInvalidExceptionTest() {
        try {
            podValidator.validateAutoRenewTime("day", 1);
            Assert.assertTrue(false);
        } catch (PodExceptions.InvalidAutoRenewTimeUnitException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.EipAutoRenewTimeUnitInvalid", e.getCode());
            Assert.assertEquals("Eip autoRenewTimeUnit is invalid.", e.getMessage());
        }
    }

    @Test(expected = PodExceptions.EipAutoRenewTimeInvalidException.class)
    public void validateTimeUnitExceptionTest() {
        podValidator.validateAutoRenewTime("month", 13);
    }

    @Test
    public void validateEipBandwidthInMbpsTest() {
        EipPurchaseRequest request = new EipPurchaseRequest();
        request.setProductType(LogicalConstant.EipProductType.POSTPAY);
        request.setSubProductType(LogicalConstant.EipSubProductType.BANDWIDTH);
        request.setBandwidthInMbps(150);
        podValidator.validateEipBandwidthInMbps(request);
    }

    @Test(expected = PodExceptions.EipBandwidthInMbpsInvalidException.class)
    public void validateEipBandwidthInMbpsBANDWIDTHExceptionTest() {
        EipPurchaseRequest request = new EipPurchaseRequest();
        request.setProductType(LogicalConstant.EipProductType.POSTPAY);
        request.setSubProductType(LogicalConstant.EipSubProductType.BANDWIDTH);
        request.setBandwidthInMbps(550);
        podValidator.validateEipBandwidthInMbps(request);
    }

    @Test(expected = PodExceptions.EipBandwidthInMbpsInvalidException.class)
    public void validateEipBandwidthInMbpsNETRAFFICExceptionTest() {
        EipPurchaseRequest request = new EipPurchaseRequest();
        request.setProductType(LogicalConstant.EipProductType.POSTPAY);
        request.setSubProductType(LogicalConstant.EipSubProductType.BANDWIDTH);
        request.setBandwidthInMbps(1250);
        podValidator.validateEipBandwidthInMbps(request);
    }

    @Test
    public void validateEipRouteTypeAndBandwidthInMbpsNETRAFFICExceptionTest() {
        EipPurchaseRequest request = new EipPurchaseRequest();
        request.setProductType(LogicalConstant.EipProductType.POSTPAY);
        request.setSubProductType(LogicalConstant.EipSubProductType.NETRAFFIC);
        try {
            request.setRouteType("BGPSSSSS");
            request.setBandwidthInMbps(250);
            podValidator.validateEipBandwidthInMbps(request);
        } catch (PodExceptions.EipRouteTypeInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("Pod.EipRouteTypeInvalid", e.getCode());
            assertEquals("Eip routetype is invalid.", e.getMessage());
        }

        try {
            request.setRouteType("BGP");
            request.setBandwidthInMbps(250);
            podValidator.validateEipBandwidthInMbps(request);
        } catch (PodExceptions.EipBandwidthInMbpsInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("Pod.EipBandwidthInMbpsInvalid", e.getCode());
            assertEquals("ByTraffic only support BGP Postpaid and bandwidthInMbps less than 200."
                    , e.getMessage());
        }
    }

    @Test
    public void validateEipProductTypeNETRAFFICExceptionTest() {
        try {
            EipPurchaseRequest request = new EipPurchaseRequest();
            request.setSubProductType(LogicalConstant.EipSubProductType.NETRAFFIC);
            request.setBandwidthInMbps(150);
            request.setProductType(LogicalConstant.EipProductType.PREPAY);
            podValidator.validateEipBandwidthInMbps(request);
        } catch (PodExceptions.EipBandwidthInMbpsInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("Pod.EipBandwidthInMbpsInvalid", e.getCode());
            assertEquals("ByTraffic only support BGP Postpaid and bandwidthInMbps less than 200."
                    , e.getMessage());
        }
    }

    @Test(expected = PodExceptions.EipOperationDeniedException.class)
    public void validateEipBlackListTest() {
        podValidator.validateEipBlackList(Arrays.asList(WhiteListKey.EIP_BLACK_LIST));
    }

    @Test(expected = PodExceptions.ContainerException.class)
    public void validateBciParametersTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setContainerPurchases(null);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = PodExceptions.NameInvalidException.class)
    public void validateBciParametersNameTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setName("efo&%$#!@_-ID");
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = PodExceptions.ProductTypeInvalid.class)
    public void validateBciParametersPrepayTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setProductType("prepay");
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void validateBciParametersVolumeTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setVolume(null);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.ExceedLimitException.class)
    public void validateBciParametersCreatedQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(3);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.NfsQuotaExceededLimit.class)
    public void validateBciParametersNfsQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(2);
        bciQuota.setEmptyDirRatio(2);
        bciQuota.setEnvRatio(2);
        bciQuota.setNfsRatio(0);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.ConfigFileQuotaExceededLimit.class)
    public void validateBciParametersConfQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(0);
        bciQuota.setEmptyDirRatio(2);
        bciQuota.setEnvRatio(2);
        bciQuota.setNfsRatio(2);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.EmptyDirQuotaExceededLimit.class)
    public void validateBciParametersEmptyQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(2);
        bciQuota.setEmptyDirRatio(0);
        bciQuota.setEnvRatio(2);
        bciQuota.setNfsRatio(2);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = PodExceptions.PendingPodQuotaExceededLimit.class)
    public void validateCreateBciPendingPodQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(2);
        bciQuota.setEmptyDirRatio(2);
        bciQuota.setEnvRatio(2);
        bciQuota.setNfsRatio(2);
        bciQuota.setPodCreated(2);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        bciQuota.setPendingPodTotal(0);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = CommonExceptions.RequestInvalidException.class)
    public void validateAndSetSubnetUuidTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.setSecurityGroupId("default");
        podPurchaseRequest.setSubnetId("effwfscs");
        podPurchaseRequest.setSubnetUuid("test");
        ZoneMapDetail zoneMapDetail = new ZoneMapDetail();
        zoneMapDetail.setSubnetUuid("evevevfsveveewfwfwfw");
        Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
        Map<String, SubnetVo> subnetVoMap = new HashMap<>();
        Map<String, VpcVo> vpcVoMap = new HashMap<>();
        podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, vpcVoMap,false);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void validateAndSetSubnetTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.setSecurityGroupId("effwfscs");
        podPurchaseRequest.setSubnetId(null);
        podPurchaseRequest.setSubnetUuid(null);
        Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
        Map<String, SubnetVo> subnetVoMap = new HashMap<>();
        Map<String, VpcVo> vpcVoMap = new HashMap<>();
        podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, vpcVoMap, false);
    }

    @Test
    public void validateAndSetSubnetTestAll() {
        // subnet参数非法
        try {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
            podPurchaseRequest.setSubnetIds(null);
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid(null);
            Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
            Map<String, SubnetVo> subnetVoMap = new HashMap<>();
            Map<String, VpcVo> vpcVoMap = new HashMap<>();
            podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, vpcVoMap, false);
            Assert.assertTrue(false);
        } catch (CommonExceptions.RequestInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("The subnet should be specified.", e.getMessage());
        }
        // SecurityGroupId参数非法
        /*
        try {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
            podPurchaseRequest.setSubnetIds("test");
            podPurchaseRequest.setSubnetId(null);
            podPurchaseRequest.setSubnetUuid(null);
            podPurchaseRequest.setSecurityGroupId(null);
            Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
            Map<String, SubnetVo> subnetVoMap = new HashMap<>();
            podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, false);
            Assert.assertTrue(false);
        } catch (CommonExceptions.RequestInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("securityGroupId should be added", e.getMessage());
        }
        // 没有可用的zone（安全组为default）
        try {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
            podPurchaseRequest.setSubnetIds("");
            podPurchaseRequest.setSubnetId("lctest");
            podPurchaseRequest.setSubnetUuid(null);
            podPurchaseRequest.setSecurityGroupId("default");
            Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
            Map<String, SubnetVo> subnetVoMap = new HashMap<>();
            podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, false);
            Assert.assertTrue(false);
        } catch (CommonExceptions.RequestInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("BCI with NAT subnet can not bind eip", e.getMessage());
        }
        // 没有可用的zone（安全组为!default）
        try {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
            podPurchaseRequest.setSubnetIds("");
            podPurchaseRequest.setSubnetId("lctest");
            podPurchaseRequest.setSubnetUuid(null);
            podPurchaseRequest.setSecurityGroupId("test");
            Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
            Map<String, SubnetVo> subnetVoMap = new HashMap<>();
            podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, false);
            Assert.assertTrue(false);
        } catch (CommonExceptions.RequestInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("BCI with NAT subnet can not bind eip", e.getMessage());
        }
        */
        // 有可用的zone （安全组为default）
        /*
        try {

            SubnetVo subnetVo = new SubnetVo();
            subnetVo.setSubnetId("");
            subnetVo.setTotalIps(10);
            subnetVo.setUsedIps(5);

            when(subnetClient.findBySubnetId("sub1")).thenReturn(subnetVo);
            // ReflectionTestUtils.setField(podValidator, "subnetClient", subnetClient);
            when(podValidator.getSubnet("sub1")).thenReturn(subnetVo);
            // ReflectionTestUtils.setField(podValidator, "podValidator", podValidator);
            // when(subnetClient.findBySubnetId("sub1")).thenReturn(subnetVo);
            // ReflectionTestUtils.setField(podValidator, "subnetClient", subnetClient);
            // Mockito.mock(PodValidator.class, "getSubnet");  
            // Mockito.when(LogicUserService.getAccountId()).thenReturn("account1");
            // Mockito.when(podValidator.getSubnet("sub1")).thenReturn(subnetVo);
            // Mockito.when(podValidator.getSubnetWithIpUsage("sub1")).thenReturn(subnetVo);

            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
            podPurchaseRequest.setSubnetIds("");
            podPurchaseRequest.setSubnetId("lctest");
            podPurchaseRequest.setSubnetUuid(null);
            podPurchaseRequest.setSecurityGroupId("default");
            podPurchaseRequest.setPurchaseNum(50);
            Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
            ZoneMapDetail zoneMapDetail = new ZoneMapDetail();
            zoneMapDetail.setSubnetUuid("sub1");
            zoneMap.put("zone1", new ZoneMapDetail());
            Map<String, SubnetVo> subnetVoMap = new HashMap<>();
            podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, false);
            Assert.assertTrue(false);
        } catch (IpInSubnetNotEnoughExceptions e) {
            Assert.assertTrue(true);
            assertEquals("The ip left in the subnet is not enouth for this create", e.getMessage());
        }
        */
    }

    @Test(expected = CommonExceptions.ResourceNotExistException.class)
    public void getSubnetWithIpUsage() {
        podValidator.getSubnetWithIpUsage(null);
    }

    @Test
    public void regMatchTest(){
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"aa"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"1aa"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"a-a"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"a1"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"11"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"1.1"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"a-.1"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"1..-a.1"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN,"1.a.b-a.1"));
        Assert.assertFalse(validator.stringMatch(Validator.PATTERN, null));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "默认项目"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "默认项目111aaa"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "HelloWorld123"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "Hello-World_123"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "Test String 123"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "abc/123.你好"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "A1B2-C3_D4"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "测试-Example/123"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY, "Example-Test/123"));
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY,
                "Example-Test/123Example-Test/123Example-Test/123Example-Test/1234"));// len(s)=65
        Assert.assertTrue(validator.stringMatch(Validator.PATTERN_TAG_KEY,
                "默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默")); // len(s)=65
        Assert.assertFalse(validator.stringMatch(Validator.PATTERN_TAG_KEY,
                "默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目默认项目")); // len(s)=68
        Assert.assertFalse(validator.stringMatch(Validator.PATTERN_TAG_KEY,
                "a7B9kZq2L1vRj8Tg4FsYxP6mWnQ3eVtX5dJr0C8yH3bK2pZl9Qw4EJJJKKKKKKKKKKKK"));
        Assert.assertFalse(validator.stringMatch(Validator.PATTERN_TAG_KEY, "Hello@World"));
        Assert.assertFalse(validator.stringMatch(Validator.PATTERN_TAG_KEY, ""));
        Assert.assertFalse(validator.stringMatch(Validator.PATTERN_TAG_KEY, "abc#def"));
    }

    @Test(expected = PodExceptions.ContainerSecurityContextCapInvalidException.class)
    public void validateInvalieContainerSecCapFailed() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        Capabilities cap = new Capabilities();
        List<String> addCap = new ArrayList<>();
        addCap.add("NET_ADMIN");
        addCap.add("SYS_PTRACE");
        cap.setAdd(addCap);
        podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext().setCapabilities(cap);
        podValidator.validateSecurityContextOfContainers(podPurchaseRequest.getContainerPurchases(), false);
    }

    @Test
    public void validateInvalieContainerSecCapSuccessForValidCapAdd() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        Capabilities cap = new Capabilities();
        cap.setAdd(Collections.singletonList("SETGID"));
        podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext().setCapabilities(cap);
        podValidator.validateSecurityContextOfContainers(podPurchaseRequest.getContainerPurchases(), false);
    }

    @Test
    public void validateInvalieContainerSecCapSuccessForNotCapAdd() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        Capabilities cap = new Capabilities();
        podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext().setCapabilities(cap);
        podValidator.validateSecurityContextOfContainers(podPurchaseRequest.getContainerPurchases(), false);
    }

    @Test
    public void validateContainerPrivileged() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext().setPrivileged(true);

        try {
            podValidator.validateContainerPrivileged("test", 
                podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext(),
                false, false);
            Assert.assertTrue(false);
        } catch (PodExceptions.ContainerSecurityContextCapInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("The container securityContext is invalid, capability [privileged] is forbidden.",
            e.getMessage());
        }
    }

    @Test
    public void validateContainerPrivileged2() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext().setPrivileged(true);

        try {
            podValidator.validateContainerPrivileged("test", 
                podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext(),
                true, false);
            Assert.assertTrue(true);
        } catch (PodExceptions.ContainerSecurityContextCapInvalidException e) {
            Assert.assertTrue(false);
            assertEquals("The container securityContext is invalid, capability [privileged] is forbidden.",
            e.getMessage());
        }
    }

    @Test
    public void validateContainerPrivileged3() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext().setPrivileged(true);

        try {
            podValidator.validateContainerPrivileged("test", 
                podPurchaseRequest.getContainerPurchases().get(0).getSecurityContext(),
                false, false);
            Assert.assertTrue(false);
        } catch (PodExceptions.ContainerSecurityContextCapInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("The container securityContext is invalid, capability [privileged] is forbidden.",
            e.getMessage());
        }
    }

    @Test
    public void validateMountVolumeTypeTest() {
        ContainerPurchase containerPurchase = new ContainerPurchase();
        Volume volume = new Volume();
        Pfs pfs = new Pfs();
        pfs.setName("pfs");
        pfs.setPath("pfs");
        pfs.setServer("pfs");
        volume.setPfs(Arrays.asList(pfs));
        
        Bos bos = new Bos();
        bos.setName("test-bos");
        bos.setBucket("bos-test-gz");
        bos.setUrl("gz.bcebos.com");
        bos.setAk("ak");
        bos.setSk("sk");
        bos.setReadOnly(false);
        volume.setBos(Arrays.asList(bos));
        podValidator.validateMountVolumeType(containerPurchase, volume);

        HostPathVolume hostPath = new HostPathVolume();
        hostPath.setName("test");
        hostPath.setPath("/var/log/pods");
        volume.setHostPath(Arrays.asList(hostPath));
        podValidator.validateMountVolumeType(containerPurchase, volume);
    }

    @Test
    public void validateSecurityGroupTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        try {
            podPurchaseRequest.setSecurityGroupId("g-9x94eb2ztdcc,,g-9x94eb2ztdcd");
            podValidator.validateSecurityGroup(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("The security groups is invalid.", e.getMessage());
        }

        try {
            podPurchaseRequest.setSecurityGroupId(",g-9x94eb2ztdce");
            podValidator.validateSecurityGroup(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("The security groups is invalid.", e.getMessage());
        }

        try {
            podPurchaseRequest.setSecurityGroupId("g-9x94eb2ztdcf,g-9x94eb2ztdcf");
            podValidator.validateSecurityGroup(podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            assertEquals("The securityGroup [g-9x94eb2ztdcf] is duplicated.", e.getMessage());
        }
    }

    @Test
    public void checkValue2Test() {
        int max = PodConstants.DELAY_RELEASE_MAX_MINUTE;

        int value1 = -1;
        boolean ans1 = ReflectionTestUtils.invokeMethod(podValidator,"checkValue2", value1, max);
        Assert.assertFalse(ans1);

        int value2 = 0;
        boolean ans2 = ReflectionTestUtils.invokeMethod(podValidator,"checkValue2", value2, max);
        Assert.assertTrue(ans2);

        int value3 = 1;
        boolean ans3 = ReflectionTestUtils.invokeMethod(podValidator,"checkValue2", value3, max);
        Assert.assertTrue(ans3);

        int value4 = max;
        boolean ans4 = ReflectionTestUtils.invokeMethod(podValidator,"checkValue2", value4, max);
        Assert.assertTrue(ans4);
    }

    @Test
    public void validateCreateBciParamsTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        List<ContainerPurchase> containerPurchases = new ArrayList<ContainerPurchase>();
        containerPurchases.add(new ContainerPurchase());
        podPurchaseRequest.setContainerPurchases(containerPurchases);
        podPurchaseRequest.setProductType("postpay");

        int max = PodConstants.DELAY_RELEASE_MAX_MINUTE;
        try {
            podPurchaseRequest.setDelayReleaseDurationMinute(-1);
            ReflectionTestUtils.invokeMethod(podValidator,"validateCreateBciParams", podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.DelayReleaseDurationMinuteException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.DelayReleaseDurationMinuteInvalid", e.getCode());
            Assert.assertEquals("The delayReleaseDurationMinute should be set between 0-"
                    + max + ".", e.getMessage());
        }

        // postpay不匹配
        try {
            podPurchaseRequest.setProductType("PostPay1");
            ReflectionTestUtils.invokeMethod(podValidator,"validateCreateBciParams", podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ProductTypeInvalid e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ProductTypeInvalid", e.getCode());
            Assert.assertEquals("The product type is invalid. Only PostPay/bidding are supported.", e.getMessage());
        }
        try {
            podPurchaseRequest.setProductType("PostPay");
            podPurchaseRequest.setDelayReleaseDurationMinute(-1);
            ReflectionTestUtils.invokeMethod(podValidator,"validateCreateBciParams", podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.DelayReleaseDurationMinuteException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.DelayReleaseDurationMinuteInvalid", e.getCode());
            Assert.assertEquals("The delayReleaseDurationMinute should be set between 0-"
                    + max + ".", e.getMessage());
        }
        try {
            podPurchaseRequest.setProductType("postpay");
            podPurchaseRequest.setDelayReleaseDurationMinute(-1);
            ReflectionTestUtils.invokeMethod(podValidator,"validateCreateBciParams", podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.DelayReleaseDurationMinuteException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.DelayReleaseDurationMinuteInvalid", e.getCode());
            Assert.assertEquals("The delayReleaseDurationMinute should be set between 0-"
                    + max + ".", e.getMessage());
        }
        // biddings不匹配
        try {
            podPurchaseRequest.setProductType("biddings1");
            ReflectionTestUtils.invokeMethod(podValidator,"validateCreateBciParams", podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ProductTypeInvalid e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ProductTypeInvalid", e.getCode());
            Assert.assertEquals("The product type is invalid. Only PostPay/bidding are supported.",
                    e.getMessage());
        }
        try {
            podPurchaseRequest.setProductType("Bidding");
            ReflectionTestUtils.invokeMethod(podValidator,"validateCreateBciParams", podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.ProductTypeInvalid e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.ProductTypeInvalid", e.getCode());
            Assert.assertEquals("The product type is invalid. Only PostPay/bidding are supported.",
                    e.getMessage());
        }
        try {
            podPurchaseRequest.setProductType("bidding");
            podPurchaseRequest.setDelayReleaseDurationMinute(-1);
            ReflectionTestUtils.invokeMethod(podValidator,"validateCreateBciParams", podPurchaseRequest);
            Assert.assertTrue(false);
        } catch (PodExceptions.DelayReleaseDurationMinuteException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.DelayReleaseDurationMinuteInvalid", e.getCode());
            Assert.assertEquals("The delayReleaseDurationMinute should be set between 0-"
                    + max + ".", e.getMessage());
        }
    }
}
