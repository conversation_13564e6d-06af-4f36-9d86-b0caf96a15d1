package com.baidu.bce.logic.bci.servicev2.mock;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthorityList;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.springframework.stereotype.Component;

@Component
public class ZoneClientMock extends ZoneClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public ZoneClientMock(String accessKey, String secretKey) {
        super(accessKey, secretKey);
    }

    public ZoneClientMock() {
        super(null, null, null);
    }

    @Override
    public ZoneMapDetail createZoneByLogicalZone(String zoneId) {
        ZoneMapDetail zoneMapDetail = new ZoneMapDetail();
        if (zoneId.equalsIgnoreCase("zoneC")) {
            zoneMapDetail.setAccountId(LogicUserService.getAccountId());
            zoneMapDetail.setLogicalZone("zoneC");
            zoneMapDetail.setPhysicalZone("AZONE-nmg02");
            zoneMapDetail.setSubnetUuid("1cb5846d-0302-432d-97ac-b1ba5448a287");
            zoneMapDetail.setType("available");
            zoneMapDetail.setZoneId("1");
        } else if (zoneId.equalsIgnoreCase("zoneB")) {
            zoneMapDetail.setAccountId(LogicUserService.getAccountId());
            zoneMapDetail.setLogicalZone("zoneB");
            zoneMapDetail.setPhysicalZone("AZONE-gzns");
            zoneMapDetail.setSubnetUuid("1cb5846d-0302-432d-97ac-b1ba5448a287");
            zoneMapDetail.setType("available");
            zoneMapDetail.setZoneId("1");
        } else {
            throw new BceInternalResponseException("");
        }
        return zoneMapDetail;
    }

    @Override
    public ZoneMapDetail createZoneByPhysicalZone(String physicalZone) {
        ZoneMapDetail zoneMapDetail = new ZoneMapDetail();
        if (physicalZone.equals("AZONE-gzns")) {
            zoneMapDetail.setAccountId(LogicUserService.getAccountId());
            zoneMapDetail.setLogicalZone("zoneC");
            zoneMapDetail.setPhysicalZone("AZONE-gzns");
            zoneMapDetail.setSubnetUuid("1cb5846d-0302-432d-97ac-b1ba5448a287");
            zoneMapDetail.setType("available");
            zoneMapDetail.setZoneId("1");
        } else {
            throw new BceInternalResponseException("");
        }
        return zoneMapDetail;
    }

    public ZoneAndAuthorityList listZoneResourceV2() {
        return new ZoneAndAuthorityList();
    }
}
