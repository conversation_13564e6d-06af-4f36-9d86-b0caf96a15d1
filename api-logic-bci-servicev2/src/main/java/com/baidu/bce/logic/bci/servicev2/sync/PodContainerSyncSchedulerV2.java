package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.sync.service.SyncServiceV2;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@Configuration("PodContainerSyncSchedulerV2")
@Profile("default")
public class PodContainerSyncSchedulerV2 extends SyncServiceV2 {

//    private static final Logger LOGGER = LoggerFactory.getLogger(PodContainerSyncSchedulerV2.class);
//
//    @Autowired
//    private PodContainerSyncServiceV2 podContainerSyncService;
//
//    public void handleAddPod(V1Pod pod) {
//        podContainerSyncService.syncPodStatus(pod);
//    }
//
//    public void handleUpdatePod(V1Pod oldPod, V1Pod newPod) {
//        if (!oldPod.getStatus().getPhase().equals(newPod.getStatus().getPhase()) ||
//                !oldPod.getStatus().getContainerStatuses().equals(newPod.getStatus().getContainerStatuses())) {
//            podContainerSyncService.syncPodStatus(newPod);
//        }
//    }
}
