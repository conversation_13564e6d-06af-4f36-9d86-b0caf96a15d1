package com.baidu.bce.logic.bci.servicev2.pod;

import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.bci.daov2.chargestatus.PodChargeStatusDaoV2;
import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.service.constant.CPT1SyncStatus;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.DeleteTagAssociationRequest;
import com.baidu.bce.logical.tag.sdk.model.Resource;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.Date;

import static com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate;


public abstract class BaseServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseServiceV2.class);

    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    protected ContainerDaoV2 containerDao;

    @Autowired
    protected PodExtraDaoV2 podExtraDao;

    @Autowired
    protected PodChargeStatusDaoV2 podChargeStatusDao;

    @Autowired
    protected LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    protected RegionConfiguration regionConfiguration;

    @Autowired
    private BillingResourceSyncManagerV2 billingResourceSyncManager;

    protected void simplyDeletePod(PodPO podPO) {
        try {
            podDao.deletePod(podPO.getUserId(), podPO.getPodUuid());
            containerDao.deleteContainers(getAccountId(), podPO.getPodId());
            unBindTags(podPO);
        } catch (Exception e) {

        }
    }

    protected PodPO getPodPOByPodId(String podId) {
        if (StringUtils.isBlank(podId)) {
            throw new CommonExceptions.RequestInvalidException();
        }
        PodPO podPO = podDao.getPodDetail(getAccountId(), podId);
        if (podPO == null) {
            LOGGER.debug("get podPO return null, uuid is {}", podId);
            throw new CommonExceptions.ResourceNotExistException();
        }
        return podPO;
    }

    protected PodPO getByPodId(String podId) {
        if (StringUtils.isBlank(podId)) {
            throw new CommonExceptions.RequestInvalidException();
        }
        PodPO podPO = podDao.getPodById(podId);
        if (podPO == null) {
            LOGGER.debug("get podPO return null, uuid is {}", podId);
            throw new CommonExceptions.ResourceNotExistException();
        }
        return podPO;
    }

    public void unBindTags(PodPO podPO) {

        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(podPO.getUserId());
        DeleteTagAssociationRequest request = new DeleteTagAssociationRequest();
        Resource resource = new Resource();
        resource.setRegion(regionConfiguration.getCurrentRegion());
        resource.setResourceId(podPO.getPodId());
        resource.setResourceUuid(podPO.getPodUuid());
        resource.setServiceType(PodConstants.SERVICE_TYPE);
        request.setResource(resource);
        tagClient.deleteTagAssociation(request);
    }

    public void unBindTags(String podId, String podUuid) {

        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
        DeleteTagAssociationRequest request = new DeleteTagAssociationRequest();
        Resource resource = new Resource();
        resource.setRegion(regionConfiguration.getCurrentRegion());
        resource.setResourceId(podId);
        resource.setResourceUuid(podUuid);
        resource.setServiceType(PodConstants.SERVICE_TYPE);
        request.setResource(resource);

        tagClient.deleteTagAssociation(request);
    }


    public String getAccountId() {
        return LogicUserService.getAccountId();
    }

    protected void deletePodFromNova(String accountID, String uuid) {
        try {
            PodClient podClient = logicPodClientFactory.createPodClientByAccountId(accountID);
            podClient.deletePod(uuid);
        } catch (BceInternalResponseException e) {
            if (e.getHttpStatus() != 404) {
                LOGGER.error("Delete from backend failed, exception is {}", e);
                throwPermissionDeniedExceptionIfAppropriate(e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
    }

    public void deletePodRecord(PodPO podPO) {
        PodChargeStatus podChargeStatus = new PodChargeStatus();
        podChargeStatus.setPodUuid(podPO.getPodUuid());
        podChargeStatus.setPreviousState(podPO.getStatus());
        podChargeStatus.setCurrentState(BciStatus.DELETED.getName());
        podChargeStatus.setChargeState(PodConstants.NO_CHARGE);
        Timestamp timestampNow = new Timestamp(new Date().getTime());
        podChargeStatus.setResourceVersion(podPO.getResourceVersion());
        podChargeStatus.setCpt1SyncState(CPT1SyncStatus.DONE);
        podChargeStatus.setCreatedTime(timestampNow);
        podChargeStatus.setUpdateTime(timestampNow);

        podChargeStatusDao.insert(podChargeStatus);
        // 对于cpt1查询最新podChargeStatus的version
        if (podPO.getCpt1()) {
            billingResourceSyncManager.doSync(podPO, podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                    podPO.getPodUuid(), podPO.getResourceVersion(), PodConstants.NO_CHARGE, 0);
            LOGGER.debug("[call billing] finish insert billing sync task in deletePodRecord1, id {}, account {}, pod {}, resource version {}, " +
                            "current status {}", podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                    podPO.getPodUuid(), podPO.getResourceVersion(), PodConstants.NO_CHARGE) ;
        }
    }

    public void deletePodRecordPodChargeStatus(PodPO podPO) {
        PodChargeStatus podChargeStatus = new PodChargeStatus();
        podChargeStatus.setPodUuid(podPO.getPodUuid());
        podChargeStatus.setPreviousState(podPO.getStatus());
        podChargeStatus.setCurrentState(BciStatus.DELETED.getName());
        podChargeStatus.setChargeState(PodConstants.NO_CHARGE);
        Timestamp timestampNow = new Timestamp(new Date().getTime());
        podChargeStatus.setResourceVersion(podPO.getResourceVersion());
        podChargeStatus.setCpt1SyncState(podPO.getIntCpt1Mode());
        podChargeStatus.setCreatedTime(timestampNow);
        podChargeStatus.setUpdateTime(timestampNow);
        podChargeStatusDao.insert(podChargeStatus);
    }
}
