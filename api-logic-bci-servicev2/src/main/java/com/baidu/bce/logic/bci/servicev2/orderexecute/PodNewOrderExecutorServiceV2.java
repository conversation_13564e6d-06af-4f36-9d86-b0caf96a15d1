package com.baidu.bce.logic.bci.servicev2.orderexecute;

import com.baidu.bce.fbi.common.Pair;
import com.baidu.bce.internalsdk.bci.EniClient;
import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.constant.EipConstant;
import com.baidu.bce.internalsdk.bci.constant.ImageConstant;
import com.baidu.bce.internalsdk.bci.model.ControllerBindEipState;
import com.baidu.bce.internalsdk.bci.model.EipResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEniSelfResponse;
import com.baidu.bce.internalsdk.bci.model.TransactionDetailResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.eipv2.model.EipListResponse;
import com.baidu.bce.internalsdk.order.BidPrice;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.OrderClientV2;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderFlavorUpdateItem;
import com.baidu.bce.internalsdk.order.model.OrderFlavorUpdateRequest;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.PricingDetail;
import com.baidu.bce.internalsdk.order.model.ResourceMapping;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.imageaccelerate.ImageAccelerateDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.model.ImageCachePO;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.model.ImageDetailPO;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.BciTagConstant;
import com.baidu.bce.logic.bci.servicev2.constant.ImageCacheOwner;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PodVolumeType;
import com.baidu.bce.logic.bci.servicev2.constant.QuotaKeys;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.model.Affinity;
import com.baidu.bce.logic.bci.servicev2.model.BatchCreatePodsResult;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.Bos;
import com.baidu.bce.logic.bci.servicev2.model.CephFSVolume;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerTermination;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.CreateImageCacheRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateImageCacheRequest.ImageInfo;
import com.baidu.bce.logic.bci.servicev2.model.DNSConfig;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.model.FlexVolume;
import com.baidu.bce.logic.bci.servicev2.model.HTTPGetAction;
import com.baidu.bce.logic.bci.servicev2.model.HTTPHeader;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageRegistrySecret;
import com.baidu.bce.logic.bci.servicev2.model.Lifecycle;
import com.baidu.bce.logic.bci.servicev2.model.LifecycleHook;
import com.baidu.bce.logic.bci.servicev2.model.MetaInfo;
import com.baidu.bce.logic.bci.servicev2.model.NFSAnnotation;
import com.baidu.bce.logic.bci.servicev2.model.Nfs;
import com.baidu.bce.logic.bci.servicev2.model.OrderSuccessContentVar;
import com.baidu.bce.logic.bci.servicev2.model.Pfs;
import com.baidu.bce.logic.bci.servicev2.model.PodCondition;
import com.baidu.bce.logic.bci.servicev2.model.PodExtra;
import com.baidu.bce.logic.bci.servicev2.model.Port;
import com.baidu.bce.logic.bci.servicev2.model.Probe;
import com.baidu.bce.logic.bci.servicev2.model.TCPSocketAction;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.pod.ImageCacheServiceV2;
import com.baidu.bce.logic.bci.servicev2.pod.PodLogService;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.statemachine.StateMachine;
import com.baidu.bce.logic.bci.servicev2.util.CacheUtil;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.bci.servicev2.util.PrometheusMetricsService;
import com.baidu.bce.logic.bci.servicev2.util.Util;
import com.baidu.bce.logic.core.constants.RegionConstant;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.order.executor.sdk.model.FaultTrace;
import com.baidu.bce.order.executor.sdk.model.MessageCenterModel;
import com.baidu.bce.order.executor.sdk.model.ReceiverType;
import com.baidu.bce.pricing.model.domain.chargeitem.ChargeItem;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1Capabilities;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1ConfigMapVolumeSource;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ContainerPort;
import io.kubernetes.client.openapi.models.V1ContainerPortBuilder;
import io.kubernetes.client.openapi.models.V1EmptyDirVolumeSource;
import io.kubernetes.client.openapi.models.V1EnvVar;
import io.kubernetes.client.openapi.models.V1EnvVarSource;
import io.kubernetes.client.openapi.models.V1ExecAction;
import io.kubernetes.client.openapi.models.V1FlexVolumeSource;
import io.kubernetes.client.openapi.models.V1HostAlias;
import io.kubernetes.client.openapi.models.V1HostPathVolumeSource;
import io.kubernetes.client.openapi.models.V1KeyToPath;
import io.kubernetes.client.openapi.models.V1Lifecycle;
import io.kubernetes.client.openapi.models.V1LifecycleHandler;
import io.kubernetes.client.openapi.models.V1ObjectFieldSelector;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodCondition;
import io.kubernetes.client.openapi.models.V1PodDNSConfig;
import io.kubernetes.client.openapi.models.V1PodDNSConfigOption;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1Probe;
import io.kubernetes.client.openapi.models.V1ResourceRequirements;
import io.kubernetes.client.openapi.models.V1SecurityContext;
import io.kubernetes.client.openapi.models.V1Volume;
import io.kubernetes.client.openapi.models.V1VolumeMount;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.hsqldb.lib.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.baidu.bce.logic.bci.servicev2.constant.BciOrderConstant.BCI_ORDER_ITEM_KEY_PREFIX;
import static com.baidu.bce.logic.bci.servicev2.constant.ImagePullPolicyConstant.IMAGE_PULL_POLICY_ALWAYS;
import static com.baidu.bce.logic.bci.servicev2.constant.ImagePullPolicyConstant.IMAGE_PULL_POLICY_IF_NOT_PRESENT;
import static com.baidu.bce.logic.bci.servicev2.constant.ImagePullPolicyConstant.IMAGE_PULL_POLICY_NEVER;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_CONTAINER_PREFIX;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_IMAGE_DOWNLOAD_WORKLOAD_INFIX;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_INJECT_KUBE_PROXY_INIT_CONTAINER_ANNOTATION_NAME;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_INTERNAL_PREFIX;
import static com.baidu.bce.logic.bci.servicev2.pod.ImageCacheServiceV2.ANNOTATION_INIT_NAME;
@Service
public class PodNewOrderExecutorServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodNewOrderExecutorServiceV2.class);

    private static final String BCI_SOURCE = "bci";
    private static final int SIXTY_MINUTES = 600;
    private static final int RETRY_NUM = 3;
    private static final String POD = "POD";
    private static final String ACTION = "create";
    private static final String ORDER_KEY = "bci";

    private static final String ERROR_STATUS = "error";
    private static final String SUCCESS_TATUS = "succ";
    private static final String OPERATING_STATUS = "operating";
    private static final String CALLBACK_ERROR = "callback_error";

    private static final String ORDER_TIME_OUT = "Create Order Timeout";
    private static final String ORDER_CREATING_TIME_OUT = "Check In Creating Status Order: Create Resource spent too "
            + "long.";
    private static final String ORDER_EXCEPTION = "Order Occurred Exception";
    private static final String BCI_NFS_BASE_PATH = "/bci-nfs/";
    private static final String BCI_PFS_BASE_PATH = "/bci-pfs/";
    private static final String BCI_BOS_BASE_PATH = "/bci-bos/";

    private static final String BCI_CEPHFS_BASE_PATH = "/bci-cephfs/";
    private static final String KUBE_PROXY_SIDECAR_LABEL_KEY = "bci.virtual-kubelet.io/kubeproxy-container";
    private static final String KUBE_PROXY_SIDECAR_HEALTHZ_ADDRESS = "bci.virtual-kubelet.io/kubeproxy-healthz-address";
    private static final String SIDECAR_EMPTY_DIR_MOUNT_PATH = "/sidecar/";
    public static final String SIDECAR_COMMAND_MOUNT_PATH = "/sidecar_cmd/";
    public static final String DNS_CONFIG_LABEL_KEY = "vk.bci.baidu.com/dns-config";
    private static final String ANNOTATION_NTP_SERVER = "bci.virtual-kubelet.io/bci-ntp-server";
    public static final String IMAGE_CACHE_POD_DNS_CONFIG_ANNOTATION_KEY = "bci.baidu.com/image-cache-pod-dns-config";
    private static final String HOST_ALIASES = "bci.virtual-kubelet.io/pod-host-aliases";
    private static final String ANNOTATION_SIDECAR_NAME = "bci_internal_sidecarContainer";
    public static final String SIDECAR_COMMAND_CM_NAME = "sidecar-command-cm";
    public static final String SIDECAR_COMMAND_CM_NAME_REF = "sidecar-cm-ref";
    private static final String NFS_CHECK_SHELL_NAME = "nfs-check.sh";
    private static final String NFS_CHECK_SHELL_NAME_DEPRECATED = "nfs_check.sh";

    private static final String CEPHFS_CHECK_SHELL_NAME = "cephfs-check.sh";

    private static final String KUBELET_PROXY_CHECK_SHELL_NAME = "kubelet_proxy_check.sh";
    private static final String PFS_CHECK_SHELL_NAME = "pfs_check.sh";
    private static final String BOS_CHECK_SHELL_NAME = "bos_check.sh";
    private static final String NTP_CHECK_SHELL_NAME = "ntp_check.sh";
    private static final String POST_START_SHELL_NAME = "post.sh";
    private static final String KUBE_PROXY_CHECK_SHELL_NAME = "kubeproxy_check.sh";
    public static final String BLS_CHECK_SHELL_NAME = "bls_check.sh";
    public static final String IMAGE_DOWNLOAD_CHECK_SHELL_NAME = "image_download_check.sh";
    public static final int ENCAPSULATE_POD_WAITTIME = 60;

    private static final String POST_START_HOOK_NAME = "bci.virtual-kubelet.io/container-post-start-hook";
    private static final String PRE_STOP_HOOK_NAME = "bci.virtual-kubelet.io/container-pre-stop-hook";

    private static final String TERMINATION_PARAM = "bci.virtual-kubelet.io/container-termination-param";
    private static final String PODNAMESPACE_NAME = "bci.virtual-kubelet.io/pod-namespace-name";
    private static final Set<String> TERMINATION_MESSAGE_POLICY_SET = new HashSet<>();
    public static final String COREDUMP_PATTERN = "bci.virtual-kubelet.io/core-pattern";

    public static final String COREDUMP_PATTERN_VALUE = "/tmp/cores";
    public static final String CORDUMPE_VOLUME_NAME = BCI_CONTAINER_PREFIX + "coredump";

    private static final String BCI_EXEC_VOLUME_CONTAINER_PATH = "/usr/execbin/";
    private static final String BCI_EXEC_BIN_NAME = "exec";
    private static final String BCI_EXEC_VOLUME_MOUNT_NAME = "exec";
    private static final String BCI_POSTSTART_COMMAND = "sleep 3";
    private static final String WORKLOAD_EMPTY_DIR_MOUNT_PATH = "/workload/";
    private static final String WORKLOAD_EMPTY_DIR_MOUNT_NAME = "workload-emptydir";
    private static final String BCI_PROBE_BIN_NAME = "prob";
    private static final String BCI_PROBE_VOLUME_MOUNT_NAME = "prob";
    private static final String BCI_PROBE_VOLUME_HOST_PATH = "/etc/bci/";
    private static final String BCI_PROBE_VOLUME_CONTAINER_PATH = "/usr/probbin/";
    private static final String BCI_PROBE_ENV_NAME = "ENV_BCI_PROB_PODIP";
    private static final String BCI_SECURITYCONTINER = "securityContainer";

    private static final String BCI_IMAGE_CACHE_BEST_MATCH_CDS_SNAPSHOT_ID 
        = "image.bci.cloud.baidu.com/best-match-cds-snapshot-id";

    private static final String BCI_DNS_CONFIG_NAME_SERVER = "***************";

    private static final String BCI_KUBE_PROXY_INIT_CHECK_COMMAND = "/bin/sh";

    private static final String BCI_KUBE_PROXY_INIT_CHECK_SCRIPT = "/home/<USER>/kube_proxy_init_check.sh";

    public static final String BCI_INIT_CONTAINER_BATCH_DOWNLOAD_IMAGE_COMMAND = "/download_image_parallel";

    public static final String BCI_INIT_CONTAINER_BATCH_DOWNLOAD_IMAGES_ENV_NAME = "DOWNLOAD_IMAGE_LIST";

    public static final String BCI_INIT_CONTAINER_BATCH_DOWNLOAD_IMAGES_CONTAINER_NAME_ANNOTATIONS_NAME =
            ANNOTATION_INIT_NAME + "_batchDownloadImagesContainer";

    private static final String SIMPLE_IPADDRESS_PATTERN = "^((\\d{1,3}\\.){3}\\d{1,3})$";

    private static final String BCI_CONTAINER_LAUNCH_PRIORITY = "BCI_CONTAINER_LAUNCH_PRIORITY";
    private static final String BCI_CONTAINER_EXIT_PRIORITY = "BCI_CONTAINER_EXIT_PRIORITY";
    private static final String BCI_WORKLOAD_CONTAINER_ORDER = "BCI_WORKLOAD_CONTAINER_ORDER";

    private static final float FLOATPRECISION = (float) 0.00001;
    static {
        TERMINATION_MESSAGE_POLICY_SET.add("File");
        TERMINATION_MESSAGE_POLICY_SET.add("FallbackToLogsOnError");
    }

    public static final String ORDER_SYSTEM_CALLBACK = "OrderSystemCallback";
    public static final String BCILOGIC_SELF_ORDER_SYNC_TASK = "BciLogicSelfOrderSyncTask";

    // 调试使用，默认为false
    @Value("${enable.change.sidecar.command:false}")
    private boolean enableChangeSidecarCommandCm;

    // sidecar检查最大周期
    @Value("${sidecar.check.max.times:120}")
    private int sidecarCheckMaxTimes;

    // bci storage sidecar镜像
    @Value("${bci.storage.sidecar.image:}")
    private String storageSidecarImage;

    @Value("${sms.create.fail.tpl.id:Tpl_2ef56822-636c-4c7d-9a0c-919925b5dd8a}")
    private String smsCreateFailId;

    @Value("${sms.bci.create.success.tpl.id:Tpl_acc9ccfc-e3ae-4472-a0e4-001faa6da57b}")
    private String smsCreateSuccessId;

    @Value("${pod.emptyDir.size:10}")
    private int emptyDirSize;

    @Value("${pod.container.size:3}")
    private int containerSize;

    @Value("${eip_createTimeOut:25000}")
    private Integer eipCreateTimeOut;

    // 从下单开始算，创建server超时报警时间，单位：秒
    @Value("${server.create.timeout:300}")
    private Integer serverCreateTimeout;

    @Value("${bci.ephemeral_quota.enable:true}")
    private boolean enableEphemeralQuota;

    @Value("${bci.lxcfs.enable:true}")
    private boolean enableLxcfs;

    @Value("${bci.eni.enable:false}")
    private boolean enableEni;

    @Value("${bci.eip.bind.maxtimes:400}")
    private int eipBindRetryMaxTimes; 

    // 订单超时时间,默认5分钟(会一直卡在check阶段，主要是在等待pending状态的pod转化为running||failed)
    @Value("${bci.order.timeout:5}")
    private int orderTimeoutMinute;

    @Value("${download.image.address}")
    private String downloadImageAddress;

    @Value("${image.initContainer.download.enable:false}")
    private boolean enableImageInitConDownload;

    @Value("${batch.create.success.ratio:0.9}")
    private float batchCreatePodSuccessRatio;

    @Value("${image.accelerate.crd.apiVersion:image.bci.cloud.baidu.com/v1}")
    private String imageAccelerateCrdApiVersion;

    @Value("${image.accelerate.crd.kind:ImageCache}")
    private String imageAccelerateCrdKind;

    @Value("${order.automatic.execute.enable:false}")
    private boolean orderAutomaticExecuteEnable;

    @Value("${bci.pod.resource.scheduled.timeout.minutes:10}")
    private long podScheduledTimeoutMinutes;

    @Value("${bci.dspod.inject.kubeletProxy.enable:true}")
    private boolean injectKubeletProxyForDaemonset;

    @Value("${bci.dspod.sidecar.kubeletProxy.image:registry.baidubce.com/bci-online-public/sidecar/kubelet-proxy:2024102102}")
    private String kubeletProxySidecarImage;

    @Value("${bci.pod.sidecar.storage.image:registry.baidubce.com/bci-dev-public/bci-storage-sidecar:2025010302}")
    private String bciStorageSidecarImage;

    @Value("${bci.dspod.zuoyebang.logpath:/var/log/bciservices/zyb}")
    private String zuoyebangLogPath;

    @Value("${bci.pod.reserve.original.pod.hostname.users:152878f8ea364ddb8de7105ea7d6dd50}")
    private String bciPodReserveOriginalPodHostnameUsers;
    
    @Value("${bci.pod.spec.dnsConfig.nameservers.noAdd.users:9ba0ab01bc4942868385729e1249b307,6c47a952db4444c5a097b41be3f24c94}")
    private String bciPodSpecDnsConfigNameserversNoAddUsers;

    @Value("${bci.initContainers.inject.kubeProxy.enable.users:6c47a952db4444c5a097b41be3f24c94," +
            "2e1be1eb99e946c3a543ec5a4eaa7d39}")
    private String bciInitContainersInjectKubeProxyEnableUsers;

    @Value("${bci.pod.sidecar.ntp.image:registry.baidubce.com/bci-online-public/sidecar/ntp:202502252019}")
    private String bciNTPSidecarImage;

    // 新增：readiness检查的可配置参数
    @Value("${bci.readiness.check.max.retries:20}")
    private int readinessCheckMaxRetries;

    @Value("${bci.readiness.check.retry.interval:3}")
    private int readinessCheckRetryInterval;

    @Value("${bci.readiness.check.initial.wait:5}")
    private int readinessCheckInitialWait;

    @Value("${bci.poststart.readiness.max.retries:12}")
    private int postStartReadinessMaxRetries;

    @Value("${bci.poststart.readiness.retry.interval:2}")
    private int postStartReadinessRetryInterval;

    @Value("${bci.poststart.basic.wait:5}")
    private int postStartBasicWait;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    private ImageAccelerateDaoV2 imageAccelerateDao;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PodLogService podLogService;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private ImageCacheServiceV2 imageCacheServiceV2;

    @Autowired
    private ImageCacheDaoV2 imageCacheDaoV2;

    @Autowired
    private PodExtraDaoV2 podExtraDaoV2;

    @Autowired
    private StateMachine stateMachine;

    @Autowired
    private PrometheusMetricsService prometheusMetricsService;

    /*用户级别订单超时时间的缓存时间,默认5分钟*/
    private static final long ORDER_TIMEOUT_EXPIRATION_TIME = 5 * 60 * 1000L;

    private CacheUtil<String, Integer> cacheUtil = new CacheUtil<>();

    @Data
    class InitImageDownloadImageInstance {
        public String image;
        public String username;
        public String password;
        public String pullPolicy;
    }

    public ExecutionResult execute(OrderClient orderClient, ResourceClient resourceClient,
                                   Order order, String source) {
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        String orderId = order.getUuid();
        // 对该order进行上锁，且状态必须为READY_FOR_CREATE
        if (orderAutomaticExecuteEnable) {
            try {
                if (!orderClient.tryLock(order.getUuid(), OrderStatus.READY_FOR_CREATE, 1)) {
                    LOGGER.error("order execute callback from {}:v2 order execute process failed to lock"
                            + "orderId {} READY_FOR_CREATE when execute",
                            source, orderId);
                    return executionResult;
                }
            } catch (BceInternalResponseException e) {
                LOGGER.error("order execute callback from {}:v2 order execute process failed to lock"
                        + " orderId {} READY_FOR_CREATE when execute:{}",
                        source, orderId, e.toString());
                return executionResult;
            }
            LOGGER.debug("order execute callback from {}:v2 order execute process succeed to lock"
                    + " orderId:{} READY_FOR_CREATE when execute",
                    source, orderId);
        }
        // 根据extra类型，执行订单逻辑。
        boolean isImageCacheReq = checkIsImageCacheRequest(order);
        LOGGER.error("order execute callback from {} isImageCacheReq is {}",
                source, isImageCacheReq);
        if (isImageCacheReq) {
            // 执行镜像缓存逻辑
            executionResult = executeCreateImcWithoutLock(orderClient, resourceClient, order, source);
        } else {
            executionResult = executeWithoutLock(orderClient, resourceClient, order, source);
        }
        if (orderAutomaticExecuteEnable) {
            try {
                orderClient.unlock(orderId);
            } catch (BceInternalResponseException e) {
                LOGGER.debug("order execute callback from {}:v2 order execute process failed to unlock"
                        + " orderId:{} READY_FOR_CREATE when execute {}",
                        source, orderId, e.toString());
                return executionResult;
            }
            LOGGER.debug("order execute callback from {}:v2 order execute process succeed to unlock"
                    + " orderId:{} READY_FOR_CREATE when execute",
                    source, orderId);
        }
        return executionResult;
    }
    // 检查是何种类型的订单
    public boolean checkIsImageCacheRequest(Order order) {
        for (Order.Item item : order.getItems()) {
            BciOrderExtra orderExtra = new BciOrderExtra();
            // TO DO,是否需要订单置为失败，pod/container数据库参数删除。
            try {
                orderExtra = PodUtils.getOrderExtra(item.getExtra());
            }  catch (IOException e) {
                LOGGER.debug("getExtraAccount failed: {}", e);
                return false;
            }   
            if (orderExtra.isImageCacheReq()) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public boolean fillOrderItemExtraWithPodExtra(Order order) {
        for (Order.Item orderItem : order.getItems()) {
            if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType()) &&
                    orderItem.getKey().startsWith(BCI_ORDER_ITEM_KEY_PREFIX)) { // 去除eip的order item
                String orderItemKey = orderItem.getKey();
                if (!StringUtils.isEmpty(orderItemKey)) {
                    String podId = PodUtils.parsePodIdFromOrderItem(orderItem);
                    if (StringUtils.isEmpty(podId)) {
                        continue;
                    }
                    PodExtraPO podExtraPO = podExtraDaoV2.getPodExtraByPodIdIgnoreStatus(podId);
                    if (podExtraPO != null && !StringUtils.isEmpty(podExtraPO.getOrderExtra())) {
                        orderItem.setExtra(podExtraPO.getOrderExtra());
                    } else {
                        LOGGER.warn("podExtraPO is null or empty, podId:{}", podId);
                        return false;
                    }
                }
            }
        }
        return true;
    }

    // 执行创建缓存操作
    public ExecutionResult executeCreateImcWithoutLock(OrderClient orderClient, ResourceClient resourceClient,
                                              Order order, String source) {
        LOGGER.error("enter executeCreateImcWithoutLock");
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        String accountID = "";
        try {
            accountID = getRealAccountID(order);
        } catch (IOException e) {
            LOGGER.debug("getExtraAccount failed: {}", e);
            return executionResult;
        }
        for (Order.Item item : order.getItems()) {
            BciOrderExtra orderExtra = new BciOrderExtra();
            try {
                orderExtra = PodUtils.getOrderExtra(item.getExtra());
            }   catch (IOException e) {
                LOGGER.debug("getExtraAccount failed: {}", e);
                return executionResult;
            }   
            k8sService.createImageAccCRD(accountID, orderExtra.getImageCacheCrdReq());
        }
        updateOrderToCreatingStatus(orderClient, order);
        return executionResult;
    }

    public ExecutionResult executeWithoutLock(OrderClient orderClient, ResourceClient resourceClient,
                                              Order order, String source) {
        String orderId = order.getUuid();
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        // 获取 extra 里的用户accountID
        String accountID = "";
        try {
            accountID = getRealAccountID(order);
        } catch (IOException e) {
            LOGGER.debug("executeWithoutLock getRealAccountID failed orderId:{}, exception:{}", orderId, e);
            return executionResult;
        }
        if (!checkOrderAndPod(orderClient, order, executionResult, accountID)) {
            return executionResult;
        }
        LOGGER.debug("executeWithoutLock order callback execute is called,orderid is:{}", orderId);

        // 0. 订单超时 -> 更新订单
        if (isOrderTimeout(order, accountID)) {
            LOGGER.error("executeWithoutLock order check timeout , order:{}", orderId);
            updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                    ResourceRecycleReason.ORDER_CHECK_TIMEOUT.toString(),
                    "order check timeout");
            return executionResult;
        }

        try {
            List<PodPO> podPOs = podDao.listByOrderId(accountID, orderId);
            if (CollectionUtils.isEmpty(podPOs)) {
                LOGGER.error("executeWithoutLock failed to fetch pod by orderId:{}, accountId:{}", orderId, accountID);
                return executionResult;
            }
        } catch (Exception e) {
            LOGGER.error("executeWithoutLock failed to fetch pod by orderId:{}, ex:{}", orderId, e);
            return executionResult;
        }

        try {
            LOGGER.debug("executeWithoutLock begin to encapsulate pod to create for order {} ", orderId);
            List<V1Pod> pods = encapsulatePodForCreate(order, ACTION);
            LOGGER.debug("executeWithoutLock finish encapsulate pod to create for order {},pod size:{} ",
                    orderId, pods.size());

            // 1.调用k8s批量创建pod
            LOGGER.debug("executeWithoutLock begin to call k8s service to create pod for order {} ", orderId);
            BatchCreatePodsResult result = k8sService.batchCreatePods(pods);
            LOGGER.debug("executeWithoutLock finish call k8s service to create pod for order {} ", orderId);
            // 2.计算成功率
            float successRatio = calculateRatio(result.getSucceedPods().size() +
                    result.getExistPods().size(), pods.size());
            // 3.根据pod创建成功率, 更新订单失败/成功

            // 3.1 处理pod创建失败的情况
            LOGGER.debug("executeWithoutLock begin processExecuteFailedPodsReasons order {} failedPodsReasons size:{}",
                    orderId, result.getFailedPodsReasons().size());
            processExecuteFailedPodsReasons(order, result);
            LOGGER.debug("executeWithoutLock end processExecuteFailedPodsReasons order {} failedPodsReasons size:{}",
                    orderId, result.getFailedPodsReasons().size());

            // 3.2 如果成功率达到阈值, 更新订单状态为CREATING
            if (successRatio >= batchCreatePodSuccessRatio) {
                LOGGER.debug("executeWithoutLock create pods success, order:{}, total:{}, success:{}, failed:{}, " +
                                "rate:{}, rule:{}",
                        orderId, pods.size(), result.getSucceedPods().size() +
                                result.getExistPods().size(), result.getFailedPods().size(),
                        successRatio, batchCreatePodSuccessRatio);
                updateOrderToCreatingStatus(orderClient, order);
            } else {
                // 3.3 如果成功率未达到阈值, 更新订单状态为CREATE_FAILED
                LOGGER.debug("executeWithoutLock create pods failed, order:{}, total:{}, success:{}, failed:{}, " +
                                "rate:{}, rule:{}",
                        orderId, pods.size(), result.getSucceedPods().size() +
                                result.getExistPods().size(), result.getFailedPods().size(),
                        successRatio, batchCreatePodSuccessRatio);
                updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                        ResourceRecycleReason.ORDER_EXECUTE_CREATE_POD_FAIL.toString(),
                        "order success ratio not to up standard");
            }
        } catch (Exception e) {
            LOGGER.error("executeWithoutLock create pods failed, order is:{}, exception is:{}", orderId, e);
            updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                    ResourceRecycleReason.ORDER_EXECUTE_EXCEPTION.toString(),
                    e.toString());
        }
        return executionResult;
    }

    private void processExecuteFailedPodsReasons(Order order, BatchCreatePodsResult result) {
        if (result.getFailedPodsReasons() != null && result.getFailedPodsReasons().size() > 0) {
            for (Map.Entry<V1Pod, String> entry : result.getFailedPodsReasons().entrySet()) {
                String podId = entry.getKey().getMetadata().getName();
                String responseBody = entry.getValue();
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    if (jsonNode.has("message")) {
                        String message = jsonNode.get("message").asText();
                        Pattern pattern = Pattern.compile("denied the request: (.*)");
                        Matcher matcher = pattern.matcher(message);
                        if (matcher.find()) {
                            String jsonErrorMessage = matcher.group(1);
                            if (JsonUtil.isJson(jsonErrorMessage)) {
                                LOGGER.debug("begin processCreatePodFailedWithReason podId:{}, jsonErrorMessage:{}",
                                        podId, jsonErrorMessage);
                                boolean ret = processCreatePodFailedWithReason(entry.getKey(), jsonErrorMessage);
                                LOGGER.debug("end processCreatePodFailedWithReason podId:{}, jsonErrorMessage:{}, " +
                                                "ret:{}", podId, jsonErrorMessage, ret);
                            }
                        }
                    }
                } catch (IOException e) {
                    LOGGER.error("failed to parse pod create failed reason, podId:{}, responseBody:{}, exception:{}",
                            podId, responseBody, e);
                }
            }
        }
    }

    private boolean processCreatePodFailedWithReason(V1Pod pod, String jsonErrorMessage) {
        String podId = pod.getMetadata().getName();
        String userId = pod.getMetadata().getNamespace();
        Map<String, String> mapErrorMessage = JsonUtil.toMap(jsonErrorMessage, String.class);
        if (mapErrorMessage.containsKey("reason")
                && "ZoneNoResourceSpecification".equalsIgnoreCase(mapErrorMessage.get("reason"))) {
            LOGGER.error("pod create failed, podId:{},jsonErrorMessage:{}",
                    podId, jsonErrorMessage);
            List<PodCondition> podConditions = new ArrayList<>();
            Date nowDate = new Date();
            podConditions.add(new PodCondition(nowDate,"False", "Initialized"));
            podConditions.add(new PodCondition(nowDate,"False", "Ready"));
            podConditions.add(new PodCondition(nowDate,"False", "ContainersReady"));
            podConditions.add(new PodCondition(nowDate,"False", "PodScheduled"));

            ContainerCurrentState containerCurrentState = new ContainerCurrentState();
            containerCurrentState.setState("Creating");
            String detailStatus = mapErrorMessage.get("reason") + ";;" + mapErrorMessage.get("message");
            containerCurrentState.setDetailStatus(detailStatus);
            // set reason
            boolean setSuccess = commonUtils.saveCreatePodFailedWithReason(userId, podId, podConditions, containerCurrentState);
            if (!setSuccess) {
                return setSuccess;
            }

            // trans this pod  to failed, and mark pod recycle 
            return processK8SPodNoResourceAvailable(userId, podId, ResourceRecycleReason.ZONE_NO_RESOURCE_SPECIFICATION.toString());


        }
        return true;
    }

    public ExecutionResult check(OrderClient orderClient, ResourceClient resourceClient,
                                 Order order, String source) {
        String orderId = order.getUuid();
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        if (orderAutomaticExecuteEnable) {
            // 对该order进行上锁，且状态必须为CREATING
            try {
                if (!orderClient.tryLock(orderId, OrderStatus.CREATING, 1)) {
                    LOGGER.error("order check callback from {}:v2 order check process failed to lock"
                            + " orderId:{} CREATING when check",
                            source, orderId);
                    return executionResult;
                }
            } catch (BceInternalResponseException e) {
                LOGGER.error("order check callback from {}:v2 order check process failed to lock"
                        + " orderId:{} CREATING when check:{}",
                        source, orderId, e.toString());
                return executionResult;
            }
            LOGGER.debug("order check callback from {}:v2 order check process succeed to lock"
                            + " orderId:{} CREATING when check",
                    source, orderId);
        }
        boolean isImageCacheReq = checkIsImageCacheRequest(order);
        if (isImageCacheReq) {
             executionResult = checkImcCreateWithoutLock(orderClient, resourceClient, order, source);
        } else {
            executionResult = checkWithoutLock(orderClient, resourceClient, order, source);
        }
        if (orderAutomaticExecuteEnable) {
            try {
                orderClient.unlock(orderId);
            } catch (BceInternalResponseException e) {
                LOGGER.debug("order check callback from {}:v2 order check process failed to unlock" +
                        " orderId:{} CREATING when check:{}",
                        source, orderId, e.toString());
                return executionResult;
            }
            LOGGER.debug("order check callback from {}:v2 order check process succeed to unlock"
                            + "orderId:{} CREATING when check",
                    source, orderId);
        }
        return executionResult;
    }

    public ExecutionResult checkImcCreateWithoutLock(OrderClient orderClient, ResourceClient resourceClient,
                                            Order order, String source) {
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        // 获取 extra 里的用户accountID
        String accountID = "";
        try {
            accountID = getRealAccountID(order);
        } catch (IOException e) {
            LOGGER.debug("getExtraAccount failed: {}", e);
            return executionResult;
        }
        if (!checkOrderAndPod(orderClient, order, executionResult, accountID)) {
            return executionResult;
        }
        LOGGER.debug("performance test:servicev2 order callback check is called,orderid is:{}", order.getUuid());
        List<PodPO> pods = null;
        try {
            pods = podDao.listByOrderId(accountID, order.getUuid());
            if (CollectionUtils.isEmpty(pods)) {
                LOGGER.error("failed to fetch pod by orderId:{}, accountId:{}", order.getUuid(), accountID);
                return executionResult;
            }
        } catch (Exception e) {
            LOGGER.error("failed to fetch pod by orderId:{}, ex:{}", order.getUuid(), e);
            return executionResult;
        }

        LOGGER.debug("check pod, {}", pods.get(0).getPodId());
        // 1.获取ns->podname
        ArrayList<Pair<String, String>> nsPodList = getNameSpaceAndPodName(pods);
        // 2.批量获取pod信息
        HashMap<Pair<String, String>, V1Pod> nsV1PodsMap = k8sService.batchGetPods(nsPodList);
        // 3.计算成功和失败数量: running状态为success, failed||不存在为failed
        int succeedNum = 0;
        int failedNum = 0;
        for (Map.Entry<Pair<String, String>, V1Pod> entry : nsV1PodsMap.entrySet()) {
            // 非批量订单：正常情况下getPod不会为null，若为null，则表明为资源控制面在迁移pod(删除后，再创建), 不能算失败(按照pending算)
            // 批量订单：TODO, 若在execute存在大量创建失败，此时会导致check卡到订单超时
            if (entry.getValue() == null) {
                LOGGER.debug("k8s not find pod when check, pod:{}", entry.getKey().getSecond());
                continue;
            }
            String status =
                    entry.getValue().getStatus().getPhase() != null ? entry.getValue().getStatus().getPhase() : "";
            LOGGER.debug("k8s find pod when check, pod:{}, status:{}",
                    entry.getKey().getSecond(), status);

            if (entry.getValue() == null) {
                failedNum++;
            } else if (BciStatus.RUNNING.getStatus().equalsIgnoreCase((entry.getValue().getStatus().getPhase()))
                    || BciStatus.SUCCEEDED.getStatus().equalsIgnoreCase((entry.getValue().getStatus().getPhase()))
                    || BciStatus.FAILED.getStatus().equalsIgnoreCase((entry.getValue().getStatus().getPhase()))) {
                succeedNum++;
            }
        }
        // 4.失败率达标->订单失败
        if (failedNum > 0) {
            LOGGER.error("check pods failed, order:{}, total:{}, success:{}, failed:{}, rule:{}",
                    order.getUuid(), pods.size(), succeedNum, failedNum, batchCreatePodSuccessRatio);
            updateOrderToFailedStatus(accountID, orderClient, order, executionResult, "[POD Backend]" + ORDER_EXCEPTION,
                    "ratio not to up standard");
            return executionResult;
        }
           // 5.全部成功||超时->更新订单
        if (succeedNum == pods.size() || isOrderTimeout(order, accountID)) {
            // 5.1 成功率达标->订单成功;
            if (succeedNum == pods.size()) {
                updateOrderFlavorByPodPhysicalZone(order, pods, nsV1PodsMap);
                createRelatedObjectAndUpdateOrderToCreated(accountID, orderClient, order,
                        pods, nsV1PodsMap, executionResult);
                updatePodUuid(accountID, pods, nsV1PodsMap);
                LOGGER.debug("performance test:after update order {} to created", order.getUuid());
                return executionResult;
                // 5.2 成功率不达标->订单失败
            } else {
                LOGGER.error("check pods failed, order:{}, total:{}, success:{}, failed:{}, rule:{}",
                        order.getUuid(), pods.size(), succeedNum, failedNum, batchCreatePodSuccessRatio);
                updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                        "[POD Backend] " + ORDER_EXCEPTION,
                        "ratio not to up standard");
                return executionResult;
            }
        }
        // 6.继续等待下一轮check
        return executionResult;
    }

    public ExecutionResult checkWithoutLock(OrderClient orderClient, ResourceClient resourceClient,
                                            Order order, String source) {
        String orderId = order.getUuid();
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        // 获取 extra 里的用户accountID
        String accountID = "";
        try {
            accountID = getRealAccountID(order);
        } catch (IOException e) {
            LOGGER.debug("checkWithoutLock getRealAccountID failed orderId:{}, exception:{}", orderId, e);
            return executionResult;
        }
        if (!checkOrderAndPod(orderClient, order, executionResult, accountID)) {
            return executionResult;
        }
        LOGGER.debug("checkWithoutLock order callback check is called,orderid is:{}, source:{}",
                orderId, source);

        // 0. 订单超时 -> 更新订单
        if (isOrderTimeout(order, accountID)) {
            LOGGER.error("checkWithoutLock order check timeout , order:{}", orderId);
            updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                    ResourceRecycleReason.ORDER_CHECK_TIMEOUT.toString(),
                    "order check timeout");
            return executionResult;
        }

        List<PodPO> pods = null;
        try {
            pods = podDao.listByOrderId(accountID, orderId);
            if (CollectionUtils.isEmpty(pods)) {
                LOGGER.error("checkWithoutLock failed to fetch pod by orderId:{}, accountId:{}", orderId, accountID);
                return executionResult;
            }
        } catch (Exception e) {
            LOGGER.error("checkWithoutLock failed to fetch pod by orderId:{}, ex:{}", orderId, e);
            return executionResult;
        }
        String podId = pods.get(0).getPodId();
        LOGGER.debug("checkWithoutLock orderId:{}, check pod, {}", orderId,  podId);
        // 1.获取ns->podname
        ArrayList<Pair<String, String>> nsPodList = getNameSpaceAndPodName(pods);
        // 2.批量获取pod信息
        HashMap<Pair<String, String>, V1Pod> nsV1PodsMap = k8sService.batchGetPods(nsPodList);
        // 3.计算成功和失败数量: running状态为success, failed||不存在为failed
        int succeedNum = 0;
        int failedNum = 0;
        EniClient eniClient = logicPodClientFactory.createEniClient(accountID);
        LogicEipClient eipClient = logicPodClientFactory.createLogicEipClient(accountID);
        for (Map.Entry<Pair<String, String>, V1Pod> entry : nsV1PodsMap.entrySet()) {
            // 非批量订单：正常情况下getPod不会为null，若为null，则表明为资源控制面在迁移pod(删除后，再创建), 不能算失败(按照pending算)
            // 批量订单：TODO, 若在execute存在大量创建失败，此时会导致check卡到订单超时
            if (entry.getValue() == null) {
                LOGGER.debug("checkWithoutLock k8s not find pod when check, orderId:{}, pod:{}",
                            orderId, entry.getKey().getSecond());
                continue;
            }
            String status =
                    entry.getValue().getStatus().getPhase() != null ? entry.getValue().getStatus().getPhase() : "";
            LOGGER.debug("checkWithoutLock k8s find pod when check, orderId:{} pod:{}, status:{}",
                    orderId, entry.getKey().getSecond(), status);

            
            // eip state: 0 binding, 1 ok, -1 fail
            int eipState = 1;
            if (entry.getValue() != null && (entry.getValue().getMetadata().getAnnotations() != null)
                    && StringUtils.isNotEmpty(entry.getValue().getMetadata()
                    .getAnnotations().get("cross-vpc-eni.cce.io/eipPurchase")) &&
                    StringUtils.isNotEmpty(entry.getValue().getStatus().getPodIP())) {
                // SUCCEED和FAILED的pod不检查eip。因为eip已经释放了
                if (!BciStatus.SUCCEEDED.getStatus().equalsIgnoreCase(entry.getValue().getStatus().getPhase())
                    && !BciStatus.FAILED.getStatus().equalsIgnoreCase((entry.getValue().getStatus().getPhase()))) {
                    eipState = checkEip(accountID, entry.getValue(), eniClient, eipClient);
                }
            }

            if (entry.getValue() == null || (eipState == -1)) {
                failedNum++;
            } else if ((BciStatus.RUNNING.getStatus().equalsIgnoreCase((entry.getValue().getStatus().getPhase()))
                    || BciStatus.SUCCEEDED.getStatus().equalsIgnoreCase((entry.getValue().getStatus().getPhase()))
                    ||
                    // FAILED状态也算
                    BciStatus.FAILED.getStatus().equalsIgnoreCase((entry.getValue().getStatus().getPhase())))
                && (eipState == 1)) {
                succeedNum++;
            }

            // process long time pending pod 
            boolean podResourceAvailable = checkPodResourceAvailable(entry.getValue());
            if (!podResourceAvailable) {
                LOGGER.debug("checkWithoutLock orderId:{}, check podResourceAvailable , {}", orderId,  podId,
                        podResourceAvailable);
                failedNum++;
                processK8SPodNoResourceAvailable(accountID,  podId, ResourceRecycleReason.POD_NO_RESOURCE_AVAILABLE_LONG_TIME.toString());
            }
           
        }
        // 4.失败率达标->订单失败
        float failedRatio = calculateRatio(failedNum, pods.size());
        if (failedRatio >= (1 - batchCreatePodSuccessRatio)) {
            LOGGER.error("checkWithoutLock check pods failed, order:{}, total:{}, success:{}, failed:{}, failed " +
                            "rate:{}, rule:{}",
                    orderId, pods.size(), succeedNum, failedNum,
                    failedRatio, batchCreatePodSuccessRatio);
            updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                    ResourceRecycleReason.ORDER_CHECK_CREATE_POD_FAIL.toString(),
                    "order check ratio not to up standard");
            return executionResult;
        }
        // 5.全部成功 -> 更新订单
        if (succeedNum == pods.size()) {
            // 5.1 成功率达标->订单成功;
            updateOrderFlavorByPodPhysicalZone(order, pods, nsV1PodsMap);
            boolean updateOrderResult = createRelatedObjectAndUpdateOrderToCreated(
                    accountID, orderClient, order, pods, nsV1PodsMap, executionResult);
            updatePodUuid(accountID, pods, nsV1PodsMap);
            LOGGER.debug("checkWithoutLock after update order {} to created updateOrderResult {}",
                    orderId, updateOrderResult);
            return executionResult;
        }
        // 6.继续等待下一轮check
        return executionResult;
    }

    private void updatePodUuid(String accountID, List<PodPO> pods, HashMap<Pair<String, String>, V1Pod> nsV1PodsMap) {
        for (PodPO pod : pods) {
            V1Pod k8sPod = nsV1PodsMap.get(new Pair<>(pod.getUserId(), pod.getPodId()));
            if (k8sPod != null && k8sPod.getMetadata() != null) {
                pod.setPodUuid(k8sPod.getMetadata().getUid());
                podDao.updatePodUuid(pod);
            }
        }
    }

    public boolean checkOrderAndPod(OrderClient orderClient, Order order, ExecutionResult executionResult,
                                    String accountID) {
        // 订单不存在:
        //    1.创建订单成功后还未来得及写入DB(drds), 导致查不到orderId.
        //    2.创建订单成功后写入drds, 但drds存在主从延迟，查从库导致查不到orderId.
        //    3.创建订单成功后写入drds异常, 导致查不到orderId.
        //    4.一些异常脏数据订单
        // 处理: 达到延迟时间后将订单置为失败.
        if (checkOrderInDB(order.getUuid())) {
            // order不在db中
            if (isOrderTimeout2(order)) {
                LOGGER.error("order {} is not find in bci2, update order status to created_failed.", order.getUuid());
                updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                        ResourceRecycleReason.ORDER_NOT_FOUND_IN_BCI_DB.toString(),
                        "order is not find in bci2");
            } else {
                LOGGER.error("order {} is not find in bci2, need retry", order.getUuid());
            }
            return false;
        }

        // 订单存在，但是订单对应的pod被全部删除:
        //    1.创建并快速删除场景下, pod被主动删除.
        //    2.异常场景下pod被删除.
        if (isOrderPodDeleted(order)) {
            LOGGER.error("order {} has no available pods in bci2, update order status to created_failed.",
                    order.getUuid());
            updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                    ResourceRecycleReason.ORDER_HAS_NOT_POD.toString(),
                    "The order is empty because has no available pods.");
            return false;
        }
        return true;
    }

    // 检查orderId是否在数据库中
    public boolean checkOrderInDB(String orderId) {
        // db中不存在orderid对应的pod(忽略pod状态)
        return podDao.getPodCountByOrderidIgnoreStatus(orderId) == 0;
    }

    public boolean isOrderPodDeleted(Order order) {
        // db中不存在orderid对应的pod(去除被删除的pod)
        return podDao.getPodCountByOrderId(order.getUuid()) == 0;
    }

    private boolean isOrderTimeout2(Order order) {
        Date baseTime = order.getCreateTime();
        Calendar retryEndTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        retryEndTime.setTime(baseTime);
        retryEndTime.add(Calendar.MINUTE, 1);
        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        return now.after(retryEndTime);
    }

    private ArrayList<Pair<String, String>> getNameSpaceAndPodName(List<PodPO> pods) {
        ArrayList<Pair<String, String>> nsPods = new ArrayList<>();
        for (PodPO pod : pods) {
            nsPods.add(new Pair<>(pod.getUserId(), pod.getPodId()));
        }
        return nsPods;
    }

    private float calculateRatio(int part, int total) {
        // 向下取整
        return (float) (Math.floor(part * 100 / total) / 100.0);
    }

    private Integer getOrderTimeoutMinute(Order order, String accountId) {
        try {
            // get order timeout minute from bci order extra
            for (Order.Item orderItem : order.getItems()) {
                if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) { // 去除eip的order item
                    BciOrderExtra orderExtra = PodUtils.getOrderExtra(orderItem.getExtra());
                    Integer maxPendingMinute = orderExtra.getMaxPendingMinute();
                    if (Objects.equals(maxPendingMinute, 0)) {
                        maxPendingMinute = getOrderTimeoutMinuteFromBciQuota(accountId);
                    }
                    LOGGER.debug("getOrderTimeoutMinute from bciOrderExtra succeed, accountId:{}, " +
                                    "maxPendingMinute:{}", accountId, maxPendingMinute);
                    return maxPendingMinute;
                }
            }
        } catch (Exception e) {
            LOGGER.error("getOrderTimeoutMinute from bciOrderExtra failed, accountId:{}, exception:{}", accountId, e);
            return getOrderTimeoutMinuteFromBciQuota(accountId);
        }
        return getOrderTimeoutMinuteFromBciQuota(accountId);
    }

    private Integer getOrderTimeoutMinuteFromBciQuota(String accountId) {
        Integer timeout = cacheUtil.get(accountId);
        if (timeout != null) {
            LOGGER.info("getOrderTimeoutMinute from logicalQuotaService cache accountId:{} timeout:{}",
                    accountId, timeout);
            return timeout;
        }
        timeout = podService.getBciQuota(accountId, QuotaKeys.ORDER_TIMEOUT_MINUTE, this.orderTimeoutMinute);
        cacheUtil.put(accountId, timeout, ORDER_TIMEOUT_EXPIRATION_TIME);
        LOGGER.info("getOrderTimeoutMinute from logicalQuotaService remote accountId:{} timeout:{}",
                accountId, timeout);
        return timeout;
    }

    /**
     * 判断是否超时
     *
     * @param order
     * @return
     */
    private boolean isOrderTimeout(Order order, String accountId) {
        Date baseTime = order.getUpdateTime();
        Calendar retryEndTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        retryEndTime.setTime(baseTime);
        Integer orderTimeoutMinute = getOrderTimeoutMinute(order, accountId);
        LOGGER.info("isOrderTimeout, accountId:{} orderId:{} orderTimeoutMinute:{}",
                accountId, order.getUuid(), orderTimeoutMinute);
        retryEndTime.add(Calendar.MINUTE, orderTimeoutMinute.intValue());
        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        return now.after(retryEndTime);
    }

    // 获取真实 accountID, 对于转移计费，order里的accountID是资源账号ID，用户ID需要从订单extra信息里获取
    private String getRealAccountID(Order order) throws IOException {
        String accountID = order.getAccountId();
        for (Order.Item orderItem : order.getItems()) {
            if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) {
                String extra = orderItem.getExtra();
                BciOrderExtra orderExtra = PodUtils.getOrderExtra(extra);
                if (StringUtils.isNotEmpty(orderExtra.getExtraAccountID())) {
                    accountID = orderExtra.getExtraAccountID();
                    LOGGER.debug("getExtraAccountID : {}", accountID);
                    break;
                }
            }
        }

        return accountID;
    }

    private List<V1EnvVar> parseEnvs(List<Environment> environments) {
        if (CollectionUtils.isEmpty(environments)) {
            return null;
        }
        List<V1EnvVar> envs = new ArrayList<>();
        for (Environment environment : environments) {
            V1EnvVar env = new V1EnvVar();
            env.setName(environment.getKey());
            env.setValue(environment.getValue());
            if (environment.getValueFrom() != null) {
                env.setValueFrom(environment.getValueFrom());
            }
            envs.add(env);
        }
        return envs;
    }

    private List<V1ContainerPort> parsePorts(List<Port> ports) {
        if (CollectionUtils.isEmpty(ports)) {
            return null;
        }
        List<V1ContainerPort> podPorts = new ArrayList<>();
        for (Port port : ports) {
            V1ContainerPort podPort = new V1ContainerPort();
            podPort.setContainerPort(port.getPort());
            // podPort.setHostPort(port.getPort());
            podPort.setProtocol(port.getProtocol());
            podPort.setName(port.getName());
            podPorts.add(podPort);
        }
        return podPorts;
    }

    public List<V1Container> geneImageContainers(BciOrderExtra orderExtra,
                                                 List<V1Container> containers, ContainerType containerType,
                                                 boolean imageDownloadInSidecar,
                                                 List<V1Container> applyImageAccelerateCons,
                                                 Map<String, String> dsContainer2ImageContainer,
                                                 Map<String, String> imageInitConAnnotations) {
        List<ImageRegistrySecret> imageRegistrySecrets =  orderExtra.getImageRegistrySecrets();
        boolean isV3 = orderExtra.isV3();
        LOGGER.debug("[init download image] enter geneImageContainers, containers is {}, imageRegistrySecrets " +
                        "is {}, containerType {}, applyImageAccelerateCons is {}", containers, imageRegistrySecrets,
                        containerType, applyImageAccelerateCons);
        
       
        List<V1Container> imageInitContainers = new ArrayList<>();
        if (containers.size() == 0) {
            return imageInitContainers;
        }
        for (int i = 0; i < containers.size(); i++) {
            V1Container originContainer = containers.get(i);
            if (applyImageAccelerateCons.contains(originContainer)) {
                continue;
            }
            if (originContainer.getImagePullPolicy() != null && !originContainer.getImagePullPolicy().equals("Never")) {
                // 需要构造imageInitContainer
                V1Container imageInitContainer = new V1Container();
                String imageContainerName;
                if (ContainerType.DS_WORKLOAD.getType().equals(containerType.getType())) {
                    // 如果指定了镜像下载容器的名字，就以指定的为准，否则生成默认的
                    imageContainerName = BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX +
                            PodConstants.BCI_IMAGE_DOWNLOAD_DS_WORKLOAD_INFIX + i;
                    if (dsContainer2ImageContainer != null) {
                        imageContainerName = dsContainer2ImageContainer.get(originContainer.getName());
                    }
                } else if (ContainerType.INIT.getType().equals(containerType.getType())) {
                    imageContainerName = BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX +
                            PodConstants.BCI_IMAGE_DOWNLOAD_INIT_INFIX + i;
                } else {
                    imageContainerName = BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX +
                            BCI_IMAGE_DOWNLOAD_WORKLOAD_INFIX + i;
                }
                imageInitContainer.setName(imageContainerName);
                imageInitConAnnotations.put(imageContainerName, originContainer.getName());
                imageInitContainer.setImage(downloadImageAddress);
                imageInitContainer.setImagePullPolicy(IMAGE_PULL_POLICY_ALWAYS);
                imageInitContainer.addCommandItem("/download_image.sh");
                imageInitContainer.addCommandItem(originContainer.getImage());
                ImageRegistrySecret imageRegistrySecret = getSatisRegistrySecret(imageRegistrySecrets,
                        originContainer.getImage());
                if (imageRegistrySecret == null) {
                    imageInitContainer.addCommandItem("none");
                    imageInitContainer.addCommandItem("none");
                } else {
                    imageInitContainer.addCommandItem(imageRegistrySecret.getUserName());
                    imageInitContainer.addCommandItem(imageRegistrySecret.getPassword());
                }
                imageInitContainer.addCommandItem(originContainer.getImagePullPolicy());
                imageInitContainer.addCommandItem("> /dev/termination-log");
                LOGGER.debug("[init download image] command is:{}", imageInitContainer.getCommand());
                imageInitContainer.setVolumeMounts(new ArrayList<>());
                // 安全容器下载第三方镜像时，不依赖 /var/run/containerd/
                if (!isV3) {
                    V1VolumeMount imageInitVolumeMount = new V1VolumeMount();
                    imageInitVolumeMount.setMountPath("/var/run/containerd/");
                    imageInitVolumeMount.setName("containerd-socket");
                    imageInitContainer.getVolumeMounts().add(imageInitVolumeMount);
                }
                originContainer.setImagePullPolicy(IMAGE_PULL_POLICY_NEVER);
                imageInitContainers.add(imageInitContainer);
                V1SecurityContext v1SecurityContext = new V1SecurityContext();
                v1SecurityContext.setRunAsGroup(0L);
                v1SecurityContext.setRunAsUser(0L);
                v1SecurityContext.runAsNonRoot(false);
                imageInitContainer.securityContext(v1SecurityContext);

                // set reource 
                V1ResourceRequirements resourceRequirements = new V1ResourceRequirements();
                Map<String, Quantity> requests = new HashMap<>();
                Map<String, Quantity> limits = new HashMap<>();
                limits.put("cpu", Quantity.fromString(Float.toString(orderExtra.getPodCpu())));
                limits.put("memory", Quantity.fromString(Float.toString(orderExtra.getPodMem()) + "Gi"));
                requests.put("cpu", Quantity.fromString(Float.toString(orderExtra.getPodCpu())));
                requests.put("memory", Quantity.fromString(Float.toString(orderExtra.getPodMem()) + "Gi"));
                resourceRequirements.setRequests(requests);
                resourceRequirements.setLimits(limits);
                imageInitContainer.setResources(resourceRequirements);

                List<V1EnvVar> envs = new ArrayList<V1EnvVar>();
                V1EnvVar originContainerImageEnv = new V1EnvVar();
                originContainerImageEnv.setName(PodConstants.ORIGIN_CONTAINER_IMAGE_NAME);
                originContainerImageEnv.setValue(originContainer.getImage());
                envs.add(originContainerImageEnv);
                if (isV3) {
                    V1VolumeMount imageInitVolumeMount2 = new V1VolumeMount();
                    imageInitVolumeMount2.setMountPath("/home/<USER>/kata-data");
                    imageInitVolumeMount2.setName("katadata");
                    imageInitContainer.getVolumeMounts().add(imageInitVolumeMount2);
                    V1EnvVar env = new V1EnvVar();
                    env.setName("RUNTIME");
                    env.setValue("kata");
                    envs.add(env);
                    V1EnvVar env2 = new V1EnvVar();
                    env2.setName("HOST_PATH");
                    env2.setValue("/home/<USER>/kata-data");
                    envs.add(env2);
                    V1EnvVar env3 = new V1EnvVar();
                    env3.setName("CONTAINER_NAME");
                    env3.setValue(originContainer.getName());
                    envs.add(env3);
                    V1EnvVar env4 = new V1EnvVar();
                    env4.setName("POD_UID");
                    env4.valueFrom(new V1EnvVarSource().fieldRef(
                        new V1ObjectFieldSelector().fieldPath("metadata.uid")));
                    envs.add(env4); 
                }
                imageInitContainer.setEnv(envs);

                if (imageDownloadInSidecar) {
                    if (imageInitContainer.getEnv() == null) {
                        imageInitContainer.setEnv(new ArrayList<V1EnvVar>());
                    }
                    V1EnvVar env = new V1EnvVar();
                    env.setName("IS_DS_CONTAINER");
                    env.setValue("yes");
                    imageInitContainer.getEnv().add(env);
                }
                LOGGER.debug("[init download image] success set imageInitContainer, name is {},  info is {}, " +
                                "originContainer name is {}, info is {}", imageInitContainer.getName(),
                        JsonUtil.toJSON(imageInitContainer), originContainer.getName(),
                        JsonUtil.toJSON(originContainer));
            }
        }
        return imageInitContainers;

    }

    public List<V1Container> geneImageInitContainers(BciOrderExtra orderExtra,
                                                     List<V1Container> initContainers, 
                                                     List<V1Container> workloadContainers, 
                                                     List<V1Container> dsContainers,
                                                     List<V1Container> applyImageAccelerateCons,
                                                     Map<String, String> imageInitConAnnotations
                                                     ) {
        LOGGER.debug("[init download image] enter geneImageInitContainers, initContainers nums is {}, " +
                     "workloadContainers nums is {}, dsContainers nums is {}", initContainers.size(), 
                     workloadContainers.size(), dsContainers.size());
        if (initContainers.size() == 0 && workloadContainers.size() == 0 && dsContainers.size() == 0) {
            return new ArrayList<V1Container>();
        }
        List<V1Container> imageInitContainersFromInit = geneImageContainers(orderExtra, initContainers,
            ContainerType.INIT, false, applyImageAccelerateCons, null, imageInitConAnnotations);
        List<V1Container> imageInitContainersFromCon = geneImageContainers(orderExtra, workloadContainers,
            ContainerType.WORKLOAD, false, applyImageAccelerateCons, null, imageInitConAnnotations);
        List<V1Container> imageInitContainersFromDsCon = geneImageContainers(orderExtra,  dsContainers,
            ContainerType.DS_WORKLOAD, false, applyImageAccelerateCons, null, imageInitConAnnotations);
        List<V1Container> imageInitContainers = new ArrayList<>();
        imageInitContainers.addAll(imageInitContainersFromInit);
        imageInitContainers.addAll(imageInitContainersFromCon);
        imageInitContainers.addAll(imageInitContainersFromDsCon);
        LOGGER.debug("[init download image] end geneImageInitContainers, imageInitContainersFromInit nums is {},  " +
                     "imageInitContainersFromCon nums is {}, imageInitContainersFromDsCon nums is {}, " + 
                     "All imageInitContainers is {}",
                     imageInitContainersFromInit.size(), imageInitContainersFromCon.size(), 
                     imageInitContainersFromDsCon.size(), imageInitContainers.size());
        return imageInitContainers;
    }

    // 判断是否需要镜像凭据
    private ImageRegistrySecret getSatisRegistrySecret(List<ImageRegistrySecret> imageRegistrySecrets, String image) {
        if (CollectionUtils.isNotEmpty(imageRegistrySecrets)) {
            for (ImageRegistrySecret imageRegistrySecret : imageRegistrySecrets) {
                if (image != null && isAddressEquals(imageRegistrySecret.getServer(),
                        image.substring(0, image.lastIndexOf(":")))) {
                    return imageRegistrySecret;
                }
            }
        }
        return null;
    }

    private Boolean isAddressEquals(String repository, String address) {
        if (repository.endsWith("/")) {
            repository = repository.substring(0, repository.lastIndexOf("/"));
        }

        String addressWithSchema = repository.replace("http://", "").replace("https://", "");
        if (address.contains("/")) {
            return addressWithSchema.equals(address.substring(0, address.lastIndexOf("/")));
        } else {
            return addressWithSchema.equals(address);
        }

    }

    public static String getPriority(ContainerPurchase containerPurchase, String sortKey) {
        if (containerPurchase == null || containerPurchase.getEnvs() == null) {
            return "0"; // 默认值
        }

        return containerPurchase.getEnvs().stream()
                .filter(envVar -> sortKey.equals(envVar.getKey()))
                .map(Environment::getValue)
                .findFirst()
                .orElse("0"); // 没有设置则返回默认值
    }


    public static List<ContainerPurchase> sortByPriority(List<ContainerPurchase> containerPurchases, String sortKey) {
        return containerPurchases.stream()
                .sorted((c1, c2) -> {
                    int p1 = Integer.parseInt(getPriority(c1, sortKey));
                    int p2 = Integer.parseInt(getPriority(c2, sortKey));
                    return Integer.compare(p2, p1); // 降序：值越大越靠前
                })
                .collect(Collectors.toList());
    }

    public List<V1Container> geneContainers(List<ContainerPurchase> containerPurchases, ContainerType containerType, 
                                            List<V1Volume> hostPathVs, boolean isV3) throws IOException {
        List<V1Container> containers = new ArrayList<>();
        List<ContainerPurchase> sortedContainerPurchases = containerPurchases;
        List<ContainerPurchase> exitSortedContainerPurchases = new ArrayList<>();

        // 根据容器启动优先级对容器进行排序，且只对工作负载容器进行排序, 若容器设置有启动优先级环境变量，则需按优先级排序，
        // 即优先级高的容器排在前面，优先级低的容器排在后面
        if (containerType == ContainerType.WORKLOAD) {
            sortedContainerPurchases = sortByPriority(containerPurchases, BCI_CONTAINER_LAUNCH_PRIORITY);
            exitSortedContainerPurchases = sortByPriority(containerPurchases, BCI_CONTAINER_EXIT_PRIORITY);
        }

        for (ContainerPurchase containerPurchase : sortedContainerPurchases) {
            // 容器类型不匹配，直接返回
            if (!StringUtils.equals(containerType.getType(), containerPurchase.getContainerType())) {
                continue;
            }

            V1Container container = new V1Container();
            container.setName(containerPurchase.getName());

            container.setArgs(containerPurchase.getArgs());
            container.setCommand(containerPurchase.getCommands());

            final V1ResourceRequirements resourceRequirements = new V1ResourceRequirements();
            final Map<String, Quantity> requests = new HashMap<>();
            final Map<String, Quantity> limits = new HashMap<>();
            limits.put("cpu", Quantity.fromString(Float.toString(containerPurchase.getCpu())));
            limits.put("memory", Quantity.fromString(containerPurchase.getMemory() + "Gi"));
            requests.put("cpu", Quantity.fromString(Float.toString(containerPurchase.getCpu())));
            requests.put("memory", Quantity.fromString(containerPurchase.getMemory() + "Gi"));

            String gpuK8sResource = podConfiguration.getGpuSpecK8sResourceMap().get(containerPurchase.getGpuType());
            if (!StringUtil.isEmpty(gpuK8sResource) && containerPurchase.getGpuCount() > 0) {
                // 业务原始的需求
                requests.put(gpuK8sResource, Quantity.fromString(Float.toString(containerPurchase.getGpuCount())));
                limits.put(gpuK8sResource, Quantity.fromString(Float.toString(containerPurchase.getGpuCount())));
            }
            resourceRequirements.setRequests(requests);
            resourceRequirements.setLimits(limits);
            container.setResources(resourceRequirements);

            // 处理docker镜像
            if (containerPurchase.getImageAddress().equalsIgnoreCase(containerPurchase.getImageName())) {
                container.setImage(ImageConstant.DOCKER_IMAGE_IO + containerPurchase.getImageAddress() + ":" +
                        containerPurchase.getImageVersion());
            } else {
                container.setImage(containerPurchase.getImageAddress() + ":" + containerPurchase.getImageVersion());
            }
            if (containerPurchase.getImagePullPolicy().equals("")) {
                container.setImagePullPolicy(IMAGE_PULL_POLICY_ALWAYS);
            } else {
                container.setImagePullPolicy(containerPurchase.getImagePullPolicy());
            }

            container.setEnv(parseEnvs(containerPurchase.getEnvs()));
            // 如果有HTTP和TCP的探针，需要将PodIP注入到环境变量中
            if (isChangeContainerProbe(containerPurchase)) {
                if (CollectionUtils.isEmpty(container.getEnv())) {
                    container.setEnv(new ArrayList<V1EnvVar>());
                }
                container.getEnv().add(parseContainerProbeEnv());
            }

            container.setWorkingDir(containerPurchase.getWorkingDir());

            container.setPorts(parsePorts(containerPurchase.getPorts()));

            container.setStdin(containerPurchase.isStdin());
            container.setStdinOnce(containerPurchase.isStdinOnce());
            container.setTty(containerPurchase.isTty());

            if (containerPurchase.getLifecycle() != null) {
                Lifecycle lc = containerPurchase.getLifecycle();
                V1Lifecycle v1Lifecycle = lc.toV1Lifecycle();
                container.setLifecycle(v1Lifecycle);
            }

            // 处理postStart、preStop的配置
            ContainerPurchase previousExitContainer = getPreviousExitPriorityContainer(containerPurchase, exitSortedContainerPurchases);
            ContainerPurchase nextExitContainer = getNextExitPriorityContainer(containerPurchase, exitSortedContainerPurchases);
            Boolean hasNextPriorityContainer = hasNextPriorityContainer(containerPurchase, sortedContainerPurchases);
            if (containerType == ContainerType.WORKLOAD && (hasNextPriorityContainer ||
                    previousExitContainer != null || nextExitContainer != null)) {
                handleContainerLifecycle(container, containerPurchase, previousExitContainer, nextExitContainer, hasNextPriorityContainer);
            }

            // 设置探针
            container.setLivenessProbe(changeContainerProbe(containerPurchase.getLivenessProbe()));
            container.setReadinessProbe(changeContainerProbe(containerPurchase.getReadinessProbe()));
            container.setStartupProbe(changeContainerProbe(containerPurchase.getStartupProbe()));

            if (CollectionUtils.isEmpty(container.getVolumeMounts())) {
                container.setVolumeMounts(new ArrayList<V1VolumeMount>());
            }

            // 设置volumeMount，排查nfs的
            container.getVolumeMounts().addAll(parseVolumeMounts(containerPurchase.getVolumeMounts()));
            if (!isV3) {
                container.getVolumeMounts().addAll(parseNfsVolumeMounts(
                    containerPurchase.getVolumeMounts(), hostPathVs));
            }
            // 如果有HTTP和TCP的探针，需要将将探针工具挂载到容器中
            if (isChangeContainerProbe(containerPurchase)) {
                container.getVolumeMounts().add(parseContainerProbeMounts());
            }

            // 如果有设置容器启动或退出优先级，将Exec工具挂载到容器中
            if(hasNextPriorityContainer || previousExitContainer != null || nextExitContainer != null) {
                container.getVolumeMounts().add(parseContainerLifecycleMounts());
                // 挂载emptydir到容器中，用于保存容器退出状态
                V1VolumeMount emptyDirVolumeMount = new V1VolumeMount();
                emptyDirVolumeMount.setName(WORKLOAD_EMPTY_DIR_MOUNT_NAME);
                emptyDirVolumeMount.setMountPath(WORKLOAD_EMPTY_DIR_MOUNT_PATH);
                container.getVolumeMounts().add(emptyDirVolumeMount);
            }

            if (containerPurchase.getSecurityContext() != null) {
                container.setSecurityContext(containerPurchase.getSecurityContext().toV1SecurityContext());
            }

            containers.add(container);
        }
        return containers;


    }

    /**
     * get nfs、pfs、bos volumemounts
     *
     * @param volumeMounts
     * @param hostPathVs
     */
    private List<V1VolumeMount> parseNfsVolumeMounts(List<VolumeMounts> volumeMounts,
                                                     List<V1Volume> hostPathVs) {
        List<V1VolumeMount> newV = new ArrayList<>();
        // handle nfs\pfs\bos volume mount
        if (volumeMounts == null || volumeMounts.size() == 0) {
            return newV;
        }
        if (hostPathVs == null || hostPathVs.size() == 0) {
            return newV;
        }
        for (VolumeMounts vl : volumeMounts) {
            for (V1Volume v1V : hostPathVs) {
                if (vl.getName().equals(v1V.getName())) {
                    V1VolumeMount v = new V1VolumeMount();
                    v.setName(vl.getName());
                    v.setMountPath(vl.getMountPath());
                    v.setMountPropagation("HostToContainer");
                    // 设置subPath、subPathExpr，如用户设置
                    if (StringUtils.isNotBlank(vl.getSubPath())) {
                        v.setSubPath(vl.getSubPath());
                    }
                    if (StringUtils.isNotBlank(vl.getSubPathExpr())) {
                        v.setSubPathExpr(vl.getSubPathExpr());
                    }
                    newV.add(v);
                }
            }
        }
        // handle other volumes

        return newV;
    }

    private List<V1VolumeMount> parseVolumeMounts(List<VolumeMounts> volumeMounts) {
        List<V1VolumeMount> result = new ArrayList<>();
        // 设置volumeMount
        if (volumeMounts == null || volumeMounts.size() == 0) {
            return result;
        }
        for (VolumeMounts volumeMount : volumeMounts) {
            // 过滤调nfs\pfs\bos\cephfs的volume
            if (PodVolumeType.NFS.getType().equals(volumeMount.getType())
                    || PodVolumeType.PFS.getType().equals(volumeMount.getType())
                    || PodVolumeType.BOS.getType().equals(volumeMount.getType())
                    || PodVolumeType.CEPHFS.getType().equals(volumeMount.getType())) {
                continue;
            }
            V1VolumeMount k8sVolumeMount = new V1VolumeMount();
            k8sVolumeMount.setMountPath(volumeMount.getMountPath());
            k8sVolumeMount.setName(volumeMount.getName());
            k8sVolumeMount.setReadOnly(volumeMount.getReadOnly());
            // 设置subPath
            if (StringUtils.isNotEmpty(volumeMount.getSubPath())) {
                k8sVolumeMount.setSubPath(volumeMount.getSubPath());
            }
            // 设置subPathExpr
            if (StringUtils.isNotEmpty(volumeMount.getSubPathExpr())) {
                k8sVolumeMount.setSubPathExpr(volumeMount.getSubPathExpr());
            }
            result.add(k8sVolumeMount);
        }
        return result;
    }

    private Map<String, String> parselabels(List<Label> createLabels) {
        if (CollectionUtils.isEmpty(createLabels)) {
            return null;
        }
        Map<String, String> labels = new HashMap<>();
        for (Label label : createLabels) {
            // 这三个label，包含k8s 内非允许字段，创建pod 失败，过滤掉
            if (KUBE_PROXY_SIDECAR_LABEL_KEY.equals(label.getLabelKey())
                    || KUBE_PROXY_SIDECAR_HEALTHZ_ADDRESS.equals(label.getLabelKey())
                    || DNS_CONFIG_LABEL_KEY.equals(label.getLabelKey())) {
                continue;
            }
            labels.put(label.getLabelKey(), label.getLabelValue());
        }
        return labels;
    }

    private Map<String, String> parseTags(List<Tag> createTags) {
        if (CollectionUtils.isEmpty(createTags)) {
            return null;
        }
        Map<String, String> tags = new HashMap<>();
        for (Tag tag : createTags) {
            if (tag.getTagKey().equals(BciTagConstant.BCI_TAG_KEY_INTERNAL_ACCOUNT_ID)) {
                continue;
            }
            tags.put(tag.getTagKey(), tag.getTagValue());
        }
        return tags;
    }

    public void set10255MetaInfoInAnnotation(BciOrderExtra orderExtra, V1ObjectMeta metadata) throws IOException {
        MetaInfo metaInfo = new MetaInfo();
        metaInfo.setLabels(new HashMap<>());
        String podNamespaceName = null;
        for (Label label : orderExtra.getLabels()) {
            if (PODNAMESPACE_NAME.equals(label.getLabelKey())) {
                podNamespaceName = label.getLabelValue();
                break;
            }
        }
        if (podNamespaceName == null) {
            return;
        }
        String ns = podNamespaceName.split("/")[0];
        String name = podNamespaceName.split("/")[1];

        metaInfo.setPodName(name);
        metaInfo.setNamespace(ns);
        Map<String, String> labels = parselabels(orderExtra.getPodExtra().getMetadataLabels());
        if (labels != null) {
            metaInfo.setLabels(labels);
        }
        ObjectMapper objectMapper = new ObjectMapper();
        String j = objectMapper.writeValueAsString(metaInfo);
        metadata.getAnnotations().put("bci.cloud.baidu.com/pod-meta-data", j);
    }

    public void setV3FlagByLabel(BciOrderExtra orderExtra) {
        if (orderExtra.getPodExtra() != null && orderExtra.getPodExtra().getMetadataLabels() != null) {
            for (Label label : orderExtra.getPodExtra().getMetadataLabels()) {
                if (label.getLabelKey().equals("bci3")) {
                    LOGGER.info("enable bci3 by label");
                    orderExtra.setV3(true);
                    break;
                }
            }
            LOGGER.info("isV3 {}", orderExtra.isV3());
        }
    }

    /**
     * 封装pod
     *
     * @param action
     * @return
     * @throws IOException
     */
    public List<V1Pod> encapsulatePodForCreate(Order order, String action)
            throws IOException, K8sServiceException, ApiException, InterruptedException {

        CopyOnWriteArrayList<V1Pod> pods = new CopyOnWriteArrayList<>();
        for (Order.Item orderItem : order.getItems()) {
            if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) { // 去除eip的order item
                V1Pod pod = new V1Pod();
                BciOrderExtra orderExtra = PodUtils.getOrderExtra(orderItem.getExtra());
                Boolean autoMatchImageCache = orderExtra.getAutoMatchImageCache();
                String podResourceIgnoreContainers = orderExtra.getPodResourceIgnoreContainers();
                String podIgnoreExitCodeContainers = orderExtra.getPodIgnoreExitCodeContainers();
                String podIgnoreNotReadyContainers = orderExtra.getPodIgnoreNotReadyContainers();
                LOGGER.debug("encapsulatePodForCreate, orderExtra is {},  podIgnoreNotReadyContainers is {}", orderExtra, podIgnoreNotReadyContainers);
                String podFailStrategy = orderExtra.getPodFailStrategy();
                V1ObjectMeta metadata = new V1ObjectMeta();
                pod.setMetadata(metadata);
                String podId = orderExtra.getPodId();
                metadata.setName(orderExtra.getPodId());
                String accountId = getRealAccountID(order);
                String namespace = accountId;
                metadata.setNamespace(accountId); // pod的namespace为accountId
                Boolean enablePodResourceLimit = commonUtils.checkWhiteList(LogicalConstant.ENABLE_POD_RESOURCE_LIMIT,
                    regionConfiguration.getCurrentRegion(), accountId);

                metadata.setAnnotations(new HashMap<String, String>());
                metadata.getAnnotations().put(LogicalConstant.LABEL_ORDER_ID, order.getUuid());
                metadata.getAnnotations().put("payment", orderItem.getProductType());
                // Annotation 中有可能包含k8s 非允许字段，因此去掉
                // if (StringUtils.isNotEmpty(orderExtra.getAnnotations())) {
                //    metadata.getAnnotations().put("annotations", orderExtra.getAnnotations());
                // }
                setV3FlagByLabel(orderExtra);
                if (orderExtra.isV3()) {
                    metadata.getAnnotations().put("eks.baidu.com/bciagentresources", "{\"podResource\":{\"disks\":{\"workspace\":{\"sizeMB\":20000,\"numInodes\":350,\"type\":\"\",\"physicalDisk\":\"/home/<USER>",\"containerPath\":\"/mycontainer/home/<USER>",\"diskBandWidth\":{\"readBandwidthMBPS\":0,\"writeBandwidthMBPS\":0}}},\"process\":{\"numThreads\":2500}}}");
                    forV3PFSAndNFS(orderExtra, metadata);
                    addPodMatchTypeAnnotation(metadata, BCI_SECURITYCONTINER);
                    set10255MetaInfoInAnnotation(orderExtra, metadata);
                }
                // 多可用区信息
                if (StringUtil.isEmpty(orderExtra.getZoneSubnets())) {
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                            + "LogicZone", orderExtra.getLogicalZone());
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                            + "ZoneID", orderExtra.getZoneId());
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                            + "PhysicalZone", orderExtra.getPhysicalZone());
                } else {
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                            + "ZoneSubnets", orderExtra.getZoneSubnets());
                }
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                        + "AccountID", accountId);
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                        + "PodCpu", String.valueOf(orderExtra.getPodCpu()));
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                        + "PodMem",  orderExtra.getPodMem() + "Gi");
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                        + "CpuType", orderExtra.getCpuType());
                String gpuK8sResource = podConfiguration.getGpuSpecK8sResourceMap().get(orderExtra.getPodGpuType());
                if (!StringUtil.isEmpty(gpuK8sResource) && orderExtra.getPodGpuCount() > 0) {
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX + "PodGpuType", gpuK8sResource);
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX + "PodGpuCount",
                            String.valueOf(orderExtra.getPodGpuCount()));
                }
                if (!enableEphemeralQuota) {
                    // 限制pod的磁盘使用空间，20G大小，默认开启
                    metadata.getAnnotations().put("bci.cloud.baidu.com/ephemeral-quota", "disabled");
                }

                // 用户没有指定cpu型号并且不在白名单中，则屏蔽cpu型号
                if (StringUtils.isEmpty(orderExtra.getCpuType()) && !podService.isDisplayOriginalCPUModel(accountId)) {
                    metadata.getAnnotations().put("bci.cloud.baidu.com/generalCPUModel", "enabled");
                }

                if (!enableLxcfs) {
                    // pod开启lxcfs视图隔离，默认开启
                    metadata.getAnnotations().put("bci.cloud.baidu.com/lxcfs", "disabled");
                }

                // for eni
                if (enableEni) {
                    metadata.getAnnotations().put("cross-vpc-eni.cce.io/userID", accountId);
                    metadata.getAnnotations().put("cross-vpc-eni.cce.io/securityGroupIDs",
                            orderExtra.getSecurityGroupShortId());
                    if (StringUtil.isEmpty(orderExtra.getZoneSubnets())) {
                        metadata.getAnnotations().put("cross-vpc-eni.cce.io/subnetID", orderExtra.getSubnetShortId());
                    }
                    metadata.getAnnotations().put("cross-vpc-eni.cce.io/vpcID", orderExtra.getVpcId());
                    String vpcCidr = StringUtils.isNotEmpty(orderExtra.getVpcCidr()) ? orderExtra.getVpcCidr() :
                            orderExtra.getSubnetCidr();
                    metadata.getAnnotations().put("cross-vpc-eni.cce.io/vpcCidr", vpcCidr);
                    // eni 2.1 缓存使用
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX + "eniPrivateIp", "1");
                    LOGGER.info("add eni info, accountid {}, subnetId {}, cidr {}, securityGroupIDs {}",
                            accountId, orderExtra.getSubnetShortId(), orderExtra.getSubnetCidr(),
                            orderExtra.getSecurityGroupShortId());
                }
                // for bid
                forBid(orderItem, metadata);
                forNTP(orderExtra, metadata);

                if (orderExtra.getAnnotationsMap() != null) {
                    for (Map.Entry<String, String> entry : orderExtra.getAnnotationsMap().entrySet()) {
                        if (validatePodAnnotation(podId, entry)) {
                            metadata.getAnnotations().put(entry.getKey(), entry.getValue());
                        }
                    }
                }

                // for eip
                boolean needEip = false;
                if (enableEni && StringUtils.isNotEmpty(orderExtra.getEipPurchaseRequest())) {
                    metadata.getAnnotations().put("cross-vpc-eni.cce.io/eipPurchase",
                            orderExtra.getEipPurchaseRequest());
                    needEip = true;

                    // set pod finalizer
                    metadata.addFinalizersItem(EipConstant.POD_FINALIZER);
                }

                metadata.setLabels(new HashMap<String, String>());

                // vk 传递的值，直接放到label中，创建pod 有可能会失败，因此注释掉
//                Map<String, String> labels = parselabels(orderExtra.getLabels());
//                if (labels != null) {
//                    metadata.getLabels().putAll(labels);
//                }

                if (orderExtra.getPodExtra() != null && orderExtra.getPodExtra().getMetadataLabels() != null) {
                    for (Label label : orderExtra.getPodExtra().getMetadataLabels()) {
                        if (validatePodLabel(podId, label)) {
                            metadata.getLabels().put(label.getLabelKey(), label.getLabelValue());
                        }
                     }
                }

                // 账户ID 和 pod short id 是必需的 label，其他的 label 都过滤掉，原因是：vk 传递的值，直接放到label中，创建pod 有可能会失败，因此注释掉
                Map<String, String> labels = parselabels(orderExtra.getLabels());
                if (labels != null) {
                    Map<String, String> finalLabels = new HashMap<String, String>();
                    finalLabels.put(LogicalConstant.LABEL_ACCOUNT_ID, labels.get(LogicalConstant.LABEL_ACCOUNT_ID));
                    finalLabels.put(LogicalConstant.LABEL_POD_ID, labels.get(LogicalConstant.LABEL_POD_ID));
                    metadata.getLabels().putAll(finalLabels);
                }

                Map<String, String> tags = parseTags(orderExtra.getTags());
                if (tags != null) {
                    metadata.getAnnotations().putAll(tags); // tag作为annotation放进去
                }

                // todo 安全组功能，设置在annotations中，可能有多个安全组
                // metadata.getAnnotations().put(BCI_INTERNAL_PREFIX + "SecurityGroup",
                //        orderExtra.getSecurityGroupId());
                // todo 子网功能
                // metadata.getAnnotations().put(BCI_INTERNAL_PREFIX+"SubnetId",orderExtra.getSubnetUuid());

                // spec
                V1PodSpec spec = new V1PodSpec();
                pod.setSpec(spec);
                // set pod hostname
                String hostName = generatePodHostName(orderExtra, accountId);
                if (StringUtils.isNotEmpty(hostName)) {
                    spec.setHostname(hostName);
                }
                spec.setVolumes(new ArrayList<V1Volume>());
                // init nfs host path volumes
                LOGGER.debug("orderExtra, {}", orderExtra.toString());
                List<V1Volume> hostPathVs = new ArrayList<>();
                List<V1Volume> pfsHostPathVs = new ArrayList<>();
                List<V1Volume> bosHostPathVs = new ArrayList<>();
                List<V1Volume> cephFSHostPathVs = new ArrayList<>();
                List<V1Container> nfsSidecars = new ArrayList<>();
                List<V1Container> pfsSidecars = new ArrayList<>();
                List<V1Container> bosSidecars = new ArrayList<>();
                List<V1Container> cephFSSidecars = new ArrayList<>();
                List<V1Container> ntpSidecars = new ArrayList<>();
                if (!orderExtra.isV3()) {
                    List<V1Volume> nfsHostPathVs = getNfsToHostPathVolumes(orderExtra);
                    LOGGER.debug("nfsHostPathVs, {}", nfsHostPathVs.toString());
                    spec.getVolumes().addAll(nfsHostPathVs);
                    hostPathVs.addAll(nfsHostPathVs);

                    pfsHostPathVs = getPfsToHostPathVolumes(orderExtra);
                    LOGGER.debug("pfsHostPathVs, {}", pfsHostPathVs.toString());
                    spec.getVolumes().addAll(pfsHostPathVs);
                    hostPathVs.addAll(pfsHostPathVs);

                    bosHostPathVs = getBosToHostPathVolumes(orderExtra);
                    LOGGER.debug("bosHostPathVs, {}", bosHostPathVs.toString());
                    spec.getVolumes().addAll(bosHostPathVs);
                    hostPathVs.addAll(bosHostPathVs);

                    cephFSHostPathVs = getCephFSToHostPathVolumes(orderExtra);
                    LOGGER.debug("cephFSHostPathVs, {}", cephFSHostPathVs.toString());
                    spec.getVolumes().addAll(cephFSHostPathVs);
                    hostPathVs.addAll(cephFSHostPathVs);

                    nfsSidecars = getNfsSidecarContainers(orderExtra, nfsHostPathVs);
                    LOGGER.debug("nfsSidecars, {}", nfsSidecars.toString());
                    pfsSidecars = getPfsSidecarContainers(orderExtra, pfsHostPathVs);
                    LOGGER.debug("pfsSidecars, {}", pfsSidecars.toString());
                    bosSidecars = getBosSidecarContainers(orderExtra, bosHostPathVs);
                    LOGGER.debug("bosSidecars, {}", bosSidecars.toString());

                    cephFSSidecars = getCephFSSidecarContainers(orderExtra, cephFSHostPathVs);
                    LOGGER.debug("cephFSSidecars, {}", cephFSSidecars.toString());
                    ntpSidecars = getNTPSidecarContainers(metadata);
                    LOGGER.debug("ntpSidecars, {}", ntpSidecars.toString());

                }
                // init container probe volumes
                List<V1Volume> probeVolumes = getContainerProbeVolumes(orderExtra);
                LOGGER.debug("probeVolumes, {}", probeVolumes.toString());
                spec.getVolumes().addAll(probeVolumes);
                // init container priority exec volumes
                List<V1Volume> lifecycleVolumes = getContainerLifecycleVolumes(orderExtra);
                LOGGER.debug("lifecycleVolumes, {}", lifecycleVolumes.toString());
                spec.getVolumes().addAll(lifecycleVolumes);
                // init containers
                List<V1Container> initContainers = geneContainers(orderExtra.getContainers(), ContainerType.INIT,
                                                                  hostPathVs, orderExtra.isV3());
                // containers
                List<V1Container> workloadContainers = geneContainers(
                    orderExtra.getContainers(), ContainerType.WORKLOAD, hostPathVs, orderExtra.isV3());
                List<V1Container> dsContainers = geneContainers(
                    orderExtra.getContainers(), ContainerType.DS_WORKLOAD, hostPathVs, orderExtra.isV3());
                List<V1Container> containers = new ArrayList<>();
                containers.addAll(workloadContainers);
                containers.addAll(dsContainers);
                // ds containers
                Set<String> dsContainerNames = new HashSet<>();
                for (ContainerPurchase purchase : orderExtra.getContainers()) {
                    if (ContainerType.DS_WORKLOAD.getType().equals(purchase.getContainerType())) {
                        dsContainerNames.add(purchase.getName());
                    }
                }

                // stdlogvolume/flexvolume需要感知用户container, 防止mount暴露内置容器
                String podContainersStr = getPodContainersStr(containers, initContainers);
                List<V1Volume> stdoutLogVolumes = podLogService.geneStdoutVolume(order.getAccountId(),
                        orderExtra, podContainersStr);
                LOGGER.debug("stdoutLogVolumes, {}", stdoutLogVolumes.toString());
                spec.getVolumes().addAll(stdoutLogVolumes);
                List<V1Volume> flexVolumes = getFlexVolumes(orderExtra, podContainersStr);
                LOGGER.debug("flexVolumes, {}", flexVolumes.toString());
                spec.getVolumes().addAll(flexVolumes);
                // 添加容器终止消息
                addContainerTerminationParam(initContainers, containers, orderExtra);

                List<V1Container> sidecarContainers = new ArrayList<>();
                // sidecars
                // sidecar 容器配置规则 : https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Jk_2Ea1IJe/mWE_hPuHeC/YzFZRYru2s-2Gd
                List<V1Container> logSidecars = podLogService.geneLogSidecarContainers(order.getAccountId(),
                        order.getUuid(), orderExtra, spec);
                LOGGER.debug("logSidecars, {}", logSidecars.toString());

                // 生成kubeproxy sidecar容器
                V1Container kubeProxySidecar = getKubeProxyContainer(orderExtra, containers);
                if (kubeProxySidecar != null) {
                    sidecarContainers.add(kubeProxySidecar);
                }

                if (CollectionUtils.isNotEmpty(nfsSidecars)) {
                    sidecarContainers.addAll(nfsSidecars);
                }

                if (CollectionUtils.isNotEmpty(pfsSidecars)) {
                    sidecarContainers.addAll(pfsSidecars);
                }

                if (CollectionUtils.isNotEmpty(bosSidecars)) {
                    sidecarContainers.addAll(bosSidecars);
                }

                if (CollectionUtils.isNotEmpty(logSidecars)) {
                    sidecarContainers.addAll(logSidecars);
                }

                if (CollectionUtils.isNotEmpty(cephFSSidecars)) {
                    sidecarContainers.addAll(cephFSSidecars);
                }

                if (CollectionUtils.isNotEmpty(ntpSidecars)) {
                    sidecarContainers.addAll(ntpSidecars);
                }

                if (!dsContainerNames.isEmpty() && injectKubeletProxyForDaemonset){
                    // 注入kubelet-proxy容器
                    sidecarContainers.add(geneKubeletProxyContainer(orderExtra.getPodId(), spec));
                }

                // 添加coredump volume
                addContainerCoredumpConfig(initContainers, containers, spec, orderExtra);

                // 镜像缓存处理逻辑开始
                spec.setInitContainers(new ArrayList<V1Container>());
                List<V1Container> bciInternalInitContainers = new ArrayList<>();
                List<V1Container> allContainers = new ArrayList<>();
                allContainers.addAll(initContainers);
                allContainers.addAll(containers);
                List<V1Container> originContainers = imageCacheServiceV2.deepCopy(containers);
                List<V1Container> originInitContainers = imageCacheServiceV2.deepCopy(initContainers);
                Map<String, String> imageInitConAnnotations = new HashMap<>();
                List<V1Container> applyImageAccelerateCons = new ArrayList<>();
                // 获取所有可以加速的镜像
                List<String> allNeedAcceImages = new ArrayList<>();
                List<String> needAcceWorkImageAddrAndTags = imageCacheServiceV2.getCanAcceImages(containers);
                List<String> needAcceInitImageAddrAndTags = imageCacheServiceV2.getCanAcceImages(initContainers);
                allNeedAcceImages.addAll(needAcceInitImageAddrAndTags);
                allNeedAcceImages.addAll(needAcceWorkImageAddrAndTags);

                // allNeedAccImages 去重
                Set<String> allNeedAccImagesSet = new HashSet<>(allNeedAcceImages);
                allNeedAcceImages.clear();
                allNeedAcceImages.addAll(allNeedAccImagesSet);

                // 添加dnsConfig
                addUserPodDnsConfig(orderExtra, pod);
                // 添加defaultDnsConfig
                addDefaultPodDnsConfigIfPossible(orderExtra, pod);

                if (autoMatchImageCache) {
                    applyImageAccelerateCons = imageCacheServiceV2.getAndSetCanAcceContainersForCcr(
                        allContainers, accountId);
                    // 有需要加速的镜像
                    if (applyImageAccelerateCons.size() > 0) {
                        imageCacheServiceV2.setPodSpecCcrImageCache(spec);
                        if (applyImageAccelerateCons.size() < allContainers.size()) {
                            // 设置imageInitContainers
                            List<V1Container> imageInitContainers =
                                geneImageInitContainers(orderExtra,
                                    originInitContainers, workloadContainers, dsContainers, applyImageAccelerateCons,
                                    imageInitConAnnotations);
                            imageCacheServiceV2.setImageInitContainers(metadata, spec, imageInitContainers,
                                bciInternalInitContainers, orderExtra);
                        }
                    } else {
                        // 设置imageInitContainers
                        List<V1Container> imageInitContainers =
                            geneImageInitContainers(orderExtra,
                                originInitContainers, workloadContainers, dsContainers, applyImageAccelerateCons,
                                imageInitConAnnotations);
                        imageCacheServiceV2.setImageInitContainers(metadata, spec, imageInitContainers,
                                bciInternalInitContainers, orderExtra);
                    }

                    // amd透明化
                    if (StringUtils.isEmpty(orderExtra.getCpuType()) &&  // 用户没有指定cpu型号
                            !podService.isDisplayOriginalCPUModel(accountId) && // 用户不在白名单中
                            applyImageAccelerateCons.size() == allContainers.size()){ // 都存在转储镜像
                        LOGGER.debug("pod need change CpuType. pod:{}", orderExtra.getPodId());
                        List<String> imageCpuTypes = imageCacheServiceV2.getImageSupportCpuType(allContainers, accountId);
                        // 转储镜像未全部完成镜像扫描, 则正常按照随机调度
                        if (applyImageAccelerateCons.size() != imageCpuTypes.size()) {
                            LOGGER.debug("pod image scan not complete and no need change cpuType. pod:{}",
                                    orderExtra.getPodId());
                            // metadata.getAnnotations().put(PodConstants.BCI_INTERNAL_PREFIX
                            //     + "CpuType", PodConstants.CPU_TYPE_INTEL);
                        // 转储镜像全部完成镜像扫描, 则需要根据扫描结果来设置CpuType
                        } else {
                            boolean supportAmd = true;
                            for (String cpuType : imageCpuTypes) {
                                List<String> cpuTypeList = JsonUtil.toList(cpuType, String.class);
                                if (!cpuTypeList.contains(PodConstants.CPU_TYPE_AMD)) {
                                    supportAmd = false;
                                }
                            }
                            if (!supportAmd) {
                                // 如果不支持amd，则将CpuType设置为intel
                                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX
                                    + "CpuType", PodConstants.CPU_TYPE_INTEL);
                                LOGGER.debug("pod image scan not support and need change to intel. pod:{}",
                                    orderExtra.getPodId());
                            } else {
                                LOGGER.debug("pod image scan support amd. pod:{}",
                                        orderExtra.getPodId());
                            }
                        }
                    }

                    // 创建镜像缓存不应该block pod的正常创建
                    try {
                        if (allNeedAcceImages.size() > 0) {
                            // 设置参数，落库
                            LOGGER.debug("all allNeedAcceImages is {}", allNeedAcceImages);
                            Collections.sort(allNeedAcceImages);
                            String originImageAddressAndTagsToString = String.join(",", allNeedAcceImages);
                            String imageCacheId =
                                accountId + DigestUtils.md5DigestAsHex(
                                originImageAddressAndTagsToString.getBytes(StandardCharsets.UTF_8));
                            // 根据镜像缓存id查询是否需要创建缓存
                            ImageCachePO imageCache = imageCacheDaoV2.getImageCacheById(imageCacheId, accountId);
                            LOGGER.debug("imageCache is {}", imageCache);
                            if (imageCache == null) {
                                LOGGER.debug("enter imageCache create");
                                String imageCacheName = "auto-create-" + imageCacheId;
                                ImageAccelerateCRDRequest crdRequest = new ImageAccelerateCRDRequest();
                                String tcPodName = "p-tc-" + imageCacheName;
                                imageCacheServiceV2.setImageCacheCrdRequest(accountId, imageCacheName, needEip,
                                    orderExtra.getLogicalZone(), orderExtra.getPhysicalZone(), orderExtra.getZoneId(),
                                    orderExtra.getSubnetShortId(), orderExtra.getSecurityGroupShortId(), orderExtra.getVpcCidr(),
                                    orderExtra.getZoneSubnets(), allNeedAcceImages, 50,
                                    orderExtra.getImageRegistrySecrets(), tcPodName, autoMatchImageCache,
                                "BGP", 200, "ByTraffic", "", crdRequest);
                                V1PodDNSConfig podDNSConfig = spec.getDnsConfig();
                                crdRequest.getMetadata().getAnnotations().put(IMAGE_CACHE_POD_DNS_CONFIG_ANNOTATION_KEY, JsonUtil.toJSON(podDNSConfig));
                                LOGGER.debug("crdRequest is name {}, json data {}", crdRequest.getMetadata().getName(),
                                        JsonUtil.toJSON(crdRequest));
                                ImageCachePO imageCachePO = new ImageCachePO();
                                List<ImageDetailPO> imageDetailPOs = new ArrayList<>();
                                CreateImageCacheRequest request = new CreateImageCacheRequest();
                                request.setImageCacheName(imageCacheName);
                                request.setImageRegistrySecrets(orderExtra.getImageRegistrySecrets());
                                request.setNeedEip(needEip);
                                List<ImageInfo> imageInfos = new ArrayList<>();
                                for (String originImage : allNeedAcceImages) {
                                    String[] originImageSplit = originImage.split(":");
                                    ImageInfo imageInfo = new ImageInfo();
                                    imageInfo.setOriginImageAddress(originImageSplit[0]);
                                    imageInfo.setOriginImageVersion(originImageSplit[1]);
                                    imageInfos.add(imageInfo);
                                }
                                request.setOriginImages(imageInfos);
                                request.setRetentionDay(30);
                                request.setSecurityGroupId(orderExtra.getSecurityGroupShortId());
                                request.setSubnetId(orderExtra.getSubnetShortId());
                                request.setZoneSubnets(orderExtra.getZoneSubnets());

                                // TO DO 优化项，存储空间大小
                                request.setTemporaryStorageSize(50);
                                LOGGER.debug("request is {}", request);
                                imageCacheServiceV2.praseRequestToDbObj(request, ImageCacheOwner.BCI.getName(),
                                imageCachePO, imageDetailPOs, tcPodName, accountId);
                                LOGGER.debug("imageCachePO is {}", imageCachePO);
                                imageCacheServiceV2.saveImageCacheToDb(imageCachePO, imageDetailPOs);
                                k8sService.createImageAccCRD(accountId, crdRequest);
                            }
                        }
                    } catch (Exception e) {
                        LOGGER.error("create image cache failed,{}", e);
                    }
                } else {
                    // 不使用和创建镜像缓存
                    List<V1Container> allAcceContainers = new ArrayList<>();
                    List<V1Container> imageInitContainers =
                        geneImageInitContainers(orderExtra, initContainers,
                        workloadContainers, dsContainers, allAcceContainers, imageInitConAnnotations);
                    imageCacheServiceV2.setImageInitContainers(metadata, spec, imageInitContainers,
                                bciInternalInitContainers, orderExtra);
                }
                imageCacheServiceV2.setImageInitContainersAnnotation(metadata, spec, bciInternalInitContainers,
                    initContainers);
                // 镜像缓存逻辑结束

                if (enablePodResourceLimit) {
                    // 对没配置resource limit的container进行配置
                    List<V1Container> allInitContainers = spec.getInitContainers();
                    addResourceLimitToInitContainers(allInitContainers, orderExtra.getPodCpu(), orderExtra.getPodMem());
                    spec.setInitContainers(allInitContainers);
                }

                if (enablePodResourceLimit) {
                    // 对没配置resource limit的container进行配置
                    addResourceLimitToContainers(containers, orderExtra.getPodCpu(), orderExtra.getPodMem());
                }

                // 设置 ds 相关的 annotation
                Map<String, String> dsVolumeName2Type = PodUtils.getVolume2TypeMap(orderExtra.getVolume(), true);
                Map<String, String> dsContainerVersionMap = Util.convertListToMap(dsContainerNames, "0");
                metadata.getAnnotations().put(PodConstants.BCI_DS_CONTAINER_NAMES,
                                              Util.saveListToString(dsContainerNames));
                metadata.getAnnotations().put(PodConstants.BCI_DS_VOLUME_NAME_2_TYPE,
                                              Util.saveMapToString(dsVolumeName2Type));
                metadata.getAnnotations().put(PodConstants.BCI_DS_CONTAINERS_VERSION, "0");
                metadata.getAnnotations().put(PodConstants.BCI_DS_CONTAINER_VERSION_MAP,
                                              Util.saveMapToString(dsContainerVersionMap));
                // 在计算qos的时候，应该忽略所有的ds容器，因为ds容器有可能增删，进而导致qos变化，进而导致pod重建
                metadata.getAnnotations().put(PodConstants.BCI_IGNORE_THIS_CONTAINERS_COMPUTING_QOS, Util.saveListToString(dsContainerNames));

                // 处理 podResourceIgnoreContainers
                if (StringUtils.isNotEmpty(podResourceIgnoreContainers)) {
                    metadata.getAnnotations().put(PodConstants.BCI_RESOURCE_IGNORE_CONTAINERS_ANNOTATION_KEY,
                            podResourceIgnoreContainers);
                    processPodResourceIgnoreContainersIfPossible(containers, podResourceIgnoreContainers);
                }

                // 处理podResourceIgnoreContainers
                if (StringUtils.isNotEmpty(podIgnoreExitCodeContainers)) {
                    metadata.getAnnotations().put(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY,
                            podIgnoreExitCodeContainers);
                }

                // 处理podIgnoreNotReadyContainers
                if (StringUtils.isNotEmpty(podIgnoreNotReadyContainers)) {
                    metadata.getAnnotations().put(PodConstants.BCI_IGNORE_NOT_READY_CONTAINERS_ANNOTATION_KEY,
                    podIgnoreNotReadyContainers);
                }

                // 处理podFailStrategy
                if (StringUtils.isNotEmpty(podFailStrategy)) {
                    metadata.getAnnotations().put(PodConstants.BCI_FAIL_STRATEGY_ANNOTATION_KEY,
                            podFailStrategy);
                }

                spec.setContainers(containers);
                spec.setRestartPolicy(orderExtra.getRestartPolicy());
                spec.setTerminationGracePeriodSeconds(orderExtra.getPodExtra() == null ?
                        null : orderExtra.getPodExtra().getTerminationGracePeriodSeconds());

                PodExtra podExtra = orderExtra.getPodExtra();
                if (podExtra != null) {
                    Affinity affinity = podExtra.getAffinity();
                    spec.setAffinity(affinity == null ? null : affinity.toV1Affinity());
                    spec.setTopologySpreadConstraints(podExtra.getTopologySpreadConstraints());
                }
                // 设置 Pod 安全上下文
                if (orderExtra.getPodExtra() != null && orderExtra.getPodExtra().getSecurityContext() != null) {
                    spec.setSecurityContext(orderExtra.getPodExtra().getSecurityContext().toV1PodSecurityContext());
                }

                // 设置潮汐pod Annotation matchType
                if (orderExtra.isTidal()) {
                    addPodMatchTypeAnnotation(metadata, PodConstants.BCI_POD_MATCH_TYPE_TIDAL);
                    // 可被controller删除pod
                    metadata.getAnnotations().put(PodConstants.BCI_AUTO_DELETE_POD_ANNOTATION_KEY, "1");
                }

                // 设置IPv6 pod annotation
                if (orderExtra.isEnableIPv6()) {
                    metadata.getAnnotations().put(PodConstants.BCI_ENABLE_IPV6_ANNOTATION_KEY, "true");
                }

                // 设置PFS pod Annotation matchType, PFS在BCI2.0需要单独的节点组
                if ((pfsHostPathVs != null && pfsHostPathVs.size() > 0) || orderExtra.isScheduleInPfsPool()) {
                    addPodMatchTypeAnnotation(metadata, PodConstants.BCI_POD_MATCH_TYPE_PFS);
                }
                // 禁止使用hostPID
                spec.setHostPID(false);

                // 配置sidecar容器
                configureSidecarContainer(sidecarContainers, pod);
                if (enablePodResourceLimit) {
                    // pod资源限制
                    configureResourceToSidecarContainers(sidecarContainers, orderExtra.getPodCpu(), orderExtra.getPodMem());
                    addResourceLimitAnnotation(metadata);
                }
                spec.getContainers().addAll(0, sidecarContainers);

                createPodConfigMapIfPossible(orderExtra, pod);
                setEmptyDirIfPossible(orderExtra, pod);
                setHostPathIfPossible(orderExtra, pod);
                // 添加hostAlias
                addHostAlias(orderExtra, pod);
                // 添加preStop 和 postStart hook, BCI目前只支持exec形式的hook，httpGet和tcpSocket类型的hook会被忽略。
                // addContainerLifecycleHook(containers, orderExtra);

                // 注入kube-proxy init容器
                // 使用场景:
                //   1. 客户的Pod里存在自定义的initContainers
                //   2. 客户自定义的initContainers需要访问k8s集群内部服务
                // 因此BCI需要在InitContainers中注入kube-proxy(initContaner)容器
                //    1. 并且还要保证注入的kube-proxy(initContainer)容器在用户自定义的initContainers之前
                //    2. 也就是BCI注入的kube-proxy(initContainer)容器是BCI Pod initContainers的第一个initContainer容器
                boolean injectKubeProxyIntoInitContainersResult = injectKubeProxyIntoInitContainers(orderExtra, pod);
                LOGGER.debug("injectKubeProxyIntoInitContainers namespace:{} podId:{} result:{}",
                        namespace, pod.getMetadata().getName(), injectKubeProxyIntoInitContainersResult);

                // 在initContainer容器中批量从客户的镜像仓库下载镜像,提升镜像下载速度;
                // 使用场景:批量从客户的镜像仓库下载镜像,提升镜像下载速度
                // 触发条件:
                //   1. 客户提交Pod里的镜像在BCI侧并没有对应的镜像缓存
                //      a. 第一次使用该镜像,之前并没有触发镜像缓存
                //      b. 镜像缓存正在进行中
                //      c. 之前触发过镜像缓存,但镜像缓存失败了
                boolean batchDownloadImageForInitContainersResult = batchDownloadImageForInitContainers(orderExtra,
                        pod);
                LOGGER.debug("batchDownloadImageForInitContainers namespace:{} podId:{} result:{}",
                        namespace, pod.getMetadata().getName(), batchDownloadImageForInitContainersResult);
                LOGGER.info("encapsulatePodForCreate namespace:{} podId:{} podSpec:{}",
                        namespace,
                        pod.getMetadata().getName(),
                        JsonUtil.toJSON(pod));

                for (int i = 0; i < orderItem.getCount(); i++) { // 批量
                    pods.add(pod);
                }
            }
        }
        return pods;
    }

    public boolean validatePodLabel(String podId, Label label) {
        if (label == null) {
            return false;
        }
        if (StringUtils.isEmpty(label.getLabelKey())) {
            return false;
        }
        if (StringUtils.length(label.getLabelKey()) > PodConstants.POD_LABEL_MAX_KEY_LENGTH) {
            LOGGER.warn("podId:{} label key is too long, key:{} skip it", podId, label.getLabelKey());
            return false;
        }
        if (StringUtils.length(label.getLabelValue()) > PodConstants.POD_LABEL_MAX_VALUE_LENGTH) {
            LOGGER.warn("podId:{} label value is too long, key:{} value:{} skip it",
                    podId, label.getLabelKey(), label.getLabelValue());
            return false;
        }
        return true;
    }

    public boolean validatePodAnnotation(String podId, Map.Entry<String, String> annotation) {
        if (annotation == null) {
            return false;
        }
        if (StringUtils.isEmpty(annotation.getKey())) {
            return false;
        }
        if (StringUtils.length(annotation.getKey()) > PodConstants.POD_ANNOTATION_MAX_KEY_LENGTH) {
            LOGGER.warn("podId:{} annotation key is too long, key:{} skip it", podId, annotation.getKey());
            return false;
        }
        if (StringUtils.length(annotation.getValue()) > PodConstants.POD_ANNOTATION_MAX_VALUE_LENGTH) {
            LOGGER.warn("podId:{} annotation value is too long, key:{} skip it",
                    podId, annotation.getKey());
            return false;
        }
        return true;
    }

    public boolean processPodResourceIgnoreContainersIfPossible(List<V1Container> containers,
                                                                    String podResourceIgnoreContainers) {
        List<String> podResourceIgnoreContainersList = Arrays.asList(podResourceIgnoreContainers.split(","));
        for (V1Container container : containers) {
            if (podResourceIgnoreContainersList.contains(container.getName())) {
                container.getResources().getRequests().put("cpu", new Quantity("0"));
                container.getResources().getRequests().put("memory", new Quantity("0"));
            }
        }
        return true;
    }

    private boolean addDefaultPodDnsConfigIfPossible(BciOrderExtra orderExtra, V1Pod pod) {
        String accountId = pod.getMetadata().getNamespace();
        if (enableEni) {
            // need config container dns
            pod.getSpec().setDnsPolicy("None");
            if (pod.getSpec().getDnsConfig() != null) {
                V1PodDNSConfig dnsConfig = pod.getSpec().getDnsConfig();
                if (dnsConfig.getNameservers() == null) {
                    dnsConfig.setNameservers(new ArrayList<String>());
                }
                if (dnsConfig.getNameservers().isEmpty()) {
                    dnsConfig.getNameservers().add(BCI_DNS_CONFIG_NAME_SERVER);
                    return true;
                } else {
                    List<String> bciPodSpecDnsConfigNameserversNoAddUsersList =
                            Arrays.asList(bciPodSpecDnsConfigNameserversNoAddUsers.split(","));
                    if (!bciPodSpecDnsConfigNameserversNoAddUsersList.contains(accountId)) {
                        dnsConfig.getNameservers().add(BCI_DNS_CONFIG_NAME_SERVER);
                        return true;
                    }
                }
            } else {
                // 如果用户没有注入DnsConfig，则注入默认的DnsConfig
                V1PodDNSConfig dnsConfig = new V1PodDNSConfig();
                List<String> nameservers = new ArrayList<>();
                nameservers.add(BCI_DNS_CONFIG_NAME_SERVER);
                dnsConfig.setNameservers(nameservers);
                pod.getSpec().setDnsConfig(dnsConfig);
                return true;
            }
        }
        return false;
    }

    private String generatePodHostName(BciOrderExtra orderExtra, String accountId) {
        if (orderExtra.getPodExtra() == null) {
            return null;
        }
        String hostName = orderExtra.getPodExtra().getHostname();
        if (StringUtils.isNotEmpty(hostName)) {
            return hostName;
        }

        List<String> bciPodReserveOriginalPodHostnameUsersList =
                Arrays.asList(bciPodReserveOriginalPodHostnameUsers.split(","));
        if (!bciPodReserveOriginalPodHostnameUsersList.contains(accountId)) {
            return null;
        }

        if (orderExtra.getPodExtra().getMetadataLabels() == null) {
            return null;
        }

        List<Label> metadataLabels = orderExtra.getPodExtra().getMetadataLabels();
        for (Label label : metadataLabels) {
            if (label.getLabelKey().equals(PodConstants.BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY)) {
                return label.getLabelValue();
            }
        }
        return null;
    }

    /**
     * 注入kube-proxy init容器
     * 使用场景:
     *   1. 客户的Pod里存在自定义的initContainers
     *   2. 客户自定义的initContainers需要访问k8s集群内部服务
     * 因此BCI需要在InitContainers中注入kube-proxy(initContaner)容器
     *    1. 并且还要保证注入的kube-proxy(initContainer)容器在用户自定义的initContainers之前
     *    2. 也就是BCI注入的kube-proxy(initContainer)容器是BCI Pod initContainers的第一个initContainer容器
     * */
    public boolean injectKubeProxyIntoInitContainers(BciOrderExtra orderExtra, V1Pod pod) {
        String namespace = pod.getMetadata().getNamespace();
        String podId = pod.getMetadata().getName();
        if (StringUtils.isEmpty(namespace) || StringUtils.isEmpty(podId)) {
            LOGGER.debug("injectKubeProxyIntoInitContainers namespace or podId is empty");
            return false;
        }

        // 判断用户是否在注入kube-proxy的用户列表中(配置文件),如果不在,则不需要注入kube-proxy init容器
        List<String> bciInitContainersInjectKubeProxyEnableUsersList =
                Arrays.asList(bciInitContainersInjectKubeProxyEnableUsers.split(","));
        if (!bciInitContainersInjectKubeProxyEnableUsersList.contains(namespace)) {
            LOGGER.debug("injectKubeProxyIntoInitContainers namespace:{} not in " +
                            "bciInitContainersInjectKubeProxyEnableUsersList:{} podId:{}",
                    namespace, bciInitContainersInjectKubeProxyEnableUsersList, podId);
            return false;
        }

        String kubeProxySidecarName = null;
        for (Label label : orderExtra.getLabels()) {
            if (KUBE_PROXY_SIDECAR_LABEL_KEY.equals(label.getLabelKey())) {
                kubeProxySidecarName = label.getLabelValue();
                break;
            }
        }
        if (kubeProxySidecarName == null) {
            LOGGER.debug("injectKubeProxyIntoInitContainers namespace:{} podId:{} label kubeProxySidecarName is null",
                    namespace, podId);
            return false;
        }

        // TODO:优化项 不添加默认dns白名单
        // 1.在白名单里,不注入默认 BCI_DNS_CONFIG_NAME_SERVER, 应该注入 kube-proxy-init container
        // 2.不在白名单里,注入默认 BCI_DNS_CONFIG_NAME_SERVER, 不需要注入 kube-proxy-init container
        V1Container kubeProxyContainer = null;
        for (V1Container container : pod.getSpec().getContainers()) {
            if (container.getName().equals(kubeProxySidecarName)) {
                kubeProxyContainer = container;
                break;
            }
        }

        // 如果没有kube-proxy容器，不需要注入kube-proxy init容器
        if (kubeProxyContainer == null) {
            LOGGER.debug("injectKubeProxyIntoInitContainers namespace:{} podId:{} not found kubeProxy container",
                    namespace, podId);
            return false;
        }

        boolean hasKubeProxyDownloadImageInitContainer = false;
        V1Container kubeProxyDownloadImageInitContainer = null;
        for (V1Container container : pod.getSpec().getInitContainers()) {
            if (container.getName().startsWith(BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX)) {
                for (V1EnvVar envVar : container.getEnv()) {
                    if (envVar.getName().equals(PodConstants.ORIGIN_CONTAINER_IMAGE_NAME) &&
                            envVar.getValue().equals(kubeProxyContainer.getImage())) {
                        hasKubeProxyDownloadImageInitContainer = true;
                        kubeProxyDownloadImageInitContainer = container;
                        break;
                    }
                }
            }
        }

        // 需要注入kube-proxy init容器, 使用k8s默认方式下载kube-proxy镜像
        // 构造kube-proxy init容器
        V1Container kubeProxyInitContainer = new V1Container();

        // 生成容器名称
        String kubeProxyInitContainerName = BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX +
                BCI_IMAGE_DOWNLOAD_WORKLOAD_INFIX + pod.getSpec().getContainers().size();
        kubeProxyInitContainer.setName(kubeProxyInitContainerName);
        // 镜像和kube-proxy container保持一致
        kubeProxyInitContainer.setImage(kubeProxyContainer.getImage());

        // set resources
        V1ResourceRequirements resourceRequirements = new V1ResourceRequirements();
        Map<String, Quantity> requests = new HashMap<>();
        Map<String, Quantity> limits = new HashMap<>();
        limits.put("cpu", Quantity.fromString(Float.toString(orderExtra.getPodCpu())));
        limits.put("memory", Quantity.fromString(Float.toString(orderExtra.getPodMem()) + "Gi"));
        requests.put("cpu", Quantity.fromString(Float.toString(orderExtra.getPodCpu())));
        requests.put("memory", Quantity.fromString(Float.toString(orderExtra.getPodMem()) + "Gi"));
        resourceRequirements.setRequests(requests);
        resourceRequirements.setLimits(limits);
        kubeProxyInitContainer.setResources(resourceRequirements);

        kubeProxyInitContainer.setWorkingDir(kubeProxyContainer.getWorkingDir());
        kubeProxyInitContainer.setImagePullPolicy(IMAGE_PULL_POLICY_IF_NOT_PRESENT);
        kubeProxyInitContainer.setCommand(new ArrayList<>(Arrays.asList(BCI_KUBE_PROXY_INIT_CHECK_COMMAND)));
        kubeProxyInitContainer.setArgs(new ArrayList<>(Arrays.asList("-c", BCI_KUBE_PROXY_INIT_CHECK_SCRIPT)));
//        String startArgs = "iptables-ensurer && nohup kube-proxy --bind-address=$MY_IP --cluster-cidr= " +
//                "--proxy-mode=iptables --masquerade-all=false --hostname-override=$MY_IP --kubeconfig=/conf/kube-proxy.conf --master= --healthz-bind-address=127.0.0.1 --healthz-port=10256 --logtostderr=true --v=6 --conntrack-tcp-timeout-established=0s --conntrack-tcp-timeout-close-wait=0s --conntrack-max-per-core=0 > glen-kube-proxy.log 2>&1 & sleep 30";
//        kubeProxyInitContainer.setArgs(new ArrayList<>(Arrays.asList("-c", startArgs)));
        // 挂载卷和kube-proxy container保持一致
        kubeProxyInitContainer.setVolumeMounts(kubeProxyContainer.getVolumeMounts());
        // 端口和kube-proxy container保持一致
        kubeProxyInitContainer.setPorts(kubeProxyContainer.getPorts());

        // 设置环境变量
        kubeProxyInitContainer.setEnv(new ArrayList<V1EnvVar>());
        for (V1EnvVar envVar : kubeProxyContainer.getEnv()) {
            if (envVar.getName().equals("containerName")) {
                V1EnvVar newEnvVar = new V1EnvVar();
                newEnvVar.setName(envVar.getName());
                newEnvVar.setValue(kubeProxyInitContainerName);
                kubeProxyInitContainer.getEnv().add(newEnvVar);
                continue;
            }
            kubeProxyInitContainer.getEnv().add(envVar);
        }
        // 设置stdin, stdinOnce, tty, securityContext 均和kube-proxy container保持一致
        kubeProxyInitContainer.setStdin(kubeProxyContainer.getStdin());
        kubeProxyInitContainer.setStdinOnce(kubeProxyContainer.getStdinOnce());
        kubeProxyInitContainer.setTty(kubeProxyContainer.getTty());
        kubeProxyInitContainer.setSecurityContext(kubeProxyContainer.getSecurityContext());

        // 将kube-proxy init容器插入到init容器的最前面
        int kubeProxyInitContainerIndex = 0;
        pod.getSpec().getInitContainers().add(kubeProxyInitContainerIndex, kubeProxyInitContainer);

        // 更新ANNOTATION_INIT_NAME
        String oldAnnotationInitName = pod.getMetadata().getAnnotations().get(ANNOTATION_INIT_NAME);
        List<String> newAnnotationInitNameList = new ArrayList<>();
        if (StringUtils.isEmpty(oldAnnotationInitName)) {
            newAnnotationInitNameList.add(kubeProxyInitContainerName);
        } else {
            newAnnotationInitNameList = new ArrayList<>(Arrays.asList(oldAnnotationInitName.split(",")));
            newAnnotationInitNameList.add(0, kubeProxyInitContainerName);
        }

        // 如果开启了注入 kube-proxy init容器，则需要删除initContainers中原来下载kube-proxy镜像的init容器
        if (hasKubeProxyDownloadImageInitContainer) {
            pod.getSpec().getInitContainers().remove(kubeProxyDownloadImageInitContainer);
            newAnnotationInitNameList.remove(kubeProxyDownloadImageInitContainer.getName());
        }

        // 更新相关Annotation
        pod.getMetadata().getAnnotations().put(ANNOTATION_INIT_NAME, String.join(",", newAnnotationInitNameList));
        // 设置注入kube-proxy init容器的标记
        pod.getMetadata().getAnnotations().put(BCI_INJECT_KUBE_PROXY_INIT_CONTAINER_ANNOTATION_NAME, "1");
        return true;
    }

    /**
     * 在initContainer容器中批量从客户的镜像仓库下载镜像
     * 使用场景:批量从客户的镜像仓库下载镜像,提升镜像下载速度
     * 触发条件:
     *   1. 客户提交Pod里的镜像在BCI侧并没有对应的镜像缓存
     *      a. 第一次使用该镜像,之前并没有触发镜像缓存
     *      b. 镜像缓存正在进行中
     *      c. 之前触发过镜像缓存,但镜像缓存失败了
     * */
    public boolean batchDownloadImageForInitContainers(BciOrderExtra orderExtra, V1Pod pod) {
        boolean result = false;
        String namespace = pod.getMetadata().getNamespace();;
        String podId = pod.getMetadata().getName();
        if (StringUtils.isEmpty(namespace) || StringUtils.isEmpty(podId)) {
            LOGGER.debug("batchDownloadImageForInitContainers namespace is empty");
            return result;
        }

        List<V1Container> initContainers = pod.getSpec().getInitContainers();
        if (initContainers == null || initContainers.isEmpty()) {
            LOGGER.debug("batchDownloadImageForInitContainers namespace:{} podId:{} initContainers is empty",
                    namespace, podId);
            return result;
        }

        boolean hasInjectKubeProxyInitContainer = false;
        V1Container kubeProxyInitContainer = null;
        if (pod.getMetadata().getAnnotations().containsKey(BCI_INJECT_KUBE_PROXY_INIT_CONTAINER_ANNOTATION_NAME)) {
            hasInjectKubeProxyInitContainer = true;
            kubeProxyInitContainer = initContainers.get(0);
        }
        List<V1Container> initContainersWithoutKubeProxyInitContainer = new ArrayList<>();
        if (hasInjectKubeProxyInitContainer) {
            initContainersWithoutKubeProxyInitContainer.addAll(initContainers.subList(1, initContainers.size()));
        } else {
            initContainersWithoutKubeProxyInitContainer.addAll(initContainers);
        }

        String bciInternalInitContainerAnnotationValue = pod.getMetadata().getAnnotations().get(ANNOTATION_INIT_NAME);
        if (StringUtils.isEmpty(bciInternalInitContainerAnnotationValue)) {
            LOGGER.debug("batchDownloadImageForInitContainers namespace:{} podId:{} ANNOTATION_INIT_NAME:{} is empty",
                    namespace, podId, ANNOTATION_INIT_NAME);
            return result;
        }

        List<V1Container> newInitContainers = new ArrayList<>();

        String batchDownloadImageInitContainerName = null;
        V1Container batchDownImageInitContainer = new V1Container();
        V1Container firstDownImageInitContainer = null;
        Map<String, V1Container> initImageDownloadContainers = new HashMap<>();
        Map<String, V1Container> initNotImageDownloadContainers = new HashMap<>();

        for (V1Container initContainer : initContainersWithoutKubeProxyInitContainer) {
            String image = initContainer.getImage();
            String name = initContainer.getName();
            if (name.startsWith(BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX)) {
                if (batchDownloadImageInitContainerName == null) {
                    batchDownloadImageInitContainerName = name;
                    firstDownImageInitContainer = initContainer;
                }
                initImageDownloadContainers.put(name, initContainer);
            } else {
                initNotImageDownloadContainers.put(name, initContainer);
            }
        }
        if (initImageDownloadContainers.isEmpty()) {
            LOGGER.debug("batchDownloadImageForInitContainers namespace:{} podId:{} initImageDownloadContainers is empty",
                    namespace, podId);
            return false;
        }

        batchDownImageInitContainer.setImage(firstDownImageInitContainer.getImage());
        batchDownImageInitContainer.setImagePullPolicy(IMAGE_PULL_POLICY_IF_NOT_PRESENT);
        batchDownImageInitContainer.setName(batchDownloadImageInitContainerName);
        batchDownImageInitContainer.setResources(firstDownImageInitContainer.getResources());
        batchDownImageInitContainer.setSecurityContext(firstDownImageInitContainer.getSecurityContext());
        batchDownImageInitContainer.setVolumeMounts(firstDownImageInitContainer.getVolumeMounts());

        batchDownImageInitContainer.setCommand(Arrays.asList(BCI_INIT_CONTAINER_BATCH_DOWNLOAD_IMAGE_COMMAND));

        V1EnvVar envVar = new V1EnvVar();
        envVar.setName(BCI_INIT_CONTAINER_BATCH_DOWNLOAD_IMAGES_ENV_NAME);
        envVar.setValue(generateInitContainerBatchDownloadImagesEnvValue(initImageDownloadContainers));
        batchDownImageInitContainer.setEnv(Arrays.asList(envVar));

        // 清空 initContainers
        pod.getSpec().getInitContainers().clear();
        // 1. 添加initKubeProxyContainer
        if (hasInjectKubeProxyInitContainer) {
            newInitContainers.add(kubeProxyInitContainer);
        }
        // 2. 添加batchDownImageInitContainer
        newInitContainers.add(batchDownImageInitContainer);
        // 3. 添加其他非ImageDownloadContainers
        newInitContainers.addAll(initNotImageDownloadContainers.values());
        pod.getSpec().setInitContainers(newInitContainers);

        // 更新pod的annotations
        // 1. 增加Annotation: batchDownloadImageInitContainer
        pod.getMetadata().getAnnotations().put(
                BCI_INIT_CONTAINER_BATCH_DOWNLOAD_IMAGES_CONTAINER_NAME_ANNOTATIONS_NAME,
                batchDownloadImageInitContainerName);

        // 2. 更新Pod原有Annotation: bci_internal_initContainer
        String annotationInitName = pod.getMetadata().getAnnotations().get(ANNOTATION_INIT_NAME);
        List<String> annotationInitNameList = new ArrayList<String>();
        if (!StringUtils.isEmpty(annotationInitName)) {
            annotationInitNameList = new ArrayList<String>(Arrays.asList(annotationInitName.split(",")));
        }
        annotationInitNameList.removeAll(initImageDownloadContainers.keySet());
        annotationInitNameList.add(batchDownImageInitContainer.getName());
        pod.getMetadata().getAnnotations().put(
                ANNOTATION_INIT_NAME,
                String.join(",", annotationInitNameList));
        return true;
    }

    public String generateInitContainerBatchDownloadImagesEnvValue(Map<String, V1Container> initImageDownloadContainers) {
        Map<String, InitImageDownloadImageInstance> initImageDownloadImageInstanceMap = new HashMap<>();
        for (Map.Entry<String, V1Container> entry : initImageDownloadContainers.entrySet()) {
            InitImageDownloadImageInstance initImageDownloadImageInstance = new InitImageDownloadImageInstance();
            String name = entry.getKey();
            V1Container container = entry.getValue();
            List<String> command = container.getCommand();
            if (command == null || command.size() < 6) {
                continue;
            }
            initImageDownloadImageInstance.setImage(command.get(1));
            initImageDownloadImageInstance.setUsername(command.get(2));
            initImageDownloadImageInstance.setPassword(command.get(3));
            initImageDownloadImageInstance.setPullPolicy(command.get(4));
            if (initImageDownloadImageInstanceMap.containsKey(command.get(1))) {
                continue;
            }
            initImageDownloadImageInstanceMap.put(command.get(1), initImageDownloadImageInstance);
        }
        return JsonUtil.toJSON(initImageDownloadImageInstanceMap.values());
    }

    private void addPodMatchTypeAnnotation(V1ObjectMeta metadata, String value) {
        if (metadata == null) {
            return;
        }
        Map<String, String> annotations = metadata.getAnnotations();
        if (MapUtils.isEmpty(annotations)) {
            annotations = new HashMap<>();
            metadata.setAnnotations(annotations);
        }
        String matchTypeValue = annotations.get(PodConstants.BCI_POD_MATCH_TYPE_ANNOTATION_KEY);
        if (StringUtils.isEmpty(matchTypeValue)) {
            annotations.put(PodConstants.BCI_POD_MATCH_TYPE_ANNOTATION_KEY, value);
            return;
        }
        // 多个值按逗号隔开
        annotations.put(PodConstants.BCI_POD_MATCH_TYPE_ANNOTATION_KEY, matchTypeValue + "," + value);
    }

    private Map<String, Object> parseAnnotationMap(BciOrderExtra orderExtra) {
        Map<String, Object> annotationMap = new HashMap<>();
        String annotations = orderExtra.getAnnotations();
        if (annotations == null || "".equals(annotations)) {
            return annotationMap;
        }


        ObjectMapper objectMapper = new ObjectMapper();

        try {
            annotationMap = objectMapper.readValue(annotations, Map.class);
        } catch (Exception e) {
            LOGGER.error("Unmarshal pod Annotation {} err {} ", annotations, e);
        }
        return annotationMap;
    }

    private void addContainerTerminationParam(List<V1Container> initContainers, List<V1Container> containers,
                                              BciOrderExtra orderExtra) {
        Map<String, Object> annotationMap = parseAnnotationMap(orderExtra);
        if (annotationMap == null || annotationMap.size() == 0) {
            return;
        }
        Object terminationParam = annotationMap.get(TERMINATION_PARAM);
        if (terminationParam == null) {
            return;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, ContainerTermination> terminationMap = null;
        try {
            String terminationStr = objectMapper.writeValueAsString(terminationParam);
            LOGGER.info("pod {} terminationStr is {}", orderExtra.getPodId(), terminationStr);
            terminationMap = objectMapper.readValue(terminationStr,
                    new TypeReference<Map<String, ContainerTermination>>() {
                    });
        } catch (Exception e) {
            LOGGER.error("Unmarshal container TERMINATION_PARAM {} err {} ", orderExtra.getAnnotations(), e);
            return;
        }
        if (terminationMap == null) {
            return;
        }


        if (CollectionUtils.isNotEmpty(initContainers)) {
            for (V1Container container : initContainers) {
                ContainerTermination containerTermination = terminationMap.get(container.getName());
                if (containerTermination == null ||
                        !ContainerType.INIT.getType().equals(containerTermination.getContainerType())) {
                    continue;
                }
                setContainerTerminationParam(containerTermination, container);
            }
        }

        if (CollectionUtils.isNotEmpty(containers)) {
            for (V1Container container : containers) {
                ContainerTermination containerTermination = terminationMap.get(container.getName());
                if (containerTermination == null
                    || (!ContainerType.WORKLOAD.getType().equals(containerTermination.getContainerType())
                        && !ContainerType.DS_WORKLOAD.getType().equals(containerTermination.getContainerType()))) {
                    continue;
                }
                setContainerTerminationParam(containerTermination, container);
            }
        }
    }

    private void setContainerTerminationParam(ContainerTermination containerTermination,
                                              V1Container container) {
        String path = containerTermination.getTerminationMessagePath();
        String policy = containerTermination.getTerminationMessagePolicy();
        if (path == null && policy == null) {
            return;
        }

        if (policy != null && TERMINATION_MESSAGE_POLICY_SET.contains(policy)) {
            container.setTerminationMessagePolicy(policy);
        }

        if (path != null) {
            container.setTerminationMessagePath(path);
        }
    }

    private void addContainerLifecycleHook(List<V1Container> containers, BciOrderExtra orderExtra) {
        if (CollectionUtils.isEmpty(containers)) {
            return;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> annotationMap = parseAnnotationMap(orderExtra);
        Object postStartHookObject = annotationMap.get(POST_START_HOOK_NAME);
        Object preStopHookObject = annotationMap.get(PRE_STOP_HOOK_NAME);

        Map<String, LifecycleHook> preStopHook = new HashMap<>();
        Map<String, LifecycleHook> postStartHook = new HashMap<>();
        if (preStopHookObject != null) {
            try {
                String preStopHookJsonStr = objectMapper.writeValueAsString(preStopHookObject);
                LOGGER.info("addContainerLifecycleHook podId {} preStopHookJsonStr {} ", orderExtra.getPodId(),
                        preStopHookJsonStr);
                preStopHook = objectMapper.readValue(preStopHookJsonStr,
                        new TypeReference<Map<String, LifecycleHook>>() {
                        });
            } catch (Exception e) {
                LOGGER.error("Unmarshal pod preStopHook {} err {} ", orderExtra.getAnnotations(), e);
            }
        }

        if (postStartHookObject != null) {
            try {
                // {"container01":{"exec":["sh","-c","sleep 1000"]}}
                String postStartHookJsonStr = objectMapper.writeValueAsString(postStartHookObject);
                LOGGER.info("addContainerLifecycleHook podId {} postStartHookJsonStr {} ", orderExtra.getPodId(),
                        postStartHookJsonStr);
                postStartHook = objectMapper.readValue(postStartHookJsonStr,
                        new TypeReference<Map<String, LifecycleHook>>() {
                        });
            } catch (Exception e) {
                LOGGER.error("Unmarshal pod postStartHook {} err {} ", orderExtra.getAnnotations(), e);
            }
        }

        for (V1Container container : containers) {
            String containerName = container.getName();
            LifecycleHook postStartLifecycleHook = postStartHook.get(containerName);
            LifecycleHook preStopLifecycleHook = preStopHook.get(containerName);
            if (postStartLifecycleHook == null && preStopLifecycleHook == null) {
                continue;
            }
            V1Lifecycle lifecycle = container.getLifecycle();
            if (lifecycle == null) {
                lifecycle = new V1Lifecycle();
                container.setLifecycle(lifecycle);
            }
            if (postStartLifecycleHook != null &&
                    postStartLifecycleHook.getExec() != null &&
                    CollectionUtils.isNotEmpty(postStartLifecycleHook.getExec().getCommand())) {
                V1LifecycleHandler postStart = lifecycle.getPostStart();
                if (postStart == null) {
                    postStart = new V1LifecycleHandler();
                    lifecycle.setPostStart(postStart);
                }
                V1ExecAction v1ExecAction = new V1ExecAction();
                v1ExecAction.setCommand(postStartLifecycleHook.getExec().getCommand());
                postStart.setExec(v1ExecAction);
            }

            if (preStopLifecycleHook != null &&
                    preStopLifecycleHook.getExec() != null &&
                    CollectionUtils.isNotEmpty(preStopLifecycleHook.getExec().getCommand())) {
                V1LifecycleHandler preStop = lifecycle.getPreStop();
                if (preStop == null) {
                    preStop = new V1LifecycleHandler();
                    lifecycle.setPreStop(preStop);
                }
                V1ExecAction v1ExecAction = new V1ExecAction();
                v1ExecAction.setCommand(preStopLifecycleHook.getExec().getCommand());
                preStop.setExec(v1ExecAction);
            }
        }
    }

    public void forBid(Order.Item orderItem, V1ObjectMeta metadata) {
        PricingDetail pricingDetail = orderItem.getPricingDetail();
        if (pricingDetail != null) {
            BidPrice bidPrice = pricingDetail.getBidPrice();
            if (bidPrice != null) {
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX + "bidModel", bidPrice.getBidModel());
                // 价格差，提交到bci的竞价实例是bcc的1.2倍，保留7位小数，最后一位4四舍五入
                if (bidPrice.getBidPrice() != null) {
                    String priceRadio = "1.2";
                    metadata.getAnnotations().put(BCI_INTERNAL_PREFIX + "bidPrice", String.valueOf(bidPrice
                            .getBidPrice().divide(new BigDecimal(priceRadio), 7, BigDecimal.ROUND_HALF_UP)));
                }
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX + "bidTimeout", "60"); // minute
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX +
                        "bidReleaseEIP", "False"); // 默认是false
                metadata.getAnnotations().put(BCI_INTERNAL_PREFIX +
                        "bidReleaseCDS", "False"); // 默认是false
                // 可被controller删除pod
                metadata.getAnnotations().put(PodConstants.BCI_AUTO_DELETE_POD_ANNOTATION_KEY, "1");
            }
        }
    }

    public void forNTP(BciOrderExtra orderExtra, V1ObjectMeta metadata) {
        String annotations = orderExtra.getAnnotations();
        if (annotations == null || "".equals(annotations)) {
            return;
        }
        Map<String, Object> annotationMap = null;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            annotationMap = objectMapper.readValue(annotations, Map.class);
        } catch (Exception e) {
            LOGGER.error("Unmarshal pod Annotation {} err {} ", annotations, e);
            throw new PodExceptions.PodAnnotationInvalid("Annotation is not a valid json");
        }
        //
        Object ntpServerIP = annotationMap.get(ANNOTATION_NTP_SERVER);
        if (ntpServerIP == null) {
            LOGGER.debug("Annotation {} not have key {}, no need inject ntp sidecar", annotations, ANNOTATION_NTP_SERVER);
            return;
        }
        if(!ntpServerIP.toString().matches(SIMPLE_IPADDRESS_PATTERN)){
            throw new PodExceptions.PodAnnotationInvalid("NTP server IP is invalid");
        }
        metadata.getAnnotations().put(ANNOTATION_NTP_SERVER, ntpServerIP.toString());
    }

    /**
     * 生成sidecar 容器配置
     *
     * @param sidecarContainers
     * @param pod
     */
    private void configureSidecarContainer(List<V1Container> sidecarContainers, V1Pod pod) throws K8sServiceException {
        if (CollectionUtils.isEmpty(sidecarContainers)) {
            return;
        }
        // 在控制面启动时,会在所有用户namespace下创建一个sidecar command configMap
        // 这里无需判断是否存在，直接使用即可
        String emptyDirName = "sidecar-emptydir";
        // 生成emptyDir
        V1Volume emptyDirVolume = new V1Volume();
        emptyDirVolume.setName(emptyDirName);
        emptyDirVolume.setEmptyDir(new V1EmptyDirVolumeSource());

        V1Volume sidecarCommandCmVolume = new V1Volume();
        sidecarCommandCmVolume.setName(SIDECAR_COMMAND_CM_NAME_REF);
        V1ConfigMapVolumeSource sidecarCommandCmSource = new V1ConfigMapVolumeSource();
        sidecarCommandCmSource.setName(SIDECAR_COMMAND_CM_NAME);
        sidecarCommandCmVolume.setConfigMap(sidecarCommandCmSource);

        if (pod.getSpec().getVolumes() == null) {
            pod.getSpec().setVolumes(new ArrayList<V1Volume>());
        }
        pod.getSpec().getVolumes().add(emptyDirVolume);
        pod.getSpec().getVolumes().add(sidecarCommandCmVolume);

        for (V1Container sidecarContainer : sidecarContainers) {
            // 校验sidecar 容器是否按照规范来实现postStart,exec 形式的command，/bin/sh -c xx.sh
            if (sidecarContainer.getLifecycle() == null || sidecarContainer.getLifecycle().getPostStart() == null
                    || sidecarContainer.getLifecycle().getPostStart().getExec() == null
                    || sidecarContainer.getLifecycle().getPostStart().getExec().getCommand() == null
                    || sidecarContainer.getLifecycle().getPostStart().getExec().getCommand().size() != 3) {
                throw new K8sServiceException(K8sServiceException.ErrorCode.CREATE_POD_FAILED, "sidecar container " +
                        "readiness exec command err");
            }
        }

        // 每个sidecar 容器在postStart阶段执行脚本，因此需要一个汇总的sidecar执行post.sh 脚本
        V1Container postStartContainer = buildPostStartSidecarContainer();
        sidecarContainers.add(postStartContainer);

        // 为每个sidecar容器挂载emptydir & sidecar-cm 脚本 & containerName env
        for (V1Container sidecarContainer : sidecarContainers) {
            if (sidecarContainer.getVolumeMounts() == null) {
                sidecarContainer.setVolumeMounts(new ArrayList<V1VolumeMount>());
            }
            V1VolumeMount emptyDirVolumeMount = new V1VolumeMount();
            emptyDirVolumeMount.setName(emptyDirName);
            emptyDirVolumeMount.setMountPath(SIDECAR_EMPTY_DIR_MOUNT_PATH);

            V1VolumeMount sidecarCommandVolumeMount = new V1VolumeMount();
            sidecarCommandVolumeMount.setName(SIDECAR_COMMAND_CM_NAME_REF);
            sidecarCommandVolumeMount.setMountPath(SIDECAR_COMMAND_MOUNT_PATH);

            sidecarContainer.getVolumeMounts().add(emptyDirVolumeMount);
            sidecarContainer.getVolumeMounts().add(sidecarCommandVolumeMount);

            // 设置sidecar count env
            if (CollectionUtils.isEmpty(sidecarContainer.getEnv())) {
                sidecarContainer.setEnv(new ArrayList<V1EnvVar>());
            }
            V1EnvVar sidecarCountEnv = new V1EnvVar();
            sidecarCountEnv.setName("sidecar_count");
            // 因为最后拼接了一个sidecar 容器，所以 -1
            sidecarCountEnv.setValue(sidecarContainers.size() - 1 + "");
            sidecarContainer.getEnv().add(sidecarCountEnv);

            V1EnvVar sidecarNameEnv = new V1EnvVar();
            sidecarNameEnv.setName("containerName");
            sidecarNameEnv.setValue(sidecarContainer.getName());
            boolean containerNameEnvExist = false;
            for (V1EnvVar env : sidecarContainer.getEnv()) {
                if ("containerName".equals(env.getName())) {
                    containerNameEnvExist = true;
                    break;
                }
            }
            if (!containerNameEnvExist) {
                sidecarContainer.getEnv().add(sidecarNameEnv);
            }

        }

        V1Lifecycle lifecycle = new V1Lifecycle();
        V1LifecycleHandler postStart = new V1LifecycleHandler();

        V1ExecAction exec = new V1ExecAction();
        List<String> command = new ArrayList<>();
        command.add("/bin/sh");
        command.add("-c");

        command.add("sh " + SIDECAR_COMMAND_MOUNT_PATH + POST_START_SHELL_NAME);
        exec.setCommand(command);
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);
        postStartContainer.setLifecycle(lifecycle);

        StringBuilder sidecarNames = new StringBuilder();
        // pod Annotation 设置sidecar信息
        for (V1Container sidecar : sidecarContainers) {
            sidecarNames.append(sidecar.getName());
            sidecarNames.append(",");
        }
        // 截取最后一个,
        String sidecarNameStr = sidecarNames.toString();
        sidecarNameStr = sidecarNameStr.substring(0, sidecarNameStr.length() - 1);
        // 给controller 感知
        pod.getMetadata().getAnnotations().put(ANNOTATION_SIDECAR_NAME, sidecarNameStr);
    }

    private V1Container buildPostStartSidecarContainer() {
        V1Container postStart = new V1Container();
        postStart.setName(PodConstants.BCI_POST_START_SIDECAR);
        // 使用nfs的image，TODO 待打镜像
        if (StringUtil.isEmpty(storageSidecarImage)) {
            postStart.setImage(bciStorageSidecarImage);
        } else {
            postStart.setImage(storageSidecarImage);
        }
        List<String> command = new ArrayList<>();
        command.add("/bin/sh");
        command.add("-c");
        command.add("while true; do sleep 36000; done");
        postStart.setCommand(command);
        return postStart;
    }

    private V1Container geneKubeletProxyContainer(String podId, V1PodSpec spec){
        V1Container kubeletProxy = new V1Container();
        kubeletProxy.setName(PodConstants.BCI_KUBELET_PROXY_SIDECAR_CONTAINER);
        kubeletProxy.setImage(kubeletProxySidecarImage);
        List<String> command = new ArrayList<>();
        command.add("/root/kubelet-proxy/kubelet-proxy-sidecar");
        kubeletProxy.setCommand(command);
        String socketPath = "/root/kubelet-proxy/sock";
        kubeletProxy.setArgs(Arrays.asList("--socket-path", socketPath));

        // 注入uds env，poststart hook中使用uds判断kubelet-proxy是否启动
        V1EnvVar envVar = new V1EnvVar();
        envVar.setName("UDSPath");
        envVar.setValue(socketPath + "/kubelet-proxy.sock");

        // 注入PodID
        V1EnvVar podIdEnv = new V1EnvVar();
        podIdEnv.setName("BCI_BACKEND_POD_ID");
        podIdEnv.setValue(podId);

        ArrayList<V1EnvVar> envVars = new ArrayList<>();
        envVars.add(envVar);
        envVars.add(podIdEnv);


        V1EnvVar podLogPathEnvVar = new V1EnvVar();
        podLogPathEnvVar.setName("PodLogPath");
        podLogPathEnvVar.setValue(zuoyebangLogPath);
        envVars.add(podLogPathEnvVar);
        kubeletProxy.setEnv(envVars);

        // kubelet-proxy sidecar需要注入hostpath，使用uds和kubelet-proxy ds通信
        V1VolumeMount kubeletProxyMount = new V1VolumeMount();
        kubeletProxyMount.setName(PodConstants.BCI_KUBELET_PROXY_SIDECAR_UDS_VOLUME_NAME);
        kubeletProxyMount.setMountPath(socketPath);
        kubeletProxyMount.setMountPropagation("HostToContainer");
        ArrayList<V1VolumeMount> volumeMounts = new ArrayList<>();
        volumeMounts.add(kubeletProxyMount);

        // kubelet-proxy sidecar需要注入hostpath, /var/log/pods
        V1VolumeMount srcPodLogsMount = new V1VolumeMount();
        String srcPodLogsPath  = "/var/log/pods";
        srcPodLogsMount.setName(PodConstants.BCI_KUBELET_PROXY_SIDECAR_SRC_POD_LOGS);
        srcPodLogsMount.setMountPath(srcPodLogsPath);
        volumeMounts.add(srcPodLogsMount);

        // kubelet-proxy sidecar需要注入业务dest log hostpath
        V1VolumeMount destPodLogsMount = new V1VolumeMount();
        String destPodLogsPath  = zuoyebangLogPath;
        destPodLogsMount.setName(PodConstants.BCI_KUBELET_PROXY_SIDECAR_DEST_POD_LOGS);
        destPodLogsMount.setMountPath(destPodLogsPath);
        volumeMounts.add(destPodLogsMount);

        kubeletProxy.setVolumeMounts(volumeMounts);

        V1Volume volume = new V1Volume();
        volume.setName(PodConstants.BCI_KUBELET_PROXY_SIDECAR_UDS_VOLUME_NAME);
        V1HostPathVolumeSource hostPathSource = new V1HostPathVolumeSource();
        hostPathSource.setPath("/var/run/kubelet-proxy");
        hostPathSource.setType("Directory");
        volume.setHostPath(hostPathSource);

        if (spec.getVolumes() == null || spec.getVolumes().isEmpty()) {
            spec.setVolumes(new ArrayList<V1Volume>());
        }
        spec.getVolumes().add(volume);

        V1Volume srcPodLogVolume = new V1Volume();
        srcPodLogVolume.setName(PodConstants.BCI_KUBELET_PROXY_SIDECAR_SRC_POD_LOGS);
        V1HostPathVolumeSource srcPodLoghostPathSource = new V1HostPathVolumeSource();
        srcPodLoghostPathSource.setPath("/var/log/pods");
        srcPodLogVolume.setHostPath(srcPodLoghostPathSource);
        spec.getVolumes().add(srcPodLogVolume);

        V1Volume destPodLogVolume = new V1Volume();
        destPodLogVolume.setName(PodConstants.BCI_KUBELET_PROXY_SIDECAR_DEST_POD_LOGS);
        V1HostPathVolumeSource destPodLoghostPathSource = new V1HostPathVolumeSource();
        destPodLoghostPathSource.setPath(zuoyebangLogPath);
        destPodLoghostPathSource.setType("DirectoryOrCreate");
        destPodLogVolume.setHostPath(destPodLoghostPathSource);
        spec.getVolumes().add(destPodLogVolume);

        V1Lifecycle lifecycle = new V1Lifecycle();
        V1LifecycleHandler postStart = new V1LifecycleHandler();
        V1ExecAction exec = new V1ExecAction();
        List<String> postStartCommandList = new ArrayList<>();
        postStartCommandList.add("/bin/sh");
        postStartCommandList.add("-c");
        postStartCommandList.add("nohup sh " + SIDECAR_COMMAND_MOUNT_PATH + KUBELET_PROXY_CHECK_SHELL_NAME + " &");
        exec.setCommand(postStartCommandList);
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);

        kubeletProxy.setLifecycle(lifecycle);

        return kubeletProxy;
    }

    /**
     * 设置host alias
     *
     * @param orderExtra
     * @param pod
     */
    private void addHostAlias(BciOrderExtra orderExtra, V1Pod pod) {
        String annotations = orderExtra.getAnnotations();
        if (annotations == null || "".equals(annotations)) {
            return;
        }
        Map<String, Object> annotationMap = null;
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            annotationMap = objectMapper.readValue(annotations, Map.class);
        } catch (Exception e) {
            LOGGER.error("Unmarshal pod Annotation {} err {} ", annotations, e);
            return;
        }

        Object objectHostAlias = annotationMap.get(HOST_ALIASES);
        if (objectHostAlias == null) {
            return;
        }
        try {
            String s = objectMapper.writeValueAsString(objectHostAlias);
            List<V1HostAlias> v1HostAlias = objectMapper.readValue(s, new TypeReference<List<V1HostAlias>>() {
            });
            pod.getSpec().setHostAliases(v1HostAlias);
        } catch (Exception e) {
            LOGGER.error("Unmarshal pod HostAlias {} err {} ", annotations, e);
        }
    }

    private void addContainerCoredumpConfig(List<V1Container> initContainers, List<V1Container> containers,
                                            V1PodSpec spec, BciOrderExtra orderExtra) {
        List<Label> labels = orderExtra.getLabels();
        boolean hasCorePattern = false;
        if (CollectionUtils.isNotEmpty(labels)) {
            for (Label label : labels) {
                if (COREDUMP_PATTERN_VALUE.equals(label.getLabelValue())) {
                    hasCorePattern = true;
                    break;
                }
            }
        }
        if (hasCorePattern) {
            return;
        }
        // TODO 待适配bci 3.0
        // 默认为所有容器生成coredump 目录，映射到hostpath上
        List<V1Volume> volumes = spec.getVolumes();
        if (CollectionUtils.isEmpty(volumes)) {
            volumes = new ArrayList<>();
            spec.setVolumes(volumes);
        }
        V1HostPathVolumeSource v1HostPathVolumeSource = new V1HostPathVolumeSource();
        v1HostPathVolumeSource.setPath("/home/<USER>/coredump/" + orderExtra.getPodId());
        v1HostPathVolumeSource.setType("DirectoryOrCreate");
        V1Volume hostPathVolume = new V1Volume();
        hostPathVolume.setName(CORDUMPE_VOLUME_NAME);
        hostPathVolume.setHostPath(v1HostPathVolumeSource);
        volumes.add(hostPathVolume);

        setContainerCoredumpVolumeMount(initContainers);
        setContainerCoredumpVolumeMount(containers);
    }

    private void setContainerCoredumpVolumeMount(List<V1Container> containers) {
        if (CollectionUtils.isEmpty(containers)) {
            return;
        }
        for (V1Container container : containers) {
            List<V1VolumeMount> volumeMounts = container.getVolumeMounts();
            if (CollectionUtils.isEmpty(volumeMounts)) {
                volumeMounts = new ArrayList<>();
                container.setVolumeMounts(volumeMounts);
            }
            // TODO bci 2.0只支持此路径，后续bci3.0适配自定义路径
            V1VolumeMount volumeMount = new V1VolumeMount();
            volumeMount.setName(CORDUMPE_VOLUME_NAME);
            volumeMount.setMountPath(COREDUMP_PATTERN_VALUE);
            volumeMounts.add(volumeMount);
        }
    }

    /**
     * 添加用户自定义pod dns config
     *
     * @param orderExtra
     * @param pod
     */
    private void addUserPodDnsConfig(BciOrderExtra orderExtra, V1Pod pod) {
        List<Label> labels = orderExtra.getLabels();
        if (CollectionUtils.isEmpty(labels)) {
            return;
        }

        String dnsConfigJson = null;
        for (Label label : labels) {
            if (DNS_CONFIG_LABEL_KEY.equals(label.getLabelKey())) {
                dnsConfigJson = label.getLabelValue();
                break;
            }
        }
        if (dnsConfigJson == null) {
            return;
        }
        // 应用用户自定义的dnsConfig
        pod.getSpec().setDnsPolicy("None");
        LOGGER.info("pod {} has dnsConfig {} ", pod.getMetadata().getName(), dnsConfigJson);
        DNSConfig vkDnsConfig = null;

        try {
            ObjectMapper mapper = new ObjectMapper();
            vkDnsConfig = mapper.readValue(dnsConfigJson, DNSConfig.class);
        } catch (Exception e) {
            LOGGER.error("Unmarshal pod {} dnsConfigJson {} err {} ", pod.getMetadata().getName(), dnsConfigJson, e);
            return;
        }

        V1PodDNSConfig dnsConfig = new V1PodDNSConfig();
        if (pod.getSpec().getDnsConfig() != null) {
            dnsConfig = pod.getSpec().getDnsConfig();
        } else {
            pod.getSpec().setDnsConfig(dnsConfig);
        }

        if (CollectionUtils.isNotEmpty(dnsConfig.getNameservers())) {
            if (CollectionUtils.isNotEmpty(vkDnsConfig.getServers())) {
                // 将用户配置的dns nameserver 放到最前面
                dnsConfig.getNameservers().addAll(0, vkDnsConfig.getServers());
            }
        } else {
            dnsConfig.setNameservers(vkDnsConfig.getServers());
        }

        if (CollectionUtils.isNotEmpty(dnsConfig.getSearches())) {
            if (CollectionUtils.isNotEmpty(vkDnsConfig.getSearches())) {
                dnsConfig.getSearches().addAll(vkDnsConfig.getSearches());
            }
        } else {
            dnsConfig.setSearches(vkDnsConfig.getSearches());
        }

        List<V1PodDNSConfigOption> options = new ArrayList<>();

        // options 封装
        if (!CollectionUtils.isEmpty(vkDnsConfig.getOptions())) {
            for (String option : vkDnsConfig.getOptions()) {
                V1PodDNSConfigOption dnsOption = new V1PodDNSConfigOption();
                options.add(dnsOption);
                if (option.contains(":")) {
                    String[] split = option.split(":");
                    if (split.length == 2) {
                        dnsOption.setName(split[0]);
                        dnsOption.setValue(split[1]);
                    }
                    continue;
                }
                dnsOption.setName(option);
            }
        }

        if (CollectionUtils.isNotEmpty(dnsConfig.getOptions())) {
            dnsConfig.getOptions().addAll(options);
        } else {
            dnsConfig.setOptions(options);
        }
    }

    /**
     * 生成kubeProxy sidecar配置信息
     *
     * @param orderExtra
     * @param containers
     * @return
     */
    private V1Container getKubeProxyContainer(BciOrderExtra orderExtra, List<V1Container> containers) {
        // 计算用户是否提交kubeProxy sidecar容器
        String kubeProxySidecarName = null;
        for (Label label : orderExtra.getLabels()) {
            if (KUBE_PROXY_SIDECAR_LABEL_KEY.equals(label.getLabelKey())) {
                kubeProxySidecarName = label.getLabelValue();
                break;
            }
        }
        if (kubeProxySidecarName == null) {
            return null;
        }

        int kubeProxyContainerIndex = -1;
        for (int i = 0; i < containers.size(); i++) {
            V1Container container = containers.get(i);
            if (kubeProxySidecarName.equals(container.getName())) {
                kubeProxyContainerIndex = i;
                break;
            }
        }
        if (kubeProxyContainerIndex == -1) {
            return null;
        }

        // 删除kubeproxy 容器从workload容器列表中
        V1Container kubeProxyContainer = containers.remove(kubeProxyContainerIndex);

        // 添加NET_ADMIN
        V1SecurityContext securityContext = new V1SecurityContext();
        V1Capabilities capabilities = new V1Capabilities();
        capabilities.add(Arrays.asList("NET_ADMIN"));
        securityContext.setCapabilities(capabilities);
        kubeProxyContainer.setSecurityContext(securityContext);
        // 开启特权模式
        securityContext.setPrivileged(true);

        String kubeProxyHealthAddress = null;
        // 是否包含健康检查端口
        for (Label label : orderExtra.getLabels()) {
            if (label.getLabelKey().equals(KUBE_PROXY_SIDECAR_HEALTHZ_ADDRESS)) {
                kubeProxyHealthAddress = label.getLabelValue();
                break;
            }
        }


        V1ExecAction exec = new V1ExecAction();
        List<String> command = new ArrayList<>();
        command.add("/bin/sh");
        command.add("-c");

        // vk 传递过来的镜像需要包含curl，没有curl 直接认为启动成功
        if (kubeProxyHealthAddress != null) {
            // 非一行的shell脚本，判断容器中是否存在curl
            command.add("nohup sh " + SIDECAR_COMMAND_MOUNT_PATH + KUBE_PROXY_CHECK_SHELL_NAME + " &");

            // 添加脚本使用的env
            if (CollectionUtils.isEmpty(kubeProxyContainer.getEnv())) {
                kubeProxyContainer.setEnv(new ArrayList<V1EnvVar>());
            }
            V1EnvVar healthAddressEnv = new V1EnvVar();
            healthAddressEnv.setName("HealthAddress");
            healthAddressEnv.setValue(kubeProxyHealthAddress);
            kubeProxyContainer.getEnv().add(healthAddressEnv);
        } else {
            command.add("echo '' > " + SIDECAR_EMPTY_DIR_MOUNT_PATH + kubeProxySidecarName);
        }

        exec.setCommand(command);
        V1Lifecycle lifecycle = new V1Lifecycle();
        V1LifecycleHandler postStartHandler = new V1LifecycleHandler();
        postStartHandler.setExec(exec);
        lifecycle.setPostStart(postStartHandler);
        kubeProxyContainer.setLifecycle(lifecycle);
        // kubeproxy sidecar不能改名,否则会和数据库里面的container name匹配不上,
        // 因为这个sidecarvk提交上来的,不是额外添加的;目前不需要改名;
        // kubeProxyContainer.setName(PodConstants.BCI_KUBEPROXY_SIDECAR_CONTAINER_PREFIX + kubeProxySidecarName);
        return kubeProxyContainer;
    }

    /**
     * create nfs sidecar containers
     * every nfs volume need a sidecar
     *
     * @param volumeName nfs volume name
     * @param nfsServer  nfs server path
     * @param sPath      nfs server side path
     * @param dPath      nfs volumeMount path (in container)
     */
    private V1Container getNfsSidecar(String volumeName, String nfsServer, String sPath,
                                      String dPath, Boolean readonly, int count) throws IOException {
        if (!sPath.startsWith("/")) {
            sPath = "/" + sPath;
        }
        if (!dPath.startsWith("/")) {
            dPath = "/" + dPath;
        }
        V1Container sdContainer = new V1Container();
        // volume mounts
        List<V1VolumeMount> vMs = new ArrayList<>();
        V1VolumeMount cvm = new V1VolumeMount();
        cvm.setName(volumeName);
        cvm.setMountPath(dPath);
        cvm.setReadOnly(false);
        cvm.setMountPropagation("Bidirectional");
        vMs.add(cvm);
        V1VolumeMount cvm2 = new V1VolumeMount();
        cvm2.setName("nfs-base");
        cvm2.setMountPath(BCI_NFS_BASE_PATH);
        cvm2.setReadOnly(false);
        cvm2.setMountPropagation("Bidirectional");
        vMs.add(cvm2);
        sdContainer.setVolumeMounts(vMs);
        String sdContainerName = PodConstants.BCI_NFS_SIDECAR_CONTAINER_PREFIX + count;
        // common config
        sdContainer.setName(sdContainerName);
        sdContainer.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);
        sdContainer.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);
        // image
        if (StringUtil.isEmpty(storageSidecarImage)) {
            sdContainer.setImage(bciStorageSidecarImage);
        } else {
            sdContainer.setImage(storageSidecarImage);
        }
        sdContainer.setImagePullPolicy(IMAGE_PULL_POLICY_IF_NOT_PRESENT);
        // set privileged true
        V1SecurityContext v1SecurityContext = new V1SecurityContext();
        v1SecurityContext.setPrivileged(true);
        v1SecurityContext.setRunAsGroup(0L);
        v1SecurityContext.setRunAsUser(0L);
        v1SecurityContext.runAsNonRoot(false);
        sdContainer.setSecurityContext(v1SecurityContext);

        // 设置readiness
//        List<String> readinessCommandList = new ArrayList<>();
//        readinessCommandList.add("/bin/sh");
//        readinessCommandList.add("-c");
//        readinessCommandList.add("nohup sh " + SIDECAR_COMMAND_MOUNT_PATH + NFS_CHECK_SHELL_NAME +" &");
//        V1Probe readinessProbe = new V1Probe();
//        readinessProbe.setTimeoutSeconds(1);
//        readinessProbe.setInitialDelaySeconds(1);
//        V1ExecAction readinessExec = new V1ExecAction();
//        readinessExec.setCommand(readinessCommandList);
//        readinessProbe.setExec(readinessExec);
//        sdContainer.setReadinessProbe(readinessProbe);


        // 设置env，供readiness探针使用
        if (CollectionUtils.isEmpty(sdContainer.getEnv())) {
            sdContainer.setEnv(new ArrayList<V1EnvVar>());
        }

        V1EnvVar nfsServerEnvVar = new V1EnvVar();
        nfsServerEnvVar.setName("nfsServer");
        nfsServerEnvVar.setValue(nfsServer);

        V1EnvVar sPathEnvVar = new V1EnvVar();
        sPathEnvVar.setName("sPath");
        sPathEnvVar.setValue(sPath);

        V1EnvVar dPathEnvVar = new V1EnvVar();
        dPathEnvVar.setName("dPath");
        dPathEnvVar.setValue(dPath);

        String nfsOptions = "";
        if (readonly) {
            nfsOptions = "ro";
        } else {
            nfsOptions = "rw";
        }
        V1EnvVar nfsOptionsEnvVar = new V1EnvVar();
        nfsOptionsEnvVar.setName("nfsOptions");
        nfsOptionsEnvVar.setValue(nfsOptions);

        V1EnvVar containerNameEnvVar = new V1EnvVar();
        containerNameEnvVar.setName("containerName");
        containerNameEnvVar.setValue(sdContainerName);

        sdContainer.getEnv().add(nfsServerEnvVar);
        sdContainer.getEnv().add(sPathEnvVar);
        sdContainer.getEnv().add(dPathEnvVar);
        sdContainer.getEnv().add(nfsOptionsEnvVar);
        sdContainer.getEnv().add(containerNameEnvVar);

        // lifecycle
        V1Lifecycle lifecycle = new V1Lifecycle();
        // preStop
        V1LifecycleHandler preStop = new V1LifecycleHandler();
        V1ExecAction preStopCmd = new V1ExecAction();
        preStopCmd.addCommandItem("/bin/nfs-mount.sh");
        preStopCmd.addCommandItem("umount");
        preStop.setExec(preStopCmd);
        lifecycle.setPreStop(preStop);

        V1LifecycleHandler postStart = new V1LifecycleHandler();
        V1ExecAction exec = new V1ExecAction();
        List<String> postStartCommandList = new ArrayList<>();
        postStartCommandList.add("/bin/sh");
        postStartCommandList.add("-c");
        postStartCommandList.add("nohup sh " + "/bin/" + NFS_CHECK_SHELL_NAME + " &");
        exec.setCommand(postStartCommandList);
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);

        sdContainer.setLifecycle(lifecycle);

        // command
        List<String> command = new ArrayList<>();
        command.add("/bin/nfs-mount.sh");
        command.add("mount");

        sdContainer.setCommand(command);
        return sdContainer;
    }

    private V1Container getCephFSSidecar(CephFSVolume cephFsVolume,
                                         VolumeMounts volumeMount,
                                         String cephFSSecret,
                                         int count) throws IOException {
        String volumeName = cephFsVolume.getName();
        String user = cephFsVolume.getUser();
        String path = cephFsVolume.getPath();
        List<String> monitors = cephFsVolume.getMonitors();
        // 暂未支持secretFile
        String secretFile = cephFsVolume.getSecretFile();
        CephFSVolume.SecretRef secretRef = cephFsVolume.getSecretRef();
        Boolean readOnly = cephFsVolume.getReadOnly();

        String cephFSAccessMode = "";
        if (readOnly) {
            cephFSAccessMode = "ro";
        } else {
            cephFSAccessMode = "rw";
        }

        String sPath = path;
        if (!sPath.startsWith("/")) {
            sPath = "/" + sPath;
        }
        String dPath = volumeMount.getMountPath();
        if (!dPath.startsWith("/")) {
            dPath = "/" + dPath;
        }
        String cephFSMonitors = String.join(",", monitors);

        V1Container sdContainer = new V1Container();
        // volume mounts
        List<V1VolumeMount> vMs = new ArrayList<>();
        V1VolumeMount cvm = new V1VolumeMount();
        cvm.setName(volumeName);
        cvm.setMountPath(dPath);
        cvm.setReadOnly(false);
        cvm.setMountPropagation("Bidirectional");
        vMs.add(cvm);

        V1VolumeMount cvm2 = new V1VolumeMount();
        cvm2.setName("cephfs-base");
        cvm2.setMountPath(BCI_CEPHFS_BASE_PATH);
        cvm2.setReadOnly(false);
        cvm2.setMountPropagation("Bidirectional");
        vMs.add(cvm2);
        sdContainer.setVolumeMounts(vMs);
        String sdContainerName = PodConstants.BCI_CEPHFS_SIDECAR_CONTAINER_PREFIX + count;
        // common config
        sdContainer.setName(sdContainerName);
        sdContainer.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);
        sdContainer.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);
        // image
        if (StringUtil.isEmpty(storageSidecarImage)) {
            sdContainer.setImage(bciStorageSidecarImage);
        } else {
            sdContainer.setImage(storageSidecarImage);
        }
        sdContainer.setImagePullPolicy(IMAGE_PULL_POLICY_IF_NOT_PRESENT);
        // set privileged true
        V1SecurityContext v1SecurityContext = new V1SecurityContext();
        v1SecurityContext.setPrivileged(true);
        v1SecurityContext.setRunAsGroup(0L);
        v1SecurityContext.setRunAsUser(0L);
        v1SecurityContext.runAsNonRoot(false);
        sdContainer.setSecurityContext(v1SecurityContext);

        // 设置env
        if (CollectionUtils.isEmpty(sdContainer.getEnv())) {
            sdContainer.setEnv(new ArrayList<V1EnvVar>());
        }

        V1EnvVar volumeNameEnvVar = new V1EnvVar();
        volumeNameEnvVar.setName("cephfsVolumeName");
        volumeNameEnvVar.setValue(volumeName);
        sdContainer.getEnv().add(volumeNameEnvVar);

        V1EnvVar userEnvVar = new V1EnvVar();
        userEnvVar.setName("cephfsUser");
        userEnvVar.setValue(user);
        sdContainer.getEnv().add(userEnvVar);

        V1EnvVar sPathEnvVar = new V1EnvVar();
        sPathEnvVar.setName("sPath");
        sPathEnvVar.setValue(sPath);
        sdContainer.getEnv().add(sPathEnvVar);

        V1EnvVar dPathEnvVar = new V1EnvVar();
        dPathEnvVar.setName("dPath");
        dPathEnvVar.setValue(dPath);
        sdContainer.getEnv().add(dPathEnvVar);

        V1EnvVar monitorsEnvVar = new V1EnvVar();
        monitorsEnvVar.setName("cephfsMonitors");
        monitorsEnvVar.setValue(cephFSMonitors);
        sdContainer.getEnv().add(monitorsEnvVar);

        V1EnvVar secretRefEnvVar = new V1EnvVar();
        secretRefEnvVar.setName("cephfsSecretRef");
        secretRefEnvVar.setValue(secretRef.getName());
        sdContainer.getEnv().add(secretRefEnvVar);

        V1EnvVar secretEnvVar = new V1EnvVar();
        secretEnvVar.setName("cephfsSecret");
        secretEnvVar.setValue(cephFSSecret);
        sdContainer.getEnv().add(secretEnvVar);

        V1EnvVar cephFSAccessModeEnvVar = new V1EnvVar();
        cephFSAccessModeEnvVar.setName("cephfsAccessMode");
        cephFSAccessModeEnvVar.setValue(cephFSAccessMode);
        sdContainer.getEnv().add(cephFSAccessModeEnvVar);

        V1EnvVar containerNameEnvVar = new V1EnvVar();
        containerNameEnvVar.setName("containerName");
        containerNameEnvVar.setValue(sdContainerName);
        sdContainer.getEnv().add(containerNameEnvVar);

        // lifecycle
        V1Lifecycle lifecycle = new V1Lifecycle();
        // preStop
        V1LifecycleHandler preStop = new V1LifecycleHandler();
        V1ExecAction preStopCmd = new V1ExecAction();
        preStopCmd.addCommandItem("/bin/cephfs-mount.sh");
        preStopCmd.addCommandItem("umount");

        preStop.setExec(preStopCmd);
        lifecycle.setPreStop(preStop);

        // postStart
        V1LifecycleHandler postStart = new V1LifecycleHandler();
        V1ExecAction exec = new V1ExecAction();
        List<String> postStartCommandList = new ArrayList<>();
        postStartCommandList.add("/bin/sh");
        postStartCommandList.add("-c");
        postStartCommandList.add("nohup sh " + "/bin/" + CEPHFS_CHECK_SHELL_NAME + " &");
        exec.setCommand(postStartCommandList);
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);

        sdContainer.setLifecycle(lifecycle);

        // command
        List<String> command = new ArrayList<>();
        command.add("/bin/cephfs-mount.sh");
        command.add("mount");

        sdContainer.setCommand(command);
        return sdContainer;
    }

    /**
     * get all nfs sidecars
     *
     * @param hostPathVs nfs sidecar need hostPath volumes
     */
    private List<V1Container> getNfsSidecarContainers(BciOrderExtra orderExtra,
                                                      List<V1Volume> hostPathVs) throws IOException {
        List<V1Container> containers = new ArrayList<>();
        // each nfs volume need a sidecar to mount
        List<String> volumeIsMount = new ArrayList<>();
        if (hostPathVs == null || hostPathVs.size() == 0) {
            return containers;
        }
        int count = 0;
        for (Nfs nfsVolume : orderExtra.getVolume().getNfs()) {
            for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
                if (containerPurchase.getVolumeMounts() == null || containerPurchase.getVolumeMounts().size() == 0) {
                    continue;
                }
                // if multie containers use same nfs volume, we only need mount once
                if (volumeIsMount.contains(nfsVolume.getName())) {
                    continue;
                }
                for (VolumeMounts vm : containerPurchase.getVolumeMounts()) {
                    // find container that used this nfs volume
                    if (vm.getName().equals(nfsVolume.getName())) {
                        String volumeName = nfsVolume.getName();
                        String nfsServer = nfsVolume.getServer();
                        String sPath = nfsVolume.getPath();
                        String dPath = vm.getMountPath();
                        Boolean readonly = nfsVolume.getReadOnly();
                        if (nfsVolume.getReadOnly() == null) {
                            readonly = false;
                        }
                        V1Container sdContainer = getNfsSidecar(volumeName, nfsServer, sPath, dPath, readonly, count);
                        containers.add(sdContainer);
                        volumeIsMount.add(nfsVolume.getName());
                        count = count + 1;
                    }
                }
            }
        }
        return containers;
    }

    private List<V1Container> getCephFSSidecarContainers(BciOrderExtra orderExtra,
                                                      List<V1Volume> hostPathVs) throws IOException {
        List<V1Container> containers = new ArrayList<>();
        // each cephfs volume need a sidecar to mount
        List<String> volumeIsMount = new ArrayList<>();
        if (hostPathVs == null || hostPathVs.size() == 0) {
            return containers;
        }
        int count = 0;
        List<ConfigFile> configFiles = orderExtra.getVolume().getConfigFile();
        for (CephFSVolume cephFsVolume : orderExtra.getVolume().getCephfs()) {
            String cephFSSecret = "";
            for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
                if (containerPurchase.getVolumeMounts() == null || containerPurchase.getVolumeMounts().size() == 0) {
                    continue;
                }
                // if multiple containers use same cephfs volume, we only need mount once
                if (volumeIsMount.contains(cephFsVolume.getName())) {
                    continue;
                }
                String cephFsVolumeName = cephFsVolume.getName();
                String cephFsVolumeSecretRefName = cephFsVolume.getSecretRef().getName();
                String cephFSConfigFileName = cephFsVolumeName + "-" + cephFsVolumeSecretRefName;
                for (ConfigFile configFile : configFiles) {
                    if (configFile.getName().equals(cephFSConfigFileName)) {
                        if (CollectionUtils.isEmpty(configFile.getConfigFiles())) {
                            continue;
                        }
                        List<ConfigFileDetail> configFileDetails = configFile.getConfigFiles();
                        String cephFSSecretRaw = configFileDetails.get(0).getFile();
                        if (StringUtils.isEmpty(cephFSSecretRaw)) {
                            continue;
                        }
                        cephFSSecret = new String(Base64.decodeBase64(cephFSSecretRaw));
                    }
                }
                for (VolumeMounts volumeMount : containerPurchase.getVolumeMounts()) {
                    // find container that used this cephfs volume
                    if (volumeMount.getName().equals(cephFsVolume.getName())) {
                        V1Container sdContainer = getCephFSSidecar(cephFsVolume, volumeMount, cephFSSecret, count);
                        containers.add(sdContainer);
                        volumeIsMount.add(cephFsVolume.getName());
                        count = count + 1;
                    }
                }
            }
        }
        return containers;
    }

    public List<V1Container> getNTPSidecarContainers(V1ObjectMeta metadata) throws IOException {
        List<V1Container> ntpContainers = new ArrayList<>();
        Map<String, String> annotations = metadata.getAnnotations();
        if (annotations == null || annotations.isEmpty()) {
            return ntpContainers;
        }
        String ntpServerIP = annotations.get(ANNOTATION_NTP_SERVER);
        if (ntpServerIP == null || ntpServerIP.isEmpty()) {
            LOGGER.debug("Annotation {} does not contain key {}, no need to inject NTP sidecar", annotations, ANNOTATION_NTP_SERVER);
            return ntpContainers;
        }

        V1Container ntpSidecar = new V1Container();
        ntpSidecar.setName(PodConstants.BCI_NTP_SIDECAR_CONTAINER_PREFIX + "0");
        ntpSidecar.setImage(bciNTPSidecarImage);
        List<String> command = new ArrayList<>();
        command.add("/bin/start.sh");
        ntpSidecar.setCommand(command);

        V1SecurityContext securityContext = new V1SecurityContext();
        securityContext.setPrivileged(true);
        ntpSidecar.setSecurityContext(securityContext);
        V1ContainerPort v1ContainerPort = new V1ContainerPortBuilder().withContainerPort(123).withProtocol("UDP").build();
        ntpSidecar.setPorts(Arrays.asList(v1ContainerPort));
        ntpSidecar.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);
        ntpSidecar.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);

        V1EnvVar ntpServerEnv = new V1EnvVar();
        ntpServerEnv.setName("NTP_SERVER");
        ntpServerEnv.setValue(ntpServerIP);
        List<V1EnvVar> envs = new ArrayList<>();
        envs.add(ntpServerEnv);
        ntpSidecar.setEnv(envs);

        V1ExecAction exec = new V1ExecAction();
        List<String> postStartCommandList = new ArrayList<>();
        postStartCommandList.add("/bin/sh");
        postStartCommandList.add("-c");
        postStartCommandList.add("nohup sh " + SIDECAR_COMMAND_MOUNT_PATH + NTP_CHECK_SHELL_NAME + " &");
        exec.setCommand(postStartCommandList);
        V1Lifecycle lifecycle = new V1Lifecycle();
        V1LifecycleHandler postStartHandler = new V1LifecycleHandler();
        postStartHandler.setExec(exec);
        lifecycle.setPostStart(postStartHandler);
        ntpSidecar.setLifecycle(lifecycle);
        ntpContainers.add(ntpSidecar);
        return ntpContainers;
    }

    /**
     * get nfs hostPath volumes
     *
     * @param
     */
    private List<V1Volume> getNfsToHostPathVolumes(BciOrderExtra orderExtra) throws IOException {
        // add hostPath for each nfs volume
        List<V1Volume> volumes = new ArrayList<>();
        List<Nfs> nfsVolumes = orderExtra.getVolume().getNfs();
        if (nfsVolumes == null || nfsVolumes.size() == 0) {
            return volumes;
        }
        for (Nfs nfsV : nfsVolumes) {
            V1Volume volume = new V1Volume();
            volume.setName(nfsV.getName());
            V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
            hostPathV.setType("DirectoryOrCreate");
            hostPathV.setPath(BCI_NFS_BASE_PATH + nfsV.getName() + orderExtra.getPodId());
            volume.setHostPath(hostPathV);
            volumes.add(volume);
        }
        // add base nfs host path
        V1Volume volume = new V1Volume();
        volume.setName("nfs-base");
        V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
        hostPathV.setType("DirectoryOrCreate");
        hostPathV.setPath(BCI_NFS_BASE_PATH);
        volume.setHostPath(hostPathV);
        volumes.add(volume);

        return volumes;
    }

    private List<V1Volume> getCephFSToHostPathVolumes(BciOrderExtra orderExtra) throws IOException {
        // add hostPath for each cephfs volume
        List<V1Volume> volumes = new ArrayList<>();
        List<CephFSVolume> cephFSVolumes = orderExtra.getVolume().getCephfs();
        if (cephFSVolumes == null || cephFSVolumes.size() == 0) {
            return volumes;
        }
        for (CephFSVolume cephFSVolume : cephFSVolumes) {
            V1Volume volume = new V1Volume();
            volume.setName(cephFSVolume.getName());
            V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
            hostPathV.setType("DirectoryOrCreate");
            hostPathV.setPath(BCI_CEPHFS_BASE_PATH + cephFSVolume.getName() + orderExtra.getPodId());
            volume.setHostPath(hostPathV);
            volumes.add(volume);
        }
        // add base cephfs host path
        V1Volume volume = new V1Volume();
        volume.setName("cephfs-base");
        V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
        hostPathV.setType("DirectoryOrCreate");
        hostPathV.setPath(BCI_CEPHFS_BASE_PATH);
        volume.setHostPath(hostPathV);
        volumes.add(volume);

        return volumes;
    }

    /**
     *  get pfs and nfs annotation
     */
    private void forV3PFSAndNFS(BciOrderExtra orderExtra, V1ObjectMeta metadata) throws IOException {
        Map<String, Map<String, NFSAnnotation>> ret = new HashMap<>();
        Map<String, NFSAnnotation> nfsAnnotations = forV3NFS(orderExtra, metadata);
        if (!nfsAnnotations.isEmpty()) {
            ret.put("nfs", nfsAnnotations);
        }

        Map<String, NFSAnnotation> pfsAnnotations = forV3PFS(orderExtra, metadata);
        if (!pfsAnnotations.isEmpty()) {
            ret.put("pfs", pfsAnnotations);
        }

        if (!ret.isEmpty()) {
            ObjectMapper objectMapper = new ObjectMapper();
            String j = objectMapper.writeValueAsString(ret);
            LOGGER.info("npfs ann: {}", j);
            metadata.getAnnotations().put("bci.cloud.baidu.com/nfs-volume", j);
        }
    }


    /**
     *  get pfs annotation
     */
    private Map<String, NFSAnnotation> forV3PFS(BciOrderExtra orderExtra, V1ObjectMeta metadata) throws IOException {
        Map<String, NFSAnnotation> pfsAnnotations = new HashMap<>();
        List<Pfs> pfsVolumes = orderExtra.getVolume().getPfs();
        if (pfsVolumes == null || pfsVolumes.size() == 0) {
            return pfsAnnotations;
        }
        for (Pfs pfsV : pfsVolumes) {
            NFSAnnotation pfsAnn = new NFSAnnotation();
            pfsAnn.setServer(pfsV.getServer());
            pfsAnn.setPath(pfsV.getPath());
            pfsAnn.setContainers(new ArrayList<NFSAnnotation.NFSContainers>());
            pfsAnn.setOptions("");
            for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
                if (containerPurchase.getVolumeMounts() == null || containerPurchase.getVolumeMounts().size() == 0) {
                    continue;
                }
                for (VolumeMounts vm : containerPurchase.getVolumeMounts()) {
                    if (vm.getName().equals(pfsV.getName())) {
                        NFSAnnotation.NFSContainers pfsC = new NFSAnnotation.NFSContainers();
                        pfsC.setPath(vm.getMountPath());
                        pfsC.setReadonly(vm.getReadOnly());
                        pfsC.setType(vm.getType());
                        pfsC.setCName(containerPurchase.getName());
                        pfsAnn.getContainers().add(pfsC);
                    }
                }
            }
            if (!pfsAnn.getContainers().isEmpty()) {
                pfsAnnotations.put(pfsV.getName(), pfsAnn);
            }
        }

        return pfsAnnotations;
    }
    /**
     * get nfs annotation
     *
     * @param
     */
    private Map<String, NFSAnnotation> forV3NFS(BciOrderExtra orderExtra, V1ObjectMeta metadata) throws IOException {
        Map<String, NFSAnnotation> nfsAnnotations = new HashMap<>();
        List<Nfs> nfsVolumes = orderExtra.getVolume().getNfs();
        if (nfsVolumes == null || nfsVolumes.size() == 0) {
            return nfsAnnotations;
        }

        for (Nfs nfsV : nfsVolumes) {
            NFSAnnotation nfsAnn = new NFSAnnotation();
            nfsAnn.setServer(nfsV.getServer());
            nfsAnn.setPath(nfsV.getPath());
            nfsAnn.setReadonly(nfsV.getReadOnly());
            nfsAnn.setContainers(new ArrayList<NFSAnnotation.NFSContainers>());
            // cfs mount options
            nfsAnn.setOptions("minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport");
            for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
                if (containerPurchase.getVolumeMounts() == null || containerPurchase.getVolumeMounts().size() == 0) {
                    continue;
                }
                for (VolumeMounts vm : containerPurchase.getVolumeMounts()) {
                    if (vm.getName().equals(nfsV.getName())) {
                        NFSAnnotation.NFSContainers nfsC = new NFSAnnotation.NFSContainers();
                        nfsC.setPath(vm.getMountPath());
                        nfsC.setReadonly(vm.getReadOnly());
                        nfsC.setType(vm.getType());
                        nfsC.setCName(containerPurchase.getName());
                        nfsAnn.getContainers().add(nfsC);
                    }
                }
            }
            if (!nfsAnn.getContainers().isEmpty()) {
                nfsAnnotations.put(nfsV.getName(), nfsAnn);
            }
        }

        return nfsAnnotations;
    }

    String getPodContainersStr(List<V1Container> podContainers, List<V1Container> initContainers) {
        String podContainetsStr = "";
        for (V1Container container : podContainers) {
            podContainetsStr += container.getName();
            podContainetsStr += ";";
        }
        for (V1Container container : initContainers) {
            podContainetsStr += container.getName();
            podContainetsStr += ";";
        }
        return podContainetsStr;
    }

    /**
     * get flex volumes
     *
     * @param orderExtra
     */
    private List<V1Volume> getFlexVolumes(BciOrderExtra orderExtra, String podContainersStr) throws IOException {
        List<V1Volume> volumes = new ArrayList<>();
        List<FlexVolume> flexVolumes = orderExtra.getVolume().getFlexVolume();
        if (flexVolumes == null || flexVolumes.size() == 0) {
            return volumes;
        }
        for (FlexVolume flexVolume : flexVolumes) {
            if (flexVolume.getName().equals("sidecar-stdout")) {
                continue;
            }
            V1Volume volume = new V1Volume();
            volume.setName(flexVolume.getName());
            volume.flexVolume(new V1FlexVolumeSource().driver(
                    flexVolume.getDriver()).putOptionsItem("pod.needMountedContainers", podContainersStr));
            volumes.add(volume);
        }
        return volumes;
    }

    /**
     * 为pod设置emptyDir
     *
     * @param orderExtra
     * @param pod
     */
    private void setEmptyDirIfPossible(BciOrderExtra orderExtra, V1Pod pod) {
        Volume volume = orderExtra.getVolume();
        if (volume == null) {
            return;
        }
        List<EmptyDir> emptyDirs = volume.getEmptyDir();
        if (CollectionUtils.isEmpty(emptyDirs)) {
            return;
        }

        for (EmptyDir emptyDir : emptyDirs) {
            pod.getSpec().getVolumes().add(convertToK8sEmptyDir(emptyDir));
        }
    }

    public static V1Volume convertToK8sEmptyDir(EmptyDir emptyDir) {
        V1Volume k8sVolume = new V1Volume();
        k8sVolume.setName(emptyDir.getName());
        V1EmptyDirVolumeSource emptyDirSrc = new V1EmptyDirVolumeSource();
        emptyDirSrc.setMedium(emptyDir.getMedium());
        if (emptyDir.getSizeLimit() != null && emptyDir.getSizeLimit() != 0) {
            emptyDirSrc.setSizeLimit(Quantity.fromString(emptyDir.getSizeLimit() + "Gi"));
        }
        k8sVolume.setEmptyDir(emptyDirSrc);
        return k8sVolume;
    }

    public static V1Volume convertToK8sHostPath(HostPathVolume hostPath) {
        V1Volume k8sVolume = new V1Volume();
        k8sVolume.setName(hostPath.getName());
        V1HostPathVolumeSource hostPathSrc = new V1HostPathVolumeSource();
        hostPathSrc.setPath(hostPath.getPath());
        hostPathSrc.setType(hostPath.getType());
        k8sVolume.setHostPath(hostPathSrc);
        return k8sVolume;
    }

    /**
     * 为pod设置hostPath
     *
     * @param orderExtra
     * @param pod
     */
    public void setHostPathIfPossible(BciOrderExtra orderExtra, V1Pod pod) {
        Volume volume = orderExtra.getVolume();
        if (volume == null) {
            return;
        }
        List<HostPathVolume> hostPaths = volume.getHostPath();
        if (CollectionUtils.isEmpty(hostPaths)) {
            return;
        }

        for (HostPathVolume hostPath : hostPaths) {
            V1Volume k8sVolume = new V1Volume();
            k8sVolume.setName(hostPath.getName());
            V1HostPathVolumeSource hostPathSrc = new V1HostPathVolumeSource();
            hostPathSrc.setPath(hostPath.getPath());
            hostPathSrc.setType(hostPath.getType());
            k8sVolume.setHostPath(hostPathSrc);
            pod.getSpec().getVolumes().add(k8sVolume);
        }
    }

    /**
     * vk 传递过来的configFile 是base64 encode过的，configMap和secret都转换成了ConfigFile
     * 传递过来的ConfigFile有可能是字符串，也有可能是二进制。
     *
     * @param orderExtra
     * @param pod
     * @throws K8sServiceException
     * @throws ApiException
     */
    private void createPodConfigMapIfPossible(BciOrderExtra orderExtra, V1Pod pod) throws K8sServiceException,
            ApiException {
        if (orderExtra.getVolume() == null) {
            return;
        }
        // 创建configMap
        List<ConfigFile> configMapList = orderExtra.getVolume().getConfigFile();
        if (CollectionUtils.isEmpty(configMapList)) {
            return;
        }

        for (ConfigFile config : configMapList) {
            V1Volume k8sVolume = createAndReturnConfigMapVolume(config, pod.getMetadata().getNamespace(),
                                                                pod.getMetadata().getName());
            pod.getSpec().getVolumes().add(k8sVolume);
        }
    }

    public V1Volume createAndReturnConfigMapVolume(ConfigFile config, String podNamespace,
                                                   String podName) throws K8sServiceException, ApiException {
        String configMapName = PodUtils.buildConfigMapName(podName, config.getName());
        // 封装k8sConfigMap
        V1ConfigMap k8sConfigMap = PodUtils.buildV1ConfigMap(podNamespace, configMapName, config);

        // 创建configMap
        k8sService.createConfigMap(k8sConfigMap);

        // 封装volume
        V1Volume k8sVolume = new V1Volume();
        k8sVolume.setName(config.getName());

        V1ConfigMapVolumeSource configMapSource = new V1ConfigMapVolumeSource();
        // 封装configmap items，如vk 传递的path 携带子路径(path/data)，则转换成item格式
        List<V1KeyToPath> keyToPaths = new ArrayList<>();
        boolean hasSubPath = false;
        for (ConfigFileDetail configFileDetail : config.getConfigFiles()) {
            if (configFileDetail.hasSubPath()) {
                hasSubPath = true;
                break;
            }
        }
        // 当存在subPath时，需要将path/data 转换成item格式
        // 举例：如客户使用projected volume，使用了configmap 和 secret，secret 指定了subPath configmap 没有指定subPath
        // projected:
        //          sources:
        //          - configMap:
        //              name: nginx-conf-wy-test
        //          - secret:
        //              name: wy-secret
        //              items:
        //                - key: username
        //                  path: a1/a2/my-group
        //                - key: password
        //                  path: b1/b2/my-group
        if (hasSubPath) {
            for (ConfigFileDetail configFileDetail : config.getConfigFiles()) {
                V1KeyToPath keyToPath = new V1KeyToPath();
                keyToPath.setKey(configFileDetail.buildConfigKeyFromPath());
                keyToPath.setPath(configFileDetail.getPath());
                keyToPaths.add(keyToPath);
            }
        }

        configMapSource.setItems(keyToPaths);
        configMapSource.setName(configMapName);
        if (config.getDefaultMode() != null) {
            configMapSource.setDefaultMode(config.getDefaultMode());
        }

        k8sVolume.setConfigMap(configMapSource);
        return k8sVolume;
    }

    /**
     * 更新订单为创建中
     *
     * @param client
     * @param order
     * @return
     */
    private boolean updateOrderToCreatingStatus(OrderClient client, Order order) {
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(OrderStatus.CREATING);
        boolean updateOrderResult = bestEffortUpdateOrder(client, order.getUuid(), updateOrderRequest);
        if (!updateOrderResult) {
            LOGGER.error("update order status to creating failed, orderId is {}", order.getUuid());
        }
        return updateOrderResult;
    }

    /**
     * 更新订单状态
     *
     * @param orderId
     * @param updateOrderRequest
     * @return
     */

    public boolean updateOrderStatus(String orderId, UpdateOrderRequest updateOrderRequest) {
        OrderClient orderClient = logicPodClientFactory.createOrderClient();
        return bestEffortUpdateOrder(orderClient, orderId, updateOrderRequest);
    }

    /**
     * 更新订单，若失败重试三次
     *
     * @param client
     * @param orderId
     * @param updateOrderRequest
     */
    private boolean bestEffortUpdateOrder(OrderClient client, String orderId, UpdateOrderRequest updateOrderRequest) {
        int count = RETRY_NUM;
        boolean flag = false;
        while (count-- > 0) {
            try {
                client.update(orderId, updateOrderRequest);
                flag = true;
                break;
            } catch (Exception ex) {
                LOGGER.error("Update order status error, retry {}, orderId is {}, exception is {}", count, orderId, ex);
            }
        }
        return flag;
    }

    /**
     * 更新订单为创建失败 & 发送创建失败的短信
     *
     * @param orderClient
     * @param order
     */
    public boolean updateOrderToFailedStatus(String accountID, OrderClient orderClient, Order order,
                                             ExecutionResult executionResult, String summary, String detail) {
        LOGGER.error("updateOrderToFailedStatus BCI_LIFECYCLE_POD_FAILED order:{}, summary:{}, detail:{}",
                order.getUuid(), summary, detail);
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
        boolean updateOrderResult = bestEffortUpdateOrder(orderClient, order.getUuid(), updateOrderRequest);
        if (!updateOrderResult) {
            LOGGER.error("update order status to create failed failed, orderId:{}", order.getUuid());
        }
        FaultTrace faultTrace = new FaultTrace(summary, detail);
        executionResult.setFaultTrace(faultTrace);
        try {
//            OrderFailedContentVar failedContentVar = new OrderFailedContentVar();
//            failedContentVar.setOrderId(order.getUuid());
//            failedContentVar.setServiceType(PodConstants.SERVICE_TYPE);
//
//            String contentVar = new ObjectMapper().writeValueAsString(failedContentVar);
//            executionResult.appendMessageCenterModel(new MessageCenterModel(smsCreateFailId, contentVar,
//                    order.getAccountId(), ReceiverType.UserId, "create failed"));
            executionResult.setExecutionStatus(ExecutionStatus.FAILURE);
            prometheusMetricsService.podCreateRecord(accountID, PrometheusMetricsService.PodCreatePhase.ASYNC,
                    PrometheusMetricsService.PodCreateResult.FAILED);
        } catch (Exception e) {
            LOGGER.warn("send mail failed, order:{} exception is {}", order.getUuid(), e);
        }
        return updateOrderResult;
    }

    /**
     * 虚机查询创建进度
     *
     * @param accountId
     * @param orderId
     * @return
     */
    private TransactionDetailResponse queryCreateServerProcessByTransactionId(String accountId, String orderId) {
        int count = RETRY_NUM;
        PodClient podClient = logicPodClientFactory.createPodClient(accountId);
        TransactionDetailResponse transactionDetail = null;
        while (count-- > 0) {
            try {
                transactionDetail = podClient.showTransaction(orderId);
                if (transactionDetail != null) {
                    break;
                }
            } catch (Exception ex) {
                LOGGER.error("Query pod create status, retry {}, orderId: {}, exception: {}", count, orderId, ex);
            }
        }
        return transactionDetail;
    }

    private boolean createRelatedObjectAndUpdateOrderToCreated(String accountID, OrderClient orderClient,
                                                            Order order, List<PodPO> pods,
                                                            HashMap<Pair<String, String>, V1Pod> nsV1PodMap,
                                                            ExecutionResult executionResult) {
        boolean updateOrderResult = false;
        List<Order.Item> eipItems = getItemsByServiceType(order, ServiceType.EIP.name());
        if (CollectionUtils.isEmpty(eipItems)) {
            updateOrderResult = updateBccOrderToCreated(accountID,
                    orderClient, order, pods, nsV1PodMap, null, executionResult);
            if (updateOrderResult) {
                prometheusMetricsService.podCreateRecord(accountID,
                        PrometheusMetricsService.PodCreatePhase.ASYNC,
                        PrometheusMetricsService.PodCreateResult.SUCCESS);
                LOGGER.info("update order to created success, orderId is {}", order.getUuid());
            } else {
                LOGGER.error("update order to created failed, orderId is {}", order.getUuid());
            }
        }
        return updateOrderResult;
    }

    // 根据 v1pod annotation 中 physcial zone 信息更新订单中 flavor，使得billing侧预留实例券能正常抵扣
    public void updateOrderFlavorByPodPhysicalZone(Order order, List<PodPO> pods,
                                                    HashMap<Pair<String, String>, V1Pod> nsV1Pods) {
        OrderFlavorUpdateRequest request = new OrderFlavorUpdateRequest();
        List<OrderFlavorUpdateItem> items = new ArrayList<>();
        // 更新订单pod地域
        for (PodPO pod : pods) {
            V1Pod k8sPod = nsV1Pods.get(new Pair<>(pod.getUserId(), pod.getPodId()));
            // 如果 k8s pod 不存在，这里不处理，在更新订单到created时会将资源标记为invalid
            if (k8sPod != null) {
                String physicalZone = "";
                // 考虑到多可用区场景，这里从 annotation 拿物理可用区信息，而不是从 podPO 获取
                if (k8sPod.getMetadata().getAnnotations() != null) {
                    if (k8sPod.getMetadata().getAnnotations().get(BCI_INTERNAL_PREFIX +
                            "PhysicalZone") != null) {
                        physicalZone = k8sPod.getMetadata().getAnnotations().get(
                            BCI_INTERNAL_PREFIX + "PhysicalZone");
                    }
                }

                // 如果有物理机区域信息，且是 gpu pod，更新订单中的flavor
                if (!StringUtils.isBlank(physicalZone)) {
                    if (pod.getGpuType() != null && !pod.getGpuType().isEmpty() && pod.getGpuCount() != 0 &&
                            pod.getCpt1()) {
                        OrderFlavorUpdateItem item = new OrderFlavorUpdateItem();
                        item.setOrderId(order.getUuid());
                        item.setOrderItemKey(ORDER_KEY + "-" + pod.getPodId());
                        List<ChargeItem> flavors = new ArrayList<>();
                        ChargeItem flavor = new ChargeItem();
                        flavor.setName("physical_zone");
                        flavor.setValue(physicalZone);
                        flavor.setScale(new BigDecimal(1));
                        flavors.add(flavor);
                        item.setFlavors(flavors);
                        items.add(item);
                    }
                }
            }
        }
        if (items.size() == 0) {
            return;
        }
        request.setRequestItems(items);
        OrderClientV2 orderClient = logicPodClientFactory.createOrderClientV2AsBceService();
        orderClient.updateOrderFlavors(request);
    }

    private List<Order.Item> getItemsByServiceType(Order serverOrder, String serviceType) {
        List<Order.Item> items = new ArrayList<>();
        for (Order.Item item : serverOrder.getItems()) {
            if (item.getServiceType().equalsIgnoreCase(serviceType)) {
                items.add(item);
            }
        }
        return items;
    }

    /**
     * 更新创建bci订单为CREATED
     *
     * @param orderClient
     * @param
     * @return
     */
    private boolean updateBccOrderToCreated(String accountID, OrderClient orderClient, Order order, List<PodPO> pods,
                                         HashMap<Pair<String, String>, V1Pod> nsV1Pods,
                                         List<EipResponse.SingleEipResponse> eipBindQueryResponses,
                                         ExecutionResult executionResult) {
        boolean updateOrdertoCreatedResult = false;
        List<ResourceMapping> resourceMappingList = new ArrayList<>();

        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(OrderStatus.CREATED);

        if (CollectionUtils.isNotEmpty(eipBindQueryResponses)) {
            for (EipResponse.SingleEipResponse eipBindResponse : eipBindQueryResponses) {
                ResourceMapping resourceMapping = new ResourceMapping();
                resourceMapping.setKey("eip1");
                resourceMapping.setStatus(ResourceStatus.RUNNING);
                resourceMapping.setId(eipBindResponse.getEncryptId());
                resourceMapping.setShortId(eipBindResponse.getAllocationId());
                resourceMappingList.add(resourceMapping);
            }
        }

        for (PodPO pod : pods) {
            V1Pod k8sPod = nsV1Pods.get(new Pair<>(pod.getUserId(), pod.getPodId()));
            ResourceMapping resourceMapping = new ResourceMapping();
            resourceMapping.setKey(ORDER_KEY + "-" + pod.getPodId());
            if (k8sPod != null) {
                resourceMapping.setStatus(getResourceStatus(k8sPod, pod));
                resourceMapping.setId(k8sPod.getMetadata().getUid());
            } else {
                resourceMapping.setStatus(ResourceStatus.INVALID);
                // 因为该pod资源没有k8s资源id，因此临时用podID(不指定ID billing会报错)
                resourceMapping.setId(pod.getPodId());
            }
            // 无论k8s是否创建成功都要绑定ShortId，后续需要根据此依据对应resource
            resourceMapping.setShortId(pod.getPodId());
            resourceMappingList.add(resourceMapping);
        }
        updateOrderRequest.setResources(resourceMappingList);

        int count = 3;
        boolean updateSucceed = false;
        String exMessage = "";
        while (count-- > 0) {
            try {
                orderClient.update(order.getUuid(), updateOrderRequest);
                updateSucceed = true;
                break;
            } catch (BceInternalResponseException ex) {
                LOGGER.error("Update bcc order status error, retry {}, orderId is {}, exception is {}",
                        count, order.getUuid(), ex);
                exMessage = ex.getMessage();
            }
        }

        if (updateSucceed) {
            updateOrdertoCreatedResult = true;
            LOGGER.debug("BCI_LIFECYCLE_PUT_ORDER_CREATED update order to created, order:{}, resources:{}",
                    order.getUuid(), JsonUtil.toJSON(updateOrderRequest));
            sendServerCreatedMsg(pods, order, executionResult);
        } else {
            LOGGER.error("BCI_LIFECYCLE_PUT_ORDER_CREATED update order to created failed, order:{}, resources:{},"
                    + " and will update order to failed status",
                    order.getUuid(), JsonUtil.toJSON(updateOrderRequest));
            updateOrderToFailedStatus(accountID, orderClient, order, executionResult,
                    ResourceRecycleReason.ORDER_UPDATE_ORDER_FAIL.toString(),
                    exMessage);
        }
        return updateOrdertoCreatedResult;
    }

    public ResourceStatus getResourceStatus(V1Pod k8sPod, PodPO podPO) {
        if (k8sPod.getStatus().getPhase().equalsIgnoreCase((BciStatus.RUNNING.getStatus()))) {
            return ResourceStatus.RUNNING;
        } else if (k8sPod.getStatus().getPhase().equalsIgnoreCase((BciStatus.SUCCEEDED.getStatus()))) {
            if (podPO.getCpt1()) {
                if (PodUtils.getChargeStatusOfCpt1Instance(
                    BciStatus.SUCCEEDED.getStatus(), podPO.getDelayReleaseDurationMinute(),
                    podPO.isDelayReleaseSucceeded(), podPO.getResourceRecycleTimestamp(),
                    podPO.getResourceRecycleComplete(),
                    podPO.getResourceRecycleReason()).equalsIgnoreCase(PodConstants.CHARGE)) {
                        return ResourceStatus.RUNNING;
                    }
                else {
                    return ResourceStatus.INVALID;
                }
            } else {
                return ResourceStatus.RUNNING;
            }
        } else {
            return ResourceStatus.INVALID;
        }
    }

    /**
     * 发送手机短信
     *
     * @param pods
     * @param order
     * @param executionResult
     */
    private void sendServerCreatedMsg(List<PodPO> pods, Order order, ExecutionResult executionResult) {
        try {
            BciOrderExtra orderExtra = PodUtils.getOrderExtra(PodUtils.getExtraByServiceType(order,
                    PodConstants.SERVICE_TYPE));
            StringBuilder userName = new StringBuilder("root");
            // 目前使用第一个item的region信息
            String region = getRegionDesc(order.getItems().get(0).getRegion());
            if (StringUtils.isNotEmpty(region)) {
                userName.append("，地域：").append(region);
            }
            // userName.append("，可用区：").append(region).append("-").append(orderExtra.getLogicalZone().replaceAll("zone",
            //        ""));
            userName.append("。其他实例信息详见控制台");

            OrderSuccessContentVar createContentVar = new OrderSuccessContentVar();
            createContentVar.setCount(String.valueOf(pods.size()));
            createContentVar.setServiceType("BCI");
            createContentVar.setInstances(getInstanceInfos(pods));
            createContentVar.setUsername(userName.toString());
            createContentVar.setExtraInfo("");

            executionResult.appendMessageCenterModel(new MessageCenterModel(smsCreateSuccessId,
                    new ObjectMapper().writeValueAsString(createContentVar), order.getAccountId(),
                    ReceiverType.UserId, "create sucess"));
            executionResult.setExecutionStatus(ExecutionStatus.SUCCESS);
            LOGGER.debug("send message success, orderId is {}", order.getUuid());
        } catch (Exception e) {
            LOGGER.error("send message error, orderId is {}, exception is {}", order.getUuid(), e);
        }
    }

    /**
     * 获取region的中文描述
     *
     * @param region
     * @return
     */
    private String getRegionDesc(String region) {
        for (RegionConstant regionConstant : RegionConstant.values()) {
            if (regionConstant.getName().equalsIgnoreCase(region)) {
                return regionConstant.getChineseName();
            }
        }
        return region;
    }

    private String getInstanceInfos(List<PodPO> pods) {
        StringBuilder instanceInfos = new StringBuilder("");
        for (PodPO pod : pods) {
            instanceInfos.append("容器组").append("名称：").append(pod.getName());
            String network = "私网IP:" + pod.getInternalIp();
            instanceInfos.append(",").append(network).append(";");
        }
        return instanceInfos.toString();
    }

    // bind eip
    private Boolean bindEip(String accountID, V1Pod pod, EniClient eniClient, LogicEipClient eipClient) {
        String podIp = pod.getStatus().getPodIP();
        String vpcId = pod.getMetadata().getAnnotations()
                .get("cross-vpc-eni.cce.io/vpcID");
        String eipIp = pod.getMetadata().getAnnotations()
                .get("cross-vpc-eni.cce.io/eipPurchase");
        try {
            // 1. get eniID
            // 2. check already bind eip or not
            QueryEniSelfResponse eniInfo = eniClient.getEniByPrivateIp(vpcId, podIp);
            if (eniInfo == null) {
                LOGGER.error("bindEip: query eni failed, account {}, pod {}, vpc {}", accountID, podIp, vpcId);
                return true;
            }
            if (StringUtils.isNotEmpty(eniInfo.getPublicIp())) {
                LOGGER.debug("bindEip: already bind eip, account {}, pod {}, eip {}",
                        accountID, podIp, eniInfo.getPublicIp());
                // check eip
                if (!StringUtils.equals(eniInfo.getPublicIp(), eipIp)) {
                    eipClient.forceReleaseEip(eipIp);
                    LOGGER.debug("bindEip: already bind eip then relase eip, account {}, pod {}, eip {} {}",
                            accountID, podIp, eniInfo.getPublicIp(), eipIp);
                }
                return true;
            }
            // 3. check eip is ready to attach
            EipListResponse eipListResponse = eipClient.queryEip(eipIp);
            if (eipListResponse == null || CollectionUtils.isEmpty(eipListResponse.getEipList())) {
                LOGGER.error("bindEip: query eip failed, account {}, pod {}, eip {}", accountID, podIp, eipIp);
                return true;
            }
            // state: creating
            if (!eipListResponse.getEipList().get(0).getStatus().equals("available")) {
                LOGGER.debug("wait eip be available, eip {} state {}", eipIp,
                        eipListResponse.getEipList().get(0).getStatus());
                return true;
            }

            // 4. bind eip
            eniClient.attachEip(eniInfo.getEniId(), podIp, eipIp);
            LOGGER.debug("bindEip: bind eip ok, account {}, pod {}, eip {}",
                    accountID, podIp, eipIp);
            return true;
        } catch (Exception ex) {
            LOGGER.error("bindEip error, account {}, pod {}, vpc {}, eip {}, exception is {}",
                    accountID, podIp, vpcId, eipIp, ex);
            // 5. if bind failed, release eip
            if (StringUtils.isNotEmpty(eipIp)) {
                eipClient.forceReleaseEip(eipIp);
            }
            return false;
        }
    }

    // check eip state
    // return: 0 binding, 1 ok, -1 fail
    private int checkEip(String accountID, V1Pod pod, EniClient eniClient, LogicEipClient eipClient) {
        String podId = pod.getMetadata().getName();
        String podIp = pod.getStatus().getPodIP();
        try {
            ControllerBindEipState eipBindState = null;
            String eipBindStateStr = pod.getMetadata().getAnnotations().get("cross-vpc-eni.cce.io/eipState");
            if (StringUtils.isNotEmpty(eipBindStateStr)) {
                eipBindState = new ObjectMapper().readValue(eipBindStateStr, ControllerBindEipState.class);
                if (StringUtils.isNotEmpty(eipBindState.getState()) && eipBindState.getState().equals("binded")) {
                    return 1;
                }
                if (eipBindState.getCreateRetrytimes() >= eipBindRetryMaxTimes) {
                    LOGGER.error("checkEip: bindEip retry times exceed {}, pod {}/{} {}, then release eip {}",
                            eipBindState.getCreateRetrytimes(), accountID, podId, podIp, eipBindState.getEip());
                    return -1;
                }
            }
            LOGGER.debug("checkEip next, pod {}/{}, {}", accountID, podId, eipBindStateStr);
            return 0;
        } catch (Exception ex) {
            LOGGER.error("checkEip error, pod {}/{} {}, exception is {}", accountID, podId, podIp, ex);
            return 0;
        }
    }

    /**
     * 生成EXEC探针需要的Pod挂载信息
     */
    private List<V1Volume> getContainerProbeVolumes(BciOrderExtra orderExtra) {
        List<V1Volume> volumes = new ArrayList<>();
        for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
            if (isChangeContainerProbe(containerPurchase)) {
                V1Volume probeMount = new V1Volume();
                probeMount.setName(BCI_PROBE_VOLUME_MOUNT_NAME);
                V1HostPathVolumeSource volumeSource = new V1HostPathVolumeSource();
                volumeSource.setPath(BCI_PROBE_VOLUME_HOST_PATH);
                probeMount.setHostPath(volumeSource);
                volumes.add(probeMount);
                return volumes;
            }
        }
        return volumes;
    }

    /**
     * 生成EXEC探针需要的容器挂载信息
     */
    private V1VolumeMount parseContainerProbeMounts() {
        V1VolumeMount probeMount = new V1VolumeMount();
        probeMount.setName(BCI_PROBE_VOLUME_MOUNT_NAME);
        probeMount.setMountPath(BCI_PROBE_VOLUME_CONTAINER_PATH);
        return probeMount;
    }

    /**
     * 生成容器EXEC探针需要的PodIP的环境变量
     */
    private V1EnvVar parseContainerProbeEnv() {
        V1EnvVarSource valueFrom = new V1EnvVarSource();
        V1ObjectFieldSelector fieldRef = new V1ObjectFieldSelector();
        fieldRef.setFieldPath("status.podIP");
        valueFrom.setFieldRef(fieldRef);

        V1EnvVar env = new V1EnvVar();
        env.setName(BCI_PROBE_ENV_NAME);
        env.setValueFrom(valueFrom);
        return env;
    }

    /**
     * 将HTTP和TCP探针转换成EXEC
     *
     * @param containerProbe
     */
    private V1Probe changeContainerProbe(Probe containerProbe) {
        if (containerProbe == null) {
            return null;
        }
        HTTPGetAction httpGet = containerProbe.getHttpGet();
        TCPSocketAction tcpSocket = containerProbe.getTcpSocket();
        V1ExecAction exec = new V1ExecAction();
        V1Probe probe = setContainerProbeParam(containerProbe);
        if (httpGet != null) {
            List<String> command = new ArrayList<>();
            command.add(BCI_PROBE_VOLUME_CONTAINER_PATH + BCI_PROBE_BIN_NAME);
            command.add("-probType");
            command.add("httpGet");
            command.add("-port");
            command.add(String.valueOf(httpGet.getPort()));
            if (httpGet.getHost() != null) {
                command.add("-host");
                command.add(httpGet.getHost());
            }
            if (httpGet.getPath() != null) {
                command.add("-path");
                command.add(httpGet.getPath());
            }
            if (httpGet.getScheme() != null) {
                command.add("-scheme");
                command.add(httpGet.getScheme());
            }
            if (httpGet.getHttpHeaders() != null) {
                command.add("-header");
                String str = "";
                for (HTTPHeader header : httpGet.getHttpHeaders()) {
                    str = str + header.getName() + ",";
                    str = str + header.getValue() + "|";
                }
                if (str.length() != 0) {
                    str = str.substring(0, str.length() - 1);
                }
                command.add(str);
            }
            if (containerProbe.getTimeoutSeconds() != 0) {
                command.add("-timeout");
                command.add(String.valueOf(containerProbe.getTimeoutSeconds()));
            }
            exec.setCommand(command);
            probe.setExec(exec);
        } else if (tcpSocket != null) {
            List<String> command = new ArrayList<>();
            command.add(BCI_PROBE_VOLUME_CONTAINER_PATH + BCI_PROBE_BIN_NAME);
            command.add("-probType");
            command.add("tcpSocket");
            command.add("-port");
            command.add(String.valueOf(tcpSocket.getPort()));
            if (tcpSocket.getHost() != null) {
                command.add("-host");
                command.add(tcpSocket.getHost());
            }
            if (containerProbe.getTimeoutSeconds() != 0) {
                command.add("-timeout");
                command.add(String.valueOf(containerProbe.getTimeoutSeconds()));
            }
            exec.setCommand(command);
            probe.setExec(exec);
        } else if (containerProbe.getExec() != null) {
            exec.setCommand(containerProbe.getExec().getCommand());
            probe.setExec(exec);
        }

        return probe;
    }

    /**
     * 设置Probe中非Handler参数
     *
     * @param containerProbe
     */
    private V1Probe setContainerProbeParam(Probe containerProbe) {
        if (containerProbe == null) {
            return null;
        }
        V1Probe probe = new V1Probe();
        if (containerProbe.getFailureThreshold() != 0) {
            probe.setFailureThreshold(containerProbe.getFailureThreshold());
        }
        if (containerProbe.getInitialDelaySeconds() != 0) {
            probe.setInitialDelaySeconds(containerProbe.getInitialDelaySeconds());
        }
        if (containerProbe.getPeriodSeconds() != 0) {
            probe.setPeriodSeconds(containerProbe.getPeriodSeconds());
        }
        if (containerProbe.getSuccessThreshold() != 0) {
            probe.setSuccessThreshold(containerProbe.getSuccessThreshold());
        }
        if (containerProbe.getTerminationGracePeriodSeconds() != 0) {
            probe.setTerminationGracePeriodSeconds(containerProbe.getTerminationGracePeriodSeconds());
        }
        if (containerProbe.getTimeoutSeconds() != 0) {
            probe.setTimeoutSeconds(containerProbe.getTimeoutSeconds());
        }
        return probe;
    }

    /**
     * 判断容器是否需要将HTTP和TCP的探针转换成EXEC
     *
     * @param containerPurchase
     */
    private boolean isChangeContainerProbe(ContainerPurchase containerPurchase) {
        List<Probe> probes = new ArrayList<>();
        if (containerPurchase.getLivenessProbe() != null) {
            probes.add(containerPurchase.getLivenessProbe());
        }
        if (containerPurchase.getStartupProbe() != null) {
            probes.add(containerPurchase.getStartupProbe());
        }
        if (containerPurchase.getReadinessProbe() != null) {
            probes.add(containerPurchase.getReadinessProbe());
        }

        for (Probe probe : probes) {
            if (probe.getHttpGet() != null || probe.getTcpSocket() != null) {
                return true;
            }
        }
        return false;
    }

    public List<V1Volume> getPfsToHostPathVolumes(BciOrderExtra orderExtra) throws IOException {
        // add hostPath for each pfs volume
        List<V1Volume> volumes = new ArrayList<>();
        List<Pfs> pfsVolumes = orderExtra.getVolume().getPfs();
        if (pfsVolumes == null || pfsVolumes.size() == 0) {
            return volumes;
        }
        for (Pfs pfsV : pfsVolumes) {
            V1Volume volume = new V1Volume();
            volume.setName(pfsV.getName());
            V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
            hostPathV.setType("DirectoryOrCreate");
            hostPathV.setPath(BCI_PFS_BASE_PATH + pfsV.getName() + orderExtra.getPodId());
            volume.setHostPath(hostPathV);
            volumes.add(volume);
        }
        // add base pfs host path
        V1Volume volume = new V1Volume();
        volume.setName("pfs-base");
        V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
        hostPathV.setType("DirectoryOrCreate");
        hostPathV.setPath(BCI_PFS_BASE_PATH);
        volume.setHostPath(hostPathV);
        volumes.add(volume);

        return volumes;
    }

    public List<V1Volume> getBosToHostPathVolumes(BciOrderExtra orderExtra) throws IOException {
        // add hostPath for each bos volume
        List<V1Volume> volumes = new ArrayList<>();
        List<Bos> bosVolumes = orderExtra.getVolume().getBos();
        if (bosVolumes == null || bosVolumes.size() == 0) {
            return volumes;
        }
        for (Bos bosV : bosVolumes) {
            V1Volume volume = new V1Volume();
            volume.setName(bosV.getName());
            V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
            hostPathV.setType("DirectoryOrCreate");
            hostPathV.setPath(BCI_BOS_BASE_PATH + bosV.getName() + orderExtra.getPodId());
            volume.setHostPath(hostPathV);
            volumes.add(volume);
        }
        // add base bos host path
        V1Volume volume = new V1Volume();
        volume.setName("bos-base");
        V1HostPathVolumeSource hostPathV = new V1HostPathVolumeSource();
        hostPathV.setType("DirectoryOrCreate");
        hostPathV.setPath(BCI_BOS_BASE_PATH);
        volume.setHostPath(hostPathV);
        volumes.add(volume);

        return volumes;
    }


    public List<V1Container> getPfsSidecarContainers(BciOrderExtra orderExtra,
                                                            List<V1Volume> hostPathVs) throws IOException {
        List<V1Container> containers = new ArrayList<>();
        // each pfs volume need a sidecar to mount
        List<String> volumeIsMount = new ArrayList<>();
        if (hostPathVs == null || hostPathVs.size() == 0) {
            return containers;
        }
        int count = 0;
        for (Pfs pfsVolume : orderExtra.getVolume().getPfs()) {
            for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
                if (containerPurchase.getVolumeMounts() == null || containerPurchase.getVolumeMounts().size() == 0) {
                    continue;
                }
                // if multie containers use same pfs volume, we only need mount once
                if (volumeIsMount.contains(pfsVolume.getName())) {
                    continue;
                }
                for (VolumeMounts vm : containerPurchase.getVolumeMounts()) {
                    // find container that used this pfs volume
                    if (vm.getName().equals(pfsVolume.getName())) {
                        String volumeName = pfsVolume.getName();
                        String pfsServer = pfsVolume.getServer();
                        String sPath = pfsVolume.getPath();
                        String dPath = BCI_PFS_BASE_PATH + volumeName + orderExtra.getPodId();

                        V1Container sdContainer = getPfsSidecar(volumeName, pfsServer, sPath, dPath, count);
                        containers.add(sdContainer);
                        volumeIsMount.add(pfsVolume.getName());
                        count = count + 1;
                    }
                }
            }
        }
        return containers;
    }

    public List<V1Container> getBosSidecarContainers(BciOrderExtra orderExtra,
                                                            List<V1Volume> hostPathVs) throws IOException {
        List<V1Container> containers = new ArrayList<>();
        // each bos volume need a sidecar to mount
        List<String> volumeIsMount = new ArrayList<>();
        if (hostPathVs == null || hostPathVs.size() == 0) {
            return containers;
        }
        int count = 0;
        for (Bos bosVolume : orderExtra.getVolume().getBos()) {
            for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
                if (containerPurchase.getVolumeMounts() == null || containerPurchase.getVolumeMounts().size() == 0) {
                    continue;
                }
                // if multie containers use same bos volume, we only need mount once
                if (volumeIsMount.contains(bosVolume.getName())) {
                    continue;
                }
                for (VolumeMounts vm : containerPurchase.getVolumeMounts()) {
                    // find container that used this bos volume
                    if (vm.getName().equals(bosVolume.getName())) {
                        String volumeName = bosVolume.getName();
                        String bucket = bosVolume.getBucket();
                        String url = bosVolume.getUrl();
                        String otherOpts = bosVolume.getOtherOpts();
                        String ak = bosVolume.getAk();
                        String sk = bosVolume.getSk();
                        Boolean readOnly = bosVolume.getReadOnly();
                        if (bosVolume.getReadOnly() == null) {
                            readOnly = false;
                        }
                        String dPath = BCI_BOS_BASE_PATH + volumeName + orderExtra.getPodId();

                        V1Container sdContainer = getBosSidecar(volumeName, bucket, url, otherOpts, ak, sk, readOnly, dPath, count);
                        containers.add(sdContainer);
                        volumeIsMount.add(bosVolume.getName());
                        count = count + 1;
                    }
                }
            }
        }
        return containers;
    }

    /**
     * create bos sidecar containers
     * every bos volume need a sidecar
     *
     * @param volumeName    bos volume name
     * @param bucket        bos bucket
     * @param url           bos url
     * @param otherOpts     bos other opts
     * @param ak            bos ak
     * @param sk            bos sk
     * @param readOnly      bos readOnly
     * @param dPath         bos volumeMount path (in container)
     */
    public V1Container getBosSidecar(String volumeName, String bucket,
                                    String url, String otherOpts, String ak, String sk,
                                    Boolean readOnly, String dPath, int count) throws IOException {
        if (!dPath.startsWith("/")) {
            dPath = "/" + dPath;
        }
        V1Container sdContainer = new V1Container();
        // volume mounts
        List<V1VolumeMount> vMs = new ArrayList<>();
        V1VolumeMount cvm = new V1VolumeMount();
        cvm.setName("bos-base");
        cvm.setMountPath(BCI_BOS_BASE_PATH);
        cvm.setReadOnly(false);
        cvm.setMountPropagation("Bidirectional");
        vMs.add(cvm);
        sdContainer.setVolumeMounts(vMs);
        String sdContainerName = PodConstants.BCI_BOS_SIDECAR_CONTAINER_PREFIX + count;
        // common config
        sdContainer.setName(sdContainerName);
        sdContainer.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);
        sdContainer.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);
        // 复用NFS镜像，脚本位置/bin/bos-mount.sh
        if (StringUtil.isEmpty(storageSidecarImage)) {
            sdContainer.setImage(bciStorageSidecarImage);
        } else {
            sdContainer.setImage(storageSidecarImage);
        }
        sdContainer.setImagePullPolicy(IMAGE_PULL_POLICY_IF_NOT_PRESENT);
        // set privileged true
        V1SecurityContext v1SecurityContext = new V1SecurityContext();
        v1SecurityContext.setPrivileged(true);
        v1SecurityContext.setRunAsGroup(0L);
        v1SecurityContext.setRunAsUser(0L);
        v1SecurityContext.runAsNonRoot(false);
        sdContainer.setSecurityContext(v1SecurityContext);

        // 设置env，供sidecar容器启动顺序使用
        if (CollectionUtils.isEmpty(sdContainer.getEnv())) {
            sdContainer.setEnv(new ArrayList<V1EnvVar>());
        }

        V1EnvVar bucketEnvVar = new V1EnvVar();
        bucketEnvVar.setName("bucket");
        bucketEnvVar.setValue(bucket);

        V1EnvVar urlEnvVar = new V1EnvVar();
        urlEnvVar.setName("url");
        urlEnvVar.setValue(url);

        V1EnvVar otherOptsEnvVar = new V1EnvVar();
        otherOptsEnvVar.setName("otherOpts");
        otherOptsEnvVar.setValue(otherOpts);

        V1EnvVar akEnvVar = new V1EnvVar();
        akEnvVar.setName("ak");
        akEnvVar.setValue(ak);

        V1EnvVar skEnvVar = new V1EnvVar();
        skEnvVar.setName("sk");
        skEnvVar.setValue(sk);

        V1EnvVar readOnlyEnvVar = new V1EnvVar();
        readOnlyEnvVar.setName("readOnly");
        readOnlyEnvVar.setValue(readOnly.toString());

        V1EnvVar dPathEnvVar = new V1EnvVar();
        dPathEnvVar.setName("dPath");
        dPathEnvVar.setValue(dPath);

        V1EnvVar containerNameEnvVar = new V1EnvVar();
        containerNameEnvVar.setName("containerName");
        containerNameEnvVar.setValue(sdContainerName);

        sdContainer.getEnv().add(bucketEnvVar);
        sdContainer.getEnv().add(urlEnvVar);
        sdContainer.getEnv().add(otherOptsEnvVar);
        sdContainer.getEnv().add(akEnvVar);
        sdContainer.getEnv().add(skEnvVar);
        sdContainer.getEnv().add(readOnlyEnvVar);
        sdContainer.getEnv().add(dPathEnvVar);
        sdContainer.getEnv().add(containerNameEnvVar);

        // lifecycle
        V1Lifecycle lifecycle = new V1Lifecycle();
        // preStop
        V1LifecycleHandler preStop = new V1LifecycleHandler();
        V1ExecAction preStopCmd = new V1ExecAction();
        preStopCmd.addCommandItem("/bin/bos-mount.sh");
        preStopCmd.addCommandItem("umount");
        preStopCmd.addCommandItem(dPath);
        preStop.setExec(preStopCmd);
        lifecycle.setPreStop(preStop);
        // postStart
        V1LifecycleHandler postStart = new V1LifecycleHandler();
        V1ExecAction exec = new V1ExecAction();
        List<String> postStartCommandList = new ArrayList<>();
        postStartCommandList.add("/bin/sh");
        postStartCommandList.add("-c");
        postStartCommandList.add("nohup sh " + SIDECAR_COMMAND_MOUNT_PATH + BOS_CHECK_SHELL_NAME + " &");
        exec.setCommand(postStartCommandList);
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);

        sdContainer.setLifecycle(lifecycle);

        // command
        List<String> command = new ArrayList<>();
        command.add("/bin/bos-mount.sh");
        command.add("mount");
        command.add(dPath);
        command.add(bucket);
        command.add(url);
        command.add(ak);
        command.add(sk);
        if (readOnly) {
            command.add("ro");
        } else {
            command.add("rw");
        }
        command.add(otherOpts);
        sdContainer.setCommand(command);

        return sdContainer;
    }


    /**
     * create pfs sidecar containers
     * every pfs volume need a sidecar
     *
     * @param volumeName pfs volume name
     * @param pfsServer  pfs server path
     * @param sPath      pfs server side path
     * @param dPath      pfs volumeMount path (in container)
     */
    public V1Container getPfsSidecar(String volumeName, String pfsServer, String sPath,
                                      String dPath, int count) throws IOException {
        if (!sPath.startsWith("/")) {
            sPath = "/" + sPath;
        }
        if (!dPath.startsWith("/")) {
            dPath = "/" + dPath;
        }
        V1Container sdContainer = new V1Container();
        // volume mounts
        List<V1VolumeMount> vMs = new ArrayList<>();
        V1VolumeMount cvm = new V1VolumeMount();
        cvm.setName("pfs-base");
        cvm.setMountPath(BCI_PFS_BASE_PATH);
        cvm.setReadOnly(false);
        cvm.setMountPropagation("Bidirectional");
        vMs.add(cvm);
        sdContainer.setVolumeMounts(vMs);
        String sdContainerName = PodConstants.BCI_PFS_SIDECAR_CONTAINER_PREFIX + count;
        // common config
        sdContainer.setName(sdContainerName);
        sdContainer.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);
        sdContainer.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);
        // 复用NFS镜像，脚本位置/bin/pfs-mount.sh
        if (StringUtil.isEmpty(storageSidecarImage)) {
            sdContainer.setImage(bciStorageSidecarImage);
        } else {
            sdContainer.setImage(storageSidecarImage);
        }
        sdContainer.setImagePullPolicy(IMAGE_PULL_POLICY_IF_NOT_PRESENT);
        // set privileged true
        V1SecurityContext v1SecurityContext = new V1SecurityContext();
        v1SecurityContext.setPrivileged(true);
        v1SecurityContext.setRunAsGroup(0L);
        v1SecurityContext.setRunAsUser(0L);
        v1SecurityContext.runAsNonRoot(false);
        sdContainer.setSecurityContext(v1SecurityContext);

        // 设置env，供sidecar容器启动顺序使用
        if (CollectionUtils.isEmpty(sdContainer.getEnv())) {
            sdContainer.setEnv(new ArrayList<V1EnvVar>());
        }

        V1EnvVar pfsServerEnvVar = new V1EnvVar();
        pfsServerEnvVar.setName("pfsServer");
        pfsServerEnvVar.setValue(pfsServer);

        V1EnvVar sPathEnvVar = new V1EnvVar();
        sPathEnvVar.setName("sPath");
        sPathEnvVar.setValue(sPath);

        V1EnvVar dPathEnvVar = new V1EnvVar();
        dPathEnvVar.setName("dPath");
        dPathEnvVar.setValue(dPath);

        V1EnvVar containerNameEnvVar = new V1EnvVar();
        containerNameEnvVar.setName("containerName");
        containerNameEnvVar.setValue(sdContainerName);

        sdContainer.getEnv().add(pfsServerEnvVar);
        sdContainer.getEnv().add(sPathEnvVar);
        sdContainer.getEnv().add(dPathEnvVar);
        sdContainer.getEnv().add(containerNameEnvVar);

        // lifecycle
        V1Lifecycle lifecycle = new V1Lifecycle();
        // preStop
        V1LifecycleHandler preStop = new V1LifecycleHandler();
        V1ExecAction preStopCmd = new V1ExecAction();
        preStopCmd.addCommandItem("/bin/pfs-mount.sh");
        preStopCmd.addCommandItem("umount");
        preStopCmd.addCommandItem(dPath); // pfs dst path
        preStop.setExec(preStopCmd);
        lifecycle.setPreStop(preStop);

        V1LifecycleHandler postStart = new V1LifecycleHandler();
        V1ExecAction exec = new V1ExecAction();
        List<String> postStartCommandList = new ArrayList<>();
        postStartCommandList.add("/bin/sh");
        postStartCommandList.add("-c");
        postStartCommandList.add("nohup sh " + SIDECAR_COMMAND_MOUNT_PATH + PFS_CHECK_SHELL_NAME + " &");
        exec.setCommand(postStartCommandList);
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);

        sdContainer.setLifecycle(lifecycle);

        // command
        List<String> command = new ArrayList<>();
        command.add("/bin/pfs-mount.sh");
        command.add("mount");
        command.add(pfsServer); // pfs server
        command.add(sPath); // pfs src path
        command.add(dPath); // pfs dst path

        sdContainer.setCommand(command);
        return sdContainer;
    }

    public BciOrderExtra getOrderExtra(String orderId, String podId) {
        Order order = logicPodClientFactory.createOrderClient().get(orderId);
        BciOrderExtra orderExtra = null;
        for (Order.Item item : order.getItems()) {
            if (StringUtils.equals(item.getKey(), ORDER_KEY + "-" + podId)) {
                try {
                    orderExtra = PodUtils.getOrderExtra(item.getExtra());
                } catch (Exception e) {
                    LOGGER.error("[bce logical bci] Get order extra failed, exception is ", e);
                    throw new CommonExceptions.ResourceNotExistException();
                }
                break;
            }
        }
        if (orderExtra == null) {
            throw new CommonExceptions.ResourceNotExistException();
        }
        return orderExtra;
    }

    public void setK8sServiceInUT(K8sService k8sService) {
        this.k8sService = k8sService;
    }

    public void addResourceLimitAnnotation(V1ObjectMeta metadata) {
        if (metadata == null) {
            return;
        }
        if (metadata.getAnnotations() == null) {
            metadata.setAnnotations(new HashMap<String, String>());
        }
        metadata.getAnnotations().put(PodConstants.BCI_INTERNAL_ENABLE_POD_LIMIT, "true");
    }


    // return
    // true: 调度中/调度成功
    // false: 调度失败
    public boolean checkPodResourceAvailable(V1Pod pod) {
        // 保证pod处于pending状态
        if (pod.getStatus().getPhase() == null || !BciStatus.PENDING.getStatus().equalsIgnoreCase(pod.getStatus().getPhase())) {
            return true;
        }

        // 保证pod的NodeName未设置
        if (pod.getSpec().getNodeName() != null && !StringUtils.isEmpty(pod.getSpec().getNodeName())) {
            return true;
        }

        // 判断pod是否处于调度中,没状态则认为调度中
        List<V1PodCondition> conditions = pod.getStatus().getConditions();
        if (CollectionUtils.isEmpty(conditions)) {
            return true;
        }

        for (V1PodCondition condition : conditions) {
            if (condition.getType().equals("PodScheduled")) {
                String status = condition.getStatus();
                // 调度成功或者调度中
                if ("True".equals(status) || "Unknown".equals(status)) {
                    return true;
                } else if ("False".equals(status)){
                    // 调度失败；检查pod的pending时长是否超过配置值
                    if (isRodScheduleTimeout(pod)) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }

        // 其他状态都认为底层有可用资源
        return true;
    }

    // 判断调度是否超时
    // return
    // true: 调度超时
    // false: 调度未超时
    private boolean isRodScheduleTimeout(V1Pod pod) {
        if (pod == null || pod.getMetadata() == null) {
            return false;
        }
        // 获取pod的调度超时时间
        OffsetDateTime cutoffTime = OffsetDateTime.now().minusMinutes(podScheduledTimeoutMinutes);
        LOGGER.debug("cutoffTime is {}", cutoffTime);
        if (!pod.getMetadata().getCreationTimestamp().isBefore(cutoffTime)) {
            LOGGER.debug("pod {} createtime is {}, shold wait scheduling",
                    pod.getMetadata().getName(), pod.getMetadata().getCreationTimestamp());
            return false;
        }
        return true;
    }

    private boolean processK8SPodNoResourceAvailable(String userId, String podId, String recycleReason) {

        PodPO podPO = podDao.getPodById(userId, podId);
        if (podPO == null) {
            LOGGER.warn("processK8SPodNoResourceAvailable: pod {} not exist", podId);
            return false;
        }

        // set recycle reason and stamp
        if (podPO.getResourceRecycleTimestamp() == 0L) {
            long recycleTimestamp = System.currentTimeMillis();
            podPO.setResourceRecycleTimestamp(recycleTimestamp);
            podPO.setResourceRecycleReason(recycleReason);
            LOGGER.debug("processK8SPodNoResourceAvailable: pod {} mark recycle, timestamp {}, reason {}",
                    podId,
                    recycleTimestamp, recycleReason);
        }

        String oldPodStatus = podPO.getStatus();
        podPO.setStatus(BciStatus.getStatus(BciStatus.STATUS_FAILED));
        // set order sync status
        podPO.setInternalStatus(PodConstants.BCI_INTERNAL_STATUS_SYNCED);
        // long podNewResourceVersion = newResourceVersion;
        String podCurrentStatus = podPO.getStatus();
        LOGGER.info("processK8SPodNoResourceAvailable pod:{} :{} "
                            + "oldPodstatus: {} newPodStatus: {}",
                    podId, userId, oldPodStatus, podCurrentStatus);
        StateMachineEvent event = StateMachineEvent.K8S_POD_NO_RESOURCE_AVAILABLE;
        // K8SPodNoResourceAvailableContext eventContext = new K8SPodNoResourceAvailableContext(podId, userId, podNewResourceVersion);
        if (!stateMachine.trigger(podPO, event, null)) {
            LOGGER.warn("processK8SPodNoResourceAvailable: pod state machine trigger failed, podId:{} event:{}",
                        podId, event.toString());
            return false;
        }
        LOGGER.info("processK8SPodNoResourceAvailable: pod state machine trigger success, podId:{} event:{}",
            podId, event.toString());
        return true;

    }

    public void addResourceLimitToInitContainers(List<V1Container> initContainers, float podCpu, float podMem) {
        addResourceLimitToContainers(initContainers, podCpu, podMem);
    }

    public void configureResourceToSidecarContainers(List<V1Container> sidecarContainers, float podCpu, float podMem) {
        // 将 sidecar container request 设置为 0，共用 pod 资源
        for (V1Container sidecarContainer : sidecarContainers) {
            if (sidecarContainer.getResources() == null) {
                sidecarContainer.setResources(new V1ResourceRequirements());
            }
            if (sidecarContainer.getResources().getRequests() == null) {
                sidecarContainer.getResources().setRequests(new HashMap<String, Quantity>());
            }
            sidecarContainer.getResources().getRequests().put("cpu", new Quantity("0"));
            sidecarContainer.getResources().getRequests().put("memory", new Quantity("0"));
        }
        addResourceLimitToContainers(sidecarContainers, podCpu, podMem);
    }
    public void addResourceLimitToContainers(List<V1Container> containers, float podCpu, float podMem) {
        // 给每个initContainer添加资源限制
        if (containers == null || containers.size() == 0) {
            return;
        }
        for (V1Container container : containers) {
            if (container.getResources() == null) {
                container.setResources(new V1ResourceRequirements());
            }

            // 由于每个容器的limit都设置了，所以request设置为0，避免k8s设置为和limit一致
            if (container.getResources().getRequests() == null) {
                container.getResources().setRequests(new HashMap<String, Quantity>());
            }
            if (container.getResources().getRequests().get("memory") == null) {
                container.getResources().getRequests().put("memory", new Quantity("0"));
            }
            if (container.getResources().getRequests().get("cpu") == null) {
                container.getResources().getRequests().put("cpu", new Quantity("0"));
            }

            if (container.getResources().getLimits() == null) {
                container.getResources().setLimits(new HashMap<String, Quantity>());
            }

            if (container.getResources().getLimits().get("cpu") == null) {
                container.getResources().getLimits().put("cpu", new Quantity(String.valueOf(podCpu)));
            } else {
                String cpuLimit = container.getResources().getLimits().get("cpu").getNumber().toString();
                if (cpuLimit == null || new Quantity(cpuLimit).getNumber().floatValue() < FLOATPRECISION ||
                        new Quantity(cpuLimit).getNumber().floatValue() >
                        new Quantity(String.valueOf(podCpu)).getNumber().floatValue()) {
                    container.getResources().getLimits().put("cpu", new Quantity(String.valueOf(podCpu)));
                }
            }

            if (container.getResources().getLimits().get("memory") == null) {
                container.getResources().getLimits().put("memory", new Quantity(String.valueOf(podMem) + "Gi"));
            } else {
                String memLimit = container.getResources().getLimits().get("memory").getNumber().toString();
                if (memLimit == null || new Quantity(memLimit).getNumber().intValue() == 0) {
                    container.getResources().getLimits().put("memory", new Quantity(String.valueOf(podMem) + "Gi"));
                }
            }
        }
    }

    /**
     * 处理容器生命周期
     * @param container 要生成的容器
     * @param containerPurchase 当前容器
     * @param preContainer 上一个优先退出的容器
     * @param nextContainer 下一个优先退出的容器
     * @param hasNextPriorityContainer 是否有下一个优先启动的容器
     */
    private void handleContainerLifecycle(V1Container container, ContainerPurchase containerPurchase,
                                          ContainerPurchase preContainer, ContainerPurchase nextContainer, Boolean hasNextPriorityContainer) {
        try {
            V1Lifecycle lifecycle = container.getLifecycle();
            if (lifecycle == null) {
                lifecycle = new V1Lifecycle();
            }
            
            // 分离职责：分别处理postStart和preStop
            handlePostStartHook(lifecycle, containerPurchase, hasNextPriorityContainer);
            handlePreStopHook(lifecycle, containerPurchase, preContainer, nextContainer);
            
            container.setLifecycle(lifecycle);
            
            LOGGER.debug("Container lifecycle configuration completed for container: {}", containerPurchase.getName());
        } catch (Exception e) {
            LOGGER.error("Failed to configure container lifecycle for container: {}", containerPurchase.getName(), e);
            // 提供降级策略：如果生命周期配置失败，至少确保容器可以启动
            handleLifecycleConfigurationFailure(container, containerPurchase, e);
        }
    }

    /**
     * 处理postStart Hook配置
     * @param lifecycle 生命周期对象
     * @param containerPurchase 容器配置
     * @param hasNextPriorityContainer 是否有下一个优先启动的容器
     */
    private void handlePostStartHook(V1Lifecycle lifecycle, ContainerPurchase containerPurchase, Boolean hasNextPriorityContainer) {
        if (!hasNextPriorityContainer) {
            LOGGER.debug("Container {} does not need startup priority coordination, skipping postStart hook", containerPurchase.getName());
            return;
        }

        try {
            Probe readinessProbe = containerPurchase.getReadinessProbe();
            String readinessCommand = convertProbeToExecCommand(readinessProbe);
            
            // 获取用户自定义的postStart命令
            String userPostStartCommand = extractUserPostStartCommand(containerPurchase);
            
            // 构建优化后的postStart命令
            String optimizedCommand = buildPostStartCommand(readinessCommand, userPostStartCommand);
            lifecycle.setPostStart(createLifecycleHandler(optimizedCommand));
            
            LOGGER.info("Container {} configured with optimized postStart hook (readiness: {}, user command: {})", 
                containerPurchase.getName(), readinessProbe != null, userPostStartCommand != null);
        } catch (Exception e) {
            LOGGER.error("Failed to configure postStart hook for container: {}", containerPurchase.getName(), e);
            // 降级策略：使用基础的启动等待
            String fallbackCommand = buildPostStartCommand(null, null);
            lifecycle.setPostStart(createLifecycleHandler(fallbackCommand));
        }
    }

    /**
     * 处理preStop Hook配置
     * @param lifecycle 生命周期对象
     * @param containerPurchase 容器配置
     * @param preContainer 前置容器
     * @param nextContainer 后续容器
     */
    private void handlePreStopHook(V1Lifecycle lifecycle, ContainerPurchase containerPurchase, 
                                  ContainerPurchase preContainer, ContainerPurchase nextContainer) {
        boolean needsExitPriority = (preContainer != null || nextContainer != null);
        
        if (!needsExitPriority) {
            LOGGER.debug("Container {} does not need exit priority logic, skipping default preStop hook", containerPurchase.getName());
            // 如果有用户自定义的preStop，保持不变
            preserveUserPreStopHook(lifecycle, containerPurchase);
            return;
        }

        try {
            // 获取用户自定义的preStop命令
            String userPreStopCommand = extractUserPreStopCommand(containerPurchase);
            
            // 构建包含优先级逻辑的preStop命令
            StringBuilder exitCommand = new StringBuilder();
            if (userPreStopCommand != null && !userPreStopCommand.trim().isEmpty()) {
                exitCommand.append(userPreStopCommand).append(" && ");
            }
            
            StringBuilder preStopCommand = genePreStopCommand(exitCommand, containerPurchase, preContainer, nextContainer);
            lifecycle.setPreStop(createLifecycleHandler(preStopCommand.toString()));
            
            LOGGER.info("Container {} configured with preStop hook (user command: {}, exit priority: {})", 
                containerPurchase.getName(), userPreStopCommand != null, needsExitPriority);
        } catch (Exception e) {
            LOGGER.error("Failed to configure preStop hook for container: {}", containerPurchase.getName(), e);
            // 降级策略：保持用户原有配置或使用基础的退出逻辑
            handlePreStopConfigurationFailure(lifecycle, containerPurchase, preContainer, nextContainer, e);
        }
    }

    /**
     * 提取用户自定义的postStart命令
     * @param containerPurchase 容器配置
     * @return 用户自定义的postStart命令，如果没有则返回null
     */
    private String extractUserPostStartCommand(ContainerPurchase containerPurchase) {
        if (containerPurchase.getLifecycle() == null || containerPurchase.getLifecycle().getPostStart() == null) {
            return null;
        }
        
        V1LifecycleHandler postStart = containerPurchase.getLifecycle().getPostStart().toV1LifecycleHandler();
        if (postStart.getExec() != null) {
            List<String> existingCommand = postStart.getExec().getCommand();
            if (CollectionUtils.isNotEmpty(existingCommand)) {
                return extractActualCommand(existingCommand);
            }
        }
        // 暂不支持httpGet类型的postStart
        return null;
    }

    /**
     * 提取用户自定义的preStop命令
     * @param containerPurchase 容器配置
     * @return 用户自定义的preStop命令，如果没有则返回null
     */
    private String extractUserPreStopCommand(ContainerPurchase containerPurchase) {
        if (containerPurchase.getLifecycle() == null || containerPurchase.getLifecycle().getPreStop() == null) {
            return null;
        }
        
        V1LifecycleHandler preStop = containerPurchase.getLifecycle().getPreStop().toV1LifecycleHandler();
        if (preStop.getExec() != null) {
            List<String> existingCommand = containerPurchase.getLifecycle().getPreStop().getExec().getCommand();
            if (CollectionUtils.isNotEmpty(existingCommand)) {
                return extractActualCommand(existingCommand);
            }
        }
        // 暂不支持httpGet类型的preStop
        return null;
    }

    /**
     * 保持用户原有的preStop Hook配置
     * @param lifecycle 生命周期对象
     * @param containerPurchase 容器配置
     */
    private void preserveUserPreStopHook(V1Lifecycle lifecycle, ContainerPurchase containerPurchase) {
        if (containerPurchase.getLifecycle() != null && containerPurchase.getLifecycle().getPreStop() != null) {
            V1LifecycleHandler preStop = containerPurchase.getLifecycle().getPreStop().toV1LifecycleHandler();
            lifecycle.setPreStop(preStop);
            LOGGER.debug("Preserved user-defined preStop hook for container: {}", containerPurchase.getName());
        }
    }

    /**
     * 处理生命周期配置失败的降级策略
     * @param container 容器对象
     * @param containerPurchase 容器配置
     * @param error 发生的错误
     */
    private void handleLifecycleConfigurationFailure(V1Container container, ContainerPurchase containerPurchase, Exception error) {
        LOGGER.warn("Applying fallback lifecycle configuration for container: {}", containerPurchase.getName());
        
        try {
            V1Lifecycle fallbackLifecycle = new V1Lifecycle();
            // 最基础的降级策略：只设置简单的sleep命令
            fallbackLifecycle.setPostStart(createLifecycleHandler(BCI_POSTSTART_COMMAND));
            container.setLifecycle(fallbackLifecycle);
            
            LOGGER.info("Applied fallback lifecycle configuration for container: {}", containerPurchase.getName());
        } catch (Exception fallbackError) {
            LOGGER.error("Even fallback lifecycle configuration failed for container: {}", containerPurchase.getName(), fallbackError);
            // 完全失败时，不设置任何生命周期配置，让容器以默认方式启动
        }
    }

    /**
     * 处理preStop配置失败的降级策略
     * @param lifecycle 生命周期对象
     * @param containerPurchase 容器配置
     * @param preContainer 前置容器
     * @param nextContainer 后续容器
     * @param error 发生的错误
     */
    private void handlePreStopConfigurationFailure(V1Lifecycle lifecycle, ContainerPurchase containerPurchase, 
                                                   ContainerPurchase preContainer, ContainerPurchase nextContainer, Exception error) {
        LOGGER.warn("Applying fallback preStop configuration for container: {}", containerPurchase.getName());
        
        try {
            // 降级策略1：尝试保持用户原有配置
            preserveUserPreStopHook(lifecycle, containerPurchase);
            
            // 降级策略2：如果没有用户配置，使用基础的退出逻辑
            if (lifecycle.getPreStop() == null && (preContainer != null || nextContainer != null)) {
                StringBuilder basicExitCommand = new StringBuilder();
                basicExitCommand.append("sleep 15"); // 基础的退出延迟
                lifecycle.setPreStop(createLifecycleHandler(basicExitCommand.toString()));
                LOGGER.info("Applied basic preStop fallback for container: {}", containerPurchase.getName());
            }
        } catch (Exception fallbackError) {
            LOGGER.error("PreStop fallback configuration also failed for container: {}", containerPurchase.getName(), fallbackError);
        }
    }

    /**
     * 判断是否有下一个优先启动的容器
     * @param currentContainerPurchase
     * @param containerPurchases
     * @return
     */
    private boolean hasNextPriorityContainer(ContainerPurchase currentContainerPurchase, List<ContainerPurchase> containerPurchases) {
        // 获取当前容器的优先级
        String currentPriority = getPriority(currentContainerPurchase, BCI_CONTAINER_LAUNCH_PRIORITY);
        int currentPriorityValue = Integer.parseInt(currentPriority);

        // 获取当前容器在列表中的索引
        int currentIndex = containerPurchases.indexOf(currentContainerPurchase);
        int lastIndex = containerPurchases.size() - 1;
        if (currentIndex == -1 || currentIndex == lastIndex) {
            return false;
        }

        // 查看后续容器是否有更低的优先级
        ContainerPurchase nextContainer = containerPurchases.get(currentIndex + 1);
        String nextPriority = getPriority(nextContainer, BCI_CONTAINER_LAUNCH_PRIORITY);
        int nextPriorityValue = Integer.parseInt(nextPriority);

        if (nextPriorityValue < currentPriorityValue) {
            return true;
        }

        return false;
    }

    /**
     * 将httpGet探针转换为exec命令
     * @param httpGet httpGet探针
     * @return
     */
    private String convertProbHttpGetToExecCommand(HTTPGetAction httpGet) {
        String scheme = httpGet.getScheme() != null ? httpGet.getScheme().toLowerCase() : "http";
        String host = httpGet.getHost() != null ? httpGet.getHost() : "localhost";
        String path = httpGet.getPath() != null ? httpGet.getPath() : "/";
        String port = String.valueOf(httpGet.getPort());

        StringBuilder command = new StringBuilder("curl -sf");

        // Add HTTP headers
        if (httpGet.getHttpHeaders() != null) {
            for (HTTPHeader header : httpGet.getHttpHeaders()) {
                command.append(" -H \"").append(header.getName())
                        .append(": ").append(header.getValue()).append("\"");
            }
        }

        String url = String.format("%s://%s:%s%s", scheme, host, port, path);
        command.append(" \"").append(url).append("\"");

        return command.toString();
    }

    /**
     * 将TCPSocket探针转换为exec命令（增强版本）
     * @param tcpSocket tcpSocket探针
     * @return 健壮的TCP连接检测命令
     */
    private String convertTcpSocketToExecCommand(TCPSocketAction tcpSocket) {
        try {
            if (tcpSocket == null) {
                LOGGER.warn("TCPSocket probe is null, returning default failure command");
                return "echo 'TCPSocket probe is null'; false";
            }
            
            String port = String.valueOf(tcpSocket.getPort());
            String host = tcpSocket.getHost() != null ? tcpSocket.getHost() : "localhost";
            
            // 验证端口号的有效性
            try {
                int portNum = Integer.parseInt(port);
                if (portNum < 1 || portNum > 65535) {
                    LOGGER.error("Invalid port number: {}. Port must be between 1 and 65535", port);
                    return "echo 'Invalid port number: " + port + "'; false";
                }
            } catch (NumberFormatException e) {
                LOGGER.error("Invalid port format: {}", port, e);
                return "echo 'Invalid port format: " + port + "'; false";
            }
            
            // 验证主机名的基本格式
            if (host.trim().isEmpty()) {
                LOGGER.error("Host name is empty");
                return "echo 'Host name is empty'; false";
            }
            
            LOGGER.debug("Converting TCPSocket probe to exec command: {}:{}", host, port);
            
            // 构建多重备选的TCP连接检测命令，提高成功率和兼容性
            StringBuilder command = new StringBuilder();
            
            // 添加调试信息
            command.append("echo 'BCI TCPSocket Probe: Testing connection to ").append(host).append(":").append(port).append("...'; ");
            
            // 使用复合命令结构，按优先级尝试不同的连接检测方式
            command.append("(");
            
            // 方案1: 使用nc命令（最常用，性能最好）
            command.append("(timeout 3 nc -z ").append(host).append(" ").append(port).append(" 2>/dev/null && ");
            command.append("echo 'TCP connection successful via nc to ").append(host).append(":").append(port).append("') || ");
            
            // 方案2: 使用/dev/tcp（bash内置，兼容性好）
            command.append("(timeout 3 bash -c 'exec 3<>/dev/tcp/").append(host).append("/").append(port).append(" && echo >&3 && exec 3<&-' 2>/dev/null && ");
            command.append("echo 'TCP connection successful via /dev/tcp to ").append(host).append(":").append(port).append("') || ");
            
            // 方案3: 使用telnet（广泛支持，但较慢）
            command.append("(timeout 3 bash -c 'echo \"\" | telnet ").append(host).append(" ").append(port).append(" 2>/dev/null | grep -E \"Connected|Escape\"' >/dev/null && ");
            command.append("echo 'TCP connection successful via telnet to ").append(host).append(":").append(port).append("') || ");
            
            // 方案4: 使用curl（适用于HTTP服务）
            command.append("(timeout 3 curl -s --connect-timeout 2 --max-time 3 telnet://").append(host).append(":").append(port).append(" 2>/dev/null && ");
            command.append("echo 'TCP connection successful via curl to ").append(host).append(":").append(port).append("') || ");
            
            // 方案5: 使用python（如果可用，作为最后手段）
            command.append("(timeout 3 python3 -c \"import socket; s=socket.socket(); s.settimeout(2); s.connect(('").append(host).append("', ").
                    append(port).append(")); s.close()\" 2>/dev/null && ");
            command.append("echo 'TCP connection successful via python to ").append(host).append(":").append(port).append("') || ");
            
            // 所有方案都失败时的处理
            command.append("(echo 'All TCP connection methods failed for ").append(host).append(":").append(port).
                    append(". Service may not be ready or network issue exists.'; false)");
            
            command.append(")");
            
            LOGGER.debug("Generated TCPSocket probe command for {}:{}", host, port);
            return command.toString();
            
        } catch (Exception e) {
            LOGGER.error("Failed to convert TCPSocket probe to exec command", e);
            // 降级策略：返回一个基础的失败命令
            return "echo 'TCPSocket probe conversion failed: " + e.getMessage() + "'; false";
        }
    }

    /**
     * 提取实际的命令，过滤掉shell包装器（如/bin/sh -c）
     * @param existingCommand 原始命令列表
     * @return 提取后的实际命令
     */
    private String extractActualCommand(List<String> existingCommand) {
        if (CollectionUtils.isEmpty(existingCommand)) {
            return "";
        }
        
        // 如果命令以 /bin/sh -c 或 /bin/bash -c 开头，提取实际命令
        if (existingCommand.size() >= 3 && 
            (existingCommand.get(0).equals("/bin/sh") || existingCommand.get(0).equals("/bin/bash")) &&
            existingCommand.get(1).equals("-c")) {
            // 返回 -c 后面的实际命令部分
            return existingCommand.get(2);
        }
        
        // 如果命令以 sh -c 或 bash -c 开头，提取实际命令
        if (existingCommand.size() >= 3 && 
            (existingCommand.get(0).equals("sh") || existingCommand.get(0).equals("bash")) &&
            existingCommand.get(1).equals("-c")) {
            // 返回 -c 后面的实际命令部分
            return existingCommand.get(2);
        }
        
        // 其他情况直接拼接所有命令部分
        return String.join(" ", existingCommand);
    }

    /**
     * 将探针转换为exec命令
     * @param probe 探针
     * @return
     */
    private String convertProbeToExecCommand(Probe probe) {
        try {
            if (probe == null) {
                LOGGER.debug("Probe is null, returning null command");
                return null;
            }

            LOGGER.debug("Converting probe to exec command: type={}", getProbeType(probe));

            if (probe.getHttpGet() != null) {
                String command = convertProbHttpGetToExecCommand(probe.getHttpGet());
                LOGGER.debug("Successfully converted HTTP probe to exec command");
                return command;
            } else if (probe.getTcpSocket() != null) {
                try {
                    String command = convertTcpSocketToExecCommand(probe.getTcpSocket());
                    LOGGER.debug("Successfully converted TCP socket probe to exec command");
                    return command;
                } catch (Exception e) {
                    LOGGER.error("Failed to convert TCP socket probe to exec command", e);
                    return "echo 'TCP socket probe conversion failed: " + e.getMessage() + "'; false";
                }
            } else if (probe.getExec() != null) {
                List<String> command = probe.getExec().getCommand();
                if (command != null && !command.isEmpty()) {
                    // 使用 extractActualCommand 方法来正确处理 shell 包装器
                    String execCommand = extractActualCommand(command);
                    LOGGER.debug("Successfully extracted exec probe command");
                    return execCommand;
                } else {
                    LOGGER.warn("Exec probe has empty command list");
                    return "echo 'Exec probe has empty command'; false";
                }
            } else {
                LOGGER.warn("Probe has no recognized action (httpGet, tcpSocket, or exec)");
                return "echo 'Probe has no recognized action'; false";
            }
        } catch (Exception e) {
            LOGGER.error("Unexpected error during probe conversion", e);
            return "echo 'Probe conversion failed: " + e.getMessage() + "'; false";
        }
    }

    /**
     * 获取探针类型的字符串表示（用于日志）
     * @param probe 探针对象
     * @return 探针类型字符串
     */
    private String getProbeType(Probe probe) {
        if (probe == null) {
            return "null";
        }
        if (probe.getHttpGet() != null) {
            return "HTTP";
        }
        if (probe.getTcpSocket() != null) {
            return "TCPSocket";
        }
        if (probe.getExec() != null) {
            return "Exec";
        }
        return "Unknown";
    }

    /**
     * 构建postStart命令，改善readiness检查时机
     * @param readinessCommand readiness检查命令
     * @param userCommand 用户自定义的postStart命令
     * @return 优化后的postStart命令
     */
    private String buildPostStartCommand(String readinessCommand, String userCommand) {
        StringBuilder command = new StringBuilder();
        
        // 1. 首先执行用户自定义的postStart命令（如果有）
        if (userCommand != null && !userCommand.trim().isEmpty()) {
            command.append(userCommand).append(" && ");
            LOGGER.debug("Including user postStart command in optimized startup sequence");
        }
        
        // 2. 基础启动等待 - 给容器更多时间完成基础初始化
        // 使用可配置的等待时间，默认8秒，可通过bci.poststart.basic.wait配置
        command.append("echo 'Container basic startup phase...'; sleep ").append(postStartBasicWait).append("; ");
        
        // 3. 如果有readiness检查，在基础启动完成后再执行
        if (readinessCommand != null && !readinessCommand.trim().isEmpty()) {
            // 检查是否是真正的测试命令（修复：改进检测逻辑，避免误判TCP探针命令）
            if (isSimpleCommand(readinessCommand)) {
                LOGGER.warn("Skipping test-only readiness command in postStart: '{}'", readinessCommand);
                command.append("echo 'Skipped test-only readiness check, container startup completed'");
            } else {
                // 执行真正的readiness检查，但使用优化后的参数
                command.append("echo 'Starting readiness verification...'; ");
                command.append(buildReadinessCheck(readinessCommand, true));
            }
        } else {
            // 无readiness检查时，只需要基础等待
            command.append("echo 'Container startup completed (no readiness check required)'");
        }
        
        return command.toString();
    }

    /**
     * 判断是否为测试用的命令（修复：改进检测逻辑，支持组合测试命令）
     * @param command 待检测的命令
     * @return 如果是测试命令返回true，否则返回false
     */
    private boolean isSimpleCommand(String command) {
        if (command == null || command.trim().isEmpty()) {
            return true;
        }
        
        String trimmedCommand = command.trim().toLowerCase();
        
        // 基础测试命令
        if ("sleep".equals(trimmedCommand) ||
                "true".equals(trimmedCommand) ||
                "/bin/true".equals(trimmedCommand) ||
                "exit 0".equals(trimmedCommand)) {
            return true;
        }

        // 检查是否包含明确的探针/服务检查关键词 - 如果包含则不是测试命令
        if (containsProbeKeywords(trimmedCommand)) {
            return false;
        }
        
        // 检查是否为组合的简单测试命令（支持 && 和 ; 连接的测试命令）
        if (isCompositeTestCommand(trimmedCommand)) {
            return true;
        }

        // 简单的sleep命令（不包含分号的情况）
        if (trimmedCommand.startsWith("sleep ") && !trimmedCommand.contains(";")) {
            return true;
        }
        
        // 简单的echo命令（不包含分号的情况）
        if (trimmedCommand.startsWith("echo ") && !trimmedCommand.contains(";")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查命令是否包含探针/服务检查关键词
     * @param command 待检测的命令
     * @return 如果包含探针关键词返回true
     */
    private boolean containsProbeKeywords(String command) {
        String[] probeKeywords = {
            "nc -z", "/dev/tcp", "curl", "wget", "telnet", 
            "timeout", "python", "health", "ready", "status",
            "ping", "test -f", "test -d", "[ -f", "[ -d",
            "netstat", "ss -", "lsof", "ps aux", "pgrep"
        };
        
        for (String keyword : probeKeywords) {
            if (command.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否为组合的简单测试命令
     * @param command 待检测的命令
     * @return 如果是组合测试命令返回true
     */
    private boolean isCompositeTestCommand(String command) {
        // 分割命令（支持 && 和 ; 作为分隔符）
        String[] parts = command.split("(&&|;)");
        
        // 检查每个部分是否都是简单的测试命令
        for (String part : parts) {
            String trimmedPart = part.trim();
            if (!isSingleTestCommand(trimmedPart)) {
                return false;
            }
        }
        
        return parts.length > 0;
    }
    
    /**
     * 检查单个命令是否为简单测试命令
     * @param command 待检测的单个命令
     * @return 如果是简单测试命令返回true
     */
    private boolean isSingleTestCommand(String command) {
        if (command == null || command.trim().isEmpty()) {
            return true;
        }
        
        String trimmed = command.trim().toLowerCase();
        
        // 基础测试命令
        if ("sleep".equals(trimmed) ||
                "true".equals(trimmed) ||
                "/bin/true".equals(trimmed) ||
                "exit 0".equals(trimmed) ||
                "false".equals(trimmed) ||
                "/bin/false".equals(trimmed)) {
            return true;
        }
        
        // sleep 命令（带参数）
        if (trimmed.matches("sleep\\s+\\d+")) {
            return true;
        }
        
        // 简单的echo命令（不包含复杂逻辑）
        if (trimmed.startsWith("echo ") && 
            !trimmed.contains("$(") && 
            !trimmed.contains("`") && 
            !trimmed.contains(">>") && 
            !trimmed.contains("2>")) {
            return true;
        }
        
        return false;
    }


    /**
     * 构建readiness检查逻辑（可配置版本）
     * @param readinessCommand readiness检查命令
     * @param isPostStartContext 是否在postStart上下文中（影响参数选择）
     * @return 优化后的readiness检查命令
     */
    private String buildReadinessCheck(String readinessCommand, boolean isPostStartContext) {
        StringBuilder command = new StringBuilder();

        // 根据上下文选择不同的参数配置
        int maxRetries = isPostStartContext ? postStartReadinessMaxRetries : readinessCheckMaxRetries;
        int retryInterval = isPostStartContext ? postStartReadinessRetryInterval : readinessCheckRetryInterval;
        int totalTime = maxRetries * retryInterval;

        // 支持从容器环境变量中读取自定义参数（优先级最高）
        command.append("# Check for custom readiness parameters from environment; ");
        command.append("max_retries=${BCI_READINESS_MAX_RETRIES:-").append(maxRetries).append("}; ");
        command.append("retry_interval=${BCI_READINESS_RETRY_INTERVAL:-").append(retryInterval).append("}; ");
        command.append("retry_count=0; ");

        // 添加参数信息日志
        command.append("echo \"Starting readiness check with max_retries=$max_retries, retry_interval=$retry_interval (estimated ~${totalTime}s)\"; ");

        // 检测探针类型并提供相应的提示信息
        if (readinessCommand.contains("nc -z") || readinessCommand.contains("/dev/tcp")) {
            command.append("echo 'Detected TCP socket probe - checking port connectivity'; ");
        } else if (readinessCommand.contains("curl") || readinessCommand.contains("wget")) {
            command.append("echo 'Detected HTTP probe - checking endpoint availability'; ");
        } else {
            command.append("echo 'Detected custom exec probe - running command check'; ");
        }

        command.append("while [ $retry_count -lt $max_retries ]; do ");
        command.append("echo \"Readiness check attempt $((retry_count + 1))/$max_retries...\"; ");
        command.append("if ").append(readinessCommand).append(" >/dev/null 2>&1; then ");
        command.append("echo \"Readiness check passed after $((retry_count + 1)) attempts - container ready\"; exit 0; fi; ");
        command.append("echo \"Application not ready yet, waiting $retry_interval seconds... (attempt $((retry_count + 1))/$max_retries)\"; ");
        command.append("sleep $retry_interval; ");
        command.append("retry_count=$((retry_count + 1)); ");
        command.append("done; ");

        // 失败处理 - 根据上下文提供不同的处理策略
        if (isPostStartContext) {
            command.append("echo \"Warning: Readiness check failed after $max_retries attempts (~").append(totalTime).
                    append("s). Container will proceed with startup sequence.\"; ");
            command.append("echo 'Note: Container readiness will be determined by Kubernetes readinessProbe'; ");
            command.append("echo 'Proceeding with container startup sequence despite readiness check failure'");
        } else {
            command.append("echo \"Readiness check failed after $max_retries attempts (total ~").append(totalTime).append("s)\"; ");
            command.append("echo 'Container may need more time to start or there may be a configuration issue'; exit 1");
        }

        return command.toString();
    }

    /**
     * 创建生命周期处理器
     * @param command Exec命令
     * @return
     */
    private V1LifecycleHandler createLifecycleHandler(String command) {
        V1LifecycleHandler handler = new V1LifecycleHandler();
        V1ExecAction exec = new V1ExecAction();
        List<String> commandList = new ArrayList<>();
        commandList.add(BCI_EXEC_VOLUME_CONTAINER_PATH + BCI_EXEC_BIN_NAME);
        commandList.add("-command");
        commandList.add(command);
        exec.setCommand(commandList);

        handler.setExec(exec);
        return handler;
    }

    /**
     * 生成preStop命令
     * @param exitCommand 当前preStop命令
     * @param currContainer 当前容器
     * @param preContainer 上一个优先退出的容器
     * @param nextContainer 下一个优先退出的容器
     * @return
     */
    private StringBuilder genePreStopCommand(StringBuilder exitCommand, ContainerPurchase currContainer, ContainerPurchase preContainer, ContainerPurchase nextContainer) {
        // 获取当前容器的退出优先级，用于计算基础延迟时间
        String currentExitPriority = getPriority(currContainer, BCI_CONTAINER_EXIT_PRIORITY);
        int currentPriorityValue = Integer.parseInt(currentExitPriority);
        
        // 基础延迟：优先级越高（数值越大），延迟越短；优先级越低（数值越小），延迟越长
        // 支持正数和负数优先级，使用线性函数确保所有优先级都有合理的区分度
        // 公式：baseDelay = max(3, min(60, 15 - currentPriorityValue))
        // 
        // 示例（正数优先级）：
        // - 优先级20:  15 - 20 = -5 → max(3, -5) = 3秒（高优先级，短延迟）
        // - 优先级10:  15 - 10 = 5秒
        // - 优先级5:   15 - 5  = 10秒  
        // - 优先级1:   15 - 1  = 14秒
        // - 优先级0:   15 - 0  = 15秒
        //
        // 示例（负数优先级）：
        // - 优先级-1:  15 - (-1) = 16秒
        // - 优先级-5:  15 - (-5) = 20秒
        // - 优先级-10: 15 - (-10) = 25秒（低优先级，长延迟）
        // - 优先级-50: 15 - (-50) = 65 → min(60, 65) = 60秒（最长不超过60秒）
        int baseDelay = Math.max(3, Math.min(60, 15 - currentPriorityValue));
        
        // 添加基础延迟
        exitCommand.append(" sleep ").append(baseDelay);
        
        // 如果有更高优先级的容器需要先退出，等待其退出信号
        if (preContainer != null) {
            String signalFile = "/workload/" + preContainer.getName() + ".exit";
            exitCommand.append(" && while [ ! -f ").append(signalFile).append(" ]; do sleep 1; done");
            LOGGER.info("Container {} will wait for container {} to exit first", currContainer.getName(), preContainer.getName());
        }
        
        // 如果有更低优先级的容器需要等待当前容器，写入退出信号
        if (nextContainer != null) {
            String signalFile = "/workload/" + currContainer.getName() + ".exit";
            exitCommand.append(" && echo 'exited' > ").append(signalFile);
            LOGGER.info("Container {} will signal exit to container {}", currContainer.getName(), nextContainer.getName());
        }
        
        // 如果既没有需要等待的容器，也没有需要通知的容器，记录日志
        if (preContainer == null && nextContainer == null) {
            // 只有基础延迟，说明没有其他依赖逻辑，这是正常情况
            LOGGER.info("Container {} has no priority dependencies, using base delay: {} seconds", currContainer.getName(), baseDelay);
        }
        
        return exitCommand;
    }

    /**
     * 获取当前容器的上一个退出优先级容器（需要先于当前容器退出的容器）
     * @param currentContainerPurchase 当前容器
     * @param containerPurchases 按退出优先级排序的容器列表（降序：高优先级在前）
     * @return 上一个退出优先级的容器，如果没有则返回null
     */
    private ContainerPurchase getPreviousExitPriorityContainer(ContainerPurchase currentContainerPurchase, List<ContainerPurchase> containerPurchases) {
        if (currentContainerPurchase == null || containerPurchases == null || containerPurchases.isEmpty()) {
            return null;
        }

        // 获取当前容器的退出优先级
        String currentPriority = getPriority(currentContainerPurchase, BCI_CONTAINER_EXIT_PRIORITY);
        int currentPriorityValue = Integer.parseInt(currentPriority);

        ContainerPurchase targetContainer = null;
        int highestPriorityValue = -1;

        // 查找优先级比当前容器高的容器中，优先级最高的那个（数值最大）
        for (ContainerPurchase container : containerPurchases) {
            if (container.equals(currentContainerPurchase)) {
                continue; // 跳过自己
            }
            
            String priority = getPriority(container, BCI_CONTAINER_EXIT_PRIORITY);
            int priorityValue = Integer.parseInt(priority);

            // 找到优先级比当前容器高，且是所有高优先级容器中最高的
            if (priorityValue > currentPriorityValue && priorityValue > highestPriorityValue) {
                targetContainer = container;
                highestPriorityValue = priorityValue;
            }
        }

        if (targetContainer != null) {
            LOGGER.info("Found previous exit container: {} (priority {}) should exit before {} (priority {})", 
                targetContainer.getName(), highestPriorityValue, currentContainerPurchase.getName(), currentPriorityValue);
        }

        return targetContainer;
    }

    /**
     * 获取当前容器的下一个退出优先级容器（需要等待当前容器退出的容器）
     * @param currentContainerPurchase 当前容器
     * @param containerPurchases 按退出优先级排序的容器列表（降序：高优先级在前）
     * @return 下一个退出优先级的容器，如果没有则返回null
     */
    private ContainerPurchase getNextExitPriorityContainer(ContainerPurchase currentContainerPurchase, List<ContainerPurchase> containerPurchases) {
        if (currentContainerPurchase == null || containerPurchases == null || containerPurchases.isEmpty()) {
            return null;
        }

        // 获取当前容器的退出优先级
        String currentPriority = getPriority(currentContainerPurchase, BCI_CONTAINER_EXIT_PRIORITY);
        int currentPriorityValue = Integer.parseInt(currentPriority);

        ContainerPurchase targetContainer = null;
        int lowestPriorityValue = Integer.MAX_VALUE;

        // 查找优先级比当前容器低的容器中，优先级最低的那个（数值最小）
        for (ContainerPurchase container : containerPurchases) {
            if (container.equals(currentContainerPurchase)) {
                continue; // 跳过自己
            }
            
            String priority = getPriority(container, BCI_CONTAINER_EXIT_PRIORITY);
            int priorityValue = Integer.parseInt(priority);

            // 找到优先级比当前容器低，且是所有低优先级容器中最低的
            if (priorityValue < currentPriorityValue && priorityValue < lowestPriorityValue) {
                targetContainer = container;
                lowestPriorityValue = priorityValue;
            }
        }

        if (targetContainer != null) {
            LOGGER.info("Found next exit container: {} (priority {}) should wait for {} (priority {}) to exit", 
                targetContainer.getName(), lowestPriorityValue, currentContainerPurchase.getName(), currentPriorityValue);
        }

        return targetContainer;
    }

    /**
     * 生成EXEC需要的容器挂载信息
     * @return
     */
    private V1VolumeMount parseContainerLifecycleMounts() {
        V1VolumeMount lifeCycleMount = new V1VolumeMount();
        lifeCycleMount.setName(BCI_EXEC_BIN_NAME);
        lifeCycleMount.setMountPath(BCI_EXEC_VOLUME_CONTAINER_PATH);
        return lifeCycleMount;
    }

    /**
     * 获取容器生命周期挂载的卷
     * @param orderExtra
     * @return
     */
    private List<V1Volume> getContainerLifecycleVolumes(BciOrderExtra orderExtra) {
        List<V1Volume> volumes = new ArrayList<>();
        if (hasContainerPriority(orderExtra.getContainers())) {
            V1Volume lifecycleMount = new V1Volume();
            lifecycleMount.setName(BCI_EXEC_VOLUME_MOUNT_NAME);
            V1HostPathVolumeSource volumeSource = new V1HostPathVolumeSource();
            volumeSource.setPath(BCI_PROBE_VOLUME_HOST_PATH);
            lifecycleMount.setHostPath(volumeSource);
            volumes.add(lifecycleMount);

            V1Volume emptyDirVolume = new V1Volume();
            emptyDirVolume.setName(WORKLOAD_EMPTY_DIR_MOUNT_NAME);
            emptyDirVolume.setEmptyDir(new V1EmptyDirVolumeSource());
            volumes.add(emptyDirVolume);
        }
        return volumes;
    }

    /**
     * 判断容器是否设置有启动/退出优先级, 若所有容器设置相同的启动优先级和相同的退出优先级，则退化为未设置启动/退出优先级，返回false
     * @param containerPurchases 容器列表
     * @return
     */
    private Boolean hasContainerPriority(List<ContainerPurchase> containerPurchases) {
        if (containerPurchases == null || containerPurchases.isEmpty()) {
            return false;
        }

        String firstLaunchPriority = null;
        String firstExitPriority = null;

        for (ContainerPurchase container : containerPurchases) {
            if (container == null) {
                continue;
            }

            String currentLaunchPriority = getPriority(container, BCI_CONTAINER_LAUNCH_PRIORITY);
            String currentExitPriority = getPriority(container, BCI_CONTAINER_EXIT_PRIORITY);

            if (firstLaunchPriority == null) {
                firstLaunchPriority = currentLaunchPriority;
                firstExitPriority = currentExitPriority;
                continue;
            }

            if (!Objects.equals(firstLaunchPriority, currentLaunchPriority) || 
                !Objects.equals(firstExitPriority, currentExitPriority)) {
                return true;
            }
        }

        return false;
    }
}
