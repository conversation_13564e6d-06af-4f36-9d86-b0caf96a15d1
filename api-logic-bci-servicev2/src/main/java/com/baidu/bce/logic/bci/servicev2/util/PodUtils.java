package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.PodEventPO;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.common.ApiUtilV2;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ContainerStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PodVolumeType;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.model.BaseVolume;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.Bos;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPreviousState;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.model.FlexVolume;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.Nfs;
import com.baidu.bce.logic.bci.servicev2.model.Pfs;
import com.baidu.bce.logic.bci.servicev2.model.PodCondition;
import com.baidu.bce.logic.bci.servicev2.model.PodDetail;
import com.baidu.bce.logic.bci.servicev2.model.PodExtra;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.Port;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.sync.service.PodContainerSyncServiceV2;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1EnvVar;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1Volume;
import io.kubernetes.client.openapi.JSON;
import okhttp3.MediaType;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.apache.commons.net.util.SubnetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.bci.servicev2.constant.BciOrderConstant.BCI_ORDER_ITEM_KEY_PREFIX;

public class PodUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodUtils.class);

    public static final String PATTERN_NAME;
    public static final long FIVE_MINUTES_MILLISECOND = 1000 * 60 * 5;
    private static final String PATTERN_PASSWORD;
    public static final String SEARCH_VALUE;

    static {
        PATTERN_NAME = "^(?!^[-_/.])[\u4e00-\u9fa5a-zA-Z\\d-_/.]{2,256}$";
        PATTERN_PASSWORD = "^(?![0-9]+$)(?![a-zA-Z]+$)(?![!@#$%^*()]+$)[0-9A-Za-z!@#$%^*()]{8,32}$";
        SEARCH_VALUE = "[\u4e00-\u9fa5a-zA-Z\\d-_/.]{1,65}";
    }

    public static boolean validaterName(String name) {
        Pattern pattern = Pattern.compile(PATTERN_NAME);
        return pattern.matcher(name).matches();
    }

    public static <T> List<T> subList(List<T> list, int pageNo, int pageSize) {
        if (CollectionUtils.isEmpty(list) || list.size() < pageSize) {
            return list;
        }
        List<T> listPage = new ArrayList<>();
        for (int i = pageSize * (pageNo - 1); i < list.size() && i < pageNo * pageSize; i++) {
            listPage.add(list.get(i));
        }
        return listPage;
    }

    public static PodDetail convertPodDetailShow(PodPO podPO, List<ContainerPO> containers) {
        PodDetail podDetail = convertBean(podPO, PodDetail.class);

        podDetail.setNfs(JsonUtil.toList(podPO.getNfs(), Nfs.class));
        podDetail.setPfs(JsonUtil.toList(podPO.getPfs(), Pfs.class));
        podDetail.setBos(JsonUtil.toList(podPO.getBos(), Bos.class));
        podDetail.setHostPath(JsonUtil.toList(podPO.getHostPath(), HostPathVolume.class));
        podDetail.setEmptyDir(JsonUtil.toList(podPO.getEmptyDir(), EmptyDir.class));
        podDetail.setPodVolumes(JsonUtil.toList(podPO.getPodVolumes(), ServersResponse.ServerBciCds.class));
        podDetail.setConfigFile(base64Decode(podPO.getConfigFile()));
        podDetail.setLabels(JsonUtil.toList(podPO.getTags(), Label.class));
        podDetail.setCpu(podPO.getvCpu());
        podDetail.setCpuType(podPO.getCpuType());
        podDetail.setPushLog(podPO.getEnableLog() == 1);
        // 设置pod status condition
        podDetail.setConditions(JsonUtil.toList(podPO.getConditions(), PodCondition.class));
        podDetail.setTidal(podPO.isTidal());

        // 设置vk 传递的extras字段
        if (StringUtils.isNotEmpty(podPO.getExtra())) {
            PodExtra podExtra = JsonUtil.fromJSON(podPO.getExtra(), PodExtra.class);
            podDetail.setExtras(podExtra == null ? null : podExtra.getVkExtras());
        }
        List<PodDetail.ContainerDetail> containerDetails = new ArrayList<>();
        for (ContainerPO container : containers) {
            PodDetail.ContainerDetail containerDetail = convertBean(container, PodDetail.ContainerDetail.class);
            PodDetail.ContainerStatus status = new PodDetail.ContainerStatus();
            status.setPreviousState(JsonUtil.fromJSON(container.getPreviousState(), ContainerPreviousState.class));
            status.setCurrentState(JsonUtil.fromJSON(container.getCurrentState(), ContainerCurrentState.class));
            status.setRestartCount(container.getRestartCount());
            if (container.getReady() != null && !container.getReady().equals("")) {
                if (container.getReady().equalsIgnoreCase("true")) {
                    status.setReady(true);
                }
                if (container.getReady().equalsIgnoreCase("false")) {
                    status.setReady(false);
                }
            }
            if (container.getStarted() != null && !container.getStarted().equals("")) {
                if (container.getStarted().equalsIgnoreCase("true")) {
                    status.setStarted(true);
                }
                if (container.getStarted().equalsIgnoreCase("false")) {
                    status.setStarted(false);
                }
            }

            containerDetail.setName(container.getName());
            containerDetail.setContainerType(container.getContainerType());
            containerDetail.setStatus(status);
            containerDetail.setArgs(JsonUtil.toList(container.getArgs(), String.class));
            containerDetail.setCommands(JsonUtil.toList(container.getCommands(), String.class));
            containerDetail.setPorts(JsonUtil.toList(container.getPorts(), Port.class));
            containerDetail.setVolumeMounts(JsonUtil.toList(container.getVolumeMounts(), VolumeMounts.class));
            containerDetail.setEnvs(JsonUtil.toList(container.getEnvs(), Environment.class));
            // 若容器包含gpu则需要计算容器与pod的显存
            if (StringUtils.isNotEmpty(containerDetail.getGpuType())) {
                float containerGpuMemory = containerDetail.getGpuCount() *
                        PodConfiguration.gpuSpecGpuMemoryMap.get(containerDetail.getGpuType());
                containerDetail.setGpuMemory(containerGpuMemory);
                podDetail.setGpuMemory(podDetail.getGpuMemory() + containerGpuMemory);
            }
            containerDetails.add(containerDetail);
        }
        podDetail.setContainers(containerDetails);
        return podDetail;
    }

    /**
     * @Description:
     * 将PodPO和ContainerPO转换为PodDetail，并设置labels、cpu、pushLog等属性。
     * 同时设置pod status condition、tidal、extras、v2等属性。
     *
     * @Param podPO PodPO - Pod对象
     * @Param containers List<ContainerPO> - 包含多个ContainerPO对象的列表
     *
     * @Return PodDetail - 返回一个PodDetail对象，包含了所有需要展示的信息
     *
     * @Throws None
     */
    public static PodDetail convertPodDetailLightShow(PodPO podPO, List<ContainerPO> containers) {
        PodDetail podDetail = convertPodPOToPodDetail(podPO);
        // 设置vk 传递的extras字段
        if (StringUtils.isNotEmpty(podPO.getExtra())) {
            PodExtra podExtra = JsonUtil.fromJSON(podPO.getExtra(), PodExtra.class);
            podDetail.setExtras(podExtra == null ? null : podExtra.getVkExtras());
        }

        List<PodDetail.ContainerDetail> containerDetails = new ArrayList<>();
        for (ContainerPO container : containers) {
            PodDetail.ContainerDetail containerDetail = convertContainerPOToContainerDetail(container);
            PodDetail.ContainerStatus status = new PodDetail.ContainerStatus();
            status.setPreviousState(JsonUtil.fromJSON(container.getPreviousState(), ContainerPreviousState.class));
            status.setCurrentState(JsonUtil.fromJSON(container.getCurrentState(), ContainerCurrentState.class));
            status.setRestartCount(container.getRestartCount());
            if (container.getReady() != null && !container.getReady().equals("")) {
                if (container.getReady().equalsIgnoreCase("true")) {
                    status.setReady(true);
                }
                if (container.getReady().equalsIgnoreCase("false")) {
                    status.setReady(false);
                }
            }
            if (container.getStarted() != null && !container.getStarted().equals("")) {
                if (container.getStarted().equalsIgnoreCase("true")) {
                    status.setStarted(true);
                }
                if (container.getStarted().equalsIgnoreCase("false")) {
                    status.setStarted(false);
                }
            }
            containerDetail.setStatus(status);

            // 若容器包含gpu则需要计算容器与pod的显存
            if (StringUtils.isNotEmpty(containerDetail.getGpuType())) {
                float containerGpuMemory = containerDetail.getGpuCount() *
                        PodConfiguration.gpuSpecGpuMemoryMap.get(containerDetail.getGpuType());
                containerDetail.setGpuMemory(containerGpuMemory);
                podDetail.setGpuMemory(podDetail.getGpuMemory() + containerGpuMemory);
            }
            containerDetails.add(containerDetail);
        }
        podDetail.setContainers(containerDetails);
        return podDetail;
    }

    public static PodDetail convertPodPOToPodDetail(PodPO podPO) {
        PodDetail podDetail = new PodDetail();
        podDetail.setName(podPO.getName());
        podDetail.setPodId(podPO.getPodId());
        podDetail.setPodUuid(podPO.getPodUuid());
        podDetail.setStatus(podPO.getStatus());
        podDetail.setNodeName(podPO.getNodeName());
        podDetail.setCpu(podPO.getvCpu());
        podDetail.setMemory(podPO.getMemory());
        podDetail.setProductType(podPO.getProductType());
        podDetail.setGpuType(podPO.getGpuType());
        podDetail.setGpuCount(podPO.getGpuCount());
        podDetail.setPublicIp(podPO.getPublicIp());
        podDetail.setCceUuid(podPO.getCceUuid());
        podDetail.setInternalIp(podPO.getInternalIp());
        podDetail.setInternalIPv6(podPO.getInternalIPv6());
        podDetail.setRestartPolicy(podPO.getRestartPolicy());
        podDetail.setOrderId(podPO.getOrderId());
        podDetail.setDescription(podPO.getDescription());
        podDetail.setUserId(podPO.getUserId());
        podDetail.setLogicalZone(podPO.getLogicalZone());
        podDetail.setTaskStatus(podPO.getTaskStatus());
        podDetail.setCreatedTime(podPO.getCreatedTime());
        podDetail.setUpdatedTime(podPO.getUpdatedTime());
        podDetail.setDeletedTime(podPO.getDeletedTime());
        podDetail.setApplication(podPO.getApplication());
        podDetail.setLabels(JsonUtil.toList(podPO.getTags(), Label.class));
        podDetail.setCpuType(podPO.getCpuType());
        podDetail.setPushLog(podPO.getEnableLog() == 1);
        podDetail.setTidal(podPO.isTidal());
        // 设置pod status condition
        podDetail.setConditions(JsonUtil.toList(podPO.getConditions(), PodCondition.class));
        podDetail.setDelayReleaseDurationMinute(podPO.getDelayReleaseDurationMinute());
        podDetail.setDelayReleaseSucceeded(podPO.isDelayReleaseSucceeded());
        podDetail.setV2(podPO.isV2());
        return podDetail;
    }

    public static PodDetail.ContainerDetail convertContainerPOToContainerDetail(ContainerPO container) {
        PodDetail.ContainerDetail containerDetail = new PodDetail.ContainerDetail();
        containerDetail.setName(container.getName());
        containerDetail.setContainerType(container.getContainerType());
        containerDetail.setContainerUuid(container.getContainerUuid());
        containerDetail.setImageName(container.getImageName());
        containerDetail.setImageVersion(container.getImageVersion());
        containerDetail.setImageID(container.getImageID());
        containerDetail.setImageAddress(container.getImageAddress());
        containerDetail.setImagePullPolicy(container.getImagePullPolicy());
        containerDetail.setCpu(container.getCpu());
        containerDetail.setMemory(container.getMemory());
        containerDetail.setGpuCount(container.getGpuCount());
        containerDetail.setGpuType(container.getGpuType());
        containerDetail.setWorkingDir(container.getWorkingDir());
        containerDetail.setCommands(JsonUtil.toList(container.getCommands(), String.class));
        containerDetail.setArgs(JsonUtil.toList(container.getArgs(), String.class));
        containerDetail.setPorts(JsonUtil.toList(container.getPorts(), Port.class));
        // containerDetail.setVolumeMounts(JsonUtil.toList(container.getVolumeMounts(), VolumeMounts.class));
        containerDetail.setEnvs(JsonUtil.toList(container.getEnvs(), Environment.class));
        containerDetail.setUserId(container.getUserId());
        containerDetail.setCreatedTime(container.getCreatedTime());
        containerDetail.setUpdatedTime(container.getUpdatedTime());
        containerDetail.setDeletedTime(container.getDeletedTime());
        return containerDetail;
    }

    /**
     * 将一个对象转换为另一个对象
     *
     * @param <T1>      要转换的对象
     * @param <T2>      转换后的类
     * @param orimodel  要转换的对象
     * @param castClass 转换后的类
     * @return 转换后的对象
     */
    public static <T1, T2> T2 convertBean(T1 orimodel, Class<T2> castClass) {
        T2 returnModel = null;
        try {
            returnModel = castClass.newInstance();
        } catch (Exception e) {
            throw new RuntimeException("创建" + castClass.getName() + "对象失败");
        }
        List<Field> fieldList = new ArrayList<>(); // 要转换的字段集合
        while (castClass != null && // 循环获取要转换的字段,包括父类的字段
                !castClass.getName().toLowerCase().equals("java.lang.object")) {
            fieldList.addAll(Arrays.asList(castClass.getDeclaredFields()));
            castClass = (Class<T2>) castClass.getSuperclass(); // 得到父类,然后赋给自己
        }
        for (Field field : fieldList) {
            PropertyDescriptor getpd = null;
            PropertyDescriptor setpd = null;
            try {
                getpd = new PropertyDescriptor(field.getName(), orimodel.getClass());
                setpd = new PropertyDescriptor(field.getName(), returnModel.getClass());
            } catch (Exception e) {
                continue;
            }
            try {
                Method getMethod = getpd.getReadMethod();
                if (getMethod != null) {  // 检查 getter 方法是否存在
                    Class<?> returnType = getMethod.getReturnType();
                    Method setMethod = setpd.getWriteMethod();
                    if (setMethod != null) {  // 检查 setter 方法是否存在
                        Class<?>[] parameterTypes = setMethod.getParameterTypes();
                        if (parameterTypes.length == 1 && parameterTypes[0].isAssignableFrom(returnType)) {
                            // 确保 getter 返回类型和 setter 参数类型兼容
                            Object transValue = getMethod.invoke(orimodel);
                            setMethod.invoke(returnModel, transValue);
                        } else {
                            LOGGER.warn("skip property " + field.getName() + " due to type mismatch");
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.warn("parse property {}, error:", field.getName(), e);
            }
        }
        return returnModel;
    }

    public static List<ConfigFile> base64Decode(String json) {
        List<ConfigFile> configFiles = JsonUtil.toList(json, ConfigFile.class);
        if (CollectionUtils.isEmpty(configFiles)) {
            return null;
        }
        for (ConfigFile configFile : configFiles) {
            if (CollectionUtils.isEmpty(configFile.getConfigFiles())) {
                continue;
            }
            for (ConfigFileDetail configFileDetail : configFile.getConfigFiles()) {
                if (StringUtils.isNotEmpty(configFileDetail.getFile())) {
                    configFileDetail.setFile(new String(Base64.decodeBase64(configFileDetail.getFile())));
                }
            }
        }
        return configFiles;
    }

    public static final <T> List<T> equalFilter(List<T> list, String propertyName,
                                                Object propertyValue, boolean filterWithNull) {
        List<T> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        boolean valueIsNull = propertyValue == null;
        if (!filterWithNull && valueIsNull) {
            return result;
        }
        for (T t : list) {
            Object value = ApiUtilV2.getValue(t, propertyName);
            if (valueIsNull) {
                if (value == null) {
                    result.add(t);
                }
            } else {
                if (propertyValue.equals(value)) {
                    result.add(t);
                }
            }
        }
        return result;
    }

    public static final <T> List<T> likeFilter(List<T> list, String propertyName, String propertyValue) {
        List<T> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        if (propertyValue == null) {
            return list;
        }
        String regex = ".*" + escapeSpecialLetter(propertyValue) + ".*";
        Pattern p = Pattern.compile(regex);
        for (T t : list) {
            String value = (String) ApiUtilV2.getValue(t, propertyName);
            if (value != null) {
                Matcher m = p.matcher(value);
                if (m.matches()) {
                    result.add(t);
                }
            }
        }
        return result;
    }

    /**
     * 转义正则特殊字符 （$()*+.[]?\^{},|）
     *
     * @param keyword
     * @return
     */
    public static final String escapeSpecialLetter(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return keyword;
        }

        StringBuilder builder = new StringBuilder(keyword);

        /**
         * 对\的转义需先做
         */
        String[] specialLetters = {"\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|"};
        for (String letter : specialLetters) {
            if (keyword.contains(letter)) {
                keyword = keyword.replace(letter, "\\" + letter);
            }
        }
        return keyword;
    }

    public static final <T> List<T> generalLikeFilter(List<T> list, String propertyValue) {
        List<T> result = new ArrayList<>();
        Set<T> resSet = new HashSet<T>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        if (propertyValue == null) {
            return list;
        }
        Class clazz = list.get(0).getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // 为了避免统计单测覆盖率的时候往里面注入的新的字段
            if (!field.isSynthetic()) {
                String propertyName = field.getName();
                resSet.addAll(likeFilter(list, propertyName, propertyValue));
            }
        }

        result.addAll(resSet);
        return result;
    }

    /**
     * {@summary 将PodListRequest转换为指定类型的对象}
     * 如果keywordType和keyword都不为空，则将它们合并到filterMap中。
     * 然后使用PodUtils.convertBean方法将PodListRequest转换为指定类型的对象返回。
     *
     * @param podListRequest PodListRequest对象
     * @param clazz 目标对象的Class类型参数，需要转换成的类型
     * @param <T> 目标对象的泛型参数，需要转换成的类型
     * @return 转换后的目标对象，clazz类型
     */
    public static <T> T convertRequestModel(PodListRequest podListRequest, Class<T> clazz) {
        // keywordType/keyword 统一到filter
        if (StringUtils.isNotEmpty(podListRequest.getKeywordType()) &&
                StringUtils.isNotEmpty(podListRequest.getKeyword())) {
            if (null == podListRequest.getFilterMap()) {
                podListRequest.setFilterMap(new HashMap<String, String>());
            }
            podListRequest.getFilterMap().put(podListRequest.getKeywordType(), podListRequest.getKeyword());
        }

        return PodUtils.convertBean(podListRequest, clazz);
    }

    /**
     * 为返回值对象添加分页信息：page类型
     *
     * @param listRequest list请求
     * @return response
     */
    public static <T> LogicPageResultResponse<T> initEdpPageResultResponse(PodListRequest listRequest) {
        LogicPageResultResponse<T> pageResultResponse = new LogicPageResultResponse<>();
        OrderModel orderModel = listRequest.getOrders() == null ? new OrderModel() : listRequest.getOrders().get(0);
        pageResultResponse.setOrder(orderModel.getOrder());
        pageResultResponse.setOrderBy(orderModel.getOrderBy());
        pageResultResponse.setPageNo(listRequest.getPageNo());
        pageResultResponse.setPageSize(listRequest.getPageSize());
        return pageResultResponse;
    }

    public static <T> LogicMarkerResultResponse<T> initMarkerResultResponse(PodListRequest listRequest) {
        LogicMarkerResultResponse<T> markerResultResponse = new LogicMarkerResultResponse<>();
        markerResultResponse.setMarker(listRequest.getMarker());
        markerResultResponse.setMaxKeys(listRequest.getMaxKeys());
        return markerResultResponse;
    }

    public static <T> LogicPageResultResponse<T> getEmptyResponse(LogicPageResultResponse<T> resultResponse) {
        resultResponse.setTotalCount(0);
        resultResponse.setResult(new ArrayList<T>());
        return resultResponse;
    }

    public static <T> LogicMarkerResultResponse<T> getEmptyResponse(LogicMarkerResultResponse<T> resultResponse) {
        resultResponse.setIsTruncated(false);
        resultResponse.setResult(new ArrayList<T>());
        return resultResponse;
    }

    public static List<String> getTags(ImageTags imageTags) {
        List<String> userImageTags = new ArrayList<>();
        List<ImageTags.Tags> tags = imageTags.getTags();
        for (ImageTags.Tags tag : tags) {
            userImageTags.add(tag.getName());
        }
        return userImageTags;
    }

    public static void quickSort(ArrayList<PodEventPO> list, int left, int right, boolean flag) {
        if (left >= right) {
            return;
        }
        int i = left;
        int j = right;
        PodEventPO podEventPO = list.get(left);

        if (flag) {
            while (i < j) {
                while (i < j && podEventPO.getEventTime().compareTo(list.get(j).getEventTime()) > 0) {
                    j--;
                }
                list.set(i, list.get(j));
                while (i < j && podEventPO.getEventTime().compareTo(list.get(i).getEventTime()) <= 0) {
                    i++;
                }
                list.set(j, list.get(i));
            }
        } else {
            while (i < j) {
                while (i < j && podEventPO.getEventTime().compareTo(list.get(j).getEventTime()) <= 0) {
                    j--;
                }
                list.set(i, list.get(j));
                while (i < j && podEventPO.getEventTime().compareTo(list.get(i).getEventTime()) > 0) {
                    i++;
                }
                list.set(j, list.get(i));
            }
        }
        list.set(i, podEventPO);

        quickSort(list, left, i - 1, flag);
        quickSort(list, i + 1, right, flag);
    }

    public static BciOrderExtra getOrderExtra(String extra) throws IOException {
        BciOrderExtra orderExtra = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // extra中有BciOrderExtra不存在的字段,忽略
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            orderExtra = objectMapper.readValue(extra, BciOrderExtra.class);
        } catch (IOException e) {
            LOGGER.error("订单extra解析失败");
            throw e;
        }
        return orderExtra;
    }

    public static String getExtraByServiceType(Order order, String serviceType) {

        if (StringUtils.isEmpty(serviceType)) {
            return "";
        }
        for (Order.Item item : order.getItems()) {
            if (serviceType.equalsIgnoreCase(item.getServiceType())) {
                return item.getExtra();
            }
        }
        return "";
    }

    public static String createLongUuid() {
        return UUID.randomUUID().toString();
    }

    public static String buildConfigMapName(String podId, String configName) {
        return podId + "-" + configName;
    }

    public static V1ConfigMap buildV1ConfigMap(String namespace, String name, ConfigFile config) {
        // 封装k8sConfigMap
        V1ConfigMap k8sConfigMap = new V1ConfigMap();
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setNamespace(namespace);
        meta.setName(name);
        k8sConfigMap.setMetadata(meta);

        Map<String, byte[]> data = new HashMap<>();
        for (ConfigFileDetail detail : config.getConfigFiles()) {
            // base64 解码
            data.put(detail.buildConfigKeyFromPath(), Base64.decodeBase64(detail.getFile()));
        }

        k8sConfigMap.setBinaryData(data);
        return k8sConfigMap;
    }

    public static Response rectifyContainerLogResponse(String containerName, ContainerCurrentState state, 
                                                       Response response) {
        if (state != null && StringUtils.equals(ContainerStatus.CONTAINER_STATE_CREATING, state.getState())) {
            if (StringUtils.isEmpty(state.getDetailStatus())) {
                return response;
            }
            String[] splits = state.getDetailStatus().split(PodContainerSyncServiceV2.reasonAndMessageDelimiter);
            if (splits.length <= 0) {
                return response;
            }
            String reason = splits[0];
            if (StringUtils.equals(PodConstants.CONTAINER_PULL_IMAGE_ERROR, reason)) {
                return response.newBuilder().body(newRespForBadReq(response.body().contentType(), containerName, 
                                                                   "trying and failing to pull image")).build();
            }
            if (StringUtils.equals(PodConstants.CONTAINER_PULLING_IMAGE, reason)) {
                return response.newBuilder().body(newRespForBadReq(response.body().contentType(), containerName, 
                                                                   "trying to pull image")).build();
            }
        }
        return response;
    }

    private static ResponseBody newRespForBadReq(MediaType contentType, String containerName, String message) {
        String errorMsg = String.format("container \"%s\" is waiting to start: %s", containerName, message);   
        return ResponseBody.create(errorMsg, contentType);
    }

    public static boolean isSubnetOverlap(String cidr1, String cidr2) {
        SubnetUtils.SubnetInfo subnetCidr = new SubnetUtils(cidr1).getInfo();
        SubnetUtils.SubnetInfo clusterCidr = new SubnetUtils(cidr2).getInfo();

        if (subnetCidr.isInRange(clusterCidr.getLowAddress())
                || subnetCidr.isInRange(clusterCidr.getHighAddress())
                || clusterCidr.isInRange(subnetCidr.getLowAddress())
                || clusterCidr.isInRange(subnetCidr.getHighAddress())) {
            return true;
        }
        return false;
    }

    public static String getChargeStatusOfCpt1Instance(String status, int delayReleaseDurationMinute, 
                                                       boolean delayReleaseSucceeded, long resourceRecycleTimestamp, 
                                                       int resourceRecycleComplete, String resourceRecycleReason) {
            // 对于 cpt1 实例，理论上计费场景应该有四个：
            //   1. 实例状态是 running
            //   2. 实例状态是 succeed 且开启了延迟释放，且对 succeed 实例生效，配置了资源回收时间戳，资源回收原因是Job任务结束，资源回收还未结束
            //   3. 实例状态是 failed 且开启了延迟释放，配置了资源回收时间戳，资源回收原因是Job任务结束，资源回收还未结束
            //   4. 实例状态是 pending，但是已经调度成功
            // 其中场景4暂未实现，等状态机的子状态包含待调度、已调度时再补充。
            if (BciStatus.RUNNING.getName().equalsIgnoreCase(status)) {
                return PodConstants.CHARGE;
            }
            if (BciStatus.SUCCEEDED.getName().equalsIgnoreCase(status)
                 && delayReleaseDurationMinute != 0 
                 && delayReleaseSucceeded
                 && resourceRecycleTimestamp != 0 
                 && resourceRecycleComplete == 0
                 && ResourceRecycleReason.JOB_POD_COMPLETE.toString().equals(resourceRecycleReason)) {
                return PodConstants.CHARGE;
            }
            if (BciStatus.FAILED.getName().equalsIgnoreCase(status)
                 && delayReleaseDurationMinute != 0 
                 && resourceRecycleTimestamp != 0 
                 && resourceRecycleComplete == 0
                 && ResourceRecycleReason.JOB_POD_COMPLETE.toString().equals(resourceRecycleReason)) {
                return PodConstants.CHARGE;
            }
            return PodConstants.NO_CHARGE;
    }

    /**
     * 更新expectDsVolumes到volumes中
     *   1.对于volumes中的非dsVolume，直接保留
     *   2.对于volumes中的dsVolume，如果也在expectDsVolumes，直接保留
     *   3.对于volumes中的dsVolume，如果不在expectDsVolumes，删除
     *   4.对于volumes中没有的dsVolume，如果在expectDsVolumes，增加
     */
    public static <T extends BaseVolume> List<T> refreshVolumes(List<T> oldVolumes, List<T> expectDsVolumes) {
        List<T> newVolumes = new ArrayList<>();
        Set<String> expectDsVolNames = getVolume2TypeMap(expectDsVolumes, "", true).keySet();
        Set<String> actualDsVolNames = getVolume2TypeMap(oldVolumes, "", true).keySet();
        if (oldVolumes != null) {
            // 把非ds卷、保持不变的ds卷放入最终结果中，并删除需要删除的ds卷
            for (T vol : oldVolumes) {
                if (vol.isDsVolume()) {
                    // 对于volumes中的dsVolume，如果也在expectDsVolumes，直接保留
                    if (expectDsVolNames.contains(vol.getName())) {
                        newVolumes.add(vol);
                    }
                    // 对于volumes中的dsVolume，如果不在expectDsVolumes，删除
                } else {
                    // 对于volumes中的非dsVolume，直接保留
                    newVolumes.add(vol);
                }
            }
        }
        if (expectDsVolumes != null) {
            for (T vol : expectDsVolumes) {
                // 对于volumes中没有的dsVolume，如果在expectDsVolumes，增加
                if (!actualDsVolNames.contains(vol.getName())) {
                    newVolumes.add(vol);
                }
            }
        }
        return newVolumes;
    }

    public static Volume getVolumeFromPodPO(PodPO podPO) {
        Volume volumes = new Volume();
        volumes.setNfs(JsonUtil.toList(podPO.getNfs(), Nfs.class));
        volumes.setEmptyDir(JsonUtil.toList(podPO.getEmptyDir(), EmptyDir.class));
        // 注意：
        // 排查代码发现 podVolumes 在 bciv2 的请求预期中并没有被实际用到，而是作为 pod 实际挂载的 volume 的现状信息。
        // 由于 podVolumes 是从底层k8s同步到bci控制面的，而非用户指定的，并且 podVolumes 字段实际值的数据类型不再是 PodVolumne.class，所以此处不会解析。
        // TODO: 后续考虑将 podVolume 从 request 中去除。
        volumes.setConfigFile(JsonUtil.toList(podPO.getConfigFile(), ConfigFile.class));
        volumes.setFlexVolume(JsonUtil.toList(podPO.getFlexVolume(), FlexVolume.class));
        volumes.setPfs(JsonUtil.toList(podPO.getPfs(), Pfs.class));
        volumes.setBos(JsonUtil.toList(podPO.getBos(), Bos.class));
        volumes.setHostPath(JsonUtil.toList(podPO.getHostPath(), HostPathVolume.class));
        return volumes;
    }

    private static Map<String, String> getVolume2TypeMap(List<? extends BaseVolume> volumes, String volumeType, 
                                                         boolean isDsVolume) {
        Map<String, String> volumeMap = new HashMap<String, String>();
        if (volumes == null) {
            return volumeMap;
        }
        for (BaseVolume volume : volumes) {
            if (isDsVolume == volume.isDsVolume()) {
                volumeMap.put(volume.getName(), volumeType);
            }
        }
        return volumeMap;
    }

    public static Map<String, String> getVolume2TypeMap(Volume vo, boolean isDsVolume) {
        Map<String, String> volumeMap = new HashMap<String, String>();
        if (vo == null) {
            return volumeMap;
        }
        volumeMap.putAll(getVolume2TypeMap(vo.getNfs(), PodVolumeType.NFS.getType(), isDsVolume));
        volumeMap.putAll(getVolume2TypeMap(vo.getEmptyDir(), PodVolumeType.EMPTY_DIR.getType(), isDsVolume));
        volumeMap.putAll(getVolume2TypeMap(vo.getConfigFile(), PodVolumeType.CONFIG_FILE.getType(), isDsVolume));
        // 注意：
        // 排查代码发现 podVolumes 在 bciv2 的请求预期中并没有被实际用到，而是作为 pod 实际挂载的 volume 的现状信息。
        // 由于 podVolumes 是从底层k8s同步到bci控制面的，而非用户指定的，并且 podVolumes 的类不再是 PodVolumne，所以此处不会解析。
        // TODO: 后续考虑将 podVolume 从 request 中去除。
        volumeMap.putAll(getVolume2TypeMap(vo.getFlexVolume(), PodVolumeType.FLEX_VOLUME.getType(), isDsVolume));
        volumeMap.putAll(getVolume2TypeMap(vo.getPfs(), PodVolumeType.PFS.getType(), isDsVolume));
        volumeMap.putAll(getVolume2TypeMap(vo.getBos(), PodVolumeType.BOS.getType(), isDsVolume));
        volumeMap.putAll(getVolume2TypeMap(vo.getHostPath(), PodVolumeType.HOST_PATH.getType(), isDsVolume));
        volumeMap.putAll(getVolume2TypeMap(vo.getCephfs(), PodVolumeType.CEPHFS.getType(), isDsVolume));
        return volumeMap;
    }

    private static <T extends BaseVolume> List<T> filterVolume(List<T> volumes, boolean isDsVolume) {
        List<T> result = new ArrayList<>();
        for (T vol : volumes) {
            if (vol.isDsVolume() == isDsVolume) {
                result.add(vol);
            }
        }
        return result;
    }

    public static Volume filterVolumes(Volume vo, boolean isDsVolume) {
        Volume finalVolume = new Volume();
        if (vo == null) {
            return finalVolume;
        }
        finalVolume.setNfs(filterVolume(vo.getNfs(), isDsVolume));
        finalVolume.setEmptyDir(filterVolume(vo.getEmptyDir(), isDsVolume));
        finalVolume.setConfigFile(filterVolume(vo.getConfigFile(), isDsVolume));
        // 注意：
        // 排查代码发现 podVolumes 在 bciv2 的请求预期中并没有被实际用到，而是作为 pod 实际挂载的 volume 的现状信息。
        // 由于 podVolumes 是从底层k8s同步到bci控制面的，而非用户指定的，并且 podVolumes 的类不再是 PodVolumne，所以此处不会解析。
        // TODO: 后续考虑将 podVolume 从 request 中去除。
        finalVolume.setFlexVolume(filterVolume(vo.getFlexVolume(), isDsVolume));
        finalVolume.setPfs(filterVolume(vo.getPfs(), isDsVolume));
        finalVolume.setBos(filterVolume(vo.getBos(), isDsVolume));
        finalVolume.setHostPath(filterVolume(vo.getHostPath(), isDsVolume));
        finalVolume.setCephfs(filterVolume(vo.getCephfs(), isDsVolume));
        return finalVolume;
    }

    public static boolean contains(List<V1Volume> volumes, String volumeName) {
        for (V1Volume volume : volumes) {
            if (StringUtils.equals(volume.getName(), volumeName)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isOrderV3(List<Label> metadataLabels) {
        if (metadataLabels != null) {
            for (Label label : metadataLabels) {
                if (label.getLabelKey().equals("bci3")) {
                    LOGGER.info("enable bci3 by label");
                    return true;
                }
            }
        }
        return false;
    }

    public static V1Pod deepCloneV1Pod(V1Pod origin) {
        // deep copy origin
        JSON json = new JSON();
        return json.deserialize(json.serialize(origin), V1Pod.class);
    }

    public static String getOriginContainerImageName(List<V1EnvVar> envs) {
        if (envs == null) {
            return null;
        }
        for (V1EnvVar env : envs) {
            if (PodConstants.ORIGIN_CONTAINER_IMAGE_NAME.equals(env.getName())) {
                return env.getValue();
            }
        }
        return null;
    }

    /**
     * 从orderItem中解析podId
     * @param orderItem
     * @return String
     */
    public static String parsePodIdFromOrderItem(Order.Item orderItem) {
        // key:bci-p-xaw7cc61
        // return: p-xaw7cc61
        String key = orderItem.getKey();
        if (StringUtils.isEmpty(key)) {
            LOGGER.error("key is empty, orderItem:{}", orderItem);
            return null;
        }
        // 首先判断key是否以ORDER_ITEM_KEY_PREFIX(bci-)开头
        // 如果是,则返回之后的字符串
        // 如果不是,则返回空null
        if (key.startsWith(BCI_ORDER_ITEM_KEY_PREFIX)) {
            return key.substring(BCI_ORDER_ITEM_KEY_PREFIX.length());
        } else {
            return null;
        }
    }

    public static String getPodAnnotationValueByKey(V1Pod pod, String key) {
        if (pod == null || pod.getMetadata() == null || pod.getMetadata().getAnnotations() == null) {
            return null;
        }
        return pod.getMetadata().getAnnotations().get(key);
    }
}
