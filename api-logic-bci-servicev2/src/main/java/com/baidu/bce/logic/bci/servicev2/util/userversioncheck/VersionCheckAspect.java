package com.baidu.bce.logic.bci.servicev2.util.userversioncheck;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache.Version;

@Aspect
@Component("VersionCheckAspect")
@Order(Integer.MAX_VALUE - 200)
public class VersionCheckAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(VersionCheckAspect.class);

    @Pointcut("@annotation(versionCheck)")
    public void versionCheckPointcut(VersionCheck versionCheck) {
    }

    @Autowired
    PodServiceV2 podService;

    @Autowired
    VersionCache versionCache;

    @Value("${bce.logical.versioncheck.enabled:true}")
    private boolean enable;

    @Around("versionCheckPointcut(versionCheck)")
    public Object versionCheck(ProceedingJoinPoint proceedingJoinPoint, VersionCheck versionCheck) throws Throwable {
        if (!enable) {
            return proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
        }
        // 0.获取拦截类名和方法名
        String className = proceedingJoinPoint.getTarget().getClass().getSimpleName();
        String methodName = proceedingJoinPoint.getSignature().getName();
        // 1.获取用户账户Id
        String accountId = podService.getAccountId();
        if (StringUtils.isEmpty(accountId)) {
            LOGGER.error("user:{} version check fail in {}.{}, because user is empty.",
                    accountId, className, methodName);
            throw new VersionCheckException("version check error, please try again later.",
                    VersionCheckException.VersionCheckErrorCode.INTERNAL_SERVER_ERROR);
        }
        // 2.从cache中获取用户版本
        Version version = versionCache.getUserVersion(accountId);
        // 3.判定是否版本一致
        if (versionCheck.Version() != version) {
            LOGGER.error("user:{} version check fail in {}.{}, because user version is {} but check version is {}.",
                    accountId, className, methodName, version, versionCheck.Version());
            throw new VersionCheckException("version permission deny, version check fail",
                    VersionCheckException.VersionCheckErrorCode.PERMISSION_DENY);
        }
        return proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
    }
}
