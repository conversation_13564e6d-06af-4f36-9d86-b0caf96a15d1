package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class VolumeMounts {

    private String mountPath;
    private Boolean readOnly;
    private String name;
    private String type;
    private String subPath;
    // 数据卷子路径
    private String subPathExpr;

    public VolumeMounts() {
    }

    public VolumeMounts(VolumeMount volumeMount) {
        this.mountPath = volumeMount.getMountPath();
        this.readOnly = volumeMount.getReadOnly();
        this.name = volumeMount.getName();
        this.type = volumeMount.getType();
        this.subPath = volumeMount.getSubPath();
        this.subPathExpr = volumeMount.getSubPathExpr();
    }
}
