package com.baidu.bce.logic.bci.servicev2.resource;

import com.baidu.bce.billing.auditing.sdk.domain.DeleteResult;
import com.baidu.bce.billing.auditing.sdk.domain.ResourceDetail;
import com.baidu.bce.billing.auditing.sdk.domain.ResourceInfo;
import com.baidu.bce.billing.auditing.sdk.domain.StartResult;
import com.baidu.bce.billing.auditing.sdk.domain.StopResult;
import com.baidu.bce.billing.auditing.sdk.domain.ClearResult;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.request.ResourceRequest;
import com.baidu.bce.billing.auditing.sdk.ResourceService;
import com.baidu.bce.billing.auditing.sdk.domain.OperatorContext;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
@Service
public class BciBillingResourceService implements ResourceService {
    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;
    
    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    protected ReservedInstanceDao reservedInstanceDao;

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    protected RegionConfiguration regionConfiguration;
    
    protected enum BciSubResourceType {
        POD, RESERVED_INSTANCE
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(BciBillingResourceService.class);

    @Override
    public ResourceDetail get(String accountId, String service, String resourceId, String region) {
        ResourceDetail resourceDetailResp = new ResourceDetail();
        resourceDetailResp.setAccountId(accountId);
        resourceDetailResp.setService(service.toUpperCase());
        resourceDetailResp.setId(resourceId);
        resourceDetailResp.setRegion(region);
        com.baidu.bce.billing.resourcemanager.model.ResourceDetail detail = getResourceDetail(accountId, service, 
                resourceId, region);
        if (detail != null && detail.getServiceType().equals("BCI")) {
            if (detail.getSubProductType().equals("ReservedPackage")) {
                // 处理预留实例券
                ReservedInstancePO reservedInstancePO = reservedInstanceDao.getReservedInstanceByReservedInstanceUuid(
                        resourceId);
                if (reservedInstancePO != null) {
                    resourceDetailResp.setStatus(ReservedInstancePO.convertToBillingStatus(
                            reservedInstancePO.getStatus()));
                    resourceDetailResp.setName(reservedInstancePO.getReservedInstanceId());
                }
            } else if (detail.getSubProductType().isEmpty()) {
                // 处理 pod
                PodPO podPO = podDao.getPodById(resourceId);
                if (podPO != null) {
                    resourceDetailResp.setStatus(BciStatus.convertToBillingStatus(podPO.getStatus()));
                    resourceDetailResp.setName(podPO.getPodId());
                }
            }
        }
        return resourceDetailResp;
    }

    @Override
    public StartResult start(ResourceInfo resource, OperatorContext context) {
        com.baidu.bce.billing.resourcemanager.model.ResourceDetail detail = getResourceDetail(resource.getAccountId(),
                resource.getService(), resource.getResourceId(), regionConfiguration.getCurrentRegion());
        if (detail != null && detail.getServiceType().equals("BCI")) {
            // 暂时不支持pod重建
            if (detail.getSubProductType().equals("ReservedPackage")) {
                LOGGER.debug("[BillingResourceService] reserved_instance_uuid {}, action start, reason {}",
                        resource.getResourceId(), context.getReason());
                // 处理预留实例券
                ReservedInstancePO reservedInstancePO = reservedInstanceDao.getReservedInstanceByReservedInstanceUuid(
                        resource.getResourceId());
                if (reservedInstancePO != null) {
                    reservedInstanceDao.updateStatus(resource.getAccountId(), 
                            reservedInstancePO.getReservedInstanceId(), 
                            ReservedInstancePO.Status.INACTIVE);
                }
            }
        }
        return StartResult.STARTED;
    }

    @Override
    public StopResult stop(ResourceInfo resource, OperatorContext context) {
        com.baidu.bce.billing.resourcemanager.model.ResourceDetail detail = getResourceDetail(resource.getAccountId(),
                resource.getService(), resource.getResourceId(), regionConfiguration.getCurrentRegion());
        if (detail != null && detail.getServiceType().equals("BCI")) {
            if (detail.getSubProductType().equals("ReservedPackage")) {
                LOGGER.debug("[BillingResourceService] reserved_instance_uuid {}, action stop, reason {}",
                        resource.getResourceId(), context.getReason());
                // 处理预留实例券
                ReservedInstancePO reservedInstancePO = reservedInstanceDao.getReservedInstanceByReservedInstanceUuid(
                        resource.getResourceId());
                String reservedInstanceStatus = ReservedInstancePO.Status.EXPIRED;
                if (context.getReason() != null && context.getReason().equals("OVERDUE")) {
                    reservedInstanceStatus = ReservedInstancePO.Status.OVERDUE;
                }
                if (reservedInstancePO != null) {        
                    reservedInstanceDao.updateStatus(resource.getAccountId(), 
                            reservedInstancePO.getReservedInstanceId(), 
                            reservedInstanceStatus);
                }
            } else if (detail.getSubProductType().isEmpty()) {
                // 处理 pod
                PodPO podPO = podDao.getPodById(resource.getResourceId());              
            }
        }
        return StopResult.STOPPED;
    }

    @Override
    public DeleteResult delete(ResourceInfo resource, OperatorContext context) {
        com.baidu.bce.billing.resourcemanager.model.ResourceDetail detail = null;
        try {
            // 预留实例券场景下，由于量包由billing负责销毁，bci 接收到回调时资源已经不存在，在获取资源详情时进行 try-catch
            detail = getResourceDetail(resource.getAccountId(),
                resource.getService(), resource.getResourceId(), regionConfiguration.getCurrentRegion());
        } catch (Exception | NoClassDefFoundError e) {
            // 返回错误信息时，sdk 无法成功解析，会报异常 java.lang.NoClassDefFoundError 这里捕获异常，不影响哦后续处理
            LOGGER.error("[BillingResourceService] get resource failed, resource: {}, {}", resource, e.getMessage());
        }
        
        BciSubResourceType bciSubResourceType = null;
        // 预留实例券对应用量包实体由billing托管，启停接口回调销毁时，量包已经被销毁，查询 detail 会返回 null。
        // pod 对应实体由 bci 自身管理，启停接口回调销毁时，pod 绑定的 resource 还存在，返回不会出现 null 的情况
        if (detail == null) {
            bciSubResourceType = BciSubResourceType.RESERVED_INSTANCE;
        } else if (detail != null && detail.getServiceType().equals("BCI")) {
            if (detail.getSubProductType().equals("ReservedPackage")) {
                bciSubResourceType = BciSubResourceType.RESERVED_INSTANCE;
            } else if (detail.getSubProductType().isEmpty()) {
                bciSubResourceType = BciSubResourceType.POD;
            }
        }
        if (bciSubResourceType == BciSubResourceType.RESERVED_INSTANCE) {
            LOGGER.debug("[BillingResourceService] reserved_instance_uuid {}, action delete, reason {}",
                    resource.getResourceId(), context.getReason());
            // 处理预留实例券
            ReservedInstancePO reservedInstancePO = reservedInstanceDao.getReservedInstanceByReservedInstanceUuid(
                    resource.getResourceId());
            if (reservedInstancePO != null) {
                reservedInstanceDao.delete(reservedInstancePO.getId());
            }
        } else if (bciSubResourceType == BciSubResourceType.POD) {
            // 处理 pod
            PodPO podPO = podDao.getPodById(resource.getResourceId());
        }
        return DeleteResult.DELETED;
    }

    @Override
    public ClearResult clear(ResourceInfo resource, OperatorContext context) {
        return ClearResult.CLEAR;
    }

    private com.baidu.bce.billing.resourcemanager.model.ResourceDetail getResourceDetail(
            String accountId, String service, String resourceId, String region) {
        ChargeResourceService chargeResourceService = logicPodClientFactory.createChargeResourceService(accountId);
        ResourceRequest resourceRequest = new ResourceRequest();
        resourceRequest.setAccountId(accountId);
        resourceRequest.setName(resourceId);
        resourceRequest.setRegion(region);
        resourceRequest.setServiceType(service);
        return chargeResourceService.getAliveResourceDetail(resourceRequest);
    } 
}
