package com.baidu.bce.logic.bci.servicev2.constant;

import com.baidu.bce.billing.auditing.sdk.domain.ResourceStatus;

public enum BciStatus {

    PENDING("pending", BciStatus.STATUS_PENDING),
    RUNNING("running", BciStatus.STATUS_RUNNING),
    FAILED("failed", BciStatus.STATUS_FAILED),
    SUCCEEDED("succeeded", BciStatus.STATUS_SUCCEEDED),
    UNKNOWN("unknown", BciStatus.STATUS_UNKNOWN),
    DELETED("deleted", BciStatus.STATUS_DELETED),
    UNUSUAL_ORDER("unusualOrder", BciStatus.STATUS_UNUSUAL_ORDER),
    EXPIRED("expired", BciStatus.STATUS_EXPIRED),
    RESCHEDULING("rescheduling", BciStatus.STATUS_RESCHEDULING);

    private String name;
    private String status;

    public static final String STATUS_PENDING = "Pending";
    public static final String STATUS_RUNNING = "Running";
    public static final String STATUS_FAILED = "Failed";
    public static final String STATUS_SUCCEEDED = "Succeeded";
    public static final String STATUS_UNKNOWN = "Unknown";

    /**
     * 注意:历史遗留原因!
     * deleted 状态比较特殊,其他状态都是以大写字母开头,而deleted是小写字母开头
     */
    public static final String STATUS_DELETED = "deleted";
    public static final String STATUS_UNUSUAL_ORDER = "UnusualOrder";
    public static final String STATUS_EXPIRED = "Expired";
    public static final String STATUS_RESCHEDULING = "Rescheduling";

    BciStatus(String name, String status) {
        this.name = name;
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public String getStatus() {
        return status;
    }

    public static String getNameByStatus(String status) {
        for (BciStatus bciStatus : values()) {
            if (bciStatus.getStatus().equalsIgnoreCase(status)) {
                return bciStatus.getName();
            }
        }
        return status;
    }

    public static String getStatusByName(String name) {
        return getStatus(name);
    }

    public static String getStatus(String name) {
        for (BciStatus bciStatus : values()) {
            if (bciStatus.getName().equalsIgnoreCase(name)) {
                return bciStatus.getStatus();
            }
        }
        return name;
    }

    public static String getBiddingConvertStatus(String status, String preemptStatus) {
        if (PENDING.getStatus().equals(status)) {
            return "Bidding";
        }
        if (SUCCEEDED.getStatus().equals(status) &&
                PreemptStatus.PREEMPTED.name().equals(preemptStatus)) {
            return "Recycled";
        }
        return status;
    }

    public static ResourceStatus convertToBillingStatus(String status) {
        if (status.equals(RUNNING.getStatus())) {
            return ResourceStatus.RUNNING;
        } else if (status.equals(FAILED.getStatus())) {
            return ResourceStatus.DESTROYED;
        } else if (status.equals(SUCCEEDED.getStatus())) {
            return ResourceStatus.RUNNING;
        } else if (status.equals(DELETED.getStatus())) {
            return ResourceStatus.DESTROYED;
        } else if (status.equals(EXPIRED.getStatus())) {
            return ResourceStatus.DESTROYED;
        }
        return ResourceStatus.RUNNING;
    }
}
