package com.baidu.bce.logic.bci.servicev2.statemachine.context;

import com.baidu.bce.logic.bci.servicev2.statemachine.exception.StateMachineException;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class StateMachineEventContext {
    private String podId = "";
    private String userId = "";
    private long newResourceVersion = 0;
    private String newStatus = "";

    public StateMachineException exception;

    public StateMachineEventContext() {
        exception = new StateMachineException();
    }

    public StateMachineEventContext(String podId, String userId) {
        this.podId = podId;
        this.userId = userId;
        exception = new StateMachineException();
    }

    public StateMachineEventContext(String podId, String userId,
                                    long newResourceVersion) {
        this(podId, userId);
        this.newResourceVersion = newResourceVersion;
        exception = new StateMachineException();
    }

    public StateMachineEventContext(String podId, String userId,
                                    long newResourceVersion, String newStatus) {
        this(podId, userId, newResourceVersion);
        this.newStatus = newStatus;
        exception = new StateMachineException();
    }
}
