package com.baidu.bce.logic.bci.servicev2.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigFileDetail {
    private String path;
    private String file;

    /**
     * vk 传递过来的path 有可能携带了子路径,此处将子路径拼接作为configMap的 key
     *
     * @return
     */
    public String buildConfigKeyFromPath() {
        if (StringUtils.isEmpty(this.path)) {
            return this.path;
        }
        String[] split = path.split("/");
        if (split.length == 1) {
            return this.path;
        }
        return StringUtils.join(split, "_", 0, split.length);
    }

    /**
     * 判断path是否包含子路径
     *
     * @return
     */
    public boolean hasSubPath() {
        if (StringUtils.isEmpty(this.path)) {
            return false;
        }
        String[] split = path.split("/");
        if (split.length == 1) {
            return false;
        }
        return true;
    }
}