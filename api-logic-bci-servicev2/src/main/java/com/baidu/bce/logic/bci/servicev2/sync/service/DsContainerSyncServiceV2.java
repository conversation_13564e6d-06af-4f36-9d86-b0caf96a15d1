package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerSecurityContext;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.Lifecycle;
import com.baidu.bce.logic.bci.servicev2.model.Port;
import com.baidu.bce.logic.bci.servicev2.model.Probe;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.bci.servicev2.util.Util;
import io.kubernetes.client.openapi.models.V1ConfigMapVolumeSource;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ExecAction;
import io.kubernetes.client.openapi.models.V1HostPathVolumeSource;
import io.kubernetes.client.openapi.models.V1Lifecycle;
import io.kubernetes.client.openapi.models.V1LifecycleHandler;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1Volume;
import io.kubernetes.client.openapi.models.V1VolumeMount;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class DsContainerSyncServiceV2 extends SyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(DsContainerSyncServiceV2.class);

    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PodNewOrderExecutorServiceV2 orderExecutorService;

    @Autowired
    private PodExtraDaoV2 podExtraDaoV2;

    private class NonSyncedPods {
        List<PodPO> actualSynced = new ArrayList<>();
        List<PodPO> actualUnsync = new ArrayList<>();
    }

    private V1Pod getK8sPodOrNullIfPodIsInvalid(String userId, String podId) {
        V1Pod k8sPodInfo = k8sService.getPod(userId, podId);
        if (k8sPodInfo == null || k8sPodInfo.getSpec() == null || k8sPodInfo.getMetadata() == null 
            || k8sPodInfo.getMetadata().getAnnotations() == null || k8sPodInfo.getMetadata().getNamespace() == null) {
            LOGGER.error("get k8s pod error, podName {}, podInfo {}", podId, JsonUtil.toJSON(k8sPodInfo));
            return null;
        }
        return k8sPodInfo;
    }

    private List<V1Container> genK8sContainerByBciContainer(BciOrderExtra orderExtra, 
                                                            List<ContainerPO> bciContainers) throws IOException {
        List<ContainerPurchase> bciContainerPurchases = new ArrayList<>();
        for (ContainerPO c : bciContainers) {
            bciContainerPurchases.add(convertContainerPOToPurchase(c));
        }
        List<V1Container> dsContainers = orderExecutorService.geneContainers(
            bciContainerPurchases, ContainerType.DS_WORKLOAD, new ArrayList<>(), orderExtra.isV3());
        return dsContainers;
    }

    private Map<String, String> getExpectDsContainer2ImageContainer(Map<String, String> oldChangedDsCon2ImageCon,
                                                                    Set<String> needAddedUpdatedDsContainers) {
        Map<String, String> expectDsContainer2ImageContainer = new HashMap<>();
        Set<String> usedImageContainerNames = new HashSet<>(oldChangedDsCon2ImageCon.values());
        // 无论是新增的ds容器，还是升级的ds容器，都需要分配一个新的镜像下载容器名字。
        // 因为容器只支持增删，不支持修改command，所以就算是存量需要升级的ds容器，也需要分配一个新的镜像下载容器，和原有镜像下载容器区分开，避免产生容器更新动作。
        for (String needAddedUpdatedDsContainer : needAddedUpdatedDsContainers) {
            String imageContainerName = "";
            for (int i = 0; i < 99; i++) {
                String temp = PodConstants.BCI_IMAGE_DOWNLOAD_CONTAINER_PREFIX 
                            + PodConstants.BCI_IMAGE_DOWNLOAD_DS_WORKLOAD_INFIX + i;
                if (usedImageContainerNames.contains(temp)) {
                    continue;
                }
                imageContainerName = temp;
                usedImageContainerNames.add(imageContainerName);
                break;
            }
            if (StringUtils.isEmpty(imageContainerName)) {
                return null;
            }
            expectDsContainer2ImageContainer.put(needAddedUpdatedDsContainer, imageContainerName);
        }
        return expectDsContainer2ImageContainer;
    }

    private NonSyncedPods getNonSyncedPods() {        
        NonSyncedPods nonSyncedPods = new NonSyncedPods();
        // 每次取 100 个处理
        for (PodPO podPO : podDao.listPodsWhichNotSyncDsContainersToK8S()) {
            V1Pod k8sPodInfo = getK8sPodOrNullIfPodIsInvalid(podPO.getUserId(), podPO.getPodId());
            if (k8sPodInfo == null) {
                continue;
            }
            Map<String, String> anno = k8sPodInfo.getMetadata().getAnnotations();
            Long actualDsVersion = Long.parseLong(anno.getOrDefault(PodConstants.BCI_DS_CONTAINERS_VERSION, "0"));
            if (actualDsVersion < podPO.getDsContainersVersion()) {
                nonSyncedPods.actualUnsync.add(podPO);
            } else {
                LOGGER.debug("actualDsVersion: {},  getDsContainersVersion: {}", actualDsVersion, 
                             podPO.getDsContainersVersion());
                nonSyncedPods.actualSynced.add(podPO);
            }
        }
        return nonSyncedPods;
    }

    public void syncDsContainers() {
        // 获取实际已同步的bci实例，以及实际未同步的bci实例，并标记实际已同步的bci实例为已同步状态
        LOGGER.debug("start to sync ds containers");
        NonSyncedPods nonSyncedPods = getNonSyncedPods();
        podDao.batchMarkDsConatinersSynced(nonSyncedPods.actualSynced);

        // 将ds containers同步到k8s
        for (PodPO podPO : nonSyncedPods.actualUnsync) {
            try {
                LOGGER.debug("start to sync ds container {}", podPO.getPodId());

                V1Pod origin = getK8sPodOrNullIfPodIsInvalid(podPO.getUserId(), podPO.getPodId());
                if (origin == null) {
                    continue;
                }
                V1Pod k8sPodInfo = PodUtils.deepCloneV1Pod(origin);
                // BciOrderExtra orderExtra = orderExecutorService.getOrderExtra(podPO.getOrderId(), podPO.getPodId());
                PodExtraPO podExtraPO = podExtraDaoV2.getPodExtraByPodIdIgnoreStatus(podPO.getPodId());
                if (podExtraPO == null || StringUtils.isEmpty(podExtraPO.getOrderExtra())) {
                    LOGGER.error("sync ds container {}, fail to get podExtra from db", podPO.getPodId());
                    continue;
                }
                BciOrderExtra orderExtra = PodUtils.getOrderExtra(podExtraPO.getOrderExtra());
                orderExecutorService.setV3FlagByLabel(orderExtra);

                // STEP.1 获取预期中的ds容器、ds镜像容器、ds卷等
                Map<String, ContainerPO> expectDsContainerPOMap = new HashMap<>();
                for (ContainerPO containerPO : containerDao.listByPodId(podPO.getPodUuid())) {
                    if (ContainerType.DS_WORKLOAD.getType().equals(containerPO.getContainerType())) {
                        expectDsContainerPOMap.put(containerPO.getName(), containerPO);
                    }
                }
                // podPO表中记录的ds容器数和containerPO表中查到的实际ds容器数不一致，说明存在主从延迟等不一致的情况，暂不同步。
                if (podPO.getDsContainersCount() != expectDsContainerPOMap.size()) {
                    LOGGER.debug("pod: {}, dsContainersCount({}) in podPO is different with dsContainersCount({}) in containerPO", 
                                 podPO.getPodId(), podPO.getDsContainersCount(), expectDsContainerPOMap.size());
                    continue;
                }
                Map<String, String> expectDsContainerVersionMap = new HashMap<>();
                // 预期所有新增、更新的ds容器 -> 对应的镜像下载容器的映射
                Map<String, String> expectChangedDsContainer2ImageContainer;
                Map<String, EmptyDir> expectDsEmptyDirs = new HashMap<>();
                Map<String, ConfigFile> expectDsConfigfiles = new HashMap<>();
                Map<String, HostPathVolume> expectDsHostPaths = new HashMap<>();
                Map<String, String> expectDsVolumeName2Type = new HashMap<>();
                for (EmptyDir emptyDir : JsonUtil.toList(podPO.getEmptyDir(), EmptyDir.class)) {
                    if (emptyDir.isDsVolume()) {
                        expectDsEmptyDirs.put(emptyDir.getName(), emptyDir);
                    }
                }
                for (ConfigFile config : JsonUtil.toList(podPO.getConfigFile(), ConfigFile.class)) {
                    if (config.isDsVolume()) {
                        expectDsConfigfiles.put(config.getName(), config);
                    }
                }
                for (HostPathVolume hostPath : JsonUtil.toList(podPO.getHostPath(), HostPathVolume.class)) {
                    if (hostPath.isDsVolume()) {
                        expectDsHostPaths.put(hostPath.getName(), hostPath);
                    }
                }
                LOGGER.debug("expect ds containers: {}, podId: {}", expectDsContainerPOMap.keySet(), podPO.getPodId());
                
                // STEP.2 获取k8s pod中实际的ds容器、ds镜像容器、ds卷
                Map<String, String> anno = k8sPodInfo.getMetadata().getAnnotations();
                Set<String> actualDsContainers = Util.getListFromString(
                    anno.getOrDefault(PodConstants.BCI_DS_CONTAINER_NAMES, ""));
                Map<String, String> actualChangedDsContainer2ImageContainer = Util.getMapFromString(
                    anno.getOrDefault(PodConstants.BCI_CHANGED_DS_CONTAINER_2_IMAGE_CONTAINER, ""));
                Map<String, String> actualDsVolumeName2Type = Util.getMapFromString(
                    anno.getOrDefault(PodConstants.BCI_DS_VOLUME_NAME_2_TYPE, ""));
                Map<String, String> actualDsContainerVersionMap = Util.getMapFromString(
                    anno.getOrDefault(PodConstants.BCI_DS_CONTAINER_VERSION_MAP, ""));
                
                // STEP.3 生成需要新增、更新、删除的ds容器
                Map<String, ContainerPO> needAddedUpdatedDsContainerPOMap = new HashMap<>();
                Set<String> needDeletedDsContainers = new HashSet<>();
                for (String cName : expectDsContainerPOMap.keySet()) {
                    ContainerPO expectDsContainer = expectDsContainerPOMap.get(cName);
                    expectDsContainerVersionMap.put(cName, String.valueOf(expectDsContainer.getDsContainerVersion()));
                    if (!actualDsContainers.contains(cName)) {
                        needAddedUpdatedDsContainerPOMap.put(cName, expectDsContainer);
                    } else {
                        // 只有容器版本变了，才会更新
                        long actualContainerV = Long.parseLong(actualDsContainerVersionMap.getOrDefault(cName, "0"));
                        if (actualContainerV != expectDsContainer.getDsContainerVersion()) {
                            needAddedUpdatedDsContainerPOMap.put(cName, expectDsContainer);
                        }
                    }
                }
                for (String cName : actualDsContainers) {
                    if (!expectDsContainerPOMap.containsKey(cName)) {
                        needDeletedDsContainers.add(cName);
                    }
                }
                LOGGER.debug("needDeletedDsContainers: {}, podId: {}", needDeletedDsContainers, podPO.getPodId());
                List<V1Container> needAddedUpdatedDsContainers = genK8sContainerByBciContainer(orderExtra, 
                                                        new ArrayList<>(needAddedUpdatedDsContainerPOMap.values()));
                LOGGER.debug("needAddedUpdatedDsContainers: {}, podId: {}", needAddedUpdatedDsContainerPOMap.keySet(),
                             podPO.getPodId());
                
                // STEP.4 生成需要新增/更新、删除的ds镜像容器
                expectChangedDsContainer2ImageContainer = getExpectDsContainer2ImageContainer(
                    actualChangedDsContainer2ImageContainer, needAddedUpdatedDsContainerPOMap.keySet());
                // 生成过程有异常，直接跳过
                if (expectChangedDsContainer2ImageContainer == null) {
                    LOGGER.warn("get image container name for new added ds container error, podid is {}",
                                podPO.getPodId());
                    continue;
                }
                // 此前所有的ds镜像下载容器都会全部删掉，然后重新添加需要新增的ds镜像下载容器
                Set<String> needAddedUpdatedImageContainerNames = 
                    new HashSet<>(expectChangedDsContainer2ImageContainer.values());
                Set<String> needDeletedImageContainerNames = 
                    new HashSet<>(actualChangedDsContainer2ImageContainer.values());
                
                Map<String, String> expectImageContainer2DsContainer = new HashMap<>();
                // 暂时不支持ds容器使用镜像加速功能，所以 geneImageContainers 的 applyImageAccelerateCons 参数是一个空列表
                List<V1Container> needAddedUpdatedImageContainers = orderExecutorService.geneImageContainers(
                    orderExtra, needAddedUpdatedDsContainers, ContainerType.DS_WORKLOAD,
                    true, new ArrayList<>(), expectChangedDsContainer2ImageContainer, expectImageContainer2DsContainer);
                // 为镜像下载容器补充 poststart 命令
                addPostStartForImageDownloadContainers(needAddedUpdatedImageContainers);
                // STEP.5 生成需要创建、删除的ds卷，目前仅支持emptyDir、configmap、hostPath
                Map<String, EmptyDir> needAddedDsEmptyDirs = new HashMap<>();
                Set<String> needDeletedDsEmptyDirs = new HashSet<>();
                Map<String, ConfigFile> needAddedDsConfigfiles = new HashMap<>();
                Set<String> needDeletedDsConfigfiles = new HashSet<>();
                Map<String, HostPathVolume> needAddedDsHostPaths = new HashMap<>();
                Set<String> needDeletedDsHostPaths = new HashSet<>();
                for (EmptyDir emptyDir : expectDsEmptyDirs.values()) {
                    if (!actualDsVolumeName2Type.containsKey(emptyDir.getName())) {
                        needAddedDsEmptyDirs.put(emptyDir.getName(), emptyDir);
                    }
                    expectDsVolumeName2Type.put(emptyDir.getName(), PodConstants.EMPTYDIR);
                }
                for (ConfigFile configFile : expectDsConfigfiles.values()) {
                    if (!actualDsVolumeName2Type.containsKey(configFile.getName())) {
                        needAddedDsConfigfiles.put(configFile.getName(), configFile);
                    }
                    expectDsVolumeName2Type.put(configFile.getName(), PodConstants.CONFIGFILE);
                }
                for (HostPathVolume hostPath : expectDsHostPaths.values()) {
                    if (!actualDsVolumeName2Type.containsKey(hostPath.getName())) {
                        needAddedDsHostPaths.put(hostPath.getName(), hostPath);
                    }
                    expectDsVolumeName2Type.put(hostPath.getName(), PodConstants.HOSTPATH);
                }
                for (String volumeName : actualDsVolumeName2Type.keySet()) {
                    String type = actualDsVolumeName2Type.get(volumeName);
                    // 现状有，预期没有，放在待删除列表中
                    if (StringUtils.equals(PodConstants.EMPTYDIR, type) 
                        && !expectDsEmptyDirs.containsKey(volumeName)) {
                        needDeletedDsEmptyDirs.add(volumeName);
                    }
                    if (StringUtils.equals(PodConstants.CONFIGFILE, type) 
                        && !expectDsConfigfiles.containsKey(volumeName)) {
                        needDeletedDsConfigfiles.add(volumeName);
                    }
                    if (StringUtils.equals(PodConstants.HOSTPATH, type) 
                        && !expectDsHostPaths.containsKey(volumeName)) {
                        needDeletedDsHostPaths.add(volumeName);
                    }
                }

                // STEP.6 生成预期中的容器、卷
                List<V1Container> expectContainers = new ArrayList<>();
                List<V1Volume> expectVolumes = new ArrayList<>();
                // 将无需任何变更的容器、卷放入预期中
                for (V1Container container : k8sPodInfo.getSpec().getContainers()) {
                    if (needAddedUpdatedDsContainerPOMap.containsKey(container.getName()) 
                        || needDeletedDsContainers.contains(container.getName())
                        || needAddedUpdatedImageContainerNames.contains(container.getName())
                        || needDeletedImageContainerNames.contains(container.getName())) {
                        continue;
                    }
                    expectContainers.add(container);
                }
                for (V1Volume volume : k8sPodInfo.getSpec().getVolumes()) {
                    if (needAddedDsEmptyDirs.containsKey(volume.getName())
                        || needDeletedDsEmptyDirs.contains(volume.getName())
                        || needAddedDsConfigfiles.containsKey(volume.getName())
                        || needDeletedDsConfigfiles.contains(volume.getName())
                        || needAddedDsHostPaths.containsKey(volume.getName())
                        || needDeletedDsHostPaths.contains(volume.getName())) {
                        continue;
                    }
                    expectVolumes.add(volume);
                }
                // 将下载镜像的内置容器放入预期容器列表
                expectContainers.addAll(needAddedUpdatedImageContainers);
                // 将需要新增和升级的容器放入预期中
                expectContainers.addAll(needAddedUpdatedDsContainers);
                // 将需要新增和升级的卷放入预期中
                for (EmptyDir emptyDir : needAddedDsEmptyDirs.values()) {
                    expectVolumes.add(PodNewOrderExecutorServiceV2.convertToK8sEmptyDir(emptyDir));
                }
                for (ConfigFile config : needAddedDsConfigfiles.values()) {
                    expectVolumes.add(orderExecutorService.createAndReturnConfigMapVolume(
                        config, k8sPodInfo.getMetadata().getNamespace(), k8sPodInfo.getMetadata().getName()));
                }
                for (HostPathVolume hostPath : needAddedDsHostPaths.values()) {
                    expectVolumes.add(PodNewOrderExecutorServiceV2.convertToK8sHostPath(hostPath));
                }
                // 镜像下载容器依赖一个volume，如果pod中不存在，需要添加
                if (needAddedUpdatedImageContainers.size() > 0) {
                    // 安全容器中不需要 containerd 下载第三方镜像，所以可以不添加如下的 hostpath
                    if (!orderExtra.isV3() && !PodUtils.contains(expectVolumes, "containerd-socket")) {
                        V1Volume v1Volume = new V1Volume();
                        v1Volume.setName("containerd-socket");
                        V1HostPathVolumeSource v1HostPathVolumeSource = new V1HostPathVolumeSource();
                        v1HostPathVolumeSource.setPath("/var/run/containerd/");
                        v1Volume.setHostPath(v1HostPathVolumeSource);
                        expectVolumes.add(v1Volume);
                    }
                    if (orderExtra.isV3() && !PodUtils.contains(expectVolumes, "katadata")) {
                        V1Volume v1Volume2 = new V1Volume();
                        v1Volume2.setName("katadata");
                        V1HostPathVolumeSource v1HostPathVolumeSource2 = new V1HostPathVolumeSource();
                        v1HostPathVolumeSource2.setPath("/home/<USER>/kata-data");
                        v1Volume2.setHostPath(v1HostPathVolumeSource2);
                        expectVolumes.add(v1Volume2);
                    }
                    // 在控制面启动时,会在所有用户namespace下创建一个sidecar command configMap
                    // 这里无需判断是否存在，直接使用即可
                    if (!PodUtils.contains(expectVolumes, PodNewOrderExecutorServiceV2.SIDECAR_COMMAND_CM_NAME_REF)) {
                        V1Volume sidecarCommandCmVolume = new V1Volume();
                        sidecarCommandCmVolume.setName(PodNewOrderExecutorServiceV2.SIDECAR_COMMAND_CM_NAME_REF);
                        V1ConfigMapVolumeSource sidecarCommandCmSource = new V1ConfigMapVolumeSource();
                        sidecarCommandCmSource.setName(PodNewOrderExecutorServiceV2.SIDECAR_COMMAND_CM_NAME);
                        sidecarCommandCmVolume.setConfigMap(sidecarCommandCmSource);
                        expectVolumes.add(sidecarCommandCmVolume);
                    }
                } 

                // 计算ds功能性容器、需要从qos计算中剔除的容器
                // 1.计算预期所有ds功能性容器，目前只有镜像下载容器，后续可能会有其他种类的容器
                Set<String> dsFunctionalContainers = new HashSet<>(needAddedUpdatedImageContainerNames);
                // 2.在计算qos的时候，应该忽略所有的ds容器和为ds功能性容器，因为这些容器有可能增删，进而导致qos变化，进而导致pod重建
                Set<String> qosIgnoreContainers = new HashSet<String>();
                qosIgnoreContainers.addAll(expectDsContainerPOMap.keySet());
                qosIgnoreContainers.addAll(dsFunctionalContainers);
                
                // STEP.7 将预期更新到 k8s pod
                k8sPodInfo.getSpec().setContainers(expectContainers);
                k8sPodInfo.getSpec().setVolumes(expectVolumes);
                V1ObjectMeta k8sPodMeta = k8sPodInfo.getMetadata();
                k8sPodMeta.getAnnotations().put(PodConstants.BCI_CHANGED_DS_CONTAINER_2_IMAGE_CONTAINER,
                                                Util.saveMapToString(expectChangedDsContainer2ImageContainer));
                k8sPodMeta.getAnnotations().put(PodConstants.BCI_DS_CONTAINER_NAMES,
                                                Util.saveListToString(expectDsContainerPOMap.keySet()));
                k8sPodMeta.getAnnotations().put(PodConstants.BCI_DS_VOLUME_NAME_2_TYPE,
                                                Util.saveMapToString(expectDsVolumeName2Type));
                k8sPodMeta.getAnnotations().put(PodConstants.BCI_DS_CONTAINERS_VERSION, 
                                                String.valueOf(podPO.getDsContainersVersion()));
                k8sPodMeta.getAnnotations().put(PodConstants.BCI_DS_CONTAINER_VERSION_MAP,
                                                Util.saveMapToString(expectDsContainerVersionMap));
                k8sPodMeta.getAnnotations().put(PodConstants.BCI_DS_FUNCTIONAL_CONTAINER_NAMES,
                                                Util.saveListToString(dsFunctionalContainers));
                k8sPodMeta.getAnnotations().put(PodConstants.BCI_IGNORE_THIS_CONTAINERS_COMPUTING_QOS, 
                                                Util.saveListToString(qosIgnoreContainers));
                for (String imageContainerName : needDeletedImageContainerNames) {
                    k8sPodMeta.getAnnotations().remove(imageContainerName);                                         
                }
                for (String imageContainerName : needAddedUpdatedImageContainerNames) {
                    k8sPodMeta.getAnnotations().put(
                        imageContainerName, expectImageContainer2DsContainer.get(imageContainerName));
                }
                k8sService.updatePod(k8sPodMeta.getNamespace(), k8sPodMeta.getName(), k8sPodInfo);

                // STEP.8 清理不再使用的ds configmap
                for (String config : needDeletedDsConfigfiles) {
                    k8sService.deleteConfigMap(k8sPodMeta.getNamespace(), 
                                               PodUtils.buildConfigMapName(podPO.getPodId(), config));
                }
            } catch (Exception e) {
                LOGGER.error("pod {} sync ds containers error", podPO.getPodId(), e);
            }
        }
    }

    private void addPostStartForImageDownloadContainers(List<V1Container> needAddedUpdatedImageContainers) {
        if (needAddedUpdatedImageContainers == null) {
            return;
        }
        
        for (V1Container container : needAddedUpdatedImageContainers) {
            // volume mounts
            if (container.getVolumeMounts() == null) {
                container.setVolumeMounts(new ArrayList<V1VolumeMount>());
            }
            // sidecar command直接打到镜像里,因此这里不需要改在sidecar-command-cm volume
            // 挂载sidecar-commmand-cm的作用其实是想回去sidecar-command-cm里的image_download_check.sh脚本
//            V1VolumeMount sidecarCommandVolumeMount = new V1VolumeMount();
//            sidecarCommandVolumeMount.setName(PodNewOrderExecutorServiceV2.SIDECAR_COMMAND_CM_NAME_REF);
//            sidecarCommandVolumeMount.setMountPath(PodNewOrderExecutorServiceV2.SIDECAR_COMMAND_MOUNT_PATH);
//            container.getVolumeMounts().add(sidecarCommandVolumeMount);

            // common config
            container.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);
            container.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);
            // lifecycle
            String originContainerImage = PodUtils.getOriginContainerImageName(container.getEnv());
            V1Lifecycle lifecycle = new V1Lifecycle();
            V1LifecycleHandler postStart = new V1LifecycleHandler();
            V1ExecAction exec = new V1ExecAction();
            List<String> postStartCommandList = new ArrayList<>();
            postStartCommandList.add("/bin/sh");
            postStartCommandList.add("-c");
            postStartCommandList.add("sh " + PodNewOrderExecutorServiceV2.SIDECAR_COMMAND_MOUNT_PATH 
                                           + PodNewOrderExecutorServiceV2.IMAGE_DOWNLOAD_CHECK_SHELL_NAME
                                           + " " + originContainerImage);
            exec.setCommand(postStartCommandList);
            postStart.setExec(exec);
            lifecycle.setPostStart(postStart);
            container.setLifecycle(lifecycle);
        }
    }

    private ContainerPurchase convertContainerPOToPurchase(ContainerPO po) {
        ContainerPurchase purchase = new ContainerPurchase();
        purchase.setName(po.getName());
        purchase.setContainerType(po.getContainerType());

        purchase.setImageName(po.getImageName());
        purchase.setImageAddress(po.getImageAddress());
        purchase.setImageVersion(po.getImageVersion());
        purchase.setImagePullPolicy(po.getImagePullPolicy());

        purchase.setCpu(po.getCpu());
        purchase.setMemory(po.getMemory());
        purchase.setGpuCount(po.getGpuCount());
        purchase.setGpuType(po.getGpuType());

        purchase.setWorkingDir(po.getWorkingDir());
        purchase.setCommands(JsonUtil.toList(po.getCommands(), String.class));
        purchase.setArgs(JsonUtil.toList(po.getArgs(), String.class));
        purchase.setPorts(JsonUtil.toList(po.getPorts(), Port.class));
        purchase.setEnvs(JsonUtil.toList(po.getEnvs(), Environment.class));
        purchase.setLivenessProbe(JsonUtil.fromJSON(po.getLivenessProbe(), Probe.class));
        purchase.setReadinessProbe(JsonUtil.fromJSON(po.getReadinessProbe(), Probe.class));
        purchase.setStartupProbe(JsonUtil.fromJSON(po.getStartupProbe(), Probe.class));
        purchase.setLifecycle(JsonUtil.fromJSON(po.getLifecycle(), Lifecycle.class));
        purchase.setStdin(po.isStdin());
        purchase.setStdinOnce(po.isStdinOnce());
        purchase.setTty(po.isTty());
        purchase.setSecurityContext(JsonUtil.fromJSON(po.getSecurityContext(), ContainerSecurityContext.class));
        purchase.setVolumeMounts(JsonUtil.toList(po.getVolumeMounts(), VolumeMounts.class));
        // TODO: containerPo 当前没有持久化logCollections，ds容器暂时不支持如下容器配置，后续等持久化后再放开。
        // purchase.setLogCollections();
        return purchase;    
    }

    public void setK8sServiceOnlyUsedInUT(K8sService k8sService) {
        this.k8sService = k8sService;
    }

    public void setOrderExecutorServiceOnlyUsedInUT(PodNewOrderExecutorServiceV2 orderService) {
        this.orderExecutorService = orderService;
    }
}