package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.logic.bci.servicev2.util.UuidUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CreateContainerGroupRequestExtra {
    private String from = "";
    private String clientToken = "";

    private boolean autoGenerateClientToken = false;

    public CreateContainerGroupRequestExtra() {
        this.from = "";
        this.clientToken = this.generateClientToken();

    }

    public CreateContainerGroupRequestExtra(String from) {
        this.from = from;
        this.clientToken = this.generateClientToken();
    }

    public CreateContainerGroupRequestExtra(String from, String clientToken) {
        this.from = from;
        this.clientToken = clientToken;
        if (StringUtils.isEmpty(this.clientToken)) {
            this.clientToken = this.generateClientToken();
        }
    }

    private String generateClientToken() {
        this.autoGenerateClientToken = true;
        return UuidUtil.generateUuid();
    }
}
