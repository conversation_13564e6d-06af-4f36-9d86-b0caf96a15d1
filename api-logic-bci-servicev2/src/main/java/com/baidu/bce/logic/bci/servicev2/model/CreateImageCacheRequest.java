package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)

public class CreateImageCacheRequest {
    
    // 镜像缓存name，同一用户空间下唯一,长度为[2, 128]个英文小写字母、数字或者连字符（-），不能以连接字符开始或结尾
    private String imageCacheName;
    private String subnetId;
    private String subnetUuid;
    private String securityGroupId;
    private String zoneName;
    private int temporaryStorageSize;
    // 单次限制20个镜像，镜像不可重复
    private List<ImageInfo> originImages;
    // 可选参数,默认true
    private boolean autoMatchImageCache = true;
    // 可选参数,默认不回收
    private int retentionDay = Integer.MAX_VALUE;
    // 可选参数
    private List<ImageRegistrySecret> imageRegistrySecrets;
    // 可选参数，另转储pod的eip计费不用考虑
    // 新建eip场景，needEip为true，使用eipName，eipRouteType，eipBandwidthInMbps，eipBandwidthInMbps
    // 复用eip场景，needEip为false，使用eipIp
    // 都不用，needEip为false，eipIp为空
    private boolean needEip = false;
    private String eipName;
    private String eipRouteType = "BGP";
    private int eipBandwidthInMbps;
    private String eipBillingMethod;
    // 可选参数
    private String eipIp;
    // 可选参数，LRU淘汰策略
    private String eliminationStrategy = "";

    // for 多可用区，内部使用
    private String zoneSubnets;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)

    public static class ImageInfo {
        private String originImageAddress;
        private String originImageVersion;
    }
}
