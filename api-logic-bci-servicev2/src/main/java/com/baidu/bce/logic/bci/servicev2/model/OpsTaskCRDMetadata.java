package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpsTaskCRDMetadata {

    private String name;
    private String namespace;
    private String resourceVersion;
    private Map<String, String> annotations = null;
    private Map<String, String> labels = null;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OpsTaskCRDMetadata that = (OpsTaskCRDMetadata) o;
        return Objects.equals(name, that.name)
                && Objects.equals(namespace, that.namespace)
                && Objects.equals(resourceVersion, that.resourceVersion)
                && Objects.equals(annotations, that.annotations)
                && Objects.equals(labels, that.labels);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, namespace, resourceVersion, annotations, labels);
    }
}
