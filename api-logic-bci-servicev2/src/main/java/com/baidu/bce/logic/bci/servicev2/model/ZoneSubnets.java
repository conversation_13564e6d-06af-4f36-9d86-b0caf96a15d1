package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZoneSubnets {
    private ZoneMapDetail zoneMapDetail;
    private SubnetVo subnetVo;
    private VpcVo vpcVo;

    public ZoneSubnets(ZoneMapDetail zoneMapDetail, SubnetVo subnetVo, VpcVo vpcVo) {
        this.subnetVo = subnetVo;
        this.zoneMapDetail = zoneMapDetail;
        this.vpcVo = vpcVo;
    }
}