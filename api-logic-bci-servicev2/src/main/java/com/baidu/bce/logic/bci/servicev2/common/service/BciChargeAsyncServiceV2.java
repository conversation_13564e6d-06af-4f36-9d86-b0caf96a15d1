package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.asyncwork.sdk.asyncaop.BceAsyncWork;
import com.baidu.bce.asyncwork.sdk.model.Level;
import com.baidu.bce.billing.proxy.model.v1.LegacyChargeDataRequest;
import com.baidu.bce.billing.proxy.service.v1.LegacyProxyService;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BciChargeAsyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(BciChargeAsyncServiceV2.class);

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;


    @BceAsyncWork(name = "bciChargeAsync", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public LegacyProxyService.EmptyResponse bciCharge(LegacyChargeDataRequest legacyChargeDataRequest) {
        return logicPodClientFactory.createLegacyProxyService().charge(legacyChargeDataRequest);
    }
}
