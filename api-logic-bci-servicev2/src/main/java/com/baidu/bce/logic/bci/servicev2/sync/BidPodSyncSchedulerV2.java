package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.sync.service.BidPodSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.sync.service.SyncServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableScheduling
@Configuration("BidPodSyncSchedulerV2")
@Profile("default")
public class BidPodSyncSchedulerV2 extends SyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(BidPodSyncSchedulerV2.class);

    private static final int FIX_DELAY_ONE = 3000;

    @Autowired
    private BidPodSyncServiceV2 bidPodSyncService;

//    // 同步bid pod状态
//    @Scheduled(fixedDelay = FIX_DELAY_ONE)
//    public void runBidPodPreemptStatusSyncTask() {
//        bidPodSyncService.bidPodPreemptStatusSync();
//    }
//
//    // 即将被删除的pod，获取pod状态，如果状态非running，从k8s集群deletepod，并更新计费
//    @Scheduled(fixedDelay = FIX_DELAY_ONE)
//    public void runStopPreemptedBidPodTask() {
//        bidPodSyncService.stopPreemptedBidPod();
//    }
//
//    // 已经在k8s server集群删除的竞价实例，24h从db中清理
//    @Scheduled(fixedDelay = FIX_DELAY_ONE)
//    public void runDeletePreemptedBidPodTask() {
//        bidPodSyncService.deletePreemptedBidPod();
//    }
//
//    @Scheduled(cron = "0 */10 * * * ?")
//    // @Scheduled(fixedDelay = FIX_DELAY_ONE)
//    public void duleLostBccPreemptEvent() {
//        bidPodSyncService.duleLostBccPreemptEvent();
//    }
}
