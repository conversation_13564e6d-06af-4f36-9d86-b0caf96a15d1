package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1Pod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.POD_PREFIX;

@Service
public class K8SResourceRecycleServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(K8SResourceRecycleServiceV2.class);

    private Map<String, List<CceCluster>> userCceClusters;

    @Autowired
    private K8sService k8sService;
    @Autowired
    private CceClusterService cceClusterService;


    @Value("${bci.recycle.k8sresource.enable:false}")
    private boolean enableRecycleK8sResource;

    @Value("${bci.configmap.stale.interval:24}")
    private int configmapStaleInterval; // 24个小时超时


    /**
     * 定时清理那些pod不存在但是相关资源还存在的泄漏资源,每天执行一次;
     */
    public void recycleK8SResource() {
        if (!enableRecycleK8sResource) {
            return;
        }
        LOGGER.debug("begin to a new k8s resource recycle loop");
        try {
            genUserCceClusterMap();
            // 当前一个userid对应一个ccecluster,所以暂时不用userCceClusters里面的clusters信息
            List<String> userIds = new ArrayList<>();
            userIds.addAll(userCceClusters.keySet());
            for (int i = 0; i < userIds.size(); i++) {
                LOGGER.debug("begin to recycle k8s resource in namespace {}", userIds.get(i));
                deleteConfigMaps(userIds.get(i));
                deleteSecrets(userIds.get(i));
            }
        } catch (Exception e) {
            LOGGER.error("recycle k8s resource task error {}", e);
        }
    }

    public void deleteConfigMaps(String namespace) {
        List<V1ConfigMap> configMaps = k8sService.getConfigMaps(namespace);
        List<V1Pod> pods = k8sService.listPods(namespace);
        // configmap的名字等于pod的podName+"-" 开头,匹配上就算找到;
        for (int i = 0; i < configMaps.size(); i++) {
            V1ConfigMap configMap = configMaps.get(i);
            LOGGER.debug("process configmap {}", configMap.getMetadata().getName());
            if (!configMap.getMetadata().getName().startsWith(POD_PREFIX)) {
                continue;
            }
            OffsetDateTime cutoffTime = OffsetDateTime.now().minusHours(configmapStaleInterval);
            LOGGER.debug("cutoffTime is {}", cutoffTime);
            if (!configMap.getMetadata().getCreationTimestamp().isBefore(cutoffTime)) {
                LOGGER.debug("configmap {} createtime is {},can't be delete",
                        configMap.getMetadata().getName(), configMap.getMetadata().getCreationTimestamp());
                continue;
            }
            boolean exist = false;
            for (int j = 0; j < pods.size(); j++) {
                if (configMap.getMetadata().getName().startsWith(pods.get(j).getMetadata().getName() + "-")) {
                    LOGGER.debug("configmap {} belongs to pod,can't be deleted", configMap.getMetadata().getName());
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                LOGGER.warn("configmap {} is alone and stale,will delete it", configMap.getMetadata().getName());
                try {
                    k8sService.deleteConfigMap(namespace, configMap.getMetadata().getName());
                } catch (Exception e) {
                    LOGGER.error("recycle configmap {} error {}", configMap.getMetadata().getName(), e);
                }
            }
        }
    }

    public void deleteSecrets(String namespace) {
        // todo
    }

    public void genUserCceClusterMap() {
        userCceClusters = new HashMap<>();
        List<CceUserMap> cceUserMaps = cceClusterService.listCceUserMaps();
        for (int i = 0; i < cceUserMaps.size(); i++) {
            String userId = cceUserMaps.get(i).getUserId();
            List<String> cceids = Arrays.asList(cceUserMaps.get(i).getCceIds().split(","));
            List<CceCluster> cceClusters = new ArrayList<>();
            for (int j = 0; j < cceids.size(); j++) {
                CceCluster cceCluster = cceClusterService.getCceClusterByCceId(cceids.get(j));
                cceClusters.add(cceCluster);
            }
            userCceClusters.put(userId, cceClusters);
        }
    }
}
