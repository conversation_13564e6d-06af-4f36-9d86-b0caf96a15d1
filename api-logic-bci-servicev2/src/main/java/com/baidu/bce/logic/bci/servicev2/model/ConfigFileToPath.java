package com.baidu.bce.logic.bci.servicev2.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigFileToPath {
    // 配置文件所在的相对文件路径。
    private String path;
    // 配置文件的权限，如果没有设置，则采用ConfigFileVolume.DefaultMode的值。
    // 采用四位八进制数表示，例如0644表示权限为rw-r–r--，即用户权限为rw-
    // 用户所在组权限为r--，其他用户权限为r--。
    private String mode;
    // 配置文件内容 (32 KB)。
    private String content;
}
