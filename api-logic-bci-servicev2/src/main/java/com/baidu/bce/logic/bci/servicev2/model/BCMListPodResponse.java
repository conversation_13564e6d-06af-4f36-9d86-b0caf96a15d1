package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BCMListPodResponse {
    private String orderBy = "";
    private String order = "";
    private int pageNo = 1;
    private int pageSize = 0;
    private int totalCount = 0;
    protected List<PodForBCM> result;
}
