package com.baidu.bce.logic.bci.servicev2.webshell;

import java.util.HashSet;
import java.util.Set;

public enum WebShellStreamType {

    OutputStream((byte) 0),
    InputStream((byte) 1),
    ErrorStream((byte) 2),
    ConnectionErrorStream((byte) 3),
    ResizeStream((byte) 4),
    <PERSON>((byte) 20),
    Pong((byte) 21);

    WebShellStreamType(byte type) {
        this.type = type;
    }

    private byte type;

    public static Set<Byte> msgTypes = new HashSet<>();

    static {
        msgTypes.add(OutputStream.getType());
        msgTypes.add(InputStream.getType());
        msgTypes.add(ErrorStream.getType());
        msgTypes.add(ConnectionErrorStream.getType());
        msgTypes.add(ResizeStream.getType());
        msgTypes.add(Ping.getType());
        msgTypes.add(Pong.getType());
    }

    public byte getType() {
        return type;
    }

    public void setType(byte type) {
        this.type = type;
    }

    public static boolean hasMessageType(byte messageType) {
        return msgTypes.contains(messageType);
    }
}
