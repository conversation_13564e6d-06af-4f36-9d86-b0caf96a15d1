package com.baidu.bce.logic.bci.servicev2.model.inner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class VirtualKubeletClusterCheckResultInfo {
    private VirtualKubeletClusterInfo cluster;
    private boolean checkResult;
    private int notFoundInstancesCount;
}