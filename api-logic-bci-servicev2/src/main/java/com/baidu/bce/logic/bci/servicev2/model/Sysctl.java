package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
// 安全上下文
public class Sysctl {
    // 通过安全上下文修改sysctl参数时，安全sysctl参数的名称
    private String name;
    // 通过安全上下文修改sysctl参数时，安全sysctl参数的取值。
    private String value;
}