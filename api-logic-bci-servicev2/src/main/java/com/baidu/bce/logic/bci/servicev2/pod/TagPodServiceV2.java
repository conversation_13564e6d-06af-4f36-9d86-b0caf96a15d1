package com.baidu.bce.logic.bci.servicev2.pod;


import com.baidu.bce.logic.bci.servicev2.common.service.LogicalTagServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.interceptor.ResourceAccountSetting;

import com.baidu.bce.logic.bci.servicev2.model.PodForTag;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.TagListPodRequest;
import com.baidu.bce.logic.bci.servicev2.model.TagListPodResponse;
import com.baidu.bce.logic.bci.servicev2.model.TagPage;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.bci.daov2.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.daov2.common.model.PodListModel;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.core.exception.CommonExceptions;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.LinkedList;
import java.util.List;
import java.util.ArrayList;


@Service("TagPodServiceV2")
public class TagPodServiceV2 extends BaseServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(TagPodServiceV2.class);
    private static final String QUERY_LOGIC_FAILED = "[Query failed] ";
    private static final String LOG_LIST_PAGE_PREFIX = "[list pod page] ";


    @Autowired
    private LogicalTagServiceV2 logicalTagService;

    public TagListPodResponse listPods(TagListPodRequest podListRequest) {
        LOGGER.debug("podListRequest request: {}", JsonUtil.toJSONString(podListRequest));
        TagListPodResponse resultResponse = new TagListPodResponse();
        TagPage tagPage = new TagPage();
        tagPage.setPageNo(podListRequest.getPageNo());
        tagPage.setPageSize(podListRequest.getPageSize());

        String order = podListRequest.getOrder();
        if (null == order || "".equals(order)) {
            order = "desc";
        }

        // orderBy: createTime ==> createdTime
        String orderBy = podListRequest.getOrderBy();
        if (null == orderBy || "".equals(orderBy) || orderBy.equalsIgnoreCase("createTime")) {
            orderBy = "createdTime";
        }

        PodListRequest listRequest = new PodListRequest("", "", 
        podListRequest.getOrder(), podListRequest.getOrderBy(), 
        podListRequest.getPageNo(), podListRequest.getPageSize(), "");
        PodListModel podListModel = PodUtils.convertRequestModel(listRequest, PodListModel.class);
        List<PodFilterQueryModel> queryList = new ArrayList<>();

        List<PodPO> podPOS;
        try {
            List<PodPO> allPodPOS = podDao.listPodsByMultiKey(getAccountId(), podListModel, queryList);
            if (ResourceAccountSetting.isUnifiedCharge()) {
                // 统一计费，匹配charge source
                podPOS = filterPodByChargeSource(allPodPOS, ResourceAccountSetting.getApplication().toLowerCase());
            } else {
                // 根据charge source 过滤列表, 不展示统一计费的资源
                podPOS = filterPodByChargeSource(allPodPOS, LogicalConstant.CHARGE_SOURCE_USER);
            }

            tagPage.setTotalCount(podPOS.size());

            // paging
            List<PodPO> pagePodList = paging(podPOS, podListRequest.getPageNo(), podListRequest.getPageSize());

            List<PodForTag> podsForTag = new LinkedList<>();
            // convert to PodForTag
            for (PodPO podPO: pagePodList) {
                PodForTag podForTag = new PodForTag();
                podForTag.setName(podPO.getName());
                podForTag.setResourceUuid(podPO.getPodUuid());
                podForTag.setResourceId(podPO.getPodId());
                podForTag.setProductType(podPO.getProductType());
                podForTag.setRegion(regionConfiguration.getCurrentRegion());
                podForTag.setCreateTime(podPO.getCreatedTime().toString());
                podForTag.setStatus(podPO.getStatus());

                podsForTag.add(podForTag);
            }
            tagPage.setResult(podsForTag);
            tagPage.setOrder(order);
            // createdTime ===> createTime
            if (orderBy.equalsIgnoreCase("createdTime")){
                orderBy = "createTime";
            }
            tagPage.setOrderBy(orderBy);
            resultResponse.setPage(tagPage);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        LOGGER.debug("getPodTags resultResponse:{}, ", JsonUtil.toJSONString(resultResponse));
        return resultResponse;
    }

    private <T> List<T> paging(List<T> list, Integer pageNo, Integer pageSize) {
        int totalCount = list.size();
        List<T> pageList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = Math.max((pageNo - 1) * pageSize, 0);
            int end = Math.min(start + pageSize, totalCount);
            for (int i = start; i < end; i++) {
                pageList.add(list.get(i));
            }
        } else {
            pageList = list;
        }

        return pageList;
    }

    private List<PodPO> filterPodByChargeSource(List<PodPO> pods, String chargeSource) {
        List<PodPO> filteredPods = new LinkedList<>();
        for (PodPO podPO : pods) {
            if (podPO.getChargeSource().equalsIgnoreCase(chargeSource)) {
                filteredPods.add(podPO);
            }
        }
        return filteredPods;
    }
}