package com.baidu.bce.logic.bci.servicev2.pod;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.externalsdk.logical.network.securitygroup.SecurityGroupClient;
import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SecurityGroupSimpleInstancesVO;
import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.request.SubnetMapRequest;
import com.baidu.bce.externalsdk.logical.network.subnet.model.response.SubnetMapResponse;
import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.BcmClient;
import com.baidu.bce.internalsdk.bci.CCRClient;
import com.baidu.bce.internalsdk.bci.CceImageClient;
import com.baidu.bce.internalsdk.bci.DockerHubClient;
import com.baidu.bce.internalsdk.bci.EniClient;
import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.constant.EipConstant;
import com.baidu.bce.internalsdk.bci.constant.ImageConstant;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeUpdateDbRequest;
import com.baidu.bce.internalsdk.bci.model.BccSpecStockRequest;
import com.baidu.bce.internalsdk.bci.model.BccSpecStockResponse;
import com.baidu.bce.internalsdk.bci.model.BcmEventRequestWrapper;
import com.baidu.bce.internalsdk.bci.model.BcmListEventsRequest;
import com.baidu.bce.internalsdk.bci.model.BcmListEventsResponse;
import com.baidu.bce.internalsdk.bci.model.CCRImage;
import com.baidu.bce.internalsdk.bci.model.CCRImageResponse;
import com.baidu.bce.internalsdk.bci.model.CceOfficialImage;
import com.baidu.bce.internalsdk.bci.model.ControllerBindEipState;
import com.baidu.bce.internalsdk.bci.model.CreateEipRequest;
import com.baidu.bce.internalsdk.bci.model.CreateEipsResponse;
import com.baidu.bce.internalsdk.bci.model.DockerHubImage;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageResponse;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageTag;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageTagResponse;
import com.baidu.bce.internalsdk.bci.model.EipBciState;
import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.OfficialImage;
import com.baidu.bce.internalsdk.bci.model.OfficialImageResponse;
import com.baidu.bce.internalsdk.bci.model.PodEventPO;
import com.baidu.bce.internalsdk.bci.model.QueryEipListResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEniSelfResponse;
import com.baidu.bce.internalsdk.bci.model.Server;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.internalsdk.bci.model.UserImageResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.eipv2.model.EipListResponse;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderCreateRequest;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.internalsdk.zone.model.LogicalZoneListResponse;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.daov2.common.model.PodListModel;
import com.baidu.bce.logic.bci.daov2.common.util.PodPOComparator;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.constant.QuotaKeys;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.common.service.AclServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.BciAsyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalQuotaServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalTagServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.BciFailStrategyConstant;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.InstanceGroup;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.Node;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.interceptor.ResourceAccountSetting;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException.ErrorCode;
import com.baidu.bce.logic.bci.servicev2.model.BatchDescribeContainerGroupDetailRequest;
import com.baidu.bce.logic.bci.servicev2.model.BatchDescribeContainerGroupDetailResponse;
import com.baidu.bce.logic.bci.servicev2.model.BciCreateResponse;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.BciQuota;
import com.baidu.bce.logic.bci.servicev2.model.Bos;
import com.baidu.bce.logic.bci.servicev2.model.CephFSVolume;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupRequestExtra;
import com.baidu.bce.logic.bci.servicev2.model.DeletePod;
import com.baidu.bce.logic.bci.servicev2.model.DescribeContainerGroupDetailRequestExtra;
import com.baidu.bce.logic.bci.servicev2.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.model.FlexVolume;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.InstanceGroupBccSpecInfo;
import com.baidu.bce.logic.bci.servicev2.model.InstanceGroupConfigMap;
import com.baidu.bce.logic.bci.servicev2.model.LeakagePodDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.Nfs;
import com.baidu.bce.logic.bci.servicev2.model.Pfs;
import com.baidu.bce.logic.bci.servicev2.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodCondition;
import com.baidu.bce.logic.bci.servicev2.model.PodDetail;
import com.baidu.bce.logic.bci.servicev2.model.PodExtra;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodNumberRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodNumberResponse;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodVpcResponse;
import com.baidu.bce.logic.bci.servicev2.model.PreviewPodCapacityRequest;
import com.baidu.bce.logic.bci.servicev2.model.PreviewPodCapacityResponse;
import com.baidu.bce.logic.bci.servicev2.model.SyncDsContainersRequest;
import com.baidu.bce.logic.bci.servicev2.model.UpdateConfigFileRequest;
import com.baidu.bce.logic.bci.servicev2.model.UserVersion;
import com.baidu.bce.logic.bci.servicev2.model.ValidatedItem;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.statemachine.StateMachine;
import com.baidu.bce.logic.bci.servicev2.sync.service.PodContainerSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.CacheUtil;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.bci.servicev2.util.PodValidator;
import com.baidu.bce.logic.bci.servicev2.util.Validator;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.utils.BeanCopyUtil;
import com.baidu.bce.plat.cloudtrail.model.Event;
import com.baidu.bce.plat.cloudtrail.model.QueryEventsRequest;
import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.FlavorItem;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1Pod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.net.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.UUID;

import static com.baidu.bce.logic.bci.servicev2.constant.BciOrderConstant.BCI_ORDER_ITEM_KEY_PREFIX;
import static com.baidu.bce.logic.bci.servicev2.constant.BciOrderConstant.BCI_ORDER_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAMESPACE_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAME_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAMESPACE_KEY;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY;
import static com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate;


@Service("PodServiceV2")
public class PodServiceV2 extends BaseServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(PodServiceV2.class);
    private static final String QUERY_LOGIC_FAILED = "[Query failed] ";
    private static final String LOG_DELETE_PREFIX = "[delete bci] ";
    private static final String LOG_CREATE_PREFIX = "[create bci] ";
    private static final String LOG_LIST_PAGE_PREFIX = "[list pod page] ";

    private static final String LOG_DESCRIBE_PREFIX = "[describe pod] ";

    public static final String WEBSHELL_LOG_PREFIX = "[webshell pod] ";

    private static final String APPLICATION_DEFAULT = "default";
    private static final String APPLICATION_INNER = "inner";

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private LogicalTagServiceV2 logicalTagService;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private AclServiceV2 aclService;

    @Autowired
    private LogicalQuotaServiceV2 logicalQuotaService;

    @Autowired
    private LogicalResourceServiceV2 logicalResourceService;

    @Autowired
    private BciAsyncServiceV2 bciAsyncService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private Validator validator;

    @Autowired
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PodLogService podLogService;

    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private com.baidu.bce.logic.bci.daov2.podmigration.dao.PodMigrationDaoV2 podMigrationDaoV2;
    
    @Value("${order.timeout.millis:45000}")
    private int orderTimeoutMillis;

    @Value("${bci.fakeorder.enable:false}")
    private boolean enableFakeOrder;

    @Value("${image.accelerate.checkImageAccelerateReadyOrNotForTheseAccounts}")
    private String checkImageAccelerateReadyOrNotForTheseAccountsStr;

    @Value("${bci.cpt1.region:true}")
    private Boolean regionCpt1;

    @Value("${bci.stock.validate.enable:false}")
    private Boolean stockValidateEnable;

    @Value("${bci.account.id:b9ca12ddf6064c5eab7961d32496d564}")
    private String bciAccountID;

    @Value("${bci.pod.stock.vaildate.enable:false}")
    private boolean bciPodStockValidateEnable;

    @Value("${bci.remote.pod.stock.vaildate.enable:false}")
    private boolean bciRemotePodStockValidateEnable;

    @Value("${bci.image.cache.disabled.users:6c47a952db4444c5a097b41be3f24c94}")
    private String bciImageCacheDisabledUsers;

    @Value("${bci.pod.max.pending.minute:60}")
    private int bciPodMaxPendingMinute;

    @Autowired
    private StateMachine stateMachine;

    @Autowired
    @Qualifier("podRoundUpEventSyncThreadPoolTaskExecutorV2")
    private ThreadPoolTaskExecutor podRoundUpEventThreadPoolExecutor;

    private static final Long TIME_SECOND = 10000000000L;
    private static final Long UTC_TIME = 8 * 60 * 60 * 1000L;
    public static final String CPT1_SUB_PRODUCT_TYPE = "cpt1SubProductType";

    private static final long SECURITY_GROUP_SIMPLE_INSTANCES_CACHE_EXPIRE_TIME = 60 * 1000L * 10;

    private CacheUtil<List<String>, SecurityGroupSimpleInstancesVO> securityGroupSimpleInstancesCache
            = new CacheUtil<>(SECURITY_GROUP_SIMPLE_INSTANCES_CACHE_EXPIRE_TIME);


    private static final long BCI_BCC_STOCK_CACHE_EXPIRE_TIME = 60 * 1000L * 10;

    private CacheUtil<String, BccSpecStockResponse> bciBCCStockCache
            = new CacheUtil<>(BCI_BCC_STOCK_CACHE_EXPIRE_TIME);

    private static final long BCI_ZONE_DETAILS_CACHE_EXPIRE_TIME = 60 * 1000L * 10;
    private CacheUtil<String, ZoneMapDetail> bciZoneDetailsCache
            = new CacheUtil<>(BCI_ZONE_DETAILS_CACHE_EXPIRE_TIME);

    private static final long BCI_INSTANCEGROUP_BCC_SPEC_INFO_CACHE_EXPIRE_TIME = 60 * 1000L * 10;
    private CacheUtil<String, InstanceGroupBccSpecInfo> instanceGroupBccSpecCache
            = new CacheUtil<>(BCI_INSTANCEGROUP_BCC_SPEC_INFO_CACHE_EXPIRE_TIME);

    private static final long BCI_BCC_QUOTA_CACHE_EXPIRE_TIME = 60 * 1000L * 10;
    private CacheUtil<String, Map<String, Map<String, Integer>>> bciBccQuotaCache
            = new CacheUtil<>(BCI_BCC_QUOTA_CACHE_EXPIRE_TIME);

    private class ContainerActions {
        Map<String, ContainerPO> toBeAdded = new HashMap<>();
        Map<String, ContainerPO> toBeUpdated = new HashMap<>();
        List<Long> toBeDeleted = new ArrayList<>();

        public boolean hasChange() {
            return toBeAdded.size() > 0 || toBeUpdated.size() > 0 || toBeDeleted.size() > 0;
        }
    }

    public LogicPageResultResponse<PodPO> listPodsWithPageByMultiKey(PodListRequest podListRequest) {
        PodListModel podListModel = PodUtils.convertRequestModel(podListRequest, PodListModel.class);
        LogicPageResultResponse<PodPO> resultResponse = PodUtils.initEdpPageResultResponse(podListRequest);

        if (null != podListRequest.getFilterMap()) {
            if (!PodValidator.validateSearchId(podListRequest.getFilterMap())) {
                // 不合法的搜索关键字字符，直接返回空结果
                LOGGER.debug(LOG_LIST_PAGE_PREFIX + "search value contains invalid char : {}",
                        podListRequest.getFilterMap().values());
                return new LogicPageResultResponse<>();
            }

            List<String> podUuids = commonUtils.filterByTag(podListRequest);
            if (podUuids != null && podUuids.size() == 0) {
                return PodUtils.getEmptyResponse(resultResponse);
            } else if (CollectionUtils.isNotEmpty(podListModel.getIncludedUuids())) {
                Set<String> oriIdSet = new HashSet<>(podListModel.getIncludedUuids());
                oriIdSet.retainAll(podUuids);
                podListModel.setIncludedUuids(new ArrayList<>(oriIdSet));
            } else {
                podListModel.setIncludedUuids(podUuids);
            }
        }

        Map<String, String> filterMap = podListModel.getFilterMap();
        List<PodFilterQueryModel> queryList = new ArrayList<>();
        if (filterMap != null && filterMap.size() > 0) {
            Set<Map.Entry<String, String>> keywordTypeSet = filterMap.entrySet();
            for (Map.Entry<String, String> keywordTypeEntry : keywordTypeSet) {
                String keyword = keywordTypeEntry.getValue();
                String keywordType = keywordTypeEntry.getKey();
                if (StringUtils.isNotEmpty(keyword)) {
                    queryList.add(new PodFilterQueryModel(keywordType, keyword));
                }
            }
        }
        podListModel.setFilterMap(podListRequest.getFilterMap());

        List<PodPO> podPOS;
        try {
            List<PodPO> allPodPOS = podDao.listPodsByMultiKey(getAccountId(), podListModel, queryList);

            if (ResourceAccountSetting.isUnifiedCharge()) {
                // 统一计费，匹配charge source
                podPOS = filterPodByChargeSource(allPodPOS, ResourceAccountSetting.getApplication().toLowerCase());
            } else {
                // 根据charge source 过滤列表, 不展示统一计费的资源
                podPOS = filterPodByChargeSource(allPodPOS, LogicalConstant.CHARGE_SOURCE_USER);
            }

            // paging
            List<PodPO> pagePodList = paging(podPOS, podListRequest.getPageNo(), podListRequest.getPageSize());

            resultResponse.setTotalCount(podPOS.size());
            // 封装标签信息
            logicalTagService.addTagInfoToPodPO(pagePodList);
            // 根据标签排序
            if ("tag".equalsIgnoreCase(podListRequest.getTagOrderBy())) {
                if ("desc".equalsIgnoreCase(podListRequest.getTagOrder())) {
                    Collections.sort(pagePodList, new PodPOComparator(false));
                } else {
                    Collections.sort(pagePodList, new PodPOComparator(true));
                }
                resultResponse.setOrderBy("tag");
                resultResponse.setOrder(podListRequest.getTagOrder());
            }
            // 封装网络信息(根据podPo.subnetUuid批量获取)
            wrapNetworkForPod(pagePodList);
            // 竞价实例状态特殊处理 Pending→Bidding，Succeed→Recycled
            biddingStatusConvert(pagePodList);

            resultResponse.setResult(pagePodList);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    public LogicMarkerResultResponse<PodPO> listPodsWithMarkerByMultiKey(PodListRequest podListRequest) {
        if (podListRequest.getMaxKeys() < 1 || podListRequest.getMaxKeys() > 1000) {
            LOGGER.error(LOG_LIST_PAGE_PREFIX + " with marker invalid maxkeys : {}", podListRequest.getMaxKeys());
            String message = "The maxKeys is invalid, a valid maxKeys must be between 1 and 1000.";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        PodListModel podListModel = PodUtils.convertRequestModel(podListRequest, PodListModel.class);
        LogicMarkerResultResponse<PodPO> resultResponse = PodUtils.initMarkerResultResponse(podListRequest);

        if (null != podListRequest.getFilterMap()) {
            if (!PodValidator.validateSearchId(podListRequest.getFilterMap())) {
                // 不合法的搜索关键字字符，直接返回空结果
                LOGGER.debug(LOG_LIST_PAGE_PREFIX + "search value contains invalid char : {}",
                        podListRequest.getFilterMap().values());
                return PodUtils.getEmptyResponse(resultResponse);
            }

            List<String> podUuids = commonUtils.filterByTag(podListRequest);
            if (podUuids != null && podUuids.size() == 0) {
                return PodUtils.getEmptyResponse(resultResponse);
            } else if (CollectionUtils.isNotEmpty(podListModel.getIncludedUuids())) {
                Set<String> oriIdSet = new HashSet<>(podListModel.getIncludedUuids());
                oriIdSet.retainAll(podUuids);
                podListModel.setIncludedUuids(new ArrayList<>(oriIdSet));
            } else {
                podListModel.setIncludedUuids(podUuids);
            }
        }

        Map<String, String> filterMap = podListModel.getFilterMap();
        List<PodFilterQueryModel> queryList = new ArrayList<>();
        if (filterMap != null && filterMap.size() > 0) {
            Set<Map.Entry<String, String>> keywordTypeSet = filterMap.entrySet();
            for (Map.Entry<String, String> keywordTypeEntry : keywordTypeSet) {
                String keyword = keywordTypeEntry.getValue();
                String keywordType = keywordTypeEntry.getKey();
                if (StringUtils.isNotEmpty(keyword)) {
                    queryList.add(new PodFilterQueryModel(keywordType, keyword));
                }
            }
        }
        podListModel.setFilterMap(podListRequest.getFilterMap());

        List<PodPO> podPOS;
        try {
            BigInteger id = new BigInteger("0");
            if (!"".equals(podListRequest.getMarker())) {
                id = podDao.queryID(podListRequest.getMarker());
                if (id == null) {
                    LOGGER.debug("Marker invaild {} {}", podListRequest.getMarker(), id);
                    String message = "The marker [" + podListRequest.getMarker()
                            + "] is invalid.";
                    throw new CommonExceptions.RequestInvalidException(message);
                }
            }
            List<PodPO> allPodPOS = podDao.listPodsByMultiKeyAndID(getAccountId(), podListModel, queryList, id);
            if (ResourceAccountSetting.isUnifiedCharge()) {
                // 统一计费，匹配charge source
                podPOS = filterPodByChargeSource(allPodPOS, ResourceAccountSetting.getApplication().toLowerCase());
            } else {
                // 根据charge source 过滤列表, 不展示统一计费的资源
                podPOS = filterPodByChargeSource(allPodPOS, LogicalConstant.CHARGE_SOURCE_USER);
            }

            // paging
            List<PodPO> pagePodList = paging(podPOS, resultResponse, podListRequest.getMarker(),
                    podListRequest.getMaxKeys());
            // 封装标签信息
            logicalTagService.addTagInfoToPodPO(pagePodList);
            // 根据标签排序
            if ("tag".equalsIgnoreCase(podListRequest.getTagOrderBy())) {
                if ("desc".equalsIgnoreCase(podListRequest.getTagOrder())) {
                    Collections.sort(pagePodList, new PodPOComparator(false));
                } else {
                    Collections.sort(pagePodList, new PodPOComparator(true));
                }
                // resultResponse.setOrderBy("tag");
                // resultResponse.setOrder(podListRequest.getTagOrder());
            }
            // 封装网络信息
            wrapNetworkForPod(pagePodList);
            // 竞价实例状态特殊处理 Pending→Bidding，Succeed→Recycled
            biddingStatusConvert(pagePodList);

            resultResponse.setResult(pagePodList);
        } catch (CommonExceptions.RequestInvalidException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    /**
     * @Description 根据多个key查询轻量级Pod列表，并返回结果和标记。同时处理分页、过滤和添加标签等操作。
     * 如果请求参数无效，则抛出CommonExceptions.RequestInvalidException异常；
     * 如果查询失败，则抛出CommonExceptions.InternalServerErrorException异常。
     * @Param podListRequest 包含最大返回数量maxKeys、标记marker、过滤条件filterMap和排序条件sortOrder的Pod列表请求对象
     * @Return LogicMarkerResultResponse<PodPO> 包含结果集、标记和下一次查询所需的marker的LogicMarkerResultResponse对象
     * @Throws CommonExceptions.RequestInvalidException 如果请求参数无效
     * @Throws CommonExceptions.InternalServerErrorException 如果查询失败
     */
    public LogicMarkerResultResponse<PodPO> listPodsLightWithMarkerByMultiKey(PodListRequest podListRequest) {
        if (podListRequest.getMaxKeys() < 1 || podListRequest.getMaxKeys() > 1000) {
            LOGGER.error(LOG_LIST_PAGE_PREFIX + " with marker invalid maxkeys : {}", podListRequest.getMaxKeys());
            String message = "The maxKeys is invalid, a valid maxKeys must be between 1 and 1000.";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        String accountId = getAccountId();
        PodListModel podListModel = PodUtils.convertRequestModel(podListRequest, PodListModel.class);
        podListModel.setQueryLimit(podListModel.getMaxKeys() + 1);
        LogicMarkerResultResponse<PodPO> resultResponse = PodUtils.initMarkerResultResponse(podListRequest);

        // 这里的filterMap仅包含 cceId:"cceId"
        if (null != podListRequest.getFilterMap()) {
            if (!PodValidator.validateSearchId(podListRequest.getFilterMap())) {
                // 不合法的搜索关键字字符，直接返回空结果
                LOGGER.debug(LOG_LIST_PAGE_PREFIX + "search value contains invalid char : {}",
                        podListRequest.getFilterMap().values());
                return PodUtils.getEmptyResponse(resultResponse);
            }
        }

        // 封装查询条件,遍历filterMap,将filterMap中的查询条件封装到queryList中
        // 这里的filterMap仅包含 cceId:"cceId",不包含tag
        // 因此queryList仅包含一个PodFilterQueryModel("cceId", "${cceId}")
        Map<String, String> filterMap = podListModel.getFilterMap();
        List<PodFilterQueryModel> queryList = new ArrayList<>();
        if (filterMap != null && filterMap.size() > 0) {
            Set<Map.Entry<String, String>> keywordTypeSet = filterMap.entrySet();
            for (Map.Entry<String, String> keywordTypeEntry : keywordTypeSet) {
                String keywordType = keywordTypeEntry.getKey();
                String keyword = keywordTypeEntry.getValue();
                if (StringUtils.isNotEmpty(keywordType) && StringUtils.isNotEmpty(keyword)) {
                    queryList.add(new PodFilterQueryModel(keywordType, keyword));
                }
            }
        }
        // 将filterMap设置到podListModel中
        // 这里的filterMap仅包含 cceId:"cceId",不包含tag
        podListModel.setFilterMap(podListRequest.getFilterMap());

        List<PodPO> podPOS;
        try {
            // 根据marker获取本次查询的起始位置
            // 起始位置:marker(pod短id)对应的id(数据库中pod表的主键id)
            BigInteger id = new BigInteger("0");
            if (!"".equals(podListRequest.getMarker())) {
                id = podDao.queryID(podListRequest.getMarker());
                if (id == null) {
                    LOGGER.debug("Marker invaild {} {}", podListRequest.getMarker(), id);
                    String message = "The marker [" + podListRequest.getMarker()
                            + "] is invalid.";
                    throw new CommonExceptions.RequestInvalidException(message);
                }
            }
            // queryList仅包含一个PodFilterQueryModel("cceId", "${cceId}")
            List<PodPO> allPodPOS = podDao.listPodsLightByMultiKeyAndID(accountId, podListModel, queryList, id);
            // 过滤统一计费的资源,不太理解这段代码逻辑,暂时继续保留不做修改
            if (ResourceAccountSetting.isUnifiedCharge()) {
                // 统一计费，匹配charge source
                podPOS = filterPodByChargeSource(allPodPOS, ResourceAccountSetting.getApplication().toLowerCase());
            } else {
                // 根据charge source 过滤列表, 不展示统一计费的资源
                podPOS = filterPodByChargeSource(allPodPOS, LogicalConstant.CHARGE_SOURCE_USER);
            }

            // paging
            List<PodPO> podResultList = paging(podPOS, resultResponse, podListRequest);
            // 封装标签信息
            logicalTagService.addTagInfoToPodPOLight(podResultList);
            resultResponse.setResult(podResultList);
        } catch (CommonExceptions.RequestInvalidException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    private List<PodPO> paging(List<PodPO> podPOList, Integer pageNo, Integer pageSize) {
        int totalCount = podPOList.size();
        List<PodPO> pagePodList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = Math.max((pageNo - 1) * pageSize, 0);
            int end = Math.min(start + pageSize, totalCount);
            for (int i = start; i < end; i++) {
                pagePodList.add(podPOList.get(i));
            }
        } else {
            pagePodList = podPOList;
        }

        return pagePodList;
    }

    private List<PodPO> paging(List<PodPO> podPOList, LogicMarkerResultResponse<PodPO> resultResponse,
                               String marker, Integer maxKeys) {
        List<PodPO> pagePodList = new LinkedList<>();
        int totalCount = podPOList.size();
        int start = 0;
        int end = Math.min(start + maxKeys, totalCount);
        for (int j = start; j < end; j++) {
            pagePodList.add(podPOList.get(j));
        }
        boolean isTruncated = totalCount - start > maxKeys;
        resultResponse.setIsTruncated(isTruncated);
        if (isTruncated) {
            resultResponse.setNextMarker(String.valueOf(podPOList.get(end).getPodId()));
        }
        return pagePodList;
    }

    private List<PodPO> paging(List<PodPO> podPOList,
                               LogicMarkerResultResponse<PodPO> resultResponse,
                               PodListRequest podListRequest) {
        List<PodPO> resultPodList = new ArrayList<>();
        if (!podPOList.isEmpty() && podPOList.size() > podListRequest.getMaxKeys()) {
            resultPodList.addAll(podPOList.subList(0, podListRequest.getMaxKeys()));
            resultResponse.setIsTruncated(true);
            resultResponse.setNextMarker(podPOList.get(podPOList.size() - 1).getPodId());
        } else {
            resultPodList = podPOList;
            resultResponse.setIsTruncated(false);
        }
        return resultPodList;
    }

    private void convertApplicationVo(List<PodPO> podPOS) {
        for (PodPO podPO : podPOS) {
            if (!APPLICATION_DEFAULT.equals(podPO.getApplication())) {
                podPO.setApplication(APPLICATION_INNER);
            }
            podPO.setPushLog(podPO.getEnableLog() == 1);
        }
    }

    public LogicPageResultResponse<PodPO> listPodsByUpdatedTime(String keywordType, String keyword,
                                                                long since) {
        if (since < TIME_SECOND) {
            since = since * 1000;
        }
        Timestamp updatedTime = new Timestamp(Long.parseLong(String.valueOf(since + UTC_TIME)));

        LOGGER.debug("list bci by since {}, updated time {}", since, updatedTime);
        LogicPageResultResponse<PodPO> resultResponse = new LogicPageResultResponse<>();

        List<PodFilterQueryModel> queryList = new ArrayList<>();

        List<PodPO> podPOS;
        try {
            List<PodPO> allPodPOS = podDao.listPodsByUpdatedTime(getAccountId(), updatedTime, keywordType, keyword);

            if (ResourceAccountSetting.isUnifiedCharge()) {
                // 统一计费，匹配charge source
                podPOS = filterPodByChargeSource(allPodPOS, ResourceAccountSetting.getApplication().toLowerCase());
            } else {
                // 根据charge source 过滤列表, 不展示统一计费的资源
                podPOS = filterPodByChargeSource(allPodPOS, LogicalConstant.CHARGE_SOURCE_USER);
            }

            resultResponse.setTotalCount(podPOS.size());
            // 封装标签信息
            logicalTagService.addTagInfoToPodPO(podPOS);

            // 封装网络信息
            wrapNetworkForPod(podPOS);
            // 竞价实例状态特殊处理 Pending→Bidding，Succeed→Recycled
            biddingStatusConvert(podPOS);

            resultResponse.setResult(podPOS);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    public List<PodPO> getPods(List<String> podIds) {
        if (CollectionUtils.isEmpty(podIds)) {
            throw new CommonExceptions.RequestInvalidException();
        }

        PodListModel podListModel = new PodListModel();
        podListModel.setIncludedUuids(podIds);

        List<PodPO> podPOS = null;
        try {
            podPOS = podDao.listPodsByMultiKey(getAccountId(), podListModel, new ArrayList<PodFilterQueryModel>());
        } catch (Exception e) {
            LOGGER.error("[bce logical bci] Query logical failed, operation "
                    + "is {}, exception is {}", "[query server]", e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return podPOS;
    }

    public void syncDsContainers(String podID, SyncDsContainersRequest request) {
        // STEP.1 bci实例状态检查，只有实例处于特定状态下才能同步更新ds容器
        // bci实例的状态必须是RUNNING
        PodPO podPO = getPodPOByPodId(podID);
        String podUuid = podPO.getPodUuid();
        if (!(BciStatus.RUNNING.getStatus().equalsIgnoreCase(podPO.getStatus()))) {
            throw new CommonExceptions.RequestInvalidException("bci instance must be running when syncing ds containers");
        }
        // BCI实例必须是V3版本
        boolean isBciV3 = false;
        boolean hasDsContainer = false;
//        PodExtra podExtra = JsonUtil.fromJSON(podPO.getExtra(), PodExtra.class);
//        if (podExtra != null) {
//            isBciV3 = PodUtils.isOrderV3(podExtra.getMetadataLabels());
//        }
//        if (!isBciV3) {
//            throw new CommonExceptions.RequestInvalidException("the version of bci instance must be v3");
//        }

        // BCI实例的podUuid必须是64位随机字符串
        // 目前podPO和containerPO的podUuid是会动态变化的，为了避免在变化期间查询到的信息有遗漏，在bci实例的poduuid没有变为64位随机字符串之前，不支持同步ds容器
        if (StringUtils.equals(podID, podUuid) || StringUtils.startsWith(podUuid, PodConstants.POD_PREFIX)) {
            throw new CommonExceptions.ResourceInTaskException("bci instance is creating, please retry later");
        }
        if (request.getExpectDsContainers() == null || request.getExpectDsVolumes() == null) {
            throw new CommonExceptions.RequestInvalidException("expectDsContainers and expectDsVolumes must be specified");
        }

        // STEP.2 获取bci实例当前状态
        // 容器相关现状
        Map<String, ContainerPO> actualNonDsContainerPOMap = new HashMap<>();
        Map<String, ContainerPO> actualDsContainerPOMap = new HashMap<>();
        Set<String> actualNonDsMountNames = new HashSet<String>();
        for (ContainerPO container : containerDao.listByPodId(podUuid)) {
            if (ContainerType.DS_WORKLOAD.getType().equals(container.getContainerType())) {
                actualDsContainerPOMap.put(container.getName(), container);
            } else {
                actualNonDsContainerPOMap.put(container.getName(), container);
                for (VolumeMounts mount : JsonUtil.toList(container.getVolumeMounts(), VolumeMounts.class)) {
                    actualNonDsMountNames.add(mount.getName());
                }
            }
        }
        LOGGER.debug("syncDsContainers actualDsContainerPOMap: {}", JsonUtil.toJSON(actualDsContainerPOMap));
        LOGGER.debug("syncDsContainers actualNonDsContainerPOMap: {}", JsonUtil.toJSON(actualNonDsContainerPOMap));
        LOGGER.debug("syncDsContainers actualNonDsMountNames: {}", JsonUtil.toJSON(actualNonDsMountNames));
        // volume相关现状
        Volume actualAllVolumes = PodUtils.getVolumeFromPodPO(podPO);
        LOGGER.debug("syncDsContainers actualAllVolumes: {}", JsonUtil.toJSON(actualAllVolumes));
        Map<String, String> actualNonVolumeMap = PodUtils.getVolume2TypeMap(actualAllVolumes, false);
        LOGGER.debug("syncDsContainers actualNonVolumeMap: {}", JsonUtil.toJSON(actualNonVolumeMap));

        // STEP.3 获取请求中的容器和volume信息
        Map<String, ContainerPurchase> expectDsContainerMap = new HashMap<>();
        Set<String> expectDsMountNames = new HashSet<String>();
        for (ContainerPurchase container : request.getExpectDsContainers()) {
            expectDsContainerMap.put(container.getName(), container);
            for (VolumeMounts mount : container.getVolumeMounts()) {
                expectDsMountNames.add(mount.getName());
            }
        }
        if (expectDsContainerMap.size() > 0) {
            hasDsContainer = true;
        }
        LOGGER.debug("syncDsContainers expectDsContainerMap: {}", JsonUtil.toJSON(expectDsContainerMap));
        LOGGER.debug("syncDsContainers expectDsMountNames: {}", JsonUtil.toJSON(expectDsMountNames));

        Map<String, String> expectDsVolumeMap = PodUtils.getVolume2TypeMap(request.getExpectDsVolumes(), true);
        LOGGER.debug("syncDsContainers expectDsVolumeMap: {}", JsonUtil.toJSON(expectDsVolumeMap));

        // STEP.4 校验请求中ds容器和ds卷
        validator.validateDsContainers(request.getExpectDsContainers(),
                new HashSet<String>(actualNonDsContainerPOMap.keySet()),
                expectDsVolumeMap, actualNonVolumeMap, isBciV3);
        validator.validateDsVolumes(request.getExpectDsVolumes(), actualNonVolumeMap.keySet(),
                expectDsMountNames, actualNonDsMountNames, isBciV3, true);

        // STEP.5 生成需要增删改的容器和volume
        ContainerActions containerActions = computeContainerActions(podUuid, expectDsContainerMap,
                actualDsContainerPOMap);
        LOGGER.debug(podID + " containerActions.toBeAdded is: " + JsonUtil.toJSON(containerActions.toBeAdded));
        LOGGER.debug(podID + " containerActions.toBeUpdated is: " + JsonUtil.toJSON(containerActions.toBeUpdated));
        LOGGER.debug(podID + " containerActions.toBeDeleted is: " + JsonUtil.toJSON(containerActions.toBeDeleted));
        // 没有变化，直接返回
        if (!containerActions.hasChange()) {
            LOGGER.debug("no change for ds containers");
            return;
        }
        genExpectVolumesAndRefrehIntoPodPO(podPO, actualAllVolumes, request.getExpectDsVolumes());
        podPO.setDsContainersSyncedToK8S(false);
        podPO.setDsContainersVersion(podPO.getDsContainersVersion() + 1);
        podPO.setDsContainersCount(expectDsContainerMap.size());
        long oldBciResourceVersion = podPO.getBciResourceVersion();
        podPO.setBciResourceVersion(oldBciResourceVersion + 1);

        // STEP.6 将需要变更的ds容器、pod信息更新到数据库
        syncDsContainersToDB(containerActions, podPO, oldBciResourceVersion);
    }

    // 事务隔离级别保证可重复读
    @Transactional(rollbackFor = CommonExceptions.ResourceInTaskException.class, isolation = Isolation.REPEATABLE_READ)
    public void syncDsContainersToDB(ContainerActions containerActions, PodPO podPO, long oldBciResourceVersion) {
        PodPO currentPodPO = podDao.getPodById(podPO.getUserId(), podPO.getPodId());
        if (currentPodPO == null || currentPodPO.getBciResourceVersion() != oldBciResourceVersion) {
            throw new CommonExceptions.ResourceInTaskException("bci instance is in task, please retry later.");
        }
        // TODO：新增容器和卷信息更新到订单的order_extra，这样实例自动迁移时，也可以看到最新的ds容器
        if (containerActions.toBeUpdated.size() > 0) {
            containerDao.batchUpdate(new ArrayList<>(containerActions.toBeUpdated.values()));
        }
        if (containerActions.toBeAdded.size() > 0) {
            containerDao.batchInsert(new ArrayList<>(containerActions.toBeAdded.values()));
        }
        if (containerActions.toBeDeleted.size() > 0) {
            containerDao.batchDelete(podPO.getUserId(), containerActions.toBeDeleted);
        }
        podDao.updatePod(podPO);
    }

    private ContainerActions computeContainerActions(String podUuid,
                                                     Map<String, ContainerPurchase> expectDsContainerMap,
                                                     Map<String, ContainerPO> actualDsContainerPOMap) {
        ContainerActions containerActions = new ContainerActions();
        for (String expect : expectDsContainerMap.keySet()) {
            // 生成需要更新的ds容器
            if (actualDsContainerPOMap.containsKey(expect)) {
                ContainerPurchase expectContainer = expectDsContainerMap.get(expect);
                ContainerPO actualContainer = actualDsContainerPOMap.get(expect);
                // 没有变化，不做升级更新
                if (StringUtils.equals(expectContainer.getImageAddress(), actualContainer.getImageAddress())
                        && StringUtils.equals(expectContainer.getImageName(), actualContainer.getImageName())
                        && StringUtils.equals(expectContainer.getImageVersion(), actualContainer.getImageVersion())) {
                    continue;
                }
                actualContainer.setImageAddress(expectContainer.getImageAddress());
                actualContainer.setImageName(expectContainer.getImageName());
                actualContainer.setImageVersion(expectContainer.getImageVersion());
                actualContainer.setImageID("");
                actualContainer.setDsContainerVersion(actualContainer.getDsContainerVersion() + 1);
                containerActions.toBeUpdated.put(expect, actualContainer);
            } else {
                // 生成需要新增的ds容器
                ContainerPO containerPO = initContainerPO(podUuid, expectDsContainerMap.get(expect));
                containerPO.setDsContainerVersion(new Random().nextInt(10000));
                containerActions.toBeAdded.put(expect, containerPO);
            }
        }
        for (String actual : actualDsContainerPOMap.keySet()) {
            // 生成需要删除的ds容器
            if (!expectDsContainerMap.containsKey(actual)) {
                containerActions.toBeDeleted.add(actualDsContainerPOMap.get(actual).getId());
            }
        }
        return containerActions;
    }

    private void genExpectVolumesAndRefrehIntoPodPO(PodPO podPO, Volume actualVolume, Volume expectDsVolume) {
        List<Nfs> finalNfs = PodUtils.refreshVolumes(actualVolume.getNfs(), expectDsVolume.getNfs());
        List<ConfigFile> finalConfigFile = PodUtils.refreshVolumes(actualVolume.getConfigFile(),
                expectDsVolume.getConfigFile());
        List<EmptyDir> finalEmptyDir = PodUtils.refreshVolumes(actualVolume.getEmptyDir(),
                expectDsVolume.getEmptyDir());
        List<FlexVolume> finalFlexVolume = PodUtils.refreshVolumes(actualVolume.getFlexVolume(),
                expectDsVolume.getFlexVolume());
        List<Pfs> finalPfs = PodUtils.refreshVolumes(actualVolume.getPfs(), expectDsVolume.getPfs());
        List<Bos> finalBos = PodUtils.refreshVolumes(actualVolume.getBos(), expectDsVolume.getBos());
        List<HostPathVolume> finalHostPath = PodUtils.refreshVolumes(actualVolume.getHostPath(), expectDsVolume.getHostPath());
        List<CephFSVolume> finalCephFS = PodUtils.refreshVolumes(actualVolume.getCephfs(), expectDsVolume.getCephfs());
        podPO.setNfs(JsonUtil.toJSON(finalNfs));
        podPO.setConfigFile(JsonUtil.toJSON(finalConfigFile));
        podPO.setEmptyDir(JsonUtil.toJSON(finalEmptyDir));
        podPO.setFlexVolume(JsonUtil.toJSON(finalFlexVolume));
        podPO.setPfs(JsonUtil.toJSON(finalPfs));
        podPO.setBos(JsonUtil.toJSON(finalBos));
        podPO.setHostPath(JsonUtil.toJSON(finalHostPath));
        podPO.setCephFS(JsonUtil.toJSON(finalCephFS));
    }

    public void deletePod(PodBatchDeleteRequest request) throws K8sServiceException {
        if (request == null || CollectionUtils.isEmpty(request.getDeletePods())) {
            LOGGER.warn(LOG_DELETE_PREFIX + "delete pods is empty");
            throw new PodExceptions.PodIdIsEmptyException();
        }

        List<DeletePod> deletePods = request.getDeletePods();
        for (DeletePod deletePod : deletePods) {
            deletePodAndEip(deletePod.getPodId(), request.getRelatedReleaseFlag());
        }
    }

    private void deletePodAndEip(String podId, boolean relatedReleaseFlag) throws K8sServiceException {
        PodPO podPO = getPodPOByPodId(podId);
        if (podPO == null) {
            LOGGER.warn(LOG_DELETE_PREFIX + "delete pod failed, pod {} not found", podId);
            throw new PodExceptions.PodNotExistException(podId);
        }
        if (ResourceAccountSetting.isUnifiedCharge()) {
            // 统一计费，匹配charge source
            if (!podPO.getChargeSource().equalsIgnoreCase(ResourceAccountSetting.getApplication())) {
                LOGGER.warn(LOG_DELETE_PREFIX + "delete pod failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        } else {
            // 根据charge source 过滤列表, 不展示统一计费的资源
            if (!podPO.getChargeSource().equalsIgnoreCase(LogicalConstant.CHARGE_SOURCE_USER)) {
                LOGGER.warn(LOG_DELETE_PREFIX + "delete pod failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        }

        String accountID = getAccountId();
        // delete eip
        try {
            boolean isUserSpecifiedEip = false;
            if (podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED ||
                    podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED_UNBINDED) {
                isUserSpecifiedEip = true;
            }

            // eip in db
            if (StringUtils.isNotEmpty(podPO.getPublicIp())) {
                deleteEipForDeletePod(accountID, podPO.getPodId(), podPO.getInternalIp(), podPO.getPublicIp(),
                        relatedReleaseFlag, isUserSpecifiedEip);
            }

            V1Pod podInfo = k8sService.getPod(podPO.getUserId(), podPO.getPodId());
            if (podInfo != null && podInfo.getMetadata() != null && podInfo.getMetadata().getAnnotations() != null) {
                // eip in pod annotation
                String eipIp = "";
                ControllerBindEipState eipBindState = null;
                String eipBindStateStr = podInfo.getMetadata().getAnnotations().get("cross-vpc-eni.cce.io/eipState");
                if (StringUtils.isNotEmpty(eipBindStateStr)) {
                    eipBindState = new ObjectMapper().readValue(eipBindStateStr, ControllerBindEipState.class);
                    eipIp = eipBindState.getEip();
                    if (StringUtils.isNotEmpty(eipIp) && !eipIp.equals(podPO.getPublicIp())) {
                        deleteEipForDeletePod(accountID, podPO.getPodId(), podPO.getInternalIp(), eipIp,
                                relatedReleaseFlag, isUserSpecifiedEip);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error(LOG_DELETE_PREFIX +
                            "unbind or release eip failed, pod {} user {}, err {}",
                    podId, accountID, e.toString());
        }

        if (ResourceAccountSetting.isUnifiedCharge()) {
            accountID = ResourceAccountSetting.getResourceAccountId();
        }
        // billing 的接口，得用创建订单的accountID
        logicalResourceService.deleteResourceByName(accountID, podPO.getPodUuid(), PodConstants.SERVICE_TYPE);

        // 注意:删除pod过程中,不需要同步删除底层K8S中的pod,仅标记pod需要被资源回收
        // 由资源回收线程异步的删除底层K8S中的pod
        // 这么做的原因是:
        // 由于资源回收线程中删除Pod操作有超时时间的限制,如果在删除Pod过程中同步删除底层K8S Pod
        // 会导致资源回收线程中需要一致等待超时时间后才能执行释放Eip等操作.
        try {
            // 标记Pod被用户主动删除
            markUserDeletePod(podPO);
        } catch (Exception e) {
            LOGGER.error(LOG_DELETE_PREFIX + "delete pod failed, pod {} user {}, err {}",
                    podId, accountID, e.toString());
            throw new K8sServiceException(ErrorCode.DELETE_POD_FAILED, e.getMessage());
        }

        try {
            LOGGER.debug("[call billing] deletePodRecord in deletePod->deletePodAndEip for pod {}", podPO.getPodUuid());
            deletePodRecordPodChargeStatus(podPO);
            // 同步删除接口中不做解绑tag操作
            // 解绑tag异步化,在资源回收线程中执行
            containerDao.deleteContainers(getAccountId(), podPO.getPodId());
            containerDao.deleteContainers(getAccountId(), podPO.getPodUuid());
        } catch (Exception e) {
            LOGGER.error(LOG_DELETE_PREFIX + "delete from logical failed, pod {} user {}, err {}",
                    podId, accountID, e.toString());
            throwPermissionDeniedExceptionIfAppropriate(e);
            throw new PodExceptions.InternalServerErrorException();
        }
    }

    public boolean deleteBLSTasks(PodPO podPO) {
        return deleteBLSTasks(podPO, podPO.getUserId());
    }

    public boolean deleteBLSTasks(PodPO podPO, String accountId) {
        String createdBLSTasksID = podPO.getCreatedBLSTasksID();
        if (!StringUtils.isEmpty(createdBLSTasksID)) {
            return podLogService.deleteBLSTasks(createdBLSTasksID, accountId);
        }
        return true;
    }

    private boolean markUserDeletePod(PodPO podPO) {
        // 标记用户主动删除Pod，用来区分Pod的删除事件的来源
        // 修改数据库中Pod的状态为已删除，并标记Pod需要被资源回收
        //  a. deleted = 1 and status = deleted
        //  b. resource_recycle_timestamp = System.currentTimeMillis();
        //  c. resource_recycle_reason = USER_DELETE_POD
        if (!stateMachine.trigger(podPO, StateMachineEvent.DELETE_INSTANCE, null)) {
            LOGGER.error("markUserDeletePod: pod state machine trigger failed, podId:{} event:{}",
                    podPO.getPodId(), StateMachineEvent.DELETE_INSTANCE.toString());
            return false;
        }
        LOGGER.info("markUserDeletePod: pod state machine trigger success, podId:{} event:{}",
                podPO.getPodId(), StateMachineEvent.DELETE_INSTANCE.toString());
        return true;
    }

    private void deleteEipForDeletePod(String accountID, String podId, String podIp, String eip,
                                       boolean relatedReleaseFlag, boolean isUserSpecifiedEip) {
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(accountID);
        if (relatedReleaseFlag && !isUserSpecifiedEip) {
            LOGGER.debug(LOG_DELETE_PREFIX + "release eip, pod {} {} user {}, eip {}", podId, podIp, accountID, eip);
            if (logicEipClient.forceReleaseEipV2(eip)) {
                podDao.deleteEip(accountID, podId, eip);
            }
        } else {
            LOGGER.debug(LOG_DELETE_PREFIX + "unbind eip, pod {} {} user {}, eip {}", podId, podIp, accountID, eip);
            QueryEipListResponse eipListResponse = logicEipClient.queryEipV2(eip);
            if (eipListResponse == null) {
                LOGGER.error(LOG_DELETE_PREFIX + "unbind eip with query eip failed, pod {} {} user {}, eip {}",
                        podId, podIp, accountID, eip);
                throw new PodExceptions.InternalServerErrorException();
            }
            if (!eipListResponse.getEipList().isEmpty() &&
                    StringUtils.isNotEmpty(eipListResponse.getEipList().get(0).getInstanceIp()) &&
                    eipListResponse.getEipList().get(0).getInstanceIp().equals(podIp)) {
                LOGGER.debug(LOG_DELETE_PREFIX + "unbind eip {}", eip);
                logicEipClient.unbindEip(eip);
            } else {
                LOGGER.warn(LOG_DELETE_PREFIX + "eip status is abnormal, try unbind eip {} latter", eip);
                // 启动一个线程异步调用tryUnbindAgainLater
                new Thread(() -> {
                    tryUnbindAgainLater(logicEipClient, podIp, eip);
                }, "tryUnbindAgainLater").start();
            }
            podDao.unbindEip(accountID, podId, eip, isUserSpecifiedEip);
        }
    }

    private void tryUnbindAgainLater(LogicEipClient logicEipClient, String podIp, String eip) {
        LOGGER.warn(LOG_DELETE_PREFIX + "tryUnbindAgainLater unbind eip {} after 3s", eip);
        try {
            // 不能sleep太久，避免用户新创建了pod，把新的(pod->eip)对给不符合预期解绑掉
            Thread.sleep(3 * 1000);
        } catch (InterruptedException ignored) {
            LOGGER.error("tryUnbindAgainLater sleep error");
        }
        QueryEipListResponse eipListResponse = logicEipClient.queryEipV2(eip);
        if (!eipListResponse.getEipList().isEmpty() &&
                StringUtils.isNotEmpty(eipListResponse.getEipList().get(0).getInstanceIp()) &&
                eipListResponse.getEipList().get(0).getInstanceIp().equals(podIp)) {
            logicEipClient.unbindEip(eip);
            LOGGER.warn(LOG_DELETE_PREFIX + "tryUnbindAgainLater unbind eip {}", eip);
        } else {
            LOGGER.warn(LOG_DELETE_PREFIX + "retry unbind eip {} failed, eipListResponse is {}, skip it", eip, eipListResponse);
        }
    }

    private void forceReleaseBindEip(Map<String, EipInstance> eipMap, String podId) {
        String accountId = getAccountId();
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(accountId);
        EipInstance eipInstance = eipMap.get(podId);
        if (eipInstance != null && !"shared".equalsIgnoreCase(eipInstance.getEipType())) {
            try {
                LOGGER.debug("PodServiceV2 forceReleaseBindEip {}", eipInstance.getEip());
                logicEipClient.forceReleaseEipV2(eipInstance.getEip());
            } catch (BceInternalResponseException e) {
                if ("PrepayEip".equalsIgnoreCase(e.getCode())) {
                    LOGGER.warn("Prepay EIP delete operation is not available and eipId:{}", eipInstance.getEip());
                    unBindEipFromPod(eipInstance.getEip());
                } else {
                    throw e;
                }
            }
        }
    }

    private void changeTidalPodContainerStatusReasonWhenDeletedByBci(PodPO podPO, PodDetail podDetail) {
        // 只处理潮汐pod状态展示
        if (!podPO.isTidal()) {
            return;
        }
        Timestamp podDefaultCommitDeletedTime = Timestamp.valueOf("1971-01-01 08:00:01");
        Timestamp podCommitDeletedmestamp = podPO.getCommitDeletedTime();
        // pod 被用户删除，不修改container status
        if (!podDefaultCommitDeletedTime.equals(podCommitDeletedmestamp)) {
            return;
        }

        // pod 被bci controller 主动删除pod后，ResourceRecycleReason 被设置为POD_DELETED_UNEXPECTEDLY
        if (!ResourceRecycleReason.CONTROLLER_AUTO_DELETE.toString().equals(podPO.getResourceRecycleReason())) {
            return;
        }

        for (PodDetail.ContainerDetail containerDetail : podDetail.getContainers()) {
            // init container 不做处理
            if (containerDetail.getContainerType().equals(ContainerType.INIT.getType())) {
                continue;
            }
            PodDetail.ContainerStatus status = containerDetail.getStatus();
            if (status == null) {
                continue;
            }
            ContainerCurrentState currentState = status.getCurrentState();
            if (currentState == null) {
                continue;
            }

            // 重新构建reason + message
            String newDetailStatus = "";

            String detailStatus = currentState.getDetailStatus();

            if (StringUtils.isEmpty(detailStatus)) {
                newDetailStatus = PodConstants.BCI_TIDAL_POD_RECYCLED;
                currentState.setDetailStatus(newDetailStatus);
                continue;
            }

            String[] split = detailStatus.split(PodContainerSyncServiceV2.reasonAndMessageDelimiter);
            if (split.length == 1) {
                newDetailStatus = PodConstants.BCI_TIDAL_POD_RECYCLED;
            } else if (split.length == 2) {
                newDetailStatus = PodConstants.BCI_TIDAL_POD_RECYCLED +
                        PodContainerSyncServiceV2.reasonAndMessageDelimiter + split[1];
            }
            currentState.setDetailStatus(newDetailStatus);
        }

    }

    /**
     * {@inheritDoc}
     * 根据请求额外信息的详细等级，返回不同的Pod详情。
     * 如果详细等级为"full"，则调用podDetail方法获取全部详情；
     * 如果详细等级为"light"，则调用podDetailLight方法获取简单详情；
     * 如果详细等级为"default"或未指定，则调用podDetail方法获取默认详情；
     * 否则，调用podDetail方法获取默认详情。
     *
     * @param podId        Pod的ID
     * @param requestExtra 请求额外信息，包含详细等级
     * @return Pod的详情对象，包括Pod的基本信息和容器组列表
     */
    public PodDetail podDetail(String podId, DescribeContainerGroupDetailRequestExtra requestExtra) {
        switch (requestExtra.getDetailLevel().getLevel()) {
            case "full":
                return podDetail(podId);
            case "light":
                return podDetailLight(podId);
            case "default":
                return podDetail(podId);
            case "deleted":
                return podDetailWithDeleted(podId);
            default:
                return podDetail(podId);
        }
    }

    public PodDetail podDetail(String podId) {
        PodPO podPO = getPodPOByPodId(podId);
        if (podPO == null) {
            LOGGER.warn(LOG_DESCRIBE_PREFIX + " pod detail failed, pod {} not found", podId);
            throw new PodExceptions.PodNotExistException(podId);
        }

        if (ResourceAccountSetting.isUnifiedCharge()) {
            // 统一计费，匹配charge source
            if (!podPO.getChargeSource().equalsIgnoreCase(ResourceAccountSetting.getApplication())) {
                LOGGER.warn(LOG_DESCRIBE_PREFIX + "pod detail failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        } else {
            // 根据charge source 过滤列表, 不展示统一计费的资源
            if (!podPO.getChargeSource().equalsIgnoreCase(LogicalConstant.CHARGE_SOURCE_USER)) {
                LOGGER.warn(LOG_DESCRIBE_PREFIX + "pod detail failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        }

        List<ContainerPO> containers = containerDao.listByPodId(podPO.getPodUuid());
        if (CollectionUtils.isEmpty(containers)) {
            containers = containerDao.listByPodId(podPO.getPodId());
        }
        if (CollectionUtils.isEmpty(containers)) {
            throw new CommonExceptions.ResourceNotExistException();
        }
        logicalTagService.addTagInfoToPodPO(Collections.singletonList(podPO));
        wrapEipForPod(Collections.singletonList(podPO));
        biddingStatusConvert(Collections.singletonList(podPO));
        PodDetail podDetail = PodUtils.convertPodDetailShow(podPO, containers);

        try {
            // 潮汐pod，被bci resource controller删掉以后，修改container status reason
            changeTidalPodContainerStatusReasonWhenDeletedByBci(podPO, podDetail);
        } catch (Exception e) {
            LOGGER.error("changeTidalPodContainerStatusReasonWhenDeletedByBci exception:{}", e);
        }

        try {
            if (podPO.getSecurityGroupUuid() != null && podPO.getSecurityGroupUuid().length() > 0) {
                List<String> securityGroups = Arrays.asList(podPO.getSecurityGroupUuid().trim().split(","));
                if (podValidator.isNormalSecurityGroupId(securityGroups)) {
                    List<SimpleSecurityGroupVO> securityGroupVO = getSecurityGroup(podPO.getSecurityGroupUuid());
                    if (securityGroupVO.size() == 1) {
                        podDetail.setSecurityGroup(securityGroupVO.get(0));
                    } else {
                        podDetail.setSecurityGroups(securityGroupVO);
                    }
                }
            }
            podDetail.setRegion(regionConfiguration.getCurrentRegion());
            List<String> subnetIds = new ArrayList<>();
            if (StringUtils.isNotEmpty(podPO.getSubnetUuid())) {
                subnetIds.add(podPO.getSubnetUuid());
            }
            SubnetMapResponse subnetMapResponse = new SubnetMapResponse();
            if (!subnetIds.isEmpty()) {
                int maxRetryCount = 3;
                int currentRetryCount = 1;
                SubnetMapRequest subnetMapRequest = new SubnetMapRequest();
                subnetMapRequest.setSubnetIds(subnetIds);
                subnetMapRequest.setAttachVpc(true);
                while (currentRetryCount <= maxRetryCount) {
                    try {
                        subnetMapResponse =
                                logicPodClientFactory.createExternalSubnetClient(getAccountId()).getSubnetMap(subnetMapRequest);
                        podDetail.setSubnet(subnetMapResponse.getSubnetMap().get(podPO.getSubnetUuid()));
                        podDetail.setVpc(subnetMapResponse.getVpcMap().get(podDetail.getSubnet().getVpcId()));
                        podDetail.setSubnetType(SubnetVo.SubnetType.findById(podDetail.getSubnet().getSubnetType()).getName());
                        break;
                    } catch (Exception e) {
                        if (currentRetryCount == maxRetryCount) {
                            LOGGER.error("podDetail call vpc getSubnetMap finally failed podId:{} currentRetryCount:{}" +
                                    " exception: {}", podId, currentRetryCount, e);
                            throw e;
                        }
                        LOGGER.warn("podDetail call vpc getSubnetMap retry failed podId:{} currentRetryCount:{} " +
                                        "exception: {}", podId, currentRetryCount, e);
                        currentRetryCount++;
                    }
                }
            }
            // get eip info
            boolean eipAvaiable = false;
            if (StringUtils.isNotEmpty(podPO.getPublicIp())) {
                if (podPO.getEipActualStatus().equals("binded") && (podPO.getEipStatus() == EipConstant.STATUS_INIT ||
                        podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED)) {
                    eipAvaiable = true;
                    if (podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED) {
                        podDetail.setEipIsUserSpecified(true);
                    }
                } else {
                    LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(podPO.getUserId());
                    QueryEipListResponse eipListResponse = logicEipClient.queryEipV2(podPO.getPublicIp());
                    if (eipListResponse != null && !eipListResponse.getEipList().isEmpty() &&
                            eipListResponse.getEipList().get(0).getStatus().equals("binded")) {
                        // check bind relationship
                        if (StringUtils.isNotEmpty(eipListResponse.getEipList().get(0).getInstanceIp()) &&
                                eipListResponse.getEipList().get(0).getInstanceIp().equals(podPO.getInternalIp())) {
                            eipAvaiable = true;
                            podDetail.setEipId(eipListResponse.getEipList().get(0).getEipId());
                            podDetail.setPublicIp(eipListResponse.getEipList().get(0).getEip());
                            podDetail.setBandwidthInMbps(eipListResponse.getEipList().get(0).getBandwidthInMbps());
                            podDetail.setEipRouteType(eipListResponse.getEipList().get(0).getRouteType());
                            podDetail.setEipPayMethod(eipListResponse.getEipList().get(0).getBillingMethod());
                            if (podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED ||
                                    podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED_UNBINDED) {
                                podDetail.setEipIsUserSpecified(true);
                            }
                        }
                    }
                }
            }
            if (!eipAvaiable) {
                podDetail.setEipId("");
                podDetail.setPublicIp("");
                podDetail.setBandwidthInMbps(0);
                podDetail.setEipRouteType("");
                podDetail.setEipPayMethod("");
            }
        } catch (BceInternalResponseException e) {
            LOGGER.error("VPC server response exception:{}", e);
        }

        // 如果application不是"default"则为内部业务
        if (!APPLICATION_DEFAULT.equals(podDetail.getApplication())) {
            podDetail.setApplication(APPLICATION_INNER);
        }
        return podDetail;
    }

    /**
     * {@inheritDoc}
     * 根据Pod ID获取Pod详情，包括容器信息。如果Pod不存在或者没有容器，则抛出异常。
     *
     * @param podId Pod的ID
     * @return PodDetail类型，包含Pod和容器的详细信息
     * @throws CommonExceptions.ResourceNotExistException Pod不存在或者没有容器时抛出此异常
     */
    public PodDetail podDetailLight(String podId) {
        PodPO podPO = podDao.getPodDetailLight(getAccountId(), podId);
        if (podPO == null) {
            LOGGER.warn(LOG_DESCRIBE_PREFIX + "delete pod failed, pod {} not found", podId);
            throw new PodExceptions.PodNotExistException(podId);
        }

        if (ResourceAccountSetting.isUnifiedCharge()) {
            // 统一计费，匹配charge source
            if (!podPO.getChargeSource().equalsIgnoreCase(ResourceAccountSetting.getApplication())) {
                LOGGER.warn(LOG_DESCRIBE_PREFIX + "pod detail failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        } else {
            // 根据charge source 过滤列表, 不展示统一计费的资源
            if (!podPO.getChargeSource().equalsIgnoreCase(LogicalConstant.CHARGE_SOURCE_USER)) {
                LOGGER.warn(LOG_DESCRIBE_PREFIX + "pod detail failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        }

        List<ContainerPO> containers = containerDao.listContainersLightByPodId(podPO.getPodUuid());
        if (CollectionUtils.isEmpty(containers)) {
            containers = containerDao.listContainersLightByPodId(podPO.getPodId());
        }
        if (CollectionUtils.isEmpty(containers)) {
            throw new CommonExceptions.ResourceNotExistException();
        }
        PodDetail podDetail = PodUtils.convertPodDetailLightShow(podPO, containers);
        return podDetail;
    }

    /**
     * {@inheritDoc}
     * 根据Pod ID获取Pod详情，包括容器信息。包括Pod不存在情况。
     *
     * @param podId Pod的ID
     * @return PodDetail类型，包含Pod和容器的详细信息
     * @throws CommonExceptions.ResourceNotExistException Pod不存在或者没有容器时抛出此异常
     */
    public PodDetail podDetailWithDeleted(String podId) {
        PodPO podPO = podDao.getPodDetailWithDeleted(getAccountId(), podId);
        if (podPO == null) {
            LOGGER.warn(LOG_DESCRIBE_PREFIX + "pod detail failed, pod {} not found", podId);
            throw new PodExceptions.PodNotExistException(podId);
        }

        if (ResourceAccountSetting.isUnifiedCharge()) {
            // 统一计费，匹配charge source
            if (!podPO.getChargeSource().equalsIgnoreCase(ResourceAccountSetting.getApplication())) {
                LOGGER.warn(LOG_DESCRIBE_PREFIX + "pod detail failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        } else {
            // 根据charge source 过滤列表, 不展示统一计费的资源
            if (!podPO.getChargeSource().equalsIgnoreCase(LogicalConstant.CHARGE_SOURCE_USER)) {
                LOGGER.warn(LOG_DESCRIBE_PREFIX + "pod detail failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        }

        List<ContainerPO> containers = containerDao.listContainersWithDeletedByPodId(podPO.getPodUuid());
        if (CollectionUtils.isEmpty(containers)) {
            containers = containerDao.listContainersWithDeletedByPodId(podPO.getPodId());
        }
        if (CollectionUtils.isEmpty(containers)) {
            throw new CommonExceptions.ResourceNotExistException();
        }
        PodDetail podDetail = PodUtils.convertPodDetailLightShow(podPO, containers);
        return podDetail;
    }

    /**
     * @Description 获取Pod详情，根据不同的detailLevel返回不同的详细信息
     * @Param request BatchDescribeContainerGroupDetailRequest，包含batchId和detailLevel两个参数
     * @Return BatchDescribeContainerGroupDetailResponse，包含podList和errorCode两个属性，分别表示Pod列表和错误码
     * @ReturnType String
     * @ReturnType List<String>
     * @Throws 无异常抛出异常
     */
    public BatchDescribeContainerGroupDetailResponse podBatchDetail(BatchDescribeContainerGroupDetailRequest request) {
        switch (request.getDetailLevel()) {
            case "light":
                return podBatchDetailLight(request);
            case "full":
                return podBatchDetailLight(request);
            case "default":
                return podBatchDetailLight(request);
            default:
                return podBatchDetailLight(request);
        }
    }

    /**
     * @Description: 获取批量容器组详情，返回包含容器组详细信息的 BatchDescribeContainerGroupDetailResponse。
     * 如果请求中包含多个 podIds，则会将其转换为一个大小不超过 1000 的列表。
     * 如果 podIds 的大小超过 1000，则会抛出 RequestInvalidException 异常。
     * 如果 podIds 为空或者为 null，则直接返回空的 BatchDescribeContainerGroupDetailResponse。
     * 如果 podIds 对应的 pod 不存在，则不会添加到结果中。
     * 如果 podIds 对应的 pod 存在，但是没有对应的 container，则不会添加到结果中。
     * 如果 podIds 对应的 pod 和 container 都存在，则会将这些 pod 和 container 的详细信息添加到结果中。
     * @Param request BatchDescribeContainerGroupDetailRequest 批量容器组详情请求参数，包含 podIds 字段。
     * @Return BatchDescribeContainerGroupDetailResponse 批量容器组详情响应，包含 result 字段，result 是一个 List<PodDetail>。
     * @Throws CommonExceptions.RequestInvalidException 当请求中的 podIds 大小超过 1000 时抛出该异常。
     */
    public BatchDescribeContainerGroupDetailResponse podBatchDetailLight(BatchDescribeContainerGroupDetailRequest request) {
        String userId = getAccountId();
        BatchDescribeContainerGroupDetailResponse response = new BatchDescribeContainerGroupDetailResponse();
        List<String> podIds = request.getPodIds();
        if (CollectionUtils.isEmpty(podIds)) {
            return response;
        }
        if (podIds.size() > 1000) {
            LOGGER.error(LOG_DESCRIBE_PREFIX + " podIds invalid");
            String message = "The podIds is invalid, a valid podIds size must be between 1 and 1000.";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        List<PodPO> allPods = podDao.listPodsDetailLightByIds(userId, podIds);
        if (CollectionUtils.isEmpty(allPods)) {
            return response;
        }

        List<PodPO> pods = new ArrayList<>();

        if (ResourceAccountSetting.isUnifiedCharge()) {
            // 统一计费，匹配charge source
            pods = filterPodByChargeSource(allPods, ResourceAccountSetting.getApplication().toLowerCase());
        } else {
            // 根据charge source 过滤列表, 不展示统一计费的资源
            pods = filterPodByChargeSource(allPods, LogicalConstant.CHARGE_SOURCE_USER);
        }

        if (CollectionUtils.isEmpty(pods)) {
            return response;
        }

        List<String> podUuids = new ArrayList<>();
        for (PodPO pod : pods) {
            podUuids.add(pod.getPodUuid());
        }
        List<ContainerPO> containers = containerDao.listContainersLightByPodUuids(userId, podUuids);
        if (CollectionUtils.isEmpty(containers)) {
            return response;
        }
        Map<String, List<ContainerPO>> podUuid2Containers = new HashMap<>();
        for (ContainerPO container : containers) {
            if (podUuid2Containers.containsKey(container.getPodUuid())) {
                podUuid2Containers.get(container.getPodUuid()).add(container);
            } else {
                List<ContainerPO> containerList = new ArrayList<>();
                containerList.add(container);
                podUuid2Containers.put(container.getPodUuid(), containerList);
            }
        }

        for (PodPO pod : pods) {
            String podUuid = pod.getPodUuid();
            if (podUuid2Containers.containsKey(podUuid) && !podUuid2Containers.get(podUuid).isEmpty()) {
                PodDetail podDetail = PodUtils.convertPodDetailLightShow(pod, podUuid2Containers.get(podUuid));
                response.getResult().add(podDetail);
            }
        }
        return response;
    }

    public EipListResponse getEipInfoOnePod(String podIp, String vpcShortId) {
        EniClient eniClient = logicPodClientFactory.createEniClient(getAccountId());
        QueryEniSelfResponse eniInfo = eniClient.getEniByPrivateIp(vpcShortId, podIp);
        if (eniInfo != null && StringUtils.isNotEmpty(eniInfo.getPublicIp())) {
            LogicEipClient eipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
            EipListResponse eipListResponse = eipClient.queryEip(eniInfo.getPublicIp());
            if (eipListResponse != null && CollectionUtils.isNotEmpty(eipListResponse.getEipList())) {
                return eipListResponse;
            }
        }
        return null;
    }

    public PodVpcResponse getVpcAndSubnetByPodId(String podId) {
        PodPO podPO = getPodPOByPodId(podId);
        List<String> subnetIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(podPO.getSubnetUuid())) {
            subnetIds.add(podPO.getSubnetUuid());
        }
        SubnetMapResponse subnetMapResponse = new SubnetMapResponse();
        if (!subnetIds.isEmpty()) {
            SubnetMapRequest subnetMapRequest = new SubnetMapRequest();
            subnetMapRequest.setSubnetIds(subnetIds);
            subnetMapRequest.setAttachVpc(true);
            subnetMapResponse =
                    logicPodClientFactory.createExternalSubnetClient("").getSubnetMap(subnetMapRequest);
        }
        PodVpcResponse response = new PodVpcResponse();
        List<String> securityGroups = Arrays.asList(podPO.getSecurityGroupUuid().trim().split(","));
        if (podValidator.isNormalSecurityGroupId(securityGroups)) {
            List<SimpleSecurityGroupVO> securityGroupVO = getSecurityGroup(podPO.getSecurityGroupUuid());
            if (securityGroupVO.size() == 1) {
                response.setSecurityGroupVO(securityGroupVO.get(0));
            } else {
                response.setSecurityGroupVOList(securityGroupVO);
            }
        }
        response.setSubnet(subnetMapResponse.getSubnetMap().get(podPO.getSubnetUuid()));
        response.setVpc(subnetMapResponse.getVpcMap().get(response.getSubnet().getVpcId()));
        return response;
    }

    public byte[] downloadConfigFile(String podId, String name, String path) {
        PodPO podPO = getPodPOByPodId(podId);
        String config = podPO.getConfigFile();
        String file = "";
        List<ConfigFile> configFiles = PodUtils.base64Decode(config);
        for (ConfigFile configFile : configFiles) {
            if (!configFile.getName().equals(name)) {
                continue;
            }
            for (ConfigFileDetail configFileDetail : configFile.getConfigFiles()) {
                if (configFileDetail.getPath().equals(path)) {
                    file = configFileDetail.getFile();
                    break;
                }
            }
        }
        byte[] bom4utf8 = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        byte[] bytes = file.getBytes();
        byte[] outputBytes = new byte[bom4utf8.length + bytes.length];
        System.arraycopy(bom4utf8, 0, outputBytes, 0, bom4utf8.length);
        System.arraycopy(bytes, 0, outputBytes, 3, bytes.length);
        return outputBytes;
    }

    public byte[] download(PodListRequest podListRequest) {

        StringBuffer csv = new StringBuffer("ID, 名称, 状态, CPU(核), " +
                "内存(GB), 公网IP, 公网带宽, 内网IP, 重启策略, 创建时间" + "\r\n");

        List<PodPO> result = (List<PodPO>) listPodsWithPageByMultiKey(podListRequest).getResult();
        LOGGER.debug("pod downLoad size is {}", result.size());
        if (StringUtils.isNotBlank(podListRequest.getKeywordType())) {
            result = PodUtils.likeFilter(result, podListRequest.getKeywordType(),
                    podListRequest.getKeyword());
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (PodPO podPO : result) {
            csv.append(podPO.getPodId()).append(",");
            csv.append(podPO.getName()).append(",");
            csv.append(LogicalConstant.POD_STATUS_MAP.get(podPO.getStatus().toUpperCase())).append(",");
            csv.append(podPO.getvCpu()).append(",");
            csv.append(podPO.getMemory()).append(",");
            csv.append(podPO.getPublicIp()).append(",");
            csv.append(podPO.getBandwidthInMbps()).append(",");
            csv.append(podPO.getInternalIp()).append(",");
            csv.append(LogicalConstant.POD_RESTART_POLICY_MAP.get(podPO.getRestartPolicy().toUpperCase())).append(",");
            csv.append(formatter.format(podPO.getCreatedTime())).append(",");
            csv.append("\r\n");
        }

        byte[] bom4utf8 = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        byte[] bytes = csv.toString().getBytes();
        byte[] outputBytes = new byte[bom4utf8.length + bytes.length];
        System.arraycopy(bom4utf8, 0, outputBytes, 0, bom4utf8.length);
        System.arraycopy(bytes, 0, outputBytes, 3, bytes.length);
        return outputBytes;
    }

    public BceInternalResponse logDownload(String podID, String containerName, Integer limitBytes,
                                           Integer tailLines, String sinceTime, Integer sinceSeconds,
                                           Boolean timestamps, Boolean previous, Boolean follow) {
        String host = "";
        PodPO podDetail = getPodPO(podID);
        String podUUID = podDetail.getPodUuid();
        try {
            // 调Nova接口
            Server server = logicPodClientFactory.createAdminPodClient().getServer(podUUID);
            host = server.getHost();
        } catch (BceInternalResponseException e) {
            LOGGER.debug("PodClient.getServer failed : {}", e);
            if (e.getHttpStatus() == 404) {
                throw new PodExceptions.ResourceNotExistException();
            }
            throw new PodExceptions.GetServerFailed();
        }
        if (StringUtils.isEmpty(host)) {
            LOGGER.debug("server host is empty");
            throw new PodExceptions.GetServerFailed();
        }
        // cn 接口
        return logicPodClientFactory.createContainerManagerClient(host, getAccountId())
                .getPodLog(podUUID, containerName, limitBytes, tailLines, sinceTime,
                        sinceSeconds, timestamps, previous, follow);
    }

    public LogicPageResultResponse<CCRImage> listCCRImage(String keyword, String keywordType, String order,
                                                          String orderBy, Integer pageNo, Integer pageSize) {
        LogicPageResultResponse<CCRImage> response = new LogicPageResultResponse<>();

        CCRClient ccrClient = logicPodClientFactory.createCCRClient(getAccountId());
        CCRImageResponse ccrImageResponse = new CCRImageResponse();

        try {
            ccrImageResponse = ccrClient.listUserImage();
        } catch (Exception e) {
            LOGGER.debug("Fail to list user image: {}", e);
        }

        List<CCRImage> imageList = ccrImageResponse.getResult();

        Comparator<CCRImage> comparator = CCRImage.getComparator(orderBy);
        if ("desc".equalsIgnoreCase(order)) {
            Collections.sort(imageList, Collections.reverseOrder(comparator));
        } else {
            Collections.sort(imageList, comparator);
        }

        int totalCount = imageList.size();
        List<CCRImage> resultList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = (pageNo - 1) * pageSize;
            start = start < 0 ? 0 : start;
            int end = start + pageSize > totalCount ? totalCount : start + pageSize;
            for (int i = start; i < end; i++) {
                resultList.add(imageList.get(i));
            }
        } else {
            resultList = imageList;
        }
        if (pageSize == null) {
            pageSize = 1000;
        }
        if (pageNo == null) {
            pageNo = 1;
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(totalCount);
        response.setResult(resultList);
        return response;
    }

    public LogicPageResultResponse<UserImage> listUserImage(String keyword, String keywordType, String order,
                                                            String orderBy, Integer pageNo, Integer pageSize) {
        LogicPageResultResponse<UserImage> response = new LogicPageResultResponse<>();

        CceImageClient cceImageClient = logicPodClientFactory.createCceImageClient(getAccountId());
        UserImageResponse userImageResponse = new UserImageResponse();

        try {
            userImageResponse = cceImageClient.listUserImage();
        } catch (Exception e) {
            LOGGER.debug("Fail to list user image");
        }

        List<UserImage> imageList = userImageResponse.getRepositories();
        LOGGER.info("begin to get image tag, begin time: {}", new Date().toString());
        Iterator<UserImage> imageIterator = imageList.iterator();
        while (imageIterator.hasNext()) {
            UserImage image = imageIterator.next();
            if ("name".equalsIgnoreCase(keywordType) && image.getName().contains(keyword)) {
                bciAsyncService.getImageTags(image, cceImageClient);
            } else {
                imageIterator.remove();
            }
        }

        for (UserImage image : imageList) {
            ImageTags imageTags = (ImageTags) asyncExecutorService.getAsyncResult(WorkKeyUtil.genWorkKey("getImageTags",
                    Arrays.asList(image, cceImageClient)));
            image.setTags(PodUtils.getTags(imageTags));
            image.setAddress(ImageConstant.USER_IMAGE_URL + image.getNamespace() + "/" + image.getName());
        }
        LOGGER.info("finish to get image tag, finish time: {}", new Date().toString());

        Comparator<UserImage> comparator = UserImage.getComparator(orderBy);
        if ("desc".equalsIgnoreCase(order)) {
            Collections.sort(imageList, Collections.reverseOrder(comparator));
        } else {
            Collections.sort(imageList, comparator);
        }

        int totalCount = imageList.size();
        List<UserImage> resultList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = (pageNo - 1) * pageSize;
            start = start < 0 ? 0 : start;
            int end = start + pageSize > totalCount ? totalCount : start + pageSize;
            for (int i = start; i < end; i++) {
                resultList.add(imageList.get(i));
            }
        } else {
            resultList = imageList;
        }
        if (pageSize == null) {
            pageSize = 1000;
        }
        if (pageNo == null) {
            pageNo = 1;
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(totalCount);
        response.setResult(resultList);
        return response;
    }

    public LogicPageResultResponse<OfficialImage> listOfficialImage(String keyword, String keywordType, String order,
                                                                    String orderBy, Integer pageNo, Integer pageSize) {
        LogicPageResultResponse<OfficialImage> response = new LogicPageResultResponse<>();

        OfficialImageResponse officialImageResponse = logicPodClientFactory.createCceImageClient(getAccountId())
                .listOfficialImage(keyword, keywordType);
        List<CceOfficialImage> imageList = officialImageResponse.getImages();

        List<CceOfficialImage> officialImages = new LinkedList<>();
        if (StringUtils.isNotBlank(keywordType) && StringUtils.isNotBlank(keyword)) {
            // 目前只支持按镜像名搜索
            if ("name".equals(keywordType)) {
                for (CceOfficialImage imageInfo : imageList) {
                    if (StringUtils.isNotBlank(imageInfo.getRepository())
                            && imageInfo.getRepository().contains(keyword)) {
                        officialImages.add(imageInfo);
                    }
                }
            }
        } else {
            officialImages = imageList;
        }

        Map<String, OfficialImage> imageMap = new HashMap<>();
        for (CceOfficialImage image : officialImages) {
            if (imageMap.containsKey(image.getRepository())) {
                OfficialImage officialImage = imageMap.get(image.getRepository());
                officialImage.getTags().add(image.getTag());
            } else {
                OfficialImage oImage = new OfficialImage();
                oImage.setName(image.getRepository());
                oImage.setDescription(image.getDescription());
                oImage.getTags().add(image.getTag());
                oImage.setAddress(image.getAddress().substring(0, image.getAddress().lastIndexOf(":")));
                oImage.setIcon(image.getIcon());
                imageMap.put(image.getRepository(), oImage);
            }
        }

        List<OfficialImage> images = new ArrayList<>(imageMap.values());

        Comparator<OfficialImage> comparator = OfficialImage.getComparator(orderBy);
        if ("desc".equalsIgnoreCase(order)) {
            Collections.sort(images, Collections.reverseOrder(comparator));
        } else {
            Collections.sort(images, comparator);
        }

        int totalCount = images.size();
        List<OfficialImage> resultList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = (pageNo - 1) * pageSize;
            start = start < 0 ? 0 : start;
            int end = start + pageSize > totalCount ? totalCount : start + pageSize;
            for (int i = start; i < end; i++) {
                resultList.add(images.get(i));
            }
        } else {
            resultList = images;
        }
        if (pageSize == null) {
            pageSize = 1000;
        }
        if (pageNo == null) {
            pageNo = 1;
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(totalCount);
        response.setResult(resultList);
        return response;
    }

    public LogicPageResultResponse<DockerHubImage> listDockerHubImages(String keyword, String keywordType, String order,
                                                                       String orderBy, Integer pageNo,
                                                                       Integer pageSize) {

        LogicPageResultResponse<DockerHubImage> response = new LogicPageResultResponse<>();
        DockerHubImageResponse dockerImage = null;

        List<DockerHubImage> imagesByKeyword = new ArrayList<>();
        if (StringUtils.isNotEmpty(keyword) || "name".equalsIgnoreCase(keywordType)) {
            dockerImage = getImages(pageNo, 1000);
            for (DockerHubImage dockerHubImage : dockerImage.getResults()) {
                if (dockerHubImage.getName().contains(keyword)) {
                    imagesByKeyword.add(dockerHubImage);
                }
            }
        } else {
            dockerImage = getImages(pageNo, pageSize);
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(imagesByKeyword.size() > 0 ? imagesByKeyword.size() : dockerImage.getCount());
        response.setResult(imagesByKeyword.size() > 0 ? PodUtils.subList(imagesByKeyword, pageNo, pageSize)
                : dockerImage.getResults());
        return response;
    }

    public LogicPageResultResponse<String> listDockerHubImageTags(String name, Integer pageNo, Integer pageSize) {

        LogicPageResultResponse<String> response = new LogicPageResultResponse<>();
        DockerHubImageTagResponse dockerImageTags = logicPodClientFactory.createDockerHubrClient()
                .listDockerHubImageTag(name, pageNo, pageSize);

        List<String> tags = new ArrayList<>();
        for (DockerHubImageTag tag : dockerImageTags.getResults()) {
            tags.add(tag.getName());
        }

        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(dockerImageTags.getCount());
        response.setResult(tags);
        return response;
    }

    public void bindEipToPod(String eip, String podId) {
        String userAccountId = getAccountId();
        PodPO podPO = podDao.getPodDetail(userAccountId, podId);
        if (podPO == null) {
            throw new CommonExceptions.RequestInvalidException();
        }

        // 1. get vpcId from pod annotation
        V1Pod podInfo = k8sService.getPod(podPO.getUserId(), podPO.getPodId());
        if (podInfo == null || podInfo.getMetadata() == null || podInfo.getStatus() == null) {
            throw new CommonExceptions.RequestInvalidException(String.format(
                    "get pod failed, user %s, pod %s",
                    podPO.getUserId(), podPO.getPodId()));
        }
        String vpcId = podInfo.getMetadata().getAnnotations().get("cross-vpc-eni.cce.io/vpcID");
        String podIp = podInfo.getStatus().getPodIP();

        // 2. get eni by podIp
        // todo: BCI 2.1 get eniId from pod annotation
        if (StringUtils.isEmpty(vpcId) || StringUtils.isEmpty(podIp)) {
            throw new CommonExceptions.RequestInvalidException(String.format(
                    "invalid pod: id %s, ip %s, vpcId %s",
                    podPO.getPodId(), podIp, vpcId));
        }

        EniClient eniClient = logicPodClientFactory.createEniClient(userAccountId);
        QueryEniSelfResponse eniInfo = eniClient.getEniByPrivateIp(vpcId, podIp);
        if (eniInfo == null) {
            throw new CommonExceptions.RequestInvalidException(String.format(
                    "get pod eni failed, pod %s vpc %s", podIp, vpcId));
        }

        // 3. bind eip to eni privateIp
        try {
            eniClient.attachEip(eniInfo.getEniId(), podPO.getInternalIp(), eip);
        } catch (Exception ex) {
            throw new CommonExceptions.RequestInvalidException(String.format(
                    "attach eip failed, pod %s, eip %s, err %s", podIp, eip, ex));

        }
        // 4. get eip bandwidth, and save eip info db
        int bandwidthInMbps = 0;
        try {
            LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(userAccountId);
            EipListResponse eipListResponse = logicEipClient.queryEip(eip);
            if (eipListResponse != null && !eipListResponse.getEipList().isEmpty()) {
                bandwidthInMbps = eipListResponse.getEipList().get(0).getBandwidthInMbps();
            }
        } catch (Exception ex) {
            LOGGER.error("bindEipToPod: get eip failed, eip {} of {} {}, err {}",
                    eip, podId, userAccountId, ex);
        }
        podDao.bindEip(userAccountId, podId, eip, bandwidthInMbps);

        // BindEipToBciRequest bindEipToBccRequest = new BindEipToBciRequest();
        // bindEipToBccRequest.setInstanceType(PodConstants.SERVICE_TYPE);
        // bindEipToBccRequest.setInstanceId(podPO.getPodUuid());

        // LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
        // logicEipClient.bindEip(eip, bindEipToBccRequest);
    }

    public void unBindEipFromPod(String eip, String podId) {
        String userAccountId = getAccountId();
        PodPO podPO = podDao.getPodDetail(userAccountId, podId);
        if (podPO == null || !podPO.getPublicIp().equals(eip)) {
            throw new CommonExceptions.RequestInvalidException();
        }

        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
        logicEipClient.unbindEip(eip);

        boolean isUserSpecifiedEip = false;
        if (podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED ||
                podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED_UNBINDED) {
            isUserSpecifiedEip = true;
        }
        podDao.unbindEip(podPO.getUserId(), podPO.getPodId(), podPO.getPublicIp(), isUserSpecifiedEip);
    }

    public void unBindEipFromPod(String eip) {
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
        logicEipClient.unbindEip(eip);
    }

    public BciQuota getBciQuota(Boolean needGlobalQuota) {
        return logicalQuotaService.getBciQuota();
    }

    /**
     * 批量获取bci配额
     *
     * @param accountId
     * @return
     */
    public BciQuota getBciQuota(String accountId) {
        return logicalQuotaService.getBciQuota(accountId);
    }

    /**
     * 获取单个bci配额,如果没有配额,则返回默认配额
     *
     * @param accountId
     * @param quotaType
     * @param defaultQuotaNum
     * @return
     */
    public int getBciQuota(String accountId, String quotaType, int defaultQuotaNum) {
        return logicalQuotaService.getBciQuota(accountId, quotaType, defaultQuotaNum);
    }

    public BciCreateResponse createPod(BaseCreateOrderRequestVo<IOrderItem> request, String from) {
        CreateContainerGroupRequestExtra requestExtra = new CreateContainerGroupRequestExtra(from);
        return createPod(request, requestExtra);
    }

    public BciCreateResponse createPod(BaseCreateOrderRequestVo<IOrderItem> request,
                                       CreateContainerGroupRequestExtra requestExtra) {
        return createPod(request, requestExtra.getFrom(), requestExtra.getClientToken());
    }

    public BciCreateResponse createPod(BaseCreateOrderRequestVo<IOrderItem> request, String from, String clientToken) {
        LOGGER.debug("performance test: enter podservice createPod from:{}, clientToken:{}", from, clientToken);
        long beginTime = new Date().getTime();
        ValidatedItem validatedItem = validator.validate(request, from, clientToken, null);
        LOGGER.debug("performance test: validate param cost {} ms from {}", new Date().getTime() - beginTime, from);
        // 校验镜像名称、地址、版本参数是否合法
        validator.checkImageAddressIsVaildOrNot(validatedItem.getPodPurchaseRequest());

        // 校验BLS任务参数是否合法
        validator.checkBlsTaskParamValidOrNot(validatedItem.getPodPurchaseRequest());

        // add stock validate 

        LOGGER.debug("account {} begin to pod stock validate", getAccountId());
        if (bciPodStockValidateEnable && !validatePodStocks(validatedItem)) {
            LOGGER.debug("account {} have no enough pod stock, create pods failed", getAccountId());
            throw new PodExceptions.NoEnoughStock();
        }
        LOGGER.debug("account {} pod stock validate success", getAccountId());

        List<CreateNewTypeOrderItem> createNewTypeOrderRequestItems = new ArrayList<>();

        Map<String, PodPO> podId2PodPOMap = new HashMap<>();

        Map<String, List<ContainerPO>> podId2ContainerPOMap = new HashMap<>();

        Map<String, BciOrderExtra> podId2BciOrderExtraMap = new HashMap<>();

        createNewTypeOrderRequestItems.addAll(handleInstanceCreateBci(validatedItem, podId2PodPOMap,
                podId2ContainerPOMap, podId2BciOrderExtraMap));
        // createNewTypeOrderRequestItems.addAll(handleBciCreateEip(validatedItem));

        Map<String, PodPO> instanceIds2PodPOMap = commonUtils.savePodCreate2DB(
                podId2PodPOMap, podId2ContainerPOMap, podId2BciOrderExtraMap);
        List<String> instanceIds = new ArrayList<>(instanceIds2PodPOMap.keySet());
        LOGGER.debug("performance test: after save pods such as {} into db ", instanceIds);
        // ===== add migration uuid -> podId mapping =====
        String migrationUuid = validatedItem.getPodPurchaseRequest() != null ?
                validatedItem.getPodPurchaseRequest().getMigrationUuid() : null;
        if (StringUtils.isNotEmpty(migrationUuid)) {
            handleMigrationMapping(migrationUuid, instanceIds);
        }
        // ===== end migration mapping logic =====

        for (Map.Entry<String, PodPO> entry : instanceIds2PodPOMap.entrySet()) {
            PodPO podPO = entry.getValue();
            if (StringUtils.isNotEmpty(podPO.getOrderId())) {
                BciCreateResponse bciCreateResponse = new BciCreateResponse();
                bciCreateResponse.setOrderId(podPO.getOrderId());
                bciCreateResponse.setPodIds(instanceIds);
                LOGGER.warn("createPod podIdempotentCheck failed, orderId:{}, instances:{}", podPO.getOrderId(),
                        JsonUtil.toJSONString(instanceIds));
                return bciCreateResponse;
            }
        }

        // 如果发生了向上取整，异步上报到bcm
        podRoundUpEventThreadPoolExecutor.execute(new SyncRoundUpEventToBCM(validatedItem, podId2PodPOMap, getAccountId()));

        CreateOrderRequest<CreateNewTypeOrderItem> createNewTypeOrderRequest = new CreateOrderRequest<>();
        createNewTypeOrderRequest.setOrderType(OrderType.NEW.name());
        createNewTypeOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createNewTypeOrderRequest.setTotal(request.getTotal());
        createNewTypeOrderRequest.setTicketId(request.getTicketId());
        createNewTypeOrderRequest.setPaymentMethod(request.getPaymentMethod());

        createNewTypeOrderRequest.setItems(createNewTypeOrderRequestItems);
        LOGGER.debug("create server,createNewTypeOrderRequest  is  {}", JsonUtil.toJSON(createNewTypeOrderRequest));
        OrderUuidResult orderUuidResult;
        try {
            orderUuidResult = submitCreateOrderToServiceCatalog(createNewTypeOrderRequest, LOG_CREATE_PREFIX);
        } catch (Exception e) {
            LOGGER.error("failed to create new order, update status for pod : {}", instanceIds);
            String accountId = getAccountId();

            for (Map.Entry<String, PodPO> entry : podId2PodPOMap.entrySet()) {
                String podId = entry.getKey();
                PodPO podPO = entry.getValue();
                String originalClientToken = podPO.getClientToken();
                // 将clientToken更新为orderId-podId, 置为无效token，避免后续客户端重试创建pod因为唯一索引无法落库
                podPO.setClientToken(originalClientToken + "-" + podId);
                podDao.updatePodClientToken(podPO);
                podDao.deletePod(accountId, podId);
                podExtraDao.deletePodExtra(accountId, podId);
                // 此处eip资源的回收交给EipRecycleSyncServiceV2, 因为这里eip状态大概率是creating，无法处理
            }

            throwPermissionDeniedExceptionIfAppropriate(e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        LOGGER.debug("performance test: after submit order {} for pods such as {}",
                orderUuidResult.getOrderId(), JsonUtil.toJSON(instanceIds));

        podDao.updateOrderId(instanceIds, getAccountId(), orderUuidResult.getOrderId());
        podExtraDao.updateOrderId(instanceIds, getAccountId(), orderUuidResult.getOrderId());

        BciCreateResponse bciCreateResponse = new BciCreateResponse();
        bciCreateResponse.setOrderId(orderUuidResult.getOrderId());
        bciCreateResponse.setPodIds(instanceIds);
        for (String podId : instanceIds) {
            PodPO podPO = podId2PodPOMap.get(podId);
            if (podPO != null && !podPO.getClientToken().equals(podId)) {
                bciCreateResponse.getPodUuid2PodIds().put(podPO.getClientToken(), podId);
            }
        }
        return bciCreateResponse;
    }

    private boolean validatePodStocks(ValidatedItem validatedItem) {

        // form previewRequest 
        PreviewPodCapacityRequest previewRquest = validator.formPreviewPodCapacityRequest(validatedItem);
        LOGGER.debug("account {} begin validate pod stocks, previewRequest:{}", getAccountId(), previewRquest.toString());

        // get candicate instance groups 
        List<InstanceGroupConfigMap> igs = getCandiatesInstanceGroups(previewRquest);
        if (CollectionUtils.isEmpty(igs)) {
            LOGGER.debug("account {} pod spec can't find candidate instance group, previewRequest:{}", getAccountId(), previewRquest.toString());
            return false;
        }

        // get all bci backand resource 
        int cpuStock = 0;
        int memStock = 0;
        int gpuStock = 0;
        for (int i = 0; i < igs.size(); i++) {
            // get backend stocks
            // eg:AZONE-gajl-bcc.incga2.c16m64-cpu-amd-pfs
            String igName = igs.get(i).getInstanceGroupName();
            String placeholderName = igName.toLowerCase() + "-placeholder";
            // get igbccSpecInfo 
            InstanceGroupBccSpecInfo igbccSpecInfo = getInstanceGroupBccSpecInfoFromCache(igName);
            if (igbccSpecInfo == null) {
                LOGGER.debug("account {} instance group %s igbccSpecInfo is null", getAccountId(), igName);
                continue;
            }
            V1Deployment placeholderDeployment = k8sService.getKubeSystemInstanceGroupPlaceholderDeployment(bciAccountID, placeholderName);

            if (placeholderDeployment == null) {
                LOGGER.debug("account {} instance group not support this spec bci, deployment is null", getAccountId());
                continue;
            }
            if (placeholderDeployment.getStatus() == null) {
                LOGGER.debug("account {} instance group placeholder deployment status is null", getAccountId());
                continue;
            }

            int availableBufferNode = 0;
            try {
                // 原始代码写的不好，这里在没有可用节点的时候会抛出异常
                availableBufferNode = placeholderDeployment.getStatus().getAvailableReplicas();
            } catch (Exception e) {
                LOGGER.debug("account {} instance group placeholder deployment status {} getAvailableReplicas error {},",
                        getAccountId(), placeholderDeployment.getStatus(), e);
                continue;
            }
            cpuStock += availableBufferNode * igbccSpecInfo.getCpu();
            memStock += availableBufferNode * igbccSpecInfo.getMemory();
            gpuStock += availableBufferNode * igbccSpecInfo.getGpuCount();
        }

        // judge bci local pod stock 
        int localPodStocks = getAvailablePodStock(cpuStock, memStock, gpuStock, validatedItem);
        LOGGER.debug("account {} have local pod stock {}, cpuStock {}, memoryStock {}, gpuGount {} ", getAccountId(),
                localPodStocks, cpuStock, memStock, gpuStock);
        if (localPodStocks > 0) {
            LOGGER.debug("account {} have enough local pod stock {}", getAccountId(), localPodStocks);
            return true;
        }
        if (!bciRemotePodStockValidateEnable) {
            LOGGER.debug("account {} don't enable remote pod stack validatate", getAccountId());
            return false;
        }
        // judge bci remote pod stock 
        for (int i = 0; i < igs.size(); i++) {
            // get backend stocks
            // eg:AZONE-gajl-bcc.incga2.c16m64-cpu-amd-pfs
            String igName = igs.get(i).getInstanceGroupName();
            // get igbccSpecInfo 
            InstanceGroupBccSpecInfo igbccSpecInfo = getInstanceGroupBccSpecInfoFromCache(igName);
            if (igbccSpecInfo == null) {
                LOGGER.debug("account {} instance group %s igbccSpecInfo is null", getAccountId(), igName);
                continue;
            }
            int remoteNode = getBccSpecRemoteStock(igName, igbccSpecInfo);
            LOGGER.debug("account {} ig {} has remote node  {}, cpu {}, memoryStock {}, gpuGount {} ", getAccountId(), igName,
                    remoteNode, igbccSpecInfo.getCpu(), igbccSpecInfo.getMemory(), igbccSpecInfo.getGpuCount());

            cpuStock += remoteNode * igbccSpecInfo.getCpu();
            memStock += remoteNode * igbccSpecInfo.getMemory();
            gpuStock += remoteNode * igbccSpecInfo.getGpuCount();
        }

        int remotePodStocks = getAvailablePodStock(cpuStock, memStock, gpuStock, validatedItem);
        LOGGER.debug("account {} has remote pod stock {}, cpuStock {}, memoryStock {}, gpuGount {} ", getAccountId(),
                remotePodStocks, cpuStock, memStock, gpuStock);
        if (remotePodStocks > 0) {
            LOGGER.debug("account {} have enough remote pod stock {}", getAccountId(), remotePodStocks);
            return true;
        }
        LOGGER.debug("account {} have no enough remote pod stock {}, ", getAccountId(), remotePodStocks);

        return false;
    }

    private int getAvailablePodStock(int cpuStock, int memStock, int gpuStock, ValidatedItem validatedItem) {
        float cpuCount = cpuStock / validatedItem.getPodPurchaseRequest().getCpu();
        float memCount = memStock / validatedItem.getPodPurchaseRequest().getMemory();

        if (validatedItem.getPodPurchaseRequest().getGpuCount() > 0) {
            float gpuCount = gpuStock / validatedItem.getPodPurchaseRequest().getGpuCount();
            return (int) Math.min(Math.min(cpuCount, memCount), gpuCount);
        } else {
            return (int) Math.min(cpuCount, memCount);
        }
    }

    private Map<String, Map<String, Integer>> getBccQuotaFromCache() {
        // from cache
        Map<String, Map<String, Integer>> bccQuota = bciBccQuotaCache.get(bciAccountID);
        if (bccQuota != null) {
            return bccQuota;
        }
        // get quota from quota service 
        bccQuota = logicalQuotaService.getBciBccQuota(bciAccountID);
        if (bccQuota == null) {
            LOGGER.debug("getBciBccQuota failed");
            return null;
        }
        bciBccQuotaCache.put(bciAccountID, bccQuota);
        return bccQuota;
    }


    private InstanceGroupBccSpecInfo getInstanceGroupBccSpecInfoFromCache(String instanceGroupName) {
        // from cache
        InstanceGroupBccSpecInfo igbccSpecInfo = instanceGroupBccSpecCache.get(instanceGroupName);
        if (igbccSpecInfo != null) {
            return igbccSpecInfo;
        }

        igbccSpecInfo = new InstanceGroupBccSpecInfo();
        boolean formSuccess = igbccSpecInfo.formInstanceGroupBccSpecInfo(instanceGroupName);
        if (!formSuccess) {
            LOGGER.debug("instanceGroupName:{} formInstanceGroupBccSpecInfo failed: {}", instanceGroupName, igbccSpecInfo);
            return null;
        }
        instanceGroupBccSpecCache.put(instanceGroupName, igbccSpecInfo);
        return igbccSpecInfo;
    }

    // get bcc stock from cache 
    private BccSpecStockResponse getBccStockFromCache(String instanceGroupName, InstanceGroupBccSpecInfo igbccSpecInfo) {
        BccSpecStockResponse bccSpecStockResponse = bciBCCStockCache.get(instanceGroupName);
        if (bccSpecStockResponse != null) {
            return bccSpecStockResponse;
        }
        BCCClient bccClient = logicPodClientFactory.createBCCClient(bciAccountID);
        BccSpecStockRequest bccSpecStockRequest = new BccSpecStockRequest();
        bccSpecStockRequest.setSpec(igbccSpecInfo.getBccSpec());
        LOGGER.debug("bccSpecStockRequest: " + bccSpecStockRequest.toString());
        BccSpecStockResponse specStockResponse = bccClient.getBccSpecStock(bccSpecStockRequest);
        LOGGER.debug("BccSpecStockResponse: " + specStockResponse.toString());
        bciBCCStockCache.put(instanceGroupName, specStockResponse);
        return specStockResponse;
    }


    // 获取BCC远端库存，综合实际库存和 bci的 BCC quota
    private int getBccSpecRemoteStock(String instanceGroupName, InstanceGroupBccSpecInfo igbccSpecInfo) {
        BccSpecStockResponse bccSpecStockResponse = getBccStockFromCache(instanceGroupName, igbccSpecInfo);
        int stock = 0;
        for (int i = 0; i < bccSpecStockResponse.getBccStocks().size(); i++) {

            ZoneMapDetail zoneDetail = getBciZoneMapDetailFromCache(igbccSpecInfo.getPhysicalZone());
            String logicZone = zoneDetail.getLogicalZone();
            String bccLogicZone = bccSpecStockResponse.getBccStocks().get(i).getLogicalZone();
            if (StringUtils.isEmpty(logicZone) || StringUtils.isEmpty(bccLogicZone)) {
                LOGGER.debug("getBccSpecRemoteStock: logicZone or bccLogicZone is empty");
                continue;
            }
            char zone = logicZone.toLowerCase().charAt(logicZone.length() - 1);
            char bccZone = bccLogicZone.charAt(bccLogicZone.length() - 1);
            LOGGER.debug("getBccSpecRemoteStock: physicalZone {}, logicZone {}, bccLogicZone {}, bccLZone {}",
                    igbccSpecInfo.getPhysicalZone(), zone, bccLogicZone, bccZone);
            if (zone == bccZone) {
                int inVentoryQuantity = bccSpecStockResponse.getBccStocks().get(i).getInventoryQuantity();
                // 8 核以下直接看 stock即可
                if (igbccSpecInfo.getCpu() <= 8) {
                    stock += inVentoryQuantity;
                } else {
                    // 8 核以上，需要看 quota
                    Map<String, Map<String, Integer>> bciBccSpecQuota = getBccQuotaFromCache();
                    if (bciBccSpecQuota.containsKey(igbccSpecInfo.getBccSpec())) {
                        Map<String, Integer> zoneSpecQuota = bciBccSpecQuota.get(igbccSpecInfo.getBccSpec());
                        LOGGER.debug("instanceGroupName:{}, zoneSpecQuota", instanceGroupName, zoneSpecQuota);

                        if (zoneSpecQuota.containsKey(logicZone)) {
                            int bccQuota = zoneSpecQuota.get(logicZone);
                            stock += Math.min(inVentoryQuantity, bccQuota);
                            LOGGER.debug("instanceGroupName:{}, igbccSpecInfo: {}, bccQuota: bccStock", igbccSpecInfo, bccQuota, inVentoryQuantity);
                        } else {
                            stock += inVentoryQuantity;
                        }
                    } else {
                        stock += inVentoryQuantity;
                    }
                    ;
                }
            }
        }
        LOGGER.debug("get remote bcc stock instanceGroupName:{}, stock {}", igbccSpecInfo, stock);
        return stock;
    }

    private ZoneMapDetail getBciZoneMapDetailFromCache(String physicalZoneName) {
        // from cache
        ZoneMapDetail zoneMapDetail = bciZoneDetailsCache.get(physicalZoneName);
        if (zoneMapDetail != null) {
            return zoneMapDetail;
        }
        try {
            ZoneClient zoneClient = logicPodClientFactory.createZoneClient(bciAccountID);
            zoneMapDetail = zoneClient.createZoneByPhysicalZone(physicalZoneName);
        } catch (Exception e) {
            LOGGER.info("getZone from physical zone error = {}", e);
            throw new PodExceptions.InvalidateZoneException();
        }

        if (zoneMapDetail == null) {
            throw new PodExceptions.InvalidateZoneException();
        }
        bciZoneDetailsCache.put(physicalZoneName, zoneMapDetail);
        return zoneMapDetail;
    }

    public class SyncRoundUpEventToBCM implements Runnable {
        private ValidatedItem validatedItem;
        private Map<String, PodPO> podId2PodPOMap;

        private String accountId;

        public SyncRoundUpEventToBCM(ValidatedItem validatedItem, Map<String, PodPO> podId2PodPOMap, String accountID) {
            this.validatedItem = validatedItem;
            this.podId2PodPOMap = podId2PodPOMap;
            this.accountId = accountID;
        }

        @Override
        public void run() {
            LOGGER.debug("SyncRoundUpEventToBCM start, podId:{}", podId2PodPOMap.keySet());
            try {
                reportResourceRoundUpEvent(this.validatedItem, this.podId2PodPOMap, this.accountId);
            } catch (Exception e) {
                LOGGER.error("reportResourceRoundUpEvent podId:{} error:", podId2PodPOMap.keySet(), e);
            }
            LOGGER.debug("SyncRoundUpEventToBCM end, podId:{}", podId2PodPOMap.keySet());
        }

        /**
         * @param validatedItem  ValidatedItem对象，包含资源向上取整标志和结果
         * @param podId2PodPOMap Map<String, PodPO>类型，包含每个Pod的ID和相应的PodPO对象
         * @return void无返回值
         * @description 报告资源向上取整的事件，并将其推送到BCM中
         */
        private void reportResourceRoundUpEvent(ValidatedItem validatedItem, Map<String, PodPO> podId2PodPOMap, String accountId) {
            // 判断是否发生了资源向上取整
            if (!validatedItem.getRoundUpFlag()) {
                LOGGER.debug("no round up event happened for podId {}", podId2PodPOMap.keySet());
                return;
            }

            // 获取当前时间戳
            ZonedDateTime now = ZonedDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
            String formattedDate = now.format(formatter);

            for (Map.Entry<String, PodPO> entry : podId2PodPOMap.entrySet()) {
                String podId = entry.getKey();
                Map<String, Float> roundUpResult = validatedItem.getRoundUpResult();
                BcmEventRequestWrapper bcmEvent = new BcmEventRequestWrapper();
                String eventID = podId + "-" + UUID.randomUUID();
                BcmEventRequestWrapper.Content contentStr = new BcmEventRequestWrapper.Content()
                        .setInfo("Created")
                        .setAdvice("string")
                        .setReason("AutoInstanceTypeMatch")
                        .setMessage(String.format(
                                "The best matched instanceType for current instance is %sc %sGi",
                                roundUpResult.get("cpu"),
                                roundUpResult.get("memory")));
                String content = JsonUtil.toJSON(contentStr);
                bcmEvent.setAccountId(accountId)
                        .setRegion(regionConfiguration.getCurrentRegion())
                        .setResourceType("INSTANCE")
                        .setResourceId(podId)
                        .setEventId(eventID)
                        .setEventType("BCINotice")
                        .setEventLevel("NOTICE")
                        .setEventAlias("通知事件")
                        .setEventAliasEn("")
                        .setTimestamp(formattedDate)
                        .setContent(content);
                pushPodEventToBCM(bcmEvent, accountId);
            }
        }

        public void pushPodEventToBCM(BcmEventRequestWrapper bcmEvent, String accountId) {
            BcmClient bcmClient = logicPodClientFactory.createBcmClient(accountId);
            bcmEvent.setAccountId(accountId);
            try {
                BcmListEventsResponse response = bcmClient.pushEvents(bcmEvent);
                LOGGER.debug("push event to bcm, response:{}", response);
            } catch (Exception e) {
                LOGGER.error("push event to bcm failed, exception: ", e);
            }
        }
    }

    /**
     * 统一提交创建order请求到ServiceCatalog
     *
     * @param createOrderRequest
     * @param logPrefix
     * @return
     */
    public OrderUuidResult submitCreateOrderToServiceCatalog(CreateOrderRequest createOrderRequest, String logPrefix) {
        OrderUuidResult result = null;
        try {
            ServiceCatalogOrderClient orderClient = getServiceCatalogOrderClient(logPrefix);
            orderClient.setReadTimeout(orderTimeoutMillis);
            result = orderClient.createOrder(createOrderRequest);

            if (StringUtils.isEmpty(result.getOrderId())) {
                LOGGER.error(logPrefix + "create order failed, order id is null");
                throw new CommonExceptions.InternalServerErrorException();
            }
        } catch (Exception e) {
            LOGGER.error(logPrefix + "revoke billing interface failed, exception is {}", e);
            throw e;
        }
        return result;
    }

    /**
     * 创建访问订单的Client
     *
     * @param prefix
     * @return
     */
    protected ServiceCatalogOrderClient getServiceCatalogOrderClient(String prefix) {
        ServiceCatalogOrderClient serviceCatalogOrderClient = null;
        String accountID = getAccountId();
        if (ResourceAccountSetting.isUnifiedCharge()) {
            accountID = ResourceAccountSetting.getResourceAccountId();
        }
        try {
            serviceCatalogOrderClient = logicPodClientFactory.createServiceCatalogOrderClient(accountID);
        } catch (Exception e) {
            LOGGER.warn(prefix + "create serviceCatalogOrderClient failed, exception is {}", e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        if (serviceCatalogOrderClient == null) {
            LOGGER.info(prefix + "serviceCatalogOrderClient is null");
            throw new CommonExceptions.InternalServerErrorException();
        }
        return serviceCatalogOrderClient;
    }

    // 状态机重构后的handleInstanceCreateBci方法
    // 代码来源:拷贝原来的handleInstanceCreateBci方法,并修改
    public List<CreateNewTypeOrderItem> handleInstanceCreateBci(
            ValidatedItem validatedItem,
            Map<String, PodPO> podId2PodPOMap,
            Map<String, List<ContainerPO>> podId2ContainerPOMap,
            Map<String, BciOrderExtra> podId2BciOrderExtraMap) {
        List<PodPO> pods = new ArrayList<>();
        List<ContainerPO> containers = new ArrayList<>();
        List<CreateNewTypeOrderItem> createNewTypeOrderItems = new ArrayList<>();
        PodPurchaseRequest podPurchaseRequest = validatedItem.getPodPurchaseRequest();
        // 这里要根据计费账号来判断实例cpt1模式
        String chargeAccountId = getAccountId();
        if (ResourceAccountSetting.isUnifiedCharge()) {
            chargeAccountId = ResourceAccountSetting.getResourceAccountId();
        }
        Boolean userCpt1 =
                cceClusterService.getCceUserMapByUserId(chargeAccountId).getCpt1();
        if (!userCpt1) {
            throw new PodExceptions.PodChargeModeNotSupported();
        }
        // 地域开启秒级计费且账号开启秒级计费才认为创建的pod支持秒级计费
        podPurchaseRequest.setSubProductType(regionCpt1 && userCpt1 ?
                CPT1_SUB_PRODUCT_TYPE : "");
        LOGGER.debug("BciPurchaseRequest {}", podPurchaseRequest);

        pods.addAll(generatePodAndContainerPO(validatedItem, podPurchaseRequest));
        for (int i = 0, n = pods.size(); i < n; i++) {
            podId2PodPOMap.put(pods.get(i).getPodId(), pods.get(i));
            List<ContainerPO> containerPOList = generateInstanceCreateContainerPO(podPurchaseRequest,
                    pods.get(i).getPodUuid());
            containers.addAll(containerPOList);
            podId2ContainerPOMap.put(pods.get(i).getPodId(), containerPOList);
        }

        OrderCreateRequest orderCreate = parseInstancePurchaseToOrder(podPurchaseRequest);
        int purchaseCount = podPurchaseRequest.getPurchaseNum();

        // 批量创建eip
        CreateEipsResponse createEipsResponse = null;
        int eipBandwidth = 0;
        boolean isNewEIP = true;
        if (validatedItem.getEipPurchaseRequest() != null) {
            // case 1. create new eip
            if (StringUtils.isEmpty(validatedItem.getEipPurchaseRequest().getEipIp())) {
                eipBandwidth = validatedItem.getEipPurchaseRequest().getBandwidthInMbps();
                CreateEipRequest createEipRequest = generateEipRequest(validatedItem.getEipPurchaseRequest());
                try {
                    LogicEipClient eipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
                    createEipsResponse = eipClient.createBatchEips(createEipRequest);
                    if (createEipsResponse == null || createEipsResponse.getEips().isEmpty()) {
                        LOGGER.error("create eip failed, request {} {}",
                                JsonUtil.toJSON(createEipRequest), "response is null");
                        throw new PodExceptions.CreateEipException("Create eip failed.");
                    }
                } catch (BceInternalResponseException e) {
                    throw new PodExceptions.CreateEipException(e.getMessage());
                } catch (Exception e) {
                    LOGGER.error("create eip failed, request {}, err {}", JsonUtil.toJSON(createEipRequest), e);
                    throw new PodExceptions.CreateEipException("Create eip failed.");
                }
                // case 2. use user-specified eip
            } else {
                isNewEIP = false;
                List<String> eips = new ArrayList<>();
                eips.add(validatedItem.getEipPurchaseRequest().getEipIp());
                createEipsResponse = new CreateEipsResponse();
                createEipsResponse.setEips(eips);
                eipBandwidth = validatedItem.getEipPurchaseRequest().getBandwidthInMbps();
            }
        }

        for (int i = 0; i < purchaseCount; i++) {
            String eipIp = "";
            if (createEipsResponse != null && i < createEipsResponse.getEips().size()) {
                eipIp = createEipsResponse.getEips().get(i);
            }
            Order.Item orderItem = orderCreate.getItems().iterator().next();
            CreateNewTypeOrderItem bciCreateNewTypeOrderItem = new CreateNewTypeOrderItem();
            bciCreateNewTypeOrderItem.setPaymentMethod(validatedItem.getBciPaymentModel());
            bciCreateNewTypeOrderItem.setPurchaseOrder(validatedItem.getBciPurchaseOrder());
            if (podPurchaseRequest.getProductType().toLowerCase().equals(podConfiguration.getBidProductType())
                    && podPurchaseRequest.getBidOption() != null) {
                bciCreateNewTypeOrderItem.setBidModel(podPurchaseRequest.getBidOption().getBidModel());
                bciCreateNewTypeOrderItem.setBidPrice(podPurchaseRequest.getBidOption().getBidPrice());
            }
            // bciCreateNewTypeOrderItem.setProductType(podPurchaseRequest.getProductType().toLowerCase());
            // 当前billing仅支持后付费
            bciCreateNewTypeOrderItem.setProductType("postpay");
            bciCreateNewTypeOrderItem.setServiceType(podPurchaseRequest.getServiceType().toUpperCase());
            bciCreateNewTypeOrderItem.setSubProductType(podPurchaseRequest.getSubProductType());
            // extra中携带了podID信息，导致订单多个item请求信息不一致，因此目前使用循环purchaseCount创建订单请求
            BciOrderExtra bciOrderExtra = generateOrderExtra(validatedItem, pods.get(i), i,
                    eipIp, eipBandwidth, isNewEIP);
//            bciCreateNewTypeOrderItem.setExtra(generateOrderExtraString(bciOrderExtra));
            podId2BciOrderExtraMap.put(pods.get(i).getPodId(), bciOrderExtra);
            Set<FlavorItem> flavorItems = BeanCopyUtil.copySetCollection(new HashSet<Object>(orderItem.getFlavor()));
            bciCreateNewTypeOrderItem.setFlavor(new LinkedHashSet<FlavorItem>(flavorItems));
            bciCreateNewTypeOrderItem.setKey(orderItem.getKey());
            bciCreateNewTypeOrderItem.setTime(orderItem.getTime());
            bciCreateNewTypeOrderItem.setTimeUnit(orderItem.getTimeUnit());
            bciCreateNewTypeOrderItem.setReleaseTime(orderCreate.getReleaseTime());
            // TODO: 需要验证(一个订单的多个item的key必须不一样)
            bciCreateNewTypeOrderItem.setKey(BCI_ORDER_ITEM_KEY_PREFIX + pods.get(i).getPodId());
            // 每个item对应一个pod
            bciCreateNewTypeOrderItem.setCount(1);
            createNewTypeOrderItems.add(bciCreateNewTypeOrderItem);
        }
        return createNewTypeOrderItems;
    }

    private BciOrderExtra generateOrderExtra(ValidatedItem validatedItem, PodPO pod, int i,
                                             String eipIp, int eipBandwidth, boolean isNewEIP) {
        PodPurchaseRequest podPurchaseRequest = validatedItem.getPodPurchaseRequest();
        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        bciOrderExtra.setContainers(podPurchaseRequest.getContainerPurchases());
        bciOrderExtra.setImageRegistrySecrets(podPurchaseRequest.getImageRegistrySecrets());
        bciOrderExtra.setVolume(podPurchaseRequest.getVolume());
        bciOrderExtra.setRestartPolicy(podPurchaseRequest.getRestartPolicy());
        bciOrderExtra.setTags(podPurchaseRequest.getTags());
        if (StringUtils.isEmpty(podPurchaseRequest.getZoneSubnets())) {
            // 可用区信息
            bciOrderExtra.setLogicalZone(podPurchaseRequest.getLogicalZone());
            bciOrderExtra.setZoneId(podPurchaseRequest.getZoneId());
            bciOrderExtra.setPhysicalZone(podPurchaseRequest.getEncryptedPhysicalZone());
            // 子网信息
            bciOrderExtra.setSubnetUuid(podPurchaseRequest.getSubnetUuid());
            bciOrderExtra.setSubnetShortId(podPurchaseRequest.getSubnetShortId());
            bciOrderExtra.setSubnetCidr(podPurchaseRequest.getSubnetCidr());
        } else {
            bciOrderExtra.setZoneSubnets(podPurchaseRequest.getZoneSubnets());
        }
        bciOrderExtra.setName(podPurchaseRequest.getName() + "-" + Integer.toString(i + 1));
        bciOrderExtra.setAnnotations(podPurchaseRequest.getAnnotations());
        bciOrderExtra.setLabels(podPurchaseRequest.getLabels());
        bciOrderExtra.setEnableLog(podPurchaseRequest.isEnableLog());
        String groupId = podPurchaseRequest.getSecurityGroupId();
        // securityGroupId BCI 2.0 未使用，多安全组只在 BCI 2.0 支持；

        List<String> securityGroups = Arrays.asList(groupId.trim().split(","));
        if (podValidator.isNormalSecurityGroupId(securityGroups)) {
            if (groupId.split(",").length == 1) {
                if (!isDefaultSecurityGroupId(groupId)) {
                    List<SimpleSecurityGroupVO> securityGroupVO = getSecurityGroup(groupId);
                    groupId = securityGroupVO.get(0).getUuid();
                }
            }
        }
        bciOrderExtra.setSecurityGroupId(groupId);
        bciOrderExtra.setSecurityGroupShortId(podPurchaseRequest.getSecurityGroupId());
        bciOrderExtra.setVpcId(podPurchaseRequest.getVpcId());
        bciOrderExtra.setVpcCidr(podPurchaseRequest.getVpcCidr());
        if (StringUtils.isNotEmpty(eipIp)) {
            String eipState = "new_create";
            pod.setPublicIp(eipIp);
            pod.setBandwidthInMbps(eipBandwidth);
            if (!isNewEIP) {
                pod.setEipStatus(EipConstant.STATUS_USER_SPECIFIED);
                eipState = "user_specified";
            }
            EipBciState eip = new EipBciState();
            eip.setEip(eipIp);
            eip.setState(eipState);
            bciOrderExtra.setEipPurchaseRequest(JsonUtil.toJSON(eip));
        }

        bciOrderExtra.setExtraAccountID("");
        bciOrderExtra.setChargeSource("");
        bciOrderExtra.setPodId(pod.getPodId());
        bciOrderExtra.setUserId(getAccountId());
        bciOrderExtra.setPodCpu(podPurchaseRequest.getCpu());
        bciOrderExtra.setPodMem(podPurchaseRequest.getMemory());
        bciOrderExtra.setCpuType(podPurchaseRequest.getCpuType());
        bciOrderExtra.setPodGpuType(podPurchaseRequest.getGpuType());
        bciOrderExtra.setPodGpuCount(podPurchaseRequest.getGpuCount());
        if (podPurchaseRequest.getAutoMatchImageCache() != null) {
            bciOrderExtra.setAutoMatchImageCache(podPurchaseRequest.getAutoMatchImageCache());
        } else {
            bciOrderExtra.setAutoMatchImageCache(true);
        }

        // 判断此用户是否关闭镜像缓存功能
        String accountId = getAccountId();
        List<String> bciImageCacheDisabledUsersList =
                Arrays.asList(bciImageCacheDisabledUsers.split(","));
        if (bciImageCacheDisabledUsersList.contains(accountId)) {
            bciOrderExtra.setAutoMatchImageCache(false);
        }
        // 如果是统一计费，需要在extra中设置用户的accountID、chargeSource
        if (ResourceAccountSetting.isUnifiedCharge()) {
            bciOrderExtra.setExtraAccountID(getAccountId());
            bciOrderExtra.setChargeSource(ResourceAccountSetting.getApplication().toLowerCase());
        }
        checkOrderLabels(bciOrderExtra);
        bciOrderExtra.setV3(isV3AccountId().isIsv3());
        // 新增 pod 短ID label
        Label label = new Label();
        label.setLabelKey(LogicalConstant.LABEL_POD_ID);
        label.setLabelValue(pod.getPodId());
        bciOrderExtra.getLabels().add(label);

        PodExtra podExtra = PodExtra.fromPodPurchaseRequest(podPurchaseRequest);
        bciOrderExtra.setPodExtra(podExtra);
        bciOrderExtra.setTidal(podPurchaseRequest.isTidal());
        bciOrderExtra.setEnableIPv6(podPurchaseRequest.isEnableIPv6());
        bciOrderExtra.setScheduleInPfsPool(podPurchaseRequest.isScheduleInPfsPool());
        bciOrderExtra.setClientToken(pod.getClientToken());
        bciOrderExtra.setMaxPendingMinute(bciPodMaxPendingMinute);
        Map<String, String> podAnnotations = generatePodAnnotations(podPurchaseRequest);
        bciOrderExtra.setAnnotationsMap(podAnnotations);

        // 设置pod extra中的max-pending-minute
        Object maxPendingMinuteObj = validatedItem.getAnnotationsValueByKey(PodConstants.BCI_MAX_PENDING_MINUTE_ANNOTATION_KEY);
        if (maxPendingMinuteObj != null) {
            int maxPendingMinute = Integer.parseInt(maxPendingMinuteObj.toString());
            if (maxPendingMinute < PodConstants.BCI_MAX_PENDING_MINUTE_MIN_VALUE) {
                maxPendingMinute = PodConstants.BCI_MAX_PENDING_MINUTE_MIN_VALUE;
            }
            if (maxPendingMinute > PodConstants.BCI_MAX_PENDING_MINUTE_MAX_VALUE) {
                maxPendingMinute = PodConstants.BCI_MAX_PENDING_MINUTE_MAX_VALUE;
            }
            bciOrderExtra.setMaxPendingMinute(maxPendingMinute);
        }

        // 设置Pod extra中的bci-resource-ignore-containers
        Object podResourceIgnoreContainersObj =
                validatedItem.getAnnotationsValueByKey(PodConstants.BCI_RESOURCE_IGNORE_CONTAINERS_ANNOTATION_KEY);
        if (podResourceIgnoreContainersObj != null) {
            bciOrderExtra.setPodResourceIgnoreContainers(podResourceIgnoreContainersObj.toString());
        }

        // 设置pod extra中的bci-ignore-exit-code-containers
        Object podIgnoreExitCodeContainersObj =
                validatedItem.getAnnotationsValueByKey(PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY);
        if (podIgnoreExitCodeContainersObj != null) {
            bciOrderExtra.setPodIgnoreExitCodeContainers(podIgnoreExitCodeContainersObj.toString());
        }

        // 设置pod extra中的bci-ignore-not-ready-containers
        Object podIgnoreNotReadyContainersObj =
                validatedItem.getAnnotationsValueByKey(PodConstants.BCI_IGNORE_NOT_READY_CONTAINERS_ANNOTATION_KEY);
        if (podIgnoreNotReadyContainersObj != null) {
            bciOrderExtra.setPodIgnoreNotReadyContainers(podIgnoreNotReadyContainersObj.toString());
        }
            
        // 设置pod extra中的bci-fail-strategy
        Object podFailStrategyObj =
                validatedItem.getAnnotationsValueByKey(PodConstants.BCI_FAIL_STRATEGY_ANNOTATION_KEY);
        if (podFailStrategyObj != null) {
            String podFailStrategyString = podFailStrategyObj.toString();
            if (StringUtils.isNotEmpty(podFailStrategyString) && BciFailStrategyConstant.isValid(podFailStrategyString)) {
                bciOrderExtra.setPodFailStrategy(podFailStrategyString);
            }
        }
        return bciOrderExtra;
    }

    public Map<String, String> generatePodAnnotations(PodPurchaseRequest podPurchaseRequest) {
        Map<String, String> podAnnotations = new HashMap<>();
        List<Label> labels = podPurchaseRequest.getMetadataLabels();
        if (labels == null || CollectionUtils.isEmpty(labels)) {
            return podAnnotations;
        }
        for (Label label : labels) {
            if (label.getLabelKey().equals(BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY)) {
                podAnnotations.put(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAME_KEY, label.getLabelValue());
            }
            if (label.getLabelKey().equals(BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAMESPACE_KEY)) {
                podAnnotations.put(BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAMESPACE_KEY, label.getLabelValue());
            }
        }
        return podAnnotations;
    }

    private String generateOrderExtraString(BciOrderExtra bciOrderExtra) {
        return commonUtils.generateOrderExtraString(bciOrderExtra);
    }

    public List<SimpleSecurityGroupVO> getSecurityGroup(String securityGroupIdString) {
        List<String> securityGroups = Arrays.asList(securityGroupIdString.trim().split(","));
        List<SimpleSecurityGroupVO> securityGroupVOs = new ArrayList<>();
        for (String securityGroupId : securityGroups) {
            // 当查询多个安全组时，无效的安全组不会报错，所以需要逐个查询查询
            List<String> securityGroupIds = Arrays.asList(securityGroupId);
            SecurityGroupSimpleInstancesVO securityGroupSimpleInstancesVO = this.getSecurityGroupFromCache(securityGroupIds);
            if (securityGroupSimpleInstancesVO == null
                    || CollectionUtils.isEmpty(securityGroupSimpleInstancesVO.getList())) {
                throw new PodExceptions.SecurityGroupInvalidException(String.format(
                        "The securityGroup [%s] is not found.", securityGroupId));
            }
            for (SimpleSecurityGroupVO securityGroupVO : securityGroupSimpleInstancesVO.getList()) {
                if (securityGroupId.equalsIgnoreCase(securityGroupVO.getSecurityGroupId())
                        || securityGroupId.equalsIgnoreCase(securityGroupVO.getUuid())) {
                    securityGroupVOs.add(securityGroupVO);
                }
            }
        }
        if (securityGroupVOs.size() > 0) {
            return securityGroupVOs;
        }
        throw new PodExceptions.SecurityGroupInvalidException(String.format(
                "Get security group %s failed.", securityGroupIdString));
    }

    private SecurityGroupSimpleInstancesVO getSecurityGroupFromCache(List<String> securityGroupIds) {
        SecurityGroupSimpleInstancesVO securityGroupSimpleInstancesVO = securityGroupSimpleInstancesCache.get(securityGroupIds);
        if (securityGroupSimpleInstancesVO != null) {
            return securityGroupSimpleInstancesVO;
        }
        SecurityGroupClient securityGroupClient = logicPodClientFactory.createSecurityGroupClient(getAccountId());
        securityGroupSimpleInstancesVO = securityGroupClient
                .getSimpleSecurityGroupListByIds(securityGroupIds);
        securityGroupSimpleInstancesCache.put(
                securityGroupIds,
                securityGroupSimpleInstancesVO);
        return securityGroupSimpleInstancesVO;
    }

    private boolean isDefaultSecurityGroupId(String sgId) {
        return org.apache.commons.lang.StringUtils.isEmpty(sgId) || "default".equals(sgId);
    }

    private boolean isEnterpriseSecurityGroupId(String sgId) {
        return sgId.startsWith("esg-");
    }

    private boolean isNormalGroupId(String sgId) {
        return sgId.startsWith("g-");
    }

    private OrderCreateRequest parseInstancePurchaseToOrder(PodPurchaseRequest podPurchaseRequest) {
        LOGGER.debug("create bci, parameters is {}", podPurchaseRequest);

        OrderCreateRequest orderCreateRequest = new OrderCreateRequest();
        orderCreateRequest.setOrderUuid(podPurchaseRequest.getOrderUuid());

        encapsulateOrderBasicInfo(podPurchaseRequest.getProductType(), orderCreateRequest);
        encapsulateOrderItems(podPurchaseRequest, orderCreateRequest);

        String accountID = getAccountId();

        orderCreateRequest.setUserId(accountID);
        orderCreateRequest.setAccountId(accountID);

        return orderCreateRequest;
    }

    private void encapsulateOrderBasicInfo(String productType, OrderCreateRequest orderCreateRequest) {
        if (Payment.isPostpay(productType)) {
            orderCreateRequest = orderCreateRequest.withNeedConfirm(false);
        }

        orderCreateRequest.setUserId(getAccountId());
        orderCreateRequest.setAccountId(getAccountId());
        orderCreateRequest.setServiceType(PodConstants.SERVICE_TYPE);
        orderCreateRequest.setProductType(productType);
    }

    private void encapsulateOrderItems(PodPurchaseRequest podPurchaseRequest,
                                       OrderCreateRequest orderCreateRequest) {
        List<Order.Item> items = new ArrayList<>();
        int purchaseNum = podPurchaseRequest.getPurchaseNum();
        for (int i = 0; i < purchaseNum; i++) {
            Order.Item item = new Order.Item();
            item.setRegion(regionConfiguration.getCurrentRegion());
            item.setCount(1);
            item.setKey(BCI_ORDER_KEY);
            item.setFlavor(generateOrderFlavor(podPurchaseRequest));
            items.add(item);
        }

        orderCreateRequest.setItems(items);
    }

    private Flavor generateOrderFlavor(PodPurchaseRequest podPurchaseRequest) {
        float vcpu = podPurchaseRequest.getCpu();
        float memory = podPurchaseRequest.getMemory();
        String gpuType = podPurchaseRequest.getGpuType();
        float gpuCount = podPurchaseRequest.getGpuCount();
        float cdsPurchaseSize = podPurchaseRequest.getCdsPurchaseSize();
        String physicalZone = podPurchaseRequest.getEncryptedPhysicalZone();

        Flavor flavor = new Flavor();

        if (StringUtils.isEmpty(gpuType)) {
            if (podPurchaseRequest.getSubProductType().equals(CPT1_SUB_PRODUCT_TYPE)) {
                // 如果 CPT1 & 线性计费，将真实cpu和内存量写入 value 值，以便预留实例券抵扣
                Flavor.FlavorItem item0 = new Flavor.FlavorItem();
                item0.setName(LogicalConstant.CPU);
                item0.setValue(String.valueOf(vcpu));
                item0.setScale(new BigDecimal(1));
                flavor.add(item0);

                Flavor.FlavorItem item1 = new Flavor.FlavorItem();
                item1.setName(LogicalConstant.MEMORY);
                item1.setValue(String.valueOf(memory));
                item1.setScale(new BigDecimal(1));
                flavor.add(item1);
            } else {
                Flavor.FlavorItem item0 = new Flavor.FlavorItem();
                item0.setName(LogicalConstant.CPU);
                item0.setValue("1");
                item0.setScale(new BigDecimal(String.valueOf(vcpu)));
                flavor.add(item0);

                Flavor.FlavorItem item1 = new Flavor.FlavorItem();
                item1.setName(LogicalConstant.MEMORY);
                item1.setValue("1");
                item1.setScale(new BigDecimal(String.valueOf(memory)));
                flavor.add(item1);
            }


            if (cdsPurchaseSize > 0) {
                Flavor.FlavorItem item2 = new Flavor.FlavorItem();
                item2.setName(LogicalConstant.DISK);
                item2.setValue("1");
                item2.setScale(new BigDecimal(String.valueOf(cdsPurchaseSize)));
                flavor.add(item2);
            }
        } else {
            String subValue = LogicalConstant.GPU_NAME;
            String name = LogicalConstant.GPU;
            if (LogicalConstant.NPU_TYPE.equals(gpuType)) {
                subValue = LogicalConstant.NPU_NAME;
                name = LogicalConstant.NPU;
            }
            String gpuBCCSpec = String.format("%s|%s|%s|%s", gpuType, (int) gpuCount, (int) vcpu, (int) memory);
            String bccSpec = podConfiguration.getGpuBCCSpecMap().get(gpuBCCSpec);

            Flavor.FlavorItem item0 = new Flavor.FlavorItem();
            item0.setName("subServiceType");
            item0.setValue(subValue);
            item0.setScale(new BigDecimal("1"));
            flavor.add(item0);

            Flavor.FlavorItem item1 = new Flavor.FlavorItem();
            item1.setName(name);
            item1.setValue(bccSpec);
            item1.setScale(new BigDecimal("1"));
            flavor.add(item1);

            String specWhiteListStr = podConfiguration.getBillingGpuSpecZoneEnabledWhiteList();
            Set<String> billingZoneEnableSpecSet = new HashSet<>(Arrays.asList(specWhiteListStr.split(",")));

            if (billingZoneEnableSpecSet.contains(bccSpec)) {
                Flavor.FlavorItem item2 = new Flavor.FlavorItem();
                item2.setName(LogicalConstant.BILLING_PHYSICAL_ZONE);

                item2.setValue(physicalZone);
                item2.setScale(new BigDecimal("1"));
                flavor.add(item2);
            }
        }

        return flavor;
    }

    private List<PodPO> generatePodAndContainerPO(ValidatedItem validatedItem, PodPurchaseRequest podPurchaseRequest) {
        List<PodPO> podPOS = new ArrayList<>();
        for (int i = 0, n = podPurchaseRequest.getPurchaseNum(); i < n; i++) {
            PodPO podPO = initPodPO(validatedItem, podPurchaseRequest, i);
            podPOS.add(podPO);
        }
        return podPOS;
    }

    public ContainerPO initContainerPO(String podUuid, ContainerPurchase containerPurchase) {
        if (StringUtils.isNotEmpty(containerPurchase.getName()) &&
                !PodUtils.validaterName(containerPurchase.getName())) {
            LOGGER.error("NameInvalidException,the container name {} is invalidate", containerPurchase.getName());
            throw new PodExceptions.NameInvalidException("container name " +
                    containerPurchase.getName() + " is invalid");
        }
        ContainerPO containerPO = new ContainerPO();
        containerPO.setPodUuid(podUuid);
        containerPO.setName(containerPurchase.getName());
        containerPO.setArgs(containerPurchase.getArgs() == null ? "" : JsonUtil.toJSON(
                containerPurchase.getArgs()));
        containerPO.setCommands(containerPurchase.getCommands() == null ? "" : JsonUtil.toJSON(
                containerPurchase.getCommands()));
        containerPO.setCpu(containerPurchase.getCpu());
        containerPO.setImageName(containerPurchase.getImageName());
        containerPO.setImageVersion(containerPurchase.getImageVersion());
        containerPO.setImageAddress(containerPurchase.getImageAddress());
        containerPO.setMemory(containerPurchase.getMemory());
        containerPO.setGpuType(containerPurchase.getGpuType());
        containerPO.setGpuCount(containerPurchase.getGpuCount());
        if (CollectionUtils.isEmpty(containerPurchase.getEnvs())) {
            containerPO.setEnvs("");
        } else {
            // 为value设置默认值为 ""
            for (Environment env : containerPurchase.getEnvs()) {
                if (env.getValue() == null) {
                    env.setValue("");
                }
                if (env.getValueFrom() != null && env.getValueFrom().getFieldRef() != null) {
                    if (env.getValueFrom().getFieldRef().getApiVersion() == null) {
                        env.getValueFrom().getFieldRef().setApiVersion("");
                    }
                }
            }
            containerPO.setEnvs(JsonUtil.toJSON(containerPurchase.getEnvs()));
        }
        containerPO.setImagePullPolicy(containerPurchase.getImagePullPolicy());
        containerPO.setUserId(getAccountId());
        containerPO.setPorts(containerPurchase.getPorts() == null ? "" :
                JsonUtil.toJSON(containerPurchase.getPorts()));
        containerPO.setVolumeMounts(containerPurchase.getVolumeMounts() == null ? "" :
                JsonUtil.toJSON(containerPurchase.getVolumeMounts()));
        containerPO.setWorkingDir(containerPurchase.getWorkingDir());
        containerPO.setContainerType(containerPurchase.getContainerType());
        // 新增字段
        containerPO.setLivenessProbe(containerPurchase.getLivenessProbe() == null ? ""
                : JsonUtil.toJSON(containerPurchase.getLivenessProbe()));
        containerPO.setReadinessProbe(containerPurchase.getReadinessProbe() == null ? ""
                : JsonUtil.toJSON(containerPurchase.getReadinessProbe()));
        containerPO.setStartupProbe(containerPurchase.getStartupProbe() == null ? ""
                : JsonUtil.toJSON(containerPurchase.getStartupProbe()));
        containerPO.setLifecycle(containerPurchase.getLifecycle() == null ? ""
                : JsonUtil.toJSON(containerPurchase.getLifecycle()));
        containerPO.setStdin(containerPurchase.isStdin());
        containerPO.setStdinOnce(containerPurchase.isStdinOnce());
        containerPO.setTty(containerPurchase.isTty());
        containerPO.setSecurityContext(containerPurchase.getSecurityContext() == null ? ""
                : JsonUtil.toJSON(containerPurchase.getSecurityContext()));
        return containerPO;
    }

    private List<ContainerPO> generateInstanceCreateContainerPO(PodPurchaseRequest podPurchaseRequest,
                                                                String podId) {
        List<ContainerPO> containers = new ArrayList<>();
        for (ContainerPurchase containerPurchase : podPurchaseRequest.getContainerPurchases()) {
            podValidator.validateMountVolumeType(containerPurchase, podPurchaseRequest.getVolume());
            containers.add(initContainerPO(podId, containerPurchase));
        }
        return containers;
    }

    private PodPO initPodPO(ValidatedItem validatedItem, PodPurchaseRequest podPurchaseRequest, int i) {
        String shortId =
                commonUtils.createExternalId(LogicalConstant.API_POD_PREFIX);
        Volume volume = podPurchaseRequest.getVolume();

        PodPO podPO = new PodPO();
        podPO.setCceUuid(podPurchaseRequest.getCceId());
        podPO.setMemory(podPurchaseRequest.getMemory());
        podPO.setvCpu(podPurchaseRequest.getCpu());
        if (podPurchaseRequest.getCpuType() == null) {
            podPO.setCpuType("");
        } else {
            podPO.setCpuType(podPurchaseRequest.getCpuType().toLowerCase());
        }
        podPO.setGpuType(podPurchaseRequest.getGpuType());
        podPO.setGpuCount(podPurchaseRequest.getGpuCount());
        podPO.setRestartPolicy(podPurchaseRequest.getRestartPolicy());
        int podIndex = i + 1;
        podPO.setName(podPurchaseRequest.getName() + "-" + Integer.toString(podIndex));
        podPO.setPodId(shortId);
        podPO.setPodUuid(shortId);
        podPO.setPodIndex(podIndex);
        // 单个创建时,clientToken设置为请求中的clientToken
        String clientToken = StringUtils.isNotEmpty(podPurchaseRequest.getClientToken()) ?
                podPurchaseRequest.getClientToken() : validatedItem.getClientToken();
        if (podPurchaseRequest.getPurchaseNum() <= 1) {
            // 如果请求中的clientToken为空,则设置为pod的短ID
            if (StringUtils.isEmpty(clientToken)) {
                clientToken = shortId;
            }
            podPO.setClientToken(clientToken);
        } else {
            // 批量创建时,clientToken设置为pod的短ID
            clientToken = shortId;
            podPO.setClientToken(clientToken);
        }
        podPO.setStatus(BciStatus.PENDING.getStatus());
        podPO.setInternalStatus(PodConstants.BCI_INTERNAL_STATUS_UNSYNC);
        podPO.setDelayReleaseDurationMinute(podPurchaseRequest.getDelayReleaseDurationMinute());
        podPO.setDelayReleaseSucceeded(podPurchaseRequest.isDelayReleaseSucceeded());
        podPO.setConfigFile(JsonUtil.toJSON(volume.getConfigFile()));
        podPO.setNfs(JsonUtil.toJSON(volume.getNfs()));
        podPO.setEmptyDir(JsonUtil.toJSON(volume.getEmptyDir()));
        podPO.setFlexVolume(JsonUtil.toJSON(volume.getFlexVolume()));
        podPO.setPfs(JsonUtil.toJSON(volume.getPfs()));
        podPO.setBos(JsonUtil.toJSON(volume.getBos()));
        podPO.setHostPath(JsonUtil.toJSON(volume.getHostPath()));
        podPO.setPodVolumes(JsonUtil.toJSON(volume.getPodVolumes()));
        podPO.setCephFS(JsonUtil.toJSON(volume.getCephfs()));
        podPO.setTags(JsonUtil.toJSON(podPurchaseRequest.getLabels()));
        podPO.setUserId(getAccountId());
        podPO.setSecurityGroupUuid(podPurchaseRequest.getSecurityGroupId());
        // 写入用户多可用区信息
        if (StringUtils.isEmpty(podPurchaseRequest.getZoneSubnets())) {
            podPO.setSubnetUuid(podPurchaseRequest.getSubnetUuid());
            podPO.setZoneId(podPurchaseRequest.getZoneId());
            podPO.setLogicalZone(podPurchaseRequest.getLogicalZone());
        } else {
            podPO.setZoneSubnets(podPurchaseRequest.getZoneSubnets());
        }
        podPO.setProductType(podPurchaseRequest.getProductType());
        // podPO.setCreatedTime(new Timestamp(new Date().getTime()));
        // podPO.setUpdatedTime(new Timestamp(new Date().getTime()));
        podPO.setApplication(podPurchaseRequest.getApplication());
        podPO.setEnableLog(podPurchaseRequest.isEnableLog() ? 1 : 0);
        // 统一计费
        if (ResourceAccountSetting.isUnifiedCharge()) {
            podPO.setChargeSource(ResourceAccountSetting.getApplication().toLowerCase());
            podPO.setChargeAccountId(ResourceAccountSetting.getResourceAccountId());
        } else {
            podPO.setChargeSource(LogicalConstant.CHARGE_SOURCE_USER);
        }
        podPO.setDeleted(0);
        if (podPurchaseRequest.getSubProductType().equals(CPT1_SUB_PRODUCT_TYPE)) {
            podPO.setCpt1(true);
        } else {
            podPO.setCpt1(false);
        }
        PodExtra podExtra = PodExtra.fromPodPurchaseRequest(podPurchaseRequest);
        podPO.setExtra(JsonUtil.toJSON(podExtra));
        podPO.setTidal(podPurchaseRequest.isTidal());
        List<PodCondition> conditions = new ArrayList<>();
        // 判断是否发生了资源向上取整
        if (validatedItem.getRoundUpFlag()) {
            Map<String, Float> roundUpResult = validatedItem.getRoundUpResult();
            PodCondition autoMatchCondition = new PodCondition();
            autoMatchCondition.setMessage(String.format(
                    "The best matched instanceType for current instance is %sc %sGi",
                    roundUpResult.get("cpu"),
                    roundUpResult.get("memory")));
            autoMatchCondition.setType("AutoInstanceTypeMatch");
            long currentTimeMillis = System.currentTimeMillis();
            autoMatchCondition.setLastTransitionTime(new Date(currentTimeMillis));
            autoMatchCondition.setStatus("True");
            conditions.add(autoMatchCondition);
        }
        PodCondition creatingCondition = new PodCondition();
        creatingCondition.setType("Creating");
        creatingCondition.setStatus("True");
        long currentTimeMillis = System.currentTimeMillis();
        creatingCondition.setLastTransitionTime(new Date(currentTimeMillis));
        conditions.add(creatingCondition);
        podPO.setConditions(JsonUtil.toJSON(conditions));

        return podPO;
    }

    private void base64Encode(List<ConfigFileDetail> configFileDetails) {
        if (CollectionUtils.isEmpty(configFileDetails)) {
            return;
        }
        for (ConfigFileDetail detail : configFileDetails) {
            detail.setFile(Base64.encodeBase64String(detail.getFile().getBytes()));
        }
    }

    private List<String> wrapEipForPod(List<PodPO> podPOS) {
        List<String> subnetIds = new ArrayList<>();
        try {
//            Map<String, EipInstance> eipInstanceMap = new HashMap<>();
//            bciAsyncService.getEipInstanceMapAsync();
//            eipInstanceMap = (Map<String, EipInstance>) asyncExecutorService.getAsyncResult(
//                    WorkKeyUtil.genWorkKey("getEipInstanceMapAsync", new LinkedList<Object>()));

            for (PodPO podPO : podPOS) {
                podPO.setRegion(regionConfiguration.getCurrentRegion());
                if (StringUtils.isNotEmpty(podPO.getSubnetUuid())) {
                    subnetIds.add(podPO.getSubnetUuid());
                }
//                if (eipInstanceMap.containsKey(podPO.getPodUuid())) {
//                    podPO.setEipUuid(eipInstanceMap.get(podPO.getPodUuid()).getEipId());
//                    podPO.setPublicIp(eipInstanceMap.get(podPO.getPodUuid()).getEip());
//                    podPO.setEipGroupId(eipInstanceMap.get(podPO.getPodUuid()).getShareGroupId());
//                    podPO.setBandwidthInMbps(eipInstanceMap.get(podPO.getPodUuid()).getBandwidthInMbps());
//                }
            }
        } catch (Exception e) {
            // 服务降级
            LOGGER.error("unknow exception!", e);
        }
        return subnetIds;
    }

    private void wrapNetworkForPod(List<PodPO> podPOS) {
        try {
//            List<String> subnetIds = wrapEipForPod(podPOS);
//            SubnetMapResponse subnetMapResponse = new SubnetMapResponse();
//            if (!subnetIds.isEmpty()) {
//                SubnetMapRequest subnetMapRequest = new SubnetMapRequest();
//                subnetMapRequest.setAttachVpc(true);
//                subnetMapRequest.setSubnetIds(subnetIds);
//                subnetMapResponse = logicPodClientFactory.createExternalSubnetClient(
//                        getAccountId()).findSubnetMap(subnetMapRequest);
//            }
//            Map<String, SubnetVo> subnetMap = subnetMapResponse.getSubnetMap();
            for (PodPO podPO : podPOS) {
//                if (subnetMap.containsKey(podPO.getSubnetUuid())) {
//                    SubnetVo subnetVo = subnetMap.get(podPO.getSubnetUuid());
//                    podPO.setSubnetUuid(subnetVo.getSubnetUuid());
//                    podPO.setSubnetType(SubnetVo.SubnetType.findById(subnetVo.getSubnetType()).getName());
//                } else {
//                    LOGGER.debug("can not query subnetVo: subnetUUid = " + podPO.getSubnetUuid());
//                }
                // get eip info
                if (StringUtils.isNotEmpty(podPO.getPublicIp())) {
                    // eip status: 0 using, 1 deleted, 2 unbind, 3 user-specified, 4 user-specified unbind
                    if (podPO.getEipStatus() != EipConstant.STATUS_INIT &&
                            podPO.getEipStatus() != EipConstant.STATUS_USER_SPECIFIED) {
                        podPO.setPublicIp("");
                    }
                }
                if (!APPLICATION_DEFAULT.equals(podPO.getApplication())) { // 转换application，内部用户返回"inner"
                    podPO.setApplication(APPLICATION_INNER);
                }
                podPO.setPushLog(podPO.getEnableLog() == 1);
            }
        } catch (Exception e) {
            // 服务降级
            LOGGER.error("unknow exception:{}", e);
        }
    }

    private DockerHubImageResponse getImages(int pageNo, int pageSize) {
        DockerHubClient dockerHubrClient = logicPodClientFactory.createDockerHubrClient();
        DockerHubImageResponse dockerImage = new DockerHubImageResponse();
        try {
            dockerImage = dockerHubrClient.listDockerHubImage(pageNo, pageSize);
        } catch (Exception e) {
            LOGGER.debug("Fail to list docker hub image {}", e);
        }
        return dockerImage;
    }

    /**
     * 2019-11-05 减少日志输出
     *
     * @param podListRequest
     * @return
     */
    public LogicPageResultResponse<PodEventPO> listPodEventsWithPageByMultiKey(PodListRequest podListRequest) {
        PodListModel podListModel = PodUtils.convertRequestModel(podListRequest, PodListModel.class);
        LogicPageResultResponse<PodEventPO> resultResponse = PodUtils.initEdpPageResultResponse(podListRequest);

        BcmListEventsRequest request = consBcmListEventsRequest(podListModel.getPageNo(), podListModel.getPageSize(),
                Integer.parseInt(podListModel.getFilterMap().get("day")), podListModel.getFilterMap().get("podId"));
        BcmClient bcmClient = logicPodClientFactory.createBcmClient(getAccountId());
        BcmListEventsResponse bcmListEventsResponse = bcmClient.listEvents(request);
        ArrayList<PodEventPO> podEventPOList = new ArrayList<>();

        if (bcmListEventsResponse != null && CollectionUtils.isNotEmpty(bcmListEventsResponse.getContent())) {
            List<BcmListEventsResponse.BcmEvent> eventList = bcmListEventsResponse.getContent();
            for (BcmListEventsResponse.BcmEvent event : eventList) {
                PodEventPO podEventPO = convertBcmEventToEventPo(event);
                podEventPOList.add(podEventPO);
            }
        }
        // 排序
        boolean flag = true;
        if ("asc".equalsIgnoreCase(podListModel.getOrders().get(0).getOrder())) {
            flag = false;
        }
        PodUtils.quickSort(podEventPOList, 0, podEventPOList.size() - 1, flag);

        resultResponse.setResult(podEventPOList);
        int totalCount = 0;
        if (bcmListEventsResponse != null) {
            totalCount = bcmListEventsResponse.getTotalElements();
        }
        resultResponse.setTotalCount(totalCount);
        LOGGER.info("podEventPOList data:{}, size:{}", podEventPOList, podEventPOList.size());

        return resultResponse;
    }

    public QueryEventsRequest consQueryEventsRequest(int pageNo, int pageSize, int day) {
        QueryEventsRequest request = new QueryEventsRequest();
        request.setDomainId(getAccountId());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize > 10000 ? pageSize : 10000);
        Date end = new Date();
        Date start = new Date();
        long timestamp = System.currentTimeMillis() - day * 24 * 60 * 60 * 1000L;
        start.setTime(timestamp);
        request.setStartTime(start);
        request.setEndTime(end);

        return request;
    }

    public BcmListEventsRequest consBcmListEventsRequest(int pageNo, int pageSize, int day, String resourceId) {
        BcmListEventsRequest request = new BcmListEventsRequest();
        request.setAccountId(getAccountId());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setResourceId(resourceId);
        Date end = new Date();
        Date start = new Date();
        long timestamp = System.currentTimeMillis() - day * 24 * 60 * 60 * 1000L;
        start.setTime(timestamp);
        end.setTime(System.currentTimeMillis());
        request.setStartTime(start);
        request.setEndTime(end);

        return request;
    }

    private PodEventPO convertEventPo(Event event) {
        PodEventPO eventPO = new PodEventPO();
        eventPO.setEventName(event.getEventName());
        eventPO.setEventType(event.getEventDetail().getAdditionalEventData().get("bciEventType"));
        eventPO.setDescription(event.getDescription());
        eventPO.setEventTime(event.getEventTime());

        return eventPO;
    }

    public PodEventPO convertBcmEventToEventPo(BcmListEventsResponse.BcmEvent event) {
        PodEventPO eventPO = new PodEventPO();
        eventPO.setEventName(event.getEventAlias());
        BcmListEventsResponse.BcmEventContent content = JsonUtil.fromJSON(event.getContent(),
                BcmListEventsResponse.BcmEventContent.class);
        eventPO.setDescription(content.getMessage());
        eventPO.setEventType(content.getInfo());
        eventPO.setEventTime(event.getTimestamp());

        return eventPO;
    }
//    public String getWebshellUrl(WebShell webshell) {
//        String podId = webshell.getPodId();
//        // 如果是内部产品，封禁webshell，抛出异常
//        PodPO podPO = getPodPOByPodId(podId);
//        if (podPO != null && !APPLICATION_DEFAULT.equals(podPO.getApplication())) {
//            throw new PodExceptions.OperationNotAvailable();
//        }
//        Boolean tty = webshell.getTty();
//        Boolean stdin = webshell.getStdin();
//        String containerName = webshell.getContainerName();
//        List<String> command = webshell.getCommand();
//        if (!StringUtil.isEmpty(podId) && podId.startsWith("p-")) {
//            podId = podDao.queryPodUuid(podId); // 短id 转化为长ID
//        }
//        String containerId = containerDao.queryContainerUuid(podId, containerName);
//        LOGGER.info("webshell param:podId is {} , tty is {} , stdin is {} , command is {} , containerId is {}",
//                podId, tty, stdin, command, containerId);
//        ExecArgs args = new ExecArgs(containerId, tty, stdin, command);
//        WebShellRequest request = new WebShellRequest(PodConstants.WEBSHELL, args);
//        WebshellResponse response = logicPodClientFactory.createPodClientByAccountId(getAccountId())
//                .getWebUrl(podId, request);
//        return response.getUrl();
//    }

    public PodPO getPodPO(String podID) {
        if (StringUtils.isEmpty(podID)) {
            throw new PodExceptions.PodIdIsEmptyException();
        }
        PodPO podDetail = podDao.getPodDetail(getAccountId(), podID);
        if (null == podDetail) {
            throw new PodExceptions.ResourceNotExistException();
        }
        return podDetail;
    }

    public int getPendingPodCountByUserId() {
        return podDao.getPendingPodCountByUserId(getAccountId());
    }

    public int getResourceRecycleIncompletePodCountByUserId() {
        return podDao.getResourceRecycleIncompletePodCountByUserId(getAccountId());
    }

    public ContainerCurrentState checkContainerPurchasedAndGetState(PodPO podPO, String containerName) {
        List<ContainerPO> containers = containerDao.listByPodId(podPO.getPodUuid());
        if (CollectionUtils.isEmpty(containers)) {
            containers = containerDao.listByPodId(podPO.getPodId());
        }
        if (CollectionUtils.isEmpty(containers)) {
            throw new CommonExceptions.ResourceNotExistException();
        }
        for (ContainerPO containerPO : containers) {
            if (containerName.equals(containerPO.getName())) {
                return convertToContainerCurrentState(containerPO.getCurrentState());
            }
        }
        throw new CommonExceptions.ResourceNotExistException();
    }

    public ContainerCurrentState convertToContainerCurrentState(String rawState) {
        return JsonUtil.fromJSON(rawState, ContainerCurrentState.class);
    }

    private List<PodPO> filterPodByChargeSource(List<PodPO> pods, String chargeSource) {
        List<PodPO> filteredPods = new LinkedList<>();
        for (PodPO podPO : pods) {
            if (podPO.getChargeSource().equalsIgnoreCase(chargeSource)) {
                filteredPods.add(podPO);
            }
        }
        return filteredPods;
    }

    private void checkOrderLabels(BciOrderExtra bciOrderExtra) {
        List<Label> labels = bciOrderExtra.getLabels();
        if (CollectionUtils.isEmpty(labels)) {
            labels = new LinkedList<>();
        }

        String accountID = getAccountId();
        boolean existed = false;
        for (Label label : labels) {
            if (LogicalConstant.LABEL_ACCOUNT_ID.equals(label.getLabelKey())) {
                label.setLabelValue(accountID);
                existed = true;
            }
        }
        if (!existed) {
            Label label = new Label();
            label.setLabelKey(LogicalConstant.LABEL_ACCOUNT_ID);
            label.setLabelValue(accountID);
            labels.add(label);
        }

        bciOrderExtra.setLabels(labels);
    }

    public void deleteLeakagePod(LeakagePodDeleteRequest request) {
        if (request == null || StringUtils.isEmpty(request.getPodId())
                || StringUtils.isEmpty(request.getPodUuid())) {
            LOGGER.warn(LOG_DELETE_PREFIX + "pods info is empty");
            throw new PodExceptions.LeakagePodInfoIsEmptyException();
        }

        bciAsyncService.getEipInstanceMapAsync();
        Map<String, EipInstance> eipInstanceMap = (Map<String, EipInstance>) asyncExecutorService.getAsyncResult(
                WorkKeyUtil.genWorkKey("getEipInstanceMapAsync", new LinkedList<Object>()));

        deleteLeakagePodAndEip(request.getPodId(), request.getPodUuid(), request.getRelatedReleaseFlag(),
                eipInstanceMap);
    }

    private void deleteLeakagePodAndEip(String podId, String podUuid, boolean relatedReleaseFlag,
                                        Map<String, EipInstance> eipInstanceMap) {

        if (relatedReleaseFlag) {
            forceReleaseBindEip(eipInstanceMap, podUuid);
        } else {
            EipInstance eipInstance = eipInstanceMap.get(podUuid);
            if (eipInstance != null) {
                unBindEipFromPod(eipInstance.getEip());
            }
        }

        String accountID = getAccountId();
        if (ResourceAccountSetting.isUnifiedCharge()) {
            accountID = ResourceAccountSetting.getResourceAccountId();
        }
        // billing 的接口，得用创建订单的accountID
        logicalResourceService.deleteResourceByName(accountID, podUuid, PodConstants.SERVICE_TYPE);
        // nova是用户ID创建的，用用户的ID
        deletePodFromNova(getAccountId(), podUuid);

        unBindTags(podId, podUuid);
    }

    public PodNumberResponse getPodNumberBySubnetId(PodNumberRequest request) {
        PodNumberResponse response = new PodNumberResponse();
        List<PodNumberResponse.PodNumber> podNumbers = new LinkedList<>();
        if (CollectionUtils.isEmpty(request.getSubnetIds())) {
            return response;
        }

        Map<String, Map<String, Long>> numbers = podDao.countPodWithSubnetId(getAccountId(), request.getSubnetIds());
        for (Map.Entry<String, Map<String, Long>> entry : numbers.entrySet()) {
            PodNumberResponse.PodNumber podNumber = new PodNumberResponse.PodNumber();
            podNumber.setSubnetId(entry.getKey());
            podNumber.setBciNum(entry.getValue().get("num").intValue());
            podNumbers.add(podNumber);
        }
        response.setList(podNumbers);
        return response;
    }

    public AttachVolumeUpdateDbRequest getAttachVolumeRequest(List<PodPO> pods,
                                                              PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(podPurchaseRequest.getVolume().getPodVolumes())) {
            return null;
        }
        AttachVolumeUpdateDbRequest attachVolumeRequest = new AttachVolumeUpdateDbRequest();
        PodPO pod = pods.get(0);
        List<Volume.PodVolume> podVolumes = podPurchaseRequest.getVolume().getPodVolumes();

        attachVolumeRequest.setInstanceUuid(pod.getPodId());
        List<String> volumeIds = new LinkedList<>();
        for (Volume.PodVolume podVolume : podVolumes) {
            if (podVolume != null && podVolume.getVolumeSource() != null
                    && podVolume.getVolumeSource().getCds() != null
                    && StringUtils.isNotEmpty(podVolume.getVolumeSource().getCds().getUuid())) {
                volumeIds.add(podVolume.getVolumeSource().getCds().getUuid());
            }
        }
        if (CollectionUtils.isEmpty(volumeIds)) {
            return null;
        }
        attachVolumeRequest.setDiskIds(volumeIds);

        return attachVolumeRequest;
    }

    public UserVersion isV1AccountId() {
        String accountId = getAccountId();
        UserVersion userVersion = new UserVersion();
        userVersion.setAccountID(accountId);
        boolean v1Res = commonUtils.checkWhiteList(LogicalConstant.ENABLE_BCI_1_0,
                regionConfiguration.getCurrentRegion(),
                accountId);
        userVersion.setIsv1(v1Res);
        return userVersion;
    }

    public UserVersion isV2AccountId() {
        String accountId = getAccountId();
        UserVersion userVersion = new UserVersion();
        userVersion.setAccountID(accountId);
        boolean v1Res = commonUtils.checkWhiteList(LogicalConstant.ENABLE_BCI_1_0,
                regionConfiguration.getCurrentRegion(),
                accountId);
        userVersion.setIsv2(!v1Res);
        return userVersion;
    }

    public boolean addCceClusterForUser(String userId) {
        if (userId == null || userId.isEmpty()) {
            LOGGER.error("user:{} add cceCluster fail, because user is null or empty.", userId);
            return false;
        }
        // 判断cceUserMap是否已经配置
        CceUserMap userMap = cceClusterService.getCceUserMapByUserId(userId);
        if (userMap != null) {
            LOGGER.debug("user:{} add cceCluster success, because cceUserMap already exist", userId);
            // double check
            // 1. 检查k8s用户命名空间 和 sidecar-command-cm configmap是否创建
            boolean createConfigMapRes = createOrUpdateSidecarCommandConfigMap(userId);
            if (!createConfigMapRes) {
                LOGGER.error("user:{} add cceCluster fail, because create sidecar-command-cm configmap error", userId);
                return false;
            }
            return true;
        }
        // 防止多接口调用导致并发问题
        synchronized (this) {
            // double check
            userMap = cceClusterService.getCceUserMapByUserId(userId);
            if (userMap != null) {
                LOGGER.debug("user:{} add cceCluster success, because cceUserMap already exist", userId);
                return true;
            }
            // 1. 判断是否存在default集群
            List<CceCluster> defaultCceClusters = cceClusterService.getDefaultCceClusters();
            if (defaultCceClusters.size() == 0) {
                LOGGER.error("user:{} add cceCluster fail, because not find default cceCluster.", userId);
                return false;
            }
            // 2. 为用户绑定default集群
            CceUserMap newUserMap = new CceUserMap(userId, defaultCceClusters.get(0).getCceId());
            boolean res = cceClusterService.createCceUserMap(newUserMap);
            if (!res) {
                LOGGER.error("user:{} add cceCluster fail, because add cceUserMap error.", userId);
                return false;
            }
            // 3. 为用户创建k8s命名空间 和 sidecar-command-cm configmap
            boolean createConfigMapRes = createOrUpdateSidecarCommandConfigMap(userId);
            if (!createConfigMapRes) {
                LOGGER.error("user:{} add cceCluster fail, because create sidecar-command-cm configmap error", userId);
                return false;
            }
        }
        return true;
    }

    public UserVersion isV3AccountId() {
        String accountId = getAccountId();
        boolean res = commonUtils.checkWhiteList(LogicalConstant.ENABLE_BCI_3_0, regionConfiguration.getCurrentRegion(),
                accountId);
        UserVersion userVersion = new UserVersion();
        userVersion.setAccountID(accountId);
        userVersion.setIsv3(res);
        return userVersion;
    }

    public List<ZoneMapDetail> listZone() {
        List<ZoneMapDetail> zones = new ArrayList<>();
        try {
            ZoneClient zoneClient = logicPodClientFactory.createZoneClient(getAccountId());
            LogicalZoneListResponse logicalZoneListResponse = zoneClient.listLogicalZone();
            List<String> logicZoneNames = logicalZoneListResponse.getLogicalZoneList();
            for (int i = 0; i < logicZoneNames.size(); i++) {
                ZoneMapDetail zoneDetail = zoneClient.getDetailByLogicalZone(logicZoneNames.get(i));
                zones.add(zoneDetail);
            }
            return zones;
        } catch (Exception e) {
            LOGGER.info("getZone from logical zone error = {}", e);
            throw new PodExceptions.InvalidateZoneException();
        }
    }

    public List<String> getAvailablePodIdsFromList(List<String> podIds) {
        List<String> availablePodIds = new ArrayList<>();
        String accountId = getAccountId();
        List<PodPO> availablePods = podDao.listPodPOByIds(podIds, accountId);
        for (PodPO podPO : availablePods) {
            availablePodIds.add(podPO.getPodId());
        }
        return availablePodIds;
    }

    private CreateEipRequest generateEipRequest(EipPurchaseRequest eipPurchaseRequest) {
        CreateEipRequest createEipRequest = new CreateEipRequest();
        createEipRequest.setName(eipPurchaseRequest.getName());
        createEipRequest.setRouteType(eipPurchaseRequest.getRouteType());
        createEipRequest.setBandwidthInMbps(eipPurchaseRequest.getBandwidthInMbps());
        createEipRequest.setCount(eipPurchaseRequest.getCount());
        CreateEipRequest.Billing billing = new CreateEipRequest.Billing();
        billing.setPaymentTiming(eipPurchaseRequest.getProductType());
        billing.setBillingMethod(eipPurchaseRequest.getSubProductType());
        createEipRequest.setBilling(billing);
        return createEipRequest;
    }

    public void biddingStatusConvert(List<PodPO> podPOS) {
        for (PodPO podPO : podPOS) {
            // todo 待创建pod代码合入后改为枚举值
            if ("bidding".equals(podPO.getProductType())) {
                podPO.setStatus(BciStatus.getBiddingConvertStatus(podPO.getStatus(), podPO.getPreemptStatus()));
            }
        }
    }

    public boolean isEnablePFS() {
        if (commonUtils.checkWhiteList(LogicalConstant.ENABLE_PFS, regionConfiguration.getCurrentRegion(),
                getAccountId())) {
            return true;
        }
        return false;
    }

    // 存量用户当前不屏蔽CPU型号，与存量用户沟通同意后，该白名单可以下掉；
    public boolean isDisplayOriginalCPUModel(String accountId) {
        if (commonUtils.checkWhiteList(LogicalConstant.DISPLAY_ORIGINAL_CPU_MODEL,
                regionConfiguration.getCurrentRegion(), accountId)) {
            return true;
        }
        return false;
    }

    // CPUType白名单，只允许白名单用户指定CPUType
    public boolean isEnableCPUType() {
        if (commonUtils.checkWhiteList(LogicalConstant.ENABLE_CPU_TYPE,
                regionConfiguration.getCurrentRegion(), getAccountId())) {
            return true;
        }
        return false;
    }

    // cpuType只能为：""，intel, amd
    public boolean isValidCPUType(String cpuType) {
        if ("".equals(cpuType) || PodConstants.CPU_TYPE_INTEL.equals(cpuType)
                || PodConstants.CPU_TYPE_AMD.equals(cpuType)) {
            return true;
        }
        return false;
    }


    public PreviewPodCapacityResponse previewPodCapacity(PreviewPodCapacityRequest request) {
        PreviewPodCapacityResponse response = new PreviewPodCapacityResponse();
        PreviewPodCapacityRequest responseRequest = (PreviewPodCapacityRequest) request.clone();

        // check and set request params
        LOGGER.debug("begin checkAndSetPreviewPodCapacityRequest, request: {}", request);
        validator.checkAndSetPreviewPodCapacityRequest(request);
        LOGGER.debug("end checkAndSetPreviewPodCapacityRequest, request: {}", request);
        // set request
        responseRequest.setCpu(request.getCpu());
        responseRequest.setMemory(request.getMemory());
        response.setRequest(responseRequest);

        // check instance group, same as webhook
        List<String> igs = getInstanceGroups(request);
        if (CollectionUtils.isEmpty(igs)) {
            LOGGER.warn("request {} selectMatchInstanceGroups result: no instance group", request);
            response.setCount(0);
            return response;
        }
        LOGGER.debug("request {} getInstanceGroups result: {}", request, igs);
        Map<String, Boolean> igIdMap = new HashMap<>();
        for (int i = 0; i < igs.size(); i++) {
            igIdMap.put(igs.get(i), true);
        }

        // get all available node
        List<V1Node> nodes = getAllAvailableNode(igIdMap);
        if (CollectionUtils.isEmpty(nodes)) {
            LOGGER.warn("request {} getAllAvailableNode result: no node", request);
            response.setCount(0);
            return response;
        }
        LOGGER.debug("request {} getAllAvailableNode node size: {}, result: {}",
                request, nodes.size(), nodes);

        // get all pods
        List<V1Pod> pods = getAllPods();

        // get preview pod count
        int previewPodCount = getPreviewPodCount(request, nodes, pods);
        if (previewPodCount == 0) {
            LOGGER.warn("request {} getPreviewPodCount result: no node fit the pod", request);
            response.setCount(0);
            return response;
        }
        LOGGER.debug("request {} getPreviewPodCount result: {}", request, previewPodCount);

        // judge
        int count = judgePreviewPodCount(previewPodCount);
        response.setCount(count);
        return response;
    }

    public int judgePreviewPodCount(int previewPodCount) {
        // get bci pod quota
        int podQuota = getBciQuota(getAccountId(), QuotaKeys.POD, previewPodCount);
        LOGGER.debug("{} get pod quota: {}", getAccountId(), podQuota);
        // get resource recycle incomplete pod quota
        int resourceRecycleIncompletePodCount = getResourceRecycleIncompletePodCountByUserId();
        LOGGER.debug("{} get resourceRecycleIncompletePodCount quota: {}",
                getAccountId(), resourceRecycleIncompletePodCount);
        return Math.min(Math.max(podQuota - resourceRecycleIncompletePodCount, 0), previewPodCount);
    }

    public Map<String, Map<String, Quantity>> getNodeAllocatableResource(List<V1Node> nodes, List<V1Pod> pods) {
        Map<String, Map<String, Quantity>> nodeAllocatableResourceMap = new HashMap<>();
        if (CollectionUtils.isEmpty(nodes)) {
            return nodeAllocatableResourceMap;
        }
        for (int i = 0; i < nodes.size(); i++) {
            String nodeName = nodes.get(i).getMetadata().getName();
            if (Strings.isNullOrEmpty(nodeName)) {
                continue;
            }
            Map<String, Quantity> nodeAlloc = nodes.get(i).getStatus().getAllocatable();
            if (nodeAlloc == null || nodeAlloc.isEmpty()) {
                continue;
            }
            // copy map
            Map<String, Quantity> nodeAllocMap = new HashMap<>();
            Iterator<String> iterator = nodeAlloc.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                Quantity value = new Quantity(
                        new BigDecimal(String.copyValueOf(nodeAlloc.get(key).getNumber().toString().toCharArray())),
                        nodeAlloc.get(key).getFormat());
                nodeAllocMap.put(key, value);
            }
            nodeAllocatableResourceMap.put(nodeName, nodeAllocMap);
        }
        LOGGER.debug("origin nodeAllocatableResourceMap: {}", nodeAllocatableResourceMap);
        for (int i = 0; i < pods.size(); i++) {
            V1Pod pod = pods.get(i);
            String nodeName = pod.getSpec().getNodeName();
            if (Strings.isNullOrEmpty(nodeName)) {
                continue;
            }
            if (!nodeAllocatableResourceMap.containsKey(nodeName)) {
                continue;
            }
            if (!Strings.isNullOrEmpty(pod.getMetadata().getName()) && pod.getMetadata().getName().contains("placeholder")) {
                continue;
            }
            Map<String, Quantity> nodeAllocatableResource = nodeAllocatableResourceMap.get(nodeName);
            LOGGER.debug("before: pod {} node name {} allocatable: {}", pod.getMetadata().getName(),
                    nodeName, nodeAllocatableResource);
            Map<String, Quantity> podRequestResource = getPodRequstResource(pod);
            Iterator<String> iterator = podRequestResource.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                Quantity podValue = podRequestResource.get(key);
                if (nodeAllocatableResource.containsKey(key)) {
                    Quantity nodeValue = nodeAllocatableResource.get(key);
                    if (Objects.equals(nodeValue.getFormat(), podValue.getFormat())) {
                        BigDecimal res = nodeValue.getNumber().subtract(podValue.getNumber());
                        nodeAllocatableResource.put(key, new Quantity(res, nodeValue.getFormat()));
                    }
                }
            }
            LOGGER.debug("after: pod {} node name {} allocatable: {}", pod.getMetadata().getName(),
                    nodeName, nodeAllocatableResource);
        }
        LOGGER.debug("modify nodeAllocatableResourceMap: {}", nodeAllocatableResourceMap);
        return nodeAllocatableResourceMap;
    }

    // 所有 Init 容器上定义的任何特定资源的 limit 或 request 的最大值，作为 Pod 有效初始 request/limit。 
    // 如果任何资源没有指定资源限制，这被视为最高限制
    // Pod 对资源的 有效 limit/request 是如下两者中的较大者
    // 1. 所有应用容器对某个资源的 limit/request 之和
    // 2. 对某个资源的有效初始 limit/request
    // 基于有效 limit/request 完成调度，这意味着 Init 容器能够为初始化过程预留资源， 这些资源在 Pod 生命周期过程中并没有被使用
    public Map<String, Quantity> getPodRequstResource(V1Pod pod) {
        Map<String, Quantity> request = new HashMap<>();
        List<V1Container> initContainers = pod.getSpec().getInitContainers();
        Map<String, Quantity> initContainerRequestMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(initContainers)) {
            for (int i = 0; i < initContainers.size(); i++) {
                if (initContainers.get(i).getResources() == null) {
                    continue;
                }
                if (initContainers.get(i).getResources().getRequests() == null) {
                    continue;
                }
                Map<String, Quantity> initContainerRequest = initContainers.get(i).getResources().getRequests();
                Iterator<String> iterator = initContainerRequest.keySet().iterator();
                // 从init container request中选取最大值
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    Quantity value = initContainerRequest.get(key);
                    if (initContainerRequestMap.containsKey(key)) {
                        Quantity leftValue = initContainerRequestMap.get(key);
                        if (Objects.equals(leftValue.getFormat(), value.getFormat())
                                && ObjectUtils.compare(leftValue.getNumber(), value.getNumber()) < 0) {
                            initContainerRequestMap.put(key, value);
                        }
                    } else {
                        initContainerRequestMap.put(key, value);
                    }
                }
            }
        }
        List<V1Container> containers = pod.getSpec().getContainers();
        Map<String, Quantity> containerRequestMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(containers)) {
            for (int i = 0; i < containers.size(); i++) {
                if (containers.get(i).getResources() == null) {
                    continue;
                }
                if (containers.get(i).getResources().getRequests() == null) {
                    continue;
                }
                Map<String, Quantity> containerRequest = containers.get(i).getResources().getRequests();
                Iterator<String> iterator = containerRequest.keySet().iterator();
                // container request累加
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    Quantity value = containerRequest.get(key);
                    if (containerRequestMap.containsKey(key)) {
                        Quantity leftValue = containerRequestMap.get(key);
                        if (Objects.equals(leftValue.getFormat(), value.getFormat())) {
                            BigDecimal res = leftValue.getNumber().add(value.getNumber());
                            containerRequestMap.put(key, new Quantity(res, leftValue.getFormat()));
                        }
                    } else {
                        containerRequestMap.put(key, value);
                    }
                }
            }
        }

        Iterator<String> initContainerIterator = initContainerRequestMap.keySet().iterator();
        while (initContainerIterator.hasNext()) {
            String key = initContainerIterator.next();
            Quantity initContainerValue = initContainerRequestMap.get(key);
            if (containerRequestMap.containsKey(key)) {
                Quantity containerValue = containerRequestMap.get(key);
                if (Objects.equals(initContainerValue.getFormat(), containerValue.getFormat())) {
                    if (ObjectUtils.compare(initContainerValue.getNumber(), containerValue.getNumber()) >= 0) {
                        request.put(key, initContainerValue);
                    } else {
                        request.put(key, containerValue);
                    }
                }
            } else {
                request.put(key, initContainerValue);
            }
        }
        Iterator<String> containerIterator = containerRequestMap.keySet().iterator();
        while (containerIterator.hasNext()) {
            String key = containerIterator.next();
            Quantity containerValue = containerRequestMap.get(key);
            if (!initContainerRequestMap.containsKey(key)) {
                request.put(key, containerValue);
            }
        }
        return request;
    }

    public int getPreviewPodCount(PreviewPodCapacityRequest request, List<V1Node> nodes, List<V1Pod> pods) {
        int count = 0;
        // get node allocatable resource
        Map<String, Map<String, Quantity>> nodeAllocatableResource = getNodeAllocatableResource(nodes, pods);
        LOGGER.debug("getNodeAllocatableResource result: {}", nodeAllocatableResource);
        if (nodeAllocatableResource == null || nodeAllocatableResource.isEmpty()) {
            return count;
        }
        Quantity cpu = Quantity.fromString(String.valueOf(request.getCpu()));
        Quantity memory = Quantity.fromString(String.valueOf(request.getMemory()) + "Gi");
        Quantity gpuCount = Quantity.fromString(String.valueOf(request.getGpuCount()));
        LOGGER.debug("preview pod param, cpu: {}, memory: {}, gpu type: {}, gpu count: {}",
                cpu, memory, request.getGpuType(), gpuCount);

        Iterator<String> iterator = nodeAllocatableResource.keySet().iterator();
        while (iterator.hasNext()) {
            String nodeName = iterator.next();
            Map<String, Quantity> alloc = nodeAllocatableResource.get(nodeName);
            LOGGER.debug("node name {} has Allocatable {}", nodeName, alloc);
            int cpuQuotaCount = 0;
            int memoryQuotaCount = 0;
            int gpuQuotaCount = 0;
            // cpu
            Quantity allocCPU = alloc.get("cpu");
            if (allocCPU == null) {
                continue;
            }
            if (Objects.equals(allocCPU.getFormat(), cpu.getFormat())) {
                if (ObjectUtils.compare(allocCPU.getNumber(), cpu.getNumber()) >= 0) {
                    cpuQuotaCount = allocCPU.getNumber().divide(cpu.getNumber(), 2, RoundingMode.HALF_UP).intValue();
                    LOGGER.debug("cpuQuotaCount: {}", cpuQuotaCount);
                }
            }
            if (cpuQuotaCount == 0) {
                continue;
            }
            // memory
            Quantity allocMemory = alloc.get("memory");
            if (allocMemory == null) {
                continue;
            }
            if (Objects.equals(allocMemory.getFormat(), memory.getFormat())) {
                if (ObjectUtils.compare(allocMemory.getNumber(), memory.getNumber()) >= 0) {
                    memoryQuotaCount = allocMemory.getNumber().divide(memory.getNumber(), 2, RoundingMode.HALF_UP).intValue();
                    LOGGER.debug("memoryQuotaCount: {}", memoryQuotaCount);
                }
            }
            if (memoryQuotaCount == 0) {
                continue;
            }
            // gpu
            if (!Strings.isNullOrEmpty(request.getGpuType())) {
                Quantity allocGPUCount = alloc.get(request.getGpuType());
                if (allocGPUCount == null) {
                    continue;
                }
                if (Objects.equals(allocGPUCount.getFormat(), gpuCount.getFormat())) {
                    if (ObjectUtils.compare(allocGPUCount.getNumber(), gpuCount.getNumber()) >= 0) {
                        gpuQuotaCount = allocGPUCount.getNumber().divide(gpuCount.getNumber(), 2, RoundingMode.HALF_UP).intValue();
                        LOGGER.debug("gpuQuotaCount: {}", gpuQuotaCount);
                    }
                }
                if (gpuQuotaCount == 0) {
                    continue;
                }
                count += Math.min(Math.min(cpuQuotaCount, memoryQuotaCount), gpuQuotaCount);
            } else {
                Quantity allocGPUCount = alloc.get(request.getGpuType());
                if (allocGPUCount != null) {
                    continue;
                }
                count += Math.min(cpuQuotaCount, memoryQuotaCount);
            }
        }
        return count;
    }

    public List<V1Pod> getAllPods() {
        List<V1Pod> pods = k8sService.listAllPods(getAccountId());
        return pods;
    }

    public List<V1Node> getAllAvailableNode(Map<String, Boolean> igIdMap) {
        List<V1Node> nodes = k8sService.listNodes(getAccountId());
        // 空闲节点，主要是init/cool状态的节点
        List<V1Node> freeNodes = new ArrayList<>();
        // 可用节点，已经被该用户占据的节点
        List<V1Node> usedNodes = new ArrayList<>();
        for (int i = 0; i < nodes.size(); i++) {
            V1Node node = nodes.get(i);
            Map<String, String> labels = node.getMetadata().getLabels();
            // filter label
            if (labels.size() == 0) {
                LOGGER.debug("getAllAvailableNode filtered: node {} has no label",
                        node.getMetadata().getName());
                continue;
            }
            // filter by instance group id
            if (!labels.containsKey(Node.NODE_LABEL_INSTANCE_GROUP_ID)) {
                LOGGER.debug("getAllAvailableNode filtered: node {} has no {} label",
                        node.getMetadata().getName(), Node.NODE_LABEL_INSTANCE_GROUP_ID);
                continue;
            }
            if (!igIdMap.containsKey(labels.get(Node.NODE_LABEL_INSTANCE_GROUP_ID))) {
                LOGGER.debug("getAllAvailableNode filtered: node {} fit no {}",
                        node.getMetadata().getName(), Node.NODE_LABEL_INSTANCE_GROUP_ID);
                continue;
            }
            // filter by node state machine
            if (!labels.containsKey(Node.NODE_LABEL_STATE_MACHINE)) {
                LOGGER.debug("getAllAvailableNode filtered: node {} has no {} label",
                        node.getMetadata().getName(), Node.NODE_LABEL_STATE_MACHINE);
                continue;
            }
            if (labels.get(Node.NODE_LABEL_STATE_MACHINE).equalsIgnoreCase(Node.NODE_STATE_MACHINE_INIT)) {
                LOGGER.debug("getAllAvailableNode get node {} success", node.getMetadata().getName());
                freeNodes.add(node);
            } else if (labels.get(Node.NODE_LABEL_STATE_MACHINE).equalsIgnoreCase(Node.NODE_STATE_MACHINE_COOL)) {
                LOGGER.debug("getAllAvailableNode get node {} success", node.getMetadata().getName());
                freeNodes.add(node);
            } else if (labels.get(Node.NODE_LABEL_STATE_MACHINE).equalsIgnoreCase(Node.NODE_STATE_MACHINE_RUNPOD)) {
                if (!labels.containsKey(Node.NODE_LABEL_TENANT_LOCK)) {
                    LOGGER.debug("getAllAvailableNode filtered: node {} has no {} label",
                            node.getMetadata().getName(), Node.NODE_LABEL_TENANT_LOCK);
                    continue;
                }
                if (labels.get(Node.NODE_LABEL_TENANT_LOCK).equalsIgnoreCase(getAccountId())) {
                    LOGGER.debug("getAllAvailableNode get node {} success", node.getMetadata().getName());
                    usedNodes.add(node);
                } else {
                    LOGGER.debug("getAllAvailableNode filtered: node {} fit no {} label",
                            node.getMetadata().getName(), Node.NODE_LABEL_TENANT_LOCK);
                }
            } else {
                LOGGER.debug("getAllAvailableNode filtered: node {} fit no {} label",
                        node.getMetadata().getName(), Node.NODE_LABEL_STATE_MACHINE);
            }

        }
        List<V1Node> filteredNodes = new ArrayList<>();
        filteredNodes.addAll(freeNodes);
        filteredNodes.addAll(usedNodes);
        return filteredNodes;
    }

    public List<InstanceGroupConfigMap> getCandiatesInstanceGroups(PreviewPodCapacityRequest request) {
        LOGGER.debug("getCandiatesInstanceGroups: request: {}", request);
        List<InstanceGroupConfigMap> candidateIgs = new ArrayList<>();
        // get ig cm
        V1ConfigMap cm = k8sService.getKubeSystemConfigMapByName(getAccountId(), InstanceGroup.INSTANCE_GROUP_CM_NAME);
        if (cm == null) {
            LOGGER.warn("getConfigMapByName fail, ns: {}, name: {}",
                    InstanceGroup.INSTANCE_GROUP_CM_NS, InstanceGroup.INSTANCE_GROUP_CM_NAME);
            return candidateIgs;
        }

        // get white list
        List<String> whiteAccountIDs = new ArrayList<>();
        String whiteAccountStr = cm.getData().get(InstanceGroup.INSTANCE_GROUP_ACCOUNT_WHITE_LIST_CM_DATA_KEY);
        if (StringUtils.isNotBlank(whiteAccountStr)) {
            whiteAccountIDs = JsonUtil.toList(whiteAccountStr, String.class);
        }
        LOGGER.debug("{}/{} config map get {} is: {}",
                InstanceGroup.INSTANCE_GROUP_CM_NS, InstanceGroup.INSTANCE_GROUP_CM_NAME,
                InstanceGroup.INSTANCE_GROUP_ACCOUNT_WHITE_LIST_CM_DATA_KEY, whiteAccountStr);
        Map<String, Boolean> whiteAccountMap = new HashMap<>();
        for (int i = 0; i < whiteAccountIDs.size(); i++) {
            whiteAccountMap.put(whiteAccountIDs.get(i), true);
        }

        // get igs
        String config = cm.getData().get(InstanceGroup.INSTANCE_GROUP_CM_KEY);
        if (Strings.isNullOrEmpty(config)) {
            LOGGER.warn("{}/{} config map is empty",
                    InstanceGroup.INSTANCE_GROUP_CM_NS, InstanceGroup.INSTANCE_GROUP_CM_NAME);
            return candidateIgs;
        }
        LOGGER.debug("{}/{} config map get {} is: {}",
                InstanceGroup.INSTANCE_GROUP_CM_NS, InstanceGroup.INSTANCE_GROUP_CM_NAME,
                InstanceGroup.INSTANCE_GROUP_CM_KEY, config);
        List<InstanceGroupConfigMap> igcms = JsonUtil.toList(config, InstanceGroupConfigMap.class);
        if (CollectionUtils.isEmpty(igcms)) {
            LOGGER.warn("{}/{} config map JsonUtil.toList empty",
                    InstanceGroup.INSTANCE_GROUP_CM_NS, InstanceGroup.INSTANCE_GROUP_CM_NAME);
            return candidateIgs;
        }
        List<InstanceGroupConfigMap> userIgcms = new ArrayList<>();
        for (int i = 0; i < igcms.size(); i++) {
            if (igcms.get(i).getInstanceGroupName().contains(InstanceGroup.INSTANCE_GROUP_FOR_STARGZ_IMAGE)) {
                continue;
            }
            userIgcms.add(igcms.get(i));
        }
        LOGGER.debug("{}/{} config map get user config map size: {}, result: {}",
                InstanceGroup.INSTANCE_GROUP_CM_NS, InstanceGroup.INSTANCE_GROUP_CM_NAME,
                userIgcms.size(), userIgcms);
        // 选择匹配的节点组
        candidateIgs = selectMatchInstanceGroups(request, userIgcms, whiteAccountMap);
        LOGGER.debug("request {} selectMatchInstanceGroups size: {}, result: {}",
                request, candidateIgs.size(), candidateIgs);
        return candidateIgs;

    }

    public List<String> getInstanceGroups(PreviewPodCapacityRequest request) {
        List<String> igs = new ArrayList<>();

        List<InstanceGroupConfigMap> candidateIgs = getCandiatesInstanceGroups(request);
        for (int i = 0; i < candidateIgs.size(); i++) {
            igs.add(candidateIgs.get(i).getInstanceGroupId());
        }
        return igs;
    }

    // selectMatchInstanceGroups 选择匹配的节点组
    // 1. 检查physicalZone
    // 2. 检查 cpu_type
    // 3. 支持的容器规格 & 白名单列表
    // 4. 支持的账户列表
    public List<InstanceGroupConfigMap> selectMatchInstanceGroups(PreviewPodCapacityRequest request,
                                                                  List<InstanceGroupConfigMap> igs, Map<String, Boolean> whiteAccountMap) {
        List<InstanceGroupConfigMap> candidateInstanceGroups = new ArrayList<>();
        List<InstanceGroupConfigMap> candidateSupportedAccountInstanceGroup = new ArrayList<>();
        for (int i = 0; i < igs.size(); i++) {
            // 1. 检查physicalZone
            if (!igs.get(i).checkPhysicalZone(request.getPhysicalZones())) {
                LOGGER.debug("ig {} can not fit physical zone", igs.get(i));
                continue;
            }
            // 2. 检查 cpu_type
            if (!igs.get(i).checkCPUType(request.getCpuType())) {
                LOGGER.debug("ig {} can not fit cpu type", igs.get(i));
                continue;
            }
            // 3. 支持的容器规格 & 白名单列表
            if (!igs.get(i).checkResourceMatch(request, whiteAccountMap)) {
                LOGGER.debug("ig {} can not fit resource match", igs.get(i));
                continue;
            }
            // 4. 支持的账户列表
            if (CollectionUtils.isEmpty(igs.get(i).getAuthorizedUsers())) {
                // 未设置账户列表，支持所有用户的pod
                candidateInstanceGroups.add(igs.get(i));
            } else if (igs.get(i).checkAuthorizedUser(getAccountId())) {
                // 设置了账户列表，只支持账户列表的pod
                candidateSupportedAccountInstanceGroup.add(igs.get(i));
            } else {
                LOGGER.debug("{} can not fit authorized user", igs.get(i));
            }
        }
        LOGGER.debug("request {} candidateInstanceGroups size: {}, result: {}",
                request, candidateInstanceGroups.size(), candidateInstanceGroups);
        LOGGER.debug("request {} candidateSupportedAccountInstanceGroup size: {}, result: {}",
                request, candidateSupportedAccountInstanceGroup.size(),
                candidateSupportedAccountInstanceGroup);
        // 如果用户在节点组所支持的账户列表中，优选选择该节点组
        if (CollectionUtils.isNotEmpty(candidateSupportedAccountInstanceGroup)) {
            return candidateSupportedAccountInstanceGroup;
        }
        return candidateInstanceGroups;
    }

    /**
     * 更新pod所在的config file
     *
     * @param podId
     * @param request
     */
    public void updateConfigFile(String podId, UpdateConfigFileRequest request) throws K8sServiceException {
        String accountId = getAccountId();
        V1Pod pod = k8sService.getPod(accountId, podId);
        if (pod == null) {
            // pod 不存在
            throw new CommonExceptions.ResourceNotExistException();
        }
        ConfigFile configFile = request.getConfigFile();
        if (CollectionUtils.isEmpty(configFile.getConfigFiles())) {
            return;
        }

        boolean updateConfigMapSuccess = false;
        String configMapName = PodUtils.buildConfigMapName(podId, configFile.getName());
        V1ConfigMap configMap = k8sService.getConfigMapByName(accountId, configMapName);
        // configmap不存在
        if (configMap == null) {
            throw new CommonExceptions.ResourceNotExistException();
        }

        Map<String, byte[]> oldConfigMapData = configMap.getBinaryData();
        if (MapUtils.isEmpty(oldConfigMapData)) {
            return;
        }

        for (int i = 0; i < 3; i++) {
            Map<String, byte[]> newConfigMapData = new HashMap<>();
            // ConfigMap类型，直接覆盖
            if (request.getVolumeType() != null && request.getVolumeType().equals("ConfigMap")) {
                for (ConfigFileDetail detail : configFile.getConfigFiles()) {
                    newConfigMapData.put(detail.buildConfigKeyFromPath(), Base64.decodeBase64(detail.getFile()));
                }
            } else {
                for (Map.Entry<String, byte[]> entry : oldConfigMapData.entrySet()) {
                    String key = entry.getKey();
                    byte[] value = entry.getValue();
                    newConfigMapData.put(key, value);
                }

                for (ConfigFileDetail detail : configFile.getConfigFiles()) {
                    if (!oldConfigMapData.containsKey(detail.buildConfigKeyFromPath())) {
                        continue;
                    }
                    newConfigMapData.put(detail.buildConfigKeyFromPath(), Base64.decodeBase64(detail.getFile()));
                }
            }

            // 拷贝一份
            V1ConfigMap newConfigMap = deepCopyConfigMap(configMap);
            newConfigMap.setBinaryData(newConfigMapData);

            if (k8sService.updateConfigMap(accountId, configMapName, newConfigMap)) {
                updateConfigMapSuccess = true;
                break;
            }
        }

        if (!updateConfigMapSuccess) {
            throw new K8sServiceException(ErrorCode.UPDATE_CONFIGMAP_FAILED, "update configfile failed. ");
        }
    }

    public void updatePodExtraInfo(String podId, Map<String, String> extras) {
        if (MapUtils.isEmpty(extras)) {
            return;
        }

        PodPO podPO = this.getPodPO(podId);
        PodExtra podExtra = JsonUtil.fromJSON(podPO.getExtra(), PodExtra.class);
        if (podExtra == null) {
            podExtra = new PodExtra();
        }
        Map<String, String> vkExtras = podExtra.getVkExtras();
        if (vkExtras == null) {
            vkExtras = new HashMap<>();
        }
        // 存量更新
        vkExtras.putAll(extras);
        podExtra.setVkExtras(vkExtras);
        String newPodExtra = JsonUtil.toJSON(podExtra);
        podPO.setExtra(newPodExtra);
        podDao.updatePodExtras(podPO);
    }

    private V1ConfigMap deepCopyConfigMap(V1ConfigMap configMap) {
        V1ConfigMap newConfigMap = new V1ConfigMap();
        newConfigMap.setMetadata(configMap.getMetadata());
        newConfigMap.setKind(configMap.getKind());
        newConfigMap.setApiVersion(configMap.getApiVersion());
        newConfigMap.setData(configMap.getData());
        newConfigMap.setBinaryData(configMap.getBinaryData());
        newConfigMap.setImmutable(configMap.getImmutable());
        return newConfigMap;
    }

    public boolean createOrUpdateSidecarCommandConfigMap(String namespace) {
        try {
            return k8sService.createSidecarCommandConfigMapFromFile(namespace);
        } catch (Exception e) {
            LOGGER.error("createSidecarCommandConfigMap in namespace:{} failed, exception: {}",
                    namespace, e);
            return false;
        }
    }

    /**
     * Separate method for persisting migrationUuid mapping, easy to mock/test.
     */
    void handleMigrationMapping(String migrationUuid, List<String> podIds) {
        if (StringUtils.isEmpty(migrationUuid) || podIds == null || podIds.isEmpty()) {
            return;
        }
        for (String podId : podIds) {
            try {
                podMigrationDaoV2.insert(podId, migrationUuid);
            } catch (Exception e) {
                LOGGER.warn("failed to insert pod migration mapping for podId:{} migrationUuid:{}, err:{}", podId, migrationUuid, e.toString());
            }
        }
    }
}
