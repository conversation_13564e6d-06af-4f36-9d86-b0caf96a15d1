package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("K8S_POD_UNKNOWN")
public class K8SPodUnknownHandler extends K8SPodEventStatusBaseHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(K8SPodUnknownHandler.class);

    @Override
    public boolean check() {
        PodPO podPO = getPodPO();
        String podId = podPO.getPodId();
        LOGGER.error("K8SPodUnknownHandler check is called and failed! k8s pod:{} enters an unexpected status, " +
                " k8s pod status:Unknown, db pod status:{}, and this pod event will be ignored",
                podId, podPO.getStatus());
        return super.check();
    }

    @Override
    public boolean execute() {
        PodPO podPO = getPodPO();
        String podId = podPO.getPodId();
        LOGGER.error("K8SPodUnknownHandler execute is called and failed! This should not happen! pod:{}",
                podId);
        return super.execute();
    }
}
