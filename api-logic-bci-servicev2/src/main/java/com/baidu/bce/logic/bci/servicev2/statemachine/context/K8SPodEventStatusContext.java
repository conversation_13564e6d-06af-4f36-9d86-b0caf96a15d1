package com.baidu.bce.logic.bci.servicev2.statemachine.context;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class K8SPodEventStatusContext extends StateMachineEventContext {
    private String newConditions;

    public K8SPodEventStatusContext(String podId, String userId,
                                    long newResourceVersion, String newStatus,
                                    String newConditions) {
        super(podId, userId, newResourceVersion, newStatus);
        this.newConditions = newConditions;
    }
}
