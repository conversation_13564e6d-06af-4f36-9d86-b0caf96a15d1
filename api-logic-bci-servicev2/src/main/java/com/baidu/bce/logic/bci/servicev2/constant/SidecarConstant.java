package com.baidu.bce.logic.bci.servicev2.constant;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Data
@Service("SidecarConstant")
public class SidecarConstant {
    public String sidecarCommandConfigMapDefaultNamespace = "kube-system";
    public String sidecarCommandConfigMapName = "sidecar-command-cm";

    // bls_check.sh
    public String sidecarCommandConfigMapBlsCheckName = "bls_check.sh";
    @Value("${bci.sidecar.command.configmap.bls.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-bls_check.sh}")
    public String sidecarCommandConfigMapBlSCheckFilePath;

    // bos_check.sh
    public String sidecarCommandConfigMapBosCheckName = "bos_check.sh";
    @Value("${bci.sidecar.command.configmap.bos.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-bos_check.sh}")
    public String sidecarCommandConfigMapBosCheckFilePath;

    // image_download_check.sh
    public String sidecarCommandConfigMapImageDownloadCheckName = "image_download_check.sh";
    @Value("${bci.sidecar.command.configmap.image.download.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-image_download_check.sh}")
    public String sidecarCommandConfigMapImageDownloadCheckFilePath;

    // kubelet_proxy_check.sh
    public String sidecarCommandConfigMapKubeletProxyCheckName = "kubelet_proxy_check.sh";
    @Value("${bci.sidecar.command.configmap.kubelet.proxy.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-kubelet_proxy_check.sh}")
    public String sidecarCommandConfigMapKubeletProxyCheckFilePath;

    // kubeproxy_check.sh
    public String sidecarCommandConfigMapKubeProxyCheckName = "kubeproxy_check.sh";
    @Value("${bci.sidecar.command.configmap.kube.proxy.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-kubeproxy_check.sh}")
    public String sidecarCommandConfigMapKubeProxyCheckFilePath;

    // nfs_check.sh
    public String sidecarCommandConfigMapNfsCheckName = "nfs_check.sh";
    @Value("${bci.sidecar.command.configmap.nfs.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-nfs_check.sh}")
    public String sidecarCommandConfigMapNfsCheckFilePath;

    // pfs_check.sh
    public String sidecarCommandConfigMapPfsCheckName = "pfs_check.sh";
    @Value("${bci.sidecar.command.configmap.pfs.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-pfs_check.sh}")
    public String sidecarCommandConfigMapPfsCheckFilePath;

    // ntp_check.sh
    public String sidecarCommandConfigMapNTPCheckName = "ntp_check.sh";
    @Value("${bci.sidecar.command.configmap.ntp.check:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-ntp_check.sh}")
    public String sidecarCommandConfigMapNTPCheckFilePath;

    // post.sh
    public String sidecarCommandConfigMapPostName = "post.sh";
    @Value("${bci.sidecar.command.configmap.post:/home/<USER>/bce-api-service/bce-api-modules/api-logic-bci/conf" +
            "/sidecar-command-cm-post.sh}")
    public String sidecarCommandConfigMapPostFilePath;
}
