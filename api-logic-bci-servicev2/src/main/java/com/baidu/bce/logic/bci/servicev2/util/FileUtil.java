package com.baidu.bce.logic.bci.servicev2.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class FileUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtil.class);
    public static boolean isFileExist(String path) {
        return new File(path).exists();
    }

    public static boolean isDirectory(String path) {
        return new File(path).isDirectory();
    }

    public static String readFile(String path) {
        try {
            if (!isFileExist(path)) {
                LOGGER.error("File not exist: {}", path);
                return null;
            }
            return new String(Files.readAllBytes(Paths.get(path)));
        } catch (IOException e) {
            LOGGER.error("Read file:{} error: {}", path, e);
            return null;
        }
    }
}
