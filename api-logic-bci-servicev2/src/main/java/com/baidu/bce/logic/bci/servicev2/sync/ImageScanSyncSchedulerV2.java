package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.sync.service.ImageScanSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("ImageScanSyncSchedulerV2")
@Profile("default")
public class ImageScanSyncSchedulerV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageAccelerateSyncSchedulerV2.class);
    private static final int SYNC_IMAGE_ACCELERATE_TIME = 60000;
    private String schedulerName = "ImageScanSyncSchedulerV2.runScheduledTask";

    @Autowired
    private ImageScanSyncServiceV2 imageScanSyncServiceV2;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    @Scheduled(fixedRate = SYNC_IMAGE_ACCELERATE_TIME)
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        imageScanSyncServiceV2.syncImageAccelerateCrdForImageScan();
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }

}