package com.baidu.bce.logic.bci.servicev2.pod;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.internalsdk.bci.ContainerManagerClient;
import com.baidu.bce.internalsdk.bci.model.CNImageCacheRequest;
import com.baidu.bce.internalsdk.bci.model.ImageCacheTaskStatus;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.daov2.common.model.ImageCacheStatus;
import com.baidu.bce.logic.bci.daov2.imagecache.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecache.model.ImageCachePO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.common.service.BciAsyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.configuration.CNHostConfigurationV2;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.CacheStatus;
import com.baidu.bce.logic.bci.servicev2.model.ImageCacheRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageCacheResponse;
import com.baidu.bce.logic.bci.servicev2.util.Validator;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("PodImageServiceV2")
public class PodImageServiceV2 {

    public static final Logger LOGGER = LoggerFactory.getLogger(PodImageServiceV2.class);

    private static final Long ONE_HOUR = 60 * 60 * 1000L;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private CNHostConfigurationV2 cnHostConfiguration;

    @Autowired
    private Validator validator;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    private BciAsyncServiceV2 bciAsyncService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private ImageCacheDaoV2 imageCacheDao;


    public ImageCacheResponse createImageCache(ImageCacheRequest request) {
        ImageCacheResponse imageCacheResponse = new ImageCacheResponse();
        String accountId = LogicUserService.getAccountId();
        // 读取后端 server list
        List<String> hosts = getHosts(request.getAvailableZone());
        // taskId
        String taskId = commonUtils.createTaskId();
        LOGGER.debug("taskId generated: {}", taskId);
        // 分发任务，TODO 失败怎么重试？
        CNImageCacheRequest cnImageCacheRequest = new CNImageCacheRequest();

        if (null != request.getImageSecret()) {
            CNImageCacheRequest.ImageSecret imageSecret = new CNImageCacheRequest.ImageSecret();
            imageSecret.setServer(request.getImageSecret().getServer());
            imageSecret.setUserName(request.getImageSecret().getUserName());
            imageSecret.setPassword(request.getImageSecret().getPassword());
            cnImageCacheRequest.setImageSecret(imageSecret);
        }

        cnImageCacheRequest.setImage(request.getImage());
        cnImageCacheRequest.setTaskId(taskId);

        List<ContainerManagerClient> clientList = new ArrayList<>();
        for (String host : hosts) {
            ContainerManagerClient client = logicPodClientFactory.createContainerManagerClient(host, accountId);
            bciAsyncService.createImageCache(cnImageCacheRequest, client);
            clientList.add(client);
        }

        for (ContainerManagerClient client : clientList) {
            try {
                asyncExecutorService.getAsyncResult(WorkKeyUtil.genWorkKey("createImageCache",
                                Arrays.asList(cnImageCacheRequest, client)));
            } catch (Exception e) {
                LOGGER.debug("failed to createImageCache, endpoint: {}, ex: {}", client.getEndpoint(), e);
                throw new PodExceptions.CreateImageCacheFailed();
            }
        }

        // 落库
        try {
            ImageCachePO imageCachePO = new ImageCachePO(taskId, accountId, ImageCacheStatus.doing,
                    request.getImage(), request.getAvailableZone());
            imageCacheDao.insert(imageCachePO);
        } catch (Exception e) {
            LOGGER.debug("failed to insert this task {}, ex: {}", taskId, e);
            throw new PodExceptions.InternalServerErrorException();
        }

        // 返回任务ID
        imageCacheResponse.setTaskId(taskId);
        return imageCacheResponse;
    }

    public CacheStatus getImageCache(String taskId) {
        CacheStatus cacheStatus = new CacheStatus();
        String accountId = LogicUserService.getAccountId();
        List<ImageCachePO> imageCachePOS = imageCacheDao.queryTask(taskId, accountId);
        if (CollectionUtils.isEmpty(imageCachePOS)) {
            LOGGER.debug("cannot find get this task: {}", taskId);
            throw new PodExceptions.ResourceNotExistException();
        }

        if (imageCachePOS.size() > 1) {
            LOGGER.debug("find multi result by {} which is unexpected", taskId);
            throw new PodExceptions.InternalServerErrorException();
        }

        ImageCachePO imageCachePO = imageCachePOS.get(0);
        // 如果数据库中状态已经是done，直接返回；failed的情况,保留手动处理的空间。
        if (ImageCacheStatus.done.equals(imageCachePO.getStatus())) {
            cacheStatus.setStatus(ImageCacheStatus.done);
            return cacheStatus;
        }
        String az = imageCachePO.getAz();
        Timestamp createdTime = imageCachePO.getCreatedTime();
        // 读取后端 server list
        List<String> hosts = getHosts(az);

        // 并发读取
        List<ContainerManagerClient> clientList = new ArrayList<>();
        for (String host : hosts) {
            ContainerManagerClient client = logicPodClientFactory.createContainerManagerClient(host, accountId);
            bciAsyncService.queryImageCache(taskId, client);
            clientList.add(client);
        }

        ImageCacheStatus mergedStatus = ImageCacheStatus.done;
        for (ContainerManagerClient client : clientList) {
            ImageCacheStatus eachStatus = ImageCacheStatus.done;
            ImageCacheTaskStatus response = new ImageCacheTaskStatus();
            try {
                response = (ImageCacheTaskStatus) asyncExecutorService
                        .getAsyncResult(WorkKeyUtil.genWorkKey("queryImageCache",
                                Arrays.asList(taskId, client)));
            } catch (Exception e) {
                LOGGER.debug("failed to getImageCache for task {}, endpoint: {}, ex: {}",
                        taskId, client.getEndpoint(), e);
                // todo 在cn 宕机或者其它原因导致的cm服务长期不可用的情况下，需要重置cn配置。
                response.setStatus(ImageCacheTaskStatus.TaskStatus.Pulling.toString());
                long current = System.currentTimeMillis();
                if (current - createdTime.getTime() > ONE_HOUR) {
                    response.setStatus(ImageCacheTaskStatus.TaskStatus.Failed.toString());
                }
            }
            switch (ImageCacheTaskStatus.TaskStatus.valueOf(response.getStatus())) {
                case Pulled:
                    break;
                case Failed:
                    LOGGER.debug("task {} failed on {} because {}", taskId, client.getEndpoint(), response.getReason());
                    eachStatus = ImageCacheStatus.failed;
                    break;
                default:
                    eachStatus = ImageCacheStatus.doing;
            }

            switch (mergedStatus) {
                // 总状态为done，可以被任意状态覆盖。
                case done:
                    mergedStatus = eachStatus;
                    break;
                // 总状态为doing，只能被failed覆盖
                case doing:
                    if (ImageCacheStatus.failed.equals(eachStatus)) {
                        mergedStatus = eachStatus;
                    }
                    break;
                default:
                    // 已经是 failed 了，不做更新
            }
        }

        if (!mergedStatus.equals(ImageCacheStatus.doing)) {
            imageCacheDao.updateTaskStatus(taskId, mergedStatus.toString());
        }
        cacheStatus.setStatus(mergedStatus);
        return cacheStatus;
    }

    private List<String> getHosts(String logicalZone) {
        List<String> hosts = new ArrayList<>();
        Map<String, List<String>> zoneMap = getZoneHostMap(cnHostConfiguration.getZoneHostMap());
        if (StringUtils.isEmpty(logicalZone)) {
            for(Map.Entry<String, List<String>> entry : zoneMap.entrySet()) {
                List<String> zoneHosts = entry.getValue();
                hosts.addAll(zoneHosts);
            }
            return hosts;
        }
        // logicalZone 转换
        ZoneMapDetail zoneMapDetail = validator.getZone(logicalZone);
        if (!zoneMap.containsKey(zoneMapDetail.getPhysicalZone())) {
            throw new PodExceptions.InvalidateZoneException();
        }

        hosts.addAll(zoneMap.get(zoneMapDetail.getPhysicalZone()));
        return hosts;
    }

    private Map<String, List<String>> getZoneHostMap(Map<String, String> zoneHostMap) {
        Map<String, List<String>> map = new HashMap<>();
        for (Map.Entry<String, String> entry : zoneHostMap.entrySet()) {
            map.put(entry.getKey(), Arrays.asList(entry.getValue().split(",")));
        }
        return map;
    }
}
