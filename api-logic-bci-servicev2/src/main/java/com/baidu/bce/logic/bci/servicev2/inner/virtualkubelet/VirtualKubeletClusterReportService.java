package com.baidu.bce.logic.bci.servicev2.inner.virtualkubelet;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.model.inner.InstanceInfo;
import com.baidu.bce.logic.bci.servicev2.model.inner.VirtualKubeletClusterCheckResultInfo;
import com.baidu.bce.logic.bci.servicev2.model.inner.VirtualKubeletClusterInfo;
import com.baidu.bce.logic.bci.servicev2.model.inner.VirtualKubeletClusterReportRequest;
import com.baidu.bce.logic.bci.servicev2.model.inner.VirtualKubeletClusterReportResponse;
import com.baidu.bce.logic.bci.servicev2.pod.BaseServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("VirtualKubeletClusterReportService")
public class VirtualKubeletClusterReportService extends BaseServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(VirtualKubeletClusterReportService.class);

    public VirtualKubeletClusterReportResponse clusterReport(VirtualKubeletClusterReportRequest request) {
        VirtualKubeletClusterReportResponse response = new VirtualKubeletClusterReportResponse();
        List<VirtualKubeletClusterInfo> clusters = request.getClusters();
        for (VirtualKubeletClusterInfo cluster : clusters) {
            VirtualKubeletClusterCheckResultInfo clusterCheckResult = new VirtualKubeletClusterCheckResultInfo();
            boolean checkResult = clusterReportCheck(cluster);
            clusterCheckResult.setCluster(cluster);
            clusterCheckResult.setCheckResult(checkResult);
            List<String> notFoundInstances = getNotFoundInstancesFromCluster(cluster);
            int notFoundInstancesCount = notFoundInstances.size();
            clusterCheckResult.setNotFoundInstancesCount(notFoundInstancesCount);
            response.getClusters().add(clusterCheckResult);
            if (checkResult) {
                LOGGER.info("clusterReport check success, VKClusterId:{}, instanceSize:{}", cluster.getClusterId(),
                        cluster.getInstances().size());
            } else {
                LOGGER.error("clusterReport check failed, VKClusterId:{}, instanceSize:{}", cluster.getClusterId(),
                        cluster.getInstances().size());
            }
            if (notFoundInstancesCount > 0) {
                LOGGER.error("clusterReport check notFoundInstances failed,"
                        + " VKClusterId:{}, instanceSize:{}, notFoundInstancesCount:{}",
                        cluster.getClusterId(),
                        cluster.getInstances().size(),
                        notFoundInstancesCount);
            } else {
                LOGGER.info("clusterReport check notFoundInstances success,"
                        + " VKClusterId:{}, instanceSize:{}, notFoundInstancesCount:{}",
                        cluster.getClusterId(),
                        cluster.getInstances().size(),
                        notFoundInstancesCount);
            }
        }
        return response;
    }

    public List<String> getNotFoundInstancesFromCluster(VirtualKubeletClusterInfo cluster) {
        List<String> notFoundInstances = new ArrayList<>();
        if (cluster == null) {
            return notFoundInstances;
        } else {
            String clusterId = cluster.getClusterId();
            if (clusterId == null || clusterId.isEmpty()) {
                LOGGER.info("clusterReportCheck VKClusterId is empty");
                return notFoundInstances;
            }
            List<InstanceInfo> instances = cluster.getInstances();
            if (instances == null) {
                LOGGER.info("clusterReportCheck VKClusterId:{}, instance is null", clusterId);
                return notFoundInstances;
            } else {
                for (InstanceInfo instanceInfo : instances) {
                    if ("NotFound".equalsIgnoreCase(instanceInfo.getStatus().getReason())) {
                        notFoundInstances.add(instanceInfo.getInstanceId());
                    }
                }
            }
        }
        return notFoundInstances;
    }

    /**
     * 检查集群信息
     * @param cluster
     * @return
     */
    private boolean clusterReportCheck(VirtualKubeletClusterInfo cluster) {
        boolean checkResult = false;
        if (cluster == null) {
            return checkResult;
        } else {
            String clusterId = cluster.getClusterId();
            if (clusterId == null || clusterId.isEmpty()) {
                LOGGER.info("clusterReportCheck VKClusterId is empty");
                return checkResult;
            }
            List<InstanceInfo> instances = cluster.getInstances();
            if (instances == null) {
                LOGGER.info("clusterReportCheck VKClusterId:{}, instance is null", clusterId);
                return checkResult;
            } else {
                checkResult = checkVKAndDBRunningPodCountEqual(clusterId, instances);
                LOGGER.info("clusterReportCheck VKClusterId:{}, instanceSize:{}, checkResult:{} ",
                        clusterId, instances.size(), checkResult);
            }
        }
        return checkResult;
    }

    private boolean checkVKAndDBRunningPodCountEqual(String clusterId, List<InstanceInfo> instances) {
        boolean checkResult = false;
        List<String> vkRunningPodIds = getVKRunningPods(instances);
        LOGGER.info("checkVKAndDBRunningPodCountEqual VKClusterId:{}, vkRunningPodSize:{}",
                clusterId, vkRunningPodIds.size());

        if (vkRunningPodIds.isEmpty()) {
            checkResult = true;
            LOGGER.info("checkVKAndDBRunningPodCountEqual success, VKClusterId:{}, vkRunningPodIds empty", clusterId);
            return checkResult;
        }

        Map<String, List<String>> dbPodStatus2PodIdMap = getPodStatus2PodIdMap(vkRunningPodIds);
        if (dbPodStatus2PodIdMap.containsKey(BciStatus.RUNNING.getStatus())) {
            List<String> dbRunningPodIds = dbPodStatus2PodIdMap.get(BciStatus.RUNNING.getStatus());
            if (dbRunningPodIds.size() == vkRunningPodIds.size()) {
                checkResult = true;
                LOGGER.info(
                        "checkVKAndDBRunningPodCountEqual success, VKClusterId:{}, VKRunningPodIdsCount:{}, DBRunningPodIdsCount:{}",
                        clusterId, vkRunningPodIds.size(), dbRunningPodIds.size());
            } else {
                checkResult = false;
                LOGGER.error(
                        "checkVKAndDBRunningPodCountEqual failed, VKClusterId:{}, VKRunningPodIdsCount:{}, DBRunningPodIdsCount:{}",
                        clusterId, vkRunningPodIds.size(), dbRunningPodIds.size());
            }
        } else {
            checkResult = false;
            LOGGER.error("checkVKAndDBRunningPodCountEqual failed, not found Running Pod from DB, VKClusterId:{}, "
                            + "VKRunningPodIdsCount:{}, DBRunningPodIdsCount:{}",
                    clusterId, vkRunningPodIds.size(), 0);
        }
        return checkResult;
    }

    private List<String> getVKRunningPods(List<InstanceInfo> instances) {
        List<String> runningPods = new ArrayList<String>();
        for (InstanceInfo instance : instances) {
            if (instance == null) {
                continue;
            }
            String podId = instance.getInstanceId();
            if (podId == null || podId.isEmpty()) {
                continue;
            }
            if (instance.getStatus() == null || instance.getStatus().getPhase().isEmpty()) {
                continue;
            }
            String podPhase = instance.getStatus().getPhase();
            if (BciStatus.RUNNING.getStatus().equalsIgnoreCase(podPhase)) {
                runningPods.add(podId);
            }
        }
        return runningPods;
    }

    private Map<String, List<String>> getPodStatus2PodIdMap(List<String> podIds) {
        List<PodPO> podPOList = podDao.getPodsByPodIds(podIds);
        Map<String, List<String>> podStatus2PodIdMap = podPOList.stream().collect(
                Collectors.groupingBy(PodPO::getStatus,
                        Collectors.mapping(PodPO::getPodId, Collectors.toList())));
        return podStatus2PodIdMap;
    }
}
