package com.baidu.bce.logic.bci.servicev2.charge;

import com.baidu.bce.logic.bci.servicev2.charge.service.PodChargePushServiceV2;
import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

/*@EnableScheduling
@Configuration("PodChargePushSchedulerV2")
@Profile("default")
public class PodChargePushSchedulerV2 {
    private String schedulerName = "PodChargePushSchedulerV2.runScheduledTask";
    @Autowired
    private PodChargePushServiceV2 podChargePushService;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    @Scheduled(cron = "0 0/1 * * * ? ")
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        podChargePushService.pushChargeTask();
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }
}*/
public class PodChargePushSchedulerV2 {
}