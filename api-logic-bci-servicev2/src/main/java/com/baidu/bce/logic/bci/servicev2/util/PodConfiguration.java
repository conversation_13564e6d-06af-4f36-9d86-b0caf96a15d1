/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.bci.servicev2.util;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import static java.util.Arrays.asList;

@Data
@Component("podConfigurationV2")
public class PodConfiguration {

    @Value("${pod.delete.status:running,failed,succeeded,unusualorder,crashed,pending}")
    private String delete;

    @Value("${pod.restart.policy:always,onfailure,never}")
    private String restartPolicy;

    @Value("${pod.image.pull.policy:Always,IfNotPresent,Never}")
    private String imagePullPolicy;

    @Value("${pod.container.containerCpu:0.25,0.5,1.0,1.5,2.0,4.0,8.0,12.0,16.0}")
    private String containerCpu;

    @Value("${pod.container.whitelist.containerCpu:0.25,0.5,1.0,1.5,2.0,4.0,8.0,12.0,16.0,32.0}")
    private String containerCpuInWhiteList;

    @Value("${pod.container.cpu.memory.min.ratio:1.0}")
    private Float minRatio;

    @Value("${pod.container.cpu.memory.max.ratio:8.0}")
    private Float maxRatio;

    @Value("${pod.container.memory.step.length:0.5}")
    private Float stepLength;

    @Value("${pod.volume.mount.type:NFS,EmptyDir,PodConfigFile}")
    private String volumeMountTypes;

    @Value("${pod.instance.type:}")
    private String podInstanceTypes;

    // 从下单开始算，同步到订单的超时报警时间，默认10分钟，单位：秒
    @Value("${order.ready.timeout:600}")
    private Integer orderReadyTimeout;

    @Value("${pod.image.repo.pattern:^(((hub|registry|(ccr-[^/]+-(pub|vpc).cnc\\.[^/]+))\\.baidubce\\.com(/[^/]*)+)" +
            "/|)([^/]+/)?[^/]+$}")
    private String imageRepoPattern;

    @Value("${pod.container.max.num:15}")
    private Integer maxContainerNum;

    @Value("${pod.root.size.gb:3}")
    private Integer rootSizeInGB;

    @Value("${free.charge.size.gb:13}")
    private Integer freeChargeSizeInGB;

    @Value("${cds.volume.size.gb.min:1}")
    private Integer cdsVolumeSizeMin;

    @Value("${cds.volume.size.gb.max:2000}")
    private Integer cdsVolumeSizeMax;

    @Value("${bci.cpumem.specification:" +
            "0.25:0.5,1,2;0.5:1,2,3,4;1:1,2,4,8;2:4,8,16;4:8,16,32;8:16,32;12:24,48;16:32,64;32:64,128}")
    private String cpuMemSpecification; // CPU:memlist;

    @Value("${bci.cpumem.exclusive.specifition:32:64,128}")
    private String exclusiveCpuMemSpecification;

    // 卡类型|卡数|cpu:mem,mem;cpu:mem,mem
    // 配置的时候，相同卡类型，从上到下卡数量增加；cpu:mem从左到右增加（格式跟cpu套餐保持一致）
    @Value("${gpu.specification:Nvidia A100 Nvswitch-80g|1|14:120,Nvidia A100 Nvswitch-80g|2|28:240," +
            "Nvidia A100 Nvswitch-80g|4|56:480,Nvidia A100 Nvswitch-80g|8|112:960,Nvidia A100 Nvswitch-40g|8|112:896," +
            "Nvidia A10 PCIE|1|8:36;18:74;28:112;38:150,Nvidia A10 PCIE|2|30:118," +
            "Nvidia A10 PCIE|4|62:240," +
            "Nvidia A30 PCIE|1|28:112,Nvidia A30 PCIE|2|56:224,Nvidia A30 PCIE|4|112:476,KUNLUN-R200|1|16:64," +
            "KUNLUN-R200|2|32:128,KUNLUN-R200|4|64:256,Nvidia V100 Nvlink-32g|1|10:80," +
            "Nvidia V100 Nvlink-32g|2|20:160,Nvidia V100 Nvlink-32g|4|40:320,Nvidia V100 Nvlink-32g|8|80:640," +
            "Nvidia T4 PCIE|1|20:80,Nvidia T4 PCIE|2|40:160,Nvidia T4 PCIE|4|80:320,Nvidia RTX 3070|1|10:40," +
            "Nvidia RTX 3070|2|20:80,Nvidia RTX 3070|4|40:160,Nvidia RTX 3070|8|80:320,Nvidia RTX 3080|1|10:40," +
            "Nvidia RTX 3080|2|20:80,Nvidia RTX 3080|4|40:160,Nvidia RTX 3080|8|80:320,Nvidia RTX 3090|1|8:36," +
            "Nvidia RTX 3090|2|18:74,Nvidia RTX 3090|4|38:150,Nvidia RTX 3090|8|78:302," +
            "Nvidia RTX 4090|8|128:476,Nvidia RTX 4090|8|128:978}")
    private String gpuSpecification;
    
    @Value("${bci.billing.gpu.spec.zone.enabled.white_list:bci.gn5rc.c128m476.8g4090}")
    private String billingGpuSpecZoneEnabledWhiteList;
    
    // pod:"gputype|gpuCount|cpu|mem", bcc spec
    private Map<String, String> gpuBCCSpecMap = new HashMap<String, String>(){{
        put("Nvidia A100 Nvswitch-80g|1|14|120", "bcc.gn5.c14m120.1A100-80g");
        put("Nvidia A100 Nvswitch-80g|2|28|240", "bcc.gn5.c28m240.2A100-80g");
        put("Nvidia A100 Nvswitch-80g|4|56|480", "bcc.gn5.c56m480.4A100-80g");
        put("Nvidia A100 Nvswitch-80g|8|112|960", "bcc.gn5.c112m960.8A100-80g");
        put("Nvidia A100 Nvswitch-40g|8|112|896", "bcc.gn5.c112m896.8A100-40g");
        // 后续需要区分amd和intel
        put("Nvidia A10 PCIE|1|8|38", "bci.gna2.c8m38.1a10");
        put("Nvidia A10 PCIE|2|30|126", "bci.gn5.c30m126.2a10");
        put("Nvidia A10 PCIE|4|62|254", "bci.gn5.c62m254.4a10");

        put("Nvidia A10 PCIE|1|8|36", "bci.gna2.c8m36.1a10");
        put("Nvidia A10 PCIE|1|18|74", "bci.gna2.c18m74.1a10");
        put("Nvidia A10 PCIE|1|28|112", "bci.gna2.c28m112.1a10");
        put("Nvidia A10 PCIE|1|38|150", "bci.gna2.c38m150.1a10");
        put("Nvidia A10 PCIE|2|30|118", "bci.gna2.c30m118.2a10");
        put("Nvidia A10 PCIE|4|62|240", "bci.gna2.c62m240.4a10");

        put("Nvidia A30 PCIE|1|28|112", "bcc.gn5.c28m112.1a30");
        put("Nvidia A30 PCIE|2|56|224", "bcc.gn5.c56m224.2a30");
        put("Nvidia A30 PCIE|4|112|476", "bcc.gn5.c112m476.4a30");
        put("KUNLUN-R200|1|16|64", "bcc.nkl5.c16m64.1r200");
        put("KUNLUN-R200|2|32|128", "bcc.nkl5.c32m128.2r200");
        put("KUNLUN-R200|4|64|256", "bcc.nkl5.c64m256.4r200");
        put("Nvidia V100 Nvlink-32g|1|10|80", "bcc.gn3.c10m80.1v100-32g");
        put("Nvidia V100 Nvlink-32g|2|20|160", "bcc.gn3.c20m160.2v100-32g");
        put("Nvidia V100 Nvlink-32g|4|40|320", "bcc.gn3.c40m320.4v100-32g");
        put("Nvidia V100 Nvlink-32g|8|80|640", "bcc.gn3.c80m640.8v100-32g");
        put("Nvidia T4 PCIE|1|20|80", "bcc.gn3.c20m80.1t4");
        put("Nvidia T4 PCIE|2|40|160", "bcc.gn3.c40m160.2t4");
        put("Nvidia T4 PCIE|4|80|320", "bcc.gn3.c80m320.4t4");
        put("Nvidia RTX 3070|1|10|40", "bcc.gn3.c10m40.1g3070l");
        put("Nvidia RTX 3070|2|20|80", "bcc.gn3.c20m80.2g3070l");
        put("Nvidia RTX 3070|4|40|160", "bcc.gn3.c40m160.4g3070l");
        put("Nvidia RTX 3070|8|80|320", "bcc.gn3.c80m320.8g3070l");
        put("Nvidia RTX 3080|1|10|40", "bcc.gn3.c10m40.1g3080l");
        put("Nvidia RTX 3080|2|20|80", "bcc.gn3.c20m80.2g3080l");
        put("Nvidia RTX 3080|4|40|160", "bcc.gn3.c40m160.4g3080l");
        put("Nvidia RTX 3080|8|80|320", "bcc.gn3.c80m320.8g3080l");
        put("Nvidia RTX 3090|1|8|36", "bci.c8m36.1g3090");
        put("Nvidia RTX 3090|2|18|74", "bci.c18m74.2g3090");
        put("Nvidia RTX 3090|4|38|150", "bci.c38m150.4g3090");
        put("Nvidia RTX 3090|8|78|302", "bci.c78m302.8g3090");
        put("Nvidia RTX 4090|8|128|476", "bci.gn5rc.c128m476.8g4090");
        put("Nvidia RTX 4090|8|128|978", "bci.lgn5rcm.c128m978.8g4090.1d");
    }};

    public static Map<String, Integer> gpuSpecGpuMemoryMap = new HashMap<String, Integer>(){{
        put("Nvidia A10 PCIE", 24);
        put("Nvidia A100 Nvswitch-40g", 40);
        put("Nvidia A100 Nvswitch-80g", 80);
        put("Nvidia A30 PCIE", 24);
        put("Nvidia RTX 3070", 8);
        put("Nvidia RTX 3080", 12);
        put("Nvidia RTX 3090", 24);
        put("Nvidia RTX 4090", 8);
        put("Nvidia T4 PCIE", 16);
        put("Nvidia V100 Nvlink-32g", 32);
        put("KUNLUN-R200", 16);
    }};

    // gpu卡类型与cce资源名称对应关系
    // 参考cce：https://cloud.baidu.com/doc/CCE/s/Pkp7ygb1u
    private Map<String, String> gpuSpecK8sResourceMap = new HashMap<String, String>(){{
        put("Nvidia A10 PCIE", "baidu.com/a10_24g_cgpu");
        put("Nvidia A100 Nvswitch-40g", "baidu.com/a100_40g_cgpu");
        put("Nvidia A100 Nvswitch-80g", "baidu.com/a100_80g_cgpu");
        put("Nvidia A30 PCIE", "baidu.com/a30_24g_cgpu");
        put("Nvidia RTX 3070", "baidu.com/rtx_3070_cgpu");
        put("Nvidia RTX 3080", "baidu.com/rtx_3080_cgpu");
        put("Nvidia RTX 3090", "baidu.com/rtx_3090_cgpu");
        put("Nvidia RTX 4090", "baidu.com/rtx_4090_cgpu");
        put("Nvidia T4 PCIE", "baidu.com/t4_16g_cgpu");
        put("Nvidia V100 Nvlink-32g", "baidu.com/v100_32g_cgpu");
        put("KUNLUN-R200", "baidu.com/R200_cxpu");
    }};

    @Value("${bid.product.type:bidding}")
    private String bidProductType;

    @Value("${bid.model.market:MARKET_PRICE_BID}")
    private String bidModelMarket;

    @Value("${bid.model.custom:CUSTOM_BID}")
    private String bidModelCustom;

    @Value("${bid.protected.period.min:60}")
    private int bidProtectedPeriodMin;

    @Value("${bid.preemt.wait.sec:270}")
    private int bidPreemtWaitSec;

    @Value("${bid.preempted.clear.hour:24}")
    private int bidPreemptedClearHour;

    public List<String> getPodInstanceTypeList() {
        return Arrays.asList(podInstanceTypes.split(","));
    }

    private Map<Float, List<Float>> biddingCpuSpecification = new HashMap<Float, List<Float>>(){{
        put(1F, asList(1F, 2F));
        put(2F, asList(2F, 4F, 8F, 16F));
        put(4F, asList(8F, 16F, 32F));
        put(8F, asList(16F, 32F));
        put(16F, asList(32F, 64F));
        put(32F, asList(64F, 128F));
    }};

}
