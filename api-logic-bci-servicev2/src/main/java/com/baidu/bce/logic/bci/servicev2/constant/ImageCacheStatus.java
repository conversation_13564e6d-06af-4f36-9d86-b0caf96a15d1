package com.baidu.bce.logic.bci.servicev2.constant;

public enum ImageCacheStatus { 

    CREATING("creating", "创建中"),
    PARTIAL("partialSuccess", "部分成功"),
    SUCCESS("success", "创建成功"),
    FAILED("failed","创建失败");

    private String name;
    private String status;

    ImageCacheStatus(String name, String status) {
        this.name = name;
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public String getStatus() {
        return status;
    }

    public static String getStatusByName(String name) {
        for (ImageCacheStatus imageCacheStatus : values()) {
            if (imageCacheStatus.getName().equalsIgnoreCase(name)) {
                return imageCacheStatus.getStatus();
            }
        }
        return name;
    }
}