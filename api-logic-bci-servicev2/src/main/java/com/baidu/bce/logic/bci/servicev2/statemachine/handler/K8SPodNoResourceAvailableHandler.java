package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.constant.BciSubStatus;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.K8SPodNoResourceAvailableContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("K8S_POD_NO_RESOURCE_AVAILABLE")
public class K8SPodNoResourceAvailableHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(K8SPodNoResourceAvailableHandler.class);

    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof K8SPodNoResourceAvailableContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        //
        return true;
    }

    /**
     * 1. resourceVersion 更新为 k8S pod resourceVersion
     * 2. 更新pod的bci资源版本号,resource_version and bci_resource_version
     * @return
     */
    @Override
    public boolean execute() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        PodPO podPO = getPodPO();
        podPO.setSubStatus(BciSubStatus.STATUS_DEFAULT);
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        int result = podDao.stateMachineK8SPodNoResourceAvailable(podPO, podDaoContext);
        if (result == 0) {
            return false;
        }
        return true;
    }
}

