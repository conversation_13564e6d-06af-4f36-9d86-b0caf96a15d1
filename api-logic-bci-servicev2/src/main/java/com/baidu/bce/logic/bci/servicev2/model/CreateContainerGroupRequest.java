package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CreateContainerGroupRequest {
    // BCI实例名称，即容器组名称
    private String name = "";

    // 可用区ID
    private String zoneName = "";

    // 实例所属于的安全组Id
    private List<String> securityGroupIds;
    // 实例所属的子网ID
    private List<String> subnetIds;

    // 实例重启策略
    private String restartPolicy = "";

    // 弹性公网IP
    private String eipIp = "";
    // Eip名称
    private String eipName = "eip";
    // 是否自动创建一个EIP，并绑定到BCI实例上。
    private Boolean autoCreateEip = false;
    // EIP线路类型，包含标准BGP（BGP）和增强BGP（BGP_S），默认标准BGP。
    private String eipRouteType = "BGP";
    // 公网带宽，单位为Mbps。对于预付费以及按使用带宽计费的后付费EIP
    // 标准型BGP限制为1~500之间的整数，增强型BGP限制为100~5000之间的整数（代表带宽上限）；
    // 对于按使用流量计费的后付费EIP，标准型BGP限制为1~200之间的整数（代表允许的带宽流量峰值）
    private Integer eipBandwidthInMbps = 100;
    // 付款时间，预支付（Prepaid）和后支付（Postpaid）
    private String eipPaymentTiming = "Postpaid";
    // 计费方式，按流量（ByTraffic）、按带宽（ByBandwidth）、按增强95（ByPeak95）（只有共享带宽后付费支持）
    private String eipBillingMethod = "ByTraffic";
    // 支持创建 EIP同时开通自动续费，取值为 month 获 year。
    private String eipAutoRenewTimeUnit;
    // 支持创建 EIP同时开通自动续费，根据autoRenewTimeUnit的取值有不同的范围，month 为1到9，year 为1到3。
    private int eipAutoRenewTime;

    // cpu and gpu
    // cpu型号
    private String cpuType;
    // cpu数量:vCPU大小。单位：核。暂不支持
    // private float cpu;
    // 卡类型，只支持独占售卖，后续共享在继续增加新字段，比如radio或者归一化的算例值
    private String gpuType;
    // 卡数量，支持<1，也支持多卡（>=1需要是整数），算例显存相同比例售卖，当前没有归一化值，暂不支持
    // private float gpu;
    // 实例级别内存大小。单位：GiB。暂不支持
    // private float memory;

    // 程序的缓冲时间，用于处理关闭之前的操作。
    private Long terminationGracePeriodSeconds;

    // 主机名称
    private String hostName = "";

    // 用户标签列表。
    private List<Tag> tags;
 
    // 镜像仓库信息
    private List<ImageRegistryCredential> imageRegistryCredentials;
    // 业务容器组
    private List<Container> containers;
    // Init 容器
    private List<Container> initContainers;

    // 数据卷信息
    private Volume volume;

    private PodPurchaseRequest convertToPodPurchaseRequest() {
        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest(this);
        return podPurchaseRequest;
    }

    private EipPurchaseRequest convertToEipPurchaseRequest() {
        EipPurchaseRequest eipPurchaseRequest = new EipPurchaseRequest(this);
        return eipPurchaseRequest;
    }

    public BaseCreateOrderRequestVo<IOrderItem> convertToBaseCreateOrderRequest() {
        BaseCreateOrderRequestVo<IOrderItem> baseCreateOrderRequest = new BaseCreateOrderRequestVo<>();
        
        BaseCreateOrderRequestVo.Item<IOrderItem> itemPodPurchaseRequest = new BaseCreateOrderRequestVo.Item<>();
        PodPurchaseRequest podPurchaseRequest = this.convertToPodPurchaseRequest();
        itemPodPurchaseRequest.setConfig(podPurchaseRequest);
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items =
            new ArrayList<BaseCreateOrderRequestVo.Item<IOrderItem>>();
        items.add(itemPodPurchaseRequest);

        if (this.getEipIp().isEmpty() && this.getAutoCreateEip()) {
            BaseCreateOrderRequestVo.Item<IOrderItem> itemEipPurchaseRequest = new BaseCreateOrderRequestVo.Item<>();
            EipPurchaseRequest eipPurchaseRequest = this.convertToEipPurchaseRequest();
            itemEipPurchaseRequest.setConfig(eipPurchaseRequest);
            items.add(itemEipPurchaseRequest);
        }
        baseCreateOrderRequest.setItems(items);
        return baseCreateOrderRequest;
    }
}