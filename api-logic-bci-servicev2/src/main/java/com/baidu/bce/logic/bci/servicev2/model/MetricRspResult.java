package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MetricRspResult {
    private String podShortId;
    private List<Metric> metrics;

    public MetricRspResult(String podShortId, List<Metric> metrics) {
        this.podShortId = podShortId;
        this.metrics = metrics;
    }
}