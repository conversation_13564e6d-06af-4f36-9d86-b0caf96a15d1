package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeRollbackDbRequest;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceIds;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.BciSubStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.statemachine.StateMachine;
import com.baidu.bce.logic.bci.servicev2.util.CacheUtil;
import com.baidu.bce.logic.bci.servicev2.util.EipUtil;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.plat.webframework.exception.BceException;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1Pod;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Service("PodInBuildSyncServiceV2")
public class PodInBuildSyncServiceV2 extends SyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodInBuildSyncServiceV2.class);

    private static final int RETRY_NUM = 3;

    @Value("${order.automatic.execute.enable:false}")
    private boolean orderAutomaticExecuteEnable;

    // 测试线程阻塞场景使用
    @Value("${bci.sync.podInBuild.delaySecondsPerRound:1}")
    private long syncPodInBuildDelaySecondsPerRound;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    @Qualifier("orderSyncAccelerateThreadPoolTaskExecutorV2")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Autowired
    private EipUtil eipUtil;

    @Autowired
    private StateMachine stateMachine;

    private long syncPodInBuildRunTimes = 0;

    @Autowired
    private PodExtraDaoV2 podExtraDaoV2;

    private CacheUtil<String, String> orderIsProcessing = new CacheUtil<>(20 * 1000L);

    public void syncPodInBuild() {
        syncPodInBuildRunTimes++;
        try {
            Map<String, List<PodPO>> unsyncPodMap = getInSpecifiedStatusPodsMap(PodConstants.BCI_INTERNAL_STATUS_UNSYNC);
            if (unsyncPodMap == null || unsyncPodMap.isEmpty()) {
                Thread.sleep(syncPodInBuildDelaySecondsPerRound * 1000L);
                LOGGER.debug("syncPodInBuild runTimes:{}, unsyncPodMap is empty", syncPodInBuildRunTimes);
                return;
            }
            LOGGER.debug("syncPodInBuild runTimes:{}, unsyncPodMap size:{}", syncPodInBuildRunTimes, unsyncPodMap.size());
            for (String orderId : unsyncPodMap.keySet()) {
                List<String> unsyncPodIds = getUnSyncPodIds(unsyncPodMap.get(orderId));
                if (orderIsProcessing.get(orderId) != null) {
                    LOGGER.debug("orderId:{} has push to processing threadPoll, skip", orderId);
                    continue;
                }
                orderIsProcessing.put(orderId, unsyncPodIds.get(0));
                LOGGER.debug("begin syncPodInBuild runTimes:{}, orderId:{} unsyncPodIds:{}",
                        syncPodInBuildRunTimes, orderId, unsyncPodIds);
                threadPoolTaskExecutor.execute(new SyncOrder(stateMachine, orderId, unsyncPodMap.get(orderId), this));
                LOGGER.debug("end syncPodInBuild runTimes:{}, orderId:{} unsyncPodIds:{}",
                        syncPodInBuildRunTimes, orderId, unsyncPodIds);
            }
        } catch (Exception e) {
            LOGGER.error("syncPodInBuild runTimes:{} error ", syncPodInBuildRunTimes, e);
        }
    }

    private List<String> getUnSyncPodIds(List<PodPO> podPOList) {
        List<String> unSyncPodIds = new ArrayList<>();
        for (PodPO podPO : podPOList) {
            unSyncPodIds.add(podPO.getPodId());
        }
        return unSyncPodIds;
    }

    private Map<String, List<PodPO>> getInSpecifiedStatusPodsMap(String status) {
        List<PodPO> pods = podDao.listByInternalStatus(status);
        if (CollectionUtils.isEmpty(pods)) {
            return null;
        }
        Map<String, List<PodPO>> result = new HashMap<>();
        for (PodPO podPO : pods) {
            if (!result.containsKey(podPO.getOrderId())) {
                result.put(podPO.getOrderId(), new ArrayList<PodPO>());
            }
            result.get(podPO.getOrderId()).add(podPO);
        }
        return result;
    }

    class SyncOrder implements Runnable {
        private String orderId;
        private List<PodPO> podPOList;

        private StateMachine stateMachine;

        private PodInBuildSyncServiceV2 podInBuildSyncServiceV2;

        public SyncOrder(StateMachine stateMachine, String orderId, List<PodPO> podPOList, PodInBuildSyncServiceV2 podInBuildSyncServiceV2) {
            this.stateMachine = stateMachine;
            this.orderId = orderId;
            this.podPOList = podPOList;
            this.podInBuildSyncServiceV2 = podInBuildSyncServiceV2;
        }

        @Override
        public void run() {
            BceInternalRequest.setThreadRequestId(PodUtils.createLongUuid());
            synchronizeBuildStatusPodData(orderId, podPOList);
            podInBuildSyncServiceV2.getOrderIsProcessing().delete(orderId);
        }

        private void synchronizeBuildStatusPodData(String orderId, List<PodPO> podPOS) {
            List<String> podIds = podPOS.stream().map(PodPO::getPodId).collect(Collectors.toList());
            LOGGER.debug("SyncOrder order {} pod size {} pods {} from cce cluster",
                    orderId, podPOS.size(), JsonUtil.toJSON(podIds));

            OrderClient orderClient = logicPodClientFactory.createOrderClient();
            ResourceClient resourceClient = logicPodClientFactory.createResourceClient();
            if (CollectionUtils.isEmpty(podPOS)) {
                LOGGER.debug("SyncOrder empty pod list order {}", orderId);
                return;
            }
            // 当前先使用第一个pod的accountID
            String accountID = podPOS.get(0).getUserId();
            Order bciOrder;
            try {
                if (StringUtils.isEmpty(orderId)) {
                    // orderID 是空
                    LOGGER.debug("SyncOrder orderID:{} is empty", orderId);
                    return;
                }
                bciOrder = orderClient.get(orderId);
                if (bciOrder == null || !orderId.equals(bciOrder.getUuid())) {
                    LOGGER.error("SyncOrder error: Query order return null or bciOrder.getUuid() not match with " +
                                    "orderId, order id is {}",
                            orderId);
                    return;
                }
            } catch (Exception e) {
                LOGGER.error("SyncOrder error, order {}, exception: {}", orderId, e);
                return;
            }
//            order.getAccountId(): 主账号ID
//            order.getUserId(): 子账号ID
            podNewOrderExecutorServiceV2.fillOrderItemExtraWithPodExtra(bciOrder);
            OrderStatus orderStatus = bciOrder.getStatus();
            if (orderAutomaticExecuteEnable && orderStatus == OrderStatus.READY_FOR_CREATE) {
                LOGGER.debug("SyncOrder order:{} process execute begin", orderId);
                ExecutionResult executionResult = podNewOrderExecutorServiceV2.execute(orderClient, null, bciOrder,
                        PodNewOrderExecutorServiceV2.BCILOGIC_SELF_ORDER_SYNC_TASK);
                ExecutionStatus executionStatus = executionResult.getExecutionStatus();
                if (executionStatus == ExecutionStatus.FAILURE) {
                    LOGGER.error("SyncOrder order:{} process execute error:{}, detail:{}", orderId,
                            executionResult.getFaultTrace().getSummary(),
                            executionResult.getFaultTrace().getDetail());
                    String podResourceRecycleReason = !executionResult.getFaultTrace().getSummary().isEmpty() ?
                            executionResult.getFaultTrace().getSummary() : ResourceRecycleReason.ORDER_SYNC_EXECUTE_FAIL.toString();
                    processOrderFailed(accountID, orderId, podResourceRecycleReason);
                    return;
                }
                LOGGER.debug("SyncOrder order:{} process execute end", orderId);
                LOGGER.debug("SyncOrder order:{} process check begin", orderId);
                podNewOrderExecutorServiceV2.check(orderClient, null, bciOrder,
                        PodNewOrderExecutorServiceV2.BCILOGIC_SELF_ORDER_SYNC_TASK);
                executionStatus = executionResult.getExecutionStatus();
                if (executionStatus == ExecutionStatus.FAILURE) {
                    LOGGER.error("SyncOrder order:{} process check with myself error:{}, detail:{}", orderId,
                            executionResult.getFaultTrace().getSummary(),
                            executionResult.getFaultTrace().getDetail());
                    String podResourceRecycleReason = !executionResult.getFaultTrace().getSummary().isEmpty() ?
                            executionResult.getFaultTrace().getSummary() :
                            ResourceRecycleReason.ORDER_SYNC_CHECK_FAIL.toString();
                    processOrderFailed(accountID, orderId, podResourceRecycleReason);
                }
                LOGGER.debug("SyncOrder order:{} process check end", orderId);
                return;
            } else if (orderAutomaticExecuteEnable && orderStatus == OrderStatus.CREATING) {
                LOGGER.debug("SyncOrder order:{} process check begin", orderId);
                ExecutionResult executionResult = podNewOrderExecutorServiceV2.check(orderClient, null, bciOrder,
                        PodNewOrderExecutorServiceV2.BCILOGIC_SELF_ORDER_SYNC_TASK);
                ExecutionStatus executionStatus = executionResult.getExecutionStatus();
                if (executionStatus == ExecutionStatus.FAILURE) {
                    LOGGER.error("SyncOrder order:{} process check error:{}, detail:{}", orderId,
                            executionResult.getFaultTrace().getSummary(),
                            executionResult.getFaultTrace().getDetail());
                    String podResourceRecycleReason = !executionResult.getFaultTrace().getSummary().isEmpty() ?
                            executionResult.getFaultTrace().getSummary() :
                            ResourceRecycleReason.ORDER_SYNC_CHECK_FAIL.toString();
                    processOrderFailed(accountID, orderId, podResourceRecycleReason);
                }
                LOGGER.debug("SyncOrder order:{} process check end", orderId);
                return;
            }
            // 创建失败，删除本地资源：将所有订单id为orderId的资源都删除，包括pod和container
            if (orderStatus == OrderStatus.CREATE_FAILED || orderStatus == OrderStatus.REFUND_SUCC
                    || orderStatus == OrderStatus.REFUND_FAILED || orderStatus == OrderStatus.EXPIRED
                    || orderStatus == OrderStatus.CANCELLED) {
                LOGGER.error("SyncOrder order:{} status abnormal status is {}", orderId, orderStatus);
                // TODO pod 因为eni或sidecar启动失败，pod phase置为pending，订单check 超时后会把订单置为CREATE_FAILED，进入此逻辑
                //  pod+container 在db中被删除，在vk 端展示pod 为notFound，可能会对用户造成疑惑，后续统一处理重构逻辑；
                // ********: 订单异常后,将order对应的Pod标记为需要进行资源回收
                // ********: 订单异常后,将order对应的Pod标记为sycned
                // ********: 订单失败后,由状态机扭转pod状态
                processOrderFailed(accountID, orderId, ResourceRecycleReason.ORDER_SYNC_STATUS_ABNORMAL.toString());
            } else {
                LOGGER.debug("SyncOrder order {} is normal, status is {}", orderId, orderStatus);
            }

            if (orderStatus == OrderStatus.CREATED) {
                try {
                    updatePodStatus(bciOrder, resourceClient, podPOS);
                    LOGGER.debug("SyncOrder finish sync order {} pod from cce cluster and update pod " +
                            "to running status", orderId);
                } catch (Exception e) {
                    LOGGER.error("SyncOrder update pod status failed after query order, order: {}, exception is {}",
                            bciOrder.getId(), e);
                }
            }
        }

        private void processOrderFailed(String accountId, String orderId, String resourceRecycleReason) {
            LOGGER.debug("processOrderFailed accountId:{}, order:{}, resourceRecycleReason:{}",
                    accountId, orderId, resourceRecycleReason);
            try {
                List<PodPO> podPOS = podDao.listByOrderId(accountId, orderId);
                LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(accountId);
                // pod的internal_status为synced状态
                if (CollectionUtils.isNotEmpty(podPOS)) {
                    for (PodPO podPO : podPOS) {
                        String podId = podPO.getPodId();
                        // release eip if exist
                        if (StringUtils.isNotEmpty(podPO.getPublicIp())) {
                            LOGGER.debug("processOrderFailed delete podId:{} eip:{} order:{} accountId:{}",
                                    podId, podPO.getPublicIp(), orderId, accountId);
                            boolean reclcleEipResult = eipUtil.recycleEip(logicEipClient, podPO);
                            if (!reclcleEipResult) {
                                LOGGER.error("processOrderFailed recycle eip failed." +
                                                "podId:{} eip:{} internalIp:{} order:{} accountId:{}",
                                        podId, podPO.getPublicIp(), podPO.getInternalIp(), orderId, accountId);
                            }
                        }
                        podPO.setInternalStatus(PodConstants.BCI_INTERNAL_STATUS_SYNCED);
                        if (podPO.getStatus().equalsIgnoreCase(BciStatus.PENDING.getStatus())) {
                            podPO.setStatus(BciStatus.FAILED.getStatus());
                        }
                        podPO.setSubStatus(BciSubStatus.STATUS_DEFAULT);
                        if (podPO.getResourceRecycleTimestamp() == 0L) {
                            podPO.setResourceRecycleTimestamp(System.currentTimeMillis());
                            podPO.setResourceRecycleReason(resourceRecycleReason);
                        }
                        if (!stateMachine.trigger(podPO, StateMachineEvent.ORDER_FAILED, null)) {
                            LOGGER.error("processOrderFailed: pod state machine trigger failed, podId:{} event:{}",
                                    podPO.getPodId(), StateMachineEvent.ORDER_FAILED.toString());
                        }
                        LOGGER.info("processOrderFailed: pod state machine trigger success, podId:{} event:{}",
                                podPO.getPodId(), StateMachineEvent.ORDER_FAILED.toString());
                    }
                }
            } catch (Exception e) {
                LOGGER.debug("processOrderFailed error for accountId:{}, order:{} exception:{}",
                        accountId, orderId, e);
            }
        }

        private void markResourceRecyclePodAndContainers(
                String accountId, List<PodPO> podPOS, Map<String, String> podResourceRecycleReasonMap) {
            LOGGER.debug("markResourceRecyclePodAndContainers pods size:{}, pods:{}, order:{}, " +
                            "podResourceRecycleReasonMap:{}",
                    podPOS.size(), JsonUtil.toJSON(podPOS),
                    orderId, JsonUtil.toJSON(podResourceRecycleReasonMap));
            try {
                LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(accountId);
                for (PodPO podPO : podPOS) {
                    try {
                        LOGGER.debug("markResourceRecyclePodAndContainers pod:{} and its containers, order:{}",
                                podPO.getPodId(), orderId);
                        // release eip if exist
                        if (StringUtils.isNotEmpty(podPO.getPublicIp())) {
                            LOGGER.debug("markResourceRecyclePodAndContainers delete eip:{}, order:{}",
                                    podPO.getPublicIp(), orderId);
                            boolean reclcleEipResult = eipUtil.recycleEip(logicEipClient, podPO);
                            if (!reclcleEipResult) {
                                LOGGER.error("markResourceRecyclePodAndContainers recycle eip failed." +
                                                " eip:{} pod:{} internalIp:{}, order:{}",
                                        podPO.getPublicIp(), podPO.getPodId(), podPO.getInternalIp(), orderId);
                            }
                        }
                        // 标记pod需要被回收,但不修改pod的状态和删除状态
                        // 不需要删除container表中的记录
                        if (podPO.getResourceRecycleTimestamp() != 0L) {
                            LOGGER.error("markResourceRecyclePodAndContainers ignore markResourceRecyclePod because " +
                                            "pod:{} orderId:{} already marked by another resourceRecycleTimestamp:{}, " +
                                            "resourceRecycleReason:{}",
                                    podPO.getPodId(), orderId,
                                    podPO.getResourceRecycleTimestamp(),
                                    podPO.getResourceRecycleReason());
                            return;
                        }
                        if (podPO.getResourceRecycleTimestamp() == 0L) {
                            podPO.setResourceRecycleTimestamp(System.currentTimeMillis());
                            podPO.setResourceRecycleReason(podResourceRecycleReasonMap.get(podPO.getPodId()));
                            LOGGER.debug("markResourceRecyclePodAndContainers update pod:{} order:{}" +
                                            "resourceRecycleTimestamp:{} resourceRecycleReason:{}",
                                    podPO.getPodId(), podPO.getOrderId(),
                                    podPO.getResourceRecycleTimestamp(),
                                    podPO.getResourceRecycleReason());
                        }
                        int markResult = podDao.markResourceRecyclePod(podPO);
                        if (markResult == 0) {
                            LOGGER.warn("markResourceRecyclePodAndContainers pod:{} failed for order:{}",
                                    podPO.getPodId(), orderId);
                        }
                    } catch (Exception e) {
                        LOGGER.error("markResourceRecyclePodAndContainers pod:{} error, order:{}, exception:{}",
                                podPO.getPodId(), orderId, e);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("markResourceRecyclePodAndContainers pod error for order:{}, exception:{}", orderId, e);
            }
        }

        private void updatePodStatus(Order order, ResourceClient resourceClient,
                                     List<PodPO> podPOS) {
            if (CollectionUtils.isEmpty(podPOS)) {
                LOGGER.debug("updatePodStatus empty pod list");
                return;
            }
            // 暂时使用第一个pod的accountID
            String accountID = podPOS.get(0).getUserId();

            List<String> resourceIds = order.getResourceIds();
            if (resourceIds == null || resourceIds.isEmpty()) {
                return;
            }
            List<Resource> podResource = getPodResources(resourceIds, resourceClient);

            // 映射podUid->k8sPod
            Map<String, V1Pod> podMap = getPodUuidMap(podPOS);
            // 映射podId->dbPod
            Map<String, PodPO> podPOMap = getPodIDMap(podPOS);
            List<String> podIds = podPOMap.keySet().stream().collect(Collectors.toList());
            List<PodExtraPO> podExtraPOs = podExtraDaoV2.getPodExtraByPodIds(accountID, podIds);
            Map<String, PodExtraPO> podId2PodExtraPOMap = new HashMap<>();
            for (PodExtraPO podExtraPO : podExtraPOs) {
                podId2PodExtraPOMap.put(podExtraPO.getPodId(), podExtraPO);
            }

            ArrayList<PodPO> needResourceRecyclePodList = new ArrayList<>();
            Map<String, String> podResourceRecycleReasonMap = new HashMap<>();
            for (int i = 0, size = podResource.size(); i < size; i++) {
                Resource resource = podResource.get(i);
                // 根据resource的name获取k8s的pod信息(resource.name == k8s.uid)
                V1Pod pod = podMap.get(resource.getName());
                // 根据resource的shortID获取pod在db的信息(resource.shortId == dbPod.podId)
                PodPO podPO = podPOMap.get(resource.getShortId());
                PodExtraPO podExtraPO = podId2PodExtraPOMap.get(podPO.getPodId());
                // 资源状态为RUNNING -> pod状态为Running/Succeed(需要收费)
                // 资源状态为INVALID -> pod不存在/pod状态为pending等(需要删除) + pod状态为failed(保留)
                podPO.setResourceUuid(resource.getUuid());
                podExtraPO.setResourceUuid(resource.getUuid());

                if (pod != null) {
                    if (pod.getMetadata() != null) {
                        podPO.setPodUuid(pod.getMetadata().getUid());
                    }
                    if (pod.getStatus() != null) {
                        podPO.setInternalIp(pod.getStatus().getPodIP());
                    }
                    if (pod.getSpec() != null) {
                        podPO.setPodVolumes(JsonUtil.toJSON(pod.getSpec().getVolumes()));
                        // 更新node name到pod db，查询竞价实例的状态
                        podPO.setNodeName(pod.getSpec().getNodeName());
                    }
                }

                if (resource.getStatus() != ResourceStatus.RUNNING) {
                    // 标记pod internal status为synced
                    podPO.setInternalStatus(PodConstants.BCI_INTERNAL_STATUS_SYNCED);
                    // Pod在底层K8S不存在
                    if (pod == null) {
                        // 标记pod需要被资源回收
                        needResourceRecyclePodList.add(podPO);
                        podResourceRecycleReasonMap.put(podPO.getPodId(), ResourceRecycleReason.BILLING_RESOURCE_STATUS_ABNORMAL.toString());
                        continue;
                    } else {
                        // Pod在底层K8S存在
                        // 1. Pod在底层K8S状态是Succeeded or Pod在数据库中的状态是Succeeded
                        if (pod.getStatus().getPhase().equalsIgnoreCase(BciStatus.SUCCEEDED.getStatus()) ||
                                podPO.getStatus().equalsIgnoreCase(BciStatus.SUCCEEDED.getStatus())) {
                            // 标记pod需要被资源回收
                            needResourceRecyclePodList.add(podPO);
                            podResourceRecycleReasonMap.put(podPO.getPodId(),
                                    ResourceRecycleReason.JOB_POD_COMPLETE.toString());
                            continue;
                        } else if (pod.getStatus().getPhase().equalsIgnoreCase(BciStatus.FAILED.getStatus()) ||
                                podPO.getStatus().equalsIgnoreCase(BciStatus.FAILED.getStatus())) {
                            // 标记pod需要被资源回收
                            needResourceRecyclePodList.add(podPO);
                            podResourceRecycleReasonMap.put(podPO.getPodId(),
                                    ResourceRecycleReason.JOB_POD_COMPLETE.toString());
                            continue;
                        } else {
                            // 2. 其他情况
                            // 标记pod需要被资源回收
                            needResourceRecyclePodList.add(podPO);
                            podResourceRecycleReasonMap.put(podPO.getPodId(), ResourceRecycleReason.JOB_POD_COMPLETE.toString());
                            continue;
                        }
                    }
                }

                if (resource.getStatus() == ResourceStatus.RUNNING) {
                    // resource状态为RUNNING才进行收费
                    LOGGER.debug("[call billing] chargeStatus in updatePodStatus for pod {}", podPO.getPodUuid());
                    chargeStatus(BciStatus.RUNNING.getStatus(), podPO, new Timestamp(new Date().getTime()), true);
                }

                // podPO.setStatus(BciStatus.getStatus("running")); // pod表的status字段完全靠k8s同步,不需要人工干预
                podPO.setInternalStatus(PodConstants.BCI_INTERNAL_STATUS_SYNCED);
            }
            assignTagAfterCreated(podPOS, order);
            podDao.batchUpdateStatus(new LinkedList<>(podPOMap.values()));
            // 标记pod需要被资源回收
            markResourceRecyclePodAndContainers(accountID, needResourceRecyclePodList, podResourceRecycleReasonMap);
            updatePodExtraResourceUuid(podExtraPOs);
        }

        private void updatePodExtraResourceUuid(List<PodExtraPO> podExtraPOs) {
            if (CollectionUtils.isEmpty(podExtraPOs)) {
                return;
            }
            for (PodExtraPO podExtraPO : podExtraPOs) {
                podExtraDaoV2.updatePodExtraResourceUuid(podExtraPO);
            }
        }

        private void getBccInstanceId(List<PodPO> podPOS) {
            LOGGER.debug("getBccInstanceId podPOS: {}", podPOS);
            for (PodPO podPO : podPOS) {
                // 非竞价订单，不更新bcc instance id
                if (podPO.getProductType() == null
                        || !podPO.getProductType().equals(podConfiguration.getBidProductType())) {
                    return;
                }
                // get node providerID==bccid from node info
                V1Node node = k8sService.getNode(podPO.getUserId(), podPO.getNodeName());
                if (node != null
                        && node.getSpec() != null
                        && !StringUtils.isEmpty(node.getSpec().getProviderID())) {
                    // providerID eg:cce://i-GEX7Ex8N
                    podPO.setBccInstanceId(node.getSpec().getProviderID().replace("cce://", ""));
                } else {
                    LOGGER.error("getBccInstanceId node error, node info: {}", node);
                }
            }
        }

        private Map<String, V1Pod> getPodUuidMap(List<PodPO> podPOS) {
            Map<String, V1Pod> result = new HashMap<>();
            for (PodPO podPO : podPOS) {
                // TODO: podPO到podName和nameSpace的映射逻辑需要修改
                // V1Pod pod = k8sService.getPod(podPO.getUserId(), podPO.getName());
                V1Pod pod = k8sService.getPod(podPO.getUserId(), podPO.getPodId());
                if (pod == null) {
                    LOGGER.warn("getPodUuidMap pod is null, pod:{}", podPO.getPodId());
                    continue;
                }
                result.put(pod.getMetadata().getUid(), pod);
            }
            return result;
        }

        private Map<String, PodPO> getPodIDMap(List<PodPO> podPOS) {
            Map<String, PodPO> result = new HashMap<>();
            for (PodPO podPO : podPOS) {
                result.put(podPO.getPodId(), podPO);
            }
            return result;
        }
        // 暂时不需要
        /*
        private void bindShotId(BindShortIdRequest request) {
            int count = RETRY_NUM;
            while (count-- > 0) {
                try {
                    logicPodClientFactory.createResourceClientV2().bindShortId(request);
                    break;
                } catch (BceInternalResponseException ex) {
                    LOGGER.warn("bind Order Resource error, retry {},exception is {}", count, ex);
                }
            }
        }
        */

        private void assignTagAfterCreated(List<PodPO> podPOs, Order order) {
            try {
                BciOrderExtra orderExtra = PodUtils.getOrderExtra(
                        PodUtils.getExtraByServiceType(order, PodConstants.SERVICE_TYPE));
                List<Tag> tags = orderExtra.getTags();
                if (CollectionUtils.isNotEmpty(tags)) {
                    assignTag(podPOs, tags, podPOs.get(0).getUserId());
                }
            } catch (Exception e) {
                LOGGER.error("assignTagAfterCreated error={}", e);
            }
        }

        /**
         * 创建时统一加标签
         *
         * @param podPOS pods
         * @param tags   标签
         */
        private void assignTag(List<PodPO> podPOS, List<Tag> tags, String accountId) {
            List<AssignResource> assignResources = new ArrayList<>();
            for (PodPO podPO : podPOS) {
                AssignResource bccAssignResource = new AssignResource();
                bccAssignResource.setRegion(regionConfiguration.getCurrentRegion());
                bccAssignResource.setResourceId(podPO.getPodId());
                bccAssignResource.setResourceUuid(podPO.getPodUuid());
                bccAssignResource.setServiceType(PodConstants.SERVICE_TYPE);
                bccAssignResource.setTags(tags);
                assignResources.add(bccAssignResource);
            }

            try {
                LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(accountId);
                CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                createAndAssignTagRequest.setResources(assignResources);
                tagClient.createAndAssignTag(createAndAssignTagRequest);
            } catch (BceException bce) {
                throw new BceInternalResponseException(bce.getMessage(), bce.getHttpStatus(), bce.getCode());
            }
        }

        private List<Resource> getPodResources(List<String> resourceUuids, ResourceClient resourceClient) {

            List<Resource> podResource = new ArrayList<>();
            ResourceIds resourceIds = new ResourceIds();
            resourceIds.setResourceIds(resourceUuids);
            Resources resources = new Resources();
            int tryTime = 3;
            while (tryTime-- > 0) {
                try {
                    resources = resourceClient.getResourcesByIds(resourceIds);
                    if (resources.size() == resourceUuids.size()) {
                        break;
                    } else {  // 可能主从同步导致查询不到，之后重试
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            LOGGER.debug("getResourcesByIds,exception:{}", e);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.debug("getResourcesByIds.exception:{},resourceUuids:{}",
                            e.getMessage(), Arrays.toString(resourceUuids.toArray()));
                    throw e;
                }
            }
            for (Resource resource : resources) {
                if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(resource.getServiceType())) {
                    podResource.add(resource);
                }
            }
            return podResource;
        }

        private AttachVolumeRollbackDbRequest getVolumeRollbackRequest(PodPO podPO) {
            AttachVolumeRollbackDbRequest rollbackDbRequest = new AttachVolumeRollbackDbRequest();
            List<Volume.PodVolume> podVolumes = new LinkedList<>();
            podVolumes = JsonUtil.toList(podPO.getPodVolumes(), Volume.PodVolume.class);
            if (CollectionUtils.isEmpty(podVolumes)) {
                return null;
            }
            List<String> volumeIds = new LinkedList<>();
            for (Volume.PodVolume podVolume : podVolumes) {
                if (podVolume != null && podVolume.getVolumeSource() != null
                        && podVolume.getVolumeSource().getCds() != null
                        && StringUtils.isNotEmpty(podVolume.getVolumeSource().getCds().getUuid())) {
                    volumeIds.add(podVolume.getVolumeSource().getCds().getUuid());
                }
            }
            if (CollectionUtils.isEmpty(volumeIds)) {
                return null;
            }

            rollbackDbRequest.setInstanceUuid(podPO.getPodId());
            rollbackDbRequest.setDiskIds(volumeIds);

            return rollbackDbRequest;
        }
    }

}