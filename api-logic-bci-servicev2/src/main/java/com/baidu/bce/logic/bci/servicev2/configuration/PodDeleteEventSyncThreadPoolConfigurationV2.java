package com.baidu.bce.logic.bci.servicev2.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class PodDeleteEventSyncThreadPoolConfigurationV2 {
    @Value("${pod.delete.event.sync.thread.pool.core.size:100}")
    private Integer threadPoolCoreSize;
    @Value("${pod.delete.event.sync.thread.pool.max.size:500}")
    private Integer threadPoolMaxSize;
    @Value("${pod.delete.event.sync.thread.pool.queue.capacity:100000}")
    private Integer threadPoolQueueCapacity;
    @Value("${pod.delete.event.sync.thread.keepalive.seconds:60}")
    private Integer threadKeepAliveSeconds;

    @Bean(name = "podDeleteEventSyncThreadPoolTaskExecutorV2")
    public ThreadPoolTaskExecutor podDeleteEventSyncThreadPoolTaskExecutorV2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(threadPoolQueueCapacity);
        executor.setKeepAliveSeconds(threadKeepAliveSeconds);

        String threadNamePrefix = "BciV2-Pod-Delete-Event-Sync-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);
        return executor;
    }
}
