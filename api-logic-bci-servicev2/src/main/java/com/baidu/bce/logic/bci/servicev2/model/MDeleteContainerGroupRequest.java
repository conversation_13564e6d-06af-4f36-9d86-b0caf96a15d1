package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceIDGetter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.ArrayList;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class MDeleteContainerGroupRequest implements AuthorizationResourceIDGetter {
    private List<String> instanceIds;
    private Boolean relatedReleaseFlag = Boolean.FALSE;

    @Override
    public List<String> getPodIDs() {
        List<String> result = new ArrayList<String>();
        if (instanceIds == null || instanceIds.size() == 0) {
            return result;
        }
        for (String instanceId : instanceIds) {
            result.add(instanceId);
        }
        return result;
    }
}