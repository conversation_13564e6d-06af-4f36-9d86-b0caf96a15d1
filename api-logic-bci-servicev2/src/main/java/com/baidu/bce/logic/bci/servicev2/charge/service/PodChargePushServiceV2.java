package com.baidu.bce.logic.bci.servicev2.charge.service;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.billing.proxy.model.v1.LegacyChargeDataRequest;
import com.baidu.bce.billing.proxy.model.v1.LegacyDimension;
import com.baidu.bce.billing.proxy.model.v1.LegacyFlavor;
import com.baidu.bce.billing.proxy.model.v1.LegacyFlavorSet;
import com.baidu.bce.billing.proxy.model.v1.LegacyMetricData;
import com.baidu.bce.billing.proxy.model.v1.LegacyStatisticValues;
import com.baidu.bce.billing.proxy.model.v1.LegacyUserChargeData;
import com.baidu.bce.billing.proxy.service.v1.LegacyProxyService;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.Account;
import com.baidu.bce.logic.bci.daov2.chargerecord.PodChargeRecordDaoV2;
import com.baidu.bce.logic.bci.daov2.chargerecord.model.PodChargeRecord;
import com.baidu.bce.logic.bci.daov2.chargestatus.PodChargeStatusDaoV2;
import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.common.service.BciChargeAsyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.ChargeStatus;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.interceptor.ResourceAccountConfig;
import com.baidu.bce.logic.bci.servicev2.model.PodCDSVolumeType;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.util.Validator;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.CPU_TYPE;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.GPU_TYPE;

@Service("PodChargePushServiceV2")
public class PodChargePushServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodChargePushServiceV2.class);

    @Autowired
    private PodChargeRecordDaoV2 podChargeRecordDao;

    @Autowired
    private PodChargeStatusDaoV2 podChargeStatusDao;

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private BciChargeAsyncServiceV2 bciChargeAsyncService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private LogicPodClientFactoryV2 bciClientFactory;

    @Autowired
    private ResourceAccountConfig resourceAccountConfig;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private Validator validator;

    @Value("${bcc.billing.batch.max.size:100}")
    private int batchMaxSize;

    private static final String INSTANCE_ID = "instanceId";
    private static final String REGION = "region";
    private static final String TIME_UNIT = "SECOND";
    private static final String METRIC_NAME = "CPC_flavor";
    private static final String METRIC_NAME_GPU = "BciGpu_flavor";

    private static final int RETRY_TIMES = 3;
    private static final int LOCK_OVER_TIME = 300;
    private static final Timestamp INIT_LAST_CHARGE_TIME = new Timestamp(31536001000L);

    public void pushChargeTask() {
        PodChargeRecord podChargeRecord = podChargeRecordDao.getNeedToPush();
        if (podChargeRecord == null) {
            return;
        }
        Boolean locked = false;
        try {
            String lockId = UUID.randomUUID().toString();
            locked = podChargeRecordDao.tryToLockOneLine(new Date(), lockId,
                    podChargeRecord.getId(), LOCK_OVER_TIME);
            if (!locked) {
                return;
            }
            boolean isSucc = pushCharge(podChargeRecord.getLastChargeTime(), podChargeRecord.getChargeTime(),
                    podChargeRecord.getMsgId());

            if (isSucc) {
                podChargeRecordDao.updateSucc(podChargeRecord.getId());
                locked = false;
            }
        } catch (Exception e) {
                LOGGER.error("Push charge error, time : {}, exception: {}", podChargeRecord.getChargeTime(), e);
        } finally {
            if (locked) {
                int index = 0;
                Boolean unLock = false;
                while (index < RETRY_TIMES) {
                    try {
                        unLock = podChargeRecordDao.unLock(podChargeRecord.getId());
                        if (unLock) {
                            break;
                        } else {
                            Thread.sleep(3000);
                        }
                    } catch (Exception e) {
                        LOGGER.error("Try unLock PodChargeRecord failed! retry : {}", index);
                    }
                    index ++;
                }
                if (!unLock) {
                    LOGGER.error("Try unLock PodChargeRecord failed!");
                }
            }
        }
    }

    private boolean pushCharge(Timestamp lastChargeTime, Timestamp chargeTime, String msgId) {
        Map<String, LinkedList<PodChargeStatus>> podChargeStatusMap =
                podChargeStatusDao.listCpcChargesByTime(lastChargeTime, chargeTime);
        long usage;
        Map<PodPO, Long> podUsage = new HashMap<>();
        for (String podUuid : podChargeStatusMap.keySet()) {
            PodPO podPO = podDao.getPodById(podUuid);
            if (podPO == null || StringUtils.isEmpty(podPO.getResourceUuid()) || podPO.getCpt1()) {
                continue;
            }
            usage = getUsageForPod(podChargeStatusMap.get(podUuid), lastChargeTime,
                    chargeTime, podPO);
            podUsage.put(podPO, usage);
        }

        List<PodPO> podPOS = podDao.listAllPods();
        for (PodPO podPO : podPOS) {
            // 若在该推送周期内出现状态变化，则按收费状态时间段收费
            if (podChargeStatusMap.keySet().contains(podPO.getPodUuid())
                    || StringUtils.isEmpty(podPO.getResourceUuid()) || podPO.getCpt1()) {
                continue;
            }
            // 在该推送期间没有出现状态变化，需要最终状态进行判断是否收费
            // CHARGE对应的状态有:
            //    running -> 需要收费
            // NO_CHARGE对应的状态有:
            //    pending/failed/succeeded/deleted/unknown —> 通常情况不需要计费(进入这些状态pod资源会被回收)
            //    特殊：failed/succeeded, 此种状态pod可能进行会延迟释放，延迟释放期间需要继续收费。
            //    标记字段:
            //        resource_recycle_timestamp: 资源回收时间
            //        resource_recycle_reason: 资源回收原因
            //        resource_recycle_complete: 是否完成资源回收标记
            //        delay_release_duration_minute: 用户指定延迟销毁时间
            //        delay_release_succeeded: 是否需要对succeed状态生效延迟删除时间
            //    判定条件:
            //        1.指定了资源回收时间: resource_recycle_timestamp != 0 (job负载完成(failed/succeeded) || eni失败(failed) || sidecar失败(failed))
            //        2.判定为job负载: resource_recycle_reason == job
            //        3.还未进行资源回收: resource_recycle_complete == 0
            if (PodConstants.NO_CHARGE.equalsIgnoreCase(ChargeStatus.getStatus(podPO.getStatus().toLowerCase()))) {
                if (podPO.getResourceRecycleTimestamp() != 0 && podPO.getResourceRecycleComplete() == 0
                        && podPO.getResourceRecycleReason().equals(ResourceRecycleReason.JOB_POD_COMPLETE.toString())
                        && podPO.getDelayReleaseDurationMinute() != 0) {
                    usage = chargeTime.getTime() -
                    (lastChargeTime.equals(INIT_LAST_CHARGE_TIME) ?
                            podPO.getCreatedTime() : lastChargeTime).getTime();
                } else {
                    usage = 0;
                }
            } else {
                // 这里的问题是，推送任务的时间段是早于当前时间的，当前时间pod可能已经不计费了，但是之前是计费的，
                // 与之对应的，会有重复计费的情况，不过误差都在1分钟之内
                usage = chargeTime.getTime() -
                        (lastChargeTime.equals(INIT_LAST_CHARGE_TIME) ?
                                podPO.getCreatedTime() : lastChargeTime).getTime();
            }
            podUsage.put(podPO, usage);
        }
        List<LegacyUserChargeData> legacyUserChargeDataList = generateLegacyChargeDataRequest(
                podUsage, msgId, chargeTime);

        return charge(legacyUserChargeDataList);
    }

    // 根据 status 里的计费状态，掐头（计费status：去掉大于record.lastChargeTime的时间 ），
    // 去尾（不计费的status，去掉小于 record.chargeTime 的时间），
    // 计算出 record.lastChargeTime 至 record.chargeTime 时间段内的计费时间
    // 这里的计算有几个问题：1.如果有多个计费状态，那 noChargeTime 的计算方法，最终只会计算最后一个计费状态的时间；
    // 2.如果第一个chargeStatus 的时间since时间> lastChargeTime，这段时间的pod其实是pending的，没计算到；
    // 如果 < lastChargeTime, 则重复计算。
    private long getUsageForPod(LinkedList<PodChargeStatus> podChargeStatuses,
                                Timestamp lastChargeTime, Timestamp chargeTime, PodPO podPO) {
        if (CollectionUtils.isEmpty(podChargeStatuses)) {
            return 0;
        }
        lastChargeTime = lastChargeTime.equals(INIT_LAST_CHARGE_TIME) ? podPO.getCreatedTime() : lastChargeTime;
        long noChargeTime = 0;
        long previous = lastChargeTime.getTime();
        PodChargeStatus endPodChargeStatus = new PodChargeStatus();
        for (PodChargeStatus podChargeStatus : podChargeStatuses) {
            if (PodConstants.CHARGE.equalsIgnoreCase(podChargeStatus.getChargeState())) {
                noChargeTime += podChargeStatus.getCreatedTime().getTime() - previous;
            }
            previous = podChargeStatus.getCreatedTime().getTime();
            endPodChargeStatus = podChargeStatus;
        }
        if (PodConstants.NO_CHARGE.equalsIgnoreCase(endPodChargeStatus.getChargeState())) {
            noChargeTime += chargeTime.getTime() - previous;
        }

        return chargeTime.getTime() - lastChargeTime.getTime() - noChargeTime ;
    }

    private List<LegacyUserChargeData> generateLegacyChargeDataRequest(Map<PodPO, Long> podUsage,
                                                                       String msgId, Timestamp chargeTime) {
        List<LegacyUserChargeData> legacyUserChargeDataList = new ArrayList<>();

        // 获取所有资源账号的accountID
        Map<String, String> raMap = getRAIDMap();

        Map<String, List<PodPO>> userPodsMap = new HashMap<>();
        for (PodPO podPO : podUsage.keySet()) {
            // 需要使用代付账号的accountID，如果没有代付账号的accountID，则使用userId
            String chargeAccountId = podPO.getRealChargeAccountId();
            // 非用户计费的资源，需要根据订单的账号来推送计费信息
            if (!LogicalConstant.CHARGE_SOURCE_USER.equalsIgnoreCase(podPO.getChargeSource())) {
                if (!raMap.containsKey(podPO.getChargeSource())) {
                    LOGGER.error("push charge error: cannot find this resource account {}", podPO.getChargeSource());
                } else {
                    chargeAccountId = raMap.get(podPO.getChargeSource());
                }
            }

            if (!userPodsMap.containsKey(chargeAccountId)) {
                userPodsMap.put(chargeAccountId, new ArrayList<PodPO>());
            }
            userPodsMap.get(chargeAccountId).add(podPO);
        }

        for (String accountId : userPodsMap.keySet()) {
            LegacyUserChargeData legacyUserChargeData = new LegacyUserChargeData();
            legacyUserChargeData.setScope("BCE_BCI");
            legacyUserChargeData.setMsgId(msgId + "-" + accountId);
            legacyUserChargeData.setUserId(accountId);

            List<LegacyMetricData> legacyMetricDataList = generateMetricDatas(userPodsMap.get(accountId),
                    chargeTime, podUsage);
            legacyUserChargeData.setMetricData(legacyMetricDataList);
            LOGGER.debug("metric data: {}", JsonUtil.toJSON(legacyUserChargeData));

            legacyUserChargeDataList.add(legacyUserChargeData);
        }
        return legacyUserChargeDataList;
    }

    public List<LegacyMetricData> generateMetricDatas(List<PodPO> podPOS, Timestamp chargeTime,
                                                       Map<PodPO, Long> podUsage) {
        List<LegacyMetricData> metricDataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(podPOS)) {
            return metricDataList;
        }

        for (PodPO podPO : podPOS) {
            String gpuType = podPO.getGpuType();
            float gpuCount = podPO.getGpuCount();
            String metricName = METRIC_NAME;
            LegacyFlavorSet legacyFlavorSet = new LegacyFlavorSet();
            try {
                // 和创建pod时对齐
                if (validator.legalPodResourceType(gpuType, gpuCount)) {
                    if (CPU_TYPE.equals(validator.choosePodResourceType(gpuType, gpuCount))) {
                        List<LegacyFlavor> flavors = new ArrayList<>();
                        LegacyFlavor flavor = new LegacyFlavor();

                        flavor.setName(LogicalConstant.CPU);
                        flavor.setValue("1");
                        flavor.setScale(new BigDecimal(String.valueOf(podPO.getvCpu())));
                        flavors.add(flavor);

                        flavor = new LegacyFlavor();
                        flavor.setName(LogicalConstant.MEMORY);
                        flavor.setValue("1");
                        flavor.setScale(new BigDecimal(String.valueOf(podPO.getMemory())));
                        flavors.add(flavor);

                        // disk
                        int diskSize = getEmptyDirScale(podPO.getPodVolumes());
                        if (diskSize != 0) {
                            flavor = new LegacyFlavor();
                            flavor.setName(LogicalConstant.DISK);
                            flavor.setValue("1");
                            flavor.setScale(new BigDecimal(diskSize));
                            flavors.add(flavor);
                        }

                        legacyFlavorSet.setFlavor(flavors);
                    } else if (GPU_TYPE.equals(validator.choosePodResourceType(gpuType, gpuCount))) {
                        metricName = METRIC_NAME_GPU;
                        String subValue = LogicalConstant.GPU_NAME;
                        String name = LogicalConstant.GPU;
                        if (LogicalConstant.NPU_TYPE.equals(gpuType)) {
                            name = LogicalConstant.NPU;
                            subValue = LogicalConstant.NPU_NAME;
                        }
                        // 校验是否存在推计费的gpu套餐，若不存在过滤掉避免影响整个请求
                        String bccSpec = validator.getGpuBCISpec(gpuType, (int) podPO.getGpuCount(),
                                (int) podPO.getvCpu(), (int) podPO.getMemory());
                        if (null == bccSpec) {
                            LOGGER.error("charge push gpu spec is not exist: {}", podPO.getPodId());
                            continue;
                        }

                        List<LegacyFlavor> flavors = new ArrayList<>();
                        LegacyFlavor flavor = new LegacyFlavor();

                        flavor.setName("subServiceType");
                        flavor.setValue(subValue);
                        flavor.setScale(new BigDecimal("1"));
                        flavors.add(flavor);

                        flavor = new LegacyFlavor();
                        flavor.setName(name);
                        flavor.setValue(bccSpec);
                        flavor.setScale(new BigDecimal("1"));
                        flavors.add(flavor);

                        legacyFlavorSet.setFlavor(flavors);
                    }
                } else {
                    LOGGER.error("pod's resource type is wrongful: {}", podPO.getPodId());
                    continue;
                }
            } catch (Exception e) {
                LOGGER.error("pod's resource type is unknown: {}", podPO.getPodId());
                continue;
            }

            LegacyMetricData legacyMetricData = new LegacyMetricData();

            LegacyDimension instanceId = new LegacyDimension();
            instanceId.setName(INSTANCE_ID);
            instanceId.setValue(podPO.getPodUuid());

            LegacyDimension region = new LegacyDimension();
            region.setName(REGION);
            region.setValue(regionConfiguration.getCurrentRegion());
            List<LegacyDimension> dimensions = Arrays.asList(instanceId, region);

            legacyMetricData.setDimensions(dimensions);
            legacyMetricData.setMetricName(metricName);

            LegacyStatisticValues legacyStatisticValues = new LegacyStatisticValues();
            legacyStatisticValues.setSum(new BigDecimal(podUsage.get(podPO) / 1000));
            legacyStatisticValues.setUnit(TIME_UNIT);

            legacyMetricData.setStatisticValues(legacyStatisticValues);
            legacyMetricData.setTimestamp(chargeTime.getTime() / 1000);
            legacyMetricData.setTag(false);

            legacyMetricData.setFlavor(legacyFlavorSet);

            metricDataList.add(legacyMetricData);
        }

        return metricDataList;
    }

    private Boolean charge(List<LegacyUserChargeData> legacyUserChargeDataList) {
        if (CollectionUtils.isEmpty(legacyUserChargeDataList)) {
            return true;
        }
        int left = legacyUserChargeDataList.size();
        int batch = 0;
        List<LegacyChargeDataRequest> legacyChargeDataRequests = new ArrayList<>();
        while (left > batchMaxSize) {  // billing批量最大支持100
            LegacyChargeDataRequest legacyChargeDataRequest = new LegacyChargeDataRequest();
            legacyChargeDataRequest.setUserChargeDatas(
                    legacyUserChargeDataList.subList(batch * batchMaxSize, (batch + 1) * batchMaxSize));
            legacyChargeDataRequests.add(legacyChargeDataRequest);
            batch++;
            left -= batchMaxSize;
        }

        LegacyChargeDataRequest legacyChargeDataRequest = new LegacyChargeDataRequest();
        legacyChargeDataRequest.setUserChargeDatas(
                legacyUserChargeDataList.subList(batch * batchMaxSize, batch * batchMaxSize + left));
        legacyChargeDataRequests.add(legacyChargeDataRequest);

        try {
            asyncExecutorService.initAsyncWorkMap();

            for (LegacyChargeDataRequest request : legacyChargeDataRequests) {
                bciChargeAsyncService.bciCharge(request);
            }

            for (LegacyChargeDataRequest request : legacyChargeDataRequests) {
                LegacyProxyService.EmptyResponse response = (LegacyProxyService.EmptyResponse) asyncExecutorService.
                        getAsyncResult(WorkKeyUtil.genWorkKey("bciChargeAsync",
                                Arrays.asList((Object) request)));
                if (response == null) {
                    return false;
                }
            }
        } catch (Exception e) {
            LOGGER.error("sync work exception!");
            return false;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
        return true;
    }

    /**
     * getRAIDMap return resource accountID map :
     * cce -> 02fa0a988caf43a9a5c0270310347581,
     * bsc -> 2a504dd6a8344952b10835451d2d2ebb
     * @return
     */
    public Map<String, String> getRAIDMap() {
        Map<String, String> resourceAccountIDMap = new HashMap<>();

        IAMClient iamClient = bciClientFactory.createIAMClientWithConsoleToken();

        for (Map.Entry<String, String> entry : resourceAccountConfig.resourceAccountMapping().entrySet()) {
            Account account = new Account();
            try {
                account = iamClient.accountsGet(entry.getValue());
            } catch (BceException e) {
                LOGGER.debug("failed to get account {} : {}", entry.getValue(), e);
            } catch (Exception e) {
                LOGGER.debug("failed to get account {} : {}", entry.getValue(), e);
            }

            if (account == null) {
                break;
            }
            LOGGER.debug("resourceAccount : {}, name : {}", entry.getKey(), account.getName());
            if (account.getName().equalsIgnoreCase(entry.getValue())) {
                resourceAccountIDMap.put(entry.getKey().toLowerCase(), account.getDomainId());
            }
        }
        return resourceAccountIDMap;
    }

    private int getEmptyDirScale(String podVolumes) {
        int diskSize = 0;
        if (StringUtils.isNotEmpty(podVolumes)) {
            List<ServersResponse.ServerBciCds> volumeList = JsonUtil.toList(podVolumes,
                    ServersResponse.ServerBciCds.class);
            if (CollectionUtils.isEmpty(volumeList)) {
                return diskSize;
            }
            for (ServersResponse.ServerBciCds podVolume : volumeList) {
                if (PodCDSVolumeType.EMPTYDIR.getType().equalsIgnoreCase(podVolume.getType())) {
                    diskSize += Integer.parseInt(podVolume.getSize());
                }
                if (PodCDSVolumeType.ROOTFS.getType().equalsIgnoreCase(podVolume.getType())) {
                    diskSize += Integer.parseInt(podVolume.getSize());
                }
            }
        }

        diskSize = diskSize - podConfiguration.getFreeChargeSizeInGB();

        return diskSize > 0 ? diskSize : 0;
    }
}
