package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.ImageAccelerateSyncServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("ImageAccelerateSyncSchedulerV2")
@Profile("default")
public class ImageAccelerateSyncSchedulerV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageAccelerateSyncSchedulerV2.class);
    private static final int SYNC_IMAGE_ACCELERATE_TIME = 60000;

    private String runScheduledTaskSchedulerName = "ImageAccelerateSyncSchedulerV2.runScheduledTask";

    private String runGcTaskSchedulerName = "ImageAccelerateSyncSchedulerV2.runGcTask";
    private static final int SYNC_IMAGE_GC_TIME = 600000;
    
    @Autowired
    private ImageAccelerateSyncServiceV2 imageAccelerateSyncServiceV2;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(runScheduledTaskSchedulerName);
        schedulerStatistics.registerScheduler(runGcTaskSchedulerName);
    }

    @Scheduled(fixedRate = SYNC_IMAGE_ACCELERATE_TIME)
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(runScheduledTaskSchedulerName);
        // 镜像缓存同步和资源回收实现
        imageAccelerateSyncServiceV2.syncImageCacheCrd();
        schedulerStatistics.afterSchedulerRun(runScheduledTaskSchedulerName);
    }
    
    @Scheduled(fixedRate = SYNC_IMAGE_GC_TIME)
    public void runGcTask() {
        schedulerStatistics.beforeSchedulerRun(runGcTaskSchedulerName);
        imageAccelerateSyncServiceV2.gcImageCache();
        schedulerStatistics.afterSchedulerRun(runGcTaskSchedulerName);
    }

}
