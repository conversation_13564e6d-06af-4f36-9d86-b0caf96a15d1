package com.baidu.bce.logic.bci.servicev2.charge;

import com.baidu.bce.logic.bci.servicev2.charge.service.PodPushTaskServiceV2;
import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

/*@EnableScheduling
@Configuration("PodPushTaskSchedulerV2")
@Profile("default")
public class PodPushTaskSchedulerV2 {

    /*private String deleteRecordSchedulerName = "PodPushTaskSchedulerV2.deleteRecord";
    private String runScheduledTaskSchedulerName = "PodPushTaskSchedulerV2.runScheduledTask";

    @Autowired
    private PodPushTaskServiceV2 podPushTaskService;

    @Autowired
    private SchedulerStatistics schedulerStatistics;
    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(deleteRecordSchedulerName);
        schedulerStatistics.registerScheduler(runScheduledTaskSchedulerName);
    }

    @Scheduled(cron = "0 0 0 1/1 * ? ")
    public void deleteRecord() {
        schedulerStatistics.beforeSchedulerRun(deleteRecordSchedulerName);
        podPushTaskService.deleteRecord();
        schedulerStatistics.afterSchedulerRun(deleteRecordSchedulerName);
    }

    @Scheduled(cron = "0/20 * * * * ? ")
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(runScheduledTaskSchedulerName);
        podPushTaskService.createPushTask();
        schedulerStatistics.afterSchedulerRun(runScheduledTaskSchedulerName);
    }

}*/
public class PodPushTaskSchedulerV2 {
}