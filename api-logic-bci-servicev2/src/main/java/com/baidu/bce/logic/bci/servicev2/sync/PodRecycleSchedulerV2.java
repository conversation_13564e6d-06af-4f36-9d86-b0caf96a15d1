package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.sync.service.PodRecycleServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@EnableScheduling
@Configuration("PodRecycleSchedulerV2")
@Profile("default")
public class PodRecycleSchedulerV2 {

    // 目前没有用到这个定时任务，先注释掉。
    // private static final Logger LOGGER = LoggerFactory.getLogger(PodRecycleSchedulerV2.class);

    // private static final int FIX_DELAY_ONE = 20000;

    // @Autowired
    // private PodRecycleServiceV2 podRecycleService;

    // @Scheduled(fixedDelay = FIX_DELAY_ONE)
    // public void runScheduledTask() {
    //     podRecycleService.recyclePodContainer();
    // }
}
