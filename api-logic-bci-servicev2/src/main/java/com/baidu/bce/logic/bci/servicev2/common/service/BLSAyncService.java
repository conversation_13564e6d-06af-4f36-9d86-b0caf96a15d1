package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.asyncwork.sdk.asyncaop.BceAsyncWork;
import com.baidu.bce.asyncwork.sdk.model.Level;
import com.baidu.bce.internalsdk.bci.model.CreateBLSTaskRequest;
import com.baidu.bce.internalsdk.bci.model.CreateBLSTaskResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBLSTaskResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTaskNameResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTokenResponse;
import com.baidu.bce.internalsdk.bci.model.CreateBlsTokenResponse;
import com.baidu.bce.internalsdk.bci.model.CreateBlsTokenRequest;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTokenIDResponse;
import com.baidu.bce.internalsdk.bci.model.QueryAllBlsTaskResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsAgentResponse;

import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BLSAyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BLSAyncService.class);

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @BceAsyncWork(name = "createBLSTask", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public CreateBLSTaskResponse createBLSTask(CreateBLSTaskRequest logTaskCreateRequest, String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).createLogTask(logTaskCreateRequest);
    }

    @BceAsyncWork(name = "deleteBLSTask", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public void deleteBLSTask(String taskId, String accountId) {
        logicPodClientFactory.createBLSClient(accountId).deleteLogTask(taskId);
    }

    @BceAsyncWork(name = "getBLSTask", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public QueryBLSTaskResponse getBLSTask(String taskId, String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).queryLogTask(taskId);
    }

    @BceAsyncWork(name = "queryBlsTaskNameAvaible", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public QueryBlsTaskNameResponse queryBlsTaskNameAvaible(String blsTaskName, String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).getBlsTaskDetailByName(blsTaskName);
    }

    @BceAsyncWork(name = "queryAllBlsTokens", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public QueryBlsTokenResponse queryAllBlsTokens(String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).queryAllBlsTokens();
    }

    @BceAsyncWork(name = "createBlsToken", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public CreateBlsTokenResponse createBlsToken(CreateBlsTokenRequest request, String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).createBlsToken(request);
    }

    @BceAsyncWork(name = "queryBlsTokenInfoByID", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public QueryBlsTokenIDResponse queryBlsTokenInfoByID(String blsTokenID, String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).queryBlsTokenInfoByID(blsTokenID);
    }

    @BceAsyncWork(name = "queryAllBlsTask", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public QueryAllBlsTaskResponse queryAllBlsTask(String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).queryAllBlsTask();
    }

    @BceAsyncWork(name = "queryBlsAgent", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public QueryBlsAgentResponse queryBlsAgent(String blsTaskID, String accountId) {
        return logicPodClientFactory.createBLSClient(accountId).queryBlsAgent(blsTaskID);
    }
}
