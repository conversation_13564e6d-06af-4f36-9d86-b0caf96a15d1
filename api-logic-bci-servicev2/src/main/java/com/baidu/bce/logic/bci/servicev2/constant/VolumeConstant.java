package com.baidu.bce.logic.bci.servicev2.constant;

public class VolumeConstant {
    /* host path type */
    public static final String HOST_PATH_TYPE_UNSET = "";
    public static final String HOST_PATH_TYPE_DIRECTORY_OR_CREATE = "DirectoryOrCreate";
    public static final String HOST_PATH_TYPE_DIRECTORY = "Directory";
    public static final String HOST_PATH_TYPE_FILE_OR_CREATE = "FileOrCreate";
    public static final String HOST_PATH_TYPE_FILE = "File";
    public static final String HOST_PATH_TYPE_SOCKET = "Socket";
    public static final String HOST_PATH_TYPE_CHAR_DEVICE = "CharDevice";
    public static final String HOST_PATH_TYPE_BLOCK_DEVICE = "BlockDevice";
}
