package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ContainerPurchase {
    private String name = "";
    private String imageName = "";
    // 新增字段，容器类型，init、workload和ds-workload
    private String containerType = ContainerType.WORKLOAD.getType();
    private String imageVersion = "";
    private String imageAddress = "";
    private float memory;
    private float cpu;
    private String gpuType; // 卡类型，只支持独占售卖，后续共享在继续增加新字段，比如radio或者归一化的算例值
    private float gpuCount; // 卡数量，支持<1，也支持多卡（>=1需要是整数），算例显存相同比例售卖，当前没有归一化值
    private String workingDir = "";
    private String imagePullPolicy = "";
    private List<String> commands;
    private List<String> args;
    private List<VolumeMounts> volumeMounts;
    private List<Port> ports;
    private List<Environment> envs;
    private List<LogCollection> logCollections;
    
    // 新增加字段
    private Probe livenessProbe;

    private Probe readinessProbe;

    private Probe startupProbe;

    private Lifecycle lifecycle;

    // Variables for interactive containers, these have very specialized use-cases (e.g. debugging)
    // and shouldn't be used for general purpose containers.

    // Whether this container should allocate a buffer for stdin in the container runtime. If this
    // is not set, reads from stdin in the container will always result in EOF.
    // Default is false.
    // +optional
    private boolean stdin = false;

    // Whether the container runtime should close the stdin channel after it has been opened by
    // a single attach. When stdin is true the stdin stream will remain open across multiple attach
    // sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the
    // first client attaches to stdin, and then remains open and accepts data until the client disconnects,
    // at which time stdin is closed and remains closed until the container is restarted. If this
    // flag is false, a container processes that reads from stdin will never receive an EOF.
    // Default is false
    // +optional
    private boolean stdinOnce = false;

    // Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.
    // Default is false.
    // +optional
    private boolean tty = false;

    // ContainerSecurityContext defines the security options the container should be run with.
    // If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.
    // More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
    // +optional
    private ContainerSecurityContext securityContext;

    public ContainerPurchase() {

    }

    public ContainerPurchase(Container container) {
        this.name = container.getName();
        this.imageName = container.getName();
        String image = container.getImage();
        if (image != null) {
            String[] imageArray = image.split(":");
            if (imageArray.length == 2) {
                this.imageAddress = imageArray[0];
                this.imageVersion = imageArray[1];
            } else {
                this.imageName = image;
            }
        }
        
        this.memory = container.getMemory();
        this.cpu = container.getCpu();
        this.gpuCount = container.getGpu();
        this.workingDir = container.getWorkingDir();
        this.imagePullPolicy = container.getImagePullPolicy();

        this.commands = container.getCommands();
        this.args = container.getArgs();
        List<VolumeMount> volumeMounts = container.getVolumeMounts();
        List<VolumeMounts> volumeMountss = new ArrayList<VolumeMounts>();
        if (volumeMounts != null) {
            for (VolumeMount volumeMount : volumeMounts) {
                volumeMountss.add(new VolumeMounts(volumeMount));
            }
        }
        this.volumeMounts = volumeMountss;
        this.ports = container.getPorts();
        this.envs = container.getEnvironmentVars();

        this.logCollections = container.getLogCollections();
        
        this.livenessProbe = container.getLivenessProbe();
        this.readinessProbe = container.getReadinessProbe();
        this.startupProbe = container.getStartupProbe();

        this.stdin = container.getStdin();
        this.stdinOnce = container.getStdinOnce();
        this.tty = container.getTty();

        this.securityContext = container.getSecurityContext();
    }

    public boolean isDsContainer() {
        return ContainerType.DS_WORKLOAD.getType().equals(this.containerType);
    }

    public boolean isInitContainer() {
        return ContainerType.INIT.getType().equals(this.containerType);
    }

    public boolean isWorkloadContainer() {
        return ContainerType.WORKLOAD.getType().equals(this.containerType);
    }

}
