package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.billing.model.PackageEntity;
import com.baidu.bce.billing.model.PackageUsageDetail;
import com.baidu.bce.billing.service.UsagePackageService;
import com.baidu.bce.fbi.common.uuid.Uuid;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.servicev2.orderexecute.ReservedInstanceOrderExecutorService;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("ReservedInstanceSyncService")
public class ReservedInstanceSyncService extends SyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReservedInstanceSyncService.class);
    
    @Autowired
    ReservedInstanceDao reservedInstanceDao;

    @Autowired
    @Qualifier("reservedInstanceSyncThreadPoolTaskExecutorV2")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired 
    private ReservedInstanceOrderExecutorService reservedInstanceOrderExecutorService;

    public void syncReservedInstanceInBuild() {
        List<ReservedInstancePO> reservedInstanceList = reservedInstanceDao.listReservedInstancesInBuild();
        Map<String, List<ReservedInstancePO>> reservedInstanceMap = new HashMap<>();
        for (ReservedInstancePO po : reservedInstanceList) {
            if (!reservedInstanceMap.containsKey(po.getOrderId())) {
                reservedInstanceMap.put(po.getOrderId(), new ArrayList<ReservedInstancePO>());
            }
            reservedInstanceMap.get(po.getOrderId()).add(po);
        }
        if (reservedInstanceMap != null && !reservedInstanceMap.isEmpty()) {
            for (String orderId : reservedInstanceMap.keySet()) {
                threadPoolTaskExecutor.execute(new SyncOrder(orderId, reservedInstanceMap.get(orderId)));
            }
        }
    }

    public void syncCreatedReservedInstance() {
        List<ReservedInstancePO> reservedInstanceToProcess = reservedInstanceDao.listCreatedReservedInstances();
        List<ReservedInstancePO> reservedInstanceToUpdate = new ArrayList<>();

        LOGGER.debug("[ReservedInstance][SyncCreated] start, {}", reservedInstanceToProcess);
        for (ReservedInstancePO reservedInstancePO : reservedInstanceToProcess) {
            if (reservedInstancePO.getStatus().equals(ReservedInstancePO.Status.INACTIVE)) {
                if (reservedInstancePO.getEffectiveTime().getTime() <= System.currentTimeMillis() + 3600 * 8 * 1000) {
                    reservedInstancePO.setStatus(ReservedInstancePO.Status.ACTIVE);
                    reservedInstanceToUpdate.add(reservedInstancePO);
                }
            } else if (reservedInstancePO.getStatus().equals(ReservedInstancePO.Status.ACTIVE)) {
                if (reservedInstancePO.getExpireTime().getTime() <= System.currentTimeMillis() + 3600 * 8 * 1000) {
                    reservedInstancePO.setStatus(ReservedInstancePO.Status.EXPIRED);
                    reservedInstanceToUpdate.add(reservedInstancePO);
                }
            }
        }

        try {
            reservedInstanceDao.batchUpdateStatus(reservedInstanceToUpdate);
        } catch (Exception e) {
            LOGGER.debug("[ReservedInstance][SyncCreated]batch update failed, {} , exception is {}", 
                    reservedInstanceToUpdate, e);
        }

        LOGGER.debug("[ReservedInstance][SyncCreated] {} end", reservedInstanceToProcess);
    }

    class SyncOrder implements Runnable {
        private String orderId;
        private List<ReservedInstancePO> reservedInstanceList;

        public SyncOrder(String orderId, List<ReservedInstancePO> reservedInstanceList) {
            this.orderId = orderId;
            this.reservedInstanceList = reservedInstanceList;
        }

        @Override
        public void run() {
            BceInternalRequest.setThreadRequestId(PodUtils.createLongUuid());
            LOGGER.debug("[ReservedInstance][SyncInBuild] orderID {} start", orderId);
            if (StringUtils.isEmpty(orderId)) {
                LOGGER.debug("[ReservedInstance][SyncInBuild] orderID of instances {} is empty", reservedInstanceList);
                return;
            }
            if (CollectionUtils.isEmpty(reservedInstanceList)) {
                LOGGER.debug("[ReservedInstance][SyncInBuild] orderID {} has no instance", orderId);
                return;
            }

            OrderClient orderClient = logicPodClientFactory.createOrderClient();
            String accountId = reservedInstanceList.get(0).getAccountId();
            Order order = null;
            try {
                order = orderClient.get(orderId);
            } catch (Exception e) {
                LOGGER.error("[ReservedInstance][SyncInBuild]get orderID {} error, exception: {}", orderId, e);
                return;
            }            
            if (order == null) {
                LOGGER.error("[ReservedInstance][SyncInBuild]get orderID {} return null", orderId);
                return;
            }
            OrderStatus orderStatus = order.getStatus();

            if (orderStatus.equals(OrderStatus.CREATED)) {
                // 为了保证预留实例的自动续费规则能够创建成功，对于订单状态CREATED，ReservedInstance Creating 状态的，创建自动续费规则
                LOGGER.debug("[ReservedInstance][SyncInBuild]orderID {}, status CREATED", orderId);
                reservedInstanceOrderExecutorService.bindTags(reservedInstanceList, order);
                reservedInstanceOrderExecutorService.createAutoRenewRule(reservedInstanceList, order);
                LOGGER.debug("[ReservedInstance][SyncInBuild]orderID {} end", orderId);
                return;
            }

            if (orderStatus.equals(OrderStatus.CREATE_FAILED) || orderStatus.equals(OrderStatus.REFUND_SUCC)
                    || orderStatus.equals(OrderStatus.REFUND_FAILED) || orderStatus.equals(OrderStatus.EXPIRED)
                    || orderStatus.equals(OrderStatus.CANCELLED)) {
                LOGGER.info("[ReservedInstance][SyncInBuild]orderID {}, status {}", orderId, orderStatus);
                try {
                    // 这里订单已处于 invalid 状态，无法加锁，需注意多实例运行是否有冲突
                    List<ReservedInstancePO> reservedInstancePOs =
                            reservedInstanceDao.listReservedInstancesByOrderId(orderId);
                    // TODO: 若有预留资源，需删除
                    UsagePackageService usagePackageService = 
                            logicPodClientFactory.createUsagePackageService(accountId);
                    for (ReservedInstancePO reservedInstancePO : reservedInstancePOs) {
                        if (!reservedInstancePO.isDeleted()) {
                            try {
                                List<PackageEntity> packageEntities = new ArrayList<>();
                                if (reservedInstancePO.getReservedInstanceUuid() != null && 
                                        !reservedInstancePO.getReservedInstanceUuid().isEmpty()) {
                                    PackageEntity packageEntity = new PackageEntity();
                                    packageEntity.setAccountId(accountId);
                                    packageEntity.setOrderId(orderId);
                                    packageEntity.setPackageName(Uuid.from(
                                            reservedInstancePO.getReservedInstanceUuid()));
                                    packageEntities.add(packageEntity);
                                }
                                // 如果已经在billing侧创建了预留实例券，需销毁，重复销毁不会导致问题
                                if (packageEntities.size() != 0) {
                                    LOGGER.info("[ReservedInstance][SyncInBuild]orderID {}, reservedInstanceID {}"
                                            + ", destroy package {}", orderId, 
                                            reservedInstancePO.getReservedInstanceId(), packageEntities);
                                    List<PackageUsageDetail> detail = 
                                            usagePackageService.destroyPackage(packageEntities);
                                    LOGGER.info("[ReservedInstance][SyncInBuild]orderID {}, reservedInstanceID {}" +
                                            ", destroy package resp {}", orderId, 
                                            reservedInstancePO.getReservedInstanceId(), detail);
                                }
                                // 如果是创建失败的订单（一般意味着系统问题），将预留实例状态置为创建失败
                                //      其他情况如退款取消等，直接删除预留实例
                                if (orderStatus.equals(OrderStatus.CREATE_FAILED)) {
                                    reservedInstanceDao.updateStatus(accountId, 
                                            reservedInstancePO.getReservedInstanceId(), 
                                            ReservedInstancePO.Status.CREATE_FAILED);
                                } else {
                                    reservedInstanceDao.delete(reservedInstancePO.getId());
                                }
                                
                            } catch (Exception e) {
                                LOGGER.debug("delete reserved instance {} error, order {}, exeption {}",
                                        reservedInstancePO.getId(), orderId, e);
                            }
                        }
                    }
                } catch (Exception e) {
                    LOGGER.debug("get reserved instance by order {} error, exception {}", orderId, e);
                }
            }
        }
    }
}
