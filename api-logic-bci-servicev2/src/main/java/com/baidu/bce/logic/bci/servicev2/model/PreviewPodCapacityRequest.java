package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PreviewPodCapacityRequest implements Cloneable {
    private Float cpu;                            // cpu quota
    private Float memory;                         // memoury quota
    private String gpuType;                       // gpu卡类型
    private Float gpuCount;                       // gpu卡数量
    private String cpuType;                       // cpu类型
    private List<String> logicalZones;            // 逻辑可用区
    @JsonIgnore
    private List<String> physicalZones;           // 物理可用区
    private List<String> types;                   // pod类型，指定是tidal、pfs
    @JsonIgnore
    private String accountId;                     // accountId

    // 浅拷贝
    @Override
    public Object clone() {
        PreviewPodCapacityRequest o = null;
        try {
            o = (PreviewPodCapacityRequest) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return o;
    }

}
