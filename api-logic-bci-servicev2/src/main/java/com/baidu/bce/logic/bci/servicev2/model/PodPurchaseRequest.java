package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.kubernetes.client.openapi.models.V1TopologySpreadConstraint;
import org.apache.commons.lang.StringUtils;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PodPurchaseRequest implements IOrderItem {
    private String name;
    private String restartPolicy = "";
    private String cceId = "";
    private String vpcId = "";
    private String vpcUuid = "";
    private String vpcCidr = "";
    private String subnetId = "";
    // 新增支持多可用区协议
    private String subnetIds = "";
    private String zoneSubnets = "";
    private String subnetUuid = "";
    private String subnetShortId = "";
    private String subnetCidr = "";
    private String subProductType = "";
    private String eipIp = "";
    @JsonProperty(value = "volumes")
    private Volume volume;
    @JsonProperty(value = "containers")
    private List<ContainerPurchase> containerPurchases;
    @JsonProperty(value = "imageRegistrySecret")
    private List<ImageRegistrySecret> imageRegistrySecrets;

    private String securityGroupId;
    private List<Tag> tags;
    private int purchaseNum = 1;
    private String logicalZone = "";
    private String zoneId = "";
    private float cpu;
    private float memory;
    private String cpuType;
    private String gpuType; // 卡类型，只支持独占售卖，后续共享在继续增加新字段，比如radio或者归一化的算例值
    private float gpuCount; // 卡数量，支持<1，也支持多卡（>=1需要是整数），算例显存相同比例售卖，当前没有归一化值
    private float cdsPurchaseSize = 0;
    private String serviceType = "BCI";
    private String productType;

    private BidOption bidOption;
    private List<V1TopologySpreadConstraint> topologySpreadConstraints;
    private Affinity affinity;
    private Long terminationGracePeriodSeconds;
    private int delayReleaseDurationMinute;
    private boolean delayReleaseSucceeded = false;

    @JsonProperty(value = "hostname")
    private String hostname;

    // SecurityContext holds pod-level security attributes and common container settings.
    // Optional: Defaults to empty.  See type description for default values of each field.
    // +optional
    private PodSecurityContext securityContext;

    /**
     * 加密后的 物理zone
     */
    private String encryptedPhysicalZone;

    private String orderUuid;
    private String annotations;

    private List<Label> labels;
    private List<Label> metadataLabels; // 保存需要在 Pod MetaData 中携带的 Labels

    private boolean enableLog = false;

    // 是否是潮汐pod
    @JsonProperty(value = "isTidal")
    private boolean isTidal = false;

    // 是否自动使用和创建镜像缓存
    private Boolean autoMatchImageCache = true;

    // 是否支持调度到pfs资源池
    private boolean scheduleInPfsPool = false;

    /**
     * 业务
     */
    private String application = "default";

    // 额外字段,vk 传递 bci 控制面不解析只做存储
    Map<String, String> extras = new HashMap<>();

    private String clientToken = "";
    // 迁移 UUID
    private String migrationUuid;

    // 是否为双栈Pod
    @JsonProperty(value = "enableIPv6")
    private boolean enableIPv6 = false;

    @Override
    public String getServiceType() {
        return serviceType;
    }

    @Override
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    @Override
    public String getProductType() {
        return productType;
    }

    @Override
    public void setProductType(String productType) {
        this.productType = productType;
    }

    public PodPurchaseRequest() {
    }

    public PodPurchaseRequest(CreateContainerGroupRequest request) {
        this.setName(request.getName().toLowerCase());
        this.setLogicalZone(request.getZoneName());
        this.setSecurityGroupId(StringUtils.join(request.getSecurityGroupIds(), ","));
        if (request.getSubnetIds() != null && request.getSubnetIds().size() > 1) {
            this.setSubnetIds(StringUtils.join(request.getSubnetIds(), ","));
            this.setLogicalZone("");
        } else if (request.getSubnetIds() != null && request.getSubnetIds().size() == 1) {
            this.setSubnetId(request.getSubnetIds().get(0));
        }
        this.setRestartPolicy(request.getRestartPolicy());
        this.setEipIp(request.getEipIp());
        this.setCpuType(request.getCpuType());
        this.setGpuType(request.getGpuType());
        this.setServiceType("BCI");
        this.setProductType("PostPay");
        this.setTerminationGracePeriodSeconds(request.getTerminationGracePeriodSeconds());
        this.setHostname(request.getHostName());
        this.setTags(request.getTags());
        this.setVolume(request.getVolume());

        // build imageRegistrySecrets
        List<ImageRegistrySecret> imageRegistrySecrets = new ArrayList<ImageRegistrySecret>();
        if (request.getImageRegistryCredentials() != null && !request.getImageRegistryCredentials().isEmpty()) {
            for (ImageRegistryCredential imageRegistryCredential : request.getImageRegistryCredentials()) {
                ImageRegistrySecret image = new ImageRegistrySecret(imageRegistryCredential);
                imageRegistrySecrets.add(image);
            }
        }
        this.setImageRegistrySecrets(imageRegistrySecrets);

        // build containers
        List<ContainerPurchase> containerPurchases = new ArrayList<ContainerPurchase>();
        if (request.getContainers() != null && !request.getContainers().isEmpty()) {
            for (Container container : request.getContainers()) {
                ContainerPurchase containerPurchase = new ContainerPurchase(container);
                containerPurchase.setContainerType(ContainerType.WORKLOAD.getType());
                containerPurchase.setGpuType(request.getGpuType());
                containerPurchases.add(containerPurchase);
            }
        }

        // build initContainers
        if (request.getInitContainers() != null && !request.getInitContainers().isEmpty()) {
            for (Container initContainer : request.getInitContainers()) {
                ContainerPurchase containerPurchase = new ContainerPurchase(initContainer);
                containerPurchase.setContainerType(ContainerType.INIT.getType());
                containerPurchase.setGpuType(request.getGpuType());
                containerPurchases.add(containerPurchase);
            }
        }
        this.setContainerPurchases(containerPurchases);
    }
}
