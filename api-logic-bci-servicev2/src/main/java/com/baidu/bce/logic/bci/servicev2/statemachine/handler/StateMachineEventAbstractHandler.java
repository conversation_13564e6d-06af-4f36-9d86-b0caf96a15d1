package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.POD_PREFIX;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Component
public abstract class StateMachineEventAbstractHandler {

    public static final Logger LOGGER = LoggerFactory.getLogger(StateMachineEventAbstractHandler.class);

    protected StateMachineContext context;

    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    protected PodExtraDaoV2 podExtraDao;

    public boolean init(String podId, StateMachineEvent event, StateMachineEventContext eventContext) {
        return initContext(podId, event, eventContext);
    }

    public boolean init(PodPO podPO, StateMachineEvent event, StateMachineEventContext eventContext) {
        return initContext(podPO, event, eventContext);
    }

    protected boolean baseCheckEventContext() {
        if (context == null) {
            return false;
        }
        if (context.getEvent() == null) {
            return false;
        }
        if (context.getPodId() == null) {
            return false;
        }
        // 检查podId是否合法
        String podId = context.getPodId();
        if (StringUtils.isEmpty(podId)) {
            return false;
        }
        if (!StringUtils.startsWith(podId, POD_PREFIX)) {
            return false;
        }
        if (context.getPodPO() == null) {
            return false;
        }
        return true;
    }

    public abstract boolean checkEventContext();

    /**
     * 检查pod当前状态是否可以被流转
     * @return
     */
    public abstract boolean check();


    /**
     * 执行pod状态流转操作
     * @return
     */
    public abstract boolean execute();

    protected String getPodId() {
        return context.getPodId();
    }

    protected String getUserId() {
        return context.getUserId();
    }

    protected StateMachineEvent getEvent() {
        return context.getEvent();
    }

    protected PodPO getPodPO() {
        return context.getPodPO();
    }

    protected PodPO getPodByIdFromDB(String podId) {
        if (StringUtils.isBlank(podId)) {
            throw new CommonExceptions.RequestInvalidException();
        }
        PodPO podPO = podDao.getPodById(podId);
        if (podPO == null) {
            LOGGER.debug("StateMachineEventAbstractHandler get podById return null, podId is {}", podId);
            throw new CommonExceptions.ResourceNotExistException();
        }
        return podPO;
    }

    protected boolean initContext(String podId, StateMachineEvent event, StateMachineEventContext eventContext) {
        PodPO podPO = getPodByIdFromDB(podId);
        return initContext(podPO, event, eventContext);
    }

    protected boolean initContext(PodPO podPO, StateMachineEvent event, StateMachineEventContext eventContext) {
        context = new StateMachineContext();
        context.setEvent(event);
        context.setEventContext(eventContext);
        if (podPO == null) {
            return false;
        }
        String podId = podPO.getPodId();
        String userId = podPO.getUserId();
        context.setPodId(podId);
        context.setUserId(userId);
        context.setPodPO(podPO);
        context.setK8SResourceVersion(podPO.getResourceVersion());
        context.setBciResourceVersion(podPO.getBciResourceVersion());
        return true;
    }

    protected long getK8SResourceVersion() {
        return context.getK8SResourceVersion();
    }

    protected long getBciResourceVersion() {
        return context.getBciResourceVersion();
    }

    protected long getNextBciResourceVersion() {
        return getBciResourceVersion() + 1;
    }

    protected StateMachinePodDaoContext generateStateMachinePodDaoContext() {
        StateMachinePodDaoContext podDaoContext = new StateMachinePodDaoContext(getPodId(), getUserId(),
                getK8SResourceVersion(), getBciResourceVersion());
        return podDaoContext;
    }

    protected StateMachineContext getContext() {
        return context;
    }

    protected StateMachineEventContext getEventContext() {
        return context.getEventContext();
    }
}
