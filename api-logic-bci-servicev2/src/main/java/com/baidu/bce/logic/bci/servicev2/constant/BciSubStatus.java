package com.baidu.bce.logic.bci.servicev2.constant;

public enum BciSubStatus {
    RESCHEDULING_TOBEDELETED(BciStatus.RESCHEDULING.getStatus(), BciSubStatus.STATUS_TOBEDELETED),
    RESCHEDULING_DELETED(BciStatus.RESCHEDULING.getStatus(), BciSubStatus.STATUS_DELETED),
    RESCHEDULING_CREATING(BciStatus.RESCHEDULING.getStatus(), BciSubStatus.STATUS_CREATING),

    PENDING_DEFAULT(BciStatus.PENDING.getStatus(), BciSubStatus.STATUS_DEFAULT),
    RUNNING_DEFAULT(BciStatus.RUNNING.getStatus(), BciSubStatus.STATUS_DEFAULT),
    SUCCEEDED_DEFAULT(BciStatus.SUCCEEDED.getStatus(), BciSubStatus.STATUS_DEFAULT),
    FAILED_DEFAULT(BciStatus.FAILED.getStatus(), BciSubStatus.STATUS_DEFAULT),
    DELETED_DEFAULT(BciStatus.DELETED.getStatus(), BciSubStatus.STATUS_DEFAULT);

    private String status;
    private String subStatus;

    public static final String STATUS_TOBEDELETED = "ToBeDeleted";
    public static final String STATUS_DELETED = "Deleted";
    public static final String STATUS_CREATING = "Creating";

    public static final String STATUS_DEFAULT = "Default";

    BciSubStatus(String status, String subStatus) {
        this.status = status;
        this.subStatus = subStatus;
    }

    public String getStatus() {
        return status;
    }
    public String getSubStatus() {
        return subStatus;
    }

    public static String getSubStatus(String subStatus) {
        for (BciSubStatus bciSubStatus : values()) {
            if (bciSubStatus.getSubStatus().equalsIgnoreCase(subStatus)) {
                return bciSubStatus.getSubStatus();
            }
        }
        return subStatus;
    }

    public static String getStatus(String status) {
        for (BciSubStatus bciSubStatus : values()) {
            if (bciSubStatus.getStatus().equalsIgnoreCase(status)) {
                return bciSubStatus.getStatus();
            }
        }
        return status;
    }
}
