package com.baidu.bce.logic.bci.servicev2.reservedinstance;


import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalQuotaServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalTagServiceV2;
import com.baidu.bce.logic.bci.servicev2.model.BciStockInfo;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.FlavorItem;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.baidu.bce.externalsdk.logical.network.common.utils.JsonUtil;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstanceSpec;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceResponse;
import com.baidu.bce.logic.bci.servicev2.model.DeleteReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.model.ReservedInstanceExtra;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.ReservedInstanceValidator;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("ReservedInstanceService")
public class ReservedInstanceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReservedInstanceService.class);
    private static final String QUERY_LOGIC_FAILED = "[query ReservedInstance failed] ";
    private static final String LOG_LIST_PAGE_PREFIX = "[list ReservedInstance by page] ";
    private static final String LOG_CREATE_PREFIX = "[create ReservedInstance] ";

    @Value("${order.timeout.millis:45000}")
    private int orderTimeoutMillis;

    @Autowired
    private ReservedInstanceDao reservedInstanceDao;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    private ReservedInstanceValidator validator;

    @Autowired
    protected RegionConfiguration regionConfiguration;
    
    @Autowired
    private PodServiceV2 podServiceV2;

    @Autowired
    private LogicalQuotaServiceV2 logicalQuotaService;
    
    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;
    
    @Autowired
    private LogicalTagServiceV2 logicalTagService;

    public String getAccountId() {
        return LogicUserService.getAccountId();
    }

    public String getUserId() {
        return LogicUserService.getUserId();
    }

    public LogicPageResultResponse<ReservedInstanceSpec> listReservedInstanceSpecs() {
        LogicPageResultResponse<ReservedInstanceSpec> resultResponse = new LogicPageResultResponse<>();
        try {
            List<ReservedInstanceSpec> reservedInstanceSpecList = reservedInstanceDao.listReservedInstanceSpecs();
            ZoneClient zoneClient = logicPodClientFactory.createZoneClient(getAccountId());
            boolean userPostpaySupport = commonUtils.checkWhiteList(LogicalConstant.ENABLE_RESERVED_INSTANCE_POSTPAY, 
                    regionConfiguration.getCurrentRegion(), getAccountId());
            // 客户物理可用区-逻辑可用区映射缓存
            Map<String, String> zoneMap = new HashMap<>();
            for (int i = 0; i < reservedInstanceSpecList.size(); i++) {
                // 可用区级预留实例券针对物理可用区备案，需映射成逻辑可用区供前端使用
                String physicalZone = reservedInstanceSpecList.get(i).getPhysicalZone();
                if (physicalZone != null && !physicalZone.isEmpty()) {
                    String logicalZone = "";
                    if (zoneMap.get(physicalZone) != null) {
                        logicalZone = zoneMap.get(physicalZone);
                    } else {
                        ZoneMapDetail zoneMapDetail = zoneClient.createZoneByPhysicalZone(physicalZone);
                        if (zoneMapDetail != null) {
                            zoneMap.put(physicalZone, zoneMapDetail.getLogicalZone());
                            logicalZone = zoneMapDetail.getLogicalZone();   
                        }
                    }
                    reservedInstanceSpecList.get(i).setLogicalZone(logicalZone);
                    reservedInstanceSpecList.get(i).setPhysicalZone("");
                }
                // 如果备案过后付费套餐
                if (reservedInstanceSpecList.get(i).isPostpaySupport()) {
                    // 根据用户是否在预留实例券后付费白名单情况，调整后付费支持
                    if (!userPostpaySupport) {
                        reservedInstanceSpecList.get(i).setPostpaySupport(userPostpaySupport);
                    }
                }
            }
            resultResponse.setTotalCount(reservedInstanceSpecList.size());
            resultResponse.setResult(reservedInstanceSpecList);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    /**
     * 分页查询预留实例券列表
     *
     * @param listRequest 查询条件和分页参数
     * @return 分页结果响应，包含查询结果和分页信息
     */
    public LogicPageResultResponse<ReservedInstancePO> listReservedInstancesWithPageByMultiKey(
            ListRequest listRequest) {
        LogicPageResultResponse<ReservedInstancePO> resultResponse = new LogicPageResultResponse<ReservedInstancePO>();
        OrderModel orderModel = listRequest.getOrders() == null ? new OrderModel() : listRequest.getOrders().get(0);
        resultResponse.setOrder(orderModel.getOrder());
        resultResponse.setOrderBy(orderModel.getOrderBy());
        resultResponse.setPageNo(listRequest.getPageNo());
        resultResponse.setPageSize(listRequest.getPageSize());
        ListRequest validatedRequest = validator.validateListReservedInstanceRequest(listRequest);

        try {
            List<ReservedInstancePO> reservedInstanceList = reservedInstanceDao.listReservedInstancesByMultiKey(
                getAccountId(), validatedRequest);
            // 减少物理可用区暴露
            for (int i = 0; i != reservedInstanceList.size(); i++) {
                reservedInstanceList.get(i).setPhysicalZone("");
            }
            // 封装标签信息
            logicalTagService.addTagInfoToReservedInstancePO(reservedInstanceList);
            int reservedInstanceNum = reservedInstanceDao.getReservedInstanceNumByMultiKey(getAccountId(), 
                    validatedRequest);

            resultResponse.setTotalCount(reservedInstanceNum);
            resultResponse.setResult(reservedInstanceList);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    public CreateReservedInstanceResponse createReservedInstance(
            BaseCreateOrderRequestVo<CreateReservedInstanceRequest> request) {
        LOGGER.debug(LOG_CREATE_PREFIX + ", parameter is {}", JsonUtil.toJSON(request));
        CreateReservedInstanceResponse resp = new CreateReservedInstanceResponse();
        validator.validate(request);
        LOGGER.debug(LOG_CREATE_PREFIX + ", validated request is {}", JsonUtil.toJSON(request));

        for (BaseCreateOrderRequestVo.Item<CreateReservedInstanceRequest> item : request.getItems()) {
            if (!checkSupplyAndDemand(item.getConfig(), "")) {
                throw new CommonExceptions.RequestInvalidException("Your quota is insufficient, please submit ticket to apply for a quota increase");
            }
            // 预留实例券后付费白名单校验
            if (item.getConfig().getPurchaseMode().equals(LogicalConstant.ReservedInstancePurchaseMode.POSTPAY)) {
                if (!commonUtils.checkWhiteList(LogicalConstant.ENABLE_RESERVED_INSTANCE_POSTPAY, 
                        regionConfiguration.getCurrentRegion(), getAccountId())) {
                    throw new CommonExceptions.RequestInvalidException("Please submit ticket to apply postpay");
                }
            }
        }
    
        CreateOrderRequest<CreateNewTypeOrderItem>  createOrderRequest = new CreateOrderRequest<>();
        createOrderRequest.setOrderType(OrderType.NEW.name());
        createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createOrderRequest.setTotal(request.getTotal());
        createOrderRequest.setTicketId(request.getTicketId());
        createOrderRequest.setPaymentMethod(request.getPaymentMethod());

        List<CreateNewTypeOrderItem> createNewTypeOrderRequestItems = new ArrayList<>();
        List<ReservedInstancePO> reservedInstances = new ArrayList<>();
        constructOrderItemsAndReservedInstancesByRequest(request, createNewTypeOrderRequestItems,
                reservedInstances);
        int ret = reservedInstanceDao.batchInsertReservedInstance(reservedInstances);
        if (ret != reservedInstances.size()) {
            throw new CommonExceptions.InternalServerErrorException();
        }
        List<String> reservedInstanceIds = new ArrayList<>();
        for (ReservedInstancePO po : reservedInstances) {
            reservedInstanceIds.add(po.getReservedInstanceId());
        }
        
        createOrderRequest.setItems(createNewTypeOrderRequestItems);
        OrderUuidResult orderUuidResult;
        try {
            orderUuidResult = podServiceV2.submitCreateOrderToServiceCatalog(createOrderRequest, LOG_CREATE_PREFIX);
        } catch (Exception e) {
            LOGGER.error(LOG_CREATE_PREFIX + ", failed to create order, " +
                    "update ReservedInstance {} to deleted", reservedInstanceIds);
            for (ReservedInstancePO po : reservedInstances) {
                // 获取插入数据库时设置的自增id
                ReservedInstancePO poInDatabase = reservedInstanceDao.getReservedInstanceByReservedInstanceId(
                        po.getReservedInstanceId());
                reservedInstanceDao.delete(poInDatabase.getId());
            }
            throw e;
        }

        LOGGER.debug(LOG_CREATE_PREFIX + ", create order result of {} is {}", reservedInstanceIds, orderUuidResult);
        reservedInstanceDao.updateOrderId(reservedInstanceIds, getAccountId(), orderUuidResult.getOrderId());

        resp.setReservedInstanceId(reservedInstanceIds);
        resp.setOrderId(orderUuidResult.getOrderId());
        return resp;
    }

    public void deleteReservedInstance(DeleteReservedInstanceRequest request) {
        reservedInstanceDao.deleteByReservedInstanceUuid(request.getReservedInstanceUuid());
    }

    private ReservedInstancePO constructReservedInstancePOByCreateReservedInstanceRequest(
            CreateReservedInstanceRequest req) {
        ReservedInstancePO reservedInstancePO = new ReservedInstancePO();
        reservedInstancePO.setName(req.getName());
        reservedInstancePO.setScope(req.getScope());
        reservedInstancePO.setLogicalZone(req.getLogicalZone());
        reservedInstancePO.setPhysicalZone(req.getPhysicalZone());
        reservedInstancePO.setReserveResource(req.isReserveResource());
        reservedInstancePO.setPurchaseMode(req.getPurchaseMode());
        reservedInstancePO.setReservedSpec(req.getReservedSpec());
        reservedInstancePO.setReservedInstanceCount(req.getReservedInstanceCount());
        reservedInstancePO.setReservedTimeUnit(req.getReservedTimeUnit());
        reservedInstancePO.setReservedTimePeriod(req.getReservedTimePeriod());
        reservedInstancePO.setAutoRenew(req.isAutoRenew());
        if (req.isAutoRenew()) {
            reservedInstancePO.setAutoRenewTimeUnit(req.getAutoRenewTimeUnit());
            reservedInstancePO.setAutoRenewTimePeriod(req.getAutoRenewTimePeriod());
        }
        if (req.getEffectiveTime() == null) {
            reservedInstancePO.setEffectiveTime(new Timestamp(System.currentTimeMillis()));
        } else {
            reservedInstancePO.setEffectiveTime(req.getEffectiveTime());
        }
        reservedInstancePO.setExpireTime(ReservedInstancePO.calcExpireTime(req.getEffectiveTime(),
                req.getReservedTimeUnit(), req.getReservedTimePeriod()));
        reservedInstancePO.setReservedInstanceId(commonUtils.createExternalId("r"));
        reservedInstancePO.setAccountId(getAccountId());
        reservedInstancePO.setUserId(getUserId());
        // 统一设置为 need_purchase, 等订单状态更新后同步
        reservedInstancePO.setStatus(ReservedInstancePO.Status.NEED_PURCHASE);
        reservedInstancePO.setDeleted(false);
        reservedInstancePO.setCreatedTime(new Timestamp(System.currentTimeMillis()));
        reservedInstancePO.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
        return reservedInstancePO;
    }

    private CreateNewTypeOrderItem constructCreateNewTypeOrderItemByCreateReservedInstanceRequest(
            CreateReservedInstanceRequest req) {
        LinkedHashSet<FlavorItem> flavor = new LinkedHashSet<>();            
        FlavorItem flavorItem;
        // 根据备案的预留实例券类型，组装flavor
        if (req.getReservedSubServiceType().equals(LogicalConstant.ReservedInstanceSubServiceType.GPU_GENERIC)) {
            flavorItem = new FlavorItem();
            flavorItem.setName("subServiceType");
            flavorItem.setValue("GPU");
            flavorItem.setScale(new BigDecimal("1"));
            flavor.add(flavorItem);

            flavorItem = new FlavorItem();
            flavorItem.setName(LogicalConstant.GPU);
            flavorItem.setValue(req.getReservedSpec());
            flavorItem.setScale(new BigDecimal(1));
            flavor.add(flavorItem);

            flavorItem = new FlavorItem();
            flavorItem.setName("deductPolicy");
            flavorItem.setValue("reservedPackagePolicyGpuZone");
            flavor.add(flavorItem);
        } else if (req.getReservedSubServiceType().equals(
                LogicalConstant.ReservedInstanceSubServiceType.CPU_GENERIC)) {
            ReservedInstanceSpec spec = reservedInstanceDao.getSpecBySpecName(req.getReservedSpec());
            if (spec == null) {
                throw new BceException(String.format("unsupported reserved spec %s", 
                        req.getReservedSpec()));
            }
            flavorItem = new FlavorItem();
            flavorItem.setName("subServiceType");
            flavorItem.setValue("default");
            flavorItem.setScale(new BigDecimal("1"));
            flavor.add(flavorItem);

            flavorItem = new FlavorItem();
            flavorItem.setName("CpuRunTime");
            flavorItem.setValue(String.valueOf(spec.getVcpuNum()));
            flavorItem.setScale(new BigDecimal("1"));
            flavor.add(flavorItem);

            flavorItem = new FlavorItem();
            flavorItem.setName("RamRunTime");
            flavorItem.setValue(String.valueOf(spec.getMemGB()));
            flavorItem.setScale(new BigDecimal(1));
            flavor.add(flavorItem);

            flavorItem = new FlavorItem();
            flavorItem.setName("deductPolicy");
            flavorItem.setValue("reservedPackagePolicyBciFixedCpuRegion");
            flavor.add(flavorItem);
        } else {
            throw new BceException(String.format("unsupported reserved sub service type %s", 
                    req.getReservedSubServiceType()));
        }
        
        // 可用区级预留实例券加入可用区信息
        if (req.getScope().equals(LogicalConstant.ReservedInstanceScope.AZ)) {
            flavorItem = new FlavorItem();
            flavorItem.setName("logicalZone");
            flavorItem.setValue(req.getLogicalZone());
            flavorItem.setScale(new BigDecimal(1));
            flavor.add(flavorItem);
            flavorItem = new FlavorItem();
            flavorItem.setName("physical_zone");
            flavorItem.setValue(req.getPhysicalZone());
            flavorItem.setScale(new BigDecimal(1));
            flavor.add(flavorItem);
        }

        flavorItem = new FlavorItem();
        flavorItem.setName("deductInstanceNum");
        flavorItem.setValue(String.valueOf(req.getReservedInstanceCount()));
        flavorItem.setScale(new BigDecimal(1));
        flavor.add(flavorItem);
        flavorItem = new FlavorItem();
        flavorItem.setName("purchaseMode");
        flavorItem.setValue(req.getPurchaseMode());
        flavorItem.setScale(new BigDecimal(1));
        flavor.add(flavorItem);

        String timeGranularity = "P" + String.valueOf(req.getReservedTimePeriod());
        if (req.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.YEAR)) {
            timeGranularity = timeGranularity + "Y";
        } else if (req.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.MONTH)) {
            timeGranularity = timeGranularity + "M";
        } else if (req.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.DAY)) {
            timeGranularity = timeGranularity + "D";
        }
        flavorItem = new FlavorItem();
        flavorItem.setName("timeGranularity");
        flavorItem.setValue(timeGranularity);
        flavorItem.setScale(new BigDecimal(1));
        flavor.add(flavorItem);

        CreateNewTypeOrderItem createNewTypeOrderItem = new CreateNewTypeOrderItem();
        createNewTypeOrderItem.setCount(1);
        createNewTypeOrderItem.setServiceType("BCI");
        createNewTypeOrderItem.setProductType("prepay");
        createNewTypeOrderItem.setSubProductType("ReservedPackage");
        createNewTypeOrderItem.setPurchaseMode(req.getPurchaseMode());
        createNewTypeOrderItem.setTime(new BigDecimal(req.getReservedTimePeriod()));
        createNewTypeOrderItem.setTimeUnit(req.getReservedTimeUnit());
        createNewTypeOrderItem.setFlavor(flavor);
        return createNewTypeOrderItem;
    }

    private void constructOrderItemsAndReservedInstancesByRequest(
            BaseCreateOrderRequestVo<CreateReservedInstanceRequest> request,
            List<CreateNewTypeOrderItem> orderItems, List<ReservedInstancePO> reservedInstances) {
        for (BaseCreateOrderRequestVo.Item<CreateReservedInstanceRequest> item : request.getItems()) {
            CreateReservedInstanceRequest req = item.getConfig();
            ReservedInstancePO reservedInstancePO = constructReservedInstancePOByCreateReservedInstanceRequest(req);
            reservedInstances.add(reservedInstancePO);
            CreateNewTypeOrderItem createNewTypeOrderItem = 
                    constructCreateNewTypeOrderItemByCreateReservedInstanceRequest(req);
            createNewTypeOrderItem.setPaymentMethod(item.getPaymentMethod());
            // 通过这里的 key 作为关键字区分多个包
            createNewTypeOrderItem.setKey(reservedInstancePO.getReservedInstanceId());
            // 订单接口用的0时区时间，和前端传的localtime会存在差异，这里做下调整
            Timestamp resourceActiveTime = new Timestamp(reservedInstancePO.getEffectiveTime().getTime() - 
                    3600 * 8 * 1000);
            createNewTypeOrderItem.setResourceActiveTime(resourceActiveTime);
            // 自动续费配置放在 extra 里
            createNewTypeOrderItem.setExtra(genExtra(req));
            orderItems.add(createNewTypeOrderItem);
        }
    }

    /**
     *  * 生成 OrderExtra
     *
     * @param req 创建预留实例请求对象。
     * @return 字符串形式的额外信息。
     * @throws BceException 生成订单额外信息失败时抛出该异常。
     */
    private String genExtra(CreateReservedInstanceRequest req) {
        ReservedInstanceExtra extra = new ReservedInstanceExtra();
        extra.setAutoRenew(req.isAutoRenew());
        extra.setAutoRenewTimePeriod(req.getAutoRenewTimePeriod());
        extra.setAutoRenewTimeUnit(req.getAutoRenewTimeUnit());
        extra.setTags(req.getTags());
        ObjectMapper objectMapper = new ObjectMapper();
        String extraString = "";
        try {
            extraString = objectMapper.writeValueAsString(extra);
        } catch (Exception e) {
            throw new BceException("gen order extra failed");
        }
        return extraString;
    }

    /**
     * 判断供给-需求是否满足本次购买请求
     * @param request
     * @return
     */
    public Boolean checkSupplyAndDemand(CreateReservedInstanceRequest request, String accountId) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (accountId.isEmpty()) {
            accountId = getAccountId();
        }
        // 组装本次预留实例券购买请求参数
        BciStockInfo bciStockInfoReq = new BciStockInfo();
        bciStockInfoReq.setUserId(accountId);
        if (request.getReservedSubServiceType().equals(LogicalConstant.ReservedInstanceSubServiceType.CPU_GENERIC)) {
            bciStockInfoReq.setType("CPU");
        } else if (request.getReservedSubServiceType().equals(
                LogicalConstant.ReservedInstanceSubServiceType.GPU_GENERIC)) {
            bciStockInfoReq.setType("GPU");
        }
        
        bciStockInfoReq.setSpec(request.getReservedSpec());
        bciStockInfoReq.setAz(request.getPhysicalZone());
        bciStockInfoReq.setRegion(regionConfiguration.getCurrentRegion());
        bciStockInfoReq.setScope(request.getScope());

        // 开始时间 = 生效时间
        bciStockInfoReq.setStartTime(format.format(request.getEffectiveTime()));
        if (StringUtils.isEmpty(request.getRenewDeadline())) {
            if (StringUtils.isEmpty(request.getReservedTimeUnit())) {
                throw new CommonExceptions.InternalServerErrorException();
            }
            // 结束时间 = 生效时间 + 预留时长
            if (request.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.DAY)) {
                bciStockInfoReq.setEndTime(format.format(DateUtils.addDays(request.getEffectiveTime(),
                        request.getReservedTimePeriod())));
            }
            if (request.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.MONTH)) {
                bciStockInfoReq.setEndTime(format.format(DateUtils.addMonths(request.getEffectiveTime(),
                        request.getReservedTimePeriod())));
            }
            if (request.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.YEAR)) {
                bciStockInfoReq.setEndTime(format.format(DateUtils.addYears(request.getEffectiveTime(),
                        request.getReservedTimePeriod())));
            }
        } else {
            // 结束时间 = 续费计算的结束时间
            bciStockInfoReq.setEndTime(request.getRenewDeadline());
        }

        bciStockInfoReq.setCount(request.getReservedInstanceCount());
        Integer reqCount = bciStockInfoReq.getCount();

        // 查询用户配额情况（供给）
        BciStockInfo bciStockInfoSupply = logicalQuotaService.getBciStockSupply(bciStockInfoReq);
        if (0 == bciStockInfoSupply.getCount()) {
            return false;
        }

        // 查询数据库未失效预留实例券情况（需求）
        BciStockInfo bciStockInfoDemand = this.getBciStockDemand(bciStockInfoReq);

        // 供给 - 需求 >= 本次请求则返回true，否则返回false
        if (bciStockInfoSupply.getCount() - bciStockInfoDemand.getCount() >= reqCount) {
            return true;
        }
        return false;
    }

    /**
     * 根据规格，可用区，用户id，生效时间，失效时间获取未失效的预留实例券个数
     * @param bciStockInfoReq
     * @return
     */
    public BciStockInfo getBciStockDemand(BciStockInfo bciStockInfoReq) {
        Integer count = reservedInstanceDao.getBciStockDemand(bciStockInfoReq.getSpec(), bciStockInfoReq.getAz(),
                bciStockInfoReq.getUserId(), bciStockInfoReq.getStartTime(), bciStockInfoReq.getEndTime());
        bciStockInfoReq.setCount(null == count ? 0 : count);
        return bciStockInfoReq;
    }

    public LogicPageResultResponse<PodPO> getPodByReservedInstanceId(String reservedInstanceId,
                                                                     PodListRequest listRequest) {
        LogicPageResultResponse<PodPO> pageResultResponse = new LogicPageResultResponse();
        ReservedInstancePO reservedInstancePO =
                reservedInstanceDao.getReservedInstanceByReservedInstanceId(reservedInstanceId);
        // 找不到预留实例券，或者预留实例券已经过期时，返回空
        if (null == reservedInstancePO || reservedInstancePO.getStatus().equals(ReservedInstancePO.Status.EXPIRED)) {
            return pageResultResponse;
        }
        String spec = reservedInstancePO.getReservedSpec();
        ReservedInstanceSpec reservedInstanceSpec = reservedInstanceDao.getSpecBySpecName(spec);

        Map<String, String> filterMap = new HashMap<>();
        if (reservedInstancePO.getScope().equals(LogicalConstant.ReservedInstanceScope.AZ)) {
            filterMap.put("zoneId", validator.getZoneByPhysicalZone(reservedInstancePO.getPhysicalZone()).getZoneId());
        }
        // 只显示切 cpt1 计费的 pod
        filterMap.put("cpt1", "1");
        filterMap.put("vCpu", String.valueOf(reservedInstanceSpec.getVcpuNum()));
        filterMap.put("memory", String.valueOf(reservedInstanceSpec.getMemGB()));
        if (reservedInstanceSpec.getDeductInstanceFamily().equals(
                LogicalConstant.ReservedInstanceSubServiceType.GPU_GENERIC)) {
            filterMap.put("gpuType", reservedInstanceSpec.getGpuName());
            filterMap.put("gpuCount", String.valueOf(reservedInstanceSpec.getGpuNum()));
        }

        listRequest.setFilterMap(filterMap);

        return podServiceV2.listPodsWithPageByMultiKey(listRequest);
    }
}