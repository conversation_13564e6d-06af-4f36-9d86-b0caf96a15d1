package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.cceuser.CceUserMapDao;
import com.baidu.bce.logic.bci.daov2.chargestatus.PodChargeStatusDaoV2;
import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.ChargeStatus;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

import java.sql.Timestamp;

public abstract class SyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(SyncServiceV2.class);

    @Autowired
    protected CceUserMapDao cceUserDao;

    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    protected ContainerDaoV2 containerDao;

    @Autowired
    protected PodChargeStatusDaoV2 podChargeStatusDao;

    @Autowired
    protected LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private BillingResourceSyncManagerV2 billingResourceSyncManager;

    public void chargeStatus(String podPhase, PodPO podPO, Timestamp insertTime, boolean isPodInBuild) {
        String currentChargeStatus = ChargeStatus.getStatus(podPhase);
        if (podPO.getCpt1()) {
            currentChargeStatus = PodUtils.getChargeStatusOfCpt1Instance(
                podPhase, podPO.getDelayReleaseDurationMinute(), podPO.isDelayReleaseSucceeded(), 
                podPO.getResourceRecycleTimestamp(), podPO.getResourceRecycleComplete(), 
                podPO.getResourceRecycleReason());
        }

        PodChargeStatus podChargeStatus = new PodChargeStatus();
        podChargeStatus.setPodUuid(podPO.getPodUuid());
        podChargeStatus.setPreviousState(podPO.getStatus());
        podChargeStatus.setCurrentState(podPhase);
        podChargeStatus.setChargeState(currentChargeStatus);
        podChargeStatus.setResourceVersion(podPO.getResourceVersion());
        podChargeStatus.setCpt1SyncState(podPO.getIntCpt1Mode());
        podChargeStatus.setCreatedTime(insertTime);
        podChargeStatus.setUpdateTime(insertTime);
        try {
            if (isPodInBuild) {
                if (!podPO.getCpt1()) {
                    // 在 SyncPodInBuild 使用时，避免重复插入 charge 信息
                    // 由于cpt1计费的pod订单更新CREATED后自动计费，不发开机请求，charge 记录由 podContainerSync 插入
                    podChargeStatusDao.insertWhenPodUnsync(podChargeStatus);
                }
            } else {
                podChargeStatusDao.insert(podChargeStatus);
                if (podPO.getCpt1()) {
                    billingResourceSyncManager.doSync(podPO, podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                            podPO.getPodUuid(), podPO.getResourceVersion(), currentChargeStatus, 0);
                    LOGGER.debug("[call billing] finish insert billing sync task in chargeStatus2, id {}, account {}, pod {}, resource version {}, " +
                            "current status {}", podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                            podPO.getPodUuid(), podPO.getResourceVersion(), currentChargeStatus);
                }
            }
        } catch (DuplicateKeyException e) {
            LOGGER.info("insert pod charge status failed because other thread already insert record, " +
                "pod uuid {}, resource version {}, current status {}", podPO.getPodUuid(), 
                podPO.getResourceVersion(), currentChargeStatus);
            return;
        }
    }

    // only used by ut
    public void setBillingResourceSyncManager(BillingResourceSyncManagerV2 billingResourceSyncManager) {
        this.billingResourceSyncManager = billingResourceSyncManager;
    }
}
