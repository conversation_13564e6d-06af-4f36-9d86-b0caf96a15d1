package com.baidu.bce.logic.bci.servicev2.model;

import lombok.Data;

import java.util.List;

import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReservedInstanceExtra {
    private boolean autoRenew;
    private String autoRenewTimeUnit;
    private int autoRenewTimePeriod;
    private List<Tag> tags;
}
