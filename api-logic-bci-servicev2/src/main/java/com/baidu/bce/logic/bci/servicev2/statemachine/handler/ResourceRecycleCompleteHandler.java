package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.BciSubStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecyleComplete;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.ResourceRecyleCompleteContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("RESOURCE_RECYCLE_COMPLETE")
public class ResourceRecycleCompleteHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(ResourceRecycleCompleteHandler.class);

    private static final List<String> ALLOWED_STATUS = Arrays.asList(
            BciStatus.FAILED.getStatus(),
            BciStatus.SUCCEEDED.getStatus(),
            BciStatus.DELETED.getStatus()
    );
    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof ResourceRecyleCompleteContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        StateMachineContext context = getContext();
        PodPO podPO = context.getPodPO();
        if (!ALLOWED_STATUS.contains(podPO.getStatus())) {
            LOGGER.warn("ResourceRecycleCompleteHandler pod status is not allowed, podId:{}, status:{}, event:{}",
                    podPO.getPodId(), podPO.getStatus(), context.getEvent());
        }

        if (podPO.getResourceRecycleTimestamp() == 0) {
            return false;
        }

        if (podPO.getResourceRecycleComplete() != ResourceRecyleComplete.INCOMPLETE) {
            return false;
        }
        return true;
    }

    @Override
    public boolean execute() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        PodPO podPO = getPodPO();
        podPO.setSubStatus(BciSubStatus.STATUS_DEFAULT);
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        podPO.setResourceRecycleComplete(ResourceRecyleComplete.COMPLETED);
        podPO.setResourceRecycleCompleteTimestamp(System.currentTimeMillis());
        int resourceRecycleCompleteResult = podDao.stateMachineResourceRecycleComplete(podPO, podDaoContext);
        if (resourceRecycleCompleteResult == 0) {
            return false;
        }
        return true;
    }
}
