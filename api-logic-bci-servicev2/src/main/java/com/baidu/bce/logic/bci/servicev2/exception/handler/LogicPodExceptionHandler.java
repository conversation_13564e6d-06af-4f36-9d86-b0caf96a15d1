package com.baidu.bce.logic.bci.servicev2.exception.handler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.trail.EventBuilder;
import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.bci.servicev2.exception.BackendExceptions;
import com.baidu.bce.plat.webframework.exception.BceException;

/**
 * Neutron异常处理类
 */

public class LogicPodExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogicPodExceptionHandler.class);

    private static final String ERROR_CODE_RESOURCE_IN_TASK = "ResourceInTaskException";
    private static final String ERROR_CODE_ORDER_PAY_FAILED = "NotEnoughBalanceForPayOrder";
    private static final String INSUFFICIENT_BALANCE = "InsufficientBalance";

    private static void handleBceInternalResponseException(EventBuilder eventBuilder,
                                                           BceInternalResponseException exception) {
        eventBuilder.error(exception);
        eventBuilder.errorCode(String.valueOf(exception.getHttpStatus()));
        eventBuilder.errorMessage(exception.getRequestId());
        handleBceInternalResponseException(exception);
    }

    private static void handleBceInternalResponseException(BceInternalResponseException exception) {

        if (ERROR_CODE_RESOURCE_IN_TASK.equalsIgnoreCase(exception.getCode())) {
            throw new BackendExceptions.RequestLockException();
        } else if (ERROR_CODE_ORDER_PAY_FAILED.equalsIgnoreCase(exception.getCode())) {
            throw new BackendExceptions.OrderPayFailedException();
        } else if (INSUFFICIENT_BALANCE.equalsIgnoreCase(exception.getCode())) {
            throw new CommonExceptions.InsufficientBalanceException();
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_INPUT_INVALID) {
            throw new BackendExceptions.RequestInvalidException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_PERMISSION_DENY
                || exception.getHttpStatus() == HttpStatus.ERROR_OPERATION_DENY) {
            throw new BackendExceptions.PermissionDenyException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_RESOURCE_NOT_EXIST) {
            throw new BackendExceptions.ResourceNotExistException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_OPERATION_TYPE) {
            throw new BackendExceptions.OperationTypeErrorException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_OPERATION_NOT_AVAILABLE) {
            throw new BackendExceptions.OperationErrorException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_TOO_MANY) {
            throw new BackendExceptions.ExceedLimitException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_MEDIA
                || exception.getHttpStatus() == HttpStatus.ERROR_COMPUTER
                || exception.getHttpStatus() == HttpStatus.ERROR_SERVICE) {
            throw new CommonExceptions.InternalServerErrorException();
        } else {
            LOGGER.debug("catch BceInternalResponseException message: {}", exception);
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    private static void handleBceInternalResponseException(BceInternalResponseException exception,
                                                           String messagePrefix) {

        if (ERROR_CODE_RESOURCE_IN_TASK.equalsIgnoreCase(exception.getCode())) {
            throw new BackendExceptions.RequestLockException();
        } else if (ERROR_CODE_ORDER_PAY_FAILED.equalsIgnoreCase(exception.getCode())) {
            throw new BackendExceptions.OrderPayFailedException();
        } else if (INSUFFICIENT_BALANCE.equalsIgnoreCase(exception.getCode())) {
            throw new CommonExceptions.InsufficientBalanceException();
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_INPUT_INVALID) {
            throw new BackendExceptions.RequestInvalidException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_PERMISSION_DENY
                || exception.getHttpStatus() == HttpStatus.ERROR_OPERATION_DENY) {
            throw new BackendExceptions.PermissionDenyException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_RESOURCE_NOT_EXIST) {
            throw new BackendExceptions.ResourceNotExistException(exception, messagePrefix);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_OPERATION_TYPE) {
            throw new BackendExceptions.OperationTypeErrorException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_OPERATION_NOT_AVAILABLE) {
            throw new BackendExceptions.OperationErrorException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_TOO_MANY) {
            throw new BackendExceptions.ExceedLimitException(exception);
        } else if (exception.getHttpStatus() == HttpStatus.ERROR_MEDIA
                || exception.getHttpStatus() == HttpStatus.ERROR_COMPUTER
                || exception.getHttpStatus() == HttpStatus.ERROR_SERVICE) {
            throw new CommonExceptions.InternalServerErrorException();
        } else {
            LOGGER.debug("catch BceInternalResponseException message: {}", exception);
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    public static void handle(Exception e) {
        if (e instanceof BceInternalResponseException) {
            handleBceInternalResponseException((BceInternalResponseException) e);
        } else if (e instanceof BceException) {
            throw (BceException) e;
        } else if (e instanceof DataAccessException) {
            LOGGER.error("logical database error, the info is :", e);
            throw new CommonExceptions.InternalServerErrorException();
        } else {
            LOGGER.error("logical internal error, the info is :", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    public static void handle(Exception e, String messagePrefix) {
        if (e instanceof BceInternalResponseException) {
            handleBceInternalResponseException((BceInternalResponseException) e, messagePrefix);
        } else if (e instanceof BceException) {
            throw (BceException) e;
        } else if (e instanceof DataAccessException) {
            LOGGER.error("logical database error, the info is :", e);
            throw new CommonExceptions.InternalServerErrorException();
        } else {
            LOGGER.error("logical internal error, the info is :", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    public static void handle(EventBuilder event, Exception e) {
        if (e instanceof BceInternalResponseException) {
            handleBceInternalResponseException(event, (BceInternalResponseException) e);
        } else if (e instanceof BceException) {
            throw (BceException) e;
        } else if (e instanceof DataAccessException) {
            throw new CommonExceptions.InternalServerErrorException();
        } else {
            throw new CommonExceptions.InternalServerErrorException();
        }
    }
    public static void throwPermissionDeniedExceptionIfAppropriate(Exception ex) {
        if (ex instanceof BceInternalResponseException) {
            BceInternalResponseException e = (BceInternalResponseException) ex;
            if (e.getHttpStatus() == HttpStatus.ERROR_OPERATION_DENY) {
                throw new CommonExceptions.OperationDeniedException();
            } else if (e.getHttpStatus() == HttpStatus.ERROR_OPERATION_NOT_AVAILABLE) {
                throw new CommonExceptions.ResourceInTaskException();
            } else if (ERROR_CODE_ORDER_PAY_FAILED.equalsIgnoreCase(e.getCode())) {
                throw new CommonExceptions.PaymentFailedException();
            } else if (INSUFFICIENT_BALANCE.equalsIgnoreCase(e.getCode())) {
                throw new CommonExceptions.InsufficientBalanceException();
            }
        }
    }
}
