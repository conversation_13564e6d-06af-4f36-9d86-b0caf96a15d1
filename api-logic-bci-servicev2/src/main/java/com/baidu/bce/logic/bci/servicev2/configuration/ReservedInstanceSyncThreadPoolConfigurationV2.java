package com.baidu.bce.logic.bci.servicev2.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ReservedInstanceSyncThreadPoolConfigurationV2 {
    @Value("${reserved.instance.sync.thread.pool.core.size:10}")
    private Integer threadPoolCoreSize;
    @Value("${reserved.instance.sync.thread.pool.max.size:50}")
    private Integer threadPoolMaxSize;
    @Value("${reserved.instance.sync.thread.pool.queue.capacity:10000}")
    private Integer threadPoolQueueCapacity;
    @Value("${reserved.instance.sync.thread.keepalive.seconds:60}")
    private Integer threadKeepAliveSeconds;

    @Bean(name = "reservedInstanceSyncThreadPoolTaskExecutorV2")
    public ThreadPoolTaskExecutor reservedInstanceSyncThreadPoolTaskExecutorV2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(threadPoolQueueCapacity);
        executor.setKeepAliveSeconds(threadKeepAliveSeconds);

        String threadNamePrefix = "BciV2-Reserved-Instance-Sync-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);
        return executor;
    }
}
