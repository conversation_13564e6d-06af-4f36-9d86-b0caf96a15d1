package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.kubernetes.client.openapi.models.V1SecurityContext;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ContainerSecurityContext {
    // The capabilities to add/drop when running containers.
	// Defaults to the default set of capabilities granted by the container runtime.
	// Note that this field cannot be set when spec.os.name is windows.
	// +optional
    private Capabilities capabilities;

	// The UID to run the entrypoint of the container process.
	// Defaults to user specified in image metadata if unspecified.
	// May also be set in PodSecurityContext.  If set in both SecurityContext and
	// PodSecurityContext, the value specified in SecurityContext takes precedence.
	// Note that this field cannot be set when spec.os.name is windows.
	// +optional
    private Long runAsUser;

	// The GID to run the entrypoint of the container process.
	// Uses runtime default if unset.
	// May also be set in PodSecurityContext.  If set in both SecurityContext and
	// PodSecurityContext, the value specified in SecurityContext takes precedence.
	// Note that this field cannot be set when spec.os.name is windows.
	// +optional
    private Long runAsGroup;

	// Indicates that the container must run as a non-root user.
	// If true, the Kubelet will validate the image at runtime to ensure that it
	// does not run as UID 0 (root) and fail to start the container if it does.
	// If unset or false, no such validation will be performed.
	// May also be set in PodSecurityContext.  If set in both SecurityContext and
	// PodSecurityContext, the value specified in SecurityContext takes precedence.
	// +optional
    private Boolean runAsNonRoot;

	// Whether this container has a read-only root filesystem.
	// Default is false.
	// Note that this field cannot be set when spec.os.name is windows.
	// +optional
    private Boolean readOnlyRootFilesystem;

    private Boolean privileged;

    public V1SecurityContext toV1SecurityContext() {
        V1SecurityContext v1SecurityContext = new V1SecurityContext();
        if (this.capabilities != null) {
            v1SecurityContext.setCapabilities(this.capabilities.toV1Capabilities());
        }
        v1SecurityContext.setRunAsUser(this.runAsUser);
        v1SecurityContext.setRunAsGroup(this.runAsGroup);
        v1SecurityContext.setRunAsNonRoot(this.runAsNonRoot);
        v1SecurityContext.setReadOnlyRootFilesystem(this.readOnlyRootFilesystem);
        v1SecurityContext.setPrivileged(this.privileged);
        // 安全加固，seccompProfile强制置为null,AllowPrivilegeEscalation强制置为false
        v1SecurityContext.setSeccompProfile(null);
        // ds容器需要使用特权模式，所以需要将AllowPrivilegeEscalation置为true
        v1SecurityContext.setAllowPrivilegeEscalation(this.getPrivileged());
        return v1SecurityContext;
    }
}
