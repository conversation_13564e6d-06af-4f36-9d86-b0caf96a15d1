package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.ImmutableSet;
import io.kubernetes.client.openapi.models.V1Capabilities;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class Capabilities {
    private List<String> add;

    private List<String> drop;

    // 白名单列表： https://dockerlabs.collabnix.com/advanced/security/capabilities/
    public static Set<String> whiteList = ImmutableSet.of(
        "CHOWN", "DAC_OVERRIDE", "FSETID", "FOWNER", "MKNOD", "SETGID", "SETUID", 
        "SETFCAP", "SETPCAP", "NET_BIND_SERVICE", "SYS_CHROOT", "KILL", "AUDIT_WRITE", "NET_ADMIN", "NET_RAW"
    );

    public V1Capabilities toV1Capabilities() {
        V1Capabilities v1Capabilities = new V1Capabilities();
        if (CollectionUtils.isNotEmpty(add)) {
            List<String> addCaps = new ArrayList<>();
            addCaps.addAll(this.add);
            v1Capabilities.setAdd(addCaps);
        }

        if (CollectionUtils.isNotEmpty(drop)) {
            List<String> dropCaps = new ArrayList<>();
            dropCaps.addAll(this.drop);
            v1Capabilities.setDrop(dropCaps);
        }

        return v1Capabilities;
    }
}
