package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.kubernetes.client.openapi.models.V1PodSecurityContext;
import io.kubernetes.client.openapi.models.V1Sysctl;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PodSecurityContext {
    // The UID to run the entrypoint of the container process.
    // Defaults to user specified in image metadata if unspecified.
    // May also be set in SecurityContext.  If set in both SecurityContext and
    // PodSecurityContext, the value specified in SecurityContext takes precedence
    // for that container.
    // Note that this field cannot be set when spec.os.name is windows.
    // +optional
    private Long runAsUser;

    // The GID to run the entrypoint of the container process.
    // Uses runtime default if unset.
    // May also be set in SecurityContext.  If set in both SecurityContext and
    // PodSecurityContext, the value specified in SecurityContext takes precedence
    // for that container.
    // Note that this field cannot be set when spec.os.name is windows.
    // +optional
    private Long runAsGroup;

    // Indicates that the container must run as a non-root user.
    // If true, the Kubelet will validate the image at runtime to ensure that it
    // does not run as UID 0 (root) and fail to start the container if it does.
    // If unset or false, no such validation will be performed.
    // May also be set in SecurityContext.  If set in both SecurityContext and
    // PodSecurityContext, the value specified in SecurityContext takes precedence.
    // +optional
    private Boolean runAsNonRoot;

    // A special supplemental group that applies to all containers in a pod.
    // Some volume types allow the Kubelet to change the ownership of that volume
    // to be owned by the pod:
    //
    // 1. The owning GID will be the FSGroup
    // 2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
    // 3. The permission bits are OR'd with rw-rw----
    //
    // If unset, the Kubelet will not modify the ownership and permissions of any volume.
    // +optional
    private Long fsGroup;

    // Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
    // sysctls (by the container runtime) might fail to launch.
    // Note that this field cannot be set when spec.os.name is windows.
    // +optional
    private List<V1Sysctl> sysctls;

    public V1PodSecurityContext toV1PodSecurityContext() {
        V1PodSecurityContext v1PodSecurityContext = new V1PodSecurityContext();
        v1PodSecurityContext.setRunAsUser(this.runAsUser);
        v1PodSecurityContext.setRunAsGroup(this.runAsGroup);
        v1PodSecurityContext.setRunAsNonRoot(this.runAsNonRoot);
        v1PodSecurityContext.setFsGroup(this.fsGroup);
        // 安全加固，seccompProfile强制置为null
        v1PodSecurityContext.setSeccompProfile(null);
        if (CollectionUtils.isNotEmpty(this.sysctls)) {
            v1PodSecurityContext.setSysctls(this.sysctls);
        }
        return v1PodSecurityContext;
    }
}
