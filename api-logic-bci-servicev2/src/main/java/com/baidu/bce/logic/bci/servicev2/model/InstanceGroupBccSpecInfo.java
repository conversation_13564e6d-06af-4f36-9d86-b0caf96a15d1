package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class InstanceGroupBccSpecInfo  {
    private String physicalZone;
    private String bccSpec;
    private int cpu;
    private int memory;
    private int gpuCount;

    public boolean formInstanceGroupBccSpecInfo(String igName) {
        if (igName == null || igName.isEmpty()){
            return false; 
        }
        String[] igNameSplit = igName.split("-");
        if (igNameSplit.length < 3){
            return false;
        }
        // parse zone 
        String physicalZone = String.join("-", igNameSplit[0], igNameSplit[1]);
        // parse bcc spec 
        String bccSpecName = igNameSplit[2];
        // parse cpu and memory 
        String[] bccSpecNameSplit = bccSpecName.split("\\.");
        if (bccSpecNameSplit.length < 2){
            return false ;
        }
        // 获取cpu和内存
        String cpuMemorySpec = bccSpecNameSplit[2];
        String[] cpuMemorySpecSplit = cpuMemorySpec.split("m");
        if (cpuMemorySpecSplit.length < 2){
            return false; 
        }
        int memory = Integer.parseInt(cpuMemorySpecSplit[1]);

        String[] cpuSpint = cpuMemorySpecSplit[0].split("c");
        if (cpuSpint.length < 2){
            return false; 
        }
        int cpu = Integer.parseInt(cpuSpint[1]);
        int gpuCount = 0;

        if (igName.contains("gpu")) {
            gpuCount = 1;
        }
        this.setBccSpec(bccSpecName);
        this.setPhysicalZone(physicalZone);
        this.setCpu(cpu);
        this.setMemory(memory);
        this.setGpuCount(gpuCount);
        return true; 
    }

    public boolean isValid() {
        if (this.getPhysicalZone() == null || this.getPhysicalZone().isEmpty()){
            return false; 
        } 
        if (this.getBccSpec() == null || this.getBccSpec().isEmpty()){
            return false; 
        } 
        if  (this.getCpu() == 0 || this.getMemory() == 0){
            return false; 
        } 
        return true; 
    }
}
