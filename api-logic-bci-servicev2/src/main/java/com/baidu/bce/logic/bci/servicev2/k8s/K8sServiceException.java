package com.baidu.bce.logic.bci.servicev2.k8s;

public class K8sServiceException extends Exception {
  private ErrorCode errorCode;
  private String message;

  private String responseBody = "";

  public K8sServiceException(ErrorCode errorCode, String message) {
    this.errorCode = errorCode;
    this.message = message;
  }

  public K8sServiceException(ErrorCode errorCode, String message, String responseBody) {
    this.errorCode = errorCode;
    this.message = message;
    this.responseBody = responseBody;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }

  public String getMessage() {
    return message;
  }

  public String getResponseBody() {
    return responseBody;
  }

  public enum ErrorCode {
    OK,
    POD_EXISTED,
    POD_NOT_EXISTED,
    CREATE_POD_FAILED,
    UPDATE_POD_FAILED,
    DELETE_POD_FAILED,
    NAMESPACE_EXISTED,
    CREATE_NAMESPACE_FAILED,
    CREATE_CONFIGMAP_FAILED,
    UPDATE_CONFIGMAP_FAILED,
    DELETE_CONFIGMAP_FAILED,
    K8S_CLUSTER_STARTED,
    K8S_CLUSTER_NOT_EXIST,
    CREATE_SECRET_FAILED,
    DELETE_SECRET_FAILED,
    MIGRATE_POD_FAILED,
    SECRET_EXISTED,
    DAEMONSET_EXISTED,
    DAEMONSET_NOT_EXISTED,
    CREATE_DAEMONSET_FAILED,
    UPDATE_DAEMONSET_FAILED,
    DELETE_DAEMONSET_FAILED;
  }
}