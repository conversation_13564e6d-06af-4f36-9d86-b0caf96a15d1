package com.baidu.bce.logic.bci.servicev2.orderresource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadPoolConfigV2 {
    @Value("${cpt1thread.pool.core.size:100}")
    private Integer threadPoolCoreSize;                     // 核心线程数（默认线程数）
    @Value("${cpt1thread.pool.max.size:350}")
    private Integer threadPoolMaxSize;                      // 最大线程数
    @Value("${cpt1thread.keep.alive.time:60}")
    private Integer threadKeepAliveTime;                    // 允许线程空闲时间（单位：默认为秒）
    @Value("${cpt1thread.pool.queue.capacity:50000}")
    private Integer threadPoolQueueCapacity;                // 缓冲队列容量，需要监控

    @Bean(name = "billingResourceTaskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setKeepAliveSeconds(threadKeepAliveTime);
        executor.setQueueCapacity(threadPoolQueueCapacity);

        // 线程池名前缀
        String threadNamePrefix = "ResourceSync-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);

        // 线程池对拒绝任务的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化
        executor.initialize();
        return executor;
    }
}
