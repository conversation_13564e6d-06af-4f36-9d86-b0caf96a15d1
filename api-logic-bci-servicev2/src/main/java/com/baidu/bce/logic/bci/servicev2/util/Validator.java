package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.response.SubnetMapResponse;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.constant.ImageConstant;
import com.baidu.bce.internalsdk.bci.model.QueryVolumesRequest;
import com.baidu.bce.internalsdk.bci.model.QueryVolumesResponse;
import com.baidu.bce.internalsdk.bci.model.VolumeVO;
import com.baidu.bce.internalsdk.bci.model.iam.AttachStatus;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.imageaccelerate.ImageAccelerateDaoV2;
import com.baidu.bce.logic.bci.daov2.imageaccelerate.model.ImageAcceleratePO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.common.service.AclServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.BciAsyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalQuotaServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.BciTagConstant;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PreviewPodCapacityConstant;
import com.baidu.bce.logic.bci.servicev2.constant.VolumeConstant;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.BaseVolume;
import com.baidu.bce.logic.bci.servicev2.model.BciQuota;
import com.baidu.bce.logic.bci.servicev2.model.BciQuotaSpecialPodSpec;
import com.baidu.bce.logic.bci.servicev2.model.BidOption;
import com.baidu.bce.logic.bci.servicev2.model.Bos;
import com.baidu.bce.logic.bci.servicev2.model.CephFSVolume;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.FlexVolume;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.LogCollection;
import com.baidu.bce.logic.bci.servicev2.model.Nfs;
import com.baidu.bce.logic.bci.servicev2.model.Pfs;
import com.baidu.bce.logic.bci.servicev2.model.PodCDSVolumeType;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.PreviewPodCapacityRequest;
import com.baidu.bce.logic.bci.servicev2.model.ValidatedItem;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.model.Volume.PodVolume;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.request.CreateTagsRequest;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.models.V1Container;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.hsqldb.lib.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.CPU_TYPE;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.GPU_TYPE;
import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.KUBE_PROXY_SIDECAR_LABEL_KEY;
import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service("ValidatorV2")
public class Validator {
    private static final Logger LOGGER = LoggerFactory.getLogger(Validator.class);

    @Autowired
    private AclServiceV2 aclService;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private BciAsyncServiceV2 bciAsyncService;

    @Autowired
    private LogicalQuotaServiceV2 logicalQuotaService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private ImageAccelerateDaoV2 imageAccelerateDao;

    @Value("${bci.hostpath.whitelist:/var/log/pods,/noah,/home/<USER>/opt/compiler,/usr/lib64,/etc/localtime,/etc/nsswitch.conf,/var/run/kubelet-proxy}")
    private String hostpathWhitelistConf;

    public static final String CPU = "cpu";
    public static final String MEMORY = "memory";
    public static final String GPU_COUNT = "gpu_count";
    private static final int GENERAL_MAX_CPU = 16;
    private static final int GENERAL_MAX_MEM = 64;
    private static final int VIP_MAX_CPU = 32;
    private static final int VIP_MAX_MEM = 128;
    private static final float FLOATPRECISION = (float) 0.00001;
    private static final int MAX_BATCH_CREATE_LIMIT = 100;


    private static final String QNAME_CHAR_FMT = "[A-Za-z0-9]";
    private static final String QNAME_EXR_CHAR_FMT = "[-A-Za-z0-9.]";
    public static final String PATTERN = "(" + QNAME_CHAR_FMT + QNAME_EXR_CHAR_FMT + "*)?" + QNAME_CHAR_FMT;
    public static final String PATTERN2 = "([a-z0-9][-a-z0-9]*)?[a-z0-9]";
    public static final String PATTERN_TAG_KEY = "^[a-zA-Z0-9\\u4e00-\\u9fa5\\-_ \\/.]{1,65}$";
    public static final String PATTERN_TAG_VALUE = "^[a-zA-Z0-9\\u4e00-\\u9fa5\\-_ \\/.]{0,65}$";

    private static final String BCI_SCHEDULE_IN_PFS_POOL = "bci.virtual-kubelet.io/schedule-in-pfs-pool";

    private static final String SRC_CONFIG_SRC_DIR_KEY = "srcDir";
    private static final String SRC_CONFIG_TTL_KEY = "ttl";
    private static final String DEST_CONFIG_RATE_LIMIT_KEY = "rateLimit";
    private static final String DEST_CONFIG_DEST_TYPE_KEY = "destType";
    private static final String DEST_CONFIG_LOG_STORE_KEY = "logStore";
    private static final List<String> SRC_CONFIG_REQUIRED_KEYS = Arrays.asList("srcDir", "matchedPattern", "ttl");
    private static final List<String> DEST_CONFIG_REQUIRED_KEYS = Arrays.asList("logStore", "destType", "rateLimit");

    /**
     * 默认超时时间60分钟
     */
    private static final long USERID_LOGICAL_ZONE_TO_ZONE_MAP_DETAIL_MAP_DEFAULT_EXPIRATION_TIME = 60 * 1000L * 60;

    /**
     * 用户逻辑区域到zoneMapDetail的缓存
     */
    private CacheUtil<String, ZoneMapDetail> userIdLogicalZoneToZoneMapDetailMap =
            new CacheUtil<>(USERID_LOGICAL_ZONE_TO_ZONE_MAP_DETAIL_MAP_DEFAULT_EXPIRATION_TIME);

    public void validateContainerNames(List<ContainerPurchase> containerPurchases, Set<String> usedNames) {
        // containers之间名字不能相同，不能在已占用的名字中
        Set<String> containerNames = new HashSet<>();
        for (int i = 0; i < containerPurchases.size(); i++) {
            String containerName = containerPurchases.get(i).getName();
            if (StringUtil.isEmpty(containerName) || containerName.length() > 40) {
                throw new PodExceptions.ContainerNameException("The container name [" +
                        containerName + "] is invalid, a valid name length must be between 1 and 40.");
            }
            if (containerNames.contains(containerName)) {
                throw new PodExceptions.ContainerNameException("The container name [" +
                        containerName + "] is duplicated.");
            }
            if (usedNames != null && usedNames.contains(containerName)) {
                throw new PodExceptions.ContainerNameException("The container name [" +
                        containerName + "] is duplicated.");
            }
            containerNames.add(containerName);
        }
        // 需要实现k8s的validate内的功能,暂时先校验以下参数
        String errMsg = " is not a valid name, a valid name must consist of alphanumeric characters," +
                " '-' or '.', and must start and end with an alphanumeric character.";
        String containerNameErrMsg = "The container name can't start with string 'bci_internal_'.";
        // container名字要符合一定规范
        for (int i = 0; i < containerPurchases.size(); i++) {
            if (!stringMatch(Validator.PATTERN, containerPurchases.get(i).getName())) {
                throw new PodExceptions.ContainerNameException("The container name [" +
                containerPurchases.get(i).getName() + "]" + errMsg);
            }
            if (containerPurchases.get(i).getName().startsWith(PodConstants.BCI_INTERNAL_PREFIX)) {
                throw new PodExceptions.ContainerNameException(containerNameErrMsg);
            }
        }
    }

    private void validateVoConflict(BaseVolume volume, Set<String> usedVoNames, boolean ensureMeIsDsVolume,
                                    Set<String> voNamesInDsContainer, Set<String> voNamesInNonDsContainer) {
        String voName = volume.getName();
        if (usedVoNames != null && usedVoNames.contains(voName)) {
            throw new PodExceptions.VolumeNameInvalidException("The volume name " + voName + " has been used.");
        }
        if (ensureMeIsDsVolume) {
            if (!volume.isDsVolume()) {
                throw new PodExceptions.VolumeAttributeInvalidException(voName + " is not a ds volume");
            }
            if (voNamesInDsContainer != null && !voNamesInDsContainer.contains(voName)) {
                throw new PodExceptions.VolumeNoMountsException();
            }
            if (voNamesInNonDsContainer != null && voNamesInNonDsContainer.contains(voName)) {
                throw new PodExceptions.PodContainerMountInvalidException();
            }
        }
    }

    public void validateVolume(Volume volume, Set<String> usedVoNames, boolean ensureMeIsDsVolume,
                               Set<String> voNamesInDsContainer, Set<String> voNamesInNonDsContainer,
                               boolean isBciV3, boolean hasDsContainer) {
        // 该方法一共检查三项
        // 1.volume名字需要合法
        // 2.volume名字不能在已占用volume名字列表中
        // 3.待检查的volume如果必须得是ds volume，则需要确保volume必须在voNamesInDsContainer，不能在voNamesInNonDsContainer中
        String errMsg = " is not a valid name, a valid name must consist of lower case alphanumeric characters " + 
                "or '-', and must start and end with an alphanumeric character.";
        List<Nfs> nfs = volume.getNfs();
        if (nfs != null) {
            for (int i = 0; i < nfs.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, nfs.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The nfs name " + 
                            nfs.get(i).getName() + errMsg);
                }
                validateVoConflict(nfs.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer, 
                                   voNamesInNonDsContainer);
            }
        }
        List<EmptyDir> emptyDir = volume.getEmptyDir();
        if (emptyDir != null) {
            for (int i = 0; i < emptyDir.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, emptyDir.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The emptyDir name " + 
                            emptyDir.get(i).getName() + errMsg);
                }
                validateVoConflict(emptyDir.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer, 
                                   voNamesInNonDsContainer);
            }
        }

        List<ConfigFile> configFile = volume.getConfigFile();
        if (configFile != null) {
            for (int i = 0; i < configFile.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, configFile.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The configFile name " +
                            configFile.get(i).getName() + errMsg);
                }
                validateVoConflict(configFile.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer, 
                                   voNamesInNonDsContainer);
            }
        }

        List<Volume.PodVolume> podVolumes = volume.getPodVolumes();
        if (podVolumes != null) {
            for (int i = 0; i < podVolumes.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, podVolumes.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The podVolume name " +
                            podVolumes.get(i).getName() + errMsg);
                }
                // 注意：
                // 排查代码发现 podVolumes 在 bciv2 的请求预期中并没有被实际用到，而是作为 pod 实际挂载的 volume 的现状信息。
                // 由于 podVolumes 是从底层k8s同步到bci控制面的，而非用户指定的，并且 podVolumes 的类不再是 PodVolumne，所以此处不会解析。
                // TODO: 后续考虑将 podVolume 从 volume 中去除。
            }
        }

        List<FlexVolume> flexVolume = volume.getFlexVolume();
        if (flexVolume != null) {
            for (int i = 0; i < flexVolume.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, flexVolume.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The flexVolume name " +
                            flexVolume.get(i).getName() + errMsg);
                }
                validateVoConflict(flexVolume.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer, 
                                   voNamesInNonDsContainer);
            }
        }

        List<Pfs> pfs = volume.getPfs();
        if (pfs != null) {
            for (int i = 0; i < pfs.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, pfs.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The pfs name " +
                            pfs.get(i).getName() + errMsg);
                }
                validateVoConflict(pfs.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer, 
                                   voNamesInNonDsContainer);
            }
        }

        List<Bos> bos = volume.getBos();
        if (bos != null) {
            for (int i = 0; i < bos.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, bos.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The bos name " +
                            bos.get(i).getName() + errMsg);
                }
                validateVoConflict(bos.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer, 
                                   voNamesInNonDsContainer);
            }
        }

        List<HostPathVolume> hostPathVolumes = volume.getHostPath();
        if (hostPathVolumes != null) {
            for (int i = 0; i < hostPathVolumes.size(); i++) {
                if (!validateHostPath(hostPathVolumes.get(i).getPath(), isBciV3, hasDsContainer)) {
                    throw new PodExceptions.VolumeNameInvalidException("invalid host path:" + 
                        hostPathVolumes.get(i).getPath());
                }
                if (!stringMatch(Validator.PATTERN2, hostPathVolumes.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The hostPathVolumes name " +
                        hostPathVolumes.get(i).getName() + errMsg);
                }
                // 不支持hostPath的Type为Create
                if (StringUtil.isEmpty(hostPathVolumes.get(i).getType())) {
                    hostPathVolumes.get(i).setType(VolumeConstant.HOST_PATH_TYPE_DIRECTORY);
                } else if (hostPathVolumes.get(i).getType().contains("Create")) {
                    if (hostPathVolumes.get(i).getType().equals(
                    VolumeConstant.HOST_PATH_TYPE_DIRECTORY_OR_CREATE)) {
                        hostPathVolumes.get(i).setType(VolumeConstant.HOST_PATH_TYPE_DIRECTORY);
                    } else if (hostPathVolumes.get(i).getType().equals(
                    VolumeConstant.HOST_PATH_TYPE_FILE_OR_CREATE)) {
                        hostPathVolumes.get(i).setType(VolumeConstant.HOST_PATH_TYPE_FILE);
                    } else {
                        throw new PodExceptions.VolumeNameInvalidException("invalid hostpath type:" + 
                            hostPathVolumes.get(i).getType());
                    }
                }
                validateVoConflict(hostPathVolumes.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer, 
                                   voNamesInNonDsContainer);
            }
        }

        List<CephFSVolume> cephFS = volume.getCephfs();
        if (cephFS != null) {
            for (int i = 0; i < cephFS.size(); i++) {
                if (!stringMatch(Validator.PATTERN2, cephFS.get(i).getName())) {
                    throw new PodExceptions.VolumeNameInvalidException("The cephFS name " +
                            cephFS.get(i).getName() + errMsg);
                }
                validateVoConflict(cephFS.get(i), usedVoNames, ensureMeIsDsVolume, voNamesInDsContainer,
                                   voNamesInNonDsContainer);
            }
        }
    }

    public boolean validatePod(PodPurchaseRequest podPurchaseRequest, boolean hasDsContainer) {
        // 批量创建的pod个数最大不能超过100
        if (podPurchaseRequest.getPurchaseNum() > MAX_BATCH_CREATE_LIMIT) {
            throw new PodExceptions.RequestInvalidException("The max purchase count should less than 100.");
        }
        if (podPurchaseRequest.getContainerPurchases() == null
                || podPurchaseRequest.getContainerPurchases().size() < 1) {
            throw new PodExceptions.ContainerException("The container should be specified.");
        }

        String errMsg = " is not a valid name, a valid name must consist of alphanumeric characters," +
                " '-' or '.', and must start and end with an alphanumeric character.";
        // 校验pod name
        if (StringUtils.isEmpty(podPurchaseRequest.getName()) || podPurchaseRequest.getName().length() < 2
                || podPurchaseRequest.getName().length() > 252) {
            throw new PodExceptions.NameInvalidException(
                    "Pod name is invalid, a valid name length must be between 2 and 252.");
        }
        boolean res = stringMatch(Validator.PATTERN, podPurchaseRequest.getName());
        if (!res) {
            throw new PodExceptions.NameInvalidException("The pod name " + podPurchaseRequest.getName() + errMsg);
        }
        // 校验容器名字
        validateContainerNames(podPurchaseRequest.getContainerPurchases(), null);

        // 校验卷名字
        try {
            boolean isBciV3 = isBciV3Pod(podPurchaseRequest.getMetadataLabels());
            validateVolume(podPurchaseRequest.getVolume(), null, false, null, null, isBciV3, hasDsContainer);
        } catch (Exception e) {
            LOGGER.error("validate pod error:{}", e);
            throw e;
        }

        return true;
    }

    public void validateDsContainers(List<ContainerPurchase> dsContainers, Set<String> nonDsContainerNames,
                                     Map<String, String> dsVolumeMap, Map<String, String> nonDsVolumeMap,
                                    boolean isBciV3) {
        if (dsContainers == null || dsContainers.size() == 0) {
            return;
        }
        // 容器名字校验，ds容器之间不能重名，ds容器不能和非ds容器重名
        validateContainerNames(dsContainers, nonDsContainerNames);

        // 容器日志采集、镜像地址、安全上下文校验
        checkBlsTaskParamValidOrNot(dsContainers);
        checkImageAddressIsVaildOrNot(dsContainers);
        podValidator.validateSecurityContextOfContainers(dsContainers, isBciV3);
        // 容器镜像、workingDir、commands、探针、env等参数校验
        podValidator.validateContainerTypeImageWorkingdirCommandsProbeAndEnv(dsContainers, false);
    
        for (ContainerPurchase container : dsContainers) {
            if (container.isDsContainer()) {
                // 校验ds容器不支持的字段
                if (container.getCpu() != 0 || container.getMemory() != 0) {
                    throw new CommonExceptions.RequestInvalidException("cpu/memory in ds container is not supported");
                }
                if (!StringUtils.isEmpty(container.getGpuType()) || container.getGpuCount() != 0) {
                    throw new CommonExceptions.RequestInvalidException("gpuType/Count in ds container is not supported");
                }
                if (container.getLogCollections() != null) {
                    throw new CommonExceptions.RequestInvalidException("logCollections in ds container is not supported");
                }
                if (container.getLivenessProbe() != null 
                    || container.getReadinessProbe() != null 
                    || container.getStartupProbe() != null) {
                    throw new CommonExceptions.RequestInvalidException("probe in ds container is not supported");
                }
                if (container.getLifecycle() != null) {
                    throw new CommonExceptions.RequestInvalidException("lifecycle in ds container is not supported");
                }
                // ds容器使用的卷必须在ds卷列表中，不允许使用非ds卷
                podValidator.validateMountVolumeType(container, dsVolumeMap, nonDsVolumeMap);
            } else {
                throw new CommonExceptions.RequestInvalidException("containerType in ds container must be " + ContainerType.DS_WORKLOAD.getType());
            }
        }
    }

    public void validateDsVolumes(Volume volume, Set<String> nonDsVolumeMap, Set<String> dsMountNames, Set<String> nonDsMountNames,
                                  boolean isBciV3, boolean hasDsContainer) {
        // STEP.1 检查卷自身合法性
        // 所有ds卷不能重名，所有ds卷不能和非ds卷重名。
        // 所有ds卷必须被ds容器使用，不能被非ds容器使用
        validateVolume(volume, nonDsVolumeMap, true, dsMountNames, nonDsMountNames, isBciV3, hasDsContainer);
        
        // STEP.2 在同步ds容器请求中，不允许配置 podVolume、nfs、pfs、flexVolume、bos
        List<PodVolume> podVolume = volume.getPodVolumes();
        if (podVolume != null && podVolume.size() > 0) {
            throw new CommonExceptions.RequestInvalidException("do not using podVolume in ds containers.");
        }
        List<Nfs> nfs = volume.getNfs();
        if (nfs != null && nfs.size() > 0) {
            throw new CommonExceptions.RequestInvalidException("do not using nfs in ds containers.");
        }
        List<Pfs> pfs = volume.getPfs();
        if (pfs != null && pfs.size() > 0) {
            throw new CommonExceptions.RequestInvalidException("do not using pfs in ds containers.");
        }
        List<FlexVolume> flexVolume = volume.getFlexVolume();
        if (flexVolume != null && flexVolume.size() > 0) {
            throw new CommonExceptions.RequestInvalidException("do not using flexVolume in ds containers.");
        }
        List<Bos> bos = volume.getBos();
        if (bos != null && bos.size() > 0) {
            throw new CommonExceptions.RequestInvalidException("do not using bos in ds containers.");
        }
        List<CephFSVolume> cephFS = volume.getCephfs();
        if (cephFS != null && cephFS.size() > 0) {
            throw new CommonExceptions.RequestInvalidException("do not using cephFS in ds containers.");
        }
    }


    public boolean stringMatch(String pattern, String str) {
        if (pattern == null || str == null) {
            return false;
        }
        return Pattern.matches(pattern, str);
    }

    public void testCheckUserId() {
        checkUserId();
    }

    private void checkUserId() {
        String userId = getAccountId();
        if (userId == null || userId.isEmpty()) {
            throw new PodExceptions.RequestInvalidException("userId is null or empty: " + userId);
        }
        CceUserMap userMap = cceClusterService.getCceUserMapByUserId(userId);
        if (userMap == null) {
            throw new PodExceptions.RequestInvalidException("userId is error, not find CceUserMap: " + userId);
        }
        List<CceCluster> cceClusters = cceClusterService.getCceClustersByUserId(userId);
        if (cceClusters == null || cceClusters.size() == 0) {
            throw new PodExceptions.RequestInvalidException("userId is error, not find cceCluster: " + userId);
        }
    }

    // 检查bls任务参数是否合法
    public void checkBlsTaskParamValidOrNot(PodPurchaseRequest podPurchaseRequest) {
        checkBlsTaskParamValidOrNot(podPurchaseRequest.getContainerPurchases());
    }
    
    public void checkBlsTaskParamValidOrNot(List<ContainerPurchase> containerPurchases) {
        List<LogCollection> allLogCollections = new ArrayList<>();
        for (ContainerPurchase containerPurchase : containerPurchases) {
            if (containerPurchase.getLogCollections() != null) {
                allLogCollections.addAll(containerPurchase.getLogCollections());
            }
        }
        List<String> existBlsName = new ArrayList<>();
        for (LogCollection logCollection : allLogCollections) {
            String name = logCollection.getName();
            if (name == null || name.length() < 1 || name.length() > 64 
                || name.contains(" ")) {
                throw new PodExceptions.RequestInvalidException("bls task name invalid,length should " +
                "be in [1,64],space is not allowed");
            }
            if (existBlsName.contains(name)) {
                throw new PodExceptions.RequestInvalidException("repeat bls task name in one pod is not allowed");
            }
            existBlsName.add(name);
            Map<String, Object> srcConfig = logCollection.getSrcConfig();
            Map<String, Object> destConfig = logCollection.getDestConfig();
            if (srcConfig == null || destConfig == null) {
                throw new PodExceptions.RequestInvalidException("srcConfig and destConfig cannot be null");
            }
            for (String requiredKey : SRC_CONFIG_REQUIRED_KEYS) {
                if (!logCollection.getSrcConfig().containsKey(requiredKey)) {
                    String err = "srcConfig " + requiredKey + " cannot be null";
                    throw new PodExceptions.RequestInvalidException(err);
                }
            }
            for (String requiredKey : DEST_CONFIG_REQUIRED_KEYS)  {
                if (!logCollection.getDestConfig().containsKey(requiredKey)) {
                    String err = "destConfig " + requiredKey + " cannot be null";
                    throw new PodExceptions.RequestInvalidException(err);
                }
            }
            String srcDir = (String) logCollection.getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY);
            int ttl = (int) logCollection.getSrcConfig().get(SRC_CONFIG_TTL_KEY);
            int rateLimit = (int) logCollection.getDestConfig().get(DEST_CONFIG_RATE_LIMIT_KEY);
            String destType = (String) logCollection.getDestConfig().get(DEST_CONFIG_DEST_TYPE_KEY);
            String logStore = (String) logCollection.getDestConfig().get(DEST_CONFIG_LOG_STORE_KEY);
            if (srcDir == null) {
                throw new PodExceptions.RequestInvalidException("srcDir param cannot be null");
            }
            if (ttl <= 0) {
                throw new PodExceptions.RequestInvalidException("ttl should be a positive integer");
            }
            if (rateLimit < 1 || rateLimit > 100) {
                throw new PodExceptions.RequestInvalidException("rateLimit should be in [1,100]");
            }
            if (destType == null || !("BLS".equals(destType))) {
                throw new PodExceptions.RequestInvalidException("destType should be BLS");
            }
            if (logStore == null || logStore.length() < 1 || logStore.length() > 128) {
                throw new PodExceptions.RequestInvalidException("logStore length should be in [1,128]");
            }
            String  pattern = "[a-zA-Z0-9-_]{1,128}";
            boolean isMatch = Pattern.matches(pattern, logStore);
            if (!isMatch) {
                throw new PodExceptions.RequestInvalidException("logStore pattern should be like " +
                "[a-zA-Z0-9-_]{1,128}");
            }
        }
    }

    // 检查镜像信息是否符合规范
    public void checkImageAddressIsVaildOrNot(PodPurchaseRequest podPurchaseRequest) {
        checkImageAddressIsVaildOrNot(podPurchaseRequest.getContainerPurchases());
    }

    // 检查镜像信息是否符合规范
    public void checkImageAddressIsVaildOrNot(List<ContainerPurchase> containerPurchases) {
        // 校验镜像name，address，version
        for (ContainerPurchase containerPurchase : containerPurchases) {
            if (containerPurchase.getImageName() == null || containerPurchase.getImageName().isEmpty()) {
                throw new PodExceptions.RequestInvalidException("Container image name is invalid");
            }
            if (containerPurchase.getImageAddress() == null || containerPurchase.getImageAddress().isEmpty()) {
                throw new PodExceptions.RequestInvalidException("Container image address is invalid");
            }
            if (containerPurchase.getImageVersion() == null || containerPurchase.getImageVersion().isEmpty()){
                throw new PodExceptions.RequestInvalidException("Container image version is invalid");
            }
        }
    }   


    // TO DO,智星云临时支持,支持手动管理镜像缓存后,需去除本部分代码
    public Boolean checkImageCachedReadyOrNot(String accountId, PodPurchaseRequest podPurchaseRequest) {
        List<V1Container> containers = new ArrayList<>();
        for (ContainerPurchase containerPurchase : podPurchaseRequest.getContainerPurchases()) {
            // 处理docker镜像
            V1Container container = new V1Container();
            if (containerPurchase.getImageAddress().equalsIgnoreCase(containerPurchase.getImageName())) {
                container.setImage(ImageConstant.DOCKER_IMAGE_IO + containerPurchase.getImageAddress() + ":" +
                        containerPurchase.getImageVersion());
            } else {
                container.setImage(containerPurchase.getImageAddress() + ":" + containerPurchase.getImageVersion());
            }
            containers.add(container);
        }
        // 只要有一个container镜像缓存条目存在且状态为不可用，则POD创建任务失败
        for (V1Container container : containers) {
            List<ImageAcceleratePO> imageAccRecords = 
                imageAccelerateDao.getImageAccByImageAddrAndStatusNotInFailed(accountId, container.getImage());
            // 是否存在镜像缓存
            Boolean hasImageCacheEntry = false;
            // 镜像缓存是否可用
            Boolean hasImageNotCached = true;
            for (ImageAcceleratePO imageAccRecord : imageAccRecords) {
                Map<String, String> images = JsonUtil.toMap(imageAccRecord.getImages(), String.class);
                if (!images.containsKey(container.getImage())) {
                    continue;
                }
                hasImageCacheEntry = true;
                if (!images.get(container.getImage()).equals("")) {
                    hasImageNotCached  = false;
                }
            }
            if (hasImageCacheEntry && hasImageNotCached) {
                LOGGER.debug("account {} has image {} not cached", accountId, container.getImage());
                return false;
            }
        }
        return true;
    }

    private boolean hasDsContainersInPod(ValidatedItem validatedItem) {
        PodPurchaseRequest podRequest = validatedItem.getPodPurchaseRequest();
        List<ContainerPurchase> dsContainers = new ArrayList<>();
        for (ContainerPurchase c : podRequest.getContainerPurchases()) {
            if (ContainerType.DS_WORKLOAD.getType().equals(c.getContainerType())) {
                dsContainers.add(c);
            }
        }
        validatedItem.setHasDsContainer(!dsContainers.isEmpty());
        if (!dsContainers.isEmpty() && !StringUtils.equalsIgnoreCase("always", podRequest.getRestartPolicy())) {
            throw new PodExceptions.RequestInvalidException("RestartPolicy must be always when ds container is specified");
        }
        return validatedItem.getHasDsContainer();
    }

    private void validateDsContainersAndVolumes(ValidatedItem validatedItem, boolean hasDsContainer) {
        PodPurchaseRequest podRequest = validatedItem.getPodPurchaseRequest();
        List<ContainerPurchase> dsContainers = new ArrayList<>();
        Set<String> nonDsContainerNames = new HashSet<>();
        Set<String> dsMountNames = new HashSet<String>();
        Set<String> nonDsMountNames = new HashSet<String>();
        for (ContainerPurchase c : podRequest.getContainerPurchases()) {
            if (ContainerType.DS_WORKLOAD.getType().equals(c.getContainerType())) {
                dsContainers.add(c);
                if (c.getVolumeMounts() != null) {
                    for (VolumeMounts mount : c.getVolumeMounts()) {
                        dsMountNames.add(mount.getName());
                    }
                }
            } else {
                nonDsContainerNames.add(c.getName());
                if (c.getVolumeMounts() != null) {
                    for (VolumeMounts mount : c.getVolumeMounts()) {
                        nonDsMountNames.add(mount.getName());
                    }
                }
            }
        }
        Map<String, String> dsVolumeMap = PodUtils.getVolume2TypeMap(podRequest.getVolume(), true);
        Map<String, String> nonDsVolumeMap = PodUtils.getVolume2TypeMap(podRequest.getVolume(), false);
        Volume dsVolume = PodUtils.filterVolumes(podRequest.getVolume(), true);

        boolean isBciV3 = validatedItem.getIsBciV3();
        validateDsContainers(dsContainers, nonDsContainerNames, dsVolumeMap, nonDsVolumeMap, isBciV3);
        validateDsVolumes(dsVolume, nonDsVolumeMap.keySet(), dsMountNames, nonDsMountNames, isBciV3, hasDsContainer);
    }

    public ValidatedItem validate(BaseCreateOrderRequestVo<IOrderItem> request,
                                  String from,
                                  String accessKey) {

        return this.validate(request, from, UuidUtil.generateUuid(), accessKey);
    }

    public ValidatedItem validate(BaseCreateOrderRequestVo<IOrderItem> request,
                                  String from,
                                  String clientToken,
                                  String accessKey) {
        if (request == null || request.getItems() == null || request.getItems().isEmpty()) {
            throw new PodExceptions.RequestInvalidException("request invalid, request is empty");
        }
        ValidatedItem validatedItem = new ValidatedItem().setAccessKey(accessKey).setFrom(from).setClientToken(clientToken)
                .setWhiteList(aclService.getAclList());
        // check user id
        checkUserId();

        formCreateRequest(request, validatedItem);
        // podValidator.validateImageRegistrySecret(validatedItem.getPodPurchaseRequest());
        podValidator.validateRepository(validatedItem.getPodPurchaseRequest());
        podValidator.validateCpuType(validatedItem.getPodPurchaseRequest().getCpuType());

        boolean isBciV3 = validatedItem.getIsBciV3();
        podValidator.validateSecurityContextOfContainers(
            validatedItem.getPodPurchaseRequest().getContainerPurchases(), isBciV3);
        podValidator.validateSecurityGroup(validatedItem.getPodPurchaseRequest());

        BciQuota bciQuota = logicalQuotaService.getBciQuota();
        podValidator.validateAndSetSubnetUuid(validatedItem.getPodPurchaseRequest(), validatedItem.getZoneMap(),
                validatedItem.getSubnetVoMap(), validatedItem.getVpcVoMap(),
                validatedItem.getEipPurchaseRequest() != null);
        bciAsyncService.validateBciParameters(validatedItem.getPodPurchaseRequest(), bciQuota);
//        bciAsyncService.getZoneResourceDetail();

        asyncExecutorService.getAsyncResult(WorkKeyUtil.genWorkKey("validateBciParameters",
                Arrays.asList(validatedItem.getPodPurchaseRequest(), bciQuota)));

        // 该校验必须放在vpcCidr被设置之后进行，即 validateAndSetSubnetUuid 之后
        podValidator.validatePfsVolume(validatedItem.getPodPurchaseRequest().getVolume().getPfs(), 
                                       validatedItem.getPodPurchaseRequest().getVpcCidr());

        if (validatedItem.getEipPurchaseRequest() != null) {
            if (StringUtils.isEmpty(validatedItem.getEipPurchaseRequest().getEipIp())){
                // 购买时开通自动续费校验
                podValidator.validateAutoRenewTime(validatedItem.getEipPurchaseRequest().getAutoRenewTimeUnit(),
                        validatedItem.getEipPurchaseRequest().getAutoRenewTime());
                podValidator.validateEipBandwidthInMbps(validatedItem.getEipPurchaseRequest());
                podValidator.validateEipBlackList(validatedItem.getWhiteList());
            } else {
                podValidator.validateEipStatus(validatedItem.getEipPurchaseRequest());
            }
        }

        return validatedItem;
    }

    private void formCreateRequest(@NotNull BaseCreateOrderRequestVo<IOrderItem> request, ValidatedItem validatedItem) {
        PodPurchaseRequest podPurchaseRequest = null;
        EipPurchaseRequest eipPurchaseRequest = null;

        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : request.getItems()) {
            switch (item.getConfig().getServiceType().toUpperCase()) {
                case "BCI":
                    podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
                    validatedItem.setPodPurchaseRequest(podPurchaseRequest)
                            .setBciPurchaseOrder(item.getPurchaseOrder())
                            .setBciPaymentModel(item.getPaymentMethod());
                    break;
                case "EIP":
                    eipPurchaseRequest = (EipPurchaseRequest) item.getConfig();
                    validatedItem.setEipPurchaseRequest(eipPurchaseRequest);
                    // .setEipPaymentModel(item.getPaymentMethod())
                    // .setEipPurchaseOrder(item.getPurchaseOrder());
                    break;
                default:
                    throw new PodExceptions.RequestInvalidException(String.format("Not support serviceType:%s",
                            item.getConfig().getServiceType()));

            }
        }
        if (podPurchaseRequest == null) {
            throw new PodExceptions.RequestInvalidException("The bciItem is empty.");
        }
        if (eipPurchaseRequest != null) {
            if (StringUtils.isEmpty(eipPurchaseRequest.getEipIp())){
                if (eipPurchaseRequest.getCount() < 1) {
                    throw new PodExceptions.EipCountInvalidException("The eip count is invalid.");
                } else if (eipPurchaseRequest.getCount() > podPurchaseRequest.getPurchaseNum()) {
                    throw new PodExceptions.
                            EipCountInvalidException("The eip count cannot greater than pod's purchaseNum.");
                }
            }
        }
        if (StringUtils.isNotEmpty(podPurchaseRequest.getEipIp())) {
            eipPurchaseRequest = new EipPurchaseRequest();
            eipPurchaseRequest.setEipIp(podPurchaseRequest.getEipIp());
            validatedItem.setEipPurchaseRequest(eipPurchaseRequest);
        }

        // parse Pod Annotations
        parsePodAnnotations(podPurchaseRequest, validatedItem);

        boolean hasDsContainer = hasDsContainersInPod(validatedItem);
        // 校验pod命名规范;
        validatePod(podPurchaseRequest, hasDsContainer);
        boolean isBciV3 = PodUtils.isOrderV3(podPurchaseRequest.getMetadataLabels());
        validatedItem.setIsBciV3(isBciV3);
        // 检查ds容器和volume,并且设置是否存在ds容器
        validateDsContainersAndVolumes(validatedItem, hasDsContainer);

        if (PodConstants.FROM_CONSOLE.equalsIgnoreCase(validatedItem.getFrom()) ||
            PodConstants.FROM_OPENAPI.equalsIgnoreCase(validatedItem.getFrom())) {
            base64ForConfigFile(podPurchaseRequest.getVolume().getConfigFile());
        }

        // 根据pod中的annotation bci.virtual-kubelet.io/resource-tag 生成pod的tag
        fillPodTagFromAnnotation(validatedItem);
        fillMetadataLabelsFromLabels(podPurchaseRequest);

        if (CollectionUtils.isNotEmpty(podPurchaseRequest.getTags())) {
            CreateTagsRequest tagRequest = new CreateTagsRequest();
            tagRequest.setTags(podPurchaseRequest.getTags());
            checkTags(tagRequest);
        }

        if (!podConfiguration.getRestartPolicy().contains(podPurchaseRequest.getRestartPolicy().toLowerCase())) {
            throw new PodExceptions.RestartPolicyInvalidException(
                    "RestartPolicy is invalid. Only Always/OnFailure/Never are supported.");
        }

        // 获取zone和subnet详细信息
        getZoneSubnets(podPurchaseRequest, validatedItem);

        // 校验抢占字段
        validateBid(podPurchaseRequest);

        // set pod resource(cpu,mem,gputype,gpucount)
        genPodResource(podPurchaseRequest, validatedItem);

        // set scheduleInPfsPool
        getScheduleInPfsPool(validatedItem);

        fillVolume(podPurchaseRequest, validatedItem);
    }

    private void parsePodAnnotations(PodPurchaseRequest podPurchaseRequest, ValidatedItem validatedItem) {
        Map<String, Object> annotationsMap = Util.convertAnnotationToMap(podPurchaseRequest.getAnnotations());
        validatedItem.setPodAnnotationsMap(annotationsMap);
    }

    public void fillPodTagFromAnnotation(ValidatedItem validatedItem) {
        PodPurchaseRequest podPurchaseRequest = validatedItem.getPodPurchaseRequest();
        Map<String, Object> annotationsMap = validatedItem.getPodAnnotationsMap();
        Object value = annotationsMap.get(PodConstants.BCI_POD_RESOURCE_TAG_ANNOTATION_KEY);
        if (value == null) {
            return;
        }
        String podResourceTag = value.toString();
        if (StringUtils.isEmpty(podResourceTag)) {
            return;
        }
        Map<String, String> podResourceTagMap = new HashMap<String, String>();
        // 按照,将podResourceTag拆分成key-value对
        String[] tagArray = podResourceTag.split(",");
        for (String tag : tagArray) {
            String[] tagKeyValue = tag.split(":");
            if (tagKeyValue.length != 2) {
                throw new PodExceptions.PodResourceTagInvalidException(
                        "The pod resource tag is invalid. The format should be key1:val1,key2:val2,key3:val3.");
            }
            podResourceTagMap.put(tagKeyValue[0], tagKeyValue[1]);
            if (podResourceTagMap.size() > PodConstants.BCI_POD_RESOURCE_TAG_MAX_SIZE) {
                throw new PodExceptions.PodResourceTagExceedLimitException(
                        "The pod resource tag is invalid. The number of tags should not exceed ",
                        PodConstants.BCI_POD_RESOURCE_TAG_MAX_SIZE);
            }
        }
        if (podPurchaseRequest.getTags() == null) {
            podPurchaseRequest.setTags(new ArrayList<Tag>());
        }
        for (Map.Entry<String, String> entry : podResourceTagMap.entrySet()) {
            Tag tag = new Tag();
            tag.setTagKey(entry.getKey());
            tag.setTagValue(entry.getValue());
            podPurchaseRequest.getTags().add(tag);
        }
    }

    public void fillMetadataLabelsFromLabels(PodPurchaseRequest podPurchaseRequest) {
        List<Label> labels = podPurchaseRequest.getLabels();
        if (labels == null || labels.isEmpty()) {
            return;
        }

        if (podPurchaseRequest.getMetadataLabels() == null) {
            podPurchaseRequest.setMetadataLabels(new ArrayList<Label>());
        }

        for (Label label : labels) {
            if (label.getLabelKey().equals(PodConstants.BCI_POD_LABELS_POD_NAME_KEY)) {
                Label metadataLabel = new Label();
                metadataLabel.setLabelKey(PodConstants.BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY);
                metadataLabel.setLabelValue(label.getLabelValue());
                podPurchaseRequest.getMetadataLabels().add(metadataLabel);
                continue;
            }

            if (label.getLabelKey().equals(PodConstants.BCI_POD_LABELS_POD_NAMESPACE_KEY)) {
                Label metadataLabel = new Label();
                metadataLabel.setLabelKey(PodConstants.BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAMESPACE_KEY);
                metadataLabel.setLabelValue(label.getLabelValue());
                podPurchaseRequest.getMetadataLabels().add(metadataLabel);
                continue;
            }
        }
    }

    public void testValidateBid(PodPurchaseRequest podPurchaseRequest) {
        validateBid(podPurchaseRequest);
    }

    public void getZoneSubnets(PodPurchaseRequest podPurchaseRequest, ValidatedItem validatedItem) {
        List<String> subnetIds = new ArrayList<>();
        int subnetFeildNum = 0;
        if (StringUtils.isNotEmpty(podPurchaseRequest.getSubnetIds())) {
            subnetIds = Arrays.asList(podPurchaseRequest.getSubnetIds().trim().split(","));
            subnetFeildNum += 1;
            // 简化逻辑, 多可用区逻辑不能指定zone
            if (StringUtils.isNotEmpty(podPurchaseRequest.getLogicalZone())) {
                throw new PodExceptions.LogicalZoneInvalidException(
                        "The logical zone should not be set when multiple subnets are specified.");
            }
        }
        if (StringUtils.isNotEmpty(podPurchaseRequest.getSubnetId())) {
            subnetIds = Arrays.asList(podPurchaseRequest.getSubnetId().trim());
            subnetFeildNum += 1;
        }
        if (StringUtils.isNotEmpty(podPurchaseRequest.getSubnetUuid())) {
            subnetIds = Arrays.asList(podPurchaseRequest.getSubnetUuid().trim());
            subnetFeildNum += 1;
        }
        if (subnetIds.isEmpty()) {
            throw new PodExceptions.
                    SubnetInvalidException("The subnet should be specified.");
        }
        if (subnetFeildNum != 1 ) {
            throw new PodExceptions.
                    SubnetInvalidException("The subnetIds、subnetId should not be specified at same time.");
        }
        if (subnetIds.size() > 10) {
            throw new PodExceptions.
                    SubnetInvalidException("The subnets count exceed max limit 10.");
        }
        Map<String, ZoneMapDetail> zoneMap = new HashMap<>();
        Map<String, SubnetVo> subnetVoMap = new HashMap<>();
        String vpcShortId = "";
        // 批量获取subnets信息
        // 由于批量获取subnet返回为map<subnetUuid, subentVo>, 但是参数传的是shortId, 需要将map转换下。
        SubnetMapResponse subnetMapResponse = podValidator.getSubnets(subnetIds);
        Map<String, SubnetVo> subnetMap = subnetMapResponse.getSubnetMap();
        Map<String, SubnetVo> newSubnetMap = new HashMap<>();
        for (String key : subnetMap.keySet()) {
            newSubnetMap.put(subnetMap.get(key).getShortId(), subnetMap.get(key));
        }
        Map<String, VpcVo> vpcVoResponseMap = subnetMapResponse.getVpcMap();
        Map<String, VpcVo> vpcVoShortIdResponseMap = new HashMap<>();
        for (String key : vpcVoResponseMap.keySet()) {
            vpcVoShortIdResponseMap.put(vpcVoResponseMap.get(key).getShortId(), vpcVoResponseMap.get(key));
        }
        Map<String, VpcVo> vpcVoMap = new HashMap<>();
        for (String subnetId : subnetIds) {
            // 改成批量接口了
            // SubnetVo subnetVo = podValidator.getSubnet(subnetId);
            SubnetVo subnetVo = newSubnetMap.get(subnetId);
            if (subnetVo == null) {
                throw new PodExceptions.SubnetInvalidException(
                            "The subnet [" + subnetId + "] is not found.");
            }
            if ("".equals(vpcShortId)) {
                vpcShortId = subnetVo.getVpcShortId();
            } else if (!vpcShortId.equals(subnetVo.getVpcShortId())) {
                throw new PodExceptions.SubnetInvalidException("The subnets should belong to the same vpc.");
            }
            if (!vpcVoMap.containsKey(vpcShortId)) {
                vpcVoMap.put(vpcShortId, vpcVoShortIdResponseMap.get(vpcShortId));
            }
            if (subnetVoMap.containsKey(subnetVo.getSubnetId())) {
                throw new PodExceptions.SubnetInvalidException("The subnet [" + subnetId + "] is duplicated.");
            }
            subnetVoMap.put(subnetVo.getSubnetId(), subnetVo);
            // 若用户指定LogicalZone
            if (StringUtils.isNotEmpty(podPurchaseRequest.getLogicalZone())
                    && !podPurchaseRequest.getLogicalZone().trim().equalsIgnoreCase(subnetVo.getAz())) {
                throw new PodExceptions.LogicalZoneInvalidException(
                            "The logical zone and subnet should belong to the same zone.");
            }
            if (!zoneMap.containsKey(subnetVo.getAz())) {
                ZoneMapDetail zoneMapDetail = getZone(subnetVo.getAz());
                zoneMap.put(subnetVo.getAz(), zoneMapDetail);
            }
        }
        if (zoneMap.isEmpty()) {
            throw new PodExceptions.SubnetInvalidException("No subnet available.");
        }
        if (subnetVoMap.isEmpty()) {
            throw new PodExceptions.SubnetInvalidException("No subnet available.");
        }
        if (vpcVoMap.isEmpty()) {
            throw new PodExceptions.SubnetInvalidException("No subnet available.");
        }
        validatedItem.setSubnetVoMap(subnetVoMap);
        validatedItem.setZoneMap(zoneMap);
        validatedItem.setVpcVoMap(vpcVoMap);
        LOGGER.debug("getZoneSubnets validatedItem info zoneMap {} subnetVoMap {} vpcVoMap {}", zoneMap, subnetVoMap,
                vpcVoMap);
    }

    // bid validate
    private void validateBid(PodPurchaseRequest podPurchaseRequest) {
        if (podPurchaseRequest.getProductType() == null
                || !podPurchaseRequest.getProductType().equals(podConfiguration.getBidProductType())) {
            return;
        }
        if (podPurchaseRequest.getBidOption() == null 
                || StringUtils.isEmpty(podPurchaseRequest.getBidOption().getBidModel())) {
            throw new PodExceptions.RequestInvalidException(String.format(
                    "productType=bidding, bidOption and bidModel is required"));
        }
        BidOption bidOption = podPurchaseRequest.getBidOption();
        if (bidOption.getBidModel().equals(podConfiguration.getBidModelMarket())) {
            return;
        } else if (bidOption.getBidModel().equals(podConfiguration.getBidModelCustom())) {
            if (bidOption.getBidPrice() == null || bidOption.getBidPrice().compareTo(BigDecimal.ZERO) <= 0){
                throw new PodExceptions.RequestInvalidException(String.format(
                    "bidModel = CUSTOM_BID, bidPrice is required and must >= 0"));
            }
        } else {
            throw new PodExceptions.RequestInvalidException(String.format("Not support bidModel:%s",
                    bidOption.getBidModel()));
        }
    }

    // fillVolume 填充volume相关字段
    private void fillVolume(PodPurchaseRequest podPurchaseRequest, ValidatedItem validatedItem) {
        Volume volume = podPurchaseRequest.getVolume();
        if (volume == null || CollectionUtils.isEmpty(podPurchaseRequest.getContainerPurchases())) {
            throw new PodExceptions.RequestInvalidException();
        }

        if (CollectionUtils.isEmpty(volume.getPodVolumes())) {
            return;
        }

        // rootfs 默认配置
        ContainerPurchase firstContainer = podPurchaseRequest.getContainerPurchases().get(0);
        Volume.PodVolume rootVolume = new Volume.PodVolume();
        rootVolume.setType(PodCDSVolumeType.ROOTFS.getType());
        rootVolume.setName(firstContainer.getName());
        rootVolume.setSizeInGB(podConfiguration.getRootSizeInGB());
        rootVolume.setVolumeSource(new Volume.VolumeSource());

        int rootFSNum = 0;
        Map<String, Volume.VolumeSource> volumeIdMap = new HashMap<>();
        // 后端 emptyDir是一块盘，所以要加起来做校验
        int emptyTotalSize = 0;
        for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
            if (PodCDSVolumeType.ROOTFS.getType().equalsIgnoreCase(podVolume.getType())) {
                rootFSNum++;
            }

            validatePodVolumeSize(podVolume);

            if (PodCDSVolumeType.EMPTYDIR.getType().equalsIgnoreCase(podVolume.getType())) {
                emptyTotalSize += podVolume.getSizeInGB();
            }

            accumulatedPurchaseSize(podPurchaseRequest, podVolume);

            Volume.VolumeSource volumeSource = podVolume.getVolumeSource();
            if (volumeSource != null &&
                    volumeSource.getCds() != null && StringUtils.isNotEmpty(volumeSource.getCds().getUuid())) {
                if (!PodCDSVolumeType.DATA.getType().equalsIgnoreCase(podVolume.getType())) {
                    throw new PodExceptions.InvalidVolumeSource();
                }

                volumeIdMap.put(volumeSource.getCds().getUuid(), volumeSource);
            }
        }

        Volume.PodVolume emptyTotal = new Volume.PodVolume();
        emptyTotal.setType(PodCDSVolumeType.EMPTYDIR.getType());
        emptyTotal.setSizeInGB(emptyTotalSize);
        validatePodVolumeSize(emptyTotal);

        // 校验已有磁盘的可用区跟pod可用区是否一致, 状态是否available
        validateVolumeZone(volumeIdMap, podPurchaseRequest, validatedItem);

        switch (rootFSNum) {
            case 0:
                podPurchaseRequest.getVolume().getPodVolumes().add(rootVolume);
                podPurchaseRequest.setCdsPurchaseSize(podPurchaseRequest.getCdsPurchaseSize()
                        + podConfiguration.getRootSizeInGB());
                break;
            case 1:
                break;
            default:
                throw new PodExceptions.RootFSVolumeExceededLimit();
        }

        // 扣除免费的额度
        float purchaseSize = podPurchaseRequest.getCdsPurchaseSize() - podConfiguration.getFreeChargeSizeInGB();
        if (purchaseSize < 0) {
            purchaseSize = 0;
        }
        podPurchaseRequest.setCdsPurchaseSize(purchaseSize);
    }

    private void base64ForConfigFile(List<ConfigFile> configFiles) {
        if (CollectionUtils.isEmpty(configFiles)) {
            return;
        }

        for (ConfigFile configFile : configFiles) {
            List<ConfigFileDetail> configFileDetails = configFile.getConfigFiles();
            base64Encode(configFileDetails);
        }
    }

    private void base64Encode(List<ConfigFileDetail> configFileDetails) {
        if (CollectionUtils.isEmpty(configFileDetails)) {
            return;
        }
        for (ConfigFileDetail detail : configFileDetails) {
            detail.setFile(Base64.encodeBase64String(detail.getFile().getBytes()));
        }
    }

    private void checkTags(CreateTagsRequest request) {
        List<String> tagKeyList = new ArrayList<>();
        Set<String> tagKeySet = new HashSet<>();
        String message = "Tag key is invalid, a valid key must consist of alphanumeric" +
                " characters, '-', '.', and must start and end with an alphanumeric character.";
        for (Tag tag : request.getTags()) {
            if (tag.getTagKey() != null) {
                if (!stringMatch(Validator.PATTERN_TAG_KEY, tag.getTagKey()) && 
                !tag.getTagKey().equals(BciTagConstant.BCI_TAG_KEY_INTERNAL_ACCOUNT_ID)) {
                    throw new PodExceptions.TagInvalidException(message);
                }
                tagKeyList.add(tag.getTagKey());
                tagKeySet.add(tag.getTagKey());
            }
        }
        if (tagKeyList.size() != tagKeySet.size()) {
            throw new PodExceptions.TagInvalidException("Duplicated tag keys.");
        }

        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
        tagClient.checkTags(request);
    }

    public ZoneMapDetail getZone(String logicalZone) {
        ZoneMapDetail zoneMapDetail = null;
        try {
            String accountId = getAccountId();
            String key = String.format("account_%s_zone_%s", accountId, logicalZone);
            zoneMapDetail = userIdLogicalZoneToZoneMapDetailMap.get(key);
            if (zoneMapDetail != null) {
                return zoneMapDetail;
            } else {
                ZoneClient zoneClient = logicPodClientFactory.createZoneClient(getAccountId());
                zoneMapDetail = zoneClient.createZoneByLogicalZone(logicalZone);
                userIdLogicalZoneToZoneMapDetailMap.put(key,
                        zoneMapDetail);
            }
        } catch (Exception e) {
            LOGGER.info("getZone from logical zone error = {}", e);
            throw new PodExceptions.InvalidateZoneException();
        }

        if (zoneMapDetail == null) {
            throw new PodExceptions.InvalidateZoneException();
        }

        return zoneMapDetail;
    }

    public Map<String, Float> getCpuMemory(float cpu, float memory) {
        Map<String, Float> cpuMemory = new HashMap<>();
        // bci常规pod规格
        List<Float> cpuSpecification = getCpuSpecification(podConfiguration.getCpuMemSpecification());
        List<List<Float>> memSpecification = getMemSpecification(podConfiguration.getCpuMemSpecification());
        
        // 特定用户指定的pod规格
        List<BciQuotaSpecialPodSpec> specialPodSpec = logicalQuotaService.getBciQuotaSpecialPodSpec(getAccountId());
        // 将【特定用户指定的pod规格】插入【bci常规pod规格】
        insertIntoCpuMemSpec(cpuSpecification, memSpecification, specialPodSpec);

        LOGGER.debug("getCpuMemory cpuSpecification: {}", cpuSpecification);
        LOGGER.debug("getCpuMemory memSpecification: {}", memSpecification);

        if (Math.abs(cpu - 0) < FLOATPRECISION && Math.abs(memory - 0) < FLOATPRECISION) { // cpu mem都为0
            cpu = 2F;
            memory = 4F;
        } else if (Math.abs(cpu - 0) < FLOATPRECISION) { // cpu为0 mem不为0
            int cpuindex = cpuSpecification.size();
            for (int i = 0; i < memSpecification.size(); i++) {
                for (int j = 0; j < memSpecification.get(i).size(); j++) {
                    float mem = memSpecification.get(i).get(j);
                    if (mem >= memory) {
                        memory = mem;
                        cpuindex = i;
                        break;
                    }
                }
                if (cpuindex < cpuSpecification.size()) {
                    break;
                }
            }
            if (cpuindex < cpuSpecification.size()) {
                cpu = cpuSpecification.get(cpuindex);
            } else {
                throw new PodExceptions.PodCpuMemSpecInvalid("The pod cpu and memory should in valid specification.");
            }
        } else { // cpu不为0 mem为0; 或者cpu mem都不为0,这两种都属于cpu不为0,逻辑可以统一
            if (cpu > VIP_MAX_CPU) {
                throw new PodExceptions.PodCpuMemSpecInvalid("The pod cpu and memory should in valid specification.");
            }
            cpu = getNearestScale(cpu, cpuSpecification);
            List<Float> memSpec = new ArrayList<>();
            for (int i = 0; i < cpuSpecification.size(); i++) {
                if (Math.abs(cpuSpecification.get(i) - cpu) < FLOATPRECISION) {
                    memSpec = memSpecification.get(i);
                    break;
                }
            }
            memory = getNearestScale(memory, memSpec);
        }

        // 是否超出最大规格cpu,超出不允许创建
        if (cpu > VIP_MAX_CPU || memory > VIP_MAX_MEM) {
            LOGGER.warn("The pod cpu and memory spec is not supported in bci. cpu: {}, memory: {}", cpu, memory);
            throw new PodExceptions.PodCpuMemSpecInvalid("The pod cpu and memory spec is not supported in bci.");
        }

        cpuMemory.put(CPU, cpu);
        cpuMemory.put(MEMORY, memory);

        return cpuMemory;
    }

    public Map<String, Float> getBiddingCpuMemory(float cpu, float memory) {
        Map<String, Float> cpuMemory = new HashMap<>();
        float succeedCpu = 0F;
        float succeedMem = 0F;
        Boolean isFind = false;
        Map<Float, List<Float>> biddingCpuSpecification = podConfiguration.getBiddingCpuSpecification();
        for (Float bccCpu : biddingCpuSpecification.keySet()) {
            if (bccCpu - cpu >= 0F && bccCpu - cpu <= 0.5F) {
                succeedCpu = bccCpu;
                isFind = true;
                break;
            }
        }
        if (!isFind) {
            throw new PodExceptions.PodCpuMemSpecInvalid("无对应竞价实例规格，创建失败");
        }
        isFind = false;

        for (Float bccMem : biddingCpuSpecification.get(succeedCpu)) {
            if (bccMem - memory >= 0F) {
                succeedMem = bccMem;
                isFind = true;
                break;
            }
        }
        if (!isFind) {
            throw new PodExceptions.PodCpuMemSpecInvalid("无对应竞价实例规格，创建失败");
        }
        cpuMemory.put(CPU, succeedCpu);
        cpuMemory.put(MEMORY, succeedMem);

        return cpuMemory;
    }

    private Map<String, Float> getGpuCountCpuMemory(String gpuType, float gpuCount, float userCpu, float userMemory) {
        Map<String, Float> gpuCountCpuMemory = new HashMap<>();
        // 获取gpu套餐
        List<String> gpuSpec = Arrays.asList(podConfiguration.getGpuSpecification().split(","));
        boolean isFind = false;
        float cpu = 0.00F;
        float memory = 0.00F;
        // 对比gputype，如果gputype相同，对比gpucount，直到套餐>=需求，获取该gpu套餐的cpu和mem
        for (String gpuSpecItemStr : gpuSpec) {
            // gpuSpecItemStr=Nvidia A10 PCIE|1|16:64;28:112
            List<String> gpuSpecItemList = Arrays.asList(gpuSpecItemStr.split("\\|"));
            LOGGER.debug("gpu spec: {}, gpuType: {}, gpuCount: {},userCpu {}, userMemory: {}", gpuSpecItemStr, gpuType, gpuCount, userCpu, userMemory);
            float gpuSpecGpuCount = Float.parseFloat(gpuSpecItemList.get(1));
            // 以gpu套餐为准，不会为了cpu和mem升级成更多的卡的套餐
            if (gpuSpecItemList.get(0).equals(gpuType) && gpuSpecGpuCount >= gpuCount) {
                isFind = true;
                LOGGER.debug("find");
                gpuCount = gpuSpecGpuCount;
                List<Float> cpuSpecification = getCpuSpecification(gpuSpecItemList.get(2));
                List<List<Float>> memSpecification = getMemSpecification(gpuSpecItemList.get(2));
                cpu = getNearestScale(userCpu, cpuSpecification);
                List<Float> memSpec = new ArrayList<>();
                for (int i = 0; i < cpuSpecification.size(); i++) {
                    if (Math.abs(cpuSpecification.get(i) - cpu) < FLOATPRECISION) {
                        memSpec = memSpecification.get(i);
                        break;
                    }
                }
                memory = getNearestScale(userMemory, memSpec);
                break;
            }
        }
        LOGGER.debug("gpu spec: {}, isFind: {}, userCpu: {}, userMemory: {}, cpu: {}, memory: {}", gpuSpec, isFind, userCpu, userMemory, cpu, memory);
        if (!isFind || cpu < userCpu || memory < userMemory) {
            throw new PodExceptions.PodCpuMemSpecInvalid("The pod cpu and memory should in valid specification.");
        }

        gpuCountCpuMemory.put(CPU, cpu);
        gpuCountCpuMemory.put(MEMORY, memory);
        gpuCountCpuMemory.put(GPU_COUNT, gpuCount);
        return gpuCountCpuMemory;
    }

    public void testGenPodResource(PodPurchaseRequest podPurchaseRequest, ValidatedItem validatedItem) {
        genPodResource(podPurchaseRequest, validatedItem);
    }

    private void genPodResource(PodPurchaseRequest podPurchaseRequest, ValidatedItem validatedItem) {
        List<ContainerPurchase> containers = podPurchaseRequest.getContainerPurchases();
        if (CollectionUtils.isEmpty(containers)) {
            throw new PodExceptions.RequestInvalidException();
        }

        String bciPodResourceIgnoreContainersString = "";
        List<String> bciPodResourceIgnoreContainersList = new ArrayList<>();
        Object value = validatedItem.getAnnotationsValueByKey(PodConstants.BCI_RESOURCE_IGNORE_CONTAINERS_ANNOTATION_KEY);
        if (value != null) {
            bciPodResourceIgnoreContainersString = value.toString();
            bciPodResourceIgnoreContainersList = Arrays.asList(bciPodResourceIgnoreContainersString.split(","));
        }

        boolean enablePodResourceLimitFlag = enablePodResourceLimit();
        float initContainerCpu = 0.00F;
        float initContainerMemory = 0.00F;
        float workLoadContainerCpu = 0.00F;
        float workLoadContainerMemory = 0.00F;
        String gpuType = "";
        float initContainerGpuCount = 0.00F;
        float workLoadContainerGpuCount = 0.00F;
        String kubeProxyContainerName = getKubeProxyContainerName(podPurchaseRequest.getLabels());
        for (ContainerPurchase container : containers) {
            String containerName = container.getName();
            String containerType = container.getContainerType();
            if (!StringUtils.isEmpty(container.getGpuType())) {
                if (!StringUtils.isEmpty(gpuType) && !gpuType.equals(container.getGpuType())) {
                    throw new PodExceptions.GPUTypeInvalidException("The container's gpuType must be consistent.");
                }
                gpuType = container.getGpuType();
                if (container.getGpuCount() <= 0) {
                    throw new PodExceptions.GPUCountInvalidException(
                            "The container's gpuCount should be greater than 0.");
                }
            }
            if (containerType != null && containerType.length() != 0) {
                // 未在白名单的用户按之前vk策略设置默认1c2g规格
                if (!enablePodResourceLimitFlag) {
                    if (Math.abs(container.getCpu() - 0) < FLOATPRECISION || 
                        Math.abs(container.getMemory() - 0) < FLOATPRECISION) {
                        container.setCpu(1F);
                        container.setMemory(2F);
                    }
                }
                if (containerType.equals(ContainerType.INIT.getType())) {
                    initContainerCpu += container.getCpu();
                    initContainerMemory += container.getMemory();
                    initContainerGpuCount += container.getGpuCount();
                } else if (containerType.equals(ContainerType.WORKLOAD.getType())) {
                    if (enablePodResourceLimitFlag) {
                        if (containerName.equals(kubeProxyContainerName)) {
                            // kubeproxy use resource under pod resource limit
                            continue;
                        }
                    }
                    if (bciPodResourceIgnoreContainersList.contains(containerName)) {
                        continue;
                    }
                    workLoadContainerCpu += container.getCpu();
                    workLoadContainerMemory += container.getMemory();
                    workLoadContainerGpuCount += container.getGpuCount();
                } else {
                    LOGGER.error("container {} type error,container type can only be init or workload",
                            container.getName());
                }
            } else { // 为空或者空串,都算workload类型的container
                if (bciPodResourceIgnoreContainersList.contains(containerName)) {
                    continue;
                }
                workLoadContainerCpu += container.getCpu();
                workLoadContainerMemory += container.getMemory();
                workLoadContainerGpuCount += container.getGpuCount();
            }
        }

        float cpu = Math.max(initContainerCpu, workLoadContainerCpu);
        float memory = Math.max(initContainerMemory, workLoadContainerMemory);
        float gpuCount = Math.max(initContainerGpuCount, workLoadContainerGpuCount);

        if (enablePodResourceLimitFlag) {
            float cpuFromAnnotation = 0.00F;
            float memoryFromAnnotation = 0.00F;
            Object podLimit = validatedItem.getAnnotationsValueByKey(PodConstants.BCI_POD_LIMIT_KEY);
            LOGGER.debug("pod limit annotation: {}", podLimit);
            if (podLimit != null) {
                String podLimitStr = JsonUtil.toJSON(podLimit);
                Pattern pattern = Pattern.compile(PodConstants.BCI_POD_LIMIT_PATTERN);
                Matcher matcher = pattern.matcher(podLimitStr);
                if (StringUtils.isNotEmpty(podLimitStr) && matcher.matches()) {
                    String[] resources = podLimitStr.split("-");
                    if (resources.length == 2) {
                        cpuFromAnnotation = Float.parseFloat(resources[0].replace("\"", ""));
                        Quantity memoryQuanFromAnn = new Quantity(resources[1].replace("\"", ""));
                        memoryFromAnnotation = memoryQuanFromAnn.getNumber().floatValue() / 1024 / 1024 / 1024;
                        LOGGER.info("got resource from annotations, cpu: {}, memory: {}", cpuFromAnnotation, memoryFromAnnotation);
                        // 如果 annotation 有效，覆盖容器设定值
                        if (cpuFromAnnotation != 0 && memoryFromAnnotation != 0) {
                            if (cpuFromAnnotation < cpu || memoryFromAnnotation < memory) {
                                throw new PodExceptions.PodCpuMemSpecInvalid(String.format("annotation %s should be equal or greater than " +
                                    "sum of container resources, cpu %.2f, memory %.2f.", PodConstants.BCI_POD_LIMIT_KEY, cpu, memory));
                            }
                            cpu = cpuFromAnnotation;
                            memory = memoryFromAnnotation;
                        }
                    }
                } else {
                    throw new PodExceptions.PodCpuMemSpecInvalid(String.format("annotation %s is not valid", 
                        PodConstants.BCI_POD_LIMIT_KEY));
                }
            }
            LOGGER.info("set resource after annotations, cpu: {}, memory: {}", cpu, memory);
        }

        float cpuBeforeRoundUp = cpu;
        float memoryBeforeRoundUp = memory;

        // 判断pod资源类型合法性
        if (legalPodResourceType(gpuType, gpuCount)) {
            if (GPU_TYPE.equals(choosePodResourceType(gpuType, gpuCount))) {
                // gen gpu pod spec
                Map<String, Float> gpuCountCpuMemory = getGpuCountCpuMemory(gpuType, gpuCount, cpu, memory);
                podPurchaseRequest.setCpu(gpuCountCpuMemory.get(CPU));
                podPurchaseRequest.setMemory(gpuCountCpuMemory.get(MEMORY));
                podPurchaseRequest.setGpuCount(gpuCountCpuMemory.get(GPU_COUNT));
                podPurchaseRequest.setGpuType(gpuType);
                // 校验是否存在推计费的gpu套餐，理论上不会发生不存在，防呆
                if (null == getGpuBCISpec(gpuType, gpuCountCpuMemory.get(GPU_COUNT).intValue(),
                        gpuCountCpuMemory.get(CPU).intValue(), gpuCountCpuMemory.get(MEMORY).intValue())) {
                    LOGGER.error("gpu charge spec is not found gpuType: {}, gpuCount: {}, cpu: {}, memory: {}", gpuType,
                            gpuCountCpuMemory.get(GPU_COUNT).intValue(), gpuCountCpuMemory.get(CPU).intValue(),
                            gpuCountCpuMemory.get(MEMORY).intValue());
                    throw new PodExceptions.InternalServerErrorException();
                }
            } else if (CPU_TYPE.equals(choosePodResourceType(gpuType, gpuCount))) {
                // gen cpu pod spec
                Map<String, Float> cpuMemory = new HashMap<>();
                if (podPurchaseRequest.getProductType().equals(podConfiguration.getBidProductType())) {
                    cpuMemory = getBiddingCpuMemory(cpu, memory);
                } else {
                    cpuMemory = getCpuMemory(cpu, memory);
                }
                podPurchaseRequest.setCpu(cpuMemory.get(CPU));
                podPurchaseRequest.setMemory(cpuMemory.get(MEMORY));
                // 如果是cpu类型pod需要重置下gpu相关参数，避免产生数据异常
                podPurchaseRequest.setGpuType("");
                podPurchaseRequest.setGpuCount(0);
            }
            validatedItem.setRoundUpFlag(false);
            if (Math.abs(podPurchaseRequest.getCpu() - cpuBeforeRoundUp) > FLOATPRECISION
                    || Math.abs(podPurchaseRequest.getMemory() - memoryBeforeRoundUp) > FLOATPRECISION) {
                // 发生了向上取整
                validatedItem.setRoundUpFlag(true);
                HashMap<String, Float> roundUpResult = new HashMap<>();
                roundUpResult.put(CPU, podPurchaseRequest.getCpu());
                roundUpResult.put(MEMORY, podPurchaseRequest.getMemory());
                validatedItem.setRoundUpResult(roundUpResult);
            }
            LOGGER.info(
                    "adjust pod resource cpu from {} to {}, memory from {} to {}",
                    cpuBeforeRoundUp,
                    podPurchaseRequest.getCpu(),
                    memoryBeforeRoundUp,
                    podPurchaseRequest.getMemory());
            LOGGER.info("adjust flag is {}", validatedItem.getRoundUpFlag());
        } else {
            throw new PodExceptions.RequestInvalidException("pod's resource type is wrongful");
        }
    }

    private void getScheduleInPfsPool(ValidatedItem validatedItem) {
        PodPurchaseRequest podPurchaseRequest = validatedItem.getPodPurchaseRequest();
        Map<String, Object> annotationsMap = validatedItem.getPodAnnotationsMap();
        Object value = annotationsMap.get(BCI_SCHEDULE_IN_PFS_POOL);
        if (value == null) {
            podPurchaseRequest.setScheduleInPfsPool(false);
            return;
        }
        String scheduleInPfsPool = value.toString();
        if (!StringUtils.isEmpty(scheduleInPfsPool) && "true".equalsIgnoreCase(scheduleInPfsPool)) {
            podPurchaseRequest.setScheduleInPfsPool(true);
        } else {
            podPurchaseRequest.setScheduleInPfsPool(false);
        }
    }

    public float getNearestScale(float value, List<Float> candidates) {
        Collections.sort(candidates);
        for (int i = 0; i < candidates.size(); i++) {
            if (candidates.get(i) >= value) {
                return candidates.get(i);
            }
        }
        return candidates.get(candidates.size() - 1);
    }

    public void insertIntoCpuMemSpec(List<Float> cpuSpec, List<List<Float>> memSpec, List<BciQuotaSpecialPodSpec> specialPodSpec) {
        if (CollectionUtils.isEmpty(specialPodSpec)) {
            return;
        }
        for (int i = 0; i < specialPodSpec.size(); i++) {
            Boolean isCpuSame = false;
            int index = -1;
            Float cpu = specialPodSpec.get(i).getCpu();
            Float memory = specialPodSpec.get(i).getMemory();
            for (int j = 0; j < cpuSpec.size(); j++) {
                if (Math.abs(cpu - cpuSpec.get(j)) < FLOATPRECISION) {
                    isCpuSame = true;
                    index = j;
                    break;
                } else if (cpu < cpuSpec.get(j)) {
                    index = j;
                    break;
                }
            }
            if (isCpuSame) {
                // 存在相同的cpu值，无需添加cpu，只需要添加memory
                memSpec.get(index).add(memory);
            } else {
                if (index == -1) {
                    // 插入最后
                    cpuSpec.add(cpu);
                    List<Float> memList = new ArrayList<>();
                    memList.add(memory);
                    memSpec.add(memList);
                } else {
                    // 插入index
                    cpuSpec.add(index, cpu);
                    List<Float> memList = new ArrayList<>();
                    memList.add(memory);
                    memSpec.add(index, memList);
                }
            }

        }
        // memSpec需要去重&排序
        for (int i = 0; i < memSpec.size(); i++) {
            List<Float> values = new ArrayList<>(new HashSet<>(memSpec.get(i)));
            Collections.sort(values);
            memSpec.set(i, values);
        }
        return;
    }

    public List<Float> getCpuSpecification(String specificationStr) {
        List<Float> cpuSpec = new ArrayList<>();
        List<String> specifications = Arrays.asList(specificationStr.split(";"));
        for (int i = 0; i < specifications.size(); i++) {
            List<String> specification = Arrays.asList(specifications.get(i).split(":"));
            Float cpu = Float.valueOf(specification.get(0));
            cpuSpec.add(cpu);
        }
        Collections.sort(cpuSpec);
        return cpuSpec;
    }

    public List<List<Float>> getMemSpecification(String specificationStr) {
        List<List<Float>> memSpec = new ArrayList<>();
        List<String> specifications = Arrays.asList(specificationStr.split(";"));
        for (int i = 0; i < specifications.size(); i++) {
            List<String> specification = Arrays.asList(specifications.get(i).split(":"));
            List<String> memsStr = Arrays.asList(specification.get(1).split(","));
            List<Float> mems = new ArrayList<>();
            for (int j = 0; j < memsStr.size(); j++) {
                mems.add(Float.valueOf(memsStr.get(j)));
            }
            Collections.sort(mems);
            memSpec.add(mems);
        }
        return memSpec;
    }

    private void checkContainerNum(List<ContainerPurchase> containers) {
        if (containers.size() > podConfiguration.getMaxContainerNum()) {
            throw new PodExceptions.ContainerNumExceededLimit(containers.size(),
                    podConfiguration.getMaxContainerNum());
        }
    }

    private void validatePodVolumeSize(Volume.PodVolume podVolume) {
        // 数据盘目前只支持已有盘，不检查
        if (PodCDSVolumeType.DATA.getType().equals(podVolume.getType())) {
            return;
        }
        if (podVolume.getSizeInGB() == null
                || podVolume.getSizeInGB().compareTo(podConfiguration.getCdsVolumeSizeMin()) == -1
                || podVolume.getSizeInGB().compareTo(podConfiguration.getCdsVolumeSizeMax()) == 1) {
            throw new PodExceptions.InvalidVolumeSize();
        }
    }

    private void accumulatedPurchaseSize(PodPurchaseRequest podPurchaseRequest, Volume.PodVolume podVolume) {
        // 数据盘目前只支持已有盘，不累加计费
        if (PodCDSVolumeType.DATA.getType().equals(podVolume.getType())) {
            return;
        }

        podPurchaseRequest.setCdsPurchaseSize(podPurchaseRequest.getCdsPurchaseSize()
                + podVolume.getSizeInGB().floatValue());
    }

    private void validateVolumeZone(Map<String, Volume.VolumeSource> volumeIdMap,
                                    PodPurchaseRequest podPurchaseRequest, ValidatedItem validatedItem) {
        if (CollectionUtils.isEmpty(volumeIdMap.keySet())) {
            return;
        }
        List<String> volumeIds = new ArrayList<>(volumeIdMap.keySet());
        BCCClient bccClient = logicPodClientFactory.createBCCClient(getAccountId());
        QueryVolumesResponse response = bccClient.getVolumes(new QueryVolumesRequest().setDiskIds(volumeIds));
        if (response == null || response.getVolumeVOS() == null) {
            throw new CommonExceptions.InternalServerErrorException();
        }
        if (response.getVolumeVOS().size() != volumeIds.size()) {
            throw new PodExceptions.VolumeNotFound();
        }
        String volumeZone = "";
        for (VolumeVO volumeVO : response.getVolumeVOS()) {
            if (!"postpay".equalsIgnoreCase(volumeVO.getProductType())) {
                throw new PodExceptions.UnsupportedCDSPayment(volumeVO.getProductType());
            }
            if (!AttachStatus.available.toString().equalsIgnoreCase(volumeVO.getStatus())) {
                throw new PodExceptions.VolumeNotAvailable();
            }
            // 多个cds必须同一个zone
            if ("".equals(volumeZone)) {
                volumeZone = volumeVO.getLogicalZone();
            } else if (!volumeZone.equalsIgnoreCase(volumeVO.getLogicalZone())) {
                throw new PodExceptions.VolumeInvalidZone(volumeVO.getLogicalZone(), volumeZone);
            }
            String mapKey = "";

            if (volumeIdMap.containsKey(volumeVO.getVolumeId())) {
                mapKey = volumeVO.getVolumeId();
            }
            if (volumeIdMap.containsKey(volumeVO.getVolumeUuid())) {
                mapKey = volumeVO.getVolumeUuid();
            }

            volumeIdMap.get(mapKey).getCds().setUuid(volumeVO.getVolumeUuid());
        }
        Map<String, ZoneMapDetail> zoneMaps = validatedItem.getZoneMap();
        if (!zoneMaps.containsKey(volumeZone)) {
            throw new PodExceptions.VolumeInvalidZoneWithSubnet(volumeZone);
        } else {
            ZoneMapDetail zoneMapDetail = zoneMaps.get(volumeZone);
            zoneMaps.clear();
            zoneMaps.put(volumeZone, zoneMapDetail);
        }
    }

    // 判断pod资源类型是否合法
    public boolean legalPodResourceType (String gpuType, float gpuCount) {
        if ((StringUtils.isEmpty(gpuType) && gpuCount > 0) || (StringUtils.isNotEmpty(gpuType) && gpuCount <= 0)) {
            return false;
        }
        return true;
    }

    // 判断pod资源类型：cpu类型，gpu类型
    public String choosePodResourceType (String gpuType, float gpuCount) {
        if (StringUtils.isEmpty(gpuType) && gpuCount <= 0) {
            return CPU_TYPE;
        }
        if (StringUtils.isNotEmpty(gpuType) && gpuCount > 0) {
            return GPU_TYPE;
        }
        throw new PodExceptions.RequestInvalidException("pod's resource type is unknown");
    }

    // 根据gpu类型，gpu卡数，cpu与memory确定套餐
    public String getGpuBCISpec(String gpuType, int gpuCount, int cpu, int memory) {
        String gpuBCISpec =
                String.format("%s|%s|%s|%s", gpuType, gpuCount, cpu, memory);
        String bciSpec = podConfiguration.getGpuBCCSpecMap().get(gpuBCISpec);
        if (null == bciSpec || bciSpec.isEmpty()) {
            return null;
        }
        return bciSpec;
    }

    // 检查并设置PreviewPodCapacityRequest参数
    public void checkAndSetPreviewPodCapacityRequest(PreviewPodCapacityRequest request) {
        // check cpu/mem/logicalZone
        if (request.getCpu() == null) {
            throw new PodExceptions.RequestInvalidException("cpu is empty.");
        }
        if (request.getMemory() == null) {
            throw new PodExceptions.RequestInvalidException("memory is empty.");
        }
        if (CollectionUtils.isEmpty(request.getLogicalZones())) {
            throw new PodExceptions.RequestInvalidException("logicalZones is empty.");
        }
        // check cpu type
        podValidator.validateCpuType(request.getCpuType());
        // check type
        podValidator.validatePreviewPodCapacityPodType(request.getTypes());

        // 判断pod资源合法性，并将preview pod规格向上取整
        float cpu = request.getCpu();
        float memory = request.getMemory();
        String gpuType = request.getGpuType();
        float gpuCount = 0F;
        if (request.getGpuCount() != null) {
            gpuCount = request.getGpuCount();
        }
        List<Float> cpuSpecification = getCpuSpecification(podConfiguration.getCpuMemSpecification());
        List<List<Float>> memSpecification = getMemSpecification(podConfiguration.getCpuMemSpecification());
        if (legalPodResourceType(gpuType, gpuCount)) {
            if (GPU_TYPE.equals(choosePodResourceType(gpuType, gpuCount))) {
                // 将用户请求的gpuType转换为后端cce能识别的gpuType
                String gpuK8sResource = podConfiguration.getGpuSpecK8sResourceMap().get(gpuType);
                if (StringUtils.isEmpty(gpuK8sResource)) {
                    throw new PodExceptions.RequestInvalidException("pod's gpu type is error");
                }
                // gen gpu pod spec
                Map<String, Float> gpuCountCpuMemory = getGpuCountCpuMemory(gpuType, gpuCount, cpu, memory);
                request.setCpu(gpuCountCpuMemory.get(CPU));
                request.setMemory(gpuCountCpuMemory.get(MEMORY));
                request.setGpuCount(gpuCountCpuMemory.get(GPU_COUNT));
                request.setGpuType(gpuK8sResource);
            } else if (CPU_TYPE.equals(choosePodResourceType(gpuType, gpuCount))) {
                // gen cpu pod spec
                Map<String, Float> cpuMemory = getCpuMemory(cpu, memory);
                request.setCpu(cpuMemory.get(CPU));
                request.setMemory(cpuMemory.get(MEMORY));
                // 设置gpu count为0，防止为null值
                request.setGpuCount(0F);
            }
        } else {
            throw new PodExceptions.RequestInvalidException("pod's resource type is error");
        }

        // get physicalZone
        Set<String> physicalZones = new HashSet<>();
        for (int i = 0; i < request.getLogicalZones().size(); i++) {
            ZoneMapDetail zoneMapDetail = getZone(request.getLogicalZones().get(i));
            physicalZones.add(zoneMapDetail.getPhysicalZone());
        }
        request.setPhysicalZones(new ArrayList<>(physicalZones));
        // set accountId
        request.setAccountId(getAccountId());
    }
    
    private boolean enablePodResourceLimit() {
        String accountId = getAccountId();
        return commonUtils.checkWhiteList(LogicalConstant.ENABLE_POD_RESOURCE_LIMIT,
            regionConfiguration.getCurrentRegion(), accountId);
    }

    private String getKubeProxyContainerName(List<Label> labels) {
        // 计算用户是否提交kubeProxy sidecar容器
        String kubeProxySidecarName = null;
        if (labels == null) {
            return null;
        }
        for (Label label : labels) {
            if (KUBE_PROXY_SIDECAR_LABEL_KEY.equals(label.getLabelKey())) {
                kubeProxySidecarName = label.getLabelValue();
                break;
            }
        }
        return kubeProxySidecarName;
    }

    // 检查hostpath是否在白名单内
    // 返回值说明
    //  true:HostPath检查通过
    //  false:HostPath检查不通过
    private boolean validateHostPath(String hostPath, boolean isBciV3, boolean hasDsContainer) {
//        // BCI2.0 不支持hostPath路径为 /var/log/pods
//        if (!isBciV3 && !hasDsContainer && hostPath.startsWith("/var/log/pods")) {
//            LOGGER.info("hostPath is not allowed to be /var/log/pods");
//            return false;
//        }
        if (hostpathWhitelistConf.isEmpty()) {
            LOGGER.info("hostpath whitelist is empty");
            return false;
        }
        String[] hostpathWhitelist = hostpathWhitelistConf.split(",");
        for (String path : hostpathWhitelist) {
            LOGGER.info("host path is {}, path is {}", hostPath, path);
            if (hostPath.startsWith(path)) {
                LOGGER.info("hostPath is allowed");
                return true;
            }
        }
        LOGGER.info("hostPath is not allowed");
        return false;
    }

    // 判断是否为BCI3.0 POD
    private boolean isBciV3Pod(List<Label> labels) {
        if (CollectionUtils.isEmpty(labels)) {
            return false;
        }
        for (Label label : labels) {
            if (label.getLabelKey().equals("bci3")) {
                return true;
            }
        }
        return false;
    }
    // form PreviewPodCapacityRequest
    public PreviewPodCapacityRequest formPreviewPodCapacityRequest(ValidatedItem validateItem) {

        PreviewPodCapacityRequest previewRequest = new PreviewPodCapacityRequest();
        previewRequest.setCpu(validateItem.getPodPurchaseRequest().getCpu());
        previewRequest.setMemory(validateItem.getPodPurchaseRequest().getMemory());
        previewRequest.setCpuType(validateItem.getPodPurchaseRequest().getCpuType());
        previewRequest.setGpuType(validateItem.getPodPurchaseRequest().getGpuType());
        previewRequest.setGpuCount(validateItem.getPodPurchaseRequest().getGpuCount());
        previewRequest.setAccountId(getAccountId());
        

        // set Physicalzones 
        List<String> physicalZones = new ArrayList();
        List<String> logicalZones = new ArrayList();
        for (String key : validateItem.getZoneMap().keySet()) {
            physicalZones.add(validateItem.getZoneMap().get(key).getPhysicalZone());
            logicalZones.add(validateItem.getZoneMap().get(key).getLogicalZone());
        }
        previewRequest.setPhysicalZones(physicalZones);
        previewRequest.setLogicalZones(logicalZones);
        // set Types 
        List<String> types = new ArrayList<String>();

        if (validateItem.getPodPurchaseRequest().isTidal()) {
            types.add(PreviewPodCapacityConstant.PREVIEW_POD_CAPACITY_TYPE_TIDAL);
        }
        if (validateItem.getPodPurchaseRequest().isScheduleInPfsPool()){
            types.add(PreviewPodCapacityConstant.PREVIEW_POD_CAPACITY_TYPE_PFS);
        }
        return previewRequest;
    }
}