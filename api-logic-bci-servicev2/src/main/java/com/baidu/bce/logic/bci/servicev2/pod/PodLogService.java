package com.baidu.bce.logic.bci.servicev2.pod;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.internalsdk.bci.model.BLSTaskConfig;
import com.baidu.bce.internalsdk.bci.model.CreateBLSTaskRequest;
import com.baidu.bce.internalsdk.bci.model.CreateBLSTaskResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBLSTaskResponse;
import com.baidu.bce.internalsdk.bci.model.QueryAllBlsTaskResponse;
import com.baidu.bce.internalsdk.bci.model.CreateBlsTokenRequest;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTokenResponse;
import com.baidu.bce.internalsdk.bci.model.CreateBlsTokenResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTokenIDResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsAgentResponse;
import com.baidu.bce.logic.bci.servicev2.model.UserInfoRequest;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.bci.daov2.cceuser.CceUserMapDao;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.common.service.BLSAyncService;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PodVolumeType;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.model.KafkaLogBeat;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.model.LogCollection;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.daov2.bls.model.BlsInfo;
import com.baidu.bce.logic.bci.daov2.bls.model.BlsInfo.TaskOwner;
import com.baidu.bce.logic.bci.daov2.bls.BlsInfoDao;
import com.baidu.bce.logic.bci.daov2.common.model.Label;

import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.core.utils.UUIDUtil;
import com.baidu.bce.plat.webframework.exception.BceException;

import endpoint.EndpointManager;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1ConfigMapVolumeSource;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1KeyToPath;
import io.kubernetes.client.openapi.models.V1Lifecycle;
import io.kubernetes.client.openapi.models.V1LifecycleHandler;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1Probe;
import io.kubernetes.client.openapi.models.V1ExecAction;
import io.kubernetes.client.openapi.models.V1ResourceRequirements;
import io.kubernetes.client.openapi.models.V1VolumeMount;
import io.kubernetes.client.openapi.models.V1EnvVar;
import io.kubernetes.client.openapi.models.V1Volume;
import io.kubernetes.client.openapi.models.V1Secret;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1FlexVolumeSource;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.dao.DataAccessException;

import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Set;
import java.util.HashSet;
import java.util.Collections;
import java.util.Comparator;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;

@Component
public class PodLogService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PodLogService.class);
    private static final String LOG_TYPE_BLS = "bls";
    private static final String LOG_TYPE_KAFKA = "kafka";
    private static final String STDOUT_LOG_SRC = "stdout";
    private static final String STDOUT_VOLUME_NAME = "sidecar-stdout";
    private static final String STDOUT_FLEX_VOLUME_DRIVER = "k8s/sidecar-stdout";
    private static final String STDOUT_VOLUME_MOUNT_PATH = "/stdout/";
    private static final String SIDECAR_NAME = "logbeat";
    private static final String KAFKA_SIDECAR_NAME = "kafka-logbeat";
    private static final String BCI_KAFKA_LOGBEAT_MATCHEDPATTERN = "BCI_KAFKA_LOGBEAT_MATCHEDPATTERN";
    private static final String BCI_KAFKA_LOGBEAT_BROKERS = "BCI_KAFKA_LOGBEAT_BROKERS";
    private static final String BCI_KAFKA_LOGBEAT_TOPIC = "BCI_KAFKA_LOGBEAT_TOPIC";
    private static final String BCI_KAFKA_LOGBEAT_SRCDIR = "BCI_KAFKA_LOGBEAT_SRCDIR";
    private static final String BCI_KAFKA_LOGBEAT_CERTS_SECRET = "BCI_KAFKA_LOGBEAT_CERTS_SECRET";
    private static final String BCI_KAFKA_LOGBEAT_COMPRESSION = "BCI_KAFKA_LOGBEAT_COMPRESSION";
    private static final String BCI_KAFKA_LOGBEAT_MAX_MESSAGE_BYTES = "BCI_KAFKA_LOGBEAT_MAX_MESSAGE_BYTES";
    private static final String SIDECAR_IMAGE = "registry.baidubce.com/bce_bls/logbeat";
    private static final String SIDECAR_MEM_LIMITE = "512Mi";
    private static final String SIDECAR_MEM_REQUEST = "64Mi";
    private static final String SIDECAR_CPU_LIMITE = "1";
    private static final String SIDECAR_CPU_REQUEST = "0.1";
    private static final String SIDECAR_TOKEN_SECRET_KEY = "BLS_USER_TOKEN";
    private static final List<String> SIDECAR_LIVENESS_PROBE_COMMAND = Arrays.asList("sh", "./bin/status.sh");
    private static final List<String> SIDECAR_COMMAND = Arrays.asList("./bin/start.sh");
    private static final List<String> KAFKA_SIDECAR_COMMAND = Arrays.asList("/opt/logbeat/logbeat");
    private static final String SRC_CONFIG_SRC_DIR_KEY = "srcDir";
    private static final String SRC_CONFIG_MATCHPATTERN_KEY = "matchedPattern";
    private static final String SRC_CONFIG_SRC_TYPE_KEY = "srcType";
    private static final List<String> SRC_CONFIG_REQUIRED_KEYS = Arrays.asList("srcDir", "matchedPattern", "ttl");
    private static final List<String> DEST_CONFIG_REQUIRED_KEYS = Arrays.asList("logStore", "destType", "rateLimit");
    private static final String LOG_SECRET_NAME = "log-secret";

    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private BLSAyncService blsAyncService;

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    private CceUserMapDao cceUserMapDao;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private BlsInfoDao blsInfoDao;

    @Value("${bls.sidecar.image.version:latest}")
    private String blsSidecarImageVersion;

    @Value("${bls.sidecar.kafka.image.version:1.4.6.1}")
    private String blsSidecarKafkaImageVersion;

    public void validatePodLogCollections(PodPurchaseRequest podPurchaseRequest) {
        boolean existLogCollection = false;
        for (ContainerPurchase container : podPurchaseRequest.getContainerPurchases()) {
            if (!CollectionUtils.isEmpty(container.getLogCollections())) {
                existLogCollection = true;
            }
            validateContainerLogCollections(container);
        }
    }

    private void validateContainerLogCollections(ContainerPurchase containerPurchase) {
        if (CollectionUtils.isEmpty(containerPurchase.getLogCollections())) {
            return;
        }
        for (LogCollection logCollection : containerPurchase.getLogCollections()) {

            if (CollectionUtils.isEmpty(logCollection.getSrcConfig())
                || CollectionUtils.isEmpty(logCollection.getDestConfig())) {
                throw new PodExceptions.ContianerLogCollectionException("srcConfig and destConfig cannot be empty");
            }

            for (String requiredKey : SRC_CONFIG_REQUIRED_KEYS) {
                if (!logCollection.getSrcConfig().containsKey(requiredKey)) {
                    throw new PodExceptions.ContianerLogCollectionException(requiredKey + " is required in srcConfig");
                }
            }

            for (String requiredKey : DEST_CONFIG_REQUIRED_KEYS) {
                if (!logCollection.getDestConfig().containsKey(requiredKey)) {
                    throw new PodExceptions.ContianerLogCollectionException(requiredKey + " is required in srcConfig");
                }
            }

            String srcDir = (String) logCollection.getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY);
            if (!STDOUT_LOG_SRC.equals(srcDir)) {
                findBestMatchVolumeMounts(srcDir, containerPurchase.getVolumeMounts());
            }
        }
    }

    // 删除bls任务
    public boolean deleteBLSTasks(String blsTasksIdStr, String accountID) throws BceException {
        List<String> needDeleteBlsTaskIds = new ArrayList<>();
        // 查询是否任务下是否还有收集器，如果无收集器或收集器个数为1，则删除。
        try {
            List<String> blsTasksIds = Arrays.asList(StringUtils.split(blsTasksIdStr, ","));
            for (String blsTaskId : blsTasksIds) {
                int size = getBlsAgentNum(blsTaskId, accountID);
                if (size > 1) {
                    continue;
                }
                // 到数据库中查询任务是否由BCI创建
                BlsInfo blsInfo = blsInfoDao.getBlsInfoByTaskId(blsTaskId, accountID);
                if (blsInfo == null) {
                    continue;
                } else {
                    if (!TaskOwner.BCI.toString().equals(blsInfo.getBlsTaskOwner())) {
                        continue;
                    }
                }
                needDeleteBlsTaskIds.add(blsTaskId);
            } 
        } catch (BceException ex) {
            LOGGER.error("get bls agent fail, accountID:{}, exception :{}", accountID, ex);
            return false;
        }
        try {
            for (String blsTaskId : needDeleteBlsTaskIds) {
                deleteBlsMap(blsTaskId, accountID);
                LOGGER.error("delete bls task succeeded, blsTaskId:{} accountID:{}", blsTaskId, accountID);
            }
            return true;
        } catch (BceException ex) {
            LOGGER.error("delete bls task error, accountID:{}, exception :{}", accountID, ex);
            return false;
        }
    }

    public void updateSecret(String acountId, String blsUserToken) throws K8sServiceException, ApiException {
        V1Secret secret = k8sService.getSecret(acountId, LOG_SECRET_NAME);
        if (secret != null && !k8sService.deleteSecret(LOG_SECRET_NAME, acountId)) {
            throw new K8sServiceException(K8sServiceException.ErrorCode.DELETE_SECRET_FAILED, "delete secret failed");
        }
        Map<String, byte[]> secretData = new HashMap<>();
        secretData.put(SIDECAR_TOKEN_SECRET_KEY, blsUserToken.getBytes());
        secret = new V1Secret().metadata(new V1ObjectMeta().namespace(acountId).name(LOG_SECRET_NAME)).data(secretData);
        k8sService.createSecret(secret);
    }

    public List<V1Volume> geneStdoutVolume(String accountId, BciOrderExtra orderExtra, String podContainersStr)
        throws BceException {
        if (!existLogCollection(orderExtra) && !existKafkaLogEnv(orderExtra)) {
            return new ArrayList<>();
        }
        // 获取所有已经存在无需创建的的bls任务(用户或bci创建),这部分任务也需要判断是否有stdout的挂载,来决定是否需要设置flexvolume
        Map<String, QueryBLSTaskResponse> existedBLSTasks = getExistBLSTaskInfoMapFromBls(orderExtra, accountId);
        if (!existStdoutLogCollection(orderExtra, existedBLSTasks) && !existKafkaStdoutLogEnv(orderExtra)) {
            return new ArrayList<>();
        }
        return Arrays.asList(
            (new V1Volume()
                .name(STDOUT_VOLUME_NAME)
                .flexVolume(
                    new V1FlexVolumeSource()
                        .driver(STDOUT_FLEX_VOLUME_DRIVER)
                        .putOptionsItem("pod.needMountedContainers", podContainersStr))));
    }

    private boolean existLogCollection(BciOrderExtra orderExtra) {
        for (ContainerPurchase container : orderExtra.getContainers()) {
            if (!CollectionUtils.isEmpty(container.getLogCollections())) {
                return true;
            }
        }
        return false;
    }

    private boolean existKafkaLogEnv(BciOrderExtra orderExtra) {
        Map<String, String> envMap = new HashMap<>();
        Map<String, String> availableSrcDirMap = new HashMap<>();
        refreshEnvMap(orderExtra, envMap, availableSrcDirMap);
        return availableSrcDirMap.size() > 0;
    }

    private boolean existKafkaStdoutLogEnv(BciOrderExtra orderExtra) {
        if (CollectionUtils.isEmpty(orderExtra.getContainers())) {
            return false;
        }
        Map<String, String> envMap = new HashMap<>();
        Map<String, String> availableSrcDirMap = new HashMap<>();
        refreshEnvMap(orderExtra, envMap, availableSrcDirMap);
        for (Map.Entry<String, String> entry : availableSrcDirMap.entrySet()) {
            if (entry.getValue().equals(STDOUT_LOG_SRC) || entry.getValue().startsWith(STDOUT_VOLUME_MOUNT_PATH)) {
                return true;
            }
        }
        return false;
    }

    private boolean existStdoutLogCollection(
        BciOrderExtra orderExtra, Map<String, QueryBLSTaskResponse> blsTaskResponseMap) {
        if (CollectionUtils.isEmpty(orderExtra.getContainers())) {
            return false;
        }
        for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
            if (CollectionUtils.isEmpty(containerPurchase.getLogCollections())) {
                continue;
            }
            for (LogCollection logCollection : containerPurchase.getLogCollections()) {
                if (CollectionUtils.isEmpty(logCollection.getSrcConfig())) {
                    continue;
                }
                String srcDir = (String) logCollection.getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY);
                if (STDOUT_LOG_SRC.equals(srcDir)) {
                    return true;
                }
            }
        }
        for (QueryBLSTaskResponse response : blsTaskResponseMap.values()) {
            String srcDir = (String) response.getTask().getConfig().getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY);
            if (srcDir.startsWith(STDOUT_VOLUME_MOUNT_PATH)) {
                return true;
            }
        }
        return false;
    }

    public V1Container geneLogSidecarContainer(
        String accountId, String orderUuid, BciOrderExtra orderExtra, String logType, V1PodSpec spec)
        throws K8sServiceException, ApiException, BceException {
        V1Container container = null;
        List<String> blsTasksId = null;
        Set<V1VolumeMount> volumeMounts = new HashSet<>();
        List<V1EnvVar> envVars = new ArrayList<>();
        if (logType.equals(LOG_TYPE_BLS)) {
            LOGGER.debug(
                "In PodLogService, [ accountId {}, podId {} ] enter bls log sidecar logic.",
                accountId,
                orderExtra.getPodId());
            String blsToken = getOrCreateBlsToken(accountId);
            Map<String, QueryBLSTaskResponse> existBLSTasksInfo = getExistBLSTaskInfoMapFromBls(orderExtra, accountId);
            geneSidecarVolumeMounts(orderExtra, existBLSTasksInfo, volumeMounts);
            container =
                new V1Container()
                    .name(PodConstants.BCI_LOG_SIDECAR_CONTAINER_PREFIX + SIDECAR_NAME)
                    .image(SIDECAR_IMAGE + ":" + blsSidecarImageVersion)
                    .imagePullPolicy("Always")
                    .livenessProbe(new V1Probe().exec(new V1ExecAction().command(SIDECAR_LIVENESS_PROBE_COMMAND)))
                    .command(SIDECAR_COMMAND);

            envVars.add(new V1EnvVar().name("BLS_USER_TOKEN").value(blsToken));
            blsTasksId = createBLSTasksBySrcAndDestConfig(orderExtra, orderUuid, accountId);           
        } else if (logType.equals(LOG_TYPE_KAFKA)) {
            LOGGER.debug(
                "In PodLogService, [ accountId {}, podId {} ] enter kafka log sidecar logic.",
                accountId,
                orderExtra.getPodId());
            container =
                new V1Container()
                    .name(PodConstants.BCI_LOG_SIDECAR_CONTAINER_PREFIX + KAFKA_SIDECAR_NAME)
                    .image(SIDECAR_IMAGE + ":" + blsSidecarKafkaImageVersion)
                    .imagePullPolicy("Always")
                    .livenessProbe(new V1Probe().exec(new V1ExecAction().command(SIDECAR_LIVENESS_PROBE_COMMAND)))
                    .command(KAFKA_SIDECAR_COMMAND);
            Map<String, KafkaLogBeat.Task> taskMap = createKafkaTasksByEnvs(orderExtra, spec, volumeMounts);

            blsTasksId = new ArrayList<>(taskMap.keySet());
            KafkaLogBeat kafkaLogBeat = new KafkaLogBeat();
            kafkaLogBeat.getTasks().addAll(new ArrayList<>(taskMap.values()));
            String kafkaLogBeatStr = JsonUtil.toJSON(kafkaLogBeat);
            String kafkaLogBeatYamlStr = JsonUtil.jsonStrToYaml(kafkaLogBeatStr);
            LOGGER.debug(
                "In PodLogService, [ accountId {}, podId {} ], kafkaLogBeatYamlStr {}.",
                accountId,
                orderExtra.getPodId(),
                kafkaLogBeatYamlStr);

            // TODO 封装configmap，生成volumeMount, volume代码整合
            // 创建并挂载kafka logbeat logbeat.yml的configmap
            String configMapName = PodUtils.buildConfigMapName(orderExtra.getPodId(), KAFKA_SIDECAR_NAME);
            ConfigFile configFile = new ConfigFile();
            configFile.setConfigFiles(
                new ArrayList<ConfigFileDetail>(
                    Collections.singletonList(
                        new ConfigFileDetail(
                            "logbeat.yml", Base64.encodeBase64String(kafkaLogBeatYamlStr.getBytes())))));
            V1ConfigMap k8sConfigMap = PodUtils.buildV1ConfigMap(accountId, configMapName, configFile);
            k8sService.createConfigMap(k8sConfigMap);
            // 添加 volumeMount
            String volumeName = KAFKA_SIDECAR_NAME + "-" + UUIDUtil.generateShortUuid().toLowerCase();
            volumeMounts.add(
                new V1VolumeMount()
                    .name(volumeName)
                    .mountPath("/opt/logbeat/logbeat.yml")
                    .readOnly(Boolean.FALSE)
                    .subPath("logbeat.yml"));
            // 添加 volume
            V1Volume k8sVolume = new V1Volume();
            k8sVolume.setName(volumeName);
            V1ConfigMapVolumeSource configMapSource = new V1ConfigMapVolumeSource();
            configMapSource.setItems(new ArrayList<V1KeyToPath>());
            configMapSource.setName(configMapName);
            k8sVolume.setConfigMap(configMapSource);
            spec.getVolumes().add(k8sVolume);
        } else {
            return null;
        }
        // lifecycle postStart ，检测是否启动成功
        V1Lifecycle lifecycle = new V1Lifecycle();
        V1LifecycleHandler postStart = new V1LifecycleHandler();
        V1ExecAction exec = new V1ExecAction();
        List<String> postStartCommandList = new ArrayList<>();
        postStartCommandList.add("/bin/sh");
        postStartCommandList.add("-c");
        postStartCommandList.add(
            "nohup sh "
                + PodNewOrderExecutorServiceV2.SIDECAR_COMMAND_MOUNT_PATH
                + PodNewOrderExecutorServiceV2.BLS_CHECK_SHELL_NAME
                + " &");
        exec.setCommand(postStartCommandList);
        postStart.setExec(exec);
        lifecycle.setPostStart(postStart);
        container.setLifecycle(lifecycle);

        V1ResourceRequirements resourceRequirements = new V1ResourceRequirements();
        resourceRequirements.putLimitsItem("memory", new Quantity(SIDECAR_MEM_LIMITE));
        resourceRequirements.putLimitsItem("cpu", new Quantity(SIDECAR_CPU_LIMITE));
        resourceRequirements.putRequestsItem("memory", new Quantity(SIDECAR_MEM_REQUEST));
        resourceRequirements.putRequestsItem("cpu", new Quantity(SIDECAR_CPU_REQUEST));
        container.setResources(resourceRequirements);

        if (container.getVolumeMounts() == null) {
            container.volumeMounts(new ArrayList<V1VolumeMount>());
        }
        container.getVolumeMounts().addAll(volumeMounts);

        envVars.add(new V1EnvVar().name("BLS_CONTAINER_TAG").value("sidecar"));
        envVars.add(new V1EnvVar().name("BLS_SERVER_ENDPOINT").value(EndpointManager.getEndpoint("BLS_MASTER")));
        envVars.add(new V1EnvVar().name("BLS_TASKS").value(StringUtils.join(blsTasksId, ",")));
        // 支持自定义设置bls日志采集容器的hostname
        String podName = "";
        if (orderExtra.getLabels() != null) {
            for (Label label : orderExtra.getLabels()) {
                if (label.getLabelKey().equals("PodName")) {
                    podName = label.getLabelValue();
                    break;
                }
            }
        }
        if (!"".equals(podName)) {
            envVars.add(new V1EnvVar().name("BLS_CUSTOM_HOSTNAME").value(podName));
        }
        container.setEnv(envVars);
        return container;
    }

    public List<V1Container> geneLogSidecarContainers(
        String accountId, String orderUuid, BciOrderExtra orderExtra, V1PodSpec spec)
        throws BceInternalResponseException, K8sServiceException, ApiException, BceException {
        List<V1Container> containers = new ArrayList<>();
        if (!existLogCollection(orderExtra) && !existKafkaLogEnv(orderExtra)) {
            return containers;
        }
        if (existLogCollection(orderExtra)) {
            V1Container container = geneLogSidecarContainer(accountId, orderUuid, orderExtra, LOG_TYPE_BLS, spec);
            if (container != null) {
                containers.add(container);
            }
        }
        if (existKafkaLogEnv(orderExtra)) {
            V1Container container = geneLogSidecarContainer(accountId, orderUuid, orderExtra, LOG_TYPE_KAFKA, spec);
            if (container != null) {
                containers.add(container);
            }
        }
        return containers;
    }

    private void refreshEnvMap(
        BciOrderExtra orderExtra, Map<String, String> envMap, Map<String, String> availableSrcDirMap) {
        // 封装成并更新Available Kafka Env
        for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
            List<Environment> containerEnvs = containerPurchase.getEnvs();
            if (CollectionUtils.isEmpty(containerEnvs)) {
                continue;
            }
            Map<String, String> srcDirMap = new HashMap<>();
            for (Environment env : containerEnvs) {
                if (env.getKey().startsWith(BCI_KAFKA_LOGBEAT_SRCDIR)) {
                    srcDirMap.put(env.getKey(), env.getValue());
                }
                envMap.put(env.getKey(), env.getValue());
            }
            for (Map.Entry<String, String> entry : srcDirMap.entrySet()) {
                String[] configSplitRet = entry.getKey().split(BCI_KAFKA_LOGBEAT_SRCDIR + "_");
                String configOffset = configSplitRet[configSplitRet.length - 1];
                if (!configOffset.equals(entry.getKey())
                    && envMap.containsKey(BCI_KAFKA_LOGBEAT_BROKERS + "_" + configOffset)
                    && envMap.containsKey(BCI_KAFKA_LOGBEAT_TOPIC + "_" + configOffset)
                    && envMap.containsKey(BCI_KAFKA_LOGBEAT_SRCDIR + "_" + configOffset)) {
                    if (!envMap.get(BCI_KAFKA_LOGBEAT_BROKERS + "_" + configOffset).equals("")
                        && !envMap.get(BCI_KAFKA_LOGBEAT_TOPIC + "_" + configOffset).equals("")) {
                        availableSrcDirMap.put(entry.getKey(), entry.getValue());
                    }
                }
            }
        }
    }

    private Map<String, KafkaLogBeat.Task> createKafkaTasksByEnvs(
        BciOrderExtra orderExtra, V1PodSpec spec, Set<V1VolumeMount> mountSet) {
        Map<String, KafkaLogBeat.Task> taskMap = new HashMap<>();
        for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
            List<Environment> containerEnvs = containerPurchase.getEnvs();
            if (CollectionUtils.isEmpty(containerEnvs)) {
                continue;
            }
            Map<String, String> envMap = new HashMap<>();
            Map<String, String> availableSrcDirMap = new HashMap<>();
            refreshEnvMap(orderExtra, envMap, availableSrcDirMap);

            for (Map.Entry<String, String> entry : availableSrcDirMap.entrySet()) {
                String[] configSplitRet = entry.getKey().split(BCI_KAFKA_LOGBEAT_SRCDIR + "_");
                String configOffset = configSplitRet[configSplitRet.length - 1];
                // TODO 生成blstaskid， task信息
                String hosts = envMap.get(BCI_KAFKA_LOGBEAT_BROKERS + "_" + configOffset);
                String topic = envMap.get(BCI_KAFKA_LOGBEAT_TOPIC + "_" + configOffset);
                String srcDir = envMap.get(BCI_KAFKA_LOGBEAT_SRCDIR + "_" + configOffset);
                String matchedPattern = envMap.get(BCI_KAFKA_LOGBEAT_MATCHEDPATTERN + "_" + configOffset);
                String certs = envMap.get(BCI_KAFKA_LOGBEAT_CERTS_SECRET + "_" + configOffset);
                String compression =
                    envMap.get(BCI_KAFKA_LOGBEAT_COMPRESSION + "_" + configOffset) == null
                        ? "none"
                        : envMap.get(BCI_KAFKA_LOGBEAT_COMPRESSION + "_" + configOffset);
                if (!"none".equals(compression)
                    && !"Gzip".equals(compression)
                    && !"Snappy".equals(compression)
                    && !"Lz4".equals(compression)) {
                    LOGGER.info(
                        "In createKafkaTasksByEnvs, [podId {}, containerName {}], compression name error, "
                            + "transfer from {} to none",
                        orderExtra.getPodId(),
                        containerPurchase.getName(),
                        compression);
                    compression = "none";
                }
                String maxMessageBytes = envMap.get(BCI_KAFKA_LOGBEAT_MAX_MESSAGE_BYTES + "_" + configOffset);

                if (matchedPattern == null) {
                    matchedPattern = "^.*$";
                }
                LOGGER.debug(
                    "In createKafkaTasksByEnvs, [podId {}, containerName {}], generate new kafka config "
                        + "[hosts {}, topic {}, srcDir {}, matchedPattern {}, certs {}, compression {}, "
                        + "maxMessageBytes{} ].",
                    orderExtra.getPodId(),
                    containerPurchase.getName(),
                    hosts,
                    topic,
                    srcDir,
                    matchedPattern,
                    certs,
                    compression,
                    maxMessageBytes);
                KafkaLogBeat.Task task = new KafkaLogBeat.Task();
                task.getInput().setPaths(new ArrayList<>(Collections.singletonList(srcDir + "/" + matchedPattern)));
                KafkaLogBeat.Output output = new KafkaLogBeat.Output();
                output
                    .setHosts(new ArrayList<>(Arrays.asList(hosts.split(","))))
                    .setTopic(topic)
                    .setCompression(compression);
                if (maxMessageBytes != null) {
                    try {
                        int maxMessageBytesInt = Integer.parseInt(maxMessageBytes);
                        output.setMax_message_bytes(maxMessageBytesInt);
                    } catch (Exception e) {
                        LOGGER.info(
                            "In createKafkaTasksByEnvs, [podId {}, containerName {}], parse maxMessageBytes"
                                + " {} to int error: {}",
                            orderExtra.getPodId(),
                            containerPurchase.getName(),
                            maxMessageBytes,
                            e.toString());
                    }
                }
                task.getOutput().put("kafka", output);
                String blsTaskId = UUID.randomUUID().toString();
                task.setTask_id(blsTaskId);
                // 添加采集的日志路径对应的 volumeMount
                if (STDOUT_LOG_SRC.equals(srcDir) || srcDir.startsWith(STDOUT_VOLUME_MOUNT_PATH)) {
                    mountSet.add(
                        new V1VolumeMount()
                            .name(STDOUT_VOLUME_NAME)
                            .mountPath(STDOUT_VOLUME_MOUNT_PATH)
                            .readOnly(Boolean.TRUE));
                } else {
                    VolumeMounts volumeMounts = findBestMatchVolumeMounts(srcDir, containerPurchase.getVolumeMounts());
                    mountSet.add(
                        new V1VolumeMount()
                            .name(volumeMounts.getName())
                            .mountPath(volumeMounts.getMountPath())
                            .readOnly(Boolean.TRUE));
                }
                // 添加kafka证书对应的 volumeMount, 不需要额外写pod spec中的volume
                if (certs != null && orderExtra.getVolume() != null) {
                    List<ConfigFile> configMapList = orderExtra.getVolume().getConfigFile();
                    if (!CollectionUtils.isEmpty(configMapList)) {
                        for (ConfigFile config : configMapList) {
                            if (config.getName().equals(certs)) {
                                V1VolumeMount certsVolumeMount =
                                    new V1VolumeMount()
                                        .name(config.getName())
                                        .mountPath("/opt/logbeat/config/" + blsTaskId + "/certs")
                                        .readOnly(Boolean.TRUE);
                                mountSet.add(certsVolumeMount);
                                break;
                            }
                        }
                    }
                }
                taskMap.put(blsTaskId, task);
            }
        }
        LOGGER.debug(
            "In createKafkaTasksByEnvs end, podId {}, taskMap {}, volumeMounts {} .",
            orderExtra.getPodId(),
            taskMap,
            mountSet);
        return taskMap;
    }

    // 获取所有业务指定的bls任务
    private Map<String, List<LogCollection>> getBlsLogCollections(BciOrderExtra orderExtra) {
        Map<String, List<LogCollection>> logCollections = new HashMap<>();
        for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
            if (CollectionUtils.isEmpty(containerPurchase.getLogCollections())) {
                continue;
            }
            logCollections.put(containerPurchase.getName(), containerPurchase.getLogCollections());
        }
        return logCollections;
    }

    // 创建一个bls任务并更新到数据库
    @Transactional(rollbackFor = BceException.class)
    private BlsInfo createBlsTaskMap(
        String containerName, String blsTaskName, LogCollection logCollection, String accountId) {
        BlsInfo blsTaskMap = new BlsInfo();
        try {
            // 以(accountId,blsTaskName,deleted,deletedTime)为唯一索引
            blsTaskMap.setUserId(accountId);
            blsTaskMap.setBlsTaskOwner(TaskOwner.BCI.toString());
            blsTaskMap.setBlsTaskName(blsTaskName);
            try {
                blsInfoDao.insert(blsTaskMap);
            } catch (DataAccessException ex) {
                LOGGER.debug(
                    "insert accountId {} blsTaskName {} failed,try to get exist,{}", accountId, blsTaskName, ex);
                BlsInfo existBlsInfoMap = blsInfoDao.select(blsTaskName, accountId);
                if (existBlsInfoMap != null && !StringUtils.isEmpty(existBlsInfoMap.getBlsTaskId())) {
                    return existBlsInfoMap;
                } else if (existBlsInfoMap != null && StringUtils.isEmpty(existBlsInfoMap.getBlsTaskId())) {
                    // 轮询15s，轮询时间应与bls创建任务接口超时时间一致
                    for (int i = 0; i < 3; i++) {
                        existBlsInfoMap = blsInfoDao.select(blsTaskName, accountId);
                        if (existBlsInfoMap != null && !StringUtils.isEmpty(existBlsInfoMap.getBlsTaskId())) {
                            return existBlsInfoMap;
                        } else if (existBlsInfoMap != null && StringUtils.isEmpty(existBlsInfoMap.getBlsTaskId())) {
                            Thread.sleep(5000);
                        } else {
                            BceException e =
                                new BceException(
                                    "other is createing {},try to get exist, get null bls task map", blsTaskName);
                            throw e;
                        }
                    }
                } else {
                    LOGGER.error(
                        "insert accountId {} blsTaskName {} failed,other is createing, " + 
                            "try to select get null blsTaskid,ex is {}",
                        accountId,
                        blsTaskName,
                        ex);
                    BceException e = new BceException("other is createing,try to select get null blsTaskid");
                    throw e;
                }
            }
            String blsTaskId = null;
            try {
                blsTaskId = createOneBlsTask(containerName, blsTaskName, logCollection, accountId);
            } catch (BceException ex) {
                LOGGER.error("create bls task {} fail,{}", blsTaskName, ex);
                throw ex;
            }
            if (blsTaskId != null && !StringUtils.isEmpty(blsTaskId)) {
                blsTaskMap.setBlsTaskId(blsTaskId);
                blsInfoDao.update(blsTaskMap);
            } else {
                BceException e = new BceException("create bls task failed,bls task id is null");
                throw e;
            }
        } catch (InterruptedException exception) {
            LOGGER.error("create bls task interruptedException ocurred,need check {}", exception);
            BceException exp = new BceException("create bls task interruptedException ocurred,need check");
            throw exp;
        }
        return blsTaskMap;
    }

    // 调用bls接口,创建一个bls任务
    private String createOneBlsTask(
        String containerName, String blsTaskName, LogCollection logCollection, String accountId) {
        BceException e = null;
        String blsTaskId = null;
        CreateBLSTaskRequest request = new CreateBLSTaskRequest();
        request.setName(blsTaskName);
        if (!CollectionUtils.isEmpty(logCollection.getTags())) {
            request.getTags().addAll(logCollection.getTags());
        }
        BLSTaskConfig config = new BLSTaskConfig();
        if (logCollection.getDestConfig() != null) {
            config.setDestConfig(new HashMap<String, Object>(logCollection.getDestConfig()));
        }
        config.setSrcConfig(new HashMap<String, Object>(logCollection.getSrcConfig()));
        if (STDOUT_LOG_SRC.equals(config.getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY))) {
            config.getSrcConfig().put(SRC_CONFIG_SRC_DIR_KEY, STDOUT_VOLUME_MOUNT_PATH + containerName);
            config
                .getSrcConfig()
                .put(SRC_CONFIG_MATCHPATTERN_KEY, logCollection.getSrcConfig().get(SRC_CONFIG_MATCHPATTERN_KEY));
        }
        // bls agent在sidecar模式下，传输任务应是源端类型为主机型任务，而不是容器型任务。
        config.getSrcConfig().put(SRC_CONFIG_SRC_TYPE_KEY, "host");
        request.setConfig(config);
        // 创建bls日志采集任务
        CreateBLSTaskResponse response = new CreateBLSTaskResponse();
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.createBLSTask(request, accountId);
            response =
                (CreateBLSTaskResponse)
                    asyncExecutorService.getAsyncResult(
                        WorkKeyUtil.genWorkKey("createBLSTask", Arrays.asList((Object) request, accountId)));
        } catch (RuntimeException ex) {
            LOGGER.error("sync createBLSTask work exception! exception is :{}", ex);
            e = new BceException("failed to sysc createBLSTask work", 400);
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
        if (e != null) {
            if (response != null && StringUtils.isEmpty(response.getTaskId())) {
                try {
                    deleteOneBlsTask(blsTaskId, accountId);
                } catch (BceException ex) {
                    LOGGER.error("create bls task {} error,try to delete fail,exception is :{}", blsTaskName, ex);
                    e = new BceException("create bls task fail,try to rollback fail", 400);
                    throw e;
                }
            }
            throw e;
        }
        if (response == null || StringUtils.isEmpty(response.getTaskId())) {
            e = new BceException("create bls task fail,unknown problem", 400);
            throw e;
        }
        blsTaskId = response.getTaskId();
        return blsTaskId;
    }

    // 调用bls接口查询任务下的收集器数目
    private int getBlsAgentNum(String blsTaskId, String accountID) {
        BceException e = null;
        QueryBlsAgentResponse response = null;
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.queryBlsAgent(blsTaskId, accountID);
            response =
                (QueryBlsAgentResponse)
                    asyncExecutorService.getAsyncResult(
                        WorkKeyUtil.genWorkKey("queryBlsAgent", Arrays.asList((Object) blsTaskId, accountID)));
        } catch (RuntimeException ex) {
            LOGGER.error("sync queryBlsAgent work error! exception: {}", ex);
            e = new BceException("sync queryBlsAgent work error", 400);
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
        if (e != null) {
            throw e;
        }
        if (response == null) {
            e = new BceException("queryBlsAgent resp is null", 400);
            throw e;
        }
        int size = response.getTotalSize();
        return size;
    }

    // 调用bls接口删除一个bls任务
    private void deleteOneBlsTask(String blsTaskId, String accountID) {
        BceException e = null;
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.deleteBLSTask(blsTaskId, accountID);
            asyncExecutorService.getAsyncResult(
                WorkKeyUtil.genWorkKey("deleteBLSTask", Arrays.asList((Object) blsTaskId, accountID)));
        } catch (RuntimeException ex) {
            LOGGER.error("sync deleteBLSTask work error! exception: {}", ex);
            e = new BceException("deleteOneBlsTask fail", 400);
            throw e;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
    }
    // 删除bls任务，更新数据库
    @Transactional(rollbackFor = BceException.class)
    private void deleteBlsMap(String blsTaskId, String accountID) {
        BceException e = null;
        BlsInfo blsTaskMap = new BlsInfo();
        blsTaskMap.setBlsTaskId(blsTaskId);
        blsTaskMap.setUserId(accountID);
        blsInfoDao.delete(blsTaskMap);
        try {
            deleteOneBlsTask(blsTaskId, accountID);
        } catch (BceException ex) {
            LOGGER.error("delete bls task {} fail,exception :{}", blsTaskId, ex);
            e = new BceException("delete bls task fail", 400);
            throw e;
        }
    }

    private void geneSidecarVolumeMounts(
        BciOrderExtra orderExtra,
        Map<String, QueryBLSTaskResponse> existBLSTaskInfoMap,
        Set<V1VolumeMount> mountSet) {
        for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
            if (CollectionUtils.isEmpty(containerPurchase.getLogCollections())) {
                continue;
            }
            for (LogCollection logCollection : containerPurchase.getLogCollections()) {
                String blsTaskName = logCollection.getName();
                String srcDir = null;
                if (StringUtils.isNotEmpty(blsTaskName) && !existBLSTaskInfoMap.isEmpty()) {
                    QueryBLSTaskResponse response = existBLSTaskInfoMap.get(blsTaskName);
                    if (response != null) {
                        srcDir = (String) response.getTask().getConfig().getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY);
                    } else {
                        srcDir = (String) logCollection.getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY);
                    }

                } else {
                    srcDir = (String) logCollection.getSrcConfig().get(SRC_CONFIG_SRC_DIR_KEY);
                }
                if (STDOUT_LOG_SRC.equals(srcDir) || srcDir.startsWith(STDOUT_VOLUME_MOUNT_PATH)) {
                    mountSet.add(
                        new V1VolumeMount()
                            .name(STDOUT_VOLUME_NAME)
                            .mountPath(STDOUT_VOLUME_MOUNT_PATH)
                            .readOnly(Boolean.TRUE));
                } else {
                    VolumeMounts volumeMounts = findBestMatchVolumeMounts(srcDir, containerPurchase.getVolumeMounts());
                    mountSet.add(
                        new V1VolumeMount()
                            .name(volumeMounts.getName())
                            .mountPath(volumeMounts.getMountPath())
                            .readOnly(Boolean.TRUE));
                }
            }
        }
    }
    // 获取已经在bls存在的任务id
    private List<String> getExistBlsTaskIDs(BciOrderExtra orderExtra, String accountId) throws BceException {
        BceException e = null;
        Map<String, List<LogCollection>> logCollections = getBlsLogCollections(orderExtra);
        QueryAllBlsTaskResponse response = new QueryAllBlsTaskResponse();
        Map<String, String> blsInfos = new HashMap<>();
        List<String> blsTaskIDs = new ArrayList<>();
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.queryAllBlsTask(accountId);
            response =
                (QueryAllBlsTaskResponse)
                    asyncExecutorService.getAsyncResult(
                        WorkKeyUtil.genWorkKey("queryAllBlsTask", Arrays.asList((Object) accountId)));
        } catch (RuntimeException ex) {
            LOGGER.error("sync getBlsTaskName work error! exception: ", ex);
            e = new BceException("failed to query bls task", 400);
            throw e;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
        for (QueryAllBlsTaskResponse.TaskDetail task : response.getTasks()) {
            blsInfos.put(task.getName(), task.getId());
        }
        for (Map.Entry<String, List<LogCollection>> entry : logCollections.entrySet()) {
            for (LogCollection logCollection : entry.getValue()) {
                if (blsInfos.containsKey(logCollection.getName())) {
                    blsTaskIDs.add(blsInfos.get(logCollection.getName()));
                }
            }
        }
        return blsTaskIDs;
    }
    // 获取在bls已经存在的任务详情
    private Map<String, QueryBLSTaskResponse> getExistBLSTaskInfoMapFromBls(BciOrderExtra orderExtra, String accountId)
        throws BceException {
        Map<String, QueryBLSTaskResponse> blsTaskResponseMap = new HashMap<>();
        List<String> blsTasksIDs = getExistBlsTaskIDs(orderExtra, accountId);
        if (!CollectionUtils.isEmpty(blsTasksIDs)) {
            blsTaskResponseMap = getBLSTasks(blsTasksIDs, accountId);
        }
        return blsTaskResponseMap;
    }

    public Map<String, QueryBLSTaskResponse> getBLSTasks(List<String> blsTasksId, String accountId)
        throws BceException {
        BceException e = null;
        Map<String, QueryBLSTaskResponse> responseHashMap = new HashMap<>();
        if (CollectionUtils.isEmpty(blsTasksId)) {
            return responseHashMap;
        }
        try {
            asyncExecutorService.initAsyncWorkMap();
            for (String blsTaskId : blsTasksId) {
                blsAyncService.getBLSTask(blsTaskId, accountId);
            }
            for (String blsTaskId : blsTasksId) {
                QueryBLSTaskResponse response =
                    (QueryBLSTaskResponse)
                        asyncExecutorService.getAsyncResult(
                            WorkKeyUtil.genWorkKey("getBLSTask", Arrays.asList((Object) blsTaskId, accountId)));
                responseHashMap.put(response.getTask().getStatus().getName(), response);
            }
        } catch (RuntimeException ex) {
            LOGGER.error("sync getBLSTask work exception! exception: ", ex);
            e = new BceException("failed to get bls task", 400);
            throw e;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }

        for (QueryBLSTaskResponse response : responseHashMap.values()) {
            if (response == null
                || response.getTask() == null
                || response.getTask().getConfig() == null
                || response.getTask().getConfig().getSrcConfig() == null
                || !response.getTask().getConfig().getSrcConfig().containsKey(SRC_CONFIG_SRC_DIR_KEY)) {
                e = new BceException("", 400, "get BLS task failed");
                throw e;
            }
        }
        return responseHashMap;
    }

    private VolumeMounts findBestMatchVolumeMounts(String srcDir, List<VolumeMounts> volumeMounts) {
        List<VolumeMounts> candidates = new ArrayList<>();
        for (VolumeMounts mounts : volumeMounts) {
            if (!PodVolumeType.EMPTY_DIR.getType().equals(mounts.getType())) {
                continue;
            }
            if (srcDir.startsWith(mounts.getMountPath()) && mounts.getMountPath().length() <= srcDir.length()) {
                candidates.add(mounts);
            }
        }
        if (CollectionUtils.isEmpty(candidates)) {
            throw new PodExceptions.ContianerLogCollectionException("no volumeMounts matched for " + srcDir);
        }
        Collections.sort(
            candidates,
            new Comparator<VolumeMounts>() {
                @Override
                public int compare(VolumeMounts o1, VolumeMounts o2) {
                    return o2.getMountPath().length() - o1.getMountPath().length();
                }
            });
        return candidates.get(0);
    }


    // 创建bls token
    private String createBlsToken(String accountId) throws BceException {
        BceException e = null;
        String blsToken = null;
        CreateBlsTokenRequest request = new CreateBlsTokenRequest();
        CreateBlsTokenResponse response = new CreateBlsTokenResponse();
        request.setDescription("BCI创建,请勿删除");
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.createBlsToken(request, accountId);
            response =
                (CreateBlsTokenResponse)
                    asyncExecutorService.getAsyncResult(
                        WorkKeyUtil.genWorkKey("createBlsToken", Arrays.asList((Object) request, accountId)));

        } catch (RuntimeException ex) {
            LOGGER.error("failed to create bls token, id: {}, ex: {}", accountId, ex);
            e = new BceException("failed to create bls token", 400);
            throw e;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }

        String blsTokenId = response.getUserTokenID();
        QueryBlsTokenIDResponse queryResponse = new QueryBlsTokenIDResponse();
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.queryBlsTokenInfoByID(blsTokenId, accountId);
            queryResponse =
                (QueryBlsTokenIDResponse)
                    asyncExecutorService.getAsyncResult(
                        WorkKeyUtil.genWorkKey("queryBlsTokenInfoByID", Arrays.asList((Object) blsTokenId, accountId)));

        } catch (RuntimeException ex) {
            LOGGER.error("failed to get bls token, id: {}, ex: {}", accountId, ex);
            e = new BceException("failed to get bls token id", 400);
            throw e;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }

        blsToken = queryResponse.getToken();
        return blsToken;
    }

    // 根据账户id查询bls token
    private String getBlsTaskToken(String accountId) throws BceException {
        BceException e = null;
        String blsToken = null;
        QueryBlsTokenResponse response = null;
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.queryAllBlsTokens(accountId);
            response =
                (QueryBlsTokenResponse)
                    asyncExecutorService.getAsyncResult(
                        WorkKeyUtil.genWorkKey("queryAllBlsTokens", Arrays.asList((Object) accountId)));
        } catch (RuntimeException ex) {
            LOGGER.error("async queryBlsToken work fail, id: {}, ex: {}", accountId, ex);
            e = new BceException("failed to get bls token", 400);
            throw e;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
        if (response != null && (response.getUserTokens().size() > 0)) {
            blsToken = response.getUserTokens().get(0).getToken();
        }
        return blsToken;
    }

    // 查询或者创建bls token并更新到数据库
    private String getOrCreateBlsToken(String accountId) throws BceException {
        String blsToken = null;
        UserInfoRequest userInfo = new UserInfoRequest();
        userInfo.setAccountId(accountId);
        CceUserMap cceUserMap = cceUserMapDao.getCceUserMapByUserId(accountId);
        if (!cceUserMap.getBlsUserToken().isEmpty()) {
            blsToken = cceUserMap.getBlsUserToken();
            return blsToken;
        } else {
            blsToken = getBlsTaskToken(accountId);
            if (blsToken != null && !blsToken.isEmpty()) {
                userInfo.setBlsTaskToken(blsToken);
                cceClusterService.updateUserInfo(accountId, userInfo);
                return blsToken;
            } else {
                blsToken = createBlsToken(accountId);
                userInfo.setBlsTaskToken(blsToken);
                cceClusterService.updateUserInfo(accountId, userInfo);
                return blsToken;
            }
        }
    }

    // 根据bls task name查询已经存在的bls任务
    private Map<String, String> getExistBlsTaskInfo(BciOrderExtra orderExtra, String accountId) throws BceException {
        BceException e = null;
        Map<String, List<LogCollection>> logCollections = getBlsLogCollections(orderExtra);
        QueryAllBlsTaskResponse response = new QueryAllBlsTaskResponse();
        Map<String, String> allBlsInfos = new HashMap<>();
        Map<String, String> existBlsInfos = new HashMap<>();
        try {
            asyncExecutorService.initAsyncWorkMap();
            blsAyncService.queryAllBlsTask(accountId);
            response =
                (QueryAllBlsTaskResponse)
                    asyncExecutorService.getAsyncResult(
                        WorkKeyUtil.genWorkKey("queryAllBlsTask", Arrays.asList((Object) accountId)));
        } catch (RuntimeException ex) {
            LOGGER.error("sync getBlsTaskName work error! exception: ", ex);
            e = new BceException("failed to query bls task", 400);
            throw e;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
        for (QueryAllBlsTaskResponse.TaskDetail task : response.getTasks()) {
            allBlsInfos.put(task.getName(), task.getId());
        }
        for (Map.Entry<String, List<LogCollection>> entry : logCollections.entrySet()) {
            for (LogCollection logCollection : entry.getValue()) {
                if (allBlsInfos.containsKey(logCollection.getName())) {
                    existBlsInfos.put(logCollection.getName(), allBlsInfos.get(logCollection.getName()));
                }
            }
        }
        return existBlsInfos;
    }

    // 创建bls任务;如果已经存在直接写入数据库，如果不存在则创建并写入到数据库
    private void createBlsTask(BciOrderExtra extra, Map<String, List<LogCollection>> logCollectionMaps, String userId) {
        List<LogCollection> needCreateLogCollections = new ArrayList<>();
        Map<String, String> existBlsTaskInfo = getExistBlsTaskInfo(extra, userId);
        List<LogCollection> allLogCollections = new ArrayList<>();
        for (Map.Entry<String, List<LogCollection>> entry : logCollectionMaps.entrySet()) {
            allLogCollections.addAll(entry.getValue());
        }
        for (LogCollection logCollection : allLogCollections) {
            if (existBlsTaskInfo.containsKey(logCollection.getName())) {
                BlsInfo blsInfo = new BlsInfo();
                blsInfo.setUserId(userId);
                blsInfo.setBlsTaskName(logCollection.getName());
                blsInfo.setBlsTaskId(existBlsTaskInfo.get(logCollection.getName()));
                blsInfo.setBlsTaskOwner(TaskOwner.USER.toString());
                try {
                    blsInfoDao.insert(blsInfo);
                } catch (DataAccessException ex) {
                    LOGGER.debug("blsInfo {} already exist,ignore", blsInfo);
                } 
            }
            else {
                needCreateLogCollections.add(logCollection);
            }
        }
        for (Map.Entry<String, List<LogCollection>> entry : logCollectionMaps.entrySet()) {
            for (LogCollection logCollection : needCreateLogCollections) {
                if (entry.getValue().contains(logCollection)) {
                    createBlsTaskMap(entry.getKey(), logCollection.getName(), logCollection, userId);
                }
            }
        }
    }

    // 处理入口
    private List<String> createBLSTasksBySrcAndDestConfig(
        BciOrderExtra orderExtra, String orderUuid, String accountId) {
        List<String> blsTasksIds = checkAndCreateBlsTaskInfo(orderExtra, accountId);
        List<PodPO> podPOs = podDao.listByOrderId(accountId, orderUuid);
        // 所有pod共用一份配置
        if (CollectionUtils.isEmpty(podPOs)) {
            return blsTasksIds;
        }
        
        podDao.batchUpdateBLSTasksID(podPOs, StringUtils.join(blsTasksIds, ","));
        return blsTasksIds;
    }
    
    private List<String> checkAndCreateBlsTaskInfo(BciOrderExtra orderExtra, String accountId) {
        List<LogCollection> allLogCollections = new ArrayList<>();
        Map<String, List<LogCollection>> logCollectionMaps = getBlsLogCollections(orderExtra);
        for (Map.Entry<String, List<LogCollection>> entry : logCollectionMaps.entrySet()) {
            allLogCollections.addAll(entry.getValue());
        }
        createBlsTask(orderExtra, logCollectionMaps, accountId);
        List<String> allBlsNameList = new ArrayList<>();
        for (LogCollection logCollection : allLogCollections) {
            allBlsNameList.add(logCollection.getName());
        }
        List<BlsInfo> blsInfos = blsInfoDao.batchSelect(allBlsNameList, accountId);
        List<String> blsTaskIds = new ArrayList<>();
        for (BlsInfo blsInfo : blsInfos) {
            blsTaskIds.add(blsInfo.getBlsTaskId());
        }
        return blsTaskIds;
    }
}
