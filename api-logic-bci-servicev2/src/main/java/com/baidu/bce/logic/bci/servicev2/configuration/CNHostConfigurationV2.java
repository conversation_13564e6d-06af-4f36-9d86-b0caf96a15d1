package com.baidu.bce.logic.bci.servicev2.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@Configuration("CNHostConfigurationV2")
@ConfigurationProperties(prefix = "bci.cn")
public class CNHostConfigurationV2 {
    Map<String, String> zoneHostMap;
}


