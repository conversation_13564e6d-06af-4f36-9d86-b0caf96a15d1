package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpsTaskCRDGetResponse {

    private OpsTaskCRDMetadata metadata;
    private OpsTaskCRDSpec spec;
    private OpsTaskCRDStatus status;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OpsTaskCRDGetResponse that = (OpsTaskCRDGetResponse) o;
        return Objects.equals(metadata, that.metadata)
                && Objects.equals(spec, that.spec)
                && Objects.equals(status,
                that.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(metadata, spec, status);
    }
}
