package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.kubernetes.client.custom.IntOrString;
import io.kubernetes.client.openapi.models.V1TCPSocketAction;
import io.kubernetes.client.openapi.models.V1LifecycleHandler;
import io.kubernetes.client.openapi.models.V1ExecAction;
import io.kubernetes.client.openapi.models.V1HTTPGetAction;
import io.kubernetes.client.openapi.models.V1HTTPHeader;

import org.apache.commons.collections.CollectionUtils;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.ArrayList;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class LifecycleHandler {
    private ExecAction exec;

    private HTTPGetAction httpGet;

    private TCPSocketAction tcpSocket;

    public V1LifecycleHandler toV1LifecycleHandler() {
        V1LifecycleHandler v1LifecycleHandler = new V1LifecycleHandler();
            
        if (this.getExec() != null && CollectionUtils.isNotEmpty(this.getExec().getCommand())) {
            ExecAction ea = this.getExec();
            V1ExecAction v1ExecAction = new V1ExecAction();
            v1ExecAction.setCommand(ea.getCommand());

            v1LifecycleHandler.setExec(v1ExecAction);
        }

        if (this.getHttpGet() != null) {
            HTTPGetAction hga = this.getHttpGet();
            V1HTTPGetAction v1httpGetAction = new V1HTTPGetAction();
            v1httpGetAction.setHost(hga.getHost());
            v1httpGetAction.setPort(new IntOrString(hga.getPort()));
            v1httpGetAction.setPath(hga.getPath());
            v1httpGetAction.setScheme(hga.getScheme());

            List<V1HTTPHeader> httpHeaders = new ArrayList<V1HTTPHeader>();
            for (HTTPHeader header : hga.getHttpHeaders()) {
                V1HTTPHeader v1httpHeader = new V1HTTPHeader();
                v1httpHeader.setName(header.getName());
                v1httpHeader.setValue(header.getValue());

                httpHeaders.add(v1httpHeader);
            }

            v1httpGetAction.setHttpHeaders(httpHeaders);

            v1LifecycleHandler.setHttpGet(v1httpGetAction);
        }

        // tcpSocket handler已弃用
        if (this.getTcpSocket() != null) {
            TCPSocketAction tsa = this.getTcpSocket();
            V1TCPSocketAction v1tcpSocketAction = new V1TCPSocketAction();
            v1tcpSocketAction.setHost(tsa.getHost());
            v1tcpSocketAction.setPort(new IntOrString(tsa.getPort()));

            v1LifecycleHandler.setTcpSocket(v1tcpSocketAction);
        }

        return v1LifecycleHandler;
    }
}
