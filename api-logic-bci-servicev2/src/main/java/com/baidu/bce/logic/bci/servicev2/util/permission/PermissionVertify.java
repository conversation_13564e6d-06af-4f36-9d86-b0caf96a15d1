package com.baidu.bce.logic.bci.servicev2.util.permission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PermissionVertify {
    // 类型
    // 如果是订单的，子用户均不可操作
    // 其他操作类，此项为空
    String type() default "";

    // 服务号，格式如：bce:bcc, 处理同一接口属于多个产品的，比如：dcc_bcc
    String[] service() default {};

    // 权限列表，如【“CreateVm”】
    String[] permission() default {};

}
