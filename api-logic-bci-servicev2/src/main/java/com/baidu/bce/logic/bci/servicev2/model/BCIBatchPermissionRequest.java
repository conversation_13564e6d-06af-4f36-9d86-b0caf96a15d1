package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest;
import com.fasterxml.jackson.annotation.JsonProperty;

public class BCIBatchPermissionRequest extends BatchPermissionRequest {
    @JsonProperty("security_token")
    private String securityToken;

    public String getSecurityToken() {
        return securityToken;
    }

    public void setSecurityToken(String securityToken) {
        this.securityToken = securityToken;
    }

    public BatchPermissionRequest withSecurityToken(String securityToken) {
        this.securityToken = securityToken;
        return this;
    }
}
