package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.user.settings.sdk.model.FeatureTypeListRequest;
import com.baidu.bce.user.settings.sdk.model.FeatureTypeListResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component
public class AclServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclServiceV2.class);

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    /**
     * 查询用户拥有的白名单列表
     *
     * @return
     */
    public List<String> getAclList() {
        try {
            FeatureTypeListRequest request = new FeatureTypeListRequest();
            request.setAclName(getAccountId());
            request.setAclType("AccountId");
            FeatureTypeListResponse response =
                    logicPodClientFactory.createUserSettingsClient(getAccountId()).showFeatureTypes(request);
            return response.getFeatureTypes();
        } catch (Exception e) {
            LOGGER.error("inBccNewFlavorWhiteList error!", e);
        }
        return new ArrayList<>();
    }

    public String getAccountId() {
        return LogicUserService.getAccountId();
    }
}

