package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.EipInfoSyncServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("EipInfoSyncSchedulerV2")
@Profile("default")
public class EipInfoSyncSchedulerV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(EipInfoSyncSchedulerV2.class);

    private String schedulerName = "EipInfoSyncSchedulerV2.runScheduledTask";
    private static final int EIP_INFO_SYNC_PERIOD = 60000;
    // 60s

    @Autowired
    private EipInfoSyncServiceV2 eipSyncService;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    @Scheduled(fixedDelay = EIP_INFO_SYNC_PERIOD)
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        eipSyncService.syncEipInfo();
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }
}
