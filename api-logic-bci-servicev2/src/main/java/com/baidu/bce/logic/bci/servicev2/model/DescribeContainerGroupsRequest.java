package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class DescribeContainerGroupsRequest {
    private String keyword = "";
    private String keywordType = "";
    private String marker = "";
    private Integer maxKeys = 10;
}