package com.baidu.bce.logic.bci.servicev2.model;

import io.kubernetes.client.openapi.models.V1ListMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class OpsTaskCRDRequestList implements io.kubernetes.client.common.KubernetesListObject {

    private String apiVersion;

    private List<OpsTaskCRDRequest> items = new ArrayList<>();

    private String kind;

    private V1ListMeta metadata;

    public OpsTaskCRDRequestList apiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
        return this;
    }

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public OpsTaskCRDRequestList items(List<OpsTaskCRDRequest> items) {

        this.items = items;
        return this;
    }

    public OpsTaskCRDRequestList addItemsItem(OpsTaskCRDRequest itemsItem) {
        this.items.add(itemsItem);
        return this;
    }


    public List<OpsTaskCRDRequest> getItems() {
        return items;
    }

    public void setItems(List<OpsTaskCRDRequest> items) {
        this.items = items;
    }

    public OpsTaskCRDRequestList kind(String kind) {

        this.kind = kind;
        return this;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public OpsTaskCRDRequestList metadata(V1ListMeta metadata) {

        this.metadata = metadata;
        return this;
    }


    public V1ListMeta getMetadata() {
        return metadata;
    }

    public void setMetadata(V1ListMeta metadata) {
        this.metadata = metadata;
    }

    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OpsTaskCRDRequestList opsTaskCRDRequestList = (OpsTaskCRDRequestList) o;
        return Objects.equals(this.apiVersion, opsTaskCRDRequestList.apiVersion)
                && Objects.equals(this.items, opsTaskCRDRequestList.items)
                && Objects.equals(this.kind, opsTaskCRDRequestList.kind)
                && Objects.equals(this.metadata, opsTaskCRDRequestList.metadata);
    }

    @Override
    public int hashCode() {
        return Objects.hash(apiVersion, items, kind, metadata);
    }
}
