package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BciOrderExtra {
    private String restartPolicy;
    private Volume volume;
    private List<ContainerPurchase> containers;
    private List<ImageRegistrySecret> imageRegistrySecrets;
    private List<Tag> tags;
    private String logicalZone;
    private String zoneId;
    private String zoneSubnets;
    private String name;
    private String securityGroupId;
    private String securityGroupShortId;
    private String subnetId;
    private String subnetShortId;
    private String subnetCidr;
    private String vpcId;
    private String vpcCidr;
    private String annotations;
    private String physicalZone;
    private String subnetUuid;
    private String eipPurchaseRequest;
    private List<Label> labels;
    private boolean enableLog;
    // 当统一计费时，会在extra中加用户的accountID
    private String extraAccountID;
    private boolean isV3;
    private String chargeSource;
    private float podCpu;
    private float podMem;
    private String cpuType;
    private String podGpuType; // 卡类型，只支持独占售卖，后续共享在继续增加新字段，比如radio或者归一化的算例值
    private float podGpuCount; // 卡数量，支持<1，也支持多卡（>=1需要是整数），算例显存相同比例售卖，当前没有归一化值

    private String podId;
    private String userId;
    private PodExtra podExtra;

    Map<String, String> annotationsMap = new HashMap<>();

    private boolean isTidal;

    private Boolean autoMatchImageCache;
    
    // 是否支持调度到pfs资源池
    private boolean scheduleInPfsPool;

    // 是否是镜像缓存请求
    private boolean isImageCacheReq = false;
    private ImageAccelerateCRDRequest imageCacheCrdReq;

    private String clientToken;
    private int maxPendingMinute;

    private String podResourceIgnoreContainers = "";
    private String podIgnoreExitCodeContainers = "";
    private String podIgnoreNotReadyContainers = "";
    private String podFailStrategy = "";
    private boolean enableIPv6; // 是否为双栈Pod
}
