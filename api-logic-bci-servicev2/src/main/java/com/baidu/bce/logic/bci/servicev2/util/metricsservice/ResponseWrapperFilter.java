package com.baidu.bce.logic.bci.servicev2.util.metricsservice;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ResponseWrapperFilter implements Filter {
    private static final ThreadLocal<HttpServletResponse> RESPONSE_HOLDER = new ThreadLocal<>();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (response instanceof HttpServletResponse) {
            CustomHttpServletResponseWrapper responseWrapper = new CustomHttpServletResponseWrapper((HttpServletResponse) response);
            RESPONSE_HOLDER.set(responseWrapper);
            chain.doFilter(request, responseWrapper);
        } else {
            chain.doFilter(request, response);
        }
    }

    public static HttpServletResponse getCurrentResponse() {
        return RESPONSE_HOLDER.get();
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {}

    @Override
    public void destroy() {}
}
