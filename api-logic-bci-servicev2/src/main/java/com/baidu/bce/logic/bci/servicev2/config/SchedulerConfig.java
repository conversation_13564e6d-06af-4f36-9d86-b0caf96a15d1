package com.baidu.bce.logic.bci.servicev2.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.beans.factory.annotation.Value;

@Configuration
@EnableScheduling
public class SchedulerConfig implements SchedulingConfigurer {

    @Value("${pod.scheduler.config.thread.pool.size:50}")
    private int schedulerConfigThreadPoolSize;

    @Value("${pod.scheduler.sync.thread.pool.size:10}")
    private int syncPodInDBSchedulerThreadPoolSize;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();

        scheduler.setPoolSize(schedulerConfigThreadPoolSize);
        scheduler.setThreadNamePrefix("BciSchedulerThreadPoolScheduler-");
        scheduler.initialize();

        taskRegistrar.setTaskScheduler(scheduler);
    }

    @Bean(name = "syncPodInDBScheduler")
    public ThreadPoolTaskScheduler podInBuildSyncScheduler() {
        ThreadPoolTaskScheduler syncPodInDBScheduler = new ThreadPoolTaskScheduler();
        syncPodInDBScheduler.setPoolSize(syncPodInDBSchedulerThreadPoolSize);
        syncPodInDBScheduler.setThreadNamePrefix("BciPodInBuildSyncThreadPoolScheduler-");
        syncPodInDBScheduler.initialize();
        return syncPodInDBScheduler;
    }
}