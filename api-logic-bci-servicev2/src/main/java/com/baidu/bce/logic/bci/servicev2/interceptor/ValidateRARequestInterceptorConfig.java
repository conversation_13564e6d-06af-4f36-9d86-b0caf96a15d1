package com.baidu.bce.logic.bci.servicev2.interceptor;

import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.plat.webframework.iam.service.IAMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;


@Configuration("ValidateRARequestInterceptorConfigV2")
public class ValidateRARequestInterceptorConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private LogicPodClientFactoryV2 bciClientFactory;

    @Autowired
    private ResourceAccountConfig config;

    @Autowired
    private IAMService iamService;

    /**
     * 对于创建，删除
     * 若是PaaS方调用，需要校验资源账号
     * @param registry
     */
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ValidateRARequestInterceptor(bciClientFactory, config, iamService))
                .addPathPatterns(
                        "/api/logical/bci/v1/pod/create",
                        "/api/logical/bci/v1/pod/list",
                        "/api/logical/bci/v1/pod/delete",
                        "/api/logical/bci/v1/pod/leakage/delete",
                        "/v1/pod/create",
                        "/v1/pod/list",
                        "/v1/pod/delete",

                        "/api/logical/bci/v2/pod/create",
                        "/api/logical/bci/v2/pod/list",
                        "/api/logical/bci/v2/pod/list/light",
                        "/api/logical/bci/v2/pod/{podId}",
                        "/api/logical/bci/v2/pod/{podId}/describe/light",
                        "/api/logical/bci/v2/pod/delete",
                        "/api/logical/bci/v2/pod/leakage/delete",
                        "/api/logical/bci/v2/pod/describe",
                        "/api/logical/bci/v2/pod/webshell",

                        "/v2/pod/create",
                        "/v2/pod/list",
                        "/v2/pod/list/light",
                        "/v2/pod/{podId}",
                        "/v2/pod/{podId}/describe/light",
                        "/v2/pod/delete",
                        "/v2/pod/describe",
                        "/v2/pod/webshell",

                        "/v2/instance",
                        "/v2/instance/{instanceId}",
                        "/v2/instance/batchDel",

                        "/v2/bcm/pod/list",
                        "/v2/bcm/pod/detail",
                        "/v2/bcm/pod/containers",
                        "/v2/bcm/resgroup/pod/list",

                        "/v2/tag/resources"
                        );
    }
}
