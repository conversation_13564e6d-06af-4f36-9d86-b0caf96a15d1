package com.baidu.bce.logic.bci.servicev2.util;

public class UtilException extends Exception {
  private ErrorCode errorCode;
  private String message;

  public UtilException(ErrorCode errorCode, String message) {
    this.errorCode = errorCode;
    this.message = message;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }

  public String getMessage() {
    return message;
  }

  public enum ErrorCode {
    OK,
    PARSE_MAP_ERROR,
  }
}