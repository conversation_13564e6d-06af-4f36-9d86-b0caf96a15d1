package com.baidu.bce.logic.bci.servicev2.webshell;

import com.baidu.bce.logic.bci.daov2.webshell.model.WebshellPO;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.util.WebSocketStreamHandler;
import io.kubernetes.client.util.WebSockets;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

public class WebShellExec extends Process {

    private WebshellPO webshellPO;

    private WebSocketStreamHandler streamHandler;

    private final Map<Integer, InputStream> input = new HashMap<>();

    public WebShellExec(WebshellPO webshellPO, WebSocketStreamHandler streamHandler) {
        this.webshellPO = webshellPO;
        this.streamHandler = streamHandler;
    }

    public void exec(ApiClient client) throws Exception {
        WebSockets.stream(webshellPO.getWsUrl(), "GET", client, streamHandler);
    }

    @Override
    public OutputStream getOutputStream() {
        return streamHandler.getOutputStream(0);
    }

    @Override
    public InputStream getInputStream() {
        return getInputStreamByStream(1);
    }

    @Override
    public InputStream getErrorStream() {
        return getInputStreamByStream(2);
    }

    public OutputStream getResizeStream() {
        return streamHandler.getOutputStream(4);
    }

    private synchronized InputStream getInputStreamByStream(int stream) {
        if (!input.containsKey(stream)) {
            input.put(stream, streamHandler.getInputStream(stream));
        }
        return input.get(stream);
    }

    @Override
    public int waitFor() throws InterruptedException {
        return 0;
    }

    @Override
    public int exitValue() {
        return 0;
    }

    @Override
    public void destroy() {

    }

    public String getWsUrl() {
        return webshellPO.getWsUrl();
    }
}
