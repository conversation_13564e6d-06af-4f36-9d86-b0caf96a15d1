package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CephFSVolume extends BaseVolume {
    @Data
    public class SecretRef {
        private String name;
    }
    private String user;
    private String path;
    private List<String> monitors;
    // 暂时不支持secretFile
    private String secretFile;
    private SecretRef secretRef;
    // CephFSVolume数据卷是否只读, 默认为false
    private Boolean readOnly = false;
}
