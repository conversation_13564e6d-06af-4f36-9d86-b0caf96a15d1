package com.baidu.bce.logic.bci.servicev2.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.request.OrderModel;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Service("ReservedInstanceValidator")
public class ReservedInstanceValidator {
    public static final Logger LOGGER = LoggerFactory.getLogger(ReservedInstanceValidator.class);

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;
    
    private static final String QNAME_CHAR_FMT = "[A-Za-z0-9]";
    private static final String QNAME_EXR_CHAR_FMT = "[-A-Za-z0-9._:]";
    private static final String SEARCH_VALUE = "[\u4e00-\u9fa5a-zA-Z\\d-_/:.]{1,256}";
    public static final String PATTERN = "(" + QNAME_CHAR_FMT + QNAME_EXR_CHAR_FMT + "*)?" + QNAME_CHAR_FMT;
    
    /**
     * 验证搜索的值是否合法
     *
     * @param value 搜索值
     * @return 返回true代表合法
     */
    public static boolean validateSearchId(String value) {
        Pattern pattern = Pattern.compile(SEARCH_VALUE);
        return StringUtils.isEmpty(value) || pattern.matcher(value).matches();
    }

    /**
     * 验证搜索的值是否合法
     *
     * @param filterMap 过滤条件
     * @return 返回true代表过滤条件合法
     */
    public static boolean validateSearchId(Map<String, String> filterMap) {
        for (Map.Entry<String, String> entry : filterMap.entrySet()) {
            if (!validateSearchId(entry.getValue())) {
                return false;
            }
        }
        return true;
    }

    public void validate(BaseCreateOrderRequestVo<CreateReservedInstanceRequest> request) {
        checkUserId();
        Map<String, String> requestMap = new HashMap<>();
        for (int i = 0; i < request.getItems().size(); i++) {
            CreateReservedInstanceRequest validatedRequest = request.getItems().get(i).getConfig();
            validateCreateReservedInstanceRequest(request.getItems().get(i).getConfig(), validatedRequest, requestMap);
            request.getItems().get(i).setConfig(validatedRequest);
        }
    }

    public ListRequest validateListReservedInstanceRequest(ListRequest listRequest) {
        ListRequest validatedRequest = listRequest;
        if (listRequest.getPageSize() < 1) {
            throw new CommonExceptions.RequestInvalidException(String.format("%d is not a valid page size", 
                    listRequest.getPageSize()));
        }

        if (listRequest.getPageNo() < 1) {
            throw new CommonExceptions.RequestInvalidException(String.format("%d is not a valid page number", 
                    listRequest.getPageNo()));
        }

        OrderModel orderModel = null;
        if (listRequest.getOrders() == null) {
            orderModel = new OrderModel();
            orderModel.setOrderBy("createdTime");
            orderModel.setOrder("DESC");
        } else {
            orderModel = listRequest.getOrders().get(0);
        }
        if (LogicalConstant.RESERVED_INSTANCE_FIELD_MAP.containsKey(orderModel.getOrderBy())) {
            orderModel.setOrderBy(LogicalConstant.RESERVED_INSTANCE_FIELD_MAP.get(orderModel.getOrderBy()));
        } else {
            throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valid order by field", 
                    orderModel.getOrderBy()));
        }
        if (!orderModel.getOrder().equalsIgnoreCase("ASC") && !orderModel.getOrder().equalsIgnoreCase("DESC")) {
            throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valid order", 
                    orderModel.getOrder()));
        } else {
            orderModel.setOrder(orderModel.getOrder().toUpperCase());
        }
        ArrayList<OrderModel> orderModels = new ArrayList<OrderModel>();
        orderModels.add(orderModel);
        validatedRequest.setOrders(orderModels);

        Map<String, String> filterMap = new java.util.HashMap<String, String>();
        if (listRequest.getFilterMap() != null) {
            for (Map.Entry<String, String> entry : listRequest.getFilterMap().entrySet()) {
                if (!LogicalConstant.RESERVED_INSTANCE_FIELD_MAP.containsKey(entry.getKey())) {
                    throw new CommonExceptions.RequestInvalidException(String.format("%s is not valid filter field", 
                            entry.getKey()));
                } else {
                    if (!validateSearchId(entry.getValue())) {
                        throw new CommonExceptions.RequestInvalidException(String.format("%s is not valid filter val",
                                entry.getValue()));
                    } else {
                        filterMap.put(LogicalConstant.RESERVED_INSTANCE_FIELD_MAP.get(entry.getKey()), 
                                entry.getValue());
                    }
                }
            }
            validatedRequest.setFilterMap(filterMap);
        }
        return validatedRequest;
    }

    private void validateCreateReservedInstanceRequest(CreateReservedInstanceRequest request,
            CreateReservedInstanceRequest validatedRequest, Map<String, String> requestMap) {
        if (!request.getName().isEmpty() && !Pattern.matches(PATTERN, request.getName())) {
            throw new CommonExceptions.RequestInvalidException(String.format(
                    " %s is not a valid name, a valid name must consist of alphanumeric characters, " + 
                    " '-', ':', '_' or '.', and must start and end with an alphanumeric character", 
                    request.getName()));
        }

        if (!request.getScope().equals(LogicalConstant.ReservedInstanceScope.REGION) &&
                !request.getScope().equals(LogicalConstant.ReservedInstanceScope.AZ)) {
            throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valid scope",
                    request.getScope()));  
        }

        String physicalZone = "";
        if (!request.getLogicalZone().isEmpty()) {
            ZoneMapDetail zoneMapDetail = getZone(request.getLogicalZone());
            physicalZone = zoneMapDetail.getPhysicalZone();
        }
        validatedRequest.setPhysicalZone(physicalZone);

        if (!request.getPurchaseMode().equals(LogicalConstant.ReservedInstancePurchaseMode.FULLY_PREPAY) &&
                !request.getPurchaseMode().equals(LogicalConstant.ReservedInstancePurchaseMode.PART_PREPAY) &&
                !request.getPurchaseMode().equals(LogicalConstant.ReservedInstancePurchaseMode.POSTPAY)) {
            throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valid purchaseMode",
                    request.getPurchaseMode()));
        }
        
        if (request.getReservedInstanceCount() <= 0) {
            throw new CommonExceptions.RequestInvalidException("reservedInstanceCount must bigger than 0");
        }
        if (!request.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.DAY) &&
                !request.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.MONTH) &&
                !request.getReservedTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.YEAR)) {
            throw new CommonExceptions.RequestInvalidException(
                    String.format("%s is not a valid reservedInstanceTimeUnit", request.getReservedTimeUnit()));
        }
        
        if (request.getReservedTimePeriod() <= 0) {
            throw new CommonExceptions.RequestInvalidException("reservedTimePeriod must bigger than 0");
        }

        if (request.isAutoRenew()) {
            if (request.getAutoRenewTimePeriod() <= 0) {
                throw new CommonExceptions.RequestInvalidException("autoRenewTimePeriod must bigger than 0");
            }
            if (!request.getAutoRenewTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.DAY) &&
                    !request.getAutoRenewTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.MONTH) &&
                    !request.getAutoRenewTimeUnit().equals(LogicalConstant.ReservedInstanceTimeUnit.YEAR)) {
                throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valid autoRenewTimeUnit",
                        request.getAutoRenewTimeUnit()));
            }
        }
        // effectiveTime 在 controller 已经处理，这里不重复校验
        // 暂时不允许一次请求出现多个相同规格的item（地域-规格-生效时间-预留时长-预留时长单位）
        String requestSpec = String.format("%s-%s-%s-%s-%s", request.getReservedSpec(), request.getLogicalZone(),
                request.getEffectiveTime(), request.getReservedTimeUnit(), request.getReservedTimePeriod());
        if (requestMap.containsKey(requestSpec)) {
            throw new CommonExceptions.RequestInvalidException(
                    "Multiple requests of the same specification are not allowed");
        } else {
            requestMap.put(requestSpec, "1");
        }
    }

    private void checkUserId() {
        String accountId = getAccountId();
        if (accountId == null || accountId.isEmpty()) {
            throw new CommonExceptions.RequestInvalidException(String.format("AccountId is null or empty: %s",
                    accountId));
        }
    }

    private ZoneMapDetail getZone(String logicalZone) {
        ZoneMapDetail zoneMapDetail = null;
        try {
            ZoneClient zoneClient = logicPodClientFactory.createZoneClient(getAccountId());
            zoneMapDetail = zoneClient.createZoneByLogicalZone(logicalZone);
        } catch (Exception e) {
            LOGGER.info("getZone from logical zone error = {}", e);
            throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valid locicalZone",
                    logicalZone));
        }

        if (zoneMapDetail == null) {
            throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valid locicalZone",
                    logicalZone));
        }

        return zoneMapDetail;
    }

    public ZoneMapDetail getZoneByPhysicalZone(String physicalZone) {
        ZoneMapDetail zoneMapDetail = null;
        try {
            ZoneClient zoneClient = logicPodClientFactory.createZoneClient(getAccountId());
            zoneMapDetail = zoneClient.createZoneByPhysicalZone(physicalZone);
        } catch (Exception e) {
            LOGGER.info("getZone from physical zone error = {}", e);
            throw new PodExceptions.InvalidateZoneException();
        }

        if (zoneMapDetail == null) {
            throw new PodExceptions.InvalidateZoneException();
        }

        return zoneMapDetail;
    }

}
