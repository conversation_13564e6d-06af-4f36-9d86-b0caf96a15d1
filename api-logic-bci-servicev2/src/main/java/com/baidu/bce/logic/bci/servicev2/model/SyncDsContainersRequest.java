package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class SyncDsContainersRequest {
    @JsonProperty(value = "expectDsContainers")
    private List<ContainerPurchase> expectDsContainers;

    @JsonProperty(value = "expectDsVolumes")
    private Volume expectDsVolumes;
}