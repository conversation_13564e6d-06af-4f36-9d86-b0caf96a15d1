package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImageAccelerateCRDSpec {
    private BciUserInfo bciUser;
    private List<String> images;
    private List<ImageRegistrySecret> auths;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BciUserInfo {
        private String accountId;
        private String logicZone;
        private String physicalZone;
        private String zoneId;
        private boolean enableEni;
        private boolean needEip;
        private String eipName;
        private String eipRouteType;
        private int eipBandwidthInMbps;
        private String eipBillingMethod;
        // 可选参数
        private String eipIp;
        private String userId;
        private String subnetId;
        private String securityGroupIds;
        private String vpcCidr;
        private String zoneSubnets;
        private String tcPodName;
    }
}
