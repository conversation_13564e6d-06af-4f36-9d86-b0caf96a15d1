package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.VpcIdsRequest;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.Map;

@Service("VpcUtil")
public class VpcUtil {
    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    public SimpleVpcVo getVpcByVpcId(String vpcId) {
        ExternalVpcClient vpcClient = logicPodClientFactory.createExternalVpcClient(getAccountId());
        VpcIdsRequest vpcIdsRequest = new VpcIdsRequest();
        vpcIdsRequest.setVpcIds(Arrays.asList(vpcId));
        Map<String, SimpleVpcVo> vpcMap = vpcClient.get(vpcIdsRequest);
        if (vpcMap == null || vpcMap.isEmpty()) {
            return null;
        }
        SimpleVpcVo simpleVpcVo = vpcMap.get(vpcId);
        return simpleVpcVo;
    }

    public String getVpcCidrByVpcId(String vpcId) {
        String vpcCidr = "";
        SimpleVpcVo simpleVpcVo = getVpcByVpcId(vpcId);
        if (simpleVpcVo == null) {
            return vpcCidr;
        }
        vpcCidr = simpleVpcVo.getCidr();
        return vpcCidr;
    }

    public String getAccountId() {
        return LogicUserService.getAccountId();
    }
}
