package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.model.BccInstanceListRequest;
import com.baidu.bce.internalsdk.bci.model.BccInstanceListResponse;
import com.baidu.bce.internalsdk.bci.model.BidInstanceEventsRequest;
import com.baidu.bce.internalsdk.bci.model.BidInstanceEventsResponse;
import com.baidu.bce.internalsdk.bci.model.BccInstanceListResponse.InstanceId;
import com.baidu.bce.internalsdk.bci.model.BidInstanceEventsResponse.BidEvent;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PreemptStatus;
import com.baidu.bce.logic.bci.servicev2.util.MessageUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodConfiguration;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Service("BidPodSyncServiceV2")
public class BidPodSyncServiceV2 extends SyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(BidPodSyncServiceV2.class);

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private LogicalResourceServiceV2 logicalResourceService;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private MessageUtil messageUtil;

    @Autowired
    private PodServiceV2 podServiceV2;

    @Autowired
    private PodRecycleServiceV2 podRecycleServiceV2;

    @Value("${bci.fakeorder.enable:false}")
    private boolean enableFakeOrder;

    @Value("${bci.bcc.accountId:test}")
    private String bciAccountId;

    public void bidPodPreemptStatusSync() {
        // 获取可能被bcc清理的pod列表（条件=竞价&running&创建时间>=1h）
        List<PodPO> pods = podDao.listBidRunningPods(PreemptStatus.RUNNING.toString(),
                                                            podConfiguration.getBidProductType(),
                                                            podConfiguration.getBidProtectedPeriodMin());
        if (pods == null || pods.isEmpty()) {
            return ;
        }
        List<String> bccInstanceIds = new ArrayList<>();
        for (PodPO podPO : pods) {
            bccInstanceIds.add(podPO.getBccInstanceId());
        }
        LOGGER.debug("bidPodPreemptStatusSync bccInstanceIds: " + bccInstanceIds);
        if (bccInstanceIds == null || bccInstanceIds.isEmpty()) {
            return;
        }
        // 如果列表不为空，则获取bcc的退场列表
        // https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/Pk9qRIMo58/cA3dD82JhJas_F?source=102
        BCCClient bccClient = logicPodClientFactory.createBCCClient(bciAccountId); 
        BidInstanceEventsRequest bidInstanceEventsRequest = new BidInstanceEventsRequest();
        // bidInstanceEventsRequest.setInstanceIds(bccInstanceIds);
        bidInstanceEventsRequest.setPageSize(100);
        bidInstanceEventsRequest.setPageNo(1);
        // bidInstanceEventsRequest.setPageNo((int) Math.ceil((double) bccInstanceIds.size() / 100));
        LOGGER.debug("bidInstanceEventsRequest: " + bidInstanceEventsRequest.toString());
        BidInstanceEventsResponse response = bccClient.getBccBidInstanceEvents(bidInstanceEventsRequest);
        LOGGER.debug("BidInstanceEventsResponse: " + response.toString());
        
        // 测试环境&bcc没数据，mock bcc event
        if (enableFakeOrder && (response == null || response.getEvents() == null || response.getEvents().isEmpty())) {
            BidEvent bidEventMock = new BidEvent();
            bidEventMock.setInstanceId("i-TestFakeBcc");
            List<BidEvent> bidEventListMock = new ArrayList<>();
            bidEventListMock.add(bidEventMock);
            if (response == null) {
                response = new BidInstanceEventsResponse();
            }
            response.setEvents(bidEventListMock);
            LOGGER.debug("mock BidInstanceEventsResponse: " + response.toString());
        }

        // 没有要退场的instanceid
        if (response == null || response.getEvents() == null || response.getEvents().isEmpty()) {
            return;
        }
        // 存在要退场的instanceid
        List<String> willBePreemptedInstanceIds = new ArrayList<>();
        for (BidEvent bidEvent : response.getEvents()) {
            willBePreemptedInstanceIds.add(bidEvent.getInstanceId());
        }
        if (willBePreemptedInstanceIds.isEmpty()) {
            return;
        }
        LOGGER.debug("willBePreemptedInstanceIds: " + willBePreemptedInstanceIds);
        // 更新pod db preemptedStatus=TOBEPREEMPTED，同一个region下，instanceid是唯一的
        podDao.updatePreemptedStatusByInstanceId(willBePreemptedInstanceIds, 
                                                PreemptStatus.RUNNING.toString(),
                                                PreemptStatus.TO_BE_PREEMPTED.toString());
        // send message
        List<String> podIds = new ArrayList<>();
        for (PodPO pod : pods) {
            if (!willBePreemptedInstanceIds.contains(pod.getBccInstanceId())) {
                continue;
            }
            podIds.add(pod.getPodId());
            // "{\"region\":\"hn\", \"podId\":\"podId\"}"
            String contentVar = "{\"region\":\"" + regionConfiguration.getCurrentRegion() 
                                + "\", \"podId\":\"" + pod.getPodId() + "\"}";
            messageUtil.sendMessage(pod.getUserId(), contentVar);
        }
        LOGGER.debug("Message podIds: " + podIds);
    }

    public void stopPreemptedBidPod() {
        // 获取TO_BE_PREEMPTED状态&超过4.5min(270s) 的pod列表，直接删除
        List<PodPO> podPOs = podDao.listToBePreemptBidPods(PreemptStatus.TO_BE_PREEMPTED.toString(), 
                                                        podConfiguration.getBidPreemtWaitSec());
        LOGGER.debug("stopPreemptedBidPod podPOs: " + podPOs);
        if (podPOs == null || podPOs.isEmpty()) {
            return;
        }
        // update podChargeStatusDao 停止计费
        List<String> podIds = new ArrayList<>();
        for (PodPO podPO : podPOs) {
            String podId = stopPod(podPO);
            if (podId != null) {
                podIds.add(podId);
            }
        }
        LOGGER.debug("stop succeed podIds: " + podIds);
        // 更新停止成功的pod db的信息（status， PreemptStatus），没有操作成功的，当前会一直重试？？
        if (podIds.size() > 0) {
            podDao.batchUpdatePreemptedPod(podIds, BciStatus.SUCCEEDED.getStatus(), PreemptStatus.PREEMPTED.toString());
        }
    }

    private String stopPod(PodPO podPO) {
        try {
            // billing 的接口，得用创建订单的accountID
            logicalResourceService.deleteResourceByName(podRecycleServiceV2.getOrderOwner(podPO), 
                                                        podPO.getPodUuid(), 
                                                        PodConstants.SERVICE_TYPE);
            // 从k8s集群删除，忽略异常？？需要有异常删除pod的回收
            deletePodFromK8s(podPO);
            // 更新pod Record
            podServiceV2.deletePodRecord(podPO);
            return podPO.getPodId();
        } catch (Exception e) {
            LOGGER.error("stop pod error: ", e);
        }
        return null;
    }

    private void deletePodFromK8s(PodPO podPO) {
        try {
            k8sService.deletePod(podPO.getPodId(), podPO.getUserId());
        } catch (Exception e) {
            LOGGER.error("delete k8s pod error:{},{}", podPO.getPodId(), e.toString());
        }
    }

    public void deletePreemptedBidPod() {
        // 获取已经被删除的pod列表&删除时间>24h
        List<PodPO> podPOs = podDao.listPreemptedBidPods(BciStatus.SUCCEEDED.getStatus(),
                                                        PreemptStatus.PREEMPTED.toString(),
                                                        podConfiguration.getBidPreemptedClearHour());
        LOGGER.debug("deletePreemptedBidPod podPOs: " + podPOs);
        if (podPOs == null || podPOs.isEmpty()) {
            return;
        }
        // 更新db信息为删除
        for (PodPO podPO : podPOs) {
            String accountID = podPO.getUserId();
            try {
                // deleted = 1, status = 'deleted'
                podDao.deletePod(accountID, podPO.getPodUuid());
                containerDao.deleteContainers(accountID, podPO.getPodId());
                containerDao.deleteContainers(accountID, podPO.getPodUuid());
            } catch (Exception e) {
                LOGGER.error("delete from logical failed, exception is {}", e);
            }
        }
    }

    public void duleLostBccPreemptEvent() {
        // 获取正在所有未被抢占的竞价实例，1h+5min
        List<PodPO> pods = podDao.listBidRunningPods(PreemptStatus.RUNNING.toString(),
                                                            podConfiguration.getBidProductType(),
                                                            podConfiguration.getBidProtectedPeriodMin() + 5);
        LOGGER.debug("start duleLostBccPreemptEvent ");
        if (pods == null || pods.isEmpty()) {
            return ;
        }
        List<String> bccInstanceIds = new ArrayList<>();
        for (PodPO podPO : pods) {
            bccInstanceIds.add(podPO.getBccInstanceId());
        }
        LOGGER.debug("duleLostBccPreemptEvent bccInstanceIds: " + bccInstanceIds);
        if (bccInstanceIds == null || bccInstanceIds.isEmpty()) {
            return;
        }
        // 获取bcc上存在的instanceid
        BccInstanceListRequest bccInstanceListRequest = new BccInstanceListRequest();
        bccInstanceListRequest.setServerUuids(bccInstanceIds);
        LOGGER.debug("bccInstanceListRequest: " + bccInstanceListRequest.toString());
        BCCClient bccClient = logicPodClientFactory.createBCCClient(bciAccountId); 
        BccInstanceListResponse bccInstanceListResponse = bccClient.getBccInstanceList(bccInstanceListRequest);
        LOGGER.debug("bccInstanceListResponse: " + bccInstanceListResponse.toString());

        List<String> existInstanceIds = new ArrayList<>();
        if (bccInstanceListResponse != null && bccInstanceListResponse.getResult() != null 
                && !bccInstanceListResponse.getResult().isEmpty()) {
            for (InstanceId instanceid : bccInstanceListResponse.getResult()) {
                existInstanceIds.add(instanceid.getInstanceId());
            }
        }
        LOGGER.debug("existInstanceIds: " + existInstanceIds);
        // 不存在直接删除
        List<String> podIds = new ArrayList<>();
        for (PodPO podPO : pods) {
            if (existInstanceIds.contains(podPO.getBccInstanceId())) {
                continue;
            }
            String podId = stopPod(podPO);
            if (podId != null) {
                podIds.add(podId);
            }
        }
        if (podIds.size() > 0) {
            podDao.batchUpdatePreemptedPod(podIds, BciStatus.SUCCEEDED.getStatus(), PreemptStatus.PREEMPTED.toString());
        }
        LOGGER.debug("duleLostBccPreemptEvent podIds: " + podIds);
    }
}
