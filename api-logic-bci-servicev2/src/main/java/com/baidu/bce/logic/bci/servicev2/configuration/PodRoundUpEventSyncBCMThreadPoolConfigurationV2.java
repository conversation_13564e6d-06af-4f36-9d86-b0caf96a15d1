package com.baidu.bce.logic.bci.servicev2.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class PodRoundUpEventSyncBCMThreadPoolConfigurationV2 {
    @Value("${pod.roundUpEvent.sync.bcm.thread.pool.core.size:50}")
    private Integer threadPoolCoreSize;
    @Value("${pod.roundUpEvent.sync.bcm.thread.pool.max.size:200}")
    private Integer threadPoolMaxSize;
    @Value("${pod.roundUpEvent.sync.bcm.thread.pool.queue.capacity:100000}")
    private Integer threadPoolQueueCapacity;
    @Value("${pod.roundUpEvent.sync.bcm.thread.keepalive.seconds:60}")
    private Integer threadKeepAliveSeconds;

    /**
     * {@literal}
     * 创建一个名为"podRoundUpEventSyncThreadPoolTaskExecutorV2"的线程池任务执行器，用于同步处理Pod事件到BCM。
     * 返回值是一个{@link ThreadPoolTaskExecutor}类型的对象。
     *
     * @return {@link ThreadPoolTaskExecutor}类型的对象，表示同步处理Pod事件到BCM的线程池任务执行器
     */
    @Bean(name = "podRoundUpEventSyncThreadPoolTaskExecutorV2")
    public ThreadPoolTaskExecutor podRoundUpEventSyncThreadPoolTaskExecutorV2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(threadPoolQueueCapacity);
        executor.setKeepAliveSeconds(threadKeepAliveSeconds);

        String threadNamePrefix = "BciV2-Pod-RoundupEventToBCM-Sync-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);
        return executor;
    }
}
