package com.baidu.bce.logic.bci.servicev2.util.userversioncheck;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;


public class VersionCheckException extends BceException {
    public VersionCheckException(String errorMessage, String errorCode) {
        super(errorMessage, HttpStatus.ERROR_PERMISSION_DENY, errorCode);
        setRequestId(LogicUserService.getRequestId());
    }

    public class VersionCheckErrorCode {
        public static final String PERMISSION_DENY = "PermissionDeny";
        public static final String INTERNAL_SERVER_ERROR = "InternalServerError";
    }
}

