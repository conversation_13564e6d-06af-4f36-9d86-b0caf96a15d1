package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.kubernetes.client.openapi.models.V1Affinity;
import io.kubernetes.client.openapi.models.V1NodeAffinity;
import io.kubernetes.client.openapi.models.V1PodAffinity;
import io.kubernetes.client.openapi.models.V1PodAntiAffinity;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class Affinity {

    private V1NodeAffinity nodeAffinity;
    private V1PodAffinity podAffinity;
    private V1PodAntiAffinity podAntiAffinity;

    public V1Affinity toV1Affinity() {
        V1Affinity v1Affinity = new V1Affinity();
        v1Affinity.setPodAntiAffinity(podAntiAffinity);
        v1Affinity.setPodAffinity(podAffinity);
        v1Affinity.setNodeAffinity(nodeAffinity);

        return v1Affinity;
    }
}
