package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.ResourceRecycleSyncServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("ResourceRecycleSyncSchedulerV2")
@Profile("default")
public class ResourceRecycleSyncSchedulerV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceRecycleSyncSchedulerV2.class);

    private String schedulerName = "ResourceRecycleSyncSchedulerV2.runScheduledTask";

    // 5s
    private static final int RESOURCE_RECYCLE_SYNC_PERIOD = 5000;

    @Autowired
    private ResourceRecycleSyncServiceV2 resourceRecycleSyncService;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    @Scheduled(fixedDelay = RESOURCE_RECYCLE_SYNC_PERIOD)
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        resourceRecycleSyncService.syncResourceRecycle();
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }
}
