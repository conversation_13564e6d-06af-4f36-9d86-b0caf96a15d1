package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.K8SResourceRecycleServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("k8SResourceRecycleSchedulerV2")
@Profile("default")
public class K8SResourceRecycleSchedulerV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(K8SResourceRecycleSchedulerV2.class);

    private static final int FIX_DELAY_ONE = 20000;

    private String schedulerName = "K8SResourceRecycleSchedulerV2.runScheduledTask";

    @Autowired
    private K8SResourceRecycleServiceV2 k8SResourceRecycleServiceV2;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    // @Scheduled(fixedRate = 60000) // 测试使用,60s一次
    @Scheduled(cron = "0 0 2 * * ?") // 每天2点执行一次
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        k8SResourceRecycleServiceV2.recycleK8SResource();
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }

}
