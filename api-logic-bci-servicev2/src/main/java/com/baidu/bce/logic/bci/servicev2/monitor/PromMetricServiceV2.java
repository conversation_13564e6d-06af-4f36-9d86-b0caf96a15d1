package com.baidu.bce.logic.bci.servicev2.monitor;

import com.baidu.bce.logic.bci.servicev2.model.MetricRspBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service("PromMetricServiceV2")
public class PromMetricServiceV2 {
    @Autowired
    private CPromClient cPromClient;

    public MetricRspBody getMetrics(List<String> podIds) {
        return cPromClient.getMetrics(podIds);
    }

    public MetricRspBody getMetricsForTopPod(List<String> podIds) {
        return cPromClient.getMetricsForTopPod(podIds);
    }

    // only used in ut test.
    public CPromClient getCPromClient() {
        return this.cPromClient;
    }
}
