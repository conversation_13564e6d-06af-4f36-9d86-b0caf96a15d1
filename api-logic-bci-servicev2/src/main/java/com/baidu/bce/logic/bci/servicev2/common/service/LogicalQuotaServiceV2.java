package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.internalsdk.trail.util.JsonUtil;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.QuotaKeys;
import com.baidu.bce.logic.bci.servicev2.model.BciQuota;
import com.baidu.bce.logic.bci.servicev2.model.BciQuotaSpecialPodSpec;
import com.baidu.bce.logic.bci.servicev2.model.BciStockInfo;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import com.baidu.bce.user.settings.sdk.model.QuotaBatchRequest;
import com.baidu.bce.user.settings.sdk.model.QuotaRequest;
import com.baidu.bce.user.settings.sdk.model.QuotaResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Service
public class LogicalQuotaServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogicalQuotaServiceV2.class);

    @Value("${bci.pod.sum.quota:20}")
    protected int podSumQuota;

    @Value("${bci.volume.quota:10}")
    protected int volumeRatio;

    @Value("${bci.nfs.quota:5}")
    protected int nfsRatio;

    @Value("${bci.emptyDir.quota:5}")
    protected int emptyDirRatio;

    @Value("${bci.configFile.quota:5}")
    protected int configFileRatio;

    @Value("${bci.port.quota:10}")
    protected int portRatio;

    @Value("${bci.env.quota:10}")
    protected int envRatio;

    @Value("${bci.datavolume.quota:10}")
    protected int dataVolumeQuota;

    @Value("${bci.pendingPod.sum.quota:100}")
    protected int pendingPodSumQuota;

    @Value("${bci.order.timeout.quota:30}")
    protected int orderTimeoutMinute;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    protected RegionConfiguration regionConfiguration;

    private String getAccountId() {
        return LogicUserService.getAccountId();
    }


    public BciQuota getBciQuota() {
        return getBciQuota(getAccountId());
    }

    /**
     * 批量获取用户配额
     *
     * @param accountId
     * @return
     */
    public BciQuota getBciQuota(String accountId) {
        int podSumQuotaNum = podSumQuota;
        int volumeRatioNum = volumeRatio;
        int nfsRatioNum = nfsRatio;
        int emptyDirRatioNum = emptyDirRatio;
        int configFileRatioNum = configFileRatio;
        int portRatioNum = portRatio;
        int envRatioNum = envRatio;
        int dataVolumeNum = dataVolumeQuota;
        int pendingPodSumQuotaNum = pendingPodSumQuota;
        int orderTimeoutMinuteNum = orderTimeoutMinute;
        try {
            LOGGER.info("query quota from resource, userId {}", accountId);
            QuotaBatchRequest quotaRequest = new QuotaBatchRequest();
            quotaRequest.setQuotaTypes(new ArrayList<>(Arrays.asList(QuotaKeys.POD,
                    QuotaKeys.VOLUME_RATIO, QuotaKeys.NFS_RATIO, QuotaKeys.EMPTY_DIR_RATIO, QuotaKeys.CONFIG_FILE_RATIO,
                    QuotaKeys.PORT_RATIO, QuotaKeys.ENV_RATIO, QuotaKeys.PENDING_POD, QuotaKeys.ORDER_TIMEOUT_MINUTE)));
            quotaRequest.setUserType(QuotaRequest.UserType.AccountId);
            quotaRequest.setUserValue(accountId);
            UserSettingsClient userSettingsClient = logicPodClientFactory.createUserSettingsClient(accountId);
            Map<String, String> quotaMap = userSettingsClient.getBatchQuota(quotaRequest).getQuotaType2quota();
            LOGGER.info("query quota from resource, userId {}, quotaMap {}", accountId, quotaMap);
            if (quotaMap.containsKey(QuotaKeys.POD)) {
                podSumQuotaNum = Integer.parseInt(quotaMap.get(QuotaKeys.POD));
            }
            if (quotaMap.containsKey(QuotaKeys.VOLUME_RATIO)) {
                volumeRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.VOLUME_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.NFS_RATIO)) {
                nfsRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.NFS_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.EMPTY_DIR_RATIO)) {
                emptyDirRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.EMPTY_DIR_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.CONFIG_FILE_RATIO)) {
                configFileRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.CONFIG_FILE_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.DATA_VOLUME_QUOTA)) {
                dataVolumeNum = Integer.parseInt(quotaMap.get(QuotaKeys.DATA_VOLUME_QUOTA));
            }
            if (quotaMap.containsKey(QuotaKeys.PORT_RATIO)) {
                portRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.PORT_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.ENV_RATIO)) {
                envRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.ENV_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.PENDING_POD)) {
                pendingPodSumQuotaNum = Integer.parseInt(quotaMap.get(QuotaKeys.PENDING_POD));
            }
            if (quotaMap.containsKey(QuotaKeys.ORDER_TIMEOUT_MINUTE)) {
                orderTimeoutMinuteNum = Integer.parseInt(quotaMap.get(QuotaKeys.ORDER_TIMEOUT_MINUTE));
            }
        } catch (Exception ex) {
            LOGGER.error("get quota messge from resource error: {}", ex);
        }

        // 统一计费的pod暂时不计入任何账户的 quota
        int podNum = podDao.getCreatedPodInQuota(accountId);

        BciQuota bciQuota = new BciQuota();
        bciQuota.setPodTotal(podSumQuotaNum);
        bciQuota.setVolumeRatio(volumeRatioNum);
        bciQuota.setEmptyDirRatio(emptyDirRatioNum);
        bciQuota.setNfsRatio(nfsRatioNum);
        bciQuota.setConfigFileRatio(configFileRatioNum);
        bciQuota.setEnvRatio(envRatioNum);
        bciQuota.setPodCreated(podNum);
        bciQuota.setPortRatio(portRatioNum);
        bciQuota.setDataVolumeQuota(dataVolumeNum);
        bciQuota.setPendingPodTotal(pendingPodSumQuotaNum);
        bciQuota.setOrderTimeoutMinute(orderTimeoutMinuteNum);
        return bciQuota;
    }

    /**
     * 获取用户制定配额
     *
     * @param accountId
     * @param quotaType
     * @param defaultQuotaNum
     * @return
     */
    public int getBciQuota(String accountId, String quotaType, int defaultQuotaNum) {
        int quotaNum = defaultQuotaNum;
        try {
            String region = regionConfiguration.getCurrentRegion();
            LOGGER.info("query quota from resource userId {}, region {}, quotaType {}",
                    accountId, region, quotaType);
            UserSettingsClient userSettingsClient = logicPodClientFactory.createUserSettingsClient(accountId);
            QuotaRequest quotaRequest = new QuotaRequest();
            quotaRequest.setQuotaType(quotaType);
            quotaRequest.setUserType(QuotaRequest.UserType.AccountId);
            quotaRequest.setUserValue(accountId);
            quotaRequest.setServiceType("bci");
            quotaRequest.setRegion(region);
            QuotaResponse quotaResponse = userSettingsClient.getQuota(quotaRequest);
            LOGGER.info("query quota from resource userId {}, region {}, quotaType {}, QuotaResponse {}",
                    accountId, region, quotaType, JsonUtil.toJson(quotaResponse));
            if (quotaResponse != null && quotaResponse.getQuota() != null) {
                quotaNum = Integer.parseInt(quotaResponse.getQuota());
            }
        } catch (Exception ex) {
            LOGGER.error("query quota from resource failed, userId {}, quotaType {}, error {}",
                    accountId, quotaType, ex);
        }
        return quotaNum;
    }

    /**
     * 获取用户制定配额
     *
     * @param accountId
     * @param quotaType
     * @return
     */
    public String getBciQuota(String accountId, String quotaType) {
        try {
            String region = regionConfiguration.getCurrentRegion();
            LOGGER.info("query string quota from resource userId {}, region {}, quotaType {}",
                    accountId, region, quotaType);
            UserSettingsClient userSettingsClient = logicPodClientFactory.createUserSettingsClient(accountId);
            QuotaRequest quotaRequest = new QuotaRequest();
            quotaRequest.setQuotaType(quotaType);
            quotaRequest.setUserType(QuotaRequest.UserType.AccountId);
            quotaRequest.setUserValue(accountId);
            quotaRequest.setServiceType("bci");
            quotaRequest.setRegion(region);
            QuotaResponse quotaResponse = userSettingsClient.getQuota(quotaRequest);
            LOGGER.info("query string quota from resource userId {}, region {}, quotaType {}, QuotaResponse {}",
                    accountId, region, quotaType, JsonUtil.toJson(quotaResponse));
            return quotaResponse.getQuota();
        } catch (Exception ex) {
            LOGGER.error("query string quota from resource failed, userId {}, quotaType {}, error {}",
                    accountId, quotaType, ex);
        }
        return null;
    }

    public List<BciQuotaSpecialPodSpec> getBciQuotaSpecialPodSpec(String accountId) {
        String specialPodSpecStr = getBciQuota(accountId, QuotaKeys.SPECIAL_POD_SPEC);
        if (Strings.isNullOrEmpty(specialPodSpecStr)) {
            return null;
        }
        List<BciQuotaSpecialPodSpec> specialPodSpec = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            specialPodSpec = objectMapper.readValue(specialPodSpecStr, new TypeReference<List<BciQuotaSpecialPodSpec>>() {});
        } catch (Exception e) {
            LOGGER.error("Unmarshal specialPodSpec {} err {} ", specialPodSpecStr, e);
            return specialPodSpec;
        }

        // 去除null
        if (CollectionUtils.isNotEmpty(specialPodSpec)) {
            for (int i = specialPodSpec.size() - 1; i >= 0; i--) {
                if (specialPodSpec.get(i) == null || specialPodSpec.get(i).getCpu() == null 
                    || specialPodSpec.get(i).getMemory() == null) {
                    specialPodSpec.remove(i);
                }
            }
        }
        LOGGER.debug("getBciQuotaSpecialPodSpec valid result: {}", specialPodSpec);
        return specialPodSpec;
    }

    public Map<String,Map<String,Integer>>  getBciBccQuota(String accountId) {
        String bccQuotaStr = getBciQuota(accountId, QuotaKeys.BCC_QUOTA);
        if (Strings.isNullOrEmpty(bccQuotaStr)) {
            return null;
        }
        
        Map<String, Map<String, Integer>> bccQuota = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            bccQuota = objectMapper.readValue(bccQuotaStr, HashMap.class);
        } catch (Exception e) {
            LOGGER.error("Unmarshal getBciBccQuota {} err {} ", bccQuotaStr, e);
            return bccQuota;
        }
        LOGGER.debug("getBciBccQuota valid result: {}", bccQuota);
        return bccQuota;
    }

    // 默认满足请求的 客户id+类型+规格+可用区+时间段 配额条目只可能 <=  1
    public BciStockInfo getBciStockSupply(BciStockInfo bciStockInfoReq) {
        BciStockInfo bciStockInfoSupply = new BciStockInfo();
        try {
            String userId = bciStockInfoReq.getUserId();
            String type = bciStockInfoReq.getType();
            String spec = bciStockInfoReq.getSpec();
            String region = bciStockInfoReq.getRegion();
            LocalDateTime startTimeReq = convertDatetimeToLocalDateTime(bciStockInfoReq.getStartTime());
            LocalDateTime endTimeReq = convertDatetimeToLocalDateTime(bciStockInfoReq.getEndTime());
            String az = bciStockInfoReq.getAz();
            String stockName = "";
            if (bciStockInfoReq.getScope().equals(LogicalConstant.ReservedInstanceScope.REGION)) {
                stockName = String.format("%s-%s-%s", type, spec, region);
            } else if (bciStockInfoReq.getScope().equals(LogicalConstant.ReservedInstanceScope.AZ)) {
                stockName = String.format("%s-%s-%s", type, spec, az);
            } else {
                return bciStockInfoSupply;
            }
            LOGGER.info("query stock from resource, userId {}", userId);
            QuotaBatchRequest quotaRequest = new QuotaBatchRequest();
            quotaRequest.setQuotaTypes(new ArrayList<>(Arrays.asList(QuotaKeys.STOCK)));
            quotaRequest.setUserType(QuotaRequest.UserType.AccountId);
            quotaRequest.setUserValue(userId);
            UserSettingsClient userSettingsClient = logicPodClientFactory.createUserSettingsClient(userId);
            Map<String, String> quotaMap = userSettingsClient.getBatchQuota(quotaRequest).getQuotaType2quota();
            if (quotaMap.containsKey(QuotaKeys.STOCK)) {
                String stockInfoJson = quotaMap.get(QuotaKeys.STOCK);
                Map<String, Map<String, String>> stockInfo = JsonUtil.fromJson(stockInfoJson, Map.class);
                // 若配额不包含类型-规格-可用区的数据，则直接返回配额为空对象
                if (!stockInfo.containsKey(stockName)) {
                    return bciStockInfoSupply;
                }
                Map<String, String> timeIntervalMap = stockInfo.get(stockName);
                for (String timeInterval : timeIntervalMap.keySet()) {
                    List<String> timeIntervalList = Arrays.asList(timeInterval.split(","));
                    LocalDateTime startTimeSupply = convertDatetimeToLocalDateTime(timeIntervalList.get(0));
                    LocalDateTime endTimeSupply = convertDatetimeToLocalDateTime(timeIntervalList.get(1));
                    // 预留实例券供需匹配仅匹配日期，不再匹配具体时间
                    if (startTimeSupply.toLocalDate().compareTo(startTimeReq.toLocalDate()) == 0 &&
                            endTimeSupply.toLocalDate().compareTo(endTimeReq.toLocalDate()) == 0) {
                        bciStockInfoSupply.setStartTime(timeIntervalList.get(0));
                        bciStockInfoSupply.setEndTime(timeIntervalList.get(1));
                        bciStockInfoSupply.setCount(Integer.valueOf(timeIntervalMap.get(timeInterval)));
                        return bciStockInfoSupply;
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.error("get stock message from resource error: {}", ex);
        }
        return bciStockInfoSupply;
    }

    public LocalDateTime convertDatetimeToLocalDateTime(String datetimeStr) {
        // 兼容之前含时间的配置
        DateTimeFormatter datetimeFormatter =  new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd[ HH:mm:ss]")
            .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
            .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
            .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
            .toFormatter();
        LocalDateTime localDatetime;
        datetimeStr = datetimeStr.trim();
        localDatetime = LocalDateTime.parse(datetimeStr, datetimeFormatter);
        return localDatetime;
    }
}
