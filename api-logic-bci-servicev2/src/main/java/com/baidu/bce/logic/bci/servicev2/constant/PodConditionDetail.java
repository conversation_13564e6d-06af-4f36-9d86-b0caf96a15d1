package com.baidu.bce.logic.bci.servicev2.constant;

public enum PodConditionDetail {

    NO_STOCK_CONDITION(PodConditionDetail.NO_STOCK, "Create BCI failed because the specified instance is out of stock");
    private String reason;
    private String message;

    public static final String NO_STOCK = "NoStock";


    PodConditionDetail(String reason, String message) {
        this.reason = reason;
        this.message = message;
    }

    public String getReason() {
        return reason;
    }

    public String getMessage() {
        return message;
    }
}
