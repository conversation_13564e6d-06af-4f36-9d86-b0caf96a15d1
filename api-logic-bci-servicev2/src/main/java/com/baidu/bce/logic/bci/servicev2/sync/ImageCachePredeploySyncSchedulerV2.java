package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.ImageCachePredeploySyncServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("ImageCachePredeploySyncSchedulerV2")
@Profile("default")
public class ImageCachePredeploySyncSchedulerV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageAccelerateSyncSchedulerV2.class);

    private String runImageCachePredeploySchedulerName = "ImageCachePredeploySyncSchedulerV2.runImageCachePredeploy";

    /* 镜像预铺时间间隔:5分钟 */
    private static final int IMAGE_CACHE_PREDEPLOY_PERIOD_MS = 1000 * 60 * 5;

    @Autowired
    private ImageCachePredeploySyncServiceV2 imageCachePredeploySyncServiceV2;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(runImageCachePredeploySchedulerName);
    }

    @Scheduled(fixedRate = IMAGE_CACHE_PREDEPLOY_PERIOD_MS)
    public void runImageCachePredeploy() {
        schedulerStatistics.beforeSchedulerRun(runImageCachePredeploySchedulerName);
        imageCachePredeploySyncServiceV2.syncImageCachePredeploy();
        schedulerStatistics.afterSchedulerRun(runImageCachePredeploySchedulerName);
    }
}
