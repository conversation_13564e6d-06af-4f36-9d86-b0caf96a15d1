package com.baidu.bce.logic.bci.servicev2.constant;

public enum ImageCacheSnapshotterType {
    CDS("cds", "cds类型"),
    STARGZ("stargz", "stargz类型");

    private String name;
    private String description;

    ImageCacheSnapshotterType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public static String getOwnerByName(String name) {
        for (ImageCacheSnapshotterType imageCacheSnapshotterType : values()) {
            if (imageCacheSnapshotterType.getName().equalsIgnoreCase(name)) {
                return imageCacheSnapshotterType.getDescription();
            }
        }
        return name;
    }
}