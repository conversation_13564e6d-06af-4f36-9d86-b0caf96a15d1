package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.BciSubStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.DeleteInstanceContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("DELETE_INSTANCE")
public class DeleteInstanceHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(DeleteInstanceHandler.class);
    private static final List<String> ALLOWED_STATUS = Arrays.asList(
            BciStatus.PENDING.getStatus(),
            BciStatus.RUNNING.getStatus(),
            BciStatus.RESCHEDULING.getStatus(),
            BciStatus.FAILED.getStatus(),
            BciStatus.SUCCEEDED.getStatus()
    );

    private static final List<String> ALLOWED_RESCHEDULING_SUB_STATUS = Arrays.asList(
            BciSubStatus.RESCHEDULING_TOBEDELETED.getSubStatus(),
            BciSubStatus.RESCHEDULING_DELETED.getSubStatus(),
            BciSubStatus.RESCHEDULING_CREATING.getSubStatus()
    );

    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof DeleteInstanceContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        StateMachineContext context = getContext();
        PodPO podPO = context.getPodPO();
        if (!ALLOWED_STATUS.contains(podPO.getStatus())) {
            return false;
        }
        if (BciStatus.RESCHEDULING.getStatus().equalsIgnoreCase(podPO.getStatus())) {
            if (!ALLOWED_RESCHEDULING_SUB_STATUS.contains(podPO.getSubStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 1. 更新pod status=deleted, deleted=1
     * 2. 更新pod的资源回收时间戳和原因 rrt and rrr
     * 3. 更新pod的bci资源版本号,resource_version and bci_resource_version
     * @return
     */
    @Override
    public boolean execute() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        PodPO podPO = getPodPO();
        podPO.setStatus(BciStatus.DELETED.getStatus());
        podPO.setSubStatus(BciSubStatus.STATUS_DEFAULT);
        if (podPO.getResourceRecycleTimestamp() == 0L) {
            podPO.setResourceRecycleTimestamp(System.currentTimeMillis());
            podPO.setResourceRecycleReason(ResourceRecycleReason.USER_DELETE_POD.toString());
        }
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        int deletePodResult = podDao.stateMachineDeletePod(podPO, podDaoContext);
        podExtraDao.deletePodExtra(podPO.getUserId(), podPO.getPodId());
        if (deletePodResult == 0) {
            return false;
        }
        return true;
    }
}
