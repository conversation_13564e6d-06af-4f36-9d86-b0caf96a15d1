package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.logic.bci.servicev2.common.service.LogicalQuotaServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.QuotaKeys;
import com.baidu.bce.logic.bci.servicev2.model.HolidayItem;
import com.baidu.bce.logic.bci.servicev2.model.HolidayResp;
import com.baidu.bce.logic.bci.servicev2.model.TidalTime;
import com.baidu.bce.logic.bci.servicev2.monitor.SimpleHttpClient;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

@Service("tidalValidator")
public class TidalValidator implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(TidalValidator.class);

    private List<String> globalTimeOffDayList = new ArrayList<>();
    private List<String> globalHolidayDayList = new ArrayList<>();

    @Autowired
    private LogicalQuotaServiceV2 logicalQuotaServiceV2;

    @Value("${bci.tidal.pod.startsubmit.time:23:30}")
    private String tidalPodSubmitTime;

    @Value("${bci.tidal.pod.startgc.time:6:30}")
    private String tidalPodStartGcTime;

    @Value("${bci.apigo.appkey:apiGoAk-683d1e06ffff478aba5d8a132c4c9d49-online}")
    private String apiGoAppKey;

    @Value("${bci.apigo.secretkey:apiGoSk-ee15560388024120bf2333b34315cd90-online}")
    private String apiGoSecretKey;

    /**
     * 是否在潮汐运行时间段内
     *
     * @return
     */
    public boolean inTidalRunTime(TidalTime tidalTime) {
        // 1、先判断是否在夜间潮汐
        if (inTidalPodNightTime(tidalTime)) {
            return true;
        }
        // 2、判断是否在节假日潮汐
        boolean inHolidayDay = dayListContainsOneDay(tidalTime.getHolidayDayList(), tidalTime.getCurrentYear(),
                tidalTime.getCurrentMonth(),
                tidalTime.getCurrentDay());
        if (inHolidayDay) {
            return true;
        }
        // 3、判断是否在周末
        if (!inWeekend(tidalTime)) {
            return false;
        }
        // 4、说明在周末，判断周末是否调休
        boolean inTimeOffDay = dayListContainsOneDay(tidalTime.getTimeOffDayList(), tidalTime.getCurrentYear(),
                tidalTime.getCurrentMonth(),
                tidalTime.getCurrentDay());
        if (inTimeOffDay) {
            return false;
        }
        return true;
    }

    public TidalTime buildCurrentTidalTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

        TidalTime result = new TidalTime();
        result.setCurrentYear(calendar.get(Calendar.YEAR));
        result.setCurrentMonth(calendar.get(Calendar.MONTH) + 1);
        result.setCurrentDay(calendar.get(Calendar.DAY_OF_MONTH));
        result.setCurrentHour(calendar.get(Calendar.HOUR_OF_DAY));
        result.setCurrentMinute(calendar.get(Calendar.MINUTE));
        result.setTimeOffDayList(this.globalTimeOffDayList);
        result.setHolidayDayList(this.globalHolidayDayList);

        try {
            String tidalParam = logicalQuotaServiceV2.getBciQuota(getAccountId(), QuotaKeys.TIDAL_PARAM);
            TidalTime tidalQuota = JsonUtil.fromJSON(tidalParam, TidalTime.class);
            if (tidalQuota == null) {
                result.setTidalStartSubmitTime(tidalPodSubmitTime);
                result.setTidalEndSubmitTime(tidalPodStartGcTime);
            } else {
                result.setTidalStartSubmitTime(tidalQuota.getTidalStartSubmitTime());
                result.setTidalEndSubmitTime(tidalQuota.getTidalEndSubmitTime());
            }
        } catch (Exception e) {
            LOGGER.error("get tidal quota error {}", e.getMessage());
            result.setTidalStartSubmitTime(tidalPodSubmitTime);
            result.setTidalEndSubmitTime(tidalPodStartGcTime);
        }
        return result;
    }

    private boolean inWeekend(TidalTime tidalTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        calendar.set(Calendar.YEAR, tidalTime.getCurrentYear());
        // 月份-1
        calendar.set(Calendar.MONTH, tidalTime.getCurrentMonth() - 1);
        calendar.set(Calendar.DAY_OF_MONTH, tidalTime.getCurrentDay());

        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY;
    }

    private boolean inTidalPodNightTime(TidalTime tidalTime) {
        String tidalNightStartTime = tidalTime.getTidalStartSubmitTime();
        String tidalNightEndTime = tidalTime.getTidalEndSubmitTime();

        String[] startTimeSplit = tidalNightStartTime.split(":");
        String[] endTimeSplit = tidalNightEndTime.split(":");

        if (startTimeSplit.length != 2 || endTimeSplit.length != 2) {
            LOGGER.error("tidal night time format error tidalNightStartTime {} tidalNightEndTime {} ",
                    tidalNightStartTime, tidalNightEndTime);
            return false;
        }
        try {
            int startHour = Integer.parseInt(startTimeSplit[0]);
            int startMinute = Integer.parseInt(startTimeSplit[1]);

            int endHour = Integer.parseInt(endTimeSplit[0]);
            int endMinute = Integer.parseInt(endTimeSplit[1]);

            int currentDayMinute = tidalTime.getCurrentHour() * 60 + tidalTime.getCurrentMinute();
            int startDayMinute = startHour * 60 + startMinute;
            int endDayMinute = endHour * 60 + endMinute;
            if (startHour > endHour) {
                return currentDayMinute >= startDayMinute || currentDayMinute <= endDayMinute;
            }

            return currentDayMinute >= startDayMinute && currentDayMinute <= endDayMinute;
        } catch (NumberFormatException e) {
            LOGGER.error("tidal night time format error tidalNightStartTime {} tidalNightEndTime {} ",
                    tidalNightStartTime, tidalNightEndTime);
        }
        return false;
    }

    // 日期格式如下: 2023-01-01
    private boolean dayListContainsOneDay(List<String> dayList, int year, int month, int day) {
        if (CollectionUtils.isEmpty(dayList)) {
            return false;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        for (String dayStr : dayList) {
            try {
                Date parseDate = dateFormat.parse(dayStr);
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                calendar.setTime(parseDate);
                int parseYear = calendar.get(Calendar.YEAR);
                int parseMonth = calendar.get(Calendar.MONTH) + 1;
                int parseDay = calendar.get(Calendar.DAY_OF_MONTH);
                if (parseYear == year && parseMonth == month && parseDay == day) {
                    return true;
                }
            } catch (ParseException e) {
                LOGGER.error("dayListContainsOneDay parse day string error, dayStr={}", dayStr, e);
            }
        }
        return false;
    }

    /**
     * 调用apigo 获取节假日和调休信息
     */
    private void getHolidays() {
        String url = "https://apigo.baidu-int.com/open/api/holiday/getHolidayByYear";
        String uri = "/open/api/holiday/getHolidayByYear";
        Map<String, String> params = new HashMap<>();
        params.put("year", getCurrentYear() + "");
        try {
            LOGGER.debug("getHolidays start ...");

            SimpleHttpClient httpClient = new SimpleHttpClient();
            Map<String, String> headerMap = ApiGoUtil.buildHmacSignHeader(apiGoAppKey, apiGoSecretKey, uri, "POST",
                    new ArrayList<>(), false);
            String resp = httpClient.callPostWithFormUrlEncoded(url, headerMap, params, 3000);

            HolidayResp holidayResp = JsonUtil.fromJSON(resp, HolidayResp.class);
            if (holidayResp == null) {
                LOGGER.error("getHolidays resp is null");
                return;
            }

            if (holidayResp.getCode() != 200) {
                LOGGER.error("getHolidays resp code is not 200 err msg {} ", holidayResp.getMsg());
                return;
            }

            // 调休
            List<String> timeOffDayList = new ArrayList<>();
            // 节假日
            List<String> holidayDayList = new ArrayList<>();

            if (CollectionUtils.isEmpty(holidayResp.getResult())) {
                LOGGER.error("getHolidays resp result is empty ");
                return;
            }

            for (HolidayItem item : holidayResp.getResult()) {
                if (item.getHolidayDaysName() == null) {
                    continue;
                }
                if (item.getHolidayDaysName().contains("工作日") ||
                        item.getHolidayDaysName().contains("1_")) {
                    timeOffDayList.add(item.getHolidayDate());
                    continue;
                }
                holidayDayList.add(item.getHolidayDate());
            }
            this.globalHolidayDayList = holidayDayList;
            this.globalTimeOffDayList = timeOffDayList;
            LOGGER.debug("globalHolidayDayList {} ", globalHolidayDayList);
            LOGGER.debug("globalTimeOffDayList {} ", globalTimeOffDayList);
        } catch (Exception e) {
            LOGGER.error("getHolidays err {} ", e);
        }
    }

    private int getCurrentYear() {
        Calendar now = Calendar.getInstance();
        now.setTime(new Date());
        now.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return now.get(Calendar.YEAR);
    }

    private String getAccountId() {
        return LogicUserService.getAccountId();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        this.getHolidays();
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("holidays-thread-%d").build();
        new ScheduledThreadPoolExecutor(1, threadFactory).scheduleAtFixedRate((new Runnable() {
            @Override
            public void run() {
                try {
                    TidalValidator.this.getHolidays();
                } catch (Exception e) {
                    LOGGER.error("getHolidays err {}", e);
                }
            }
        }), 0, 30, TimeUnit.SECONDS);
    }
}
