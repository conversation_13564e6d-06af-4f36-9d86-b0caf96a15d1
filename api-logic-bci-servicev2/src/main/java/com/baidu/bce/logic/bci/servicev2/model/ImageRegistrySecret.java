package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ImageRegistrySecret {
    private String server;
    private String userName;
    private String password;

    public ImageRegistrySecret() {
    }

    public ImageRegistrySecret(ImageRegistryCredential image) {
        this.setServer(image.getServer());
        this.setUserName(image.getUserName());
        this.setPassword(image.getPassword());
    }
}
