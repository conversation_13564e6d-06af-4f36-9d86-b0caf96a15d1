package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SecurityGroupSimpleInstancesVO;
import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.request.SubnetMapRequest;
import com.baidu.bce.externalsdk.logical.network.subnet.model.response.SubnetMapResponse;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.BelongSameVpcRequest;
import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.constant.EipConstant;
import com.baidu.bce.internalsdk.eipv2.model.EipListResponse;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PodVolumeType;
import com.baidu.bce.logic.bci.servicev2.constant.PreviewPodCapacityConstant;
import com.baidu.bce.logic.bci.servicev2.constant.WhiteListKey;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.interceptor.ResourceAccountSetting;
import com.baidu.bce.logic.bci.servicev2.model.BaseVolume;
import com.baidu.bce.logic.bci.servicev2.model.BciQuota;
import com.baidu.bce.logic.bci.servicev2.model.Bos;
import com.baidu.bce.logic.bci.servicev2.model.Capabilities;
import com.baidu.bce.logic.bci.servicev2.model.CephFSVolume;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.model.ContainerSecurityContext;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.DNSConfig;
import com.baidu.bce.logic.bci.servicev2.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.EmptyDir;
import com.baidu.bce.logic.bci.servicev2.model.Environment;
import com.baidu.bce.logic.bci.servicev2.model.ExecAction;
import com.baidu.bce.logic.bci.servicev2.model.FlexVolume;
import com.baidu.bce.logic.bci.servicev2.model.GRPCAction;
import com.baidu.bce.logic.bci.servicev2.model.HTTPGetAction;
import com.baidu.bce.logic.bci.servicev2.model.HostPathVolume;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.ImageRegistrySecret;
import com.baidu.bce.logic.bci.servicev2.model.Nfs;
import com.baidu.bce.logic.bci.servicev2.model.Pfs;
import com.baidu.bce.logic.bci.servicev2.model.PodCDSVolumeType;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.Probe;
import com.baidu.bce.logic.bci.servicev2.model.TCPSocketAction;
import com.baidu.bce.logic.bci.servicev2.model.TidalTime;
import com.baidu.bce.logic.bci.servicev2.model.Volume;
import com.baidu.bce.logic.bci.servicev2.model.VolumeMounts;
import com.baidu.bce.logic.bci.servicev2.model.ZoneSubnets;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.pod.PodLogService;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.permission.PermissionExceptionUtil;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.kubernetes.client.openapi.models.V1EnvVarSource;
import io.kubernetes.client.openapi.models.V1ObjectFieldSelector;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hsqldb.lib.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service("PodValidatorV2")
public class PodValidator {
    private static final Logger LOGGER = LoggerFactory.getLogger(PodValidator.class);

    public static final String PATTERN_NAME;
    public static final String SEARCH_VALUE;

    static {
        PATTERN_NAME = "^(?!^\\d)(?!^[-_/.])[\u4e00-\u9fa5a-zA-Z\\d-_/.]{1,256}$";
        SEARCH_VALUE = "[\u4e00-\u9fa5a-zA-Z\\d-_/.]{1,256}";
    }

    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private PodLogService podLogService;

    @Autowired
    private TidalValidator tidalValidator;

    @Value("${bce.enable.debug:false}")
    private boolean debug;

    @Value("${image.address.need.validate:false}")
    private boolean imageAddressNeedValidate;

    @Value("${bci.cce.vpc.cidr:}")
    private String bciVpcCidr;

    @Value("${bci.cce.clusterIP.cidr:}")
    private String bciClusterCidr;

    @Value("${bci.container.securityContext.enable.users:152878f8ea364ddb8de7105ea7d6dd50," +
            "eca97e148cb74e9683d7b7240829d1ff,2e1be1eb99e946c3a543ec5a4eaa7d39}")
    private String bciContainerSecurityContextEnableUsers;

    @Value("${bci.container.securityContext.privileged.enable.users:6c47a952db4444c5a097b41be3f24c94}")
    private String bciContainerSecurityContextPrivilegedEnableUsers;

    /**
     * 默认超时时间 60分钟
     **/
    private static final long SUBNET_SECURITY_GROUP_IN_SAME_VPC_MAP_DEFAULT_EXPIRATION_TIME = 60 * 1000L * 60;

    private CacheUtil<BelongSameVpcRequest, Boolean> subnetSecurityGroupInSameVpcMap
            = new CacheUtil<>(SUBNET_SECURITY_GROUP_IN_SAME_VPC_MAP_DEFAULT_EXPIRATION_TIME);

    /**
     * 默认超时时间 10分钟
     **/
    private static final long SUBNET_MAP_DEFAULT_EXPIRATION_TIME = 60 * 1000L * 10;

    private CacheUtil<SubnetMapRequest, SubnetMapResponse> subnetMapCache = new CacheUtil<>(SUBNET_MAP_DEFAULT_EXPIRATION_TIME);


    public void validateCreateBCIServiceType(BaseCreateOrderRequestVo<IOrderItem> request) {
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : request.getItems()) {
            if (!"BCI".equalsIgnoreCase(item.getConfig().getServiceType())
                    && !ServiceType.EIP.name().equalsIgnoreCase(item.getConfig().getServiceType())) {
                throw new PodExceptions.RequestInvalidException(String.format("Not support serviceType:%s",
                        item.getConfig().getServiceType()));
            }
        }
    }


    private static boolean checkValue(String name, int value, int max) {
        if (value <= 0 || value > max) {
            return false;
        }
        return true;
    }

    private static boolean checkValue2(int value, int max) {
        if (value < 0 || value > max) {
            return false;
        }
        return true;
    }

    public void validateAutoRenewTime(String renewTimeUnit, int renewTime) {
        if (renewTime < 0) {
            throw new PodExceptions.InvalidAutoRenewTimeException();
        } else if (renewTime == 0) {
            // 0表示不开通自动续费
            return;
        }
        if (PodConstants.RENEW_TIME_UNIT_MONTH.equalsIgnoreCase(renewTimeUnit)) {
            if (!checkValue("autoRenewTime", renewTime, PodConstants.RENEW_MAX_MONTH)) {
                String message = "When the autoRenewTimeUnit is month, the autoRenewTime should be set between 1-9.";
                throw new PodExceptions.EipAutoRenewTimeInvalidException(message);
            }
        } else if (PodConstants.RENEW_TIME_UNIT_YEAR.equalsIgnoreCase(renewTimeUnit)) {
            if (!checkValue("autoRenewTime", renewTime, PodConstants.RENEW_MAX_YEAR)) {
                String message = "When the autoRenewTimeUnit is year, the autoRenewTime should be set between 1-3.";
                throw new PodExceptions.EipAutoRenewTimeInvalidException(message);
            }
        } else {
            throw new PodExceptions.InvalidAutoRenewTimeUnitException();
        }
    }

    public void validateEipBandwidthInMbps(EipPurchaseRequest eipPurchaseRequest) {
        int bandwidthInMbps = eipPurchaseRequest.getBandwidthInMbps();
        if (bandwidthInMbps <= 0) {
            throw new PodExceptions.EipBandwidthInMbpsInvalidException(
                    "Eip bandwidth is invalid.");
        }
        String billingMethod = eipPurchaseRequest.getSubProductType();
        String routeType = eipPurchaseRequest.getRouteType();
        if (StringUtils.isEmpty(routeType)) {
            // default route type: bgp
            routeType = LogicalConstant.EipRouteType.BGP;
        }
        String productType = eipPurchaseRequest.getProductType();
        if (LogicalConstant.EipSubProductType.NETRAFFIC.equalsIgnoreCase(billingMethod)) {
            if (LogicalConstant.EipRouteType.BGP.equalsIgnoreCase(routeType)) {
                if (bandwidthInMbps > 200 || !LogicalConstant.EipProductType.POSTPAY.equals(productType)) {
                throw new PodExceptions.EipBandwidthInMbpsInvalidException(
                        "ByTraffic only support BGP Postpaid and bandwidthInMbps less than 200.");
                }
            } else if (LogicalConstant.EipRouteType.BGP_S.equalsIgnoreCase(routeType)) {
                if (!LogicalConstant.EipProductType.POSTPAY.equals(productType) || bandwidthInMbps > 1000 ||  bandwidthInMbps < 100 )  {
                    throw new PodExceptions.EipBandwidthInMbpsInvalidException("ByTraffic only support BGP_S Postpaid and " +
                            "bandwidthInMbps in 100~1000.");
                }
            }
        }
        int maxBandWidth = 0;
        int minBandWidth = 1;
        if (LogicalConstant.EipSubProductType.BANDWIDTH.equalsIgnoreCase(billingMethod)) {
            if (LogicalConstant.EipRouteType.BGP.equalsIgnoreCase(routeType)) {
                maxBandWidth = 500;
            } else if (LogicalConstant.EipRouteType.BGP_S.equalsIgnoreCase(routeType)) {
                minBandWidth = 100;
                maxBandWidth = 5000;
            } else if (LogicalConstant.EipRouteType.CMC.equalsIgnoreCase(routeType) ||
                    LogicalConstant.EipRouteType.CUC.equalsIgnoreCase(routeType) ||
                    LogicalConstant.EipRouteType.CTC.equalsIgnoreCase(routeType)) {
                maxBandWidth = 5000;
            } else {
                throw new PodExceptions.EipRouteTypeInvalidException("Eip routetype is invalid.");
            }

            if (bandwidthInMbps < minBandWidth || bandwidthInMbps > maxBandWidth) {
                throw new PodExceptions.EipBandwidthInMbpsInvalidException("Eip bandwidth is invalid.");
            }
        }
    }

    public void validateEipBlackList(List<String> whiteList) {
        if (whiteList.contains(WhiteListKey.EIP_BLACK_LIST)) {
            throw new PodExceptions.EipOperationDeniedException();
        }
    }

    // check user-specified eip
    public void validateEipStatus(EipPurchaseRequest eipPurchaseRequest) {
        // check db
        List<PodPO> podPOS = podDao.getPodsByEip(getAccountId(), eipPurchaseRequest.getEipIp(),
                EipConstant.STATUS_USER_SPECIFIED);
        if (!podPOS.isEmpty()) {
            String msg = "Eip already be in use by " + podPOS.get(0).getPodId() + " " + podPOS.get(0).getInternalIp();
            throw new PodExceptions.EipAreadyInUseException(msg);
        }

        // eip status should be available
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
        EipListResponse eipListResponse = logicEipClient.queryEip(eipPurchaseRequest.getEipIp());
        if (eipListResponse == null || eipListResponse.getEipList().isEmpty() ||
                !eipListResponse.getEipList().get(0).getStatus().equals("available")) {
            throw new PodExceptions.EipNotAvailableException("Eip status should be available.");
        }
        eipPurchaseRequest.setBandwidthInMbps(eipListResponse.getEipList().get(0).getBandwidthInMbps());
    }
    public void validateBciParameters(PodPurchaseRequest podPurchaseRequest, BciQuota bciQuota) {
        validateCreateBciParams(podPurchaseRequest);
        validateCreateBciFlowControl(bciQuota);
        // 统一计费的资源不计入用户的quota，因为created pod 的数量不好统计，暂时不做quota限制。
        if (!ResourceAccountSetting.isUnifiedCharge()) {
            validateQuotaLimit(podPurchaseRequest, bciQuota);
        }
    }

    public void validateCreateBciFlowControl(BciQuota bciQuota) {
        if (bciQuota == null) {
            return;
        }
        int pendingPodCount = podService.getPendingPodCountByUserId();
        if (pendingPodCount > bciQuota.getPendingPodTotal()) {
            LOGGER.error("Pending pod quota exceeded limit, userId:{} pendingPodCount:{} quota:{}",
                    getAccountId(), pendingPodCount, bciQuota.getPendingPodTotal());
            throw new PodExceptions.PendingPodQuotaExceededLimit();
        }
    }

    public void validateRepository(PodPurchaseRequest podPurchaseRequest) {
        if (podPurchaseRequest == null || podPurchaseRequest.getContainerPurchases() == null) {
            return;
        }
        validateContainerTypeImageWorkingdirCommandsProbeAndEnv(podPurchaseRequest.getContainerPurchases(), true);
        // 校验dns配置
        validatePodDnsConfig(podPurchaseRequest);
        // 校验coredump配置
        validateCoredumpConfig(podPurchaseRequest);
        if (podPurchaseRequest.isTidal()) {
            validateTidalPodSubmitTime(podPurchaseRequest.getName());
        }
    }

    public void validateContainerTypeImageWorkingdirCommandsProbeAndEnv(List<ContainerPurchase> containers,
                                                                        boolean checkWorkloadContainerExist) {
        // 业务容器数量
        int workloadContainerCount = 0;
        for (ContainerPurchase container : containers) {
            String imageAddress = container.getImageAddress();
            // 校验是否为合法地址
            if (imageAddressNeedValidate && imageAddress.contains("baidubce") &&
                    !Pattern.matches(podConfiguration.getImageRepoPattern(), imageAddress)) {
                throw new PodExceptions.ContainerImageAddressException(imageAddress);
            }

            // 校验containerType
            String containerType = container.getContainerType();
            if ("".equals(containerType) || containerType == null) {
                container.setContainerType(ContainerType.WORKLOAD.getType());
            } else if (!(ContainerType.WORKLOAD.getType().equals(containerType)
                    || ContainerType.INIT.getType().equals(containerType)
                    || ContainerType.DS_WORKLOAD.getType().equals(containerType))) {
                throw new PodExceptions.ContainerTypeException(container.getName());
            }

            if (ContainerType.WORKLOAD.getType().equals(container.getContainerType())) {
                workloadContainerCount++;
            }
            // 校验imagePullPolicy
            if (!StringUtil.isEmpty(container.getImagePullPolicy())) {
                if (!"Always".equals(container.getImagePullPolicy())
                        && !"IfNotPresent".equals(container.getImagePullPolicy())
                        && !"Never".equals(container.getImagePullPolicy())) {
                    throw new PodExceptions.ContainerImagePullPolicyInvalid("The container imagePullPolicy " +
                            "is invalid. Only Always/IfNotPresent/Never are supported.");
                }
            }
            // 校验imageName
            if (StringUtils.isNotEmpty(container.getImageName())) {
                if (!checkValue2(container.getImageName().length(), 1024)) {
                    String message = "The container imageName is invalid, " +
                            "a valid imageName length must be between 1 and 1024.";
                    throw new PodExceptions.ContainerImageNameException(message);
                }
            }
            // 校验imageVersion
            if (StringUtils.isNotEmpty(container.getImageVersion())) {
                if (!checkValue2(container.getImageVersion().length(), 1024)) {
                    String message = "The container imageVersion is invalid, " +
                            "a valid imageVersion length must be between 1 and 1024.";
                    throw new PodExceptions.ContainerImageVersionException(message);
                }
            }
            // 校验imageAddress
            if (StringUtils.isNotEmpty(container.getImageAddress())) {
                if (!checkValue2(container.getImageAddress().length(), 1024)) {
                    throw new PodExceptions.ContainerImageAddressException();
                }
            }
            // 校验workingDir
            if (StringUtils.isNotEmpty(container.getWorkingDir())) {
                if (!checkValue2(container.getWorkingDir().length(), 1024)) {
                    String message = "The container workingDir is invalid, " +
                            "a valid workingDir length must be between 1 and 1024.";
                    throw new PodExceptions.ContainerWorkingDirException(message);
                }
            }
            // 校验commands
            if (container.getCommands() != null) {
                String command = JsonUtil.toJSON(container.getCommands());
                if (!checkValue2(command.length(), 4096)) {
                    String message = "The container commands is invalid, " +
                            "a valid commands length must be between 1 and 4096.";
                    throw new PodExceptions.ContainerCommandsException(message);
                }
            }
            // 校验探针
            validateContainerProbe(container.getLivenessProbe());
            validateContainerProbe(container.getReadinessProbe());
            validateContainerProbe(container.getStartupProbe());

            // 校验env
            validateContainerEnv(container.getEnvs());
        }
        if (checkWorkloadContainerExist && workloadContainerCount == 0) {
            // 必须要有workload 容器
            throw new PodExceptions.ContainerTypeException();
        }
    }

    private void validateTidalPodSubmitTime(String podId) {
        TidalTime tidalTime = tidalValidator.buildCurrentTidalTime();
        // TODO 带去掉
        LOGGER.debug("validateTidalPodSubmitTime tidalTime {} ", tidalTime);
       if (!tidalValidator.inTidalRunTime(tidalTime)) {
           LOGGER.debug("validateTidalPodSubmitTime podId {} tidalTime {} is not in tidal time ",
                   podId, tidalTime.printCurrentDate());
           throw new PodExceptions.TidalException("tidal pod submit time only support "
                   + tidalTime.getTidalStartSubmitTime() + "-" + tidalTime.getTidalEndSubmitTime());
       }
    }

    public void validateCpuType(String cpuType) {
        if (cpuType != null) {
            // 只有白名单用户才能设置cpuType
            if (!"".equals(cpuType) && !podService.isEnableCPUType()) {
                throw new PodExceptions.CpuTypePermissionDenyException();
            }
            // 检查cpuType参数，cpuType只能为：“”，intel, amd
            if (!podService.isValidCPUType(cpuType)) {
                throw new PodExceptions.CpuTypeInvalidException();
            }
        }
    }

    public void validatePreviewPodCapacityPodType(List<String> types) {
        if (CollectionUtils.isEmpty(types)) {
            return;
        }
        for (int i = 0; i < types.size(); i++) {
            if (types.get(i).equalsIgnoreCase(PreviewPodCapacityConstant.PREVIEW_POD_CAPACITY_TYPE_TIDAL)) {
                continue;
            } else if (types.get(i).equalsIgnoreCase(PreviewPodCapacityConstant.PREVIEW_POD_CAPACITY_TYPE_PFS)) {
                // BCI 2.0 使用PFS需要在云桥上开白名单
                if (!podService.isEnablePFS()) {
                    throw new PermissionExceptionUtil.PFSPermissionDenyException();
                }
                continue;
            }
            throw new PodExceptions.PreviewPodCapacityTypeInvalidException();
        }
    }

    private void validateCoredumpConfig(PodPurchaseRequest podPurchaseRequest) {
        List<Label> labels = podPurchaseRequest.getLabels();
        if (CollectionUtils.isEmpty(labels)) {
            return;
        }
        boolean hasCoredumpPattern = false;
        for (Label label : labels) {
            if (!PodNewOrderExecutorServiceV2.COREDUMP_PATTERN.equals(label.getLabelKey())) {
                continue;
            }
            hasCoredumpPattern = true;
            // TODO 待适配bci3.0
            if (!PodNewOrderExecutorServiceV2.COREDUMP_PATTERN_VALUE.equals(label.getLabelValue())) {
                throw new PodExceptions.CoreDumpException("Pod coredump pattern is invalid. Only support /tmp/cores");
            }
            break;
        }
        if (!hasCoredumpPattern) {
            return;
        }

        List<ContainerPurchase> containers = podPurchaseRequest.getContainerPurchases();
        if (CollectionUtils.isEmpty(containers)) {
            return;
        }

        int coredumpContainerCount = 0;
        for (ContainerPurchase container : containers) {
            List<VolumeMounts> volumeMounts = container.getVolumeMounts();
            if (CollectionUtils.isEmpty(volumeMounts)) {
                continue;
            }
            for (VolumeMounts volumeMount : volumeMounts) {
                if (PodNewOrderExecutorServiceV2.COREDUMP_PATTERN_VALUE.equals(volumeMount.getMountPath())) {
                    coredumpContainerCount++;
                }
            }
        }

        // 必须要有容器使用coredump volumemount
        if (coredumpContainerCount == 0) {
            throw new PodExceptions.CoreDumpException("Pod Container volumeMount must have coredump pattern value " +
                    PodNewOrderExecutorServiceV2.COREDUMP_PATTERN_VALUE);
        }
    }

    private void validatePodDnsConfig(PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(podPurchaseRequest.getLabels())) {
            return;
        }
        String dnsConfigJson = null;
        for (Label label : podPurchaseRequest.getLabels()) {
            if (PodNewOrderExecutorServiceV2.DNS_CONFIG_LABEL_KEY.equals(label.getLabelKey())) {
                dnsConfigJson = label.getLabelValue();
                break;
            }
        }

        if (dnsConfigJson == null) {
            return;
        }
        LOGGER.info("validatePodDnsConfig pod {} dnsConfig {} check start ", podPurchaseRequest.getName(),
                dnsConfigJson);
        DNSConfig vkDnsConfig = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            vkDnsConfig = mapper.readValue(dnsConfigJson, DNSConfig.class);
        } catch (Exception e) {
            LOGGER.error("validatePodDnsConfig Unmarshal pod {} dnsConfigJson {} err {} ",
                    podPurchaseRequest.getName(), dnsConfigJson, e);
            return;
        }
        if (vkDnsConfig != null) {
            List<String> servers = vkDnsConfig.getServers();
            if (servers != null && servers.size() > 2) {
                // k8s dns nameserver 最多允许创建3个，创建pod时默认加了子网的dns nameserver ，此处加个判断
                throw new PodExceptions.DnsConfigException("Pod Dns Config Nameserver length is invalid. No more " +
                        "than 2 nameservers");
            }

            List<String> searches = vkDnsConfig.getSearches();
            if (searches == null || searches.size() == 0) {
                return;
            }
            if (searches.size() > 6) {
                // k8s 校验规则
                throw new PodExceptions.DnsConfigException("Pod Dns Config Searches length is invalid. No more " +
                        "than 6 Searches");
            }

            String searchStr = String.join(" ", searches);
            if (searchStr.length() > 256) {
                // must not have more than 256 characters (including spaces) in the search list
                throw new PodExceptions.DnsConfigException("Pod Dns Config Searches must not have more than 256 " +
                        "characters (including spaces)");
            }
        }
    }

    private void validateContainerEnv(List<Environment> envs) {
        if (CollectionUtils.isEmpty(envs)) {
            return;
        }
        for (Environment environment : envs) {
            V1EnvVarSource valueFrom = environment.getValueFrom();
            if (valueFrom == null) {
                continue;
            }
            V1ObjectFieldSelector fieldRef = valueFrom.getFieldRef();
            if (fieldRef == null || (valueFrom.getConfigMapKeyRef() != null || valueFrom.getResourceFieldRef() != null
                    || valueFrom.getSecretKeyRef() != null)) {
                throw new PodExceptions.ContainerEnvironmentException();
            }

            String podIP = "status.podIP";
            String podIPs = "status.podIPs";
            String hostIP = "status.hostIP";
            if ( !(podIP.equals(fieldRef.getFieldPath()) || podIPs.equals(fieldRef.getFieldPath()) || hostIP.equals(fieldRef.getFieldPath()))) {
                throw new PodExceptions.ContainerEnvironmentException();
            }
        }
    }

    private void validateContainerProbe(Probe containerProbe) {
        if (containerProbe == null) {
            return;
        }
        HTTPGetAction httpGet = containerProbe.getHttpGet();
        TCPSocketAction tcpSocket = containerProbe.getTcpSocket();
        GRPCAction grpc = containerProbe.getGrpc();
        ExecAction exec = containerProbe.getExec();
        // 目前只支持exec/http/tcp；
        if (grpc != null) {
            throw new PodExceptions.ContainerProbeException();
        }
        if (exec == null && tcpSocket == null && httpGet == null) {
            throw new PodExceptions.ContainerProbeException();
        }
    }

    public void validateImageRegistrySecret(PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(podPurchaseRequest.getImageRegistrySecrets())) {
            return;
        }
        String patternStr = "(http|https):\\/\\/([\\w.]+\\/?)\\S*";
        Pattern pattern = Pattern.compile(patternStr);
        Map<String, ImageRegistrySecret> imageRegistrySecretMap = new HashMap<>();
        for (ImageRegistrySecret registrySecret : podPurchaseRequest.getImageRegistrySecrets()) {
            if (!pattern.matcher(registrySecret.getServer()).matches()) {
                throw new PodExceptions.ImageRegistryException();
            }
            String address = registrySecret.getServer().replace("http://", "")
                    .replace("https://", "");
            if (address.endsWith("/")) {
                address = address.substring(0, address.lastIndexOf("/"));
            }
            imageRegistrySecretMap.put(address, registrySecret);
        }
        LOGGER.debug("ImageRegistrySecretMap adress : {}", imageRegistrySecretMap.keySet());

        boolean isImageRegistry = false;
        for (ContainerPurchase container : podPurchaseRequest.getContainerPurchases()) {
            String repository = container.getImageAddress();
            int sublen = repository.lastIndexOf("/");
            // docker hub 只传 image name，这里做下兼容
            if (sublen == -1) {
                continue;
            }
            repository = repository.substring(0, sublen);
            LOGGER.debug("Image repository : {}", repository);
            if (imageRegistrySecretMap.keySet().contains(repository)) {
                isImageRegistry = true;
                break;
            }
        }

        if (!isImageRegistry) {
            throw new PodExceptions.ImageRegistryException();
        }
    }

    private void validateCreateBciParams(PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(podPurchaseRequest.getContainerPurchases())) {
            throw new PodExceptions.ContainerException("Pod containers must be set.");
        }
        if (StringUtils.isNotEmpty(podPurchaseRequest.getName()) &&
                !PodUtils.validaterName(podPurchaseRequest.getName())) {
            LOGGER.error("NameInvalidException,the pod name {} is invalidate", podPurchaseRequest.getName());
            throw new PodExceptions.NameInvalidException("Pod name is invalid.");
        }
        String productType = podPurchaseRequest.getProductType();
        if (!Payment.isPostpay(productType) && !podConfiguration.getBidProductType().equals(productType)) {
            LOGGER.warn("validateProductType failed!");
            String message = "The product type is invalid. Only PostPay/bidding are supported.";
            throw new PodExceptions.ProductTypeInvalid(message);
        }

        boolean checkResultOfDelayReleaseDurationMinute =
                checkValue2(podPurchaseRequest.getDelayReleaseDurationMinute(), PodConstants.DELAY_RELEASE_MAX_MINUTE);
        if (!checkResultOfDelayReleaseDurationMinute) {
            String message = "The delayReleaseDurationMinute should be set between 0-"
                    + PodConstants.DELAY_RELEASE_MAX_MINUTE + ".";
            throw new PodExceptions.DelayReleaseDurationMinuteException(message);
        }
        Volume volume = podPurchaseRequest.getVolume();
        if (volume == null) {
            LOGGER.warn("validate volume failed!");
            throw new CommonExceptions.RequestInvalidException();
        }

        List<BaseVolume> volumes = new ArrayList<>();
        volumes.addAll(volume.getNfs());
        volumes.addAll(volume.getEmptyDir());
        volumes.addAll(volume.getConfigFile());
        volumes.addAll(volume.getFlexVolume());
        volumes.addAll(volume.getPfs());
        volumes.addAll(volume.getBos());
        volumes.addAll(volume.getCephfs());
        volumes.addAll(volume.getHostPath());
        Map<String, BaseVolume> volumeMap = new HashMap<>();
        for (BaseVolume baseVolume : volumes) {
            if (volumeMap.containsKey(baseVolume.getName())) {
                throw new PodExceptions.
                        VolumeNameInvalidException("Invalid volume name: " + baseVolume.getName() + ", duplicated.");
            }
            volumeMap.put(baseVolume.getName(), baseVolume);
        }

        Map<String, HostPathVolume> hostpathMap = new HashMap<>();
        for (HostPathVolume hostpathVolume : volume.getHostPath()) {
            // 禁止用户挂载containerd目录下的socket文件，以及proc文件系统
            if (hostpathVolume.getPath().startsWith("/run/containerd/")
                    || hostpathVolume.getPath().startsWith("/var/run/containerd")
                    || hostpathVolume.getPath().startsWith("/proc") ) {
                throw new PodExceptions.HostPathInvalidException("host path is invalid.");
            }
            hostpathMap.put(hostpathVolume.getName(), hostpathVolume);
        }

        // 校验 name
        int rootfsNum = 0;
        if (CollectionUtils.isNotEmpty(volume.getPodVolumes())) {
            // volume ids
            rootfsNum = 1;
            for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
                if (volumeMap.containsKey(podVolume.getName())) {
                    throw new PodExceptions.
                            VolumeNameInvalidException("Invalid volume name: " + podVolume.getName() + ", duplicated.");
                }
                volumeMap.put(podVolume.getName(), new BaseVolume());
            }
        }

        List<VolumeMounts> volumeMounts = new LinkedList<>();
        // 校验 volumeMounts 和 volumes：必须能互相对应
        for (ContainerPurchase container : podPurchaseRequest.getContainerPurchases()) {
            if (container != null && CollectionUtils.isNotEmpty(container.getVolumeMounts())) {
                volumeMounts.addAll(container.getVolumeMounts());
            }
        }

        Map<String, Boolean> mountHitVolume = new HashMap<>();
        for (VolumeMounts volumeMount : volumeMounts) {
            String mountName = volumeMount.getName();
            if (!volumeMap.containsKey(mountName)) {
                throw new PodExceptions.MountsNoVolumeException(mountName);
            }

            // hostPath限制为只读，除了 /var/log/pods
            if (hostpathMap.containsKey(mountName)) {
                HostPathVolume hostpathVolume = hostpathMap.get(mountName);
                if (!volumeMount.getReadOnly() && !hostpathVolume.getPath().startsWith("/var/log/pods") && !hostpathVolume.getPath().startsWith("/var/log/bciservices")) {
                    throw new PodExceptions.MountsHostPathWithoutReadOnlyException(hostpathVolume.getPath());
                }
            }

            mountHitVolume.put(mountName, true);
        }

        // 按道理现在应该只有 rootfs；这里不校验用户传了rootfs mounts 的情况，其他地方会校验，所以不考虑 size == 0
        if (volumeMap.size() - mountHitVolume.size() > rootfsNum) {
            LOGGER.warn("volumeMap size:{} not match mountHitVolume size:{} rootfsNum:{}, "
                    + "volumeMap.keys:{} mountHitVolume.keys:{}",
                    volumeMap.size(), mountHitVolume.size(), rootfsNum,
                    volumeMap.keySet().toString(), mountHitVolume.keySet().toString());
        }
        podLogService.validatePodLogCollections(podPurchaseRequest);
    }

    private void validateQuotaLimit(PodPurchaseRequest podPurchaseRequest, BciQuota bciQuota) {
        if (bciQuota.getPodCreated() >= bciQuota.getPodTotal()) {
            throw new PodExceptions.ExceedLimitException();
        }
        Volume volume = podPurchaseRequest.getVolume();

        List<Nfs> nfs = volume.getNfs();
        List<EmptyDir> emptyDirs = volume.getEmptyDir();
        List<ConfigFile> configFile = volume.getConfigFile();

        if (nfs.size() > bciQuota.getNfsRatio()) {
            throw new PodExceptions.NfsQuotaExceededLimit();
        }
        if (emptyDirs.size() > bciQuota.getEmptyDirRatio()) {
            throw new PodExceptions.EmptyDirQuotaExceededLimit();
        }
        if (configFile.size() > bciQuota.getConfigFileRatio()) {
            throw new PodExceptions.ConfigFileQuotaExceededLimit();
        }

        Map<String, Integer> volumeMap = new HashMap<>();

        // 新配置和旧配置分开校验，不排除有用户两种方式都用，这种情况让用户改一下
        if (CollectionUtils.isNotEmpty(volume.getPodVolumes())) {
            for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
                String volumeType = podVolume.getType();

                if (!volumeMap.containsKey(volumeType)) {
                    volumeMap.put(volumeType, 1);
                    continue;
                }
                volumeMap.put(volumeType, volumeMap.get(volumeType) + 1);
            }
        }

        String emptyDir = PodCDSVolumeType.EMPTYDIR.getType();
        String dataVolume = PodCDSVolumeType.DATA.getType();
        String rootfs = PodCDSVolumeType.ROOTFS.getType();

        // cds 和 本地盘不能混用
        if (CollectionUtils.isNotEmpty(emptyDirs) && volumeMap.containsKey(emptyDir)) {
            throw new PodExceptions.LocalDiskAndCDSMixed();
        }
        // emptyDir 在一起算
        if (volumeMap.containsKey(emptyDir) && (volumeMap.get(emptyDir) > bciQuota.getEmptyDirRatio())) {
            throw new PodExceptions.EmptyDirQuotaExceededLimit();
        }
        if (volumeMap.containsKey(dataVolume) && (volumeMap.get(dataVolume) > bciQuota.getDataVolumeQuota())) {
            throw new PodExceptions.DataVolumeQuotaExceededLimit();
        }
    }

    public SecurityGroupSimpleInstancesVO getSecurityGroupsByVpc(String vpcId) {
        SecurityGroupSimpleInstancesVO securityGroupSimpleInstancesVO = new SecurityGroupSimpleInstancesVO();
        if (StringUtils.isEmpty(vpcId)) {
            return securityGroupSimpleInstancesVO;
        }
        List<String> vpcIds = new ArrayList<>();
        vpcIds.add(vpcId);
        try {
            securityGroupSimpleInstancesVO = logicPodClientFactory.
                    createSecurityGroupClient(getAccountId()).getSimpleSecurityGroupListByVpcIds(vpcIds);
        } catch (Exception e) {
            LOGGER.error("getSimpleSecurityGroupListByVpcIds error with vpcId = {}, error = {}", vpcId, e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        return securityGroupSimpleInstancesVO;
    }

    public String getDefaultSecurityGroupIdByVpc(String vpcId) {
        SecurityGroupSimpleInstancesVO securityGroupSimpleInstancesVO = getSecurityGroupsByVpc(vpcId);
        for (SimpleSecurityGroupVO simpleSecurityGroupVO : securityGroupSimpleInstancesVO.getList()) {
            if ("默认安全组".equals(simpleSecurityGroupVO.getName())) {
                LOGGER.debug("getDefaultSecurityGroupIdByVpc success with vpcId = {}, securityGroupVO = {}",
                        vpcId, simpleSecurityGroupVO);
                return simpleSecurityGroupVO.getSecurityGroupId();
            }
        }
        LOGGER.error("getDefaultSecurityGroupIdByVpc error with vpcId = {}", vpcId);
        throw new CommonExceptions.InternalServerErrorException();
    }

    public void validateAndSetSubnetUuid(PodPurchaseRequest podPurchaseRequest,
                                         Map<String, ZoneMapDetail> zoneMap,
                                         Map<String, SubnetVo> subnetVoMap,
                                         Map<String, VpcVo> vpcVoMap,
                                         boolean isCreateWithEip) {
        // 简化逻辑，子网和安全组，必须都传 && 安全组不允许为default
        if (StringUtils.isEmpty(podPurchaseRequest.getSubnetId())
                && StringUtils.isEmpty(podPurchaseRequest.getSubnetUuid())
                && StringUtils.isEmpty(podPurchaseRequest.getSubnetIds())) {
            throw new CommonExceptions.RequestInvalidException("The subnet should be specified.");
        }
        /*
        if (StringUtils.isEmpty(podPurchaseRequest.getSecurityGroupId())) {
            throw new CommonExceptions.RequestInvalidException("securityGroupId should be added");
        }
        */
        VpcVo vpcVo = null;
        List<ZoneSubnets> zoneSubnetsList = new ArrayList<>();
        for (SubnetVo value : subnetVoMap.values()) {
            if (!zoneMap.containsKey(value.getAz())) {
                // 说明该AZ被过滤
                LOGGER.debug("subnet {} invalid because az not exit",
                        value.getShortId());
                continue;
            }
            ZoneMapDetail zoneMapDetail = zoneMap.get(value.getAz());
            if (vpcVo == null) {
                vpcVo = vpcVoMap.get(value.getVpcShortId());
            }
            if (vpcVo == null) {
                LOGGER.warn("vpcVo is null with vpcShortId {}", value.getVpcShortId());
                throw new CommonExceptions.RequestInvalidException(
                        "vpcVo is null with vpcShortId is " + value.getVpcShortId());
            }
            zoneSubnetsList.add(new ZoneSubnets(zoneMapDetail, value, vpcVo));
        }
        if (isDefaultSecurityGroupId(podPurchaseRequest.getSecurityGroupId())) {
            // 使用vpc内的默认安全组
            String securityGroupId = getDefaultSecurityGroupIdByVpc(zoneSubnetsList.get(0).getSubnetVo().getVpcId());
            podPurchaseRequest.setSecurityGroupId(securityGroupId);
        } else {
            // 前面已经验证过subnets属于同一个vpc，此处验证一个即可
            if (!zoneSubnetsList.isEmpty()) {
                List<String> securityGroups = Arrays.asList(podPurchaseRequest.getSecurityGroupId().trim().split(","));
                boolean isNormal = isNormalSecurityGroupId(securityGroups);
                if (isNormal) {
                    validateSubnetSecurityGroupInSameVpc(podPurchaseRequest.getSecurityGroupId().split(",")[0],
                        zoneSubnetsList.get(0).getSubnetVo().getSubnetId());
                }
                
            }
        }

        // int availableIPs = 0;
        // boolean needSkipIpCheck = false;
        List<ZoneSubnets> availableZoneSubnets = new ArrayList<>();
        for (ZoneSubnets zoneSubnets : zoneSubnetsList) {
            if (isCreateWithEip && SubnetVo.SubnetType.BCC_NAT.getId() == zoneSubnets.getSubnetVo().getSubnetType()) {
                // 原有逻辑, 过滤调该subnet
                LOGGER.debug("subnet {} invalid because isCreateWithEip and BCC_NAT",
                        zoneSubnets.getSubnetVo().getShortId());
                continue;
            }
            availableZoneSubnets.add(zoneSubnets);
            /*
            SubnetVo subnetVo = new SubnetVo();
            try {
                subnetVo = getSubnetWithIpUsage(zoneSubnets.getSubnetVo().getSubnetUuid());
            } catch (BackendExceptions.ExceedLimitException e) {
                // 判断是否存在subnet获取异常(存在异常, 则跳过后续ip数量校验)
                needSkipIpCheck = true;
                // 接口被限速, 不能判断ip是否够用, 直接加入候选集, 让后端进行判断。
                availableZoneSubnets.add(zoneSubnets);
                LOGGER.debug("getSubnetWithIpUsage {} ExceedLimit {}", zoneSubnets.getSubnetVo().getShortId(), e);
                continue;
            }
            // 若正常获取到ip数量, 则根据是否有即可用ip判断是否加入候选集
            if (subnetVo.getTotalIps() >= 0 && subnetVo.getUsedIps() >= 0 
                        && subnetVo.getTotalIps() - subnetVo.getUsedIps() > 0) {
                availableZoneSubnets.add(zoneSubnets);
                availableIPs += subnetVo.getTotalIps() - subnetVo.getUsedIps();
            } else {
                LOGGER.debug("subnet {} invalid because ip not enough, detail {}",
                        subnetVo.getShortId(), subnetVo);
            }
            */
        }
        // 判断过滤后是否有可用的subnet
        if (availableZoneSubnets.isEmpty()) {
            throw new PodExceptions.RequestInvalidException("No subnet available.");
        }
        // 暂时先不做校验
        /*
        // 子网内IP容量判断
        if (!needSkipIpCheck && availableIPs < podPurchaseRequest.getPurchaseNum()) {
            LOGGER.debug("subnets ip check failed, available:{}, need:{}",
                    availableIPs, podPurchaseRequest.getPurchaseNum());
            throw new VpcExceptions.IpInSubnetNotEnoughExceptions();
        }
        */
        // 区分单可用区和多可用区(单可用区直接确定zone, 多可用区还得有后端筛选)
        if (availableZoneSubnets.size() == 1) {
            // 回写请求子网信息
            podPurchaseRequest.setSubnetId(availableZoneSubnets.get(0).getSubnetVo().getSubnetId());
            podPurchaseRequest.setSubnetUuid(availableZoneSubnets.get(0).getSubnetVo().getSubnetUuid());
            podPurchaseRequest.setSubnetShortId(availableZoneSubnets.get(0).getSubnetVo().getShortId());
            podPurchaseRequest.setSubnetCidr(availableZoneSubnets.get(0).getSubnetVo().getCidr());
            // 回写请求可用区信息
            podPurchaseRequest.setLogicalZone(availableZoneSubnets.get(0).getZoneMapDetail().getLogicalZone());
            podPurchaseRequest.setZoneId(availableZoneSubnets.get(0).getZoneMapDetail().getZoneId());
            podPurchaseRequest.setEncryptedPhysicalZone(
                    availableZoneSubnets.get(0).getZoneMapDetail().getPhysicalZone());
        } else {
            podPurchaseRequest.setZoneSubnets(JsonUtil.toJSON(availableZoneSubnets));
        }
        podPurchaseRequest.setVpcId(vpcVo.getShortId());
        podPurchaseRequest.setVpcCidr(vpcVo.getCidr());
    }

    public SubnetVo getSubnetWithIpUsage(String subnetUuid) {
        SubnetVo subnetVo = null;
        try {
            ExternalSubnetClient subnetClient = logicPodClientFactory.createExternalSubnetClient(getAccountId());
            subnetVo = subnetClient.findSubnetWithIpUsage(subnetUuid);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e, "subnet");
        }
        if (subnetVo == null) {
            throw new CommonExceptions.ResourceNotExistException("subnet");
        }

        return subnetVo;
    }

    private boolean isDefaultSecurityGroupId(String sgId) {
        return StringUtils.isEmpty(sgId) || "default".equals(sgId);
    }

    public boolean isNormalSecurityGroupId(List<String>  sgIds) {
        return allStringsStartWithPrefix(sgIds, "g-");
    }

    public boolean isEnterpriseSecurityGroupId(List<String>  sgIds) {
        return allStringsStartWithPrefix(sgIds, "esg-");
    }

    public static boolean allStringsStartWithPrefix(List<String> array, String prefix) {
        for (String str : array) {
            if (!str.startsWith(prefix)) {
                return false;
            }
        }
        return true;
    }

    public SubnetVo getSubnet(String subnetUuid) {
        SubnetVo subnetVo = null;
        try {
            ExternalSubnetClient subnetClient = logicPodClientFactory.createExternalSubnetClient(getAccountId());
            subnetVo = subnetClient.findBySubnetId(subnetUuid);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e, "subnet");
        }
        if (subnetVo == null) {
            throw new CommonExceptions.ResourceNotExistException("subnet");
        }

        return subnetVo;
    }

    public SubnetMapResponse getSubnets(List<String> subnetIds) {
        SubnetMapResponse subnetMapResponse = new SubnetMapResponse();
        if (subnetIds.isEmpty()) {
            return subnetMapResponse;
        }
        SubnetMapRequest subnetMapRequest = new SubnetMapRequest();
        subnetMapRequest.setSubnetIds(subnetIds);
        subnetMapRequest.setAttachVpc(true);
        try {
            subnetMapResponse = subnetMapCache.get(subnetMapRequest);
            if (subnetMapResponse != null) {
                return subnetMapResponse;
            }
            subnetMapResponse = new SubnetMapResponse();
            ExternalSubnetClient subnetClient = logicPodClientFactory.createExternalSubnetClient(getAccountId());
            subnetMapResponse = subnetClient.getSubnetMap(subnetMapRequest);
            subnetMapCache.put(subnetMapRequest, subnetMapResponse);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e, "subnet");
        }
        if (subnetMapResponse == null) {
            throw new CommonExceptions.ResourceNotExistException("subnet");
        }
        return subnetMapResponse;
    }

    public void validateSubnetSecurityGroupInSameVpc(String securityGroupId, String subnetId) {
        boolean same = false;
        BelongSameVpcRequest belongSameVpcRequest = new BelongSameVpcRequest();
        belongSameVpcRequest.setSecurityGroupId(securityGroupId);
        belongSameVpcRequest.setSubnetId(subnetId);
        Boolean result = subnetSecurityGroupInSameVpcMap.get(belongSameVpcRequest);
        if (result != null) {
            LOGGER.debug("validateBelongSameVpc begin with belongSameVpcRequest = {}, result = {}",
                    belongSameVpcRequest.toString(), result.booleanValue());
            if (!result.booleanValue()) {
                throw new PodExceptions.SecurityGroupInvalidException(
                        "The security groups and subnets should belong to the same vpc.");
            }
        } else {
            try {
                same = logicPodClientFactory.
                        createExternalVpcClient(getAccountId()).validateBelongSameVpc(belongSameVpcRequest);
                LOGGER.debug("validateBelongSameVpc success with belongSameVpcRequest = {}, result = {}",
                        belongSameVpcRequest.toString(), same);
                Boolean cacheResult = Boolean.valueOf(same);
                subnetSecurityGroupInSameVpcMap.put(belongSameVpcRequest,
                        cacheResult);
            } catch (Exception e) {
                LOGGER.error("validateBelongSameVpc error with belongSameVpcRequest = {}, error = {}",
                        belongSameVpcRequest.toString(), e);
                throw new CommonExceptions.InternalServerErrorException();
            }
            if (!same) {
                throw new PodExceptions.SecurityGroupInvalidException(
                        "The security groups and subnets should belong to the same vpc.");
            }
        }
    }

    /**
     * 验证搜索的值是否合法
     *
     * @param value 搜索值
     * @return 返回true代表合法
     */
    public static boolean validateSearchId(String value) {
        Pattern pattern = Pattern.compile(SEARCH_VALUE);
        return StringUtils.isEmpty(value) || pattern.matcher(value).matches();
    }

    /**
     * 验证搜索的值是否合法
     *
     * @param filterMap 过滤条件
     * @return 返回true代表过滤条件合法
     */
    public static boolean validateSearchId(Map<String, String> filterMap) {
        for (Map.Entry<String, String> entry : filterMap.entrySet()) {
            if ("tag".equalsIgnoreCase(entry.getKey())) {
                continue;
            }
            if ("zoneId".equals(entry.getKey()) || "vCpu".equals(entry.getKey()) ||
                    "memory".equals(entry.getKey()) || "gpuCount".equals(entry.getKey()) ||
                    "gpuType".equals(entry.getKey())) {
                continue;
            }
            // 其他query key,需要校验其value是否合法
            if (!validateSearchId(entry.getValue())) {
                return false;
            }
        }
        return true;
    }

    public void validateCpuAndMemory(String name, Float cpu, Float memory) {
        if (podConfiguration.getContainerCpu() == null) {
            return;
        }
        LOGGER.debug("container cpu and memory setting, cpu:{}, minRatio:{}, maxRatio:{}, stepLength:{}",
                podConfiguration.getContainerCpu(), podConfiguration.getMinRatio(),
                podConfiguration.getMaxRatio(), podConfiguration.getStepLength());
        LOGGER.debug("cpu:{}", cpu.toString());
        if (!podConfiguration.getContainerCpu().contains(cpu.toString())) {
            throw new PodExceptions.ContainerCPUInvalidException(name);
        } else {
            Float ratio = memory / cpu;
            if (ratio < podConfiguration.getMinRatio()
                    || ratio > podConfiguration.getMaxRatio()
                    || memory % podConfiguration.getStepLength() != 0) {
                throw new PodExceptions.ContainerCPUMemoryRatioInvalidException(name);
            }
        }
    }

    public void validateCpuAndMemoryInWhiteList(String name, Float cpu, Float memory) {
        if (podConfiguration.getContainerCpu() == null) {
            return;
        }
        LOGGER.debug("container cpu and memory setting, cpu:{}, minRatio:{}, maxRatio:{}, stepLength:{}",
                podConfiguration.getContainerCpu(), podConfiguration.getMinRatio(),
                podConfiguration.getMaxRatio(), podConfiguration.getStepLength());
        LOGGER.debug("cpu:{}", cpu.toString());
        if (!podConfiguration.getContainerCpuInWhiteList().contains(cpu.toString())) {
            throw new PodExceptions.ContainerCPUInvalidException(name);
        } else {
            Float ratio = memory / cpu;
            if (ratio < podConfiguration.getMinRatio()
                    || ratio > podConfiguration.getMaxRatio()
                    || memory % podConfiguration.getStepLength() != 0) {
                throw new PodExceptions.ContainerCPUMemoryRatioInvalidException(name);
            }
        }

    }

    public void validateMountVolumeType(ContainerPurchase containerPurchase, Volume volume) {
        Map<String, String> volumeMap = new HashMap<>();
        for (Nfs nfs : volume.getNfs()) {
            if (volumeMap.containsKey(nfs.getName())) {
                throw new PodExceptions.
                        VolumeNameInvalidException("Invalid volume name: " + nfs.getName() + ", duplicated.");
            }
            volumeMap.put(nfs.getName(), PodVolumeType.NFS.getType());
        }
        for (EmptyDir emptyDir : volume.getEmptyDir()) {
            if (volumeMap.containsKey(emptyDir.getName())) {
                throw new PodExceptions.
                        VolumeNameInvalidException("Invalid volume name: " + emptyDir.getName() + ", duplicated.");
            }
            volumeMap.put(emptyDir.getName(), PodVolumeType.EMPTY_DIR.getType());
        }
        for (ConfigFile configFile : volume.getConfigFile()) {
            if (volumeMap.containsKey(configFile.getName())) {
                throw new PodExceptions.
                        VolumeNameInvalidException("Invalid volume name: " + configFile.getName() + ", duplicated.");
            }
            volumeMap.put(configFile.getName(), PodVolumeType.CONFIG_FILE.getType());
        }

        if (CollectionUtils.isNotEmpty(volume.getPodVolumes())) {
            for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
                volumeMap.put(podVolume.getName(), podVolume.getType());
            }
        }

        for (FlexVolume flexVolume : volume.getFlexVolume()) {
            if (volumeMap.containsKey(flexVolume.getName())) {
                throw new PodExceptions.
                        VolumeNameInvalidException("Invalid volume name: " + flexVolume.getName() + ", duplicated.");
            }
            volumeMap.put(flexVolume.getName(), PodVolumeType.FLEX_VOLUME.getType());
        }

        for (Pfs pfs : volume.getPfs()) {
            if (volumeMap.containsKey(pfs.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            volumeMap.put(pfs.getName(), PodVolumeType.PFS.getType());
        }

        for (Bos bos : volume.getBos()) {
            if (volumeMap.containsKey(bos.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            volumeMap.put(bos.getName(), PodVolumeType.BOS.getType());
        }

        for (HostPathVolume hostPathVolume : volume.getHostPath()) {
            if (volumeMap.containsKey(hostPathVolume.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            volumeMap.put(hostPathVolume.getName(), PodVolumeType.HOST_PATH.getType());
        }

        for (CephFSVolume cephFS : volume.getCephfs()) {
            if (volumeMap.containsKey(cephFS.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            volumeMap.put(cephFS.getName(), PodVolumeType.CEPHFS.getType());
        }

        validateMountVolumeType(containerPurchase, volumeMap, null);
    }

    public void validateMountVolumeType(ContainerPurchase containerPurchase, Map<String, String> allowedVolume2TypeMap, 
                                        Map<String, String> nonAllowedVolume2TypeMap) {
        List<VolumeMounts> volumeMountsList = containerPurchase.getVolumeMounts();
        if (volumeMountsList == null) {
            return;
        }
        Map<String, Boolean> mountPathValidator = new HashMap<>();
        for (VolumeMounts volumeMount : volumeMountsList) {
            String mountPath = volumeMount.getMountPath();
            String mountName = volumeMount.getName();
            String mountType = volumeMount.getType();
            if (allowedVolume2TypeMap.get(mountName) == null) {
                throw new PodExceptions.PodContainerMountInvalidException();
            }
            if (StringUtils.isNotEmpty(mountType) &&
                    !mountType.equals(allowedVolume2TypeMap.get(mountName))) {
                throw new PodExceptions.PodContainerMountTypeInvalidException();
            }
            if (nonAllowedVolume2TypeMap != null && nonAllowedVolume2TypeMap.containsKey(mountName)) {
                throw new PodExceptions.PodContainerMountInvalidException();
            }
            if (mountPathValidator.containsKey(mountPath)) {
                throw new PodExceptions.MountPathDuplicated(mountPath);
            }
            // subpath 和 subpathExpr不能同时存在
            if (StringUtils.isNotEmpty(volumeMount.getSubPath()) &&
                    StringUtils.isNotEmpty(volumeMount.getSubPathExpr())) {
                throw new PodExceptions.SubPathDuplicated();
            }
            mountPathValidator.put(mountPath, true);
            volumeMount.setType(allowedVolume2TypeMap.get(mountName));
        }
    }

    public void validateSecurityGroup(PodPurchaseRequest podPurchaseRequest) {
        if (isDefaultSecurityGroupId(podPurchaseRequest.getSecurityGroupId())) {
            return;
        }
        List<String> securityGroupIds = Arrays.asList(podPurchaseRequest.getSecurityGroupId().trim().split(","));
        if (securityGroupIds.size() > 10) {
            throw new PodExceptions.SecurityGroupInvalidException("The security groups count exceed max limit 10.");
        }
        // 判断SecurityGroupId是否存在""、是否存在重复值
        Set<String> securityGroupIdSet = new HashSet<>();
        for (String securityGroupId : securityGroupIds) {
            if ("".equals(securityGroupId)) {
                throw new PodExceptions.SecurityGroupInvalidException("The security groups is invalid.");
            }
            if (!securityGroupIdSet.add(securityGroupId)) {
                throw new PodExceptions.SecurityGroupInvalidException("The securityGroup ["
                        + securityGroupId + "] is duplicated.");
            }
        }

        // 判断SecurityGroupId是否为均为普通安全组或者均为企业安全组
        boolean isNormal = isNormalSecurityGroupId(securityGroupIds);
        boolean isEnterprise = isEnterpriseSecurityGroupId(securityGroupIds);
        if (!isNormal && !isEnterprise) {
            throw new PodExceptions.SecurityGroupInvalidException("The security groups must be normal(all  startwith g-), or enterprise(all startwith esg-)");
        }

        if (isNormal) {
            List<SimpleSecurityGroupVO> securityGroupVOs = podService.getSecurityGroup(podPurchaseRequest.getSecurityGroupId());
            String vpcId = "";
            for (SimpleSecurityGroupVO securityGroupVO : securityGroupVOs) {
                if ("".equals(vpcId)) {
                    vpcId = securityGroupVO.getVpcId();
                 } else {
                    if (!vpcId.equals(securityGroupVO.getVpcId())) {
                       throw new PodExceptions.SecurityGroupInvalidException(
                        "The security groups should belong to the same vpc.");
                    }
                }
            }
        }

    }

    public void validateSecurityContextOfContainers(List<ContainerPurchase> containers,
                                                    boolean isBciV3) {
        List<String> bciContainerSecurityContextEnableUsersList =
                Arrays.asList(bciContainerSecurityContextEnableUsers.split(","));
        String accountId = getAccountId();
        if (bciContainerSecurityContextEnableUsersList.contains(accountId)) {
            return;
        }
        if (CollectionUtils.isEmpty(containers)) {
            return;
        }
        for (ContainerPurchase container : containers) {
            boolean isDsContainer = ContainerType.DS_WORKLOAD.getType().equals(container.getContainerType());
            validateContainerSecurityContext(container.getName(), container.getSecurityContext(), isBciV3, isDsContainer);
        }
    }

    public void validateContainerSecurityContext(String containerName,
                                                 ContainerSecurityContext containerSecurityContext,
                                                 boolean isBciV3, boolean isDsContainer) {
        if (containerSecurityContext == null) {
            return;
        }

        validateContainerPrivileged(containerName, containerSecurityContext, isBciV3, isDsContainer);
        if (containerSecurityContext.getCapabilities() != null) {
            Capabilities cap = containerSecurityContext.getCapabilities();
            if (CollectionUtils.isEmpty(cap.getAdd())) {
                return;
            }
            for (String addCap : cap.getAdd()) {
                if (!Capabilities.whiteList.contains(addCap)) {
                    throw new PodExceptions.ContainerSecurityContextCapInvalidException(containerName, addCap);
                }
            }
        }
    }

    public void validateContainerPrivileged(String containerName,
                                            ContainerSecurityContext containerSecurityContext,
                                            boolean isV3, boolean isDsContainer) {
        Boolean privileged = false;                             
        if (containerSecurityContext != null && containerSecurityContext.getPrivileged() != null) {
            privileged = containerSecurityContext.getPrivileged();
        }
        List<String> bciContainerSecurityContextPrivilegedEnableUsersList =
                Arrays.asList(bciContainerSecurityContextPrivilegedEnableUsers.split(","));
        String accountId = getAccountId();
        if (!bciContainerSecurityContextPrivilegedEnableUsersList.contains(accountId) && !isV3 && !isDsContainer && privileged) {
            throw new PodExceptions.ContainerSecurityContextCapInvalidException(containerName, "privileged");
        }
    }

    public void validatePfsVolume(List<Pfs> pfs, String vpcCidr) {
        if (!pfs.isEmpty() && podService.isV2AccountId().isIsv2()) {
            // BCI 2.0 使用PFS需要在云桥上开白名单
            if (!podService.isEnablePFS()) {
                throw new PermissionExceptionUtil.PFSPermissionDenyException();
            }

            // BCI 2.0 用户的VPC和BCI VPC不能冲突
            // vpcCidr 在之前已经设置
            LOGGER.debug("vpcCidr:{}, bciVpcCidr:{}, bciClusterCidr:{}", vpcCidr, bciVpcCidr, bciClusterCidr);
            if (vpcCidr != "" && bciVpcCidr != "" && bciClusterCidr != ""
                    && (PodUtils.isSubnetOverlap(vpcCidr, bciVpcCidr)
                    || PodUtils.isSubnetOverlap(vpcCidr, bciClusterCidr))) {
                throw new CommonExceptions.RequestInvalidException("the cidr of user vpc and bci vpc cannot overlap");
            }
        }
    }
}
