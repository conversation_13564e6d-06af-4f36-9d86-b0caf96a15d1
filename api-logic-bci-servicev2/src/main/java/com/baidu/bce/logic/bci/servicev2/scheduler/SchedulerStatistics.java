package com.baidu.bce.logic.bci.servicev2.scheduler;

import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

@Data
@Accessors(chain = true)
@Service("SchedulerStatistics")
public class SchedulerStatistics {
    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulerStatistics.class);
    private ZoneId beijingZone = ZoneId.of("Asia/Shanghai");

    private Map<String, SchedulerStatsItem> schedulerStatsMap = new HashMap<>();

    public class SchedulerStatsItem {
        private String schedulerName = "";
        private SchedulerStatus schedulerStatus = SchedulerStatus.SCHEDULER_INIT;
        private LocalDateTime schedulerInitTime = null;
        private LocalDateTime schedulerFirstRunningTime = null;
        private LocalDateTime schedulerLastRunningTime = null;
        private LocalDateTime schedulerLastFinishedTime = null;
        private long schedulerRunTimes = 0;
        private long totalSchedulerRunCostMillis = 0;

        private long averageSchedulerRunCostMillis = 0;
        private long maxSchedulerRunCostMillis = 0;
        private long minSchedulerRunCostMillis = 0;

        public SchedulerStatsItem(String schedulerName) {
            this.schedulerName = schedulerName;
            this.schedulerStatus = SchedulerStatus.SCHEDULER_INIT;
            this.schedulerInitTime = LocalDateTime.now(beijingZone);
        }

        public void beforeSchedulerRun() {
            schedulerStatus = SchedulerStatus.SCHEDULER_RUNNING;
            if (schedulerFirstRunningTime == null) {
                schedulerFirstRunningTime = LocalDateTime.now(beijingZone);
            }
            schedulerLastRunningTime = LocalDateTime.now(beijingZone);
            schedulerRunTimes++;
            LOGGER.debug("schedulerName:{} is Running, schedulerRunTimes:{},"
                            + " schedulerInitTime:{}, schedulerFirstRunningTime:{},"
                            + " schedulerLastRunningTime:{} schedulerLastFinishedTime:{}",
                    schedulerName, schedulerRunTimes,
                    schedulerInitTime, schedulerFirstRunningTime,
                    schedulerLastRunningTime, schedulerLastFinishedTime);
        }

        public void afterSchedulerRun() {
            schedulerLastFinishedTime = LocalDateTime.now(beijingZone);
            schedulerStatus = SchedulerStatus.SCHEDULER_FINISHED;
            Duration duration = Duration.between(schedulerLastRunningTime,
                    schedulerLastFinishedTime);
            long currentSchedulerRunCostMillis = duration.toMillis();
            totalSchedulerRunCostMillis += currentSchedulerRunCostMillis;
            averageSchedulerRunCostMillis = totalSchedulerRunCostMillis / schedulerRunTimes;
            if (currentSchedulerRunCostMillis > maxSchedulerRunCostMillis) {
                maxSchedulerRunCostMillis = currentSchedulerRunCostMillis;
            }

            if (schedulerRunTimes == 1) {
                minSchedulerRunCostMillis = currentSchedulerRunCostMillis;
            } else if (currentSchedulerRunCostMillis < minSchedulerRunCostMillis) {
                minSchedulerRunCostMillis = currentSchedulerRunCostMillis;
            }

            LOGGER.debug("schedulerName:{} is Finished, schedulerRunTimes:{},"
                            + " currentSchedulerRunCostMillis:{}, totalSchedulerRunCostMillis:{},"
                            + " averageSchedulerRunCostMillis:{},"
                            + " maxSchedulerRunCostMillis:{}, minSchedulerRunCostMillis:{},"
                            + " schedulerInitTime:{}, schedulerFirstRunningTime:{},"
                            + " schedulerLastRunningTime:{}, schedulerLastFinishedTime:{}",
                    schedulerName, schedulerRunTimes,
                    currentSchedulerRunCostMillis, totalSchedulerRunCostMillis,
                    averageSchedulerRunCostMillis,
                    maxSchedulerRunCostMillis, minSchedulerRunCostMillis,
                    schedulerInitTime, schedulerFirstRunningTime,
                    schedulerLastRunningTime, schedulerLastFinishedTime);
        }
    }

    public enum SchedulerStatus {
        SCHEDULER_INIT("Init"),
        SCHEDULER_RUNNING("Running"),
        SCHEDULER_FINISHED("Finished");

        private String status;

        private SchedulerStatus(String status) {
            this.status = status;
        }

        public String getStatus() {
            return status;
        }
    }

    public void registerScheduler(String schedulerName) {
        if (hasRegistered(schedulerName)) {
            LOGGER.error("schedulerName:{} is already registered", schedulerName);
            return;
        }
        schedulerStatsMap.put(schedulerName, new SchedulerStatsItem(schedulerName));
        LOGGER.debug("schedulerName:{} registered successfully", schedulerName);
    }

    public void unRegisterScheduler(String schedulerName) {
        if (!hasRegistered(schedulerName)) {
            LOGGER.error("schedulerName:{} is not registered", schedulerName);
            return;
        }
        schedulerStatsMap.remove(schedulerName);
        LOGGER.debug("schedulerName:{} unregistered successfully", schedulerName);
    }

    public boolean hasRegistered(String schedulerName) {
        return schedulerStatsMap.containsKey(schedulerName);
    }

    public SchedulerStatsItem getSchedulerStatsItem(String schedulerName) {
        return schedulerStatsMap.get(schedulerName);
    }

    public void beforeSchedulerRun(String schedulerName) {
        SchedulerStatsItem schedulerStatsItem = schedulerStatsMap.get(schedulerName);
        if (schedulerStatsItem == null) {
            LOGGER.error("schedulerName:{} is not registered", schedulerName);
            return;
        }
        schedulerStatsItem.beforeSchedulerRun();
    }

    public void afterSchedulerRun(String schedulerName) {
        SchedulerStatsItem schedulerStatsItem = schedulerStatsMap.get(schedulerName);
        if (schedulerStatsItem == null) {
            LOGGER.error("schedulerName:{} is not registered", schedulerName);
            return;
        }
        schedulerStatsItem.afterSchedulerRun();
    }
}
