package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class SecurityGroupModel {
    private String securityGroupId;
    private String name;
    private String description;
    private String vpcId;

    public SecurityGroupModel(SimpleSecurityGroupVO securityGroup) {
        this.securityGroupId = securityGroup.getSecurityGroupId();
        this.name = securityGroup.getName();
        this.description = securityGroup.getDescription();
        this.vpcId =
            (securityGroup.getVpcShortId() == null || securityGroup.getVpcShortId().isEmpty()) ?
            securityGroup.getVpcId() : securityGroup.getVpcShortId();
    }
}