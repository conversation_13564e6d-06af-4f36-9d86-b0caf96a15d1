package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class Bos extends BaseVolume {
    private String bucket;
    private String url;
    private String otherOpts;
    private String ak;
    private String sk;
    // bos是否只读。默认为false。
    private Boolean readOnly = false;
}
