package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.BciSubStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecyleComplete;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.ReschedulingDeleteK8sPodContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("RESCHEDULING_DELETE_K8S_POD")
public class ReschedulingDeleteK8sPodHander extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(ReschedulingDeleteK8sPodHander.class);
    
    private static final List<String> ALLOWED_STATUS = Arrays.asList(
        BciStatus.RESCHEDULING.getStatus()
    );

    private static final List<String> ALLOWED_RESCHEDULING_SUB_STATUS = Arrays.asList(
        BciSubStatus.RESCHEDULING_TOBEDELETED.getSubStatus()
    );

    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof ReschedulingDeleteK8sPodContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        StateMachineContext context = getContext();
        PodPO podPO = context.getPodPO();
        if (!ALLOWED_STATUS.contains(podPO.getStatus())) {
            return false;
        }
        if (BciStatus.RESCHEDULING.getStatus().equalsIgnoreCase(podPO.getStatus())) {
            if (!ALLOWED_RESCHEDULING_SUB_STATUS.contains(podPO.getSubStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 1. 更新pod状态为Rescheduling
     * 2. 更新pod子状态Deleted
     * 3. 更新pod的bci资源版本号
     * @return
     */
    @Override
    public boolean execute() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        StateMachineContext context = getContext();
        PodPO podPO = context.getPodPO();
        podPO.setStatus(BciStatus.RESCHEDULING.getStatus());
        podPO.setSubStatus(BciSubStatus.RESCHEDULING_DELETED.getStatus());
        podPO.setResourceRecycleTimestamp(0L);
        podPO.setResourceRecycleComplete(ResourceRecyleComplete.INCOMPLETE);
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        int updatePodRet = podDao.stateMachineUpdatePod(podPO, podDaoContext);
        if (updatePodRet == 0) {
            return false;
        }
        return true;
    }

    
}