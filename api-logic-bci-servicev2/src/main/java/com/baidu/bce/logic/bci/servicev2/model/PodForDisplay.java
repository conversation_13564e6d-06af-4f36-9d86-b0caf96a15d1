package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.bci.sdk.model.common.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PodForDisplay implements Cloneable {

    private String name = "";
    private String podId = "";
    private String podUuid = "";
    private String status = "";
    @JsonProperty(value = "vCpu")
    private float vCpu = 0;
    private float memory = 0;
    private String eipUuid = "";
    private String publicIp = "";
    private int bandwidthInMbps = 0;
    private String cceUuid = "";
    private String internalIp = "";
    private String securityGroupUuid;
    private String restartPolicy = "";
    private String orderId = "";
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    private String description = "";
    private String region = "";
    private String userId = "";
    private String resourceUuid = "";
    private String taskStatus = "";
    @JsonProperty(value = "tags")
    private List<Tag> podTags;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String subnetUuid;
    private String zoneId = "";
    private String logicalZone = "";
    // 标记是否为v2 pod
    private boolean v2;
}