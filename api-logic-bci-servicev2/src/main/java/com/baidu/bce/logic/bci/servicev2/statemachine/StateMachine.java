package com.baidu.bce.logic.bci.servicev2.statemachine;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.CreateInstanceContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.CreateInstanceHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.DeleteInstanceHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodDeletedByK8SUnexpectedlyHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodDeletedByUserHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodEvictedHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodFailedHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodNoResourceAvailableHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodPendingHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodRunningHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodSucceededHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.K8SPodUnknownHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.OrderFailedHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.ResourceRecycleCompleteHandler;
import com.baidu.bce.logic.bci.servicev2.statemachine.handler.StateMachineEventAbstractHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service("StateMachine")
public class StateMachine {
    private static final Logger LOGGER = LoggerFactory.getLogger(StateMachine.class);

    private Map<StateMachineEvent, Class<? extends StateMachineEventAbstractHandler>> eventHandlerMap;
    private final ApplicationContext applicationContext;

    @Autowired
    public StateMachine(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        this.eventHandlerMap = new ConcurrentHashMap<>();
        // 注册状态机事件处理函数
        this.eventHandlerMap.put(StateMachineEvent.DELETE_INSTANCE, DeleteInstanceHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.CREATE_INSTANCE, CreateInstanceHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.ORDER_FAILED, OrderFailedHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.RESOURCE_RECYCLE_COMPLETE, ResourceRecycleCompleteHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_DELETED_BY_USER, K8SPodDeletedByUserHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_DELETED_BY_K8S_UNEXPECTEDLY, K8SPodDeletedByK8SUnexpectedlyHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_PENDING, K8SPodPendingHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_RUNNING, K8SPodRunningHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_SUCCEEDED, K8SPodSucceededHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_FAILED, K8SPodFailedHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_UNKNOWN, K8SPodUnknownHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_EVICTED, K8SPodEvictedHandler.class);
        this.eventHandlerMap.put(StateMachineEvent.K8S_POD_NO_RESOURCE_AVAILABLE, K8SPodNoResourceAvailableHandler.class);
    }

    public StateMachineEventAbstractHandler getEventHandler(String podId, StateMachineEvent event) {
        Class<? extends StateMachineEventAbstractHandler> handlerClass = eventHandlerMap.get(event);
        if (handlerClass != null) {
            AutowireCapableBeanFactory autowireCapableBeanFactory = applicationContext.getAutowireCapableBeanFactory();
            return autowireCapableBeanFactory.createBean(handlerClass);
        }
        LOGGER.error("StateMachine getEventHandler failed not found event, podId:{} event:{}",
                podId, event);
        return null;
    }

    public boolean trigger(String podId, StateMachineEvent event, StateMachineEventContext eventContext) {
        try {
            StateMachineEventAbstractHandler eventHandler = getEventHandler(podId, event);
            if (eventHandler == null) {
                LOGGER.error("StateMachine trigger getEventHandler failed, podId:{} event:{}",
                        podId, event);
                return false;
            }
            boolean initResult = eventHandler.init(podId, event, eventContext);
            if (!initResult) {
                LOGGER.error("StateMachine trigger eventHandler init failed, podId:{} event:{}",
                        podId, event);
                return initResult;
            }
            boolean checkEventContextResult = eventHandler.checkEventContext();
            if (!checkEventContextResult) {
                LOGGER.error("StateMachine trigger eventHandler checkEventContext failed, podId:{} event:{}",
                        podId, event);
                return checkEventContextResult;
            }
            boolean checkResult = eventHandler.check();
            if (!checkResult) {
                LOGGER.error("StateMachine trigger eventHandler check failed, podId:{} event:{}",
                        podId, event);
                return checkResult;
            }
            boolean executeResult = eventHandler.execute();
            if (!executeResult) {
                LOGGER.error("StateMachine trigger eventHandler execute failed, podId:{} event:{}",
                        podId, event);
                return executeResult;
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("StateMachine trigger failed with Exception, podId:{} event:{}",
                    podId, event, e);
            return false;
        }
    }

    public boolean trigger(PodPO podPO, StateMachineEvent event, StateMachineEventContext eventContext) {
        String podId = podPO.getPodId();
        try {
            StateMachineEventAbstractHandler eventHandler = getEventHandler(podId, event);
            if (eventHandler == null) {
                LOGGER.error("StateMachine trigger getEventHandler failed, podId:{} event:{}",
                        podId, event);
                return false;
            }
            boolean initResult = eventHandler.init(podPO, event, eventContext);
            if (!initResult) {
                LOGGER.error("StateMachine trigger eventHandler init failed, podId:{} event:{}",
                        podId, event);
                return initResult;
            }
            boolean checkEventContextResult = eventHandler.checkEventContext();
            if (!checkEventContextResult) {
                LOGGER.error("StateMachine trigger eventHandler checkEventContext failed, podId:{} event:{}",
                        podId, event);
                return checkEventContextResult;
            }
            boolean checkResult = eventHandler.check();
            if (!checkResult) {
                LOGGER.error("StateMachine trigger eventHandler check failed, podId:{} event:{}",
                        podId, event);
                return checkResult;
            }
            boolean executeResult = eventHandler.execute();
            if (!executeResult) {
                LOGGER.error("StateMachine trigger eventHandler execute failed, podId:{} event:{}",
                        podId, event);
                return executeResult;
            }
            return true;
        } catch (DuplicateKeyException e) {
            // 处理 DuplicateKeyException
            Throwable rootCause = e.getRootCause();
            // 目前仅处理创建Pod的事件
            if (event == StateMachineEvent.CREATE_INSTANCE) {
                LOGGER.error("StateMachine trigger failed with DuplicateKeyException, podId:{} event:{} Duplicate" +
                                " entry detected:{}",
                        podId, event, rootCause.getMessage());
                CreateInstanceContext createEventContext = (CreateInstanceContext) eventContext;
                createEventContext.getException().setException(new PodExceptions.CreateInstanceDuplicateKeyException());
            } else {
                LOGGER.error("StateMachine trigger failed with DuplicateKeyException, podId:{} event:{}",
                        podId, event, e);
            }
            return false;
        } catch (Exception e) {
            LOGGER.error("StateMachine trigger failed with Exception, podId:{} event:{}",
                    podId, event, e);
            return false;
        }
    }
}
