package com.baidu.bce.logic.bci.servicev2.k8s;

import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException.ErrorCode;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDResponse;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDResponseList;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDGetResponse;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDRequestList;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import io.kubernetes.client.custom.V1Patch;
import io.kubernetes.client.informer.ResourceEventHandler;
import io.kubernetes.client.informer.SharedIndexInformer;
import io.kubernetes.client.informer.SharedInformerFactory;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.apis.CustomObjectsApi;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1ConfigMapList;
import io.kubernetes.client.openapi.models.V1DaemonSet;
import io.kubernetes.client.openapi.models.V1DaemonSetList;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1DeploymentList;
import io.kubernetes.client.openapi.models.V1Namespace;
import io.kubernetes.client.openapi.models.V1NamespaceList;
import io.kubernetes.client.openapi.models.V1NamespaceSpec;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1NodeList;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodList;
import io.kubernetes.client.openapi.models.V1Secret;
import io.kubernetes.client.openapi.models.V1SecretList;
import io.kubernetes.client.util.CallGenerator;
import io.kubernetes.client.util.CallGeneratorParams;
import io.kubernetes.client.util.PatchUtils;
import lombok.Data;
import okhttp3.Call;
import okhttp3.Response;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 负责单个K8S集群所有资源的读、写、watch等操作。
 */
@Data
public class K8sCluster {
    private static final Logger LOGGER = LoggerFactory.getLogger(K8sCluster.class);
    private static final String DEFAULT_FIELD_MANAGER = "BCI";
    private static final String IMAGE_ACCELERATE_CRD_GROUP = "image.bci.cloud.baidu.com";
    private static final String IMAGE_ACCELERATE_CRD_VERSION = "v1";
    private static final String IMAGE_ACCELERATE_CRD_PLURAL = "imagecaches";
    private static final int IMAGE_ACCELERATE_CRD_DELETE_GRACE_SECONDS = 300;

    // 维护访问k8s的相关配置及Token等
    private ApiClient apiClient;
    // 操作Pod等核心资源的客户端，如有非核心分组的资源操作需求，需要单独创建新的客户端
    private CoreV1Api coreV1Api;
    private AppsV1Api appsV1Api;
    private CustomObjectsApi customObjectsApi;
    // 主要用来监听资源变更事件和大部分读操作
    private SharedInformerFactory factory;
    // k8sCluster是否已经启动
    private boolean started = false;

    // only used in ut
    public K8sCluster() {
    }

    public K8sCluster(ApiClient kubeApiClient) {
        this.apiClient = kubeApiClient;
        this.coreV1Api = new CoreV1Api(kubeApiClient);
        this.appsV1Api = new AppsV1Api(kubeApiClient);
        this.customObjectsApi = new CustomObjectsApi(kubeApiClient);
        this.factory = new SharedInformerFactory(apiClient);
    }

    // 在k8s集群启动之后无法调用
    public void registerPodEventHandlers(List<ResourceEventHandler<V1Pod>> handlers) throws K8sServiceException {
        LOGGER.info("begin to register pod event handlers: {}.", handlers);
        if (started == true) {
            throw new K8sServiceException(ErrorCode.K8S_CLUSTER_STARTED,
                    "pod event handlers should be registered before starting k8s cluster.");
        }
        SharedIndexInformer<V1Pod> podInformer = factory.getExistingSharedIndexInformer(V1Pod.class);
        if (podInformer == null) {
            podInformer = newPodInformer();
        }
        for (ResourceEventHandler<V1Pod> handler : handlers) {
            podInformer.addEventHandler(handler);
        }
        LOGGER.info("end to register pod event handlers successfully.");
    }

    public void registerOpsTaskEventHandlers(List<ResourceEventHandler<OpsTaskCRDRequest>> handlers)
            throws K8sServiceException {
        LOGGER.info("begin to register opsTask event handlers: {}.", handlers);
        if (started) {
            throw new K8sServiceException(ErrorCode.K8S_CLUSTER_STARTED,
                    "opsTask event handlers should be registered before starting k8s cluster.");
        }
        SharedIndexInformer<OpsTaskCRDRequest> opsTaskInformer =
                factory.getExistingSharedIndexInformer(OpsTaskCRDRequest.class);
        if (opsTaskInformer == null) {
            opsTaskInformer = newOpsTaskInformer();
        }
        for (ResourceEventHandler<OpsTaskCRDRequest> handler : handlers) {
            opsTaskInformer.addEventHandler(handler);
        }
        LOGGER.info("end to register opsTask event handlers successfully.");
    }

    public void start() throws InterruptedException {
        LOGGER.info("begin to start k8s cluster.");
        // 初始化 informer
        if (factory.getExistingSharedIndexInformer(V1Pod.class) == null) {
            newPodInformer();
        }
        if (factory.getExistingSharedIndexInformer(V1ConfigMap.class) == null) {
            newConfigMapInformer();
        }
        if (factory.getExistingSharedIndexInformer(V1Deployment.class) == null) {
            newDeploymentInformer();
        }
        if (factory.getExistingSharedIndexInformer(V1Namespace.class) == null) {
            newNamespaceInformer();
        }
        if (factory.getExistingSharedIndexInformer(V1Secret.class) == null) {
            newSecretInformer();
        }
        if (factory.getExistingSharedIndexInformer(V1Node.class) == null) {
            newNodeInformer();
        }
        if (factory.getExistingSharedIndexInformer(OpsTaskCRDRequest.class) == null) {
            newOpsTaskInformer();
        }
        if (factory.getExistingSharedIndexInformer(V1DaemonSet.class) == null) {
            newDaemonSetInformer();
        }
        // start all registered informers
        factory.startAllRegisteredInformers();

        // wait podInformer synced
        SharedIndexInformer<V1Pod> podInformer = factory.getExistingSharedIndexInformer(V1Pod.class);
        while (!podInformer.hasSynced()) {
            Thread.sleep(1000);
            LOGGER.info("startting k8s cluster and waiting pod informer synced.");
        }
        SharedIndexInformer<V1ConfigMap> configMapInformer = factory.getExistingSharedIndexInformer(V1ConfigMap.class);
        while (!configMapInformer.hasSynced()) {
            Thread.sleep(1000);
            LOGGER.info("startting k8s cluster and waiting configMap informer synced.");
        }
        this.started = true;
        LOGGER.info("end to start k8s cluster successfully.");
    }

    public void stop() {
        LOGGER.info("begin to stop k8s cluster.");
        this.factory.stopAllRegisteredInformers();
        this.started = false;
        LOGGER.info("end to stop k8s cluster successfully.");
    }

    public V1Node getNode(String name) {
        SharedIndexInformer<V1Node> nodeInformer = factory.getExistingSharedIndexInformer(V1Node.class);
        if (nodeInformer == null) {
            nodeInformer = newNodeInformer();
        }
        V1Node input = new V1Node();
        input.setMetadata(genSimpleObjectMeta(name, null));
        return (V1Node) (nodeInformer.getIndexer().get(input));
    }

    public List<V1Node> listNodes() {
        SharedIndexInformer<V1Node> nodeInformer = factory.getExistingSharedIndexInformer(V1Node.class);
        if (nodeInformer == null) {
            nodeInformer = newNodeInformer();
        }
        List<V1Node> nodes = nodeInformer.getIndexer().list();
        return nodes;
    }

    public V1Namespace getNameSpace(String name) {
        SharedIndexInformer<V1Namespace> namespaceInformer = factory.getExistingSharedIndexInformer(V1Namespace.class);
        if (namespaceInformer == null) {
            namespaceInformer = newNamespaceInformer();
        }
        V1Namespace input = new V1Namespace();
        input.setMetadata(genSimpleObjectMeta(name, null));
        return (V1Namespace) (namespaceInformer.getIndexer().get(input));
    }

    public boolean createNamespace(String name) throws K8sServiceException {
        Map<String, String> labels = new HashMap<>();
        labels.put("bci_internal_accountID", name);
        V1ObjectMeta meta = genSimpleObjectMeta(name, null);
        meta.setLabels(labels);

        V1Namespace newNamespace = new V1Namespace();
        newNamespace.setApiVersion("v1");
        newNamespace.setKind("Namespace");
        newNamespace.setMetadata(meta);
        newNamespace.setSpec(new V1NamespaceSpec());
        try {
            coreV1Api.createNamespace(newNamespace, null, null, DEFAULT_FIELD_MANAGER, null);
        } catch (ApiException e) {
            throw new K8sServiceException(ErrorCode.CREATE_NAMESPACE_FAILED, e.getMessage());
        }
        LOGGER.debug("create namespace {} successfully", name);
        return true;
    }

    public V1Secret getSecret(String namespace, String secretName) {
        SharedIndexInformer<V1Secret> secretInformer = factory.getExistingSharedIndexInformer(V1Secret.class);
        if (secretInformer == null) {
            secretInformer = newSecretInformer();
        }
        V1Secret input = new V1Secret();
        input.metadata(genSimpleObjectMeta(secretName, namespace));
        if (secretInformer.getIndexer().get(input) != null) {
            return (V1Secret) (secretInformer.getIndexer().get(input));
        }
        return null;
    }

    public void createSecret(V1Secret secret) throws K8sServiceException {
        try {
            LOGGER.debug("secret {} will be created", secret.getMetadata().getName());
            coreV1Api.createNamespacedSecret(secret.getMetadata().getNamespace(), secret, null, null,
                    DEFAULT_FIELD_MANAGER, null);
        } catch (ApiException e) {
            LOGGER.error("create secret {} failed, error:{}", secret.getMetadata().getName(), e.getResponseBody());
            throw new K8sServiceException(ErrorCode.CREATE_SECRET_FAILED, e.getMessage());
        }
    }

    public boolean deleteSecret(String namespace, String secretName) {
        try {
            coreV1Api.deleteNamespacedSecret(secretName, namespace, null, null, null, null, null, null);
            return true;
        } catch (ApiException e) {
            LOGGER.error("delete secret {} error:{}", secretName, e.getResponseBody());
            return false;
        }
    }

    public Response getPodLogWithResponse(String podName, String namespace, String container, Boolean follow,
                                          Boolean insecureSkipTLSVerifyBackend, Integer limitBytes, String pretty,
                                          Boolean previous, Integer sinceSeconds, Integer tailLines, Boolean timestamps)
            throws K8sServiceException, IOException, ApiException {
        return coreV1Api.readNamespacedPodLogCall(podName, namespace, container, follow,
                insecureSkipTLSVerifyBackend, limitBytes, pretty, previous, sinceSeconds, tailLines,
                timestamps, null).execute();
    }

    public V1Pod getPod(String namespace, String podName) {
        SharedIndexInformer<V1Pod> podInformer = factory.getExistingSharedIndexInformer(V1Pod.class);
        if (podInformer == null) {
            podInformer = newPodInformer();
        }
        V1Pod input = new V1Pod();
        input.metadata(genSimpleObjectMeta(podName, namespace));
        if (podInformer.getIndexer().get(input) != null) {
            return (V1Pod) (podInformer.getIndexer().get(input));
        }
        return null;
    }

    public List<V1Pod> listPods(String namespace) {
        V1PodList pl = null;
        try {
            pl = coreV1Api.listNamespacedPod(namespace, null, null, null,
                    null, null, Integer.MAX_VALUE, null,
                    null, Integer.MAX_VALUE, false);
        } catch (ApiException e) {
            return new ArrayList<>();
        }
        return pl.getItems();
    }

    public List<V1Pod> listAllPods() {
        SharedIndexInformer<V1Pod> podInformer = factory.getExistingSharedIndexInformer(V1Pod.class);
        if (podInformer == null) {
            podInformer = newPodInformer();
        }
        List<V1Pod> pods = podInformer.getIndexer().list();
        return pods;
    }

    public void createPod(V1Pod pod) throws K8sServiceException {
        try {
            LOGGER.debug("pod detail is,{}:{}", pod.getMetadata().getName(),
                    JsonUtil.toJSON(pod));

            coreV1Api.createNamespacedPod(pod.getMetadata().getNamespace(), pod, null, null,
                    DEFAULT_FIELD_MANAGER, null);
        } catch (ApiException e) {
            LOGGER.error("K8sService create pod {} failed, error:{}", pod.getMetadata().getName(), e.getResponseBody());
            if (e.getCode() == HttpStatus.SC_CONFLICT) {
                throw new K8sServiceException(ErrorCode.POD_EXISTED, e.getMessage(), e.getResponseBody());
            } else {
                throw new K8sServiceException(ErrorCode.CREATE_POD_FAILED, e.getMessage(), e.getResponseBody());
            }
        }
    }

    public void updatePod(final String namespace, final String name, V1Pod v1Pod) throws K8sServiceException {
        try {
            LOGGER.debug("updatePod pod detail is, namespace {}, name {}, v1Pod {}", namespace, name, JsonUtil.toJSON(v1Pod));
            coreV1Api.replaceNamespacedPod(name, namespace, v1Pod, null, null, null, null);
        } catch (ApiException e) {
            LOGGER.error("updatePod pod {} failed, error is {}", name, e.getResponseBody());
            throw new K8sServiceException(ErrorCode.UPDATE_POD_FAILED, e.getMessage());
        }
    }

    public boolean deletePod(String podName, String namespace) throws ApiException { // 保留k8s原生的exception，里面信息较多
        try {
            coreV1Api.deleteNamespacedPod(podName, namespace, null, null, null, null, null, null);
            return true;
        } catch (ApiException e) {
            LOGGER.error("delete pod {} error:{}", podName, e.getResponseBody());
            return false;
        }
    }

    public V1DaemonSet getDaemonSet(String namespace, String name) {
        SharedIndexInformer<V1DaemonSet> daemonSetInformer = factory.getExistingSharedIndexInformer(V1DaemonSet.class);
        if (daemonSetInformer == null) {
            daemonSetInformer = newDaemonSetInformer();
        }
        V1DaemonSet input = new V1DaemonSet();
        input.metadata(genSimpleObjectMeta(name, namespace));
        if (daemonSetInformer.getIndexer().get(input) != null) {
            return (V1DaemonSet) (daemonSetInformer.getIndexer().get(input));
        }
        return null;
    }

    public void createDaemonSet(V1DaemonSet daemonSet) throws K8sServiceException {
        try {
            LOGGER.debug("K8sCluster createDaemonSet name:{} namespace:{} daemonSet:{}",
                    daemonSet.getMetadata().getName(),
                    daemonSet.getMetadata().getNamespace(),
                    JsonUtil.toJSONString(daemonSet));
            appsV1Api.createNamespacedDaemonSet(daemonSet.getMetadata().getNamespace(), daemonSet, null, null,
                    DEFAULT_FIELD_MANAGER, null);
        } catch (ApiException e) {
            LOGGER.error("K8sService createDaemonSet name:{}, namespace:{} failed, error:{}",
                    daemonSet.getMetadata().getName(),
                    daemonSet.getMetadata().getNamespace(),
                    e.getResponseBody());
            if (e.getCode() == HttpStatus.SC_CONFLICT) {
                LOGGER.error("K8sService createDaemonSet name:{}, namespace:{} failed, "
                                + " Because the resources already exist, error:{}",
                        daemonSet.getMetadata().getName(),
                        daemonSet.getMetadata().getNamespace(),
                        e.getResponseBody());
                throw new K8sServiceException(ErrorCode.DAEMONSET_EXISTED, e.getMessage(), e.getResponseBody());
            } else {
                LOGGER.error("K8sService createDaemonSet name:{}, namespace:{} failed error:{}",
                        daemonSet.getMetadata().getName(),
                        daemonSet.getMetadata().getNamespace(),
                        e.getResponseBody());
                throw new K8sServiceException(ErrorCode.CREATE_DAEMONSET_FAILED, e.getMessage(), e.getResponseBody());
            }
        }
    }

    public void createImageAccCRD(String namespace, ImageAccelerateCRDRequest imageAccelerateCRDRequest) {
        Object object = JsonUtil.convertValue(imageAccelerateCRDRequest, Object.class);
        try {
            customObjectsApi.createNamespacedCustomObject(IMAGE_ACCELERATE_CRD_GROUP,
                    IMAGE_ACCELERATE_CRD_VERSION, namespace, IMAGE_ACCELERATE_CRD_PLURAL, object,
                    null, null, null);
        } catch (ApiException e) {
            LOGGER.error("create crd {} in namespace {} error:{}", imageAccelerateCRDRequest, namespace,
                    e.getResponseBody());
        }
    }

    public void createOpsTaskCRD(String namespace, OpsTaskCRDRequest opsTaskCRDRequest) throws ApiException {
        Object object = JsonUtil.convertValue(opsTaskCRDRequest, Object.class);
        try {
            customObjectsApi.createNamespacedCustomObject(OpsTaskCRDRequest.OPS_TASK_CRD_GROUP,
                    OpsTaskCRDRequest.OPS_TASK_CRD_VERSION, namespace, OpsTaskCRDRequest.OPS_TASK_CRD_PLURAL, object,
                    null, null, null);
        } catch (ApiException e) {
            LOGGER.error("create opsTask crd {} in namespace {} error:{}", opsTaskCRDRequest, namespace,
                    e.getResponseBody());
            throw e;
        }
    }

    public void patchOpsTaskCRDAnnotation(final String namespace, final String name,
                                                       final String patchStr) throws ApiException {
        try {
            PatchUtils.patch(OpsTaskCRDRequest.class, new PatchUtils.PatchCallFunc() {
                @Override
                public Call getCall() throws ApiException {
                    return customObjectsApi.patchNamespacedCustomObjectCall(OpsTaskCRDRequest.OPS_TASK_CRD_GROUP,
                            OpsTaskCRDRequest.OPS_TASK_CRD_VERSION, namespace, OpsTaskCRDRequest.OPS_TASK_CRD_PLURAL,
                            name, new V1Patch(patchStr), null, null, null, null);
                }
            }, V1Patch.PATCH_FORMAT_JSON_MERGE_PATCH, apiClient);
        } catch (ApiException e) {
            LOGGER.error("patch opsTask crd {} in namespace {} value {} error:{}", name, namespace, patchStr,
                    e.getResponseBody());
            throw e;
        }
    }

    public OpsTaskCRDGetResponse getOpsTaskCRD(String namespace, String name) {
        try {
            Object namespacedCustomObject =
                    customObjectsApi.getNamespacedCustomObject(OpsTaskCRDRequest.OPS_TASK_CRD_GROUP,
                            OpsTaskCRDRequest.OPS_TASK_CRD_VERSION, namespace, OpsTaskCRDRequest.OPS_TASK_CRD_PLURAL,
                            name);
            return JsonUtil.convertValue(namespacedCustomObject, OpsTaskCRDGetResponse.class);
        } catch (Exception e) {
            LOGGER.error("get opsTask crd {} error: {}", name, e);
            return null;
        }
    }

    public void deleteImageAccCRD(String namespace, String name) {
        try {
            customObjectsApi.deleteNamespacedCustomObject(IMAGE_ACCELERATE_CRD_GROUP,
                    IMAGE_ACCELERATE_CRD_VERSION, namespace, IMAGE_ACCELERATE_CRD_PLURAL, name,
                    IMAGE_ACCELERATE_CRD_DELETE_GRACE_SECONDS, null, null, null, null);
        } catch (ApiException e) {
            LOGGER.error("delete crd {} in namespace {} error:{}", name, namespace, e.getResponseBody());
        }
    }

    public void deleteOpsTaskCRD(String namespace, String name) throws ApiException {
        try {
            customObjectsApi.deleteNamespacedCustomObject(OpsTaskCRDRequest.OPS_TASK_CRD_GROUP,
                    OpsTaskCRDRequest.OPS_TASK_CRD_VERSION, namespace, OpsTaskCRDRequest.OPS_TASK_CRD_PLURAL, name,
                    null, null, null, null, null);
        } catch (ApiException e) {
            LOGGER.error("delete opsTask crd {} in namespace {} error:{}", name, namespace, e.getResponseBody());
            throw e;
        }
    }

    public void patchImageAccCRD(String namespace, String name, ImageAccelerateCRDRequest imageAccelerateCRDRequest) {
        Object object = JsonUtil.convertValue(imageAccelerateCRDRequest, Object.class);
        try {
            customObjectsApi.replaceNamespacedCustomObject(IMAGE_ACCELERATE_CRD_GROUP,
                    IMAGE_ACCELERATE_CRD_VERSION, namespace, IMAGE_ACCELERATE_CRD_PLURAL, name, object,
                    null, null);
        } catch (ApiException e) {
            LOGGER.error("patch crd {} in namespace {} error:{}", imageAccelerateCRDRequest, namespace,
                    e.getResponseBody());
        }
    }

    public ImageAccelerateCRDResponse getImageAccCRD(String namespace, String name) {
        try {
            Object o = customObjectsApi.getNamespacedCustomObject(IMAGE_ACCELERATE_CRD_GROUP,
                    IMAGE_ACCELERATE_CRD_VERSION, namespace, IMAGE_ACCELERATE_CRD_PLURAL, name);
            return JsonUtil.convertValue(o, ImageAccelerateCRDResponse.class);
        } catch (ApiException e) {
            LOGGER.error("get crd {} in namespsace {} error:{}", name, namespace, e.getResponseBody());
            return null;
        }
    }

    public ImageAccelerateCRDResponseList listImageAccCRDs(String cceClusterId) {
        try {
            Object o = customObjectsApi.listClusterCustomObject(IMAGE_ACCELERATE_CRD_GROUP,
                    IMAGE_ACCELERATE_CRD_VERSION, IMAGE_ACCELERATE_CRD_PLURAL, null, null, null, null, null, null, null,
                    null, null, null);
            return JsonUtil.convertValue(o, ImageAccelerateCRDResponseList.class);
        } catch (ApiException e) {
            LOGGER.error("list cluster {} all image accelerate crd error:{}", cceClusterId, e.getResponseBody());
            return null;
        }
    }

    public V1ConfigMap getConfigMap(String name, String namespace) {
        SharedIndexInformer<V1ConfigMap> configMapInformer = factory.getExistingSharedIndexInformer(V1ConfigMap.class);
        if (configMapInformer == null) {
            configMapInformer = newConfigMapInformer();
        }
        V1ConfigMap input = new V1ConfigMap();
        input.metadata(genSimpleObjectMeta(name, namespace));
        if (configMapInformer.getIndexer().get(input) != null) {
            return (V1ConfigMap) (configMapInformer.getIndexer().get(input));
        }
        return null;
    }

    public List<V1ConfigMap> listConfigMaps(String namespace) {
        V1ConfigMapList cm = null;
        try {
            cm = coreV1Api.listNamespacedConfigMap(namespace, null, null, null,
                    null, null, Integer.MAX_VALUE, null,
                    null, Integer.MAX_VALUE, false);
        } catch (ApiException e) {
            return new ArrayList<>();
        }
        return cm.getItems();
    }

    public boolean updateConfigMap(String namespace, String name, V1ConfigMap configMap) {
        try {
            coreV1Api.replaceNamespacedConfigMap(name, namespace, configMap, null, null, null, null);
        } catch (ApiException e) {
            LOGGER.error("update configMap {} in namespace {} error:{}", configMap, namespace, e.getResponseBody());
            return false;
        }
        return true;
    }

    public void createConfigMap(V1ConfigMap configMap) throws K8sServiceException {
        try {
            coreV1Api.createNamespacedConfigMap(configMap.getMetadata().getNamespace(), configMap, null, null,
                    DEFAULT_FIELD_MANAGER, null);
        } catch (ApiException e) {
            LOGGER.error("create configMap {} in namespace {} Exception message:{} responseCode:{} responseBody:{}",
                    configMap.getMetadata().getName(),
                    configMap.getMetadata().getNamespace(),
                    e.getMessage(), e.getCode(), e.getResponseBody());
            String exceptionMessage =
                    "Message:" + e.getMessage() + " code:" + e.getCode() + " responseBody:" + e.getResponseBody();
            throw new K8sServiceException(ErrorCode.CREATE_CONFIGMAP_FAILED, exceptionMessage);
        }
    }

    public void deleteConfigMap(String name, String namespace) throws K8sServiceException {
        try {
            if (getConfigMap(name, namespace) == null) {
                return;
            }
            coreV1Api.deleteNamespacedConfigMap(name, namespace, null, null, null, null, null, null);
        } catch (ApiException e) {
            // 删除configmap失败后由定时清理任务删除
            LOGGER.error("delete configmap {} error:{}", name, e);
        }
    }

    public V1Deployment getDeployment(String name, String namespace) {
        SharedIndexInformer<V1Deployment> deploymentInformer = factory.getExistingSharedIndexInformer(V1Deployment.class);
        if (deploymentInformer == null) {
            deploymentInformer = newDeploymentInformer();
        }
        V1Deployment input = new V1Deployment();
        input.metadata(genSimpleObjectMeta(name, namespace));
        if (deploymentInformer.getIndexer().get(input) != null) {
            return (V1Deployment) (deploymentInformer.getIndexer().get(input));
        }
        return null;
    }

    public List<V1Deployment> listDeployments(String namespace) {
        V1DeploymentList deploy = null;
        try {
            deploy = appsV1Api.listNamespacedDeployment(namespace, null, null, null,
                    null, null, Integer.MAX_VALUE, null,
                    null, Integer.MAX_VALUE, false);
        } catch (ApiException e) {
            return new ArrayList<>();
        }
        return deploy.getItems();
    } 

    private SharedIndexInformer<V1Namespace> newNamespaceInformer() {
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return coreV1Api.listNamespaceCall(null, null, null, null, null, null, params.resourceVersion,
                                null, params.timeoutSeconds, params.watch, null);
                    }
                }, V1Namespace.class, V1NamespaceList.class);
    }

    private SharedIndexInformer<V1Node> newNodeInformer() {
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return coreV1Api.listNodeCall(null, null, null, null, null, null,
                                params.resourceVersion, null, params.timeoutSeconds,
                                params.watch, null);
                    }
                }, V1Node.class, V1NodeList.class);
    }

    private SharedIndexInformer<V1Pod> newPodInformer() {
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return coreV1Api.listPodForAllNamespacesCall(null, null, null, null, null, null,
                                params.resourceVersion, null, params.timeoutSeconds,
                                params.watch, null);
                    }
                }, V1Pod.class, V1PodList.class);
    }

    private SharedIndexInformer<V1DaemonSet> newDaemonSetInformer() {
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return appsV1Api.listDaemonSetForAllNamespacesCall(null, null, null, null, null, null,
                                params.resourceVersion, null, params.timeoutSeconds,
                                params.watch, null);
                    }
                }, V1DaemonSet.class, V1DaemonSetList.class);
    }

    private SharedIndexInformer<V1Secret> newSecretInformer() {
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return coreV1Api.listSecretForAllNamespacesCall(null, null, null, null, null, null,
                                params.resourceVersion, null, params.timeoutSeconds,
                                params.watch, null);
                    }
                }, V1Secret.class, V1SecretList.class);
    }

    private SharedIndexInformer<V1ConfigMap> newConfigMapInformer() {
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return coreV1Api.listConfigMapForAllNamespacesCall(null, null, null, null, null, null,
                                params.resourceVersion, null,
                                params.timeoutSeconds, params.watch, null);
                    }
                }, V1ConfigMap.class, V1ConfigMapList.class);
    }

    private SharedIndexInformer<V1Deployment> newDeploymentInformer() {
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return appsV1Api.listDeploymentForAllNamespacesCall(null, null, null, null, null, null,
                                params.resourceVersion, null,
                                params.timeoutSeconds, params.watch, null);
                    }
                }, V1Deployment.class, V1DeploymentList.class);
    }

    private SharedIndexInformer<OpsTaskCRDRequest> newOpsTaskInformer() {
        // TODO 待测试
        return factory.sharedIndexInformerFor(
                new CallGenerator() {
                    @Override
                    public Call generate(CallGeneratorParams params) throws ApiException {
                        return customObjectsApi.listClusterCustomObjectCall(OpsTaskCRDRequest.OPS_TASK_CRD_GROUP,
                                OpsTaskCRDRequest.OPS_TASK_CRD_VERSION, OpsTaskCRDRequest.OPS_TASK_CRD_PLURAL,
                                null, null, null, null, null, null, params.resourceVersion, null, params.timeoutSeconds,
                                params.watch, null);
                    }
                }, OpsTaskCRDRequest.class, OpsTaskCRDRequestList.class);
    }

    private V1ObjectMeta genSimpleObjectMeta(String name, String namespace) {
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setNamespace(namespace);
        meta.name(name);
        return meta;
    }

    // 必须加get set方法，否则json序列化会出错 jackson库的一个问题

    public static Logger getLOGGER() {
        return LOGGER;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public CoreV1Api getCoreV1Api() {
        return coreV1Api;
    }

    public void setCoreV1Api(CoreV1Api coreV1Api) {
        this.coreV1Api = coreV1Api;
    }

    public SharedInformerFactory getFactory() {
        return factory;
    }

    public void setFactory(SharedInformerFactory factory) {
        this.factory = factory;
    }

    public boolean isStarted() {
        return started;
    }

    public void setStarted(boolean started) {
        this.started = started;
    }

    public CustomObjectsApi getCustomObjectsApi() {
        return customObjectsApi;
    }

    public void setCustomObjectsApi(CustomObjectsApi customObjectsApi) {
        this.customObjectsApi = customObjectsApi;
    }
}