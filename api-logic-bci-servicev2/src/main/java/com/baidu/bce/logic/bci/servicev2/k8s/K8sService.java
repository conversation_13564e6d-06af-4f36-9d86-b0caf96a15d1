package com.baidu.bce.logic.bci.servicev2.k8s;

import com.baidu.bce.fbi.common.Pair;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.daov2.ccecluster.CceClusterDao;
import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.SidecarConstant;
import com.baidu.bce.logic.bci.servicev2.exception.OpsExceptions;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException.ErrorCode;
import com.baidu.bce.logic.bci.servicev2.model.BatchCreatePodsResult;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDResponse;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDResponseList;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDGetResponse;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.PatchValue;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import com.baidu.bce.logic.bci.servicev2.sync.service.OpsTaskSyncService;
import com.baidu.bce.logic.bci.servicev2.sync.service.PodContainerSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.FileUtil;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.core.constants.HttpStatus;
import io.kubernetes.client.common.KubernetesObject;
import io.kubernetes.client.informer.ResourceEventHandler;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1DaemonSet;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1Secret;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class K8sService {
    private static final Logger LOGGER = LoggerFactory.getLogger(K8sCluster.class);
    private HashMap<String, K8sCluster> k8sClusters = new HashMap<>();

    @Autowired
    private CceClusterDao clusterDao;
    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private PodDaoV2 podDaoV2;

    @Autowired
    private PodExtraDaoV2 podExtraDaoV2;

    @Autowired
    protected LogicPodClientFactoryV2 logicPodClientFactory;

    /*
     * 所有需要监听Pod事件的组件，都需要在这里注册。
     * K8sService 会依次调用上述组件的 getPodEventHandlers 函数，并将获取的事件处理者注册到 PodInformer。
     */
    @Autowired
    private PodContainerSyncServiceV2 podContainerSyncService;

    @Autowired
    private OpsTaskSyncService opsTaskSyncService;

    @Autowired
    private SidecarConstant sidecarConstant;

    private List<ResourceEventHandler<V1Pod>> getAllPodEventHandlers() {
        List<ResourceEventHandler<V1Pod>> handlers = new ArrayList<>();
        handlers.add(podContainerSyncService.getPodEventHandler());
        return handlers;
    }

    private List<ResourceEventHandler<OpsTaskCRDRequest>> getAllOpsTaskEventHandlers() {
        List<ResourceEventHandler<OpsTaskCRDRequest>> handlers = new ArrayList<>();
        handlers.add(opsTaskSyncService.getOpsTaskCRDEventHandler());
        return handlers;
    }

    @PostConstruct
    public void start() throws K8sServiceException, InterruptedException, IOException {
        try {
            List<ResourceEventHandler<V1Pod>> eventHandlers;
            List<ResourceEventHandler<OpsTaskCRDRequest>> allOpsTaskEventHandlers;
            for (CceCluster cluster : clusterDao.getAllCceClusters()) {
                // 给每个集群分配独立的 eventHandler, 以便于出现异常时区分不同集群
                eventHandlers = getAllPodEventHandlers();
                allOpsTaskEventHandlers = getAllOpsTaskEventHandlers();
                LOGGER.debug("will new K8sCluster {}", cluster.toString());
                K8sCluster k8sCluster = new K8sCluster(genApiClient(cluster));
                LOGGER.debug("after new K8sCluster");
                k8sCluster.registerPodEventHandlers(eventHandlers);
                k8sCluster.registerOpsTaskEventHandlers(allOpsTaskEventHandlers);
                k8sClusters.put(cluster.getCceId(), k8sCluster);
            }
            for (String cceID : k8sClusters.keySet()) {
                LOGGER.debug("{} will start K8sCluster", cceID);
                k8sClusters.get(cceID).start();
            }

            LOGGER.info("begin to create all k8s user namespace and sidecar-command-cm");
            List<CceUserMap> cceUserMapList = cceClusterService.listActiveCceUserMaps();
            for (CceUserMap cceUserMap : cceUserMapList) {
                String userId = cceUserMap.getUserId();
                LOGGER.info("begin to create k8s user:{} namespace and sidecar-command-cm ...", userId);
                if (!createSidecarCommandConfigMapFromFile(userId)) {
                    LOGGER.error("end to create k8s user:{} namespace and sidecar-command-cm with failed", userId);
                    continue;
                }
                LOGGER.info("end to create k8s user:{} namespace and sidecar-command-cm successfully", userId);
            }
            LOGGER.info("end to create all k8s user namespace and sidecar-command-cm successfully");
        } catch (Exception e) {
            LOGGER.error("k8sservice genApiClient error:error:{}", e.toString());
        }

    }

    @PreDestroy
    public void stop() {
        for (K8sCluster k8sCluster : k8sClusters.values()) {
            k8sCluster.stop();
        }
    }

    public V1Pod getPod(String nameSpace, String podName) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.getPod(nameSpace, podName);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("get pod failed, message is {}", e.getMessage());
            return null;
        }
    }

    public V1Pod tryGetPod(String nameSpace, String podName) {
        int retryTimes = 3;
        for (int i = 1; i <= retryTimes; i ++) {
            LOGGER.debug("try to get pod {}, number of times: {}", podName, i);
            V1Pod pod = getPod(nameSpace, podName);
            if (pod != null) {
                return pod;
            }
        }
        return null;
    }

    public V1Node getNode(String nameSpace, String nodeName) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.getNode(nodeName);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("get node failed, message is {}", e.getMessage());
            return null;
        }
    }

    public List<V1Node> listNodes(String nameSpace) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.listNodes();
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("list nodes failed, message is {}", e.getMessage());
            return null;
        }
    }

    public Response getPodLogWithResponse(String podName, String namespace, String container, Boolean follow,
                                          Boolean insecureSkipTLSVerifyBackend, Integer limitBytes, String pretty,
                                          Boolean previous, Integer sinceSeconds, Integer tailLines, Boolean timestamps)
            throws ApiException, IOException, K8sServiceException {
        K8sCluster k8sCluster = getClusterByUserId(namespace);
        return k8sCluster.getPodLogWithResponse(podName, namespace, container, follow, insecureSkipTLSVerifyBackend,
                limitBytes, pretty, previous, sinceSeconds, tailLines, timestamps);
    }

    public HashMap<Pair<String, String>, V1Pod> batchGetPods(ArrayList<Pair<String, String>> nsPods) {
        HashMap<Pair<String, String>, V1Pod> pods = new HashMap<>();
        // 当前先使用循环进行创建，后续可以优化成多线程并发
        for (Pair<String, String> nsPod : nsPods) {
            V1Pod pod = getPod(nsPod.getFirst(), nsPod.getSecond());
            pods.put(nsPod, pod);
        }
        return pods;
    }

    public List<V1Pod> listPods(String nameSpace) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.listPods(nameSpace);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("list pods in namespace {} error, message is {}", nameSpace, e.getMessage());
            return new ArrayList<>();
        }
    }

    public List<V1Pod> listAllPods(String nameSpace) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.listAllPods();
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("list all pods error, message is {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    public void createImageAccCRD(String nameSpace, ImageAccelerateCRDRequest imageAccelerateCRDRequest) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            k8sCluster.createImageAccCRD(nameSpace, imageAccelerateCRDRequest);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("create image accelerate {} crd in namespace {} error, message is {}",
                    imageAccelerateCRDRequest.toString(), nameSpace, e.getMessage());
        }
    }

    public void createOpsTaskCRD(String nameSpace, OpsTaskCRDRequest opsTaskCRDRequest) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            k8sCluster.createOpsTaskCRD(nameSpace, opsTaskCRDRequest);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("create ops task {} crd in namespace {} error, message is {}",
                    opsTaskCRDRequest.toString(), nameSpace, e.getMessage());
            throw new OpsExceptions.OpsTaskException("error occured.", HttpStatus.ERROR_COMPUTER);
        }
    }

    public void patchOpsTaskCRDAnnotation(OpsTaskCRDGetResponse opsTaskCRDRequest,
                                                       List<PatchValue> patchValue) {
        // 此处需确保上层调用不会抛空指针
        String name = opsTaskCRDRequest.getMetadata().getName();
        String namespace = opsTaskCRDRequest.getMetadata().getNamespace();
        try {

            K8sCluster k8sCluster = getClusterByUserId(namespace);
            Map<String, String> oldAnnotation = new HashMap<>();
            if (opsTaskCRDRequest.getMetadata() != null && opsTaskCRDRequest.getMetadata().getAnnotations() != null) {
                oldAnnotation = opsTaskCRDRequest.getMetadata().getAnnotations();
            }
            Map<String, String> newAnnotation = new HashMap<>(oldAnnotation);

            boolean annotationChange = false;
            for (PatchValue patch : patchValue) {
                if (PatchValue.DELETE_TYPE.equals(patch.getType()) && oldAnnotation.get(patch.getValue()) != null) {
                    newAnnotation.put(patch.getKey(), null);
                    annotationChange = true;
                    continue;
                }

                if (PatchValue.UPDATE_TYPE.equals(patch.getType()) && !patch.getValue().equals(
                        oldAnnotation.get(patch.getValue()))) {
                    newAnnotation.put(patch.getKey(), patch.getValue());
                    annotationChange = true;
                }
            }

            if (!annotationChange) {
                return;
            }

            Map<String, Map<String, String>> metaData = new HashMap<>();
            metaData.put("annotations", newAnnotation);

            Map<String, Object> metaDataMap = new HashMap<>();
            metaDataMap.put("metadata", metaData);
            String patchStr = JsonUtil.toJSON(metaDataMap);
            k8sCluster.patchOpsTaskCRDAnnotation(namespace, name, patchStr);
        } catch (Exception e) {
            LOGGER.error("patch ops task {} crd in namespace {} error, message is {}",
                    namespace, e.getMessage());
            throw new OpsExceptions.OpsTaskException("error occured.", HttpStatus.ERROR_COMPUTER);
        }
    }

    public OpsTaskCRDGetResponse getOpsTaskCRD(String namespace, String name) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(namespace);
            return k8sCluster.getOpsTaskCRD(namespace, name);
        } catch (ApiException | K8sServiceException e) {
            LOGGER.error("get opsTask {} crd in namespace {} error, message is {}",
                    name, namespace, e.getMessage());
            return null;
        }
    }

    public void deleteImageAccCRD(String nameSpace, String name) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            k8sCluster.deleteImageAccCRD(nameSpace, name);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("delete image accelerate crd {} in namespace {} error, message is {}",
                    name, nameSpace, e.getMessage());
        }
    }

    public void deleteOpsTaskCRD(String nameSpace, String name) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            k8sCluster.deleteOpsTaskCRD(nameSpace, name);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("delete opsTask crd {} in namespace {} error, message is {}",
                    name, nameSpace, e.getMessage());
        }
    }

    public void patchImageAccCRD(String nameSpace, String name,
                                              ImageAccelerateCRDRequest imageAccelerateCRDRequest) {

        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            k8sCluster.patchImageAccCRD(nameSpace, name, imageAccelerateCRDRequest);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("patch image accelerate crd {} in namespace {} error, message is {}",
                    name, nameSpace, e.getMessage());
        }
    }

    public ImageAccelerateCRDResponse getImageAccCRD(String nameSpace, String name) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.getImageAccCRD(nameSpace, name);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("get image accelerate {} crd in namespace {} error, message is {}",
                    name, nameSpace, e.getMessage());
            return null;
        }
    }

    public List<ImageAccelerateCRDResponse> listAllImageAccCRDs() {
        List<ImageAccelerateCRDResponse> allImageAccelerateCRD = new ArrayList<>();
        List<CceCluster> cceClusters = clusterDao.getAllCceClusters();

        if (cceClusters.size() == 0) {
            return allImageAccelerateCRD;
        }
        for (CceCluster cluster : cceClusters) {
            K8sCluster k8sCluster = k8sClusters.get(cluster.getCceId());
            if (k8sCluster == null) {
                continue;
            }
            ImageAccelerateCRDResponseList clusterCRDs = k8sCluster.listImageAccCRDs(cluster.getCceId());
            if (clusterCRDs == null || clusterCRDs.getItems().size() == 0) {
                continue;
            }
            allImageAccelerateCRD.addAll(clusterCRDs.getItems());
        }
        return allImageAccelerateCRD;
    }

    public void createPod(V1Pod pod) throws K8sServiceException, ApiException {
        // 获取 Pod 所在的集群
        LOGGER.debug("in k8Sservice createPod:will create pod {}", pod.getMetadata().getName());

        K8sCluster k8sCluster = initK8sClusterNamespace(pod);

        // 创建 pod
        String namespace = pod.getMetadata().getNamespace();
        String podName = pod.getMetadata().getName();   
        if (k8sCluster.getPod(namespace, podName) != null) {
            LOGGER.warn("pod {} is exsit in cce,will not create it again", pod.getMetadata().getName());
            throw new K8sServiceException(ErrorCode.POD_EXISTED, "pod existed.");
        }
        k8sCluster.createPod(pod);
    }

    public void updatePod(String namespace, String name, V1Pod v1Pod) throws K8sServiceException, ApiException {
        K8sCluster k8sCluster = getClusterByUserId(namespace);
        if (k8sCluster.getPod(namespace, name) == null) {
            LOGGER.warn("pod {} is not existed in cce.", name);
            throw new K8sServiceException(ErrorCode.POD_NOT_EXISTED, "pod not existed.");
        }
        k8sCluster.updatePod(namespace, name, v1Pod);
    }

    public V1Secret getSecret(String nameSpace, String secretName) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.getSecret(nameSpace, secretName);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("get secret {} failed, message is {}", secretName, e.getMessage());
            return null;
        }
    }

    public boolean deleteSecret(String secretName, String namespace)
            throws ApiException, K8sServiceException {
        // 获取 Pod 所在的集群
        K8sCluster k8sCluster = getClusterByUserId(namespace);

        // 检查 namespace 是否已经存在，不存在直接返回
        if (k8sCluster.getNameSpace(namespace) == null) {
            return true;
        }
        // 删除 pod
        if (null == getSecret(namespace, secretName)) {
            LOGGER.info("secret {} is not exist in backend cce cluster", secretName);
            return true;
        } else {
            boolean res = k8sCluster.deleteSecret(namespace, secretName);
            if (!res) {
                LOGGER.error("delete secret {} failed", secretName);
                return false; // 删除失败,不删除后续的configmap
            }
        }
        return true;
    }

    public void createSecret(V1Secret secret) throws K8sServiceException, ApiException {
        // 获取 secret 所在的集群
        LOGGER.debug("in k8Sservice createSecret:will create secret {}", secret.getMetadata().getName());

        K8sCluster k8sCluster = initK8sClusterNamespace(secret);

        // 创建 secret
        String name = secret.getMetadata().getName();
        String namespace = secret.getMetadata().getNamespace();
        if (k8sCluster.getSecret(name, namespace) != null) {
            LOGGER.warn("secret {} is exsit in cce,will not create it again", secret.getMetadata().getName());
            throw new K8sServiceException(ErrorCode.SECRET_EXISTED, "secret existed.");
        }
        k8sCluster.createSecret(secret);
    }

    // 批量创建pod
    public BatchCreatePodsResult batchCreatePods(List<V1Pod> pods) {
        BatchCreatePodsResult result = new BatchCreatePodsResult();
        // 当前先使用循环进行创建，后续可以优化成多线程并发
        for (V1Pod pod : pods) {
            try {
                createPod(pod);
                result.addSucceedPod(pod);
            } catch (K8sServiceException e) {
                if (e.getErrorCode() == ErrorCode.POD_EXISTED) {
                    result.addExistPod(pod);
                } else {
                    result.addFailedPod(pod);
                    if (e.getErrorCode() == ErrorCode.CREATE_POD_FAILED
                            && StringUtils.isNotEmpty(e.getResponseBody())
                            && JsonUtil.isJson(e.getResponseBody())) {
                        LOGGER.error("K8sService batchCreatePods CREATE_POD_FAILED podId:{}, error:{}",
                                pod.getMetadata().getName(), e.getResponseBody());
                        result.getFailedPodsReasons().put(pod, e.getResponseBody());
                    }
                }
            } catch (ApiException e) {
                result.addFailedPod(pod);
            }
        }
        return result;
    }

    private K8sCluster initK8sClusterNamespace(KubernetesObject k8sObject) throws K8sServiceException, ApiException {
        String namespace = k8sObject.getMetadata().getNamespace();
        if (namespace == null) {
            LOGGER.error("initK8sClusterNamespace {}: namespace is not exist in pod metadata",
                    k8sObject.getMetadata().getName());
            throw new K8sServiceException(ErrorCode.CREATE_POD_FAILED, "namespace is not exsit in pod.");

        }
        K8sCluster k8sCluster = getClusterByUserId(namespace);
        if (k8sCluster == null) {
            LOGGER.error("initK8sClusterNamespace {}: userId:{},getClusterByUserId: k8sCluster is not exsit",
                    k8sObject.getMetadata().getName(), k8sObject.getMetadata().getNamespace());
            throw new K8sServiceException(ErrorCode.CREATE_POD_FAILED, "getClusterByUserId failed.");
        }
        // 确保 namespace 已经存在
        if (k8sCluster.getNameSpace(namespace) == null) {
            LOGGER.debug("initK8sClusterNamespace {}: namespace {} is not exist in cce, should create it",
                    k8sObject.getMetadata().getName(), k8sObject.getMetadata().getNamespace());
            k8sCluster.createNamespace(namespace);
        }

        return k8sCluster;
    }

    private K8sCluster initK8sClusterNamespace(String namespace) throws K8sServiceException, ApiException {
        K8sCluster k8sCluster = getClusterByUserId(namespace);
        if (k8sCluster == null) {
            LOGGER.error("initK8sClusterNamespace namespace {} k8sCluster is not exsit",
                    namespace);
            throw new K8sServiceException(ErrorCode.K8S_CLUSTER_NOT_EXIST, "getClusterByUserId failed.");
        }
        // 确保 namespace 已经存在
        if (k8sCluster.getNameSpace(namespace) == null) {
            LOGGER.debug("initK8sClusterNamespace namespace {} is not exist in cce, should create it",
                    namespace);
            k8sCluster.createNamespace(namespace);
        }
        return k8sCluster;
    }

    public boolean createK8sClusterNamespace(String namespace) {
        try {
            K8sCluster k8sCluster = initK8sClusterNamespace(namespace);
        } catch (Exception e) {
            LOGGER.error("createK8sClusterNamespace namespace {} error:{}", namespace, e);
            return false;
        }
        return true;
    }

    public void createConfigMap(V1ConfigMap configMap) throws K8sServiceException, ApiException {
        // 校验
        Map<String, String> data = configMap.getData();
        Map<String, byte[]> binaryData = configMap.getBinaryData();

        if (configMap.getMetadata() == null || (binaryData == null && data == null)) {
            throw new K8sServiceException(ErrorCode.CREATE_CONFIGMAP_FAILED, "configMap metadata or data or binaryData is null.");
        }

        String name = configMap.getMetadata().getName();
        String namespace = configMap.getMetadata().getNamespace();

        K8sCluster k8sCluster = initK8sClusterNamespace(configMap);

        V1ConfigMap cacheConfigMap = k8sCluster.getConfigMap(name, namespace);

        // 存在configMap
        if (cacheConfigMap != null) {
            // AbstractMap 重写了equals方法，会遍历map中的值进行equals比较
            boolean isDataEqual = Objects.equals(data, cacheConfigMap.getData());
            boolean isBinaryDataEqual = Objects.equals(binaryData, cacheConfigMap.getBinaryData());

            if (isDataEqual && isBinaryDataEqual) {
                LOGGER.info("createConfigMap ns:{} name:{} is exist, data and binaryData not change", namespace,
                        name);
                return;
            }
            /*
            // 删除已经存在的configMap
            LOGGER.debug("createConfigMap ns:{} name:{} is exist, data or binaryData have been " +
                            "changed, the old configmap will be deleted and create new configmap",
                    namespace, name);
            k8sCluster.deleteConfigMap(name, namespace);
            try {
                // 给informer 同步时间
                Thread.sleep(500);
            } catch (Exception e) {
            }
             */
            LOGGER.debug("createConfigMap ns:{} name:{} is exist, data or binaryData have been " +
                            "changed, the old configmap will be updated",
                    namespace, name);
            cacheConfigMap.setData(data);
            cacheConfigMap.setBinaryData(binaryData);
            k8sCluster.updateConfigMap(namespace, name, cacheConfigMap);
        } else {
            LOGGER.debug("createConfigMap ns:{} name:{} isn't exist, the configmap will be created",
                    namespace, name);
            k8sCluster.createConfigMap(configMap);
        }
    }

    public void deleteConfigMap(String namespace, String configmapName) throws K8sServiceException, ApiException {
        K8sCluster k8sCluster = getClusterByUserId(namespace);
        k8sCluster.deleteConfigMap(configmapName, namespace);
    }

    public synchronized boolean deleteMigratePod(String namespace, String podName) throws ApiException,
            K8sServiceException {
        // 获取 Pod 所在的集群
        K8sCluster k8sCluster = getClusterByUserId(namespace);

        // 检查 namespace 是否已经存在，不存在直接返回
        if (k8sCluster.getNameSpace(namespace) == null) {
            return false;
        }
        if (null == getPod(namespace, podName)) {
            return true;
        }

        boolean res = k8sCluster.deletePod(podName, namespace);
        if (!res) {
            LOGGER.error("delete Migrate pod {} failed", podName);
            return false;
        }
        return true;
    }

    public boolean deletePod(String podName, String namespace) throws ApiException, K8sServiceException {
        // 获取 Pod 所在的集群
        K8sCluster k8sCluster = getClusterByUserId(namespace);

        // 检查 namespace 是否已经存在，不存在直接返回
        if (k8sCluster.getNameSpace(namespace) == null) {
            return false;
        }
        // 删除 pod
        if (null == getPod(namespace, podName)) {
            LOGGER.info("pod {} is not exist in backend cce cluster", podName);
            // pod不存在了,也把configmap删除;
        } else {
            boolean res = k8sCluster.deletePod(podName, namespace);
            if (!res) {
                LOGGER.error("delete pod {} failed", podName);
                return false; // 删除失败,不删除后续的configmap
            }
        }
        // configmap删除;
        deletePodConfigMap(namespace, podName);
        return true;
    }

    public boolean deletePodConfigMap(String namespace, String podName) throws K8sServiceException, ApiException {
        try {
            // 删除configMap
            PodPO podPO = podDaoV2.getPodByIdIgnoreStatus(namespace, podName); // podName就是bci数据库里面的pod_id
            if (podPO == null) {
                return true;
            }

            List<ConfigFile> configMapList = getConfigFilesFromDBPodExtra(podPO);
            if (configMapList == null || CollectionUtils.isEmpty(configMapList)) {
                return true;
            }
            for (int i = 0; i < configMapList.size(); i++) {
                String configMapName = PodUtils.buildConfigMapName(podName, configMapList.get(i).getName());
                K8sCluster k8sCluster = getClusterByUserId(namespace);
                k8sCluster.deleteConfigMap(configMapName, namespace);
            }
        } catch (Exception e) {
            LOGGER.error("delete pod {} configmap error:{}", podName, e);
            return false;
        }
        return true;
    }

    public List<ConfigFile> getConfigFilesFromOrderSystem(PodPO podPO) {
        String podId = podPO.getPodId();
        try {
            OrderClient orderClient = logicPodClientFactory.createOrderClient();
            Order order = orderClient.get(podPO.getOrderId());

            BciOrderExtra orderExtra = null;
            for (Order.Item orderItem : order.getItems()) {
                if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) { // 去除eip的order item
                    orderExtra = PodUtils.getOrderExtra(orderItem.getExtra());
                    break;
                }
            }
            if (orderExtra == null || orderExtra.getVolume() == null) {
                return null;
            }
            return orderExtra.getVolume().getConfigFile();
        }  catch (IOException e) {
            LOGGER.debug("getConfigFilesFromOrderSystem podId:{} exception: {}", podId, e);
            return null;
        }
    }

    public List<ConfigFile> getConfigFilesFromDBPodExtra(PodPO podPO) {
        String podId = podPO.getPodId();
        try {
            if (StringUtils.isEmpty(podId)) {
                return null;
            }
            PodExtraPO podExtraPO = podExtraDaoV2.getPodExtraByPodIdIgnoreStatus(podId);
            if (podExtraPO == null || StringUtils.isEmpty(podExtraPO.getOrderExtra())) {
                // 如果从DB podExtra中没有获取到orderExtra信息,则回退到从order system中获取
                // TODO:这种情况进针对存量pod,后续可以考虑删除
                return getConfigFilesFromOrderSystem(podPO);
            }

            BciOrderExtra orderExtra = PodUtils.getOrderExtra(podExtraPO.getOrderExtra());
            if (orderExtra == null || orderExtra.getVolume() == null) {
                return null;
            }
            return orderExtra.getVolume().getConfigFile();
        }  catch (IOException e) {
            LOGGER.debug("getConfigFilesFromDBPodExtra podId:{} exception: {}", podId, e);
            return null;
        }
    }


    public List<V1ConfigMap> getConfigMaps(String nameSpace) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.listConfigMaps(nameSpace);
        } catch (K8sServiceException | ApiException e) {
            LOGGER.error("list configmap from namespace {} failed, message is {}", nameSpace, e.getMessage());
            return new ArrayList<>();
        }
    }

    public V1ConfigMap getConfigMapByName(String nameSpace, String cmName) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.getConfigMap(cmName, nameSpace);
        } catch (Exception e) {
            LOGGER.error("get configmap from namespace {} name {} failed, message is {}", nameSpace, cmName,
                    e.getMessage());
            return null;
        }
    }

    public V1ConfigMap getConfigMap(String userId, String nameSpace, String cmName) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(userId);
            return k8sCluster.getConfigMap(cmName, nameSpace);
        } catch (Exception e) {
            LOGGER.error("get configmap from userId:{} namespace {} name {} failed, message is {}",
                    userId, nameSpace, cmName, e.getMessage());
            return null;
        }
    }

    public V1ConfigMap getConfigMap(String nameSpace, String cmName) {
        return getConfigMapByName(nameSpace, cmName);
    }

    public boolean updateConfigMap(String namespace, String name, V1ConfigMap configMap) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(namespace);
            return k8sCluster.updateConfigMap(namespace, name, configMap);
        } catch (Exception e) {
            LOGGER.error("update configmap from namespace {} name {} failed, message is {}", namespace, name,
                    e.getMessage());
        }
        return false;
    }

    public V1ConfigMap getKubeSystemConfigMapByName(String nameSpace, String cmName) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.getConfigMap(cmName, "kube-system");
        } catch (Exception e) {
            LOGGER.error("get configmap from namespace {} name {} failed, message is {}", nameSpace, cmName,
                    e.getMessage());
            return null;
        }
    }

    public V1Deployment getKubeSystemInstanceGroupPlaceholderDeployment(String nameSpace, String deployName) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(nameSpace);
            return k8sCluster.getDeployment(deployName, "kube-system");
        } catch (Exception e) {
            LOGGER.error("get deployment from namespace {} name {} failed, message is {}", nameSpace, deployName,
                    e.getMessage());
            return null;
        }
    }

    public K8sCluster getClusterByUserId(String userId) throws ApiException, K8sServiceException {
        List<CceCluster> cceClusters = cceClusterService.getCceClustersByUserId(userId);

        if (cceClusters.size() == 0) {
            throw new K8sServiceException(ErrorCode.K8S_CLUSTER_NOT_EXIST, "k8s cluster not exist.");
        }
        K8sCluster k8sCluster = k8sClusters.get(cceClusters.get(0).getCceId());
        if (k8sCluster == null) {
            throw new K8sServiceException(ErrorCode.K8S_CLUSTER_NOT_EXIST, "k8s cluster not exist.");
        }
        return k8sCluster;
    }

    private ApiClient genApiClient(CceCluster cluster) throws IOException {
        try {
            String kubeConfigStr = cluster.getCceKubeConfig();
            KubeConfig config = KubeConfig.loadKubeConfig(new StringReader(kubeConfigStr));
            ApiClient client = ClientBuilder.kubeconfig(config).build();
            return client;
        } catch (Exception e) {
            LOGGER.error("k8sservice genApiClient error:error:{}", e.toString());
        }
        return null;
    }

    public V1DaemonSet getDaemonSet(String namespace, String name) {
        try {
            K8sCluster k8sCluster = getClusterByUserId(namespace);
            return k8sCluster.getDaemonSet(namespace, name);
        } catch (Exception e) {
            LOGGER.error("get daemonset from namespace {} name {} failed, message is {}", namespace, name,
                    e.getMessage());
            return null;
        }
    }

    public synchronized void createDaemonSet(V1DaemonSet daemonSet) throws K8sServiceException,
            ApiException {
        String namespace = daemonSet.getMetadata().getNamespace();
        String name = daemonSet.getMetadata().getName();
        LOGGER.debug("k8Sservice createDaemonSet namespace:{}, name:{}",
                namespace, name);
        K8sCluster k8sCluster = initK8sClusterNamespace(namespace);
        if (k8sCluster.getDaemonSet(namespace, name) != null) {
            LOGGER.warn("k8Sservice createDaemonSet daemonSet {} is exsit in cce namespace {}, will not create it " +
                    "again", name, namespace);
            throw new K8sServiceException(ErrorCode.DAEMONSET_EXISTED, "daemonset existed.");
        }
        // 创建 daemonSet
        k8sCluster.createDaemonSet(daemonSet);
    }

    public synchronized void createDaemonSetWithSts(String stsNamespace, V1DaemonSet daemonSet) throws K8sServiceException,
            ApiException {
        String namespace = daemonSet.getMetadata().getNamespace();
        String name = daemonSet.getMetadata().getName();
        LOGGER.debug("k8Sservice createDaemonSetWithSts namespace:{}, name:{}",
                namespace, name);
        K8sCluster k8sCluster = initK8sClusterNamespace(stsNamespace);
        if (k8sCluster.getDaemonSet(namespace, name) != null) {
            LOGGER.warn("k8Sservice createDaemonSetWithSts daemonSet {} is exsit in cce namespace {}, will not create it " +
                            "again", name, namespace);
            throw new K8sServiceException(ErrorCode.DAEMONSET_EXISTED, "daemonset existed.");
        }
        // 创建 daemonSet
        k8sCluster.createDaemonSet(daemonSet);
    }

    public V1ConfigMap readConfigMapFromFile() {
        try {
            V1ConfigMap configMap = new V1ConfigMap();
            V1ObjectMeta meta = new V1ObjectMeta();
            meta.setNamespace(sidecarConstant.getSidecarCommandConfigMapDefaultNamespace());
            meta.setName(sidecarConstant.getSidecarCommandConfigMapName());
            Map<String, String> configMapDataMap = new HashMap<>();
            // bls_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapBlsCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapBlSCheckFilePath());

            // bos_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapBosCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapBosCheckFilePath());

            // image_download_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapImageDownloadCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapImageDownloadCheckFilePath());

            // kubelet_proxy_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapKubeletProxyCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapKubeletProxyCheckFilePath());

            // kubeproxy_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapKubeProxyCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapKubeProxyCheckFilePath());

            // nfs_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapNfsCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapNfsCheckFilePath());

            // pfs_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapPfsCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapPfsCheckFilePath());

            // ntp_check.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapNTPCheckName(),
                    sidecarConstant.getSidecarCommandConfigMapNTPCheckFilePath());

            // post.sh
            configMapDataMap.put(
                    sidecarConstant.getSidecarCommandConfigMapPostName(),
                    sidecarConstant.getSidecarCommandConfigMapPostFilePath());

            configMap.setMetadata(meta);
            Map<String, String> commandMap = new HashMap<>();
            for (Map.Entry<String, String> entry : configMapDataMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String fileContent = FileUtil.readFile(value);
                if (StringUtils.isNotEmpty(fileContent)) {
                    commandMap.put(key, fileContent);
                }
            }
            configMap.setData(commandMap);
            return configMap;
        } catch (Exception e) {
            LOGGER.error("read configmap from file failed, message is {}", e.getMessage());
            return null;
        }
    }

    public boolean createSidecarCommandConfigMapFromFile(String namespace) {
        try {
            V1ConfigMap configMap = readConfigMapFromFile();
            if (configMap == null) {
                return false;
            }
            // 替换namespace
            configMap.getMetadata().setNamespace(namespace);
            createConfigMap(configMap);
        } catch (Exception e) {
            LOGGER.error("createConfigMapFromFile in namespace:{} failed with Exception message is {}", namespace,
                    e.getMessage());
            return false;
        }
        return true;
    }
}