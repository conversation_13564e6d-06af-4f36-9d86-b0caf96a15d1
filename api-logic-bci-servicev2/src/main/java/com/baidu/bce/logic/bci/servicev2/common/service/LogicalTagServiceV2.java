package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service
public class LogicalTagServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogicalTagServiceV2.class);

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    /**
     * 获取以resourceUuid为key的tag列表Map
     * 报错降级
     * @param resourceUuids 资源id
     * @param accountID 账号id
     * @return 以resourceUuid为key的tag列表Map
     */
    private Map<String, List<Tag>> queryTagsByResourceUuids(List<String> resourceUuids, String accountID) {
        Map<String, List<Tag>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(resourceUuids)) {
            return result;
        }

        try {
            FullTagListRequest request = new FullTagListRequest();
            request.setResourceUuids(resourceUuids);
            LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(accountID);
            TagAssociationFulls response = tagClient.listFullTags(request);
            for (TagAssociationFull tagFull : response.getTagAssociationFulls()) {
                String resourceUuid = tagFull.getResourceUuid();
                Tag tag = new Tag();
                tag.setTagKey(tagFull.getTagKey());
                tag.setTagValue(tagFull.getTagValue());
                if (result.containsKey(resourceUuid)) {
                    result.get(resourceUuid).add(tag);
                } else {
                    List<Tag> temp = new ArrayList<>();
                    temp.add(tag);
                    result.put(resourceUuid, temp);
                }
            }
        } catch (Exception e) {
            LOGGER.error("queryTagsByResourceUuids error:{}", e);
        }
        return result;
    }

    /**
     * @Description: 将标签信息添加到PodPO列表中
     * @param podPOS PodPO类型的列表，需要添加标签信息
     * @return void 无返回值
     */
    public void addTagInfoToPodPO(List<PodPO> podPOS) {
        List<String> podUuids = new ArrayList<>();
        for (PodPO pod : podPOS) {
            podUuids.add(pod.getPodUuid());
        }
        Map<String, List<Tag>> tagMap = queryTagsByResourceUuids(podUuids, getAccountId());
        for (PodPO pod : podPOS) {
            pod.setPodTags(tagMap.get(pod.getPodUuid()));
            pod.setLabels(JsonUtil.toList(pod.getTags(), Label.class));
        }
    }

    /**
     * 将标签信息添加到PodPO列表中，并转换为Label类型的List。
     *
     * @param podPOS PodPO列表，每个元素包含一个PodPO对象
     * @return void，不返回任何值
     */
    public void addTagInfoToPodPOLight(List<PodPO> podPOS) {
        for (PodPO pod : podPOS) {
            pod.setLabels(JsonUtil.toList(pod.getTags(), Label.class));
        }
    }

    /**
     * 将每个 ReservedInstancePO 对象中的标签信息添加到指定的预留实例券列表中。
     *
     * @param reservedInstancePOs 指定的预留实例券列表。
     */
    public void addTagInfoToReservedInstancePO(List<ReservedInstancePO> reservedInstancePOs) {
        List<String> reservedInstanceUuids = new ArrayList<>();
        for (ReservedInstancePO ri : reservedInstancePOs) {
            reservedInstanceUuids.add(ri.getReservedInstanceUuid());
        }
        Map<String, List<Tag>> tagMap = queryTagsByResourceUuids(reservedInstanceUuids, getAccountId());
        for (ReservedInstancePO ri : reservedInstancePOs) {
            ri.setTags(tagMap.get(ri.getReservedInstanceUuid()));
        }
    }

    /**
     * 判断资源是否绑定了标签
     *
     * @param resourceUuid 资源UUID
     * @param accountID    用户账号ID
     * @return 如果资源存在指定标签，则返回true；否则返回false
     */
    public boolean hasTags(String resourceUuid, String accountID) {
        List<String> resourceUuids = new ArrayList<>();
        resourceUuids.add(resourceUuid);
        Map<String, List<Tag>> tagMap = queryTagsByResourceUuids(resourceUuids, accountID);
        return CollectionUtils.isNotEmpty(tagMap.get(resourceUuid));
    }
}
