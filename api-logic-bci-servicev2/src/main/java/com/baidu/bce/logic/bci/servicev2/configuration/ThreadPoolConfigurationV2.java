package com.baidu.bce.logic.bci.servicev2.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfigurationV2 {
    @Value("${thread.pool.core.size:100}")
    private Integer threadPoolCoreSize;
    @Value("${thread.pool.max.size:350}")
    private Integer threadPoolMaxSize;
    @Value("${thread.pool.queue.capacity:50000}")
    private Integer threadPoolQueueCapacity;
    @Value("${thread.keepalive.seconds:60}")
    private Integer threadKeepAliveSeconds;
    @Bean(name = "theadPoolTaskExecutorV2")
    public ThreadPoolTaskExecutor theadPoolTaskExecutorV2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(threadPoolQueueCapacity);
        executor.setKeepAliveSeconds(threadKeepAliveSeconds);

        String threadNamePrefix = "BciV2-Common-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);
        return executor;
    }
}