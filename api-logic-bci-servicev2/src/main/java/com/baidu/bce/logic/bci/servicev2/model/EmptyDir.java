package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class EmptyDir extends BaseVolume {
    // EmptyDirVolume的存储媒介，默认为空，使用node文件系统；支持 memory，表示使用内存
    private String medium;
    // EmptyDirVolume的大小。单位为GiB。
    private Float sizeLimit;
}
