package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.ArrayList;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceIDGetter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class LeakagePodDeleteRequest implements AuthorizationResourceIDGetter {
    private String podId;
    private String podUuid;
    private Boolean relatedReleaseFlag;

    @Override
    public List<String> getPodIDs() {
        List<String> result = new ArrayList<String>();
        result.add(podId);
        return result;
    }
}
