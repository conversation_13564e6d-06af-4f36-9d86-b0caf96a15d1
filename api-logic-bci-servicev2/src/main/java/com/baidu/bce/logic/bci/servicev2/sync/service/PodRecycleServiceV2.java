package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.DeleteTagAssociationRequest;
import com.baidu.bce.logical.tag.sdk.model.Resource;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate;

@Service("PodRecycleServiceV2")
public class PodRecycleServiceV2 extends SyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodRecycleServiceV2.class);

    @Autowired
    private LogicalResourceServiceV2 logicalResourceService;

    @Autowired
    protected RegionConfiguration regionConfiguration;

    @Autowired
    private BillingResourceSyncManagerV2 billingResourceSyncManager;

    public void recyclePodContainer() {
        // get pod from db
        List<PodPO> podPOList = podDao.listAllPods();

        try {
            // expired pod
            podExpired(podPOList);
        } catch (Exception ex) {
            LOGGER.error("fail to recycle pod", ex);
        }
    }

    private void podExpired(List<PodPO> podPOS) {
        Map<String, List<PodPO>> mapByAccount = getPodMapByAccount(podPOS);
        for (Map.Entry<String, List<PodPO>> entry : mapByAccount.entrySet()) {
            String accountId = entry.getKey();
            List<PodPO> userPods = entry.getValue();

            Map<String, EipInstance> eipInstanceMap = new HashMap<>();
            try {
                eipInstanceMap = getEipMap(accountId);
            } catch (Exception ex) {
                LOGGER.error("fail to get eip, skip delete eip, ex: ", ex);
            }

            for (PodPO podPO : userPods) {
                if (BciStatus.EXPIRED.getStatus().equalsIgnoreCase(podPO.getStatus())) {
                    // delete pod
                    try {
                        LOGGER.debug("pod {} is expire,will delete it", podPO.getPodId());
                        deletePodAndEip(podPO, true, eipInstanceMap);
                    } catch (Exception ex) {
                        LOGGER.error("failed to delete pod: ", ex);
                    }
                }
            }
        }
    }

    private Map<String, EipInstance> getEipMap(String accountId) {
        List<EipInstance> eipInstances = getBackendEips(null, null, null,
                PodConstants.SERVICE_TYPE, accountId);
        Map<String, EipInstance> eipMap = new HashMap<>();
        for (EipInstance eipInstance : eipInstances) {
            eipMap.put(eipInstance.getInstanceLongId(), eipInstance);
        }
        return eipMap;
    }

    private List<EipInstance> getBackendEips(String eipStatus, String eipName, String eip, String serviceName,
                                             String accountId) {
        return new LinkedList<>();

        // cancel in bci2.0
        /* 
        List<EipInstance> eipInstances = null;
        EipLogicalClient eipLogicalClient = logicPodClientFactory.createEipClient(accountId);
        EipListResponse eipListResponse = eipLogicalClient.listEip("", eipStatus, serviceName,
                "", "", 1000);
        if (eipListResponse == null || CollectionUtils.isEmpty(eipListResponse.getEipList())) {
            return new LinkedList<>();
        }
        eipInstances = eipListResponse.getEipList();
        for (Iterator<EipInstance> iter = eipInstances.iterator(); iter.hasNext(); ) {
            EipInstance eipInstance = iter.next();
            if (StringUtils.isNotBlank(eipName) && !eipInstance.getName().contains(eipName)) {
                iter.remove();
                continue;
            }
            if (StringUtils.isNotBlank(eip) && !eipInstance.getEip().contains(eip)) {
                iter.remove();
                continue;
            }
        }
        return eipInstances;
        */
    }

    // 返回 map: userId -> podList; 只返回过期pod
    private Map<String, List<PodPO>> getPodMapByAccount(List<PodPO> podPOS) {
        Map<String, List<PodPO>> mapByAccount = new HashMap<>();
        for (PodPO pod : podPOS) {
            if (!BciStatus.EXPIRED.getStatus().equalsIgnoreCase(pod.getStatus())) {
                continue;
            }
            if (!mapByAccount.containsKey(pod.getUserId())) {
                List<PodPO> podList = new LinkedList<>();
                podList.add(pod);
                mapByAccount.put(pod.getUserId(), podList);
                continue;
            }
            mapByAccount.get(pod.getUserId()).add(pod);
        }

        return mapByAccount;
    }

    private void deletePodAndEip(PodPO podPO, boolean relatedReleaseFlag,
                                 Map<String, EipInstance> eipInstanceMap) {
        if (relatedReleaseFlag) {
            forceReleaseBindEip(eipInstanceMap, podPO);
        } else {
            EipInstance eipInstance = eipInstanceMap.get(podPO.getPodUuid());
            if (eipInstance != null) {
                unBindEipFromPod(eipInstance.getEip(), podPO.getUserId());
            }
        }

        String orderOwner = getOrderOwner(podPO);
        String resourceOwner = podPO.getUserId();

        // billing 的接口，得用创建订单的accountID
        logicalResourceService.deleteResourceByName(orderOwner, podPO.getPodUuid(), PodConstants.SERVICE_TYPE);
        // nova是用户ID创建的，用用户的ID
        deletePodFromNova(resourceOwner, podPO.getPodUuid());

        try {
            LOGGER.debug("[call billing] deletePodRecord in podExpired->deletePodAndEip for pod {}", podPO.getPodUuid());
            deletePodRecord(podPO);
            unBindTags(podPO);
            podDao.deletePod(podPO.getUserId(), podPO.getPodUuid());
            containerDao.deleteContainers(resourceOwner, podPO.getPodId());
            containerDao.deleteContainers(resourceOwner, podPO.getPodUuid());
        } catch (Exception e) {
            LOGGER.error("delete from logical failed, exception is {}", e);
            throwPermissionDeniedExceptionIfAppropriate(e);
            throw new PodExceptions.InternalServerErrorException();
        }
    }

    public String getOrderOwner(PodPO podPO) {
        String orderOwner = podPO.getUserId();

        if (podPO.getChargeSource().equalsIgnoreCase("user")) {
            return orderOwner;
        }

        if (StringUtils.isEmpty(podPO.getOrderId())) {
            LOGGER.error("orderId is empty");
            return orderOwner;
        }

        // 统一计费，获取资源账号ID
        OrderClient orderClient = logicPodClientFactory.createOrderClient();
        Order order = orderClient.get(podPO.getOrderId());
        orderOwner = order.getAccountId();
        return orderOwner;
    }

    private void unBindTags(PodPO podPO) {

        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(podPO.getUserId());
        DeleteTagAssociationRequest request = new DeleteTagAssociationRequest();
        Resource resource = new Resource();
        resource.setRegion(regionConfiguration.getCurrentRegion());
        resource.setResourceId(podPO.getPodId());
        resource.setResourceUuid(podPO.getPodUuid());
        resource.setServiceType(PodConstants.SERVICE_TYPE);
        request.setResource(resource);

        tagClient.deleteTagAssociation(request);
    }

    private void deletePodRecord(PodPO podPO) {
        PodChargeStatus podChargeStatus = new PodChargeStatus();
        podChargeStatus.setPodUuid(podPO.getPodUuid());
        podChargeStatus.setPreviousState(podPO.getStatus());
        podChargeStatus.setCurrentState(BciStatus.DELETED.getName());
        podChargeStatus.setChargeState(PodConstants.NO_CHARGE);
        podChargeStatus.setResourceVersion(podPO.getResourceVersion());
        podChargeStatus.setCpt1SyncState(podPO.getIntCpt1Mode());
        Timestamp timestampNow = new Timestamp(new Date().getTime());
        podChargeStatus.setCreatedTime(timestampNow);
        podChargeStatus.setUpdateTime(timestampNow);

        podChargeStatusDao.insert(podChargeStatus);
        if (podPO.getCpt1()) {
            billingResourceSyncManager.doSync(podPO, podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                    podPO.getPodUuid(), podPO.getResourceVersion(), PodConstants.NO_CHARGE, 0);
            LOGGER.debug("[call billing] finish insert billing sync task in deletePodRecord2, id {}, account {}, pod {}, resource version {}, " +
                            "current status {}", podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                    podPO.getPodUuid(), podPO.getResourceVersion(), PodConstants.NO_CHARGE) ;
        }
    }

    private void deletePodFromNova(String accountID, String uuid) {
        try {
            PodClient podClient = logicPodClientFactory.createPodClientByAccountId(accountID);
            podClient.deletePod(uuid);
        } catch (BceInternalResponseException e) {
            if (e.getHttpStatus() != 404) {
                LOGGER.error("Delete from backend failed, exception is {}", e);
                throwPermissionDeniedExceptionIfAppropriate(e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
    }

    private void forceReleaseBindEip(Map<String, EipInstance> eipMap, PodPO podPO) {
        String accountId = podPO.getUserId();
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(accountId);
        EipInstance eipInstance = eipMap.get(podPO.getPodUuid());
        if (eipInstance != null && !"shared".equalsIgnoreCase(eipInstance.getEipType())) {
            try {
                LOGGER.debug("PodRecycleServiceV2 forceReleaseBindEip {}", eipInstance.getEip());
                logicEipClient.forceReleaseEipV2(eipInstance.getEip());
            } catch (BceInternalResponseException e) {
                if ("PrepayEip".equalsIgnoreCase(e.getCode())) {
                    LOGGER.warn("Prepay EIP delete operation is not available and eipId:{}", eipInstance.getEip());
                    unBindEipFromPod(eipInstance.getEip(), podPO.getUserId());
                } else {
                    throw e;
                }
            }
        }
    }

    private void unBindEipFromPod(String eip, String accountId) {
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(accountId);
        logicEipClient.unbindEip(eip);
    }
}
