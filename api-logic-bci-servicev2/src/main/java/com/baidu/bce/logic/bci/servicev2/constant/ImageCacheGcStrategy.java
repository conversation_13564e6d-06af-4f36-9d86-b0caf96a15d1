package com.baidu.bce.logic.bci.servicev2.constant;

public enum ImageCacheGcStrategy {
    LRU("LRU", "LRU回收策略");

    private String name;
    private String description;

    ImageCacheGcStrategy(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
}
