package com.baidu.bce.logic.bci.servicev2.orderresource;

import com.baidu.bce.logic.bci.servicev2.orderresource.service.ResourceSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.SyncServiceV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("AccountResourceSyncSchedulerV2")
@Profile("default")
public class BillingSyncSchedulerV2 extends SyncServiceV2 {
    private static final int FIX_DELAY_ONE_TIME_MS = 1 * 3600 * 1000;

    private String schedulerName = "BillingSyncSchedulerV2.runAccountResourceStatusSyncTask";

    @Autowired
    private ResourceSyncServiceV2 resourceSyncService;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    // 启动立即执行一次
    @Scheduled(fixedDelay = FIX_DELAY_ONE_TIME_MS)
    public void runAccountResourceStatusSyncTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        resourceSyncService.syncInstanceStatusToBilling();
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }
}
