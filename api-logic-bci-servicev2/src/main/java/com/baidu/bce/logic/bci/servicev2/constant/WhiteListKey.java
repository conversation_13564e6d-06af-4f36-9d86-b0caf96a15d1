package com.baidu.bce.logic.bci.servicev2.constant;

public class WhiteListKey {

    /**
     * 不受全局购买按需bcc阈值限制
     */
    public static final String FREE_TO_POST_PAY_BCC_GLOBAL_QUOTA = "FreeToPostPayBccGlobalQuota";

    /**
     * 是否可以自定义虚机套餐
     */
    public static final String CUSTOM_VM_FLAVOR = "CustomVmFlavor";


    /**
     * BCC2型白名单用户不受售罄限制购买
     */
    public static final String BCC2_OUT_OF_STACK_WHITE_LIST = "Bcc2OutOfStackWhiteList";

    /**
     * 是否在fpga实例白名单中
     */
    public static final String FPGA_VM_FLAVOR = "fpgaBccWhiteList";

    /**
     * 是否在gpu实例白名单中
     */
    public static final String GPU_VM_FLAVOR = "gpuBccWhiteList";

    /**
     * 是否在bcc新机型套餐白名单中
     */
    public static final String BCC_NEW_FLAVOR = "BCCNewFlavor";

    /**
     * 是否在创建BCC时指定脚本地址白名单中
     */
    public static final String BCC_CREATE_WITH_SCRIPT = "bccCreateWithScript";

    /**
     * 是否在bcc新机型套餐白名单中
     */
    public static final String BCC_READ_STATUS_FROM_BACKEND = "BCCReadStatusFromBackend";

    /**
     * 是否在eip服务的白名单中，在则禁止购买绑定EIP
     */
    public static final String EIP_BLACK_LIST = "EipBlackList";

    /**
     * 是否允许购买gpu的k1200卡
     */
    public static final String ENABLE_GPU_K1200 = "gpuK1200";

    /**
     * 是否使用新版的show transaction
     */
    public static final String NEW_TRANSACTION_WHITE_LIST = "newTransactionWhiteList";

    /**
     * 后付费回收站白名单
     */
    public static final String RECYCLE_POST_PAY = "RecyclePostpay";

    /**
     * GPU BCC 扩容操作白名单
     */
    public static final String GPU_BCC_RESIZE_WHITE_LIST = "gpuBccResizeWhiteList";

    /**
     * 关机不计费功能白名单
     */
    public static final String BCC_STOP_WITH_NO_CHARGE_WHITE_LIST = "bccStopWithNoChargeWhiteList";
}
