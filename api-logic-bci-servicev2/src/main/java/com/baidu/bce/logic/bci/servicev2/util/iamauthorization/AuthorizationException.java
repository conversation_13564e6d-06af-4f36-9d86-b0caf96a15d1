package com.baidu.bce.logic.bci.servicev2.util.iamauthorization;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

public class AuthorizationException extends BceException {
    public AuthorizationException(String errorMessage, String errorCode) {
        super(errorMessage, HttpStatus.ERROR_PERMISSION_DENY, errorCode);
        setRequestId(LogicUserService.getRequestId());
    }

    public class AuthorizationErrorCode {
        public static final String BCI_ZONE_NO_RESOURCE_SPECIFICATION = "ZoneNoResourceSpecification";
        public static final String BCI_ACCESS_NO_SPECIFIED_RESOURCE = "AccessNoSpecifiedResource";
        public static final String BCI_ACCESS_NON_OWNED_RESOURCE = "AccessNonOwnedResource";
        public static final String IAM_PERMISSION_DENY = "PermissionDeny";
    }
}
