package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PodCondition {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Date lastProbeTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Date lastTransitionTime;

    private String message;

    private String reason;

    private String status;

    private String type;

    public PodCondition() {
    }

    public PodCondition(Date lastTransitionTime, String status, String type) {
        this.lastTransitionTime = lastTransitionTime;
        this.status = status;
        this.type = type;
    }

    @Override
    public String toString() {
        return "PodCondition{" +
                "lastProbeTime=" + lastProbeTime +
                ", lastTransitionTime=" + lastTransitionTime +
                ", message='" + message + '\'' +
                ", reason='" + reason + '\'' +
                ", status='" + status + '\'' +
                ", type='" + type + '\'' +
                '}';
    }

    public Date getLastProbeTime() {
        return lastProbeTime;
    }

    public void setLastProbeTime(Date lastProbeTime) {
        this.lastProbeTime = lastProbeTime;
    }

    public Date getLastTransitionTime() {
        return lastTransitionTime;
    }

    public void setLastTransitionTime(Date lastTransitionTime) {
        this.lastTransitionTime = lastTransitionTime;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
