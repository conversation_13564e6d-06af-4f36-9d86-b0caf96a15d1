package com.baidu.bce.logic.bci.servicev2.ops;

import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.ops.OpsTaskDao;
import com.baidu.bce.logic.bci.daov2.ops.model.OpsTaskPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.exception.OpsExceptions;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sCluster;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.BciOpsDetailRecord;
import com.baidu.bce.logic.bci.servicev2.model.BciOpsDetailResponse;
import com.baidu.bce.logic.bci.servicev2.model.OpsCreateRequest;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDGetResponse;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDSpec;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDStatus;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskContent;
import com.baidu.bce.logic.bci.servicev2.model.PatchValue;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1Volume;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service("opsTaskService")
public class OpsTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpsTaskService.class);

    private static final String COREDUMP_OPS_TYPE = "coredump";

    private static final String OPS_ENABLE = "enable";
    private static final String OPS_DISABLE = "disable";

    @Autowired
    private K8sService k8sService;

    @Autowired
    private OpsTaskDao opsTaskDao;

    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private PodServiceV2 podServiceV2;

    @Transactional(rollbackFor = OpsExceptions.OpsTaskException.class)
    public void createOpsTask(OpsCreateRequest request) {
        checkOpsCreateParam(request);


        String accountID = getAccountId();
        // todo 待适配tcpdump
        if (COREDUMP_OPS_TYPE.equals(request.getOpsType()) && OPS_DISABLE.equals(request.getOpsValue())) {
            OpsTaskCRDGetResponse opsTaskCRD = k8sService.getOpsTaskCRD(accountID,
                    request.getOpsType() + "-" + request.getPodId());
            if (opsTaskCRD == null) {
                throw new OpsExceptions.OpsTaskException("opsTask is not exist");
            }
            // 告知ops agent，停止任务
            List<PatchValue> patchs = new ArrayList<>();
            PatchValue patchValue = new PatchValue();
            patchValue.setKey("bci_internal_ops_task_value");
            patchValue.setValue("disable");
            patchValue.setType(PatchValue.UPDATE_TYPE);
            patchs.add(patchValue);
            k8sService.patchOpsTaskCRDAnnotation(opsTaskCRD, patchs);
            return;
        }

        // 同一时刻只有一个ops 任务可运行(按opsType)
        List<OpsTaskPO> podOpsRecords = opsTaskDao.queryOpsRecordByPodIdAndOpsType(
                request.getPodId(), request.getOpsType());
        for (OpsTaskPO opsPo : podOpsRecords) {
            if (opsPo.getCompleted() == 0) {
                throw new OpsExceptions.OpsTaskException("only one parallel opsTask at the same time .");
            }
        }
        // 获取cceID
        List<CceCluster> cceCluster = cceClusterService.getCceClustersByUserId(accountID);
        if (CollectionUtils.isEmpty(cceCluster)) {
            throw new OpsExceptions.OpsTaskException("pod is not exist .");
        }
        String requestUuid = UUID.randomUUID().toString();
        // 入库
        OpsTaskPO opsRecord = new OpsTaskPO();
        opsRecord.setOpsType(request.getOpsType());
        opsRecord.setCceId(cceCluster.get(0).getCceId());
        opsRecord.setCompleted(0);
        opsRecord.setDeleted(0);
        opsRecord.setStorageType("bos");
        opsRecord.setStorageContents("");
        opsRecord.setUserId(getAccountId());
        opsRecord.setPodId(request.getPodId());
        opsRecord.setOpsValue(request.getOpsValue());
        opsRecord.setUuid(requestUuid);
        opsTaskDao.insert(opsRecord);

        // 创建crd资源
        OpsTaskCRDRequest crdRequest = new OpsTaskCRDRequest();
        V1ObjectMeta meta = new V1ObjectMeta();
        meta.setName(request.getOpsType() + "-" + request.getPodId());
        meta.setNamespace(getAccountId());
        meta.setAnnotations(new HashMap<String, String>());
        meta.setLabels(new HashMap<String, String>());
        crdRequest.setMetadata(meta);

        Map<String, String> storageParamMap = new HashMap<>();
        storageParamMap.put("bucket", request.getBucket());

        String storageParam = JsonUtil.toJSON(storageParamMap);
        OpsTaskCRDSpec spec = new OpsTaskCRDSpec();
        spec.setOpsType(request.getOpsType());
        spec.setOpsValue(request.getOpsValue());
        spec.setStorageType("bos");
        spec.setStorageParam(storageParam);
        spec.setPodID(request.getPodId());
        spec.setNodeName(request.getNodeName());
        spec.setUuid(requestUuid);
        spec.setAccountID(accountID);
        crdRequest.setSpec(spec);

        crdRequest.setStatus(new OpsTaskCRDStatus());
        k8sService.createOpsTaskCRD(accountID, crdRequest);
    }


    private void checkOpsCreateParam(OpsCreateRequest request) {
        if (request == null) {
            throw new OpsExceptions.OpsTaskException("Request param is null .");
        }

        if (request.getBucket() == null || "".equals(request.getBucket())) {
            throw new OpsExceptions.OpsTaskException("Bos bucket is null .");
        }
        if (!COREDUMP_OPS_TYPE.equals(request.getOpsType())) {
            throw new OpsExceptions.OpsTaskException("opsType is only support coredump .");
        }

        // TODO 待支持tcpdump
        if (COREDUMP_OPS_TYPE.equals(request.getOpsType())) {
            String opsValue = request.getOpsValue();
            if (!OPS_ENABLE.equals(opsValue) && !OPS_DISABLE.equals(opsValue)) {
                throw new OpsExceptions.OpsTaskException("coredump param value is only support enable and disable .");
            }
        }

        String accountId = getAccountId();
        K8sCluster cluster = null;
        try {
            // 不存在，方法内会抛异常
            cluster = k8sService.getClusterByUserId(accountId);
        } catch (Exception e) {
            throw new OpsExceptions.OpsTaskException("pod is not exist .");
        }
        if (cluster == null) {
            throw new OpsExceptions.OpsTaskException("pod is not exist .");
        }

        PodPO podPo = podServiceV2.getPodPO(request.getPodId());
        if (podPo == null) {
            throw new OpsExceptions.OpsTaskException("pod is not exist .");
        }

        if (!BciStatus.RUNNING.getStatus().equals(podPo.getStatus())) {
            throw new OpsExceptions.OpsTaskException("pod is not Running .");
        }

        V1Pod pod = k8sService.getPod(accountId, request.getPodId());
        if (pod == null || pod.getStatus() == null || pod.getSpec() == null) {
            throw new OpsExceptions.OpsTaskException("pod is not exist .");
        }

        if (!"Running".equals(pod.getStatus().getPhase())) {
            throw new OpsExceptions.OpsTaskException("pod is not Running .");
        }
        // 校验pod是否开启coredump pattern
        List<V1Volume> volumes = pod.getSpec().getVolumes();
        if (CollectionUtils.isEmpty(volumes)) {
            throw new OpsExceptions.OpsTaskException("pod have no coredump volume .");
        }
        boolean hasCoredumpHostPath = false;
        for (V1Volume volume : volumes) {
            if (PodNewOrderExecutorServiceV2.CORDUMPE_VOLUME_NAME.equals(volume.getName())
                    && volume.getHostPath() != null) {
                hasCoredumpHostPath = true;
            }
        }
        if (!hasCoredumpHostPath) {
            throw new OpsExceptions.OpsTaskException("pod have coredump pattern , not support opsTask .");
        }
        request.setNodeName(pod.getSpec().getNodeName());
    }

    private String getAccountId() {
        return LogicUserService.getAccountId();
    }


    public OpsTaskPO queryOpsTaskByUuid(String uuid) {
        return opsTaskDao.queryOpsTaskByUuid(uuid);
    }

    public int updateOpsTaskContents(String storageContents, int completed, String uuid, String podId) {
        return opsTaskDao.updateOpsTaskContents(storageContents, completed, uuid, podId);
    }

    public BciOpsDetailResponse record(String podId, String opsType) {
        // podID 参数校验
        if (StringUtils.isEmpty(podId)) {
            throw new OpsExceptions.OpsTaskException("podId is empty .");
        }
        // 添加tcpdump 后待适配
        if (StringUtils.isEmpty(opsType)) {
            throw new OpsExceptions.OpsTaskException("opsType is empty .");
        }
        if (!COREDUMP_OPS_TYPE.equals(opsType)) {
            throw new OpsExceptions.OpsTaskException("opsType is only support coredump .");
        }
        BciOpsDetailResponse response = new BciOpsDetailResponse();
        List<BciOpsDetailRecord> result = new ArrayList<>();
        response.setResult(result);

        String accountId = getAccountId();
        List<OpsTaskPO> opsTasks = opsTaskDao.queryOpsRecordByPodIdAndOpsType(podId, opsType);
        // 校验是否是此用户的task任务
        for (OpsTaskPO opsTask : opsTasks) {
            if (accountId.equals(opsTask.getUserId())) {
                continue;
            }
            throw new OpsExceptions.OpsTaskException("pod opsTask is not exist .");
        }

        for (OpsTaskPO opsTask : opsTasks) {
            String storageContents = opsTask.getStorageContents();
            try {
                List<OpsTaskContent> bciOpsDetailRecords = JsonUtil.toList(storageContents,
                        OpsTaskContent.class);
                for (OpsTaskContent recordDetail : bciOpsDetailRecords) {
                    BciOpsDetailRecord record = new BciOpsDetailRecord();
                    record.setStorageType(opsTask.getStorageType());
                    record.setOpsType(opsTask.getOpsType());
                    record.setOpsStatus(recordDetail.getOpsStatus());
                    String failedMessage = recordDetail.getFailedMessage();
                    if (failedMessage != null && !"".equals(failedMessage)) {
                        record.setStorageContent(failedMessage);
                    } else {
                        record.setStorageContent(recordDetail.getStorageURL());
                    }
                    // golang Unix时间戳转换为字符串
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String createTime = sdf.format(new Date(recordDetail.getCreateTime() * 1000));
                    record.setCreateTimeStr(createTime);
                    record.setCreateTime(recordDetail.getCreateTime() * 1000);
                    result.add(record);
                }
            } catch (Exception e) {
                LOGGER.warn("OpsTaskService pod {} uuid {} storageContents toJosn err {} ", opsTask.getPodId(),
                        opsTask.getUuid(), e);
            }
        }
        // 按照创建时间排序
        Collections.sort(result);
        return response;
    }
}
