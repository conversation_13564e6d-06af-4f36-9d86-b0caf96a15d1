package com.baidu.bce.logic.bci.servicev2.pod;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.dao.pod.model.PodPOForBcm;
import com.baidu.bce.logic.bci.dao.container.ContainerDao;
import com.baidu.bce.logic.bci.dao.container.model.ContainerPOForBcm;
import com.baidu.bce.logic.bci.servicev2.common.service.BciAsyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.interceptor.ResourceAccountSetting;
import com.baidu.bce.logic.bci.servicev2.model.BCMListPodRequest;
import com.baidu.bce.logic.bci.servicev2.model.BCMListPodResponse;
import com.baidu.bce.logic.bci.servicev2.model.BCMPodContainerResponse;
import com.baidu.bce.logic.bci.servicev2.model.PodContainerForBCM;
import com.baidu.bce.logic.bci.servicev2.model.PodForBCM;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;


@Service("BCMPodServiceV2")
public class BCMPodServiceV2 extends BaseServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(BCMPodServiceV2.class);
    private static final String QUERY_LOGIC_FAILED = "[Query failed] ";
    private static final String LOG_LIST_PAGE_PREFIX = "[list pod page] ";

    @Autowired
    private BciAsyncServiceV2 bciAsyncService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private PodDao podDaoV1;

    @Autowired
    private ContainerDao containerDaoV1;

    public BCMListPodResponse listPods(BCMListPodRequest podListRequest) {
        BCMListPodResponse resultResponse = new BCMListPodResponse();
        resultResponse.setPageNo(podListRequest.getPageNo());
        resultResponse.setPageSize(podListRequest.getPageSize());
        List<PodPOForBcm> podPOs;
        try {
            // get pod list from v1.0 and v2.0
            List<? extends PodPOForBcm> allPodPOsInV1 = podDaoV1.listPods(getAccountId(), null, 
                    podListRequest.getKeywordType(), podListRequest.getKeyword());
            List<? extends PodPOForBcm> allPodPOsInV2 = podDao.listPods(getAccountId(), null, 
                    podListRequest.getKeywordType(), podListRequest.getKeyword());
            List<? super PodPOForBcm> allPodPOs = new ArrayList<>();
            allPodPOs.addAll(allPodPOsInV1);
            allPodPOs.addAll(allPodPOsInV2);

            podPOs = filterPodByChargeSource(allPodPOs);

            resultResponse.setTotalCount(podPOs.size());

            // paging
            List<PodPOForBcm> pagePodList = paging(podPOs, podListRequest.getPageNo(), podListRequest.getPageSize());
            // eip
            wrapEipForPod(pagePodList);

            List<PodForBCM> podsForBCM = new LinkedList<>();

            for (PodPOForBcm podPO: pagePodList) {
                PodForBCM podForBCM = new PodForBCM();
                BeanUtils.copyProperties(podForBCM, podPO);
                podsForBCM.add(podForBCM);
            }
            resultResponse.setResult(podsForBCM);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    public BCMPodContainerResponse listPodContainer(BCMListPodRequest podListRequest) {
        String accountId = getAccountId();
        BCMPodContainerResponse resultResponse = new BCMPodContainerResponse();
        resultResponse.setPageNo(podListRequest.getPageNo());
        resultResponse.setPageSize(podListRequest.getPageSize());
        List<PodContainerForBCM> podContainerForBCMList;
        try {
            // get pod list from v1.0 and v2.0
            List<? extends PodPOForBcm> allPodPOsInV1 = podDaoV1.listPods(accountId, null, 
                    podListRequest.getKeywordType(), podListRequest.getKeyword());
            List<? extends PodPOForBcm> allPodPOsInV2 = podDao.listPods(accountId, null, 
                    podListRequest.getKeywordType(), podListRequest.getKeyword());
            List<? super PodPOForBcm> allPodPOs = new ArrayList<>();
            allPodPOs.addAll(allPodPOsInV1);
            allPodPOs.addAll(allPodPOsInV2);

            List<PodPOForBcm> podPOs = filterPodByChargeSource(allPodPOs);

            resultResponse.setTotalCount(podPOs.size());

            // paging
            List<PodPOForBcm> onePagePods = paging(podPOs, podListRequest.getPageNo(), podListRequest.getPageSize());

            List<String> podIds = new LinkedList<>();
            for (PodPOForBcm podPO : onePagePods) {
                podIds.add(podPO.getPodUuid());
            }

            List<? extends ContainerPOForBcm> containerPOsInV1 = containerDaoV1.listContainerByPod(accountId, podIds);
            List<? extends ContainerPOForBcm> containerPOsInV2 = containerDao.listContainerByPod(accountId, podIds);
            List<? super ContainerPOForBcm> allContainerPOs = new ArrayList<>();
            allContainerPOs.addAll(containerPOsInV1);
            allContainerPOs.addAll(containerPOsInV2);

            // PodContainer 转换为 PodContainerForBCM
            podContainerForBCMList = transPodContainerForBCM(onePagePods, allContainerPOs);

            resultResponse.setResult(podContainerForBCMList);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    public PodForBCM podDetail(String podId) {
        PodForBCM podForBCM = new PodForBCM();
        PodPOForBcm podPO = getPodPOByPodId(podId);

        wrapEipForPod(Collections.singletonList(podPO));
        try {
            BeanUtils.copyProperties(podForBCM, podPO);
        } catch (Exception e) {
            LOGGER.error("failed to copy pod: ", e);
        }
        return podForBCM;
    }

    private <T> List<T> paging(List<T> list, Integer pageNo, Integer pageSize) {
        int totalCount = list.size();
        List<T> pageList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = Math.max((pageNo - 1) * pageSize, 0);
            int end = Math.min(start + pageSize, totalCount);
            for (int i = start; i < end; i++) {
                pageList.add(list.get(i));
            }
        } else {
            pageList = list;
        }

        return pageList;
    }

    private void wrapEipForPod(List<PodPOForBcm> podPOS) {
        try {
            Map<String, EipInstance> eipInstanceMap = new HashMap<>();
            bciAsyncService.getEipInstanceMapAsync();
            eipInstanceMap = (Map<String, EipInstance>) asyncExecutorService.getAsyncResult(
                    WorkKeyUtil.genWorkKey("getEipInstanceMapAsync", new LinkedList<Object>()));
            for (PodPOForBcm podPO : podPOS) {
                podPO.setRegion(regionConfiguration.getCurrentRegion());
                if (eipInstanceMap.containsKey(podPO.getPodUuid())) {
                    podPO.setPublicIp(eipInstanceMap.get(podPO.getPodUuid()).getEip());
                }
            }
        } catch (Exception e) {
            // 服务降级
            LOGGER.error("query eip error: ", e);
        }
    }

    private List<PodPOForBcm> filterPodByChargeSource(List<? super PodPOForBcm> pods) {
        String chargeSource;
        if (ResourceAccountSetting.isUnifiedCharge()) {
            // 统一计费，匹配charge source
            chargeSource = ResourceAccountSetting.getApplication().toLowerCase();
        } else {
            // 根据charge source 过滤列表, 不展示统一计费的资源
            chargeSource = LogicalConstant.CHARGE_SOURCE_USER;
        }
        List<PodPOForBcm> filteredPods = new LinkedList<>();
        for (Object obj : pods) {
            PodPOForBcm podPO = (PodPOForBcm) obj;
            if (podPO.getChargeSource().equalsIgnoreCase(chargeSource)) {
                filteredPods.add(podPO);
            }
        }
        return filteredPods;
    }

    // containers 转换为 pod-> container list
    private List<PodContainerForBCM> transPodContainerForBCM(List<PodPOForBcm> pods, 
                                                             List<? super ContainerPOForBcm> containers) {
        List<PodContainerForBCM> podContainerForBCMS = new LinkedList<>();

        for (PodPOForBcm pod : pods) {
            PodContainerForBCM podContainer = new PodContainerForBCM();
            podContainer.setPodId(pod.getPodId());
            podContainer.setPodUuid(pod.getPodUuid());
            podContainer.setStatus(pod.getStatus());

            List<PodContainerForBCM.Container> bcmContainers = new LinkedList<>();
            for (Object obj : containers) {
                ContainerPOForBcm containerPO = (ContainerPOForBcm) obj;
                if (pod.getPodUuid().equalsIgnoreCase(containerPO.getPodUuid())) {
                    PodContainerForBCM.Container container = new PodContainerForBCM.Container();
                    container.setContainerName(containerPO.getName());
                    container.setId(containerPO.getContainerUuid());

                    bcmContainers.add(container);
                }
            }
            podContainer.setContainers(bcmContainers);
            podContainerForBCMS.add(podContainer);
        }

        return podContainerForBCMS;
    }
}