package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class PodDetail {
    private String name = "";
    private String podId = "";
    private String podUuid = "";
    private String status = "";
    private String nodeName = "";
    private int delayReleaseDurationMinute = 0;
    private boolean delayReleaseSucceeded = false;
    @JsonProperty(value = "vCpu")
    private float cpu = 0f;
    private float memory = 0f;
    private String cpuType = "";
    private String productType = "";
    private String gpuType = "";
    private float gpuCount = 0f;
    private float gpuMemory = 0f;
    private String eipUuid = "";
    private String eipId = "";
    private String publicIp = "";
    private int bandwidthInMbps = 0;
    private String eipRouteType = "";
    private String eipPayMethod = "";
    private boolean eipIsUserSpecified = false;
    private String cceUuid = "";
    private String internalIp = "";
    private String internalIPv6 = "";
    private String securityGroupUuid;
    private String restartPolicy = "";
    private String orderId = "";
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    private String description = "";
    private String userId = "";
    private String taskStatus = "";
    private List<Nfs> nfs = new ArrayList<>();
    private List<Pfs> pfs = new ArrayList<>();
    private List<Bos> bos = new ArrayList<>();
    private List<HostPathVolume> hostPath = new ArrayList<>();
    private List<EmptyDir> emptyDir = new ArrayList<>();
    private List<ServersResponse.ServerBciCds> podVolumes = new ArrayList<>();
    private List<ConfigFile> configFile = new ArrayList<>();
    @JsonProperty(value = "tags")
    private List<Tag> podTags = new ArrayList<>();
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ContainerDetail> containers = new ArrayList<>();
    private SimpleSecurityGroupVO securityGroup;
    private List<SimpleSecurityGroupVO> securityGroups;
    private VpcVo vpc;
    private SubnetVo subnet;
    private String logicalZone = "";
    private String region = "";
    private String subnetType = "";
    private String eipGroupId = "";
    private List<Label> labels;
    // 标记是否为v2 pod
    private boolean v2 = true;
    /**
     * 业务
     */
    private String application = "default";
    private boolean pushLog = false;
    // 新增字段，存储pod conditions信息
    private List<PodCondition> conditions;

    private Affinity affinity;
    private Long terminationGracePeriodSeconds;

    // 是否是潮汐pod
    private boolean isTidal;
    // vk传递的extra字段
    private Map<String,String> extras;

    @Data
    public static class ContainerDetail {

        private String name = "";
        private String containerType = "";
        private String containerUuid = "";
        private String imageName = "";
        private String imageVersion = "";
        private String imageID = "";
        private String imageAddress = "";
        private float cpu;
        private float memory;
        private String gpuType = "";
        private float gpuCount = 0f;
        private float gpuMemory = 0f;
        private String workingDir = "";
        private String imagePullPolicy = "";
        private List<String> commands = new ArrayList<>();
        private List<String> args = new ArrayList<>();
        private List<Port> ports = new ArrayList<>();
        private List<VolumeMounts> volumeMounts = new ArrayList<>();
        private List<Environment> envs = new ArrayList<>();
        private String userId = "";
        private ContainerStatus status;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Timestamp createdTime;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Timestamp updatedTime;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Timestamp deletedTime;
    }

    @Data
    public static class ContainerStatus {
        private ContainerPreviousState previousState;
        private ContainerCurrentState currentState;
        private int restartCount;
        private boolean ready;
        private Boolean started;
    }
}
