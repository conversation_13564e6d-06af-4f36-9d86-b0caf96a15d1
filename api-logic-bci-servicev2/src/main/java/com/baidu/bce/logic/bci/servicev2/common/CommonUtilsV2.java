package com.baidu.bce.logic.bci.servicev2.common;

import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.imagecache.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.PodCondition;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.statemachine.StateMachine;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.CreateInstanceContext;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.permission.PermissionExceptionUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.utils.UUIDUtil;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import com.baidu.bce.user.settings.sdk.model.FeatureAclRequest;
import com.baidu.bce.user.settings.sdk.model.FeatureAclResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;


@Component
public class CommonUtilsV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonUtilsV2.class);

    @Autowired
    private PodDaoV2 podDao;

    @Autowired
    private ContainerDaoV2 containerDao;

    @Autowired
    private PodExtraDaoV2 podExtraDao;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private ImageCacheDaoV2 imageCacheDao;

    @Autowired
    private StateMachine stateMachine;

    /**
     * 判断用户输入是否全部为短ID, 如是则将原ID SET 替换为对应的UUID SET
     *
     * @param instanceIdSet 原ID集合(若原ID全部为短ID,则执行此方法后会被全部替换为长ID)
     * @return
     */
    public void shortIdCheck(Set<String> instanceIdSet) {

        Set<String> idSetForCheck = new HashSet<>();
        Map<String, String> podMap = podDao.podIdMap(getAccountId());

        for (String originId : instanceIdSet) {
            if (podMap.containsKey(originId)) {
                idSetForCheck.add(podMap.get(originId));
            } else {
                LOGGER.debug("permission vertify deny! resouce {} not exist", originId);
                throw new PermissionExceptionUtil.PermissionDenyException();
            }
        }

        instanceIdSet.clear();
        instanceIdSet.addAll(idSetForCheck);
    }

    /**
     * {@inheritDoc}
     * 创建外部ID，格式为{idPrefix}-{shortUuid}。如果在重试10次后仍无法获取到唯一的ID，则抛出DuplicateKeyException异常。
     *
     * @param idPrefix ID前缀，不能为空
     * @return 返回格式为{idPrefix}-{shortUuid}的字符串，长度为36个字符，不包含大写字母
     * @throws DuplicateKeyException 如果在重试10次后仍无法获取到唯一的ID，则抛出此异常
     */
    public String createExternalId(String idPrefix) {
        int retryCount = 0;
        while (retryCount < 10) {
            try {
                String shortId = UUIDUtil.generateShortUuid();
                shortId = shortId.toLowerCase(); // k8s不允许大写字母
                String bciPodId = idPrefix + "-" + shortId;
                String uuid = podDao.queryPodUuid(bciPodId);
                if (StringUtils.isEmpty(uuid)) {
                    return bciPodId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate external_id in database.");
    }

    /**
     * @Description 根据标签过滤Pod列表，返回符合条件的Pod UUID列表。如果请求中不包含标签过滤信息或者标签过滤信息无效，则返回null。
     * @Param podListRequest Pod列表请求，包含过滤条件，格式为"key1__value1,key2__value2"，其中key和value之间用"__"分隔，多个过滤条件以","分隔。
     * @Return List<String> 符合过滤条件的Pod UUID列表，如果过滤条件无效或者没有符合条件的Pod，则返回空列表。
     * @Throws CommonExceptions.RequestInvalidException 当请求中的标签过滤信息无效时抛出此异常。
     */
    public List<String> filterByTag(PodListRequest podListRequest) {

        List<String> podUuids = new ArrayList<>();
        if (podListRequest == null || !podListRequest.getFilterMap().containsKey("tag")) {
            return null;
        }

        String[] tagKeyValues = podListRequest.getFilterMap().get("tag").split("__");
        if (tagKeyValues.length < 1) {
            throw new CommonExceptions.RequestInvalidException("tag filter is invalid");
        }

        if (StringUtils.isBlank(tagKeyValues[0])) {
            return podUuids;
        }

        // 查询tag服务,获取tag对应的bci资源uuid
        try {
            LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
            FullTagListRequest tagRequest = new FullTagListRequest();
            tagRequest.setServiceTypes(Arrays.asList(PodConstants.SERVICE_TYPE));
            tagRequest.setTagKey(tagKeyValues[0]);
            if (tagKeyValues.length < 2) {
                tagRequest.setTagValue("");
            } else {
                tagRequest.setTagValue(tagKeyValues[1]);
            }

            TagAssociationFulls tagFulls = tagClient.listFullTags(tagRequest);
            for (TagAssociationFull tagFull : tagFulls.getTagAssociationFulls()) {
                if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(tagFull.getServiceType())) {
                    podUuids.add(tagFull.getResourceUuid());
                }
            }
        } catch (Exception e) {
            // 降级tag服务
            LOGGER.error("filter by tag error: {}", e);
        }
        return podUuids;
    }

    @Transactional(rollbackFor = CommonExceptions.InternalServerErrorException.class)
    public int savePodExtraCreate2DB(PodExtraPO podExtraPO) {
        return podExtraDao.insertPodExtra(podExtraPO);
    }

    // TODO:原有savePodCreate2DB方法，状态机重构上线后删除
    // 理论上状态机重构上线后,此方法不会再被调用
    @Transactional(rollbackFor = CommonExceptions.InternalServerErrorException.class)
    public List<String> savePodCreate2DB(PodPO podPO, List<ContainerPO> containers) {
        if (podPO == null || CollectionUtils.isEmpty(containers)) {
            LOGGER.error("savePodCreate2DB: pod or container is null");
            throw new CommonExceptions.InternalServerErrorException();
        }
        List<String> podIds = new ArrayList<>();

        if (StringUtils.isEmpty(podPO.getClientToken())) {
            podPO.setClientToken(podPO.getPodId());
        }
        podIds.add(podPO.getPodId());

        try {
            List<PodPO> pods = new ArrayList<>();
            pods.add(podPO);
            podDao.batchInsertPods(pods);
            LOGGER.debug("[bce logical bci] Update containers : {}", JsonUtil.toJSON(containers));
            containerDao.batchInsert(containers);
        } catch (Exception e) {
            LOGGER.error("[bce logical bci] Update logical failed, operation is {}", "[create bci] ", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        return podIds;
    }

    // 状态机重构后savePodCreate2DB方法
    // 代码来源:拷贝原来的savePodCreate2DB方法,并修改
    @Transactional(rollbackFor = CommonExceptions.InternalServerErrorException.class)
    public Map<String, PodPO> savePodCreate2DB(
            Map<String, PodPO> pods,
            Map<String, List<ContainerPO>> containers,
            Map<String, BciOrderExtra> bciOrderExtras) {
        if (pods.isEmpty() || containers.isEmpty()) {
            LOGGER.error("savePodCreate2DB pods or containers is empty");
            throw new CommonExceptions.InternalServerErrorException();
        }
        Map<String, PodPO> podMap = new HashMap<>();
        try {
            for (Map.Entry<String, PodPO> entry : pods.entrySet()) {
                PodPO podPO = entry.getValue();
                String podId = podPO.getPodId();
                PodPO podIdempotentCheck = podDao.podIdempotentCheck(podPO);
                if (podIdempotentCheck != null) {
                    if (StringUtils.isNotEmpty(podIdempotentCheck.getOrderId())) {
                        LOGGER.info("pod has created, podId:{} orderId:{}", podId, podIdempotentCheck.getOrderId());
                        podMap.put(podIdempotentCheck.getPodId(), podIdempotentCheck);
                        continue;
                    }
                    // 上一个请求正在执行中，例如：创建订单，抛出重复调用异常
                    throw new PodExceptions.CreateRepeatedInvocationException();
                }
                StateMachineEvent createEvent = StateMachineEvent.CREATE_INSTANCE;
                CreateInstanceContext createContext = new CreateInstanceContext();
                LOGGER.debug("savePodCreate2DB begin state machine trigger podId:{} event:{} pod:{}",
                        podId, createEvent, JsonUtil.toJSON(podPO));
                if (!stateMachine.trigger(podPO, createEvent, createContext)) {
                    LOGGER.error("savePodCreate2DB state machine trigger failed, podId:{} event:{}",
                            podId, createEvent);
                    if (createContext.getException() != null) {
                        LOGGER.error("savePodCreate2DB state machine trigger failed, podId:{} event:{} exception:{}",
                                podId, createEvent, createContext.getException().getException());
                        throw createContext.getException().getException();
                    }
                    continue;
                }
                LOGGER.info("savePodCreate2DB end state machine trigger success, podId:{} event:{}",
                        podId, createEvent);
                LOGGER.info("savePodCreate2DB save pod containers into db, podId:{} event:{} containers:{}",
                        podId, createEvent, JsonUtil.toJSON(containers));
                if (containers.get(entry.getKey()) != null) {
                    containerDao.batchInsert(containers.get(entry.getKey()));
                }
                if (bciOrderExtras.get(podId) != null) {
                    PodExtraPO podExtraPO = generatePodExtraPOFromBciOrderExtra(bciOrderExtras.get(podId));
                    podExtraDao.insertPodExtra(podExtraPO);
                }
                podMap.put(podPO.getPodId(), podPO);
            }
        } catch (BceException e) {
            LOGGER.error("savePodCreate2DB save pod and contianer into db failed, Exception is {}", "[create bci] ", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("savePodCreate2DB save pod and contianer into db failed, Exception is {}", "[create bci] ", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        return podMap;
    }

    public PodExtraPO generatePodExtraPOFromBciOrderExtra(BciOrderExtra bciOrderExtra) {
        PodExtraPO podExtraPO = new PodExtraPO();
        podExtraPO.setPodId(bciOrderExtra.getPodId());
        podExtraPO.setUserId(bciOrderExtra.getUserId());
        podExtraPO.setOrderExtra(generateOrderExtraString(bciOrderExtra));
        return podExtraPO;
    }

    public String generateOrderExtraString(BciOrderExtra bciOrderExtra) {
        String extra = "";
        if (bciOrderExtra == null) {
            return extra;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            extra = objectMapper.writeValueAsString(bciOrderExtra);
        } catch (IOException e) {
            return extra;
        }
        return extra;
    }

    public boolean checkWhiteList(String featureType, String region, String accountId) {
        UserSettingsClient client = logicPodClientFactory.createUserSettingsClient(accountId);
        FeatureAclRequest request = new FeatureAclRequest();
        request.setFeatureType(featureType);
        request.setAclType("AccountId");
        request.setAclName(accountId);
        request.setRegion(region);
        FeatureAclResponse response = client.isInFeatureAcl(request);
        return response.getIsExist();
    }


    public String createTaskId() {
        int retryCount = 0;
        while (retryCount < 10) {

            String id = UUID.randomUUID().toString();
            int count = imageCacheDao.getTaskById(id);
            if (count == 0) {
                return id;
            }
            retryCount++;
        }
        throw new DuplicateKeyException("Duplicate external_id in database.");
    }

    @Transactional(rollbackFor = CommonExceptions.InternalServerErrorException.class)
    public boolean saveCreatePodFailedWithReason(String userId,
                                                 String podId,
                                                 List<PodCondition> podConditions,
                                                 ContainerCurrentState containerCurrentState) {
        if (StringUtils.isEmpty(userId) ||
                StringUtils.isEmpty(podId) ||
                CollectionUtils.isEmpty(podConditions) ||
                containerCurrentState == null) {
            LOGGER.error("saveCreatePodFailedWithReason:" +
                    " userId or podId or podConditions or containerCurrentState is null");
            throw new CommonExceptions.InternalServerErrorException();
        }
        try {
            String podConditionsJson = JsonUtil.toJSON(podConditions);
            LOGGER.debug("begin updatePodConditionsByPodId podId:{}, podConditionsJson:{}",
                    podId, podConditionsJson);
            int updatePodConditionsByPodIdRet = podDao.updatePodConditionsByPodId(userId, podId, podConditionsJson);
            LOGGER.debug("end updatePodConditionsByPodId podId:{}, podConditionsJson:{} " +
                    "updatePodConditionsByPodIdRet:{}", podId, podConditionsJson, updatePodConditionsByPodIdRet);

            String containerCurrentStateJson = JsonUtil.toJSON(containerCurrentState);
            LOGGER.debug("begin updateContainerCurrentStateByPodUuid podId:{}, containerCurrentStateJson:{}",
                    podId, containerCurrentStateJson);
            int updateContainerCurrentStateByPodUuidRet =
                    containerDao.updateContainerCurrentStateByPodUuid(userId, podId, containerCurrentStateJson);
            LOGGER.debug("end updateContainerCurrentStateByPodUuid podId:{}, containerCurrentStateJson:{} " +
                            "updateContainerCurrentStateByPodUuidRet:{}",
                    podId, containerCurrentStateJson, updateContainerCurrentStateByPodUuidRet);
        } catch (Exception e) {
            LOGGER.error("saveCreatePodFailedWithReason failed, podId:{}, exception is {}", podId, e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        return true;
    }
}
