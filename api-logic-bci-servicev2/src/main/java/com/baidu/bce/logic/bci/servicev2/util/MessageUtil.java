package com.baidu.bce.logic.bci.servicev2.util;

import java.util.UUID;

import com.baidu.bce.internalsdk.iam.model.AccessKey;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.messages.facade.constant.ReceiverType;
import com.baidu.bce.messages.facade.http.SendMessageRequest;
import com.baidu.bce.messages.facade.http.SendMessageResponse;
import com.baidu.bce.messages.sdk.MessagesClient;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

// import endpoint.EndpointManager;

@Component
public class MessageUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageUtil.class);

    // private final String messageTemplateId = "Tpl_fb3f1cf0-8a72-4368-b53a-423336ed8f7c";--测试，邮件
    private final String messageTemplateId = "Tpl_c1e19b91-b3bd-479e-8817-4fb2b25e429e";

    @Autowired
    private IamLogicService iamLogicService;

    private MessagesClient createMessagesClient() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        // 需要使用服务号accesskey，普通用户身份会被拒绝
        // Client遵循bce-internal-sdk-core约定，如endpoint获取、requestid透传、BceInternalResponseException异常等
        // EndpointManager.setEndpoint("Messages", "http://messages.bj.bce-internal.baidu.com:8681/v1");
        // MessagesClient client = new MessagesClient("97f90c884ff544459fcbed3459e5e6b7", 
        //                                        "f5ef0895d82e45ddb9c9cc5b18bd4564");
        // EndpointManager.setEndpoint("Messages", "http://gzbh-sandbox20-6271.gzbh.baidu.com:8681/v1");
        // MessagesClient client = new MessagesClient("0380cf430e2b44b18ad78378052e53d2", 
        //                                        "dcd8ac7c20494130a824f40a259c9455");
        MessagesClient client = new MessagesClient(accessKey.getAccess(), accessKey.getSecret());
        return client;
    }
     
    public void sendMessage(String userId, String contentVar) {
        // https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/qVJyAQ7bS3/THaLpPuU_IlPGT
        // https://eop.baidu-int.com/osp-product/product/iam/message/task_template_list
        SendMessageRequest request = new SendMessageRequest();
        request.setId(UUID.randomUUID().toString()); // 每次发送，需要使用不同的id
        // request.setTemplateId("Tpl_c1e19b91-b3bd-479e-8817-4fb2b25e429e"); // 需要指定所使用的模板id test
        request.setTemplateId(messageTemplateId);
        request.setContentVar(contentVar); // JSON格式字符串,"{\"region\":\"hn\", \"podId\":\"podId\"}"
        request.setReceiver(userId);
        request.setReceiverType(ReceiverType.UserId);
        try {
            SendMessageResponse response = createMessagesClient().sendMessage(request);
        } catch (BceInternalResponseException e) {
            // 此处根据返回错误码进行业务的容错处理
            LOGGER.error("send to userId:" + userId + " message failed, error: " + e);
        }
    }
}
