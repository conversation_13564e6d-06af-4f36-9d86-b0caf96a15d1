package com.baidu.bce.logic.bci.servicev2.exception;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

public class OpsExceptions extends CommonExceptions {

    public static class OpsTaskException extends BceException {
        public OpsTaskException(String errMsg) {
            super(errMsg, HttpStatus.ERROR_INPUT_INVALID, "OPS.RequestParamException");
            setRequestId(LogicUserService.getRequestId());
        }

        public OpsTaskException(String errMsg, int errCode) {
            super(errMsg, errCode, "OPS.InternelServerError");
            setRequestId(LogicUserService.getRequestId());
        }
    }

}
