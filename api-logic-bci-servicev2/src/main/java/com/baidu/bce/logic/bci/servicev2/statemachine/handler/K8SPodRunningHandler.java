package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("K8S_POD_RUNNING")
public class K8SPodRunningHandler extends K8SPodEventStatusBaseHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(K8SPodRunningHandler.class);
    @Override
    public boolean check() {
        return super.check();
    }

    @Override
    public boolean execute() {
        return super.execute();
    }
}
