package com.baidu.bce.logic.bci.servicev2.monitor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.http.NameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SimpleHttpClient {
    private static final Logger LOG = LoggerFactory.getLogger(SimpleHttpClient.class);

    public String callPostWithFormUrlEncoded(String url, Map<String, String> headerParams, 
                                             Map<String, String> bodyParams, int timeoutMS) throws IOException {
        HttpPost httpMethod = new HttpPost(url);
        RequestConfig config = RequestConfig.custom().setConnectTimeout(timeoutMS)
                                            .setConnectionRequestTimeout(timeoutMS)
                                            .setSocketTimeout(timeoutMS).build();
        httpMethod.setConfig(config);
        httpMethod.addHeader("content-type", "application/x-www-form-urlencoded");

        // set header params
        for (Map.Entry<String, String> entry : headerParams.entrySet()) {
            httpMethod.addHeader(entry.getKey(), entry.getValue());
        }

        // set body params
        List<NameValuePair> nameValuePairs = new ArrayList<NameValuePair>();
        for (Map.Entry<String, String> paramEntry : bodyParams.entrySet()) {
            nameValuePairs.add(new BasicNameValuePair(paramEntry.getKey(), paramEntry.getValue()));
        }
        httpMethod.setEntity(new UrlEncodedFormEntity(nameValuePairs));

        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String result = null;
        try {
            response = client.execute(httpMethod);
            int statusCode = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK != statusCode) {
                String msg = String.format("req %s failed, status code is %s", url, statusCode);
                LOG.warn(msg);
                throw new HttpException(msg);
            }
            if (response.getEntity() != null) {
                result = EntityUtils.toString(response.getEntity());
            }
        } finally {
            client.close();
            if (response != null) {
                response.close();
            }
        }
        return result;
    }
}