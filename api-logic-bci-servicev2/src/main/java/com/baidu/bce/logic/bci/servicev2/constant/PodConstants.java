package com.baidu.bce.logic.bci.servicev2.constant;


public class PodConstants {

    public static final String POD_PREFIX = "p-";
    public static final String FROM_API = "api";
    public static final String FROM_CONSOLE = "console";
    public static final String FROM_OPENAPI = "openapi";

    public static final String FROM_INNERAPI = "innerapi";

    public static final String FROM_BILLING = "billing";

    public static final String RENEW_TIME_UNIT_MONTH = "month";
    public static final String RENEW_TIME_UNIT_YEAR = "year";
    public static final int RENEW_MAX_MONTH = 9;
    public static final int RENEW_MAX_YEAR = 3;

    public static final int DELAY_RELEASE_MAX_MINUTE = 2147483647;

    public static final String NFS = "nfs";
    public static final String EMPTYDIR = "empty_dir";
    public static final String CONFIGFILE = "config_file";
    public static final String HOSTPATH = "host_path";

    public static final String SERVICE_TYPE = "BCI";

    public static final String WEBSHELL = "webshell";

    public static final String CHAREG_SUCC = "succ";

    public static final String CHARGE = "charge";
    public static final String NO_CHARGE = "noCharge";

    public static final String DELETED = "deleted";

    public static final String CPU_TYPE_INTEL = "intel";
    public static final String CPU_TYPE_AMD = "amd";

    public static final String BCI_INTERNAL_STATUS_UNSYNC = "unsync";
    public static final String BCI_INTERNAL_STATUS_SYNCED = "synced";

    public static final String BCI_INTERNAL_PREFIX = "bci_internal_";
    public static final String BCI_CONTAINER_PREFIX = "bci-internal-";
    public static final String BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX =
            "bci-internal-image-download-init-container-";
    public static final String BCI_IMAGE_DOWNLOAD_CONTAINER_PREFIX =
            "bci-internal-image-download-container-";
    public static final String BCI_IMAGE_DOWNLOAD_INIT_INFIX = "image-init-";
    public static final String BCI_IMAGE_DOWNLOAD_WORKLOAD_INFIX = "image-workload-";
    public static final String BCI_IMAGE_DOWNLOAD_DS_WORKLOAD_INFIX = "image-ds-";
    public static final String BCI_NFS_SIDECAR_CONTAINER_PREFIX =
            "bci-internal-nfs-sidecar-container-";
    public static final String BCI_PFS_SIDECAR_CONTAINER_PREFIX =
            "bci-internal-pfs-sidecar-container-";
    public static final String BCI_BOS_SIDECAR_CONTAINER_PREFIX =
            "bci-internal-bos-sidecar-container-";
    public static final String BCI_LOG_SIDECAR_CONTAINER_PREFIX =
            "bci-internal-log-sidecar-container-";
    public static final String BCI_KUBEPROXY_SIDECAR_CONTAINER_PREFIX =
            "bci-internal-kubeproxy-sidecar-container-";

    public static final String BCI_CEPHFS_SIDECAR_CONTAINER_PREFIX =
            "bci-internal-cephfs-sidecar-container-";
    public static final String BCI_NTP_SIDECAR_CONTAINER_PREFIX = "bci-internal-ntp-sidecar-container-";
    public static final String BCI_POST_START_SIDECAR = "bci-internal-post-start-sidecar";
    public static final String BCI_KUBELET_PROXY_SIDECAR_CONTAINER = "bci-internal-kubeletproxy-sidecar-container";

    public static final String BCI_KUBELET_PROXY_SIDECAR_UDS_VOLUME_NAME = "kubelet-proxy-socket";
    public static final String BCI_KUBELET_PROXY_SIDECAR_SRC_POD_LOGS = "src-pod-logs";
    public static final String BCI_KUBELET_PROXY_SIDECAR_DEST_POD_LOGS = "dest-pod-logs";

    public static final String CONTAINER_PULL_IMAGE_ERROR = "PullImageErr";
    public static final String CONTAINER_PULLING_IMAGE = "PullingImage";

    public static final String POD_ALLOCATE_IP_ERROR = "AllocateIPErr";

    public static final String BCI_POD_MATCH_TYPE_ANNOTATION_KEY = BCI_INTERNAL_PREFIX + "matchType";

    public static final String BCI_POD_MATCH_TYPE_TIDAL = "tidal";

    public static final String BCI_POD_MATCH_TYPE_PFS = "pfs";

    public static final String BCI_POD_MATCH_TYPE_BOS = "bos";

    public static final String BCI_AUTO_DELETE_POD_ANNOTATION_KEY = BCI_INTERNAL_PREFIX + "AutoDeletePod";

    public static final String BCI_TIDAL_POD_RECYCLED = "Recycled";

    public static final String KUBE_PROXY_SIDECAR_LABEL_KEY = "bci.virtual-kubelet.io/kubeproxy-container";

    public static final String BCI_POD_LIMIT_KEY = "bci.virtual-kubelet.io/bci-pod-limits";

    public static final String BCI_POD_RESOURCE_TAG_ANNOTATION_KEY = "bci.virtual-kubelet.io/resource-tag";

    public static final String BCI_MAX_PENDING_MINUTE_ANNOTATION_KEY = "bci.virtual-kubelet.io/bci-max-pending-minute";

    public static final String BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY = "bci.virtual-kubelet" +
            ".io/bci-ignore-exit-code-containers"; 

    public static final String BCI_IGNORE_NOT_READY_CONTAINERS_ANNOTATION_KEY = "bci.virtual-kubelet.io/bci-ignore-not-ready-containers";

    public static final String BCI_RESOURCE_IGNORE_CONTAINERS_ANNOTATION_KEY = "bci.virtual-kubelet" +
            ".io/bci-resource-ignore-containers";

    public static final String BCI_FAIL_STRATEGY_ANNOTATION_KEY = "bci.virtual-kubelet.io/bci-fail-strategy";

    public static final int BCI_MAX_PENDING_MINUTE_MIN_VALUE = 10;

    public static final int BCI_MAX_PENDING_MINUTE_MAX_VALUE = 1440;

    public static final String BCI_POD_LABELS_POD_NAME_KEY = "PodName";

    public static final String BCI_POD_LABELS_POD_NAMESPACE_KEY = "Namespace";

    public static final String BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY = "original-pod-name";

    public static final String BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAMESPACE_KEY = "original-pod-namespace";

    public static final String BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAME_KEY = BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAME_KEY;
    public static final String BCI_POD_METADATA_ANNOATION_ORIGINAL_POD_NAMESPACE_KEY = BCI_POD_METADATA_LABELS_ORIGINAL_POD_NAMESPACE_KEY;

    public static final int BCI_POD_RESOURCE_TAG_MAX_SIZE = 10;

    public static final String BCI_INTERNAL_ENABLE_POD_LIMIT = "bci_internal_EnablePodLimits";
    public static final String BCI_POD_LIMIT_PATTERN = "^\"(([0-9]\\d*)|(0{1}))(\\.\\d{1,2})?-(([0-9]\\d*)|(0{1}))(\\.\\d{1,2})?(E|P|T|G|M|k|Ei|Pi|Ti|Gi|Mi|Ki)\"$";

    public static final String BCI_DS_CONTAINERS_VERSION = BCI_INTERNAL_PREFIX + "dsContainersVersion";

    public static final String BCI_DS_CONTAINER_VERSION_MAP = BCI_INTERNAL_PREFIX + "dsContainerVersionMap";

    public static final String BCI_DS_CONTAINER_NAMES = BCI_INTERNAL_PREFIX + "dsContainers";

    public static final String BCI_DS_FUNCTIONAL_CONTAINER_NAMES = BCI_INTERNAL_PREFIX + "dsFunctionalContainers";

    public static final String BCI_DS_VOLUME_NAME_2_TYPE = BCI_INTERNAL_PREFIX + "dsVolumeName2Type";

    public static final String BCI_CHANGED_DS_CONTAINER_2_IMAGE_CONTAINER = 
                               BCI_INTERNAL_PREFIX + "changedDsContainer2ImageContainer";
    
    public static final String BCI_IGNORE_THIS_CONTAINERS_COMPUTING_QOS = 
                               BCI_INTERNAL_PREFIX + "ignore_this_contianers_computing_qos";

    public static final String ORIGIN_CONTAINER_IMAGE_NAME = "ORIGIN_CONTAINER_IMAGE_NAME";

    public static final String BCI_INJECT_KUBE_PROXY_INIT_CONTAINER = "injectKubeProxyInitContainer";

    public static final String BCI_INJECT_KUBE_PROXY_INIT_CONTAINER_ANNOTATION_NAME = BCI_INTERNAL_PREFIX + BCI_INJECT_KUBE_PROXY_INIT_CONTAINER;

    /**
     * 普1  kvm
     * 普2  HM-CPU-v4_e5_2680
     * 普3  HM-CPU-Gold_6148
     * 普4  HM-CPU-Gold_6271
     */

    public static final String INSTANCE_TYPE_G1 = "kvm";
    public static final String INSTANCE_TYPE_G2 = "HM-CPU-v4_e5_2680";
    public static final String INSTANCE_TYPE_G3 = "HM-CPU-Gold_6148";
    public static final String INSTANCE_TYPE_G4 = "HM-CPU-Gold_6271";

    public static final String CPU_TYPE = "cpu";
    public static final String GPU_TYPE = "gpu";

    public static final int POD_NO_DELETED = 0;
    public static final int POD_DELETED = 1;

    public static final String TERMINATION_MESSAGE_POLICY_FILE = "File";
    public static final String TERMINATION_MESSAGE_POLICY_FALLBACKTOLOGSONERROR = "FallbackToLogsOnError";
    public static final String TERMINATION_MESSAGE_PATH_DEFAULT = "/dev/termination-log";

    public static final int POD_LABEL_MAX_KEY_LENGTH = 63;
    public static final int POD_LABEL_MAX_VALUE_LENGTH = 63;

    public static final int POD_ANNOTATION_MAX_KEY_LENGTH = 253;
    public static final int POD_ANNOTATION_MAX_VALUE_LENGTH = 65536;

    public static final String BCI_ENABLE_IPV6_ANNOTATION_KEY = "bci.baidu.com/bci-enable-ipv6";

}
