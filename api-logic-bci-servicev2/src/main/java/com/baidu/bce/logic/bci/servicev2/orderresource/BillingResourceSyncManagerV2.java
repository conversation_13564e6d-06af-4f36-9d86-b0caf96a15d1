package com.baidu.bce.logic.bci.servicev2.orderresource;

import com.baidu.bce.logic.bci.daov2.chargestatus.PodChargeStatusDaoV2;
import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.constant.CPT1SyncStatus;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

@Service("BillingResourceSyncManager")
public class BillingResourceSyncManagerV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(BillingResourceSyncManagerV2.class);

    @Autowired
    private LogicalResourceServiceV2 logicalResourceService;

    @Autowired
    @Qualifier("billingResourceTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private PodChargeStatusDaoV2 podChargeStatusDao;

    public static final int MAX_RETRY_COUNT_AFTER_SYNC_TO_BILLING_FAILED = 10;

    public void doSync(PodPO podPO, String accountId, String podUuid, long resourceVersion, String chargeStatus) {
        threadPoolTaskExecutor.execute(new SyncPodChargeStatus(podPO, 0, accountId, podUuid,
                resourceVersion, chargeStatus, 0, true));
    }

    public void doSync(PodPO podPO, long chargeStatusId, String accountId, String podUuid, long resourceVersion,
                       String chargeStatus, int retriedCount) {
        if (retriedCount > MAX_RETRY_COUNT_AFTER_SYNC_TO_BILLING_FAILED) {
            LOGGER.error("[call billing] sync pod status to billing over times limit, version {}, pod {}, status {}, account {}," +
                    " retriedCount {}", resourceVersion, podUuid, chargeStatus, accountId, retriedCount);
            return;
        }

        threadPoolTaskExecutor.execute(new SyncPodChargeStatus(podPO, chargeStatusId, accountId, podUuid,
                resourceVersion,
                chargeStatus, retriedCount, false));
        LOGGER.debug("[call billing] insert billing sync work, id {}, version {}, pod {}, status {}, account {}, retriedCount {}",
                chargeStatusId, resourceVersion, podUuid, chargeStatus, accountId, retriedCount);
        // 获取当前线程池任务数量，超过给定值报警，暂定超过队列最大容量一半报警
        int waitNum = threadPoolTaskExecutor.getThreadPoolExecutor().getQueue().size();
        if (waitNum > threadPoolTaskExecutor.getMaxPoolSize() / 2) {
            LOGGER.warn("[call billing] billing sync thread pool wait task over flow, wait num {}, max pool size {}",
                    waitNum, threadPoolTaskExecutor.getMaxPoolSize());
        }
    }

    public class SyncPodChargeStatus implements Runnable {
        private final PodPO podPO;
        private final long id;
        private final String accountId;
        private final String podUuid;
        private final long resourceVersion;
        private final String chargeStatus;
        private final int retriedCount;
        private final boolean noRetryIfVersionIsNonNewest;

        public SyncPodChargeStatus(PodPO podPO, long id, String accountId, String podUuid, long resourceVersion,
                                   String chargeStatus, int retriedCount, boolean noRetryIfVersionIsNonNewest) {
            this.podPO = podPO;
            this.id = id;
            this.accountId = accountId;
            this.podUuid = podUuid;
            this.resourceVersion = resourceVersion;
            this.chargeStatus = chargeStatus;
            this.retriedCount = retriedCount;
            this.noRetryIfVersionIsNonNewest = noRetryIfVersionIsNonNewest;

        }

        @Override
        public void run() {
            syncStatusToBilling(podPO, id, accountId, podUuid, resourceVersion, chargeStatus, retriedCount,
                    noRetryIfVersionIsNonNewest);
        }

        public void syncStatusToBilling(PodPO podPO, long chargeStatusId, String accountId, String podUuid,
                                        long resourceVersion,
                                         String chargeStatus, int retriedCount, boolean noRetryIfVersionIsNonNewest) {
            retriedCount += 1;

            LOGGER.info("[call billing] begin sync pod status to billing, id {}, version {}, pod {}, status {}, account {}, " +
                            "retriedCount {}", chargeStatusId, resourceVersion, podUuid, chargeStatus,
                    accountId, this.retriedCount);
            try {
                if (chargeStatus.equals(PodConstants.CHARGE)) {
                    logicalResourceService.startResourceByNameV2(accountId, podUuid, PodConstants.SERVICE_TYPE);
                } else if (chargeStatus.equals(PodConstants.NO_CHARGE)) {
                    if (null != podPO) {
                        long resourceRecycleTimestamp = podPO.getResourceRecycleTimestamp();
                        String resourceRecycleReason = podPO.getResourceRecycleReason();
                        // 如果pod被删除或者资源回收时间不为0且回收原因为JOB_POD_COMPLETE，则直接销毁资源
                        if (podPO.getDeleted() == 1 ||
                                (resourceRecycleTimestamp != 0L &&
                                        resourceRecycleReason.equals(ResourceRecycleReason.JOB_POD_COMPLETE.toString())
                                )) {
                            logicalResourceService.destroyResourceByNameV2(accountId, podUuid, PodConstants.SERVICE_TYPE);
                        } else {
                            logicalResourceService.pauseResourceByNameV2(accountId, podUuid, PodConstants.SERVICE_TYPE);
                        }
                    } else {
                        logicalResourceService.pauseResourceByNameV2(accountId, podUuid, PodConstants.SERVICE_TYPE);
                    }
                } else {
                    LOGGER.error("[call billing] sync pod status to billing given wrong status, id {}, version {}, pod {}, " +
                                    "status {}, account {}, retriedCount {}",
                            chargeStatusId, resourceVersion, podUuid, chargeStatus, accountId, this.retriedCount);
                }
                if (noRetryIfVersionIsNonNewest) {
                    // 定时对账场景下，bci实例元信息与podChargeStatus表状态可能不一致，此时以bci元信息为准
                    // 所以不需要查看最新的状态与当前状态是否一致以及不一致时的重试操作
                    return;
                }
                // 标记当前记录为同步完成，并查询是否有比当前状态更新的记录，有就加入队列
                if (chargeStatusId != 0) {
                    // 当chargeStatusId等于0时，说明不是当前副本插入的podChargeStatus表，无需更新
                    podChargeStatusDao.updateCpt1SyncStateById(chargeStatusId, resourceVersion,
                            podUuid, CPT1SyncStatus.DONE);
                }

                long maxVersion = podChargeStatusDao.getMaxResourceVersion(podUuid, resourceVersion);
                // 小于等于不用再做，大于时说明有新的podChargeStatus插入，需要继续做同步
                if (maxVersion <= resourceVersion) {
                    LOGGER.info("[call billing] sync pod status to billing success, version {}, pod {}, status {}, account {}, " +
                            "retriedCount {}", resourceVersion, podUuid, chargeStatus, accountId, this.retriedCount);
                    return;
                }
                // 当前同步任务执行期间，产生了更新的chargeStatus记录，所以继续更新
                PodChargeStatus podChargeStatus = podChargeStatusDao.queryByResourceVersion(podUuid, maxVersion);
                if (null == podChargeStatus) {
                    return;
                }

                LOGGER.warn("[call billing] sync pod status to billing, id {}, version {}, pod {}, status {}, account {}, has new " +
                                "status, newId {}, newVersion {}, newStatus {}, retriedCount {}",
                        chargeStatusId, resourceVersion, podUuid, chargeStatus, accountId, podChargeStatus.getId(),
                        podChargeStatus.getResourceVersion(), podChargeStatus.getChargeState(), this.retriedCount);

                doSync(podPO, podChargeStatus.getId(), accountId, podUuid, podChargeStatus.getResourceVersion(),
                        podChargeStatus.getChargeState(), retriedCount);
            } catch (Exception e) {
                if (retriedCount > MAX_RETRY_COUNT_AFTER_SYNC_TO_BILLING_FAILED) {
                    LOGGER.error("[call billing] sync pod status to billing error, id {}, version {}, pod {}, status {}, " +
                                    "account {}, retriedCount {}, error ",
                            chargeStatusId, resourceVersion, podUuid, chargeStatus, accountId, retriedCount, e);
                }
                // 需要查询当前order的状态，重新放入队列
                doSync(podPO, chargeStatusId, accountId, podUuid, resourceVersion, chargeStatus, retriedCount);
            }
        }
    }
}
