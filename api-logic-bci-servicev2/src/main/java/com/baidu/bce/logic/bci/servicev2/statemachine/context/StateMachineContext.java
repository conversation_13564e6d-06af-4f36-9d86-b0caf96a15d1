package com.baidu.bce.logic.bci.servicev2.statemachine.context;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Service;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service
public class StateMachineContext {
    private String podId;
    private String userId;
    private PodPO podPO;
    private StateMachineEvent event;
    private StateMachineEventContext eventContext;
    private long k8SResourceVersion;
    private long bciResourceVersion;
}
