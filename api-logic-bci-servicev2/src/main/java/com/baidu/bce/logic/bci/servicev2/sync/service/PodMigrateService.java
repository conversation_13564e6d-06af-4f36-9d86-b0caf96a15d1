package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodSpec;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.baidu.bce.logic.bci.servicev2.sync.service.PodContainerSyncServiceV2.ENI_CREATE_FAILED;

@Service
public class PodMigrateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodMigrateService.class);

    private static final String MIGRATE_ENI_POD_KEY = "bci_internal_migrateEniPod";

    private static final String MIGRATE_ENI_POD_RETRY_COUNT = "bci_internal_migrateEniPodRetryCount";
    @Autowired
    private K8sService k8sService;

    @Autowired
    protected LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private PodDaoV2 podDaoV2;

    private static ExecutorService executorService = new ThreadPoolExecutor(1, 6,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>());

    /**
     * 是否是重调度pod，目前只适配eni创建失败case
     *
     * @param pod
     * @return
     * @throws ApiException
     * @throws K8sServiceException
     */
    public boolean isMigratePod(V1Pod pod) throws ApiException, K8sServiceException {
        if (pod.getMetadata() == null || MapUtils.isEmpty(pod.getMetadata().getAnnotations())) {
            return false;
        }
        String migrateEniPod = pod.getMetadata().getAnnotations().get(MIGRATE_ENI_POD_KEY);
        if (migrateEniPod == null) {
            return false;
        }
        // 删除pod
        k8sService.deleteMigratePod(pod.getMetadata().getNamespace(), pod.getMetadata().getName());
        return true;
    }

    /**
     * 重调度迁移pod
     *
     * @param pod
     * @throws K8sServiceException
     * @throws ApiException
     */
    public void createMigratePod(final V1Pod pod) throws K8sServiceException, ApiException {
        LOGGER.error("PodMigrateService create Migrate pod {}:{} ", pod.getMetadata().getNamespace(),
                pod.getMetadata().getName());

        executorService.execute(new Runnable() {
            @Override
            public void run() {
                V1Pod migratePod = null;
                try {
                    migratePod = buildMigrateEniPod(pod);
                } catch (K8sServiceException e) {
                    return;
                }
                // 最多重试32s
                for (int i = 0; i <= 15; i++) {
                    try {
                        V1Pod k8sPod = k8sService.getPod(pod.getMetadata().getNamespace(), pod.getMetadata().getName());
                        // 确保pod 已经不在后在提交，出现过以下错误
                        // "message":"object is being deleted: pods \"p-vcvwzrrf\" already exists",
                        // "reason":"AlreadyExists"
                        if (k8sPod != null && k8sPod.getMetadata() != null
                                && k8sPod.getMetadata().getDeletionTimestamp() != null) {
                            LOGGER.error("PodMigrateService get pod {}:{} form informer exist , retry ",
                                    pod.getMetadata().getNamespace(),
                                    pod.getMetadata().getName());
                            Thread.sleep(2000);
                            continue;
                        }
                        k8sService.createPod(migratePod);
                        // 创建pod 需要的configmap
                        // 有定时任务定期删除configmap(pod 不存在时)，因此需要重建cm
                        List<ConfigFile> configFiles = getConfigFileFromOrder(migratePod);
                        createMigratePodConfigMap(migratePod, configFiles);
                        return;
                    } catch (Exception e) {
                        LOGGER.error("PodMigrateService create Migrate pod {}:{} err ",
                                pod.getMetadata().getNamespace(),
                                pod.getMetadata().getName(), e);
                    }
                }
            }
        });
    }

    private void createMigratePodConfigMap(V1Pod pod, List<ConfigFile> configFiles) {
        if (CollectionUtils.isEmpty(configFiles)) {
            return;
        }
        for (ConfigFile config : configFiles) {
            String configMapName = PodUtils.buildConfigMapName(pod.getMetadata().getName(), config.getName());
            // 封装k8sConfigMap
            V1ConfigMap v1ConfigMap = PodUtils.buildV1ConfigMap(pod.getMetadata().getNamespace(), configMapName,
                    config);
            try {
                // 创建configMap
                k8sService.createConfigMap(v1ConfigMap);
            } catch (Exception e) {
                LOGGER.error("PodMigrateService create Migrate pod {} configMap {} err {} ",
                        pod.getMetadata().getName(), configMapName, e);
            }
        }
    }

    private List<ConfigFile> getConfigFileFromOrder(V1Pod pod) {
        String podName = pod.getMetadata().getName();
        String orderId = null;
        if (MapUtils.isNotEmpty(pod.getMetadata().getAnnotations())) {
            orderId = pod.getMetadata().getAnnotations().get(LogicalConstant.LABEL_ORDER_ID);
        }
        List<ConfigFile> result = new ArrayList<>();
        if (orderId == null) {
            PodPO podPO = podDaoV2.getPodById(podName);
            if (podPO == null) {
                LOGGER.error("PodMigrateService pod {} query db null", podName);
                return result;
            }
            orderId = podPO.getOrderId();
        }

        OrderClient orderClient = logicPodClientFactory.createOrderClient();
        Order order = orderClient.get(orderId);

        BciOrderExtra orderExtra = null;
        for (Order.Item orderItem : order.getItems()) {
            if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) { // 去除eip的order item
                try {
                    orderExtra = PodUtils.getOrderExtra(orderItem.getExtra());
                } catch (IOException e) {
                }
                break;
            }
        }
        if (orderExtra == null || orderExtra.getVolume() == null) {
            return result;
        }
        return orderExtra.getVolume().getConfigFile();
    }

    /**
     * 重新提交node eni绑定失败的pod
     *
     * @param pod
     */
    private V1Pod buildMigrateEniPod(V1Pod pod) throws K8sServiceException {
        V1Pod migratePod = new V1Pod();
        V1ObjectMeta meta = new V1ObjectMeta();
        // Annotations
        Map<String, String> annotations = new HashMap<>();
        meta.setAnnotations(annotations);
        // 在上层校验是否为空
        for (Map.Entry<String, String> entity : pod.getMetadata().getAnnotations().entrySet()) {
            if (MIGRATE_ENI_POD_KEY.equals(entity.getKey())) {
                continue;
            }
            annotations.put(entity.getKey(), entity.getValue());
        }

        String migrateCount = annotations.get(MIGRATE_ENI_POD_RETRY_COUNT);
        if (migrateCount == null || "".equals(migrateCount)) {
            annotations.put(MIGRATE_ENI_POD_RETRY_COUNT, "1");
        } else {
            int retryCount = 1;
            try {
                retryCount = Integer.parseInt(migrateCount);
            } catch (NumberFormatException e) {
                LOGGER.error("PodMigrateService pod {} parse migrateCount {} err", pod.getMetadata().getName(),
                        migrateCount);
            }
            if (retryCount >= 3) {
                LOGGER.info("PodMigrateService pod {} has retry 3 times,ignore ", pod.getMetadata().getName());
                annotations.put(ENI_CREATE_FAILED, "retry eni attach failed");
            } else {
                retryCount++;
                annotations.put(MIGRATE_ENI_POD_RETRY_COUNT, retryCount + "");
            }

        }

        // labels
        Map<String, String> labels = new HashMap<>();
        if (MapUtils.isNotEmpty(pod.getMetadata().getLabels())) {
            labels.putAll(pod.getMetadata().getLabels());
        }
        meta.setLabels(labels);

        // name、namespace
        meta.setName(pod.getMetadata().getName());
        meta.setNamespace(pod.getMetadata().getNamespace());
        migratePod.setMetadata(meta);

        // spec
        V1PodSpec spec = new V1PodSpec();
        try {
            BeanUtils.copyProperties(pod.getSpec(), spec);
        } catch (BeansException e) {
            LOGGER.error("PodMigrateService copy pod {}:{} spec err {} ", pod.getMetadata().getNamespace(),
                    pod.getMetadata().getName(), e);
            throw new K8sServiceException(K8sServiceException.ErrorCode.MIGRATE_POD_FAILED, "copy pod spec err");
        }
        // 置空nodeName
        spec.setNodeName("");
        migratePod.setSpec(spec);

        return migratePod;
    }
}
