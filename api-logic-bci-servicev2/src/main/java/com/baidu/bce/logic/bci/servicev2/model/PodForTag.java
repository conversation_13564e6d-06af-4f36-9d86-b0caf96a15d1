package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PodForTag implements Cloneable {
    private String name = ""; // 资源标识名称
    private String resourceUuid = ""; // Tag绑定的资源场id
    private String resourceId = ""; // Tag绑定的资源短id
    private String region = ""; 
    private String status = ""; // 同billing状态
    private String productType = ""; // prepay or postpay
    private String createTime = "";
}
