package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Volume {

    private List<Nfs> nfs;
    private List<EmptyDir> emptyDir;
    private List<ConfigFile> configFile;
    private List<PodVolume> podVolumes;
    private List<FlexVolume> flexVolume;
    private List<Pfs> pfs;
    private List<Bos> bos;
    private List<HostPathVolume> hostPath;

    private List<CephFSVolume> cephfs;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PodVolume {
        private String name;
        private String type; // data, empty_dir，rootfs
        private VolumeSource volumeSource;
        private Integer sizeInGB;
        @JsonProperty(value = "fs")
        private FS fs;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class FS {
        @JsonProperty(value = "fs_type")
        private String fsType = "ext4";
        @JsonProperty(value = "mount_flags")
        private List<String> mountFlags;
        @JsonProperty(value = "force_format")
        private Boolean forceFormat = false;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VolumeSource {
        private CDS cds;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CDS {
        private String uuid = "";
        private String name;
        private String type; // 磁盘类型
    }

    @Data
    public static class Local {

    }

    public List<Nfs> getNfs() {
        if (nfs == null) {
            nfs = new ArrayList<>();
        }
        return nfs;
    }

    public void setNfs(List<Nfs> nfs) {
        this.nfs = nfs;
    }

    public List<EmptyDir> getEmptyDir() {
        if (emptyDir == null) {
            emptyDir = new ArrayList<>();
        }
        return emptyDir;
    }

    public void setEmptyDir(List<EmptyDir> emptyDir) {
        this.emptyDir = emptyDir;
    }

    public List<ConfigFile> getConfigFile() {
        if (configFile == null) {
            configFile = new ArrayList<>();
        }
        return configFile;
    }

    public void setConfigFile(List<ConfigFile> configFile) {
        this.configFile = configFile;
    }

    public List<FlexVolume> getFlexVolume() {
        if (flexVolume == null) {
            flexVolume = new ArrayList<>();
        }
        return flexVolume;
    }

    public void setFlexVolume(List<FlexVolume> flexVolume) {
        this.flexVolume = flexVolume;
    }

    public List<Pfs> getPfs() {
        if (pfs == null) {
            pfs = new ArrayList<>();
        }
        return pfs;
    }

    public void setPfs(List<Pfs> pfs) { 
        this.pfs = pfs;
    }

    public List<Bos> getBos() {
        if (bos == null) {
            bos = new ArrayList<>();
        }
        return bos;
    }

    public List<CephFSVolume> getCephfs() {
        if (cephfs == null) {
            cephfs = new ArrayList<>();
        }
        return cephfs;
    }

    public void setCeph(List<CephFSVolume> cephFSVolume) {
        this.cephfs = cephFSVolume;
    }

    public void setBos(List<Bos> bos) {
        this.bos = bos;
    }

    public List<HostPathVolume> getHostPath() {
        if (hostPath == null) {
            hostPath = new ArrayList<>();
        }
        return hostPath;
    }

    public void setHostPath(List<HostPathVolume> hostPath) { 
        this.hostPath = hostPath;
    }
}
