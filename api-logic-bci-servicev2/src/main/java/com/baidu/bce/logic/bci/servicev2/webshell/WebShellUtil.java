package com.baidu.bce.logic.bci.servicev2.webshell;

import com.google.gson.reflect.TypeToken;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.openapi.models.V1StatusCause;
import io.kubernetes.client.openapi.models.V1StatusDetails;
import io.kubernetes.client.util.Streams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;
import java.util.List;

import static io.kubernetes.client.KubernetesConstants.V1STATUS_CAUSE_REASON_EXITCODE;
import static io.kubernetes.client.KubernetesConstants.V1STATUS_REASON_NONZEROEXITCODE;
import static io.kubernetes.client.KubernetesConstants.V1STATUS_SUCCESS;

public class WebShellUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(WebShellUtil.class);

    public static int parseExitCode(ApiClient client, InputStream inputStream) {
        try {
            Type returnType = new TypeToken<V1Status>() {
            }.getType();
            String body;
            try (final Reader reader = new InputStreamReader(inputStream)) {
                body = Streams.toString(reader);
            }

            V1Status status = client.getJSON().deserialize(body, returnType);
            if (status == null) {
                return -1;
            }
            if (V1STATUS_SUCCESS.equals(status.getStatus())) {
                return 0;
            }

            if (V1STATUS_REASON_NONZEROEXITCODE.equals(status.getReason())) {
                V1StatusDetails details = status.getDetails();
                if (details != null) {
                    List<V1StatusCause> causes = details.getCauses();
                    if (causes != null) {
                        for (V1StatusCause cause : causes) {
                            if (V1STATUS_CAUSE_REASON_EXITCODE.equals(cause.getReason())) {
                                try {
                                    return Integer.parseInt(cause.getMessage());
                                } catch (NumberFormatException nfe) {
                                    LOGGER.error("WebShellUtil Error parsing exit code from status channel response " +
                                            "{}", nfe);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Throwable t) {
            System.out.println(t);
        }

        // Unable to parse the exit code from the content
        return -1;
    }
}
