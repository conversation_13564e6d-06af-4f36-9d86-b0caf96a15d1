package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class SubnetModel {
    private String subnetId;
    private String name;
    private String cidr;
    private String vpcId;
    private String subnetType;
    private String description;
    private String createTime;

    public SubnetModel(SubnetVo subnet) {
        this.subnetId = subnet.getShortId();
        this.name = subnet.getName();
        this.cidr = subnet.getCidr();
        this.vpcId = subnet.getVpcShortId();
        this.subnetType = SubnetVo.SubnetType.findById(subnet.getSubnetType()).getName();
        this.description = subnet.getDescription();
        this.createTime = subnet.getCreatedTime().toString();
    }
}