package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
import java.util.ArrayList;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceIDGetter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BCMPodDetailRequest implements AuthorizationResourceIDGetter {
    private String id;
    private List<String> ids;

    @Override
    public List<String> getPodIDs() {
        List<String> result = new ArrayList<String>();
        result.add(id);
        return result;
    }
}
