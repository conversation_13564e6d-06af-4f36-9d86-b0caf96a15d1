package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class VpcModel {
    private String vpcId;
    private String name;
    private String cidr;
    private String createTime;
    private String description;
    private Boolean isDefault;

    public VpcModel(VpcVo vpu) {
        this.vpcId = vpu.getShortId();
        this.name = vpu.getName();
        this.cidr = vpu.getCidr();
        this.createTime = vpu.getCreateTime().toString();
        this.description = vpu.getDescription();
        this.isDefault = vpu.isDefaultVpc();
    }
}