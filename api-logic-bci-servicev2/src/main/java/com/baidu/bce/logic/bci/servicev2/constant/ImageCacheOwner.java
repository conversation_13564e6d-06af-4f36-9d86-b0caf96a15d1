package com.baidu.bce.logic.bci.servicev2.constant;

public enum ImageCacheOwner {
    USER("USER", "用户手动创建"),
    BCI("BCI", "BCI自动创建");

    private String name;
    private String description;

    ImageCacheOwner(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public static String getOwnerByName(String name) {
        for (ImageCacheOwner imageCacheOwner : values()) {
            if (imageCacheOwner.getName().equalsIgnoreCase(name)) {
                return imageCacheOwner.getDescription();
            }
        }
        return name;
    }
}