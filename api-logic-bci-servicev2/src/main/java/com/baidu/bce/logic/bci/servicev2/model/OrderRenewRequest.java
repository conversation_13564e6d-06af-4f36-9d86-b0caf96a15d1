package com.baidu.bce.logic.bci.servicev2.model;

import java.util.List;
import java.util.Set;

import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderRenewRequest {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OrderRenewRequestItemConfig {
        // 续费时长
        private int duration;
        // 实例ID, billing resource name
        private String instanceId;
        // 产品名称
        private String serviceType;
        // 续费时长单位，默认值 month, 可取值 year, month, day
        private String timeUnit = "month";
        // 是否为统一到期日订单，默认（不是）不传，是传 1
        private int unionExpireOrderFlag = 1;
        // billing resource uuid
        private String uuid;
        // 代金券等配置
        private Set<PaymentModel> paymentMethod;
    }
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class OrderRenewRequestItem {
        private OrderRenewRequestItemConfig config;
        private Set<PaymentModel> paymentMethod;
    }

    private List<OrderRenewRequestItem> items;
    private Set<PaymentModel> paymentMethod;
}
