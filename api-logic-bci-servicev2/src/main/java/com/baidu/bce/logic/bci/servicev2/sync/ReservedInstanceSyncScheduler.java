package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.ReservedInstanceSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("ReservedInstanceSyncScheduler")
@Profile("default")
public class ReservedInstanceSyncScheduler {

    private static final int FIX_DELAY_SYNC_IN_BUILD = 3000;
    private static final int FIX_DELAY_SYNC_CREATED = 60000;

    private String syncReservedInstanceInBuildSchedulerName =
            "ReservedInstanceSyncScheduler.syncReservedInstanceInBuild";
    private String syncCreatedReservedInstanceSchedulerName =
            "ReservedInstanceSyncScheduler.syncCreatedReservedInstance";

    @Autowired
    private ReservedInstanceSyncService reservedInstanceSyncService;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(syncReservedInstanceInBuildSchedulerName);
        schedulerStatistics.registerScheduler(syncCreatedReservedInstanceSchedulerName);
    }

    @Scheduled(fixedDelay = FIX_DELAY_SYNC_IN_BUILD)
    public void syncReservedInstanceInBuild() {
        schedulerStatistics.beforeSchedulerRun(syncReservedInstanceInBuildSchedulerName);
        reservedInstanceSyncService.syncReservedInstanceInBuild();
        schedulerStatistics.afterSchedulerRun(syncReservedInstanceInBuildSchedulerName);
    }

    @Scheduled(fixedDelay = FIX_DELAY_SYNC_CREATED)
    public void syncCreatedReservedInstance() {
        schedulerStatistics.beforeSchedulerRun(syncCreatedReservedInstanceSchedulerName);
        reservedInstanceSyncService.syncCreatedReservedInstance();
        schedulerStatistics.afterSchedulerRun(syncCreatedReservedInstanceSchedulerName);
    }
}
