package com.baidu.bce.logic.bci.servicev2.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class PodUpdateEventSyncThreadPoolConfigurationV2 {
    @Value("${pod.update.event.sync.thread.pool.core.size:200}")
    private Integer threadPoolCoreSize;
    @Value("${pod.update.event.sync.thread.pool.max.size:1000}")
    private Integer threadPoolMaxSize;
    @Value("${pod.update.event.sync.thread.pool.queue.capacity:500000}")
    private Integer threadPoolQueueCapacity;
    @Value("${pod.update.event.sync.thread.keepalive.seconds:60}")
    private Integer threadKeepAliveSeconds;

    @Bean(name = "podUpdateEventSyncThreadPoolTaskExecutorV2")
    public ThreadPoolTaskExecutor podUpdateEventSyncThreadPoolTaskExecutorV2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(threadPoolQueueCapacity);
        executor.setKeepAliveSeconds(threadKeepAliveSeconds);

        String threadNamePrefix = "BciV2-Pod-Update-Event-Sync-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);
        return executor;
    }
}
