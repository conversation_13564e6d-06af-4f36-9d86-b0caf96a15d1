package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.model.QueryEipListResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEipResponse;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPOWithId;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EipInfoSyncServiceV2 extends SyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(EipInfoSyncServiceV2.class);

    // sync update eip info, that already in use
    public void syncEipInfo() {
        // 1. get bci user list
        List<CceUserMap> bciUsers = cceUserDao.getActiveUsers();
        for (CceUserMap user : bciUsers) {
            try {
                if (StringUtils.isEmpty(user.getUserId())) {
                    continue;
                }
                // 1. 获取 userId 的pod. 如果 userId 为子用户, 这里获取到的 pod 为空
                List<PodPOWithId> podPOSActive = podDao.getActiveEipByUser(user.getUserId());
                if (podPOSActive.isEmpty()) {
                    continue;
                }

                // 2. get all eip that binded eni
                LogicEipClient eipClient = logicPodClientFactory.createLogicEipClient(user.getUserId());
                QueryEipListResponse eipListResponse = eipClient.queryEipBindedV2("ENI");
                if (eipListResponse == null) {
                    LOGGER.error("EipInfoSyncServiceV2: query eip failed, account {}", user.getUserId());
                    continue;
                }
                Map<String, QueryEipResponse> eip2EipInfo = new HashMap<>();
                for (QueryEipResponse eipResponse : eipListResponse.getEipList()) {
                    eip2EipInfo.put(eipResponse.getEip(), eipResponse);
                }

                // 3. check eip info
                List<PodPO> updateBindedEIP = new ArrayList<>();
                List<PodPO> updateUnbindedEIP = new ArrayList<>();
                List<PodPO> updateNewEIP = new ArrayList<>();
                
                for (PodPO podPO : podPOSActive) {
                    // eip status: not binded
                    String eipIP = podPO.getPublicIp();
                    if (!eip2EipInfo.containsKey(eipIP)) {
                        if (podPO.getEipActualStatus().equals("binded")) {
                            updateUnbindedEIP.add(podPO);
                        }
                        continue;
                    }

                    // write new eip info
                    if (StringUtils.isEmpty(podPO.getEipId())) {
                        podPO.setEipId(eip2EipInfo.get(eipIP).getEipId());
                        podPO.setEipRouteType(eip2EipInfo.get(eipIP).getRouteType());
                        podPO.setEipPayMethod(eip2EipInfo.get(eipIP).getBillingMethod());
                        podPO.setBandwidthInMbps(eip2EipInfo.get(eipIP).getBandwidthInMbps());
                        // check eip bind relationship, whether binded with this pod
                        if (!StringUtils.isEmpty(eip2EipInfo.get(eipIP).getInstanceIp()) && 
                        eip2EipInfo.get(eipIP).getInstanceIp().equals(podPO.getInternalIp())) {    
                            podPO.setEipActualStatus(eip2EipInfo.get(eipIP).getStatus());
                        } else {
                            podPO.setEipActualStatus("");
                        }
                        updateNewEIP.add(podPO);
                        continue;
                    }

                    // update eip info: bandwidth, iaas_status
                    if (whetherUpdateEipInfo(podPO, eip2EipInfo.get(eipIP))) {
                        updateBindedEIP.add(podPO);
                    }
                }

                // 4. sync eip into DB
                podDao.updateUnbindedEipsInfo(updateUnbindedEIP, user.getUserId());
                podDao.updateNewEipsInfo(updateNewEIP, user.getUserId());
                podDao.updateEipsInfo(updateBindedEIP, user.getUserId());

                // 减轻 mysql 的压力
                Thread.sleep(50);
            } catch (Exception e) {
                LOGGER.error("EipInfoSyncServiceV2 userId:{} Exception: {}", user.getUserId(), e);
            }
        } 
    }

    private boolean whetherUpdateEipInfo(PodPO podPO, QueryEipResponse eipInfo) {
        // eip is bound with this pod
        if (!StringUtils.isEmpty(eipInfo.getInstanceIp()) && 
        eipInfo.getInstanceIp().equals(podPO.getInternalIp())) {
            if (podPO.getBandwidthInMbps() != eipInfo.getBandwidthInMbps() || 
            !podPO.getEipActualStatus().equals("binded")) {
                podPO.setBandwidthInMbps(eipInfo.getBandwidthInMbps());
                podPO.setEipActualStatus("binded");
                return true;
            }
        } else {
            if (podPO.getBandwidthInMbps() != eipInfo.getBandwidthInMbps() || 
            !podPO.getEipActualStatus().equals("")) {
                podPO.setBandwidthInMbps(eipInfo.getBandwidthInMbps());
                podPO.setEipActualStatus("");
                return true;
            }
        }
        return false;
    }

}
