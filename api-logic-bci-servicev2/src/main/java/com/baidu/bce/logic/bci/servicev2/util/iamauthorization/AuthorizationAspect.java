package com.baidu.bce.logic.bci.servicev2.util.iamauthorization;

import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.logic.bcc.sdk.model.common.IDListRequest;
import com.baidu.bce.logic.bci.daov2.common.model.WebShell;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.model.BCIBatchPermissionRequest;
import com.baidu.bce.logic.bci.servicev2.util.PrometheusMetricsService;
import com.baidu.bce.logic.bci.servicev2.util.metricsservice.ResponseWrapperFilter;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Aspect
@Component("AuthorizationAspect")
@Order(Integer.MAX_VALUE - 100)
public class AuthorizationAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthorizationAspect.class);

    @Autowired
    private IamLogicService iamLogicService;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    private PrometheusMetricsService prometheusMetricsService;

    @Value("${bce.logical.iamauthorization.enabled:false}")
    private boolean enable;

    private IAMClient iamClient;

    /**
     * 定义需要鉴权的函数，com.baidu.bce包下面的所有类中，有@RequestMapping注解，且有@Authorization注解的方法。
     */
    @Pointcut("execution(* com.baidu.bce..*(..)) "
            + "&& @annotation(org.springframework.web.bind.annotation.RequestMapping) "
            + "&& @annotation(com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization) ")
    public void authorizationPointcut() {
    }

    /**
     * 对所有需要鉴权的方法，解析鉴权上下文，触发鉴权；如果通过，调用原方法，如果不通过，抛出鉴权失败的异常。
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    @Around("authorizationPointcut()")
    public Object authorizationInterceptor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String userAccountID = LogicUserService.getAccountId();
        // 获取 HttpServletRequest
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String requestMethod = request.getMethod();
        String requestUri = request.getRequestURI();
        String requestId = request.getHeader("x-bce-request-id");
        int responseStatusCode = HttpServletResponse.SC_OK; // 默认状态码
        // 获取类名和方法名称
        String fullClassName = proceedingJoinPoint.getTarget().getClass().getName();
        String simpleClassName = proceedingJoinPoint.getTarget().getClass().getSimpleName();
        String methodName = proceedingJoinPoint.getSignature().getName();
        String authorizationResult = "failed";
        try {
            if (!enable) {
                return proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
            }
            // STEP1. 获取鉴权所需的上下文
            // 获取临时权限Token（一般用在临时授权带有ACL限制的场景）
            // header 不存在，返回 null
            String securityToken = request.getHeader("x-bce-security-token");
            LOGGER.debug("requestUri:{} requestMethod:{} simpleClassName:{} methodName:{} got securityToken {}",
                    requestUri, requestMethod, simpleClassName, methodName, securityToken);

            // 获取 services、permissions、resourceids、resourceOwner
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            Method method = methodSignature.getMethod();
            Authorization authorizationAnnotation = method.getAnnotation(Authorization.class);
            if (authorizationAnnotation == null) {
                throw new Exception(String.format(
                    "method %s is invalid, there is no Authorization annotation decorating it", method.getName()));
            }
            String service = authorizationAnnotation.service();
            String[] permissions = authorizationAnnotation.permissions();
            Set<String> resourceIDs = new HashSet<>();
            String resourceOwner = null;
            if (Authorization.ResourceLocation.NO_RESOURCE_ID == authorizationAnnotation.resourceLocation()) {
                resourceIDs.add(AuthorizationConstant.DEFAULT_CREATE_RESOURCE);
                resourceOwner = AuthorizationConstant.RESOURCE_OWNER_FOR_CREATING;
            } else {
                resourceIDs.addAll(getResourceIDs(proceedingJoinPoint, authorizationAnnotation));
                // 对于非create/list场景，resource id是必需的，如果不指定，直接鉴权失败
                if (CollectionUtils.isEmpty(resourceIDs)) {
                    throw new AuthorizationException("no pod is specified, authorization fail", 
                        AuthorizationException.AuthorizationErrorCode.BCI_ACCESS_NO_SPECIFIED_RESOURCE);
                }

                LOGGER.debug("in authorizationInterceptor, resourceIDs {}, userAccountID {}", resourceIDs,
                        userAccountID);

                if (Authorization.ResourceLocation.IGNORE_UNAVAILABLE_RESOURCE_FROM_ID_LIST == authorizationAnnotation.resourceLocation()) {
                    if (!resourcesBelongToThisAccountIgnoreUnavailableResources(resourceIDs, userAccountID)) {
                        throw new AuthorizationException(
                                "the request resource dose not exist, authorization fail",
                                AuthorizationException.AuthorizationErrorCode.BCI_ACCESS_NON_OWNED_RESOURCE);
                    }
                } else {
                    if (!resourcesBelongToThisAccount(resourceIDs, userAccountID) && !("podDetailWithDeleted".equals(methodName))) {
                        // podDetails接口，判断resource是否存在，如果已经缩容，鉴权失败，抛异常 BCI_ACCESS_NO_SPECIFIED_RESOURCE
                        // 单独对podDetails接口做特殊处理，是要把 BCI_ZONE_NO_RESOURCE_SPECIFICATION 异常从 BCI_ACCESS_NON_OWNED_RESOURCE 区分开来
                        if ("BciControllerV2".equals(simpleClassName) && "podDetail".equals(methodName)) {
                            for(String resourceId : resourceIDs) {
                                if (podZoneNoResourceSpecification(userAccountID, resourceId)) {
                                    LOGGER.debug("podDetail resourceId {}, no resource supply for pod in zone.", resourceId);
                                    throw new AuthorizationException("there is no resource supply for pod in zone.",
                                            AuthorizationException.AuthorizationErrorCode.BCI_ZONE_NO_RESOURCE_SPECIFICATION);
                                }
                            }
                        }
                        throw new AuthorizationException(
                                "there are some pods that are not belonged to you, authorization fail",
                                AuthorizationException.AuthorizationErrorCode.BCI_ACCESS_NON_OWNED_RESOURCE);
                    }
                }
                // 上文逻辑已经明确要处理的所有资源都属于当前用户所在的账户，故资源owner就是用户所在的账户。
                // TODO：注意，这种判断方式不支持跨账户资源访问。由于跨账户资源访问需要对接口内的逻辑整体梳理改造，加之用户暂无此冷门需求，故暂不支持。
                //       待以后BCI支持跨账户资源访问，此处需要相应调整。
                resourceOwner = userAccountID;
            }
            LOGGER.debug("got service {}, permissions {}, resourceids {}, resourceOwner {} simpleClassName {} " +
                            "methodName {}",
                         service, permissions, resourceIDs, resourceOwner, simpleClassName, methodName);

            // STEP2. 开始鉴权
            boolean authorizationAllow = takeAuthorization(resourceIDs, resourceOwner, service, permissions, 
                                                           securityToken);
            if (!authorizationAllow) {
                throw new AuthorizationException("iam permission deny, authorization fail", 
                        AuthorizationException.AuthorizationErrorCode.IAM_PERMISSION_DENY);
            }
            authorizationResult = "successfully";
            LOGGER.debug("authorization {} requestUri:{} requestMethod:{} simpleClassName:{} methodName:{}",
                    authorizationResult, requestUri, requestMethod, simpleClassName, methodName);
            // STEP3. 正常执行方法
            return proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
        } catch (BceException e) {
            responseStatusCode = e.getHttpStatus();
            LOGGER.debug("authorization {} and call proceed with BceException, "
                            + "requestUri:{} requestMethod:{} simpleClassName:{} methodName:{} "
                            + "requestId {} responseStatusCode {} error msg is {}",
                    authorizationResult,
                    requestUri, requestMethod, simpleClassName, methodName,
                    requestId, responseStatusCode, e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.debug("authorization {} and call proceed with Exception, "
                            + "requestUri:{} requestMethod:{} simpleClassName:{} methodName:{} "
                            + "requestId {} error msg is {}",
                    authorizationResult,
                    requestUri, requestMethod, simpleClassName, methodName,
                    requestId, e.getMessage());
            // 获取 HttpServletResponse
            HttpServletResponse response = ResponseWrapperFilter.getCurrentResponse();
            // 获取实际的状态码并记录
            if (response != null) {
                responseStatusCode = response.getStatus();
                if (responseStatusCode == HttpServletResponse.SC_OK) {
                    responseStatusCode = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
                }
            }
            throw e;
        } finally {
            // 计算延迟并记录
            double requestLatency = (System.currentTimeMillis() - startTime) / 1000.0;
            prometheusMetricsService.requestInfoRecord(requestId, userAccountID,
                    requestUri, requestMethod,
                    simpleClassName, methodName,
                    requestLatency, responseStatusCode);
        }
    }

    /**
     * 获取本次请求的资源ID
     *
     * @param proceedingJoinPoint
     * @param authorizationAnn
     * @return 本次请求的资源ID列表
     */
    private Set<String> getResourceIDs(ProceedingJoinPoint proceedingJoinPoint, Authorization authorizationAnn) {
            Set<String> resourceIds = new HashSet<>();
            // STEP1. 拿到所有用 AuthorizationResourceID 修饰的参数
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            Method method = methodSignature.getMethod();
            Object[] args = proceedingJoinPoint.getArgs();                           // 获取所有参数
            Annotation[][] parameterAnnotations = method.getParameterAnnotations();  // 获取所有参数的注解，与args顺序对应
            boolean resourceIDHasFound = false;
            int foundArgIndex = 0;
            for (int argIndex = 0; argIndex < args.length; argIndex++) { 
                if (resourceIDHasFound) {
                    break;
                }
                for (Annotation annotation : parameterAnnotations[argIndex]) {
                    if (annotation instanceof AuthorizationResourceID) {
                        resourceIDHasFound = true;
                        foundArgIndex = argIndex;
                        break;
                    }
                }
            }
            if (!resourceIDHasFound) {
                return resourceIds;
            }

            // STEP2. 从参数中解析出 resource ids
            Object arg = args[foundArgIndex];
            if (Authorization.ResourceLocation.IN_STRING == authorizationAnn.resourceLocation()) {
                resourceIds.add(arg.toString());
            }
            if (Authorization.ResourceLocation.IN_ID_LIST == authorizationAnn.resourceLocation()) {
                resourceIds.addAll(getResourceIDsFromIDList(arg));
            }
            if (Authorization.ResourceLocation.IGNORE_UNAVAILABLE_RESOURCE_FROM_ID_LIST == authorizationAnn.resourceLocation()) {
                resourceIds.addAll(getResourceIDsFromIDList(arg));
            }
            if (Authorization.ResourceLocation.IN_WEBSHELL_REQ == authorizationAnn.resourceLocation()) {
                resourceIds.addAll(getResourceIDsFromWebShellReq(arg));
            }
            if (Authorization.ResourceLocation.IN_CLASS_GETTER == authorizationAnn.resourceLocation()) {
                resourceIds.addAll(getResourceIDsFromCustomClass(arg));
            }
            return resourceIds;
    }

    private Set<String> getResourceIDsFromIDList(Object arg) {
        Set<String> idStrings = new HashSet<>();
        if (arg instanceof IDListRequest) {
            IDListRequest req = (IDListRequest) arg;
            idStrings.addAll(req.getIds());
        }
        return idStrings;
    }

    private Set<String> getResourceIDsFromWebShellReq(Object arg) {
        Set<String> idStrings = new HashSet<>();
        if (arg instanceof WebShell) {
            WebShell req = (WebShell) arg;
            idStrings.add(req.getPodId());
        }
        return idStrings;
    }

    private Set<String> getResourceIDsFromCustomClass(Object arg) {
        Set<String> idStrings = new HashSet<>();
        if (arg instanceof AuthorizationResourceIDGetter) {
            AuthorizationResourceIDGetter resourceIDGetter = (AuthorizationResourceIDGetter) arg;
            idStrings.addAll(resourceIDGetter.getPodIDs());
        }
        return idStrings;
    }

    /**
     * 判断云资源是否全部属于当前账户
     *
     * @param resourceIDSet
     * @param userAccountID
     * @return boolean
     */
    private boolean resourcesBelongToThisAccount(Set<String> resourceIDSet, String userAccountID) {
        List<String> resourceIDs = new ArrayList<String>();
        resourceIDs.addAll(resourceIDSet);
        List<PodPO> pods = podDao.listPodPOByIds(resourceIDs, userAccountID);
        return pods.size() == resourceIDs.size();
    }

    private boolean resourcesBelongToThisAccountIgnoreUnavailableResources(Set<String> resourceIDSet,
                                                                           String userAccountID) {
        List<String> resourceIDs = new ArrayList<String>();
        resourceIDs.addAll(resourceIDSet);
        List<PodPO> pods = podDao.listPodPOByIds(resourceIDs, userAccountID);
        return pods.size() <= resourceIDs.size();
    }

    private boolean podZoneNoResourceSpecification(String userAccountID, String resourceID) {
        PodPO pod = podDao.getPodDetailForAllStatus(userAccountID, resourceID);
        return pod != null && ResourceRecycleReason.ZONE_NO_RESOURCE_SPECIFICATION.name().equals(pod.getResourceRecycleReason());
    }

    /**
     * 去IAM进行鉴权
     *
     * @param resourceIDs
     * @param resourceOwner
     * @param service
     * @param permissions
     * @param securityToken
     * @return 通过返回true，异常或拒绝返回false
     */
    private boolean takeAuthorization(Set<String> resourceIDs, String resourceOwner, String service, 
                                      String[] permissions, String securityToken) throws UnsupportedEncodingException {
        // STEP1. 准备IAM鉴权所需的上下文信息
        String region = regionConfiguration.getCurrentRegion();
        if (StringUtils.isBlank(region)) {
            region = "bj";
        }
        String userId = LogicUserService.getUserId();
        List<String> resourcesWithPrefix = new ArrayList<String>();
        for (String resourceID : resourceIDs) {
            if (StringUtils.equals(resourceID, AuthorizationConstant.DEFAULT_CREATE_RESOURCE)) {
                resourcesWithPrefix.add(resourceID);
            } else {
                resourcesWithPrefix.add(AuthorizationConstant.TYPE_PREFIX_POD + resourceID);
            }
        }
        List<String> permissionList = Arrays.asList(permissions);

        // STEP2. 准备 IAM client
        IAMClient iamClient = getIamClient(region);
        String authToken = iamLogicService.getConsoleToken().getId();
        iamClient.setxAuthToken(authToken);

        // STEP3. 生成批量鉴权请求
        BCIBatchPermissionRequest batchPermissionRequest = new BCIBatchPermissionRequest();
        List<BatchPermissionRequest.Request> verifyList = new ArrayList<>();
        BatchPermissionRequest.Request verifyItem = new BatchPermissionRequest.Request();
        verifyItem.setPermission(permissionList);
        verifyItem.setRegion(region);
        verifyItem.setResourceOwner(resourceOwner);
        verifyItem.setResource(resourcesWithPrefix);
        verifyItem.setService(service);
        verifyList.add(verifyItem);
        batchPermissionRequest.setVerifyList(verifyList);
        if (securityToken != null) {
            batchPermissionRequest.setSecurityToken(securityToken);
        }

        // STEP4. 发起鉴权请求
        BatchVerifyResults verifyResults = iamClient.batchVerify(userId, batchPermissionRequest);
        if (verifyResults == null) {
            LOGGER.debug("vertify results is null");
            return false;
        }
        List<BatchVerifyResults.Result> results = verifyResults.getVerifyResults();
        if (CollectionUtils.isEmpty(results) || results.size() != 1) {
            LOGGER.debug("size of vertify results must be 1");
            return false;
        }
        BatchVerifyResults.Result result = results.get(0);
        if (result == null || CollectionUtils.isEmpty(result.getResult())) {
            LOGGER.debug("size of sub vertify results is 0");
            return false;
        }
        for (VerifyResult subResult : result.getResult()) {
            if (subResult == null) {
                return false;
            }
            if (!AuthorizationConstant.ALLOW_PERMISSION.equals(subResult.getEffect())) {
                LOGGER.debug("permission not allow. result = {}", subResult);
                return false;
            }
        }
        LOGGER.debug("vertify success!");
        return true;
    }

    private IAMClient getIamClient(String region) {
        if (iamClient != null) {
            return iamClient;
        }
        String endpoint = EndpointManager.getInstance().getRegion(region).getEndpoint(IAMClient.SERVICE_NAME);
        iamClient = new IAMClient(endpoint);
        iamClient.setSubuserEnabled(true);
        return iamClient;
    }

    // only used in ut
    public void setIamClient(IAMClient iamClient) {
        this.iamClient = iamClient;
    }

    // only used in ut
    public void setIamLogicService(IamLogicService iamLogicService) {
        this.iamLogicService = iamLogicService;
    }

    // only used in ut
    public void setEnable(boolean enable) {
        this.enable = enable;
    }
}