package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class VolumeMount {
    // 数据卷名称。
    private String name;
    // 数据卷类型
    private String type;
    // 容器挂载数据卷的目录
    private String mountPath;
    // 数据卷是否只读。默认为false。
    private Boolean readOnly = false;
    // 数据卷子路径
    private String subPath;
    // 数据卷子路径
    private String subPathExpr;

    /**
     * 数据卷的挂载传播设置。挂载传播允许将Container挂载的卷共享到同一Pod中的其他Container，甚至可以共享到同一节点上的其他Pod。取值范围：
     * None：该卷不感知任何后续在此卷或其子目录上执行的挂载操作。
     * HostToCotainer：该卷将会感知到后续在此卷或其子目录上的挂载操作。
     * Bidirectional：和HostToCotainer类似，能感知挂载操作。另外，该卷将被传播回主机和使用同一卷的所有Pod的所有容器。
     * 默认值：None
     */
    // private String mountPropagation = "None";
    public VolumeMount() {

    }

    public VolumeMount(VolumeMounts volumMount) {
        this.name = volumMount.getName();
        this.type = volumMount.getType();
        this.mountPath = volumMount.getMountPath();
        this.readOnly = volumMount.getReadOnly();
        this.subPath = volumMount.getSubPath();
        this.subPathExpr = volumMount.getSubPathExpr();
    }
}
