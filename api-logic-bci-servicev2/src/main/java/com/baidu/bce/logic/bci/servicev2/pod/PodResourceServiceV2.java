package com.baidu.bce.logic.bci.servicev2.pod;

//@Service("PodResourceServiceV2")
//public class PodResourceServiceV2 extends BaseServiceV2 implements ResourceService {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(PodResourceServiceV2.class);
//
//    @Autowired
//    private RegionConfiguration regionConfiguration;
//
//    @Autowired
//    private LogicPodClientFactoryV2 logicPodClientFactory;
//
//    @Override
//    public ResourceDetail get(String accountId, String serviceType, String resourceId, String region) {
//
//        validateRequest(resourceId, serviceType, region);
//        ResourceDetail detail = packResource(resourceId, "",
//                ResourceStatus.RUNNING, serviceType, accountId, region);
//
//        PodPO podPO = getByPodId(resourceId);
//        if (podPO == null) {
//            detail.setStatus(ResourceStatus.DESTROYED);
//            return detail;
//        }
//
//        detail.setName(podPO.getName());
//        String status = podPO.getStatus();
//
//        if (BciStatus.DELETED.getStatus().equalsIgnoreCase(status)) {
//            detail.setStatus(ResourceStatus.DESTROYED);
//            return detail;
//        }
//
//        return detail;
//    }
//
//    @Override
//    public StartResult start(ResourceInfo resourceInfo) {
//        return StartResult.STARTED;
//    }
//
//    @Override
//    public StopResult stop(ResourceInfo resourceInfo) {
//        return StopResult.STOPPED;
//    }
//
//    @Override
//    public DeleteResult delete(ResourceInfo resourceInfo) {
//        String resourceId = resourceInfo.getResourceId();
//        String serviceType = resourceInfo.getService();
//        String region = resourceInfo.getRegion();
//        String accountId = resourceInfo.getAccountId();
//
//        validateRequest(resourceId, serviceType, region);
//
//        PodPO podPO = getByPodId(resourceId);
//        if (podPO == null) {
//            return DeleteResult.DELETED;
//        }
//
//        PodClient podClient = logicPodClientFactory.createPodClientByAccountId(accountId);
//        try {
//            podClient.deletePod(podPO.getPodUuid());
//            simplyDeletePod(podPO);
//        } catch (Exception e) {
//            LOGGER.error("nova delete failed, exception is {}", e);
//            return DeleteResult.DELETING;
//        }
//
//        return DeleteResult.DELETED;
//    }
//
//    /**
//     * 校验请求
//     *
//     * @param resourceId
//     * @param serviceType
//     * @param region
//     */
//    private void validateRequest(String resourceId, String serviceType, String region) {
//
//        if (!PodConstants.SERVICE_TYPE.equalsIgnoreCase(serviceType)
//                && !regionConfiguration.getCurrentRegion().equals(region)) {
//            LOGGER.error("Error request,resourceId:{},serviceType:{},region:{}", resourceId, serviceType, region);
//            throw new PodExceptions.PermissionDenyException();
//        }
//
//        Token token = LogicUserService.getSubjectToken();
//        String userDomainId = token.getUser().getDomain().getId();
//        String userName = token.getUser().getName();
//
//        LOGGER.info("operate pod with token Domain Id:{}, User Name:{}",
//                userDomainId, userName);
//        if (!LogicalConstant.DEFAULT.equals(userDomainId)
//                || !LogicalConstant.BILLING_SERVICE_NAME.equals(userName)) {
//            throw new PodExceptions.PermissionDenyException();
//        }
//    }
//
//    /**
//     * 组装ResourceDetail对象
//     *
//     * @param podId
//     * @param name
//     * @param status
//     * @param serviceType
//     * @param accountId
//     * @param region
//     * @return
//     */
//    private ResourceDetail packResource(String podId, String name, ResourceStatus status,
//                                        String serviceType, String accountId, String region) {
//
//        ResourceDetail detail = new ResourceDetail();
//        detail.setId(podId);
//        detail.setName(name);
//        detail.setStatus(status);
//        detail.setService(serviceType);
//        detail.setAccountId(accountId);
//        detail.setRegion(region);
//
//        return detail;
//    }
//}
