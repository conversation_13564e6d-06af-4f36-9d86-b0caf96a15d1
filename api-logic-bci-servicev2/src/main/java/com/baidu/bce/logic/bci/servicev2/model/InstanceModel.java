package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class InstanceModel implements Cloneable {
    private String instanceId = "";
    private String instanceName = "";
    private String status = "";

    private String zoneName = "";
    private String cpuType = "";
    private String gpuType = "";
    private Integer bandwidthInMbps;

    private String publicIp = "";
    private String internalIp = "";

    private float cpu = 0f;
    private float memory = 0f;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updateTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deleteTime;

    private String restartPolicy = "";
    private List<Tag> tags;

    public InstanceModel() {
    }

    public InstanceModel(PodPO podPO) {
        this.setInstanceId(podPO.getPodId());
        this.setInstanceName(podPO.getName());
        this.setStatus(podPO.getStatus());

        this.setZoneName(podPO.getLogicalZone());
        this.setCpuType(podPO.getCpuType());
        this.setGpuType(podPO.getGpuType());
        this.setBandwidthInMbps(podPO.getBandwidthInMbps());

        this.setInternalIp(podPO.getInternalIp());
        this.setPublicIp(podPO.getPublicIp());
        
        this.setCreateTime(podPO.getCreatedTime());
        this.setUpdateTime(podPO.getUpdatedTime());
        this.setDeleteTime(podPO.getDeletedTime());

        this.setRestartPolicy(podPO.getRestartPolicy());
        this.setTags(podPO.getPodTags());
        this.setCpu(podPO.getvCpu());
        this.setMemory(podPO.getMemory());
    }

    public InstanceModel(PodDetail podDetail) {
        this.setInstanceId(podDetail.getPodId());
        this.setInstanceName(podDetail.getName());
        this.setStatus(podDetail.getStatus());

        this.setZoneName(podDetail.getLogicalZone());
        this.setCpuType(podDetail.getCpuType());
        this.setGpuType(podDetail.getGpuType());
        this.setBandwidthInMbps(podDetail.getBandwidthInMbps());

        this.setInternalIp(podDetail.getInternalIp());
        this.setPublicIp(podDetail.getPublicIp());

        this.setCreateTime(podDetail.getCreatedTime());
        this.setUpdateTime(podDetail.getUpdatedTime());
        this.setDeleteTime(podDetail.getDeletedTime());

        this.setRestartPolicy(podDetail.getRestartPolicy());
        this.setTags(podDetail.getPodTags());
        this.setCpu(podDetail.getCpu());
        this.setMemory(podDetail.getMemory());
    }
}