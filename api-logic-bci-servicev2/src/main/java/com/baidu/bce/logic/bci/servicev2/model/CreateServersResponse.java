package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CreateServersResponse {

    @JsonProperty("transaction_id")
    private String transcationId;
    @JsonProperty("instances")
    private List<String> instanceIds;
}