package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class DescribeContainerGroupDetailRequestExtra {
    private String from = "";
    private DetailLevel detailLevel = DetailLevel.DEFAULT;

    public enum DetailLevel {
        DEFAULT("default"),
        LIGHT("light"),
        FULL("full"),
        DELETED("deleted");

        private String level;

        DetailLevel(String level) {
            this.level = level;
        }

        public String getLevel() {
            return level;
        }
    }

    public DescribeContainerGroupDetailRequestExtra() {
        this.from = "";
        this.detailLevel = DetailLevel.DEFAULT;
    }

    public DescribeContainerGroupDetailRequestExtra(String from) {
        this.from = from;
        this.detailLevel = DetailLevel.DEFAULT;
    }

    public DescribeContainerGroupDetailRequestExtra(String from, String detailLevel) {
        this.from = from;
        this.detailLevel = DetailLevel.valueOf(detailLevel.toUpperCase());
    }
}
