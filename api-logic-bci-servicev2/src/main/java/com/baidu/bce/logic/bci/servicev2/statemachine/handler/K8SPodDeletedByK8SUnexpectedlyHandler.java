package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.K8SPodDeletedByK8SUnexpectedlyContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("K8S_POD_DELETED_BY_K8S_UNEXPECTEDLY")
public class K8SPodDeletedByK8SUnexpectedlyHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(K8SPodDeletedByK8SUnexpectedlyHandler.class);

    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof K8SPodDeletedByK8SUnexpectedlyContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        return true;
    }

    /**
     * @return
     */
    @Override
    public boolean execute() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        PodPO podPO = getPodPO();
        K8SPodDeletedByK8SUnexpectedlyContext eventContext = (K8SPodDeletedByK8SUnexpectedlyContext) getContext().getEventContext();
        podPO.setStatus(eventContext.getNewStatus());
        podPO.setResourceVersion(eventContext.getNewResourceVersion());
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        int deletePodResult = podDao.stateMachineK8SPodDeletedByK8SUnexpectedly(podPO, podDaoContext);
        if (deletePodResult == 0) {
            return false;
        }
        return true;
    }
}
