package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.kubernetes.client.openapi.models.V1Lifecycle;
import io.kubernetes.client.openapi.models.V1LifecycleHandler;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class Lifecycle {
    
    private LifecycleHandler postStart;

    private LifecycleHandler preStop;

    public V1Lifecycle toV1Lifecycle() {
        V1Lifecycle v1Lifecycle = new V1Lifecycle();
        if (this.getPostStart() != null) {
            LifecycleHandler lch = this.getPostStart();
            V1LifecycleHandler v1LifecycleHandler = lch.toV1LifecycleHandler();
            v1Lifecycle.setPostStart(v1LifecycleHandler);
        }

        if (this.getPreStop() != null) {
            LifecycleHandler lch = this.getPreStop();
            V1LifecycleHandler v1LifecycleHandler = lch.toV1LifecycleHandler();
            v1Lifecycle.setPreStop(v1LifecycleHandler);
        }

        return v1Lifecycle;
    }
}
