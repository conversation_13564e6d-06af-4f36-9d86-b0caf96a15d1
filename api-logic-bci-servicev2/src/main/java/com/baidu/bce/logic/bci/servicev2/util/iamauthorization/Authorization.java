package com.baidu.bce.logic.bci.servicev2.util.iamauthorization;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 如果需要对某个接口鉴权，请用此注解修饰，并注意配置属性
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Authorization {
    // 资源ID所在的位置，方便鉴权模块获取资源对象
    ResourceLocation resourceLocation() default ResourceLocation.NO_RESOURCE_ID;

    // 云产品的服务号，默认 BCI
    String service() default AuthorizationConstant.SERVICE_BCI;

    // 权限列表，如["CreatePod", "READ", "OPERATE"]
    String[] permissions() default {};

    public enum ResourceLocation {
        NO_RESOURCE_ID,
        IN_STRING,
        IN_ID_LIST,
        IGNORE_UNAVAILABLE_RESOURCE_FROM_ID_LIST,
        IN_WEBSHELL_REQ,
        IN_CLASS_GETTER;
    }
}