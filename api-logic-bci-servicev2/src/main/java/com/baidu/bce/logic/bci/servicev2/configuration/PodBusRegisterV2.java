package com.baidu.bce.logic.bci.servicev2.configuration;

import com.baidu.bce.internalsdk.iam.model.AccessKey;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.service.bus.sdk.BusClient;
import com.baidu.bce.service.bus.sdk.util.InetAddressHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
@ConditionalOnExpression("${pod.bus.enabled:false}")
public class PodBusRegisterV2 implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodBusRegisterV2.class);

    @Value("${pod.bus.registered:true}")
    private boolean podBusRegistered;

    @Value("${server.port}")
    private String port;

    @Value(("${bce.logical.region}"))
    private String region;

    @Value(("${bce.tag.path:/v2/tag/resources}"))
    private String tagPath;

    @Autowired
    private IamLogicService iamLogicService;

    @Override
    public void afterPropertiesSet() throws Exception {
        AccessKey accessKey = iamLogicService.getConsoleAccessKey();
        BusClient busClient = new BusClient(accessKey.getAccess(), accessKey.getSecret());
        LOGGER.info("Register beginning...");
        

        String endpoint = InetAddressHelper.getHostAddress() + ":" + port;

     
        HashMap<String, String> configuration = new HashMap<>();
        configuration.put("tagPath", tagPath);
       
        if (podBusRegistered) {
            busClient.registerService("tag", "BCI",
                    endpoint, region, configuration);
            LOGGER.info("Register success : {}.", "tag, BCI," + endpoint);
        } else {
            busClient.unregisterService("tag", "BCI", endpoint);
            LOGGER.info("Unregister success : {}.", "tag, BCI, " + endpoint);
        }
    }

}
