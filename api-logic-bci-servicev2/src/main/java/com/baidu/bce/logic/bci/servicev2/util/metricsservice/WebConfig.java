package com.baidu.bce.logic.bci.servicev2.util.metricsservice;

import org.springframework.boot.context.embedded.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WebConfig {
    @Bean
    public FilterRegistrationBean responseWrapperFilter() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(new ResponseWrapperFilter());
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }
}
