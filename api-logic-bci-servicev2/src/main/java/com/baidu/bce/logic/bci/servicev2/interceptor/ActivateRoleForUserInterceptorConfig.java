package com.baidu.bce.logic.bci.servicev2.interceptor;

import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;


/**
* @Configuration  2020-03-26 激活在console-bci fe调iam接口，不再后端默认激活
*/
public class ActivateRoleForUserInterceptorConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Value("${iam.sts.rolename:BceServiceRole_bci}")
    private String roleName;

    @Value("${pod.sts.policy.id}")
    private String policyId;

    @Value("${pod.sts.service.account.id}")
    private String podServiceAccountId;

    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ActivateRoleForUserInterceptor(
                logicPodClientFactory, roleName, policyId, podServiceAccountId))
                .addPathPatterns("/**");
    }
}
