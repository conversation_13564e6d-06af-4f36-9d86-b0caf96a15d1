package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class Container {
    // 容器名
    private String name;
    // 镜像名称
    private String image;
    // CPU类型
    // private String cpuType;
    // CPU数量
    private float cpu;
    // Gpu类型
    // private String gpuType;
    // Gpu类型
    private float gpu;
    // 内存，限制该容器最多可使用的内存值，该值不可超过容器实例的总内存值。单位：GiB
    private float memory;
    // 容器工作目录
    private String workingDir;
    // 镜像拉取策略。
    private String imagePullPolicy;
    // 容器启动命令
    private List<String> commands;
    // 容器启动参数
    private List<String> args;
    // 数据卷挂载信息
    private List<VolumeMount> volumeMounts;
    // 容器内端口信息
    private List<Port> ports;
    // 容器内操作系统的环境变量
    private List<Environment> environmentVars;
    // 容器日志收集信息
    private List<LogCollection> logCollections;
    // 就绪探针
    private Probe readinessProbe;
    // 存活探针
    private Probe livenessProbe;
    // 启动探针
    private Probe startupProbe;

    // 此容器是否应在容器运行时为标准输入分配缓冲区。如果未设置，则容器中标准输入的读取值将导致EOF。默认为false。
    private Boolean stdin = false;
    // 当标准输入为true时，标准输入流将在多个附加会话中是否保持开启状态。
    // 如果StdinOnce设为true，标准输入在容器开启时被打开，在首个客户端附加到标准输入之前都为空
    // 然后会一直保持开启状态，接收数据，直到客户端连接断开，此时标准输入被关闭，在容器重启前一直保持关闭状态。
    private Boolean stdinOnce = false;
    // 是否开启交互。默认为false
    private Boolean tty = false;

    // 实例运行的安全上下文
    private ContainerSecurityContext securityContext;
}
