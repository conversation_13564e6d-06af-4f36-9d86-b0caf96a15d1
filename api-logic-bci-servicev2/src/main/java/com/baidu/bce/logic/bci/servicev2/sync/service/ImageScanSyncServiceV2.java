package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.imagecachev2.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.model.ImageCachePO;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDResponse;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ImageScanSyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageAccelerateSyncServiceV2.class);
    @Autowired
    private K8sService k8sService;

    @Autowired
    protected ImageCacheDaoV2 imageCacheDaoV2;

    @Value("${enable.image.scan:false}")
    private boolean enableImageScan;

    public void syncImageAccelerateCrdForImageScan() {
        if (!enableImageScan) {
            return;
        }
        LOGGER.debug("ImageScan: begin");
        List<ImageCachePO> allImageAccDBInfo = imageCacheDaoV2.listAllSuccessImageAccAndNotScanDone();
        LOGGER.debug("ImageScanSize: {}", allImageAccDBInfo.size());
        Map<String, ImageCachePO> allImageAccDbMap = new HashMap<>();
        for (ImageCachePO imageCachePO : allImageAccDBInfo) {
            allImageAccDbMap.put(imageCachePO.getImageCacheName(), imageCachePO);
        }
        List<ImageAccelerateCRDResponse> allImageAccCRDInfo = k8sService.listAllImageAccCRDs();
        Map<String, ImageAccelerateCRDResponse> allImageAccCRDMap = new HashMap<>();
        for (ImageAccelerateCRDResponse imageAccelerateCRDResponse : allImageAccCRDInfo) {
            allImageAccCRDMap.put(imageAccelerateCRDResponse.getMetadata().getName(), imageAccelerateCRDResponse);
        }
        for (Map.Entry<String, ImageCachePO> imageAccDbEntry : allImageAccDbMap.entrySet()) {
            // crd名称
            String imageAccName = imageAccDbEntry.getKey();
            if (!allImageAccCRDMap.containsKey(imageAccName)) {
                LOGGER.debug("ImageScan: image crd not exist in k8s. crd name: {}", imageAccName);
                continue;
            }
            // scan未完成, 则跳过处理
            ImageAccelerateCRDResponse crdInfo = allImageAccCRDMap.get(imageAccName);
            if (crdInfo.getStatus() == null || !crdInfo.getStatus().isImageScanDone()) {
                LOGGER.debug("ImageScan: image crd scan not done. crd name: {}, status {}", imageAccName, crdInfo);
                continue;
            }
            String cpuTypesString = "{}";
            if (crdInfo.getStatus().getImageCPUTypes() != null) {
                cpuTypesString = JsonUtil.toJSON(crdInfo.getStatus().getImageCPUTypes());
            }
            // 更新数据库cpuTypes字段
            imageCacheDaoV2.updateImageAccScanDoneByName(imageAccName, cpuTypesString);
            LOGGER.debug("ImageScan: update cpuTypes to db success. crd name: {}, cpuTypes: {}",
                imageAccName, cpuTypesString);
        }
        LOGGER.debug("ImageScan: end");
    }
}