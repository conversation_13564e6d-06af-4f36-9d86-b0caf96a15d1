package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.K8SPodDeletedByUserContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("K8S_POD_DELETED_BY_USER")
public class K8SPodDeletedByUserHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(K8SPodDeletedByUserHandler.class);

    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof K8SPodDeletedByUserContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        return true;
    }

    /**
     * 1. resourceVersion 更新为 k8S pod resourceVersion
     * 2. 更新pod的bci资源版本号,resource_version and bci_resource_version
     * @return
     */
    @Override
    public boolean execute() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        PodPO podPO = getPodPO();
        K8SPodDeletedByUserContext eventContext = (K8SPodDeletedByUserContext) getContext().getEventContext();
        podPO.setResourceVersion(eventContext.getNewResourceVersion());
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        int deletePodResult = podDao.stateMachineK8SPodDeletedByUser(podPO, podDaoContext);
        if (deletePodResult == 0) {
            return false;
        }
        return true;
    }
}

