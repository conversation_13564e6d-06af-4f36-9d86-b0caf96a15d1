package com.baidu.bce.logic.bci.servicev2.util.metricsservice;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;

public class CustomHttpServletResponseWrapper extends HttpServletResponseWrapper {
    private int status;

    public CustomHttpServletResponseWrapper(HttpServletResponse response) {
        super(response);
    }

    @Override
    public void setStatus(int sc) {
        super.setStatus(sc);
        this.status = sc; // 保存状态码
    }

    @Override
    public int getStatus() {
        return this.status != 0 ? this.status : super.getStatus(); // 返回保存的状态码或默认值
    }
}