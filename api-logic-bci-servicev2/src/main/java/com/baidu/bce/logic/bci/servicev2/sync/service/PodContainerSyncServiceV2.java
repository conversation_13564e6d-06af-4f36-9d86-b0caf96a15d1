package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciFailStrategyConstant;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ChargeStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ContainerStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConditionDetail;
import com.baidu.bce.logic.bci.servicev2.constant.PodConditionTypeConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.PodRestartPolicyConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodStatusReasonConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PreemptStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPreviousState;
import com.baidu.bce.logic.bci.servicev2.model.ContainerType;
import com.baidu.bce.logic.bci.servicev2.model.PodCondition;
import com.baidu.bce.logic.bci.servicev2.model.PodStatus;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;
import com.baidu.bce.logic.bci.servicev2.statemachine.StateMachine;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.K8SPodDeletedByK8SUnexpectedlyContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.K8SPodDeletedByUserContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.K8SPodEventStatusContext;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.bci.servicev2.util.Util;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import io.kubernetes.client.extended.workqueue.DefaultRateLimitingQueue;
import io.kubernetes.client.extended.workqueue.RateLimitingQueue;
import io.kubernetes.client.informer.ResourceEventHandler;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ContainerState;
import io.kubernetes.client.openapi.models.V1ContainerStateRunning;
import io.kubernetes.client.openapi.models.V1ContainerStateTerminated;
import io.kubernetes.client.openapi.models.V1ContainerStateWaiting;
import io.kubernetes.client.openapi.models.V1ContainerStatus;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodCondition;
import io.kubernetes.client.openapi.models.V1PodStatus;
import lombok.SneakyThrows;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

@Service("PodContainerSyncServiceV2")
public class PodContainerSyncServiceV2 extends SyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodContainerSyncServiceV2.class);
    private LinkedBlockingQueue<V1Pod> podQueue;
    private boolean closeThread = false; // 退出使用，暂时没用上
    public static final String reasonAndMessageDelimiter = ";;";

    public static final String BCI_IGNORE_EXIT_CODE_CONTAINER_REASON = "Completed";
    public static final String BCI_IGNORE_EXIT_CODE_CONTAINER_MESSAGE = "Force this container to be success(137, Error, )";

    private RateLimitingQueue<SyncPodEvent> addWorkQueue;
    private Map<String, Integer> addRequeueRecord;

    private RateLimitingQueue<SyncPodEvent> updateWorkQueue;
    private Map<String, Integer> updateRequeueRecord;

    private RateLimitingQueue<V1Pod> deleteWorkQueue;
    private Map<String, Integer> deleteRequeueRecord;

    @Value("${bci.pod.add.event.requeue.delay.time.second:1}")
    private int podAddEventRequeueDelayTimeSecond = 1;

    @Value("${bci.pod.update.event.requeue.delay.time.second:1}")
    private int podUpdateEventRequeueDelayTimeSecond = 1;

    @Value("${bci.pod.delete.event.requeue.delay.time.second:1}")
    private int podDeleteEventRequeueDelayTimeSecond = 1;

    @Value("${bci.pod.add.event.sync.cycle.interval.millsecond:1000}")
    private int podAddEventSyncCycleIntervalMillsecond = 1000;
    @Value("${bci.pod.update.event.sync.cycle.interval.millsecond:1000}")
    private int podUpdateEventSyncCycleIntervalMillsecond = 1000;
    @Value("${bci.pod.delete.event.sync.cycle.interval.millsecond:1000}")
    private int podDeleteEventSyncCycleIntervalMillsecond = 1000;

    @Value("${bci.pod.add.event.sync.retry.limit:50}")
    private int podAddEventSyncRetryLimit = 50;

    @Value("${bci.pod.update.event.sync.retry.limit:50}")
    private int podUpdateEventSyncRetryLimit = 50;

    @Value("${bci.pod.delete.event.sync.retry.limit:50}")
    private int podDeleteEventSyncRetryLimit = 50;

    private static final String SIDECAR_START_FAILED = "bci_internal_sidecarStartFailed";
    private static final String ANNOTATION_SIDECAR_NAME = "bci_internal_sidecarContainer";
    public static final String ENI_CREATE_FAILED = "bci_internal_createEniFailed";

    public static final String BCI_INTERNAL_EVICTNOTREADYNODEPOD_ANNOTATION_KEY = "bci_internal_evictNotReadyNodePod";

    public static final String BCI_INTERNAL_EVICTNOTREADYNODEPOD_REASON = "evicted";

    public static final int RETRY_LIMIT = 50;
    public static final String UPDATE_TRIGGER = "updateTrigger";
    public static final String IMAGE_DOWNLOAD_WORKLOAD_CONTAINER_PREFIX =
            PodConstants.BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX + "image-workload-";
    public static final String IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX =
            PodConstants.BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX + "image-init-";
    private static final String INCOMPLETE_STATUS_PREFIX = "containers with incomplete status: ";
    private static final String UNREADY_STATUS_PREFIX = "containers with unready status: ";
    private static final String UNKNOWN_STATUS_PREFIX = "containers with unknown status: ";

    private static final String BCI_INTERNAL_POD_RECYCLE_REASON = "bci_internal_PodRecycleReason";

    private static final String BCI_INTERNAL_CONTROLLER_AUTO_DELETE = "controllerAutoDelete";

    private static final String BCI_INTERNAL_TRANSFER_IMAGE_CACHE_SUCCEED = "transferImageCacheSucceed";

    @Value("${bci.filterpod.enable:true}")
    private boolean enableFilterPod;

    @Value("${bci.sync.podContainerSync.syncContainers.delaySecondsPerRound:0}")
    private long syncContainersDelaySecondsPerRound;

    @Autowired
    @Qualifier("podAddEventSyncThreadPoolTaskExecutorV2")
    private ThreadPoolTaskExecutor podAddThreadPoolTaskExecutor;

    @Autowired
    @Qualifier("podUpdateEventSyncThreadPoolTaskExecutorV2")
    private ThreadPoolTaskExecutor podUpdateThreadPoolTaskExecutor;

    @Autowired
    @Qualifier("podDeleteEventSyncThreadPoolTaskExecutorV2")
    private ThreadPoolTaskExecutor podDeleteThreadPoolTaskExecutor;

    @Autowired
    private BillingResourceSyncManagerV2 billingResourceSyncManager;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PodMigrateService podMigrateService;

    @Autowired
    private StateMachine stateMachine;

    public enum PodEventType {
        ADD,
        UPDATE,
        DELETE;
    }

    public enum K8SDeletePodType {
        K8S_EVICT_POD,
        K8S_DELETE_POD_UNEXPECTEDLY;
    }

    // use postConstruct to ensure only execute once
    @PostConstruct
    private void postConstruct() {
        LOGGER.info("PodContainerSyncService postConstruct");
        new Thread(new Runnable() {
            //@SneakyThrows
            @Override
            public void run() {
                addWorkQueue = new DefaultRateLimitingQueue<>();
                addRequeueRecord = new ConcurrentHashMap<>();
                try {
                    syncPodAdd(stateMachine);
                } catch (InterruptedException e) {
                    LOGGER.error("syncPodAdd worker exit .", e);
                }
            }
        }).start();

        new Thread(new Runnable() {
            //@SneakyThrows
            @Override
            public void run() {
                updateWorkQueue = new DefaultRateLimitingQueue<>();
                updateRequeueRecord = new ConcurrentHashMap<>();
                try {
                    syncPodUpdate(stateMachine);
                } catch (InterruptedException e) {
                    LOGGER.error("syncPodUpdate worker exit .", e);
                }
            }
        }).start();

        new Thread(new Runnable() {
            //@SneakyThrows
            @Override
            public void run() {
                deleteWorkQueue = new DefaultRateLimitingQueue<>();
                deleteRequeueRecord = new ConcurrentHashMap<>();
                try {
                    syncPodDelete(stateMachine);
                } catch (InterruptedException e) {
                    LOGGER.error("syncPodDelete worker exit .", e);
                }
            }
        }).start();
    }
    public class SyncPodEvent {
        public SyncPodEvent(V1Pod pod, PodEventType podEventType) {
            this.podName = pod.getMetadata().getName();
            this.namespace = pod.getMetadata().getNamespace();
            this.podEventType = podEventType;
        }

        private String podName;
        private String namespace;
        private PodEventType podEventType;

        public String getPodName() {
            return podName;
        }

        public void setPodName(String podName) {
            this.podName = podName;
        }

        public String getNamespace() {
            return namespace;
        }

        public void setNamespace(String namespace) {
            this.namespace = namespace;
        }

        public PodEventType getPodEventType() {
            return podEventType;
        }

        public void setPodEventType(PodEventType podEventType) {
            this.podEventType = podEventType;
        }
    }

    public ResourceEventHandler<V1Pod> getPodEventHandler() {
        return new ResourceEventHandler<V1Pod>() {
            @SneakyThrows
            @Override
            public void onAdd(V1Pod pod) {
                if (pod == null || pod.getMetadata() == null || pod.getStatus() == null) {
                    return;
                }
                LOGGER.debug("receive cce pod {} add event, resource version is {}",
                        pod.getMetadata().getName(), pod.getMetadata().getResourceVersion());
                // syncPodStatus(pod);
                // podQueue.put(pod);
                if (enableFilterPod) {
                    if (!pod.getMetadata().getName().startsWith("p-")) {
                        return;
                    }
                }

                try {
                    ContainerCurrentState containerCurrentState =
                            PodUtils.convertBean(pod.getStatus().getContainerStatuses().get(0).getState(),
                                    ContainerCurrentState.class);
                    LOGGER.debug("receive pod {} add event, currentstate is:{}",
                            pod.getMetadata().getName(),
                            JsonUtil.toJSON(containerCurrentState));
                } catch (Exception e) {
                    // 忽略空指针异常，防止丢失事件
                }
                SyncPodEvent syncPodEvent = new SyncPodEvent(pod, PodEventType.ADD);
                addWorkQueue.add(syncPodEvent);
            }

            @SneakyThrows
            @Override
            public void onUpdate(V1Pod oldPod, V1Pod newPod) {
                if (newPod == null || newPod.getMetadata() == null || newPod.getStatus() == null) {
                    return;
                }
                LOGGER.debug("receive cce pod {} update event, new resource version is {}",
                        newPod.getMetadata().getName(), newPod.getMetadata().getResourceVersion());
                // syncPodStatus(newPod);
                // podQueue.put(newPod);
                if (enableFilterPod) {
                    if (!newPod.getMetadata().getName().startsWith("p-")) {
                        return;
                    }
                }
                try {
                    LOGGER.debug("receive pod {} update event, prestate is:{}, currentstate is:{}",
                            newPod.getMetadata().getName(), JsonUtil.toJSON(PodUtils.convertBean(
                                    newPod.getStatus().getContainerStatuses().get(0).getState(),
                                    ContainerCurrentState.class)), JsonUtil.toJSON(PodUtils.convertBean(
                                    newPod.getStatus().getContainerStatuses().get(0).getLastState(),
                                    ContainerCurrentState.class)));
                } catch (Exception e) {
                    // 忽略空指针异常，防止丢失事件
                }
                SyncPodEvent syncPodEvent = new SyncPodEvent(newPod, PodEventType.UPDATE);
                updateWorkQueue.add(syncPodEvent);
            }

            @Override
            public void onDelete(V1Pod pod, boolean b) {
                if (pod == null || pod.getMetadata() == null || pod.getStatus() == null) {
                    return;
                }
                LOGGER.debug("receive cce pod {} delete event, resource version is {}",
                        pod.getMetadata().getName(), pod.getMetadata().getResourceVersion());
                if (enableFilterPod) {
                    if (!pod.getMetadata().getName().startsWith("p-")) {
                        return;
                    }
                }

                try {
                    boolean migratePod = podMigrateService.isMigratePod(pod);
                    if (migratePod) {
                        podMigrateService.createMigratePod(pod);
                    }
                    deleteWorkQueue.add(pod);
                } catch (Exception e) {
                    LOGGER.error("migratePod {}:{} onDelete event process err {} ", pod.getMetadata().getNamespace(),
                            pod.getMetadata().getName(), e);
                }
            }
        };
    }

    private boolean setControllerAutoDeleteRecycleReasonIfPossible(Map<String,String> annotations, PodPO podPO) {
        if (annotations == null || annotations.size() == 0) {
            return false;
        }
        // pod 被controller 主动删除
        String podRecycleReason = annotations.get(BCI_INTERNAL_POD_RECYCLE_REASON);
        if (!StringUtils.isEmpty(podRecycleReason) &&
                BCI_INTERNAL_CONTROLLER_AUTO_DELETE.equals(podRecycleReason)) {
            if (podPO.getResourceRecycleTimestamp() == 0L) {
                LOGGER.debug("pod:{} evicted by controller", podPO.getPodId());
                podPO.setResourceRecycleTimestamp(System.currentTimeMillis());
                podPO.setResourceRecycleReason(ResourceRecycleReason.CONTROLLER_AUTO_DELETE.toString());
            }
            return true;
        }
        return false;
    }

    private boolean setTcSucceedRecycleReasonIfPossible(Map<String,String> annotations, PodPO podPO) {
        if (!podPO.getName().startsWith("p-tc-")) {
            return false;
        }

        if (annotations == null || annotations.size() == 0) {
            return false;
        }
        // tc-pod-xxx 执行成功后，被controller 主动标记资源回收
        String podRecycleReason = annotations.get(BCI_INTERNAL_POD_RECYCLE_REASON);
        if (!StringUtils.isEmpty(podRecycleReason) && BCI_INTERNAL_TRANSFER_IMAGE_CACHE_SUCCEED.equals(podRecycleReason)) {
            if (podPO.getResourceRecycleTimestamp() == 0L) {
                LOGGER.debug("tcPod:{} will be recycled", podPO.getPodId());
                podPO.setResourceRecycleTimestamp(System.currentTimeMillis());
                podPO.setResourceRecycleReason(ResourceRecycleReason.TRANSFER_IMAGE_CACHE_SUCCEED.toString());
            }
            return true;
        }
        return false;
    }

    public String getPodNextStatusWhenK8SPodDeletePod(
            PodPO podPO, PodEventType podEventType, K8SDeletePodType k8SDeletePodType) {
        String podOldStatus = podPO.getStatus();
        String podNewStatus = podOldStatus;
        if (podEventType == PodEventType.DELETE || podEventType == PodEventType.UPDATE) {
            if (podOldStatus.equalsIgnoreCase(BciStatus.SUCCEEDED.getStatus())) {
                podNewStatus = BciStatus.SUCCEEDED.getStatus();
            } else if (podOldStatus.equalsIgnoreCase(BciStatus.PENDING.getStatus())) {
                podNewStatus = BciStatus.PENDING.getStatus();
            } else if (podOldStatus.equalsIgnoreCase(BciStatus.FAILED.getStatus())) {
                podNewStatus = BciStatus.FAILED.getStatus();
                if (podPO.getResourceRecycleTimestamp() == 0L) {
                    long recycleTimestamp = System.currentTimeMillis();
                    podPO.setResourceRecycleTimestamp(recycleTimestamp);
                    if (k8SDeletePodType == K8SDeletePodType.K8S_EVICT_POD ||
                        k8SDeletePodType == K8SDeletePodType.K8S_DELETE_POD_UNEXPECTEDLY) {
                        podPO.setResourceRecycleReason(ResourceRecycleReason.POD_DELETED_UNEXPECTEDLY.toString());
                    }
                }
            } else {
                podNewStatus = BciStatus.FAILED.getStatus();
                if (podPO.getResourceRecycleTimestamp() == 0L) {
                    long recycleTimestamp = System.currentTimeMillis();
                    podPO.setResourceRecycleTimestamp(recycleTimestamp);
                    if (k8SDeletePodType == K8SDeletePodType.K8S_EVICT_POD ||
                        k8SDeletePodType == K8SDeletePodType.K8S_DELETE_POD_UNEXPECTEDLY) {
                        podPO.setResourceRecycleReason(ResourceRecycleReason.POD_DELETED_UNEXPECTEDLY.toString());
                    }
                }
            }
        }
        return podNewStatus;
    }

    public void syncPodAdd(StateMachine stateMachine) throws InterruptedException {
        LOGGER.debug("syncPodAdd start thread for processing pod add event");
        long loopRunTimes = 0;
        while (!addWorkQueue.isShuttingDown()) {
            loopRunTimes++;
            LOGGER.debug("syncPodAdd loopRunTimes:{} podAddWorkQueueSize:{} podAddRequeueRecordSize:{}",
                    loopRunTimes, addWorkQueue.length(), addRequeueRecord.size());
            long curLoopSucceededCount = 0;
            long curLoofailedCount = 0;
            while (addWorkQueue.length() > 0) {
                SyncPodEvent pod = addWorkQueue.get();
                addWorkQueue.done(pod);
                if (pod == null) {
                    LOGGER.error("syncPodAdd get pod add event from addWorkQueue error");
                    curLoopSucceededCount++;
                    continue;
                }
                String podName = pod.getPodName();
                try {
                    podAddThreadPoolTaskExecutor.execute(new PodContainerSyncServiceV2.SyncPod(pod,
                            stateMachine));
                    curLoopSucceededCount++;
                } catch (Exception e) {
                    LOGGER.error("syncPodAdd podAddThreadPoolTaskExecutor execute syncPod " +
                            "podName:{}, exception:{}", podName, e);
                    curLoofailedCount++;
                }
            }
            LOGGER.debug("syncPodAdd podAddThreadPoolTaskExecutor " +
                            "Add Event stats syncPodAddLoopRunTimes:{} " +
                            "syncPodAddCurLoopSucceededCount:{} syncPodAddCurLoopFailedCount:{} " +
                            "syncPodAddPoolSize:{} syncPodAddActiveCount:{} syncPodAddMaxPoolSize:{} " +
                            "syncPodAddQueueSize:{} syncPodAddCompletedTaskCount:{}",
                    loopRunTimes,
                    curLoopSucceededCount, curLoofailedCount,
                    podAddThreadPoolTaskExecutor.getPoolSize(),
                    podAddThreadPoolTaskExecutor.getActiveCount(),
                    podAddThreadPoolTaskExecutor.getMaxPoolSize(),
                    podAddThreadPoolTaskExecutor.getThreadPoolExecutor().getQueue().size(),
                    podAddThreadPoolTaskExecutor.getThreadPoolExecutor().getCompletedTaskCount());
            if (podAddEventSyncCycleIntervalMillsecond > 0) {
                try {
                    Thread.sleep(podAddEventSyncCycleIntervalMillsecond);
                } catch (InterruptedException e) {
                    LOGGER.error("syncPodAdd sleep exception:{}", e);
                }
            }
        }
    }

    public void syncPodUpdate(StateMachine stateMachine) throws InterruptedException {
        LOGGER.debug("syncPodUpdate start thread for processing pod update event");
        long loopRunTimes = 0;
        while (!updateWorkQueue.isShuttingDown()) {
            loopRunTimes++;
            HashMap<String, SyncPodEvent> podmap = new HashMap();
            LOGGER.debug("syncPodUpdate loopRunTimes:{} podUpdateWorkQueueSize:{} podUpdateRequeueRecordSize:{}",
                    loopRunTimes, updateWorkQueue.length(), updateRequeueRecord.size());
            long curLoopSucceededCount = 0;
            long curLoofailedCount = 0;
            long curLoopPodUpdateEventCount = 0;
            while (updateWorkQueue.length() > 0) {
                SyncPodEvent pod = updateWorkQueue.get();
                updateWorkQueue.done(pod);
                if (pod == null) {
                    LOGGER.error("syncPodUpdate get pod event from updateWorkQueue error");
                    continue;
                }
                curLoopPodUpdateEventCount++;
                podmap.put(pod.getPodName(), pod);
            }
            LOGGER.debug("syncPodUpdate loopRunTimes:{} curLoopPodUpdateEventCount:{} curLoopPodUpdatePodMapSize:{}",
                    loopRunTimes, curLoopPodUpdateEventCount, podmap.size());
            for (Map.Entry<String, SyncPodEvent> entry : podmap.entrySet()) {
                SyncPodEvent pod = entry.getValue();
                String podName = pod.getPodName();
                try {
                    podUpdateThreadPoolTaskExecutor.execute(new PodContainerSyncServiceV2.SyncPod(pod, stateMachine));
                    curLoopSucceededCount++;
                } catch (Exception e) {
                    LOGGER.error("syncPodUpdate podUpdateThreadPoolTaskExecutor execute syncPod " +
                            "podName:{},exception exception:{}", podName, e);
                    curLoofailedCount++;
                }
            }
            LOGGER.debug("syncPodUpdate podUpdateThreadPoolTaskExecutor " +
                            "Update Event stats " +
                            "syncPodUpdateLoopRunTimes:{} " +
                            "syncPodUpdateCurLoopSucceededCount:{} syncPodUpdateCurLoopFailedCount:{} " +
                            "syncPodUpdatePoolSize:{} syncPodUpdateActiveCount:{} syncPodUpdateMaxPoolSize:{} " +
                            "syncPodUpdateQueueSize:{} syncPodUpdateCompletedTaskCount:{}",
                    loopRunTimes,
                    curLoopSucceededCount, curLoofailedCount,
                    podUpdateThreadPoolTaskExecutor.getPoolSize(),
                    podUpdateThreadPoolTaskExecutor.getActiveCount(),
                    podUpdateThreadPoolTaskExecutor.getMaxPoolSize(),
                    podUpdateThreadPoolTaskExecutor.getThreadPoolExecutor().getQueue().size(),
                    podUpdateThreadPoolTaskExecutor.getThreadPoolExecutor().getCompletedTaskCount());
            if (podUpdateEventSyncCycleIntervalMillsecond > 0) {
                try {
                    Thread.sleep(podUpdateEventSyncCycleIntervalMillsecond);
                } catch (InterruptedException e) {
                    LOGGER.error("syncPodUpdate sleep exception:{}", e);
                }
            }
        }
    }

    public void syncPodDelete(StateMachine stateMachine) throws InterruptedException {
        LOGGER.debug("syncPodDelete start thread for processing pod delete event");
        long loopRunTimes = 0;
        while (!deleteWorkQueue.isShuttingDown()) {
            loopRunTimes++;
            LOGGER.debug("syncPodDelete loopRunTimes:{} podDeleteWorkQueueSize:{} podDeleteRequeueRecordSize:{}",
                    loopRunTimes, deleteWorkQueue.length(), deleteRequeueRecord.size());
            long curLoopSucceededCount = 0;
            long curLoofailedCount = 0;
            while (deleteWorkQueue.length() > 0) {
                V1Pod pod = deleteWorkQueue.get();
                deleteWorkQueue.done(pod);
                if (pod == null) {
                    LOGGER.error("syncPodDelete get pod delete event from deleteWorkQueue error");
                    curLoopSucceededCount++;
                    continue;
                }
                String podName = pod.getMetadata().getName();
                try {
                    podDeleteThreadPoolTaskExecutor.execute(new PodContainerSyncServiceV2.SyncDeletePod(pod, stateMachine));
                    curLoopSucceededCount++;
                } catch (Exception e) {
                    LOGGER.error("syncPodDelete podDeleteThreadPoolTaskExecutor execute syncDeletePod " +
                                    "podName:{}, exception:{}", podName, e);
                    curLoofailedCount++;
                }
            }
            LOGGER.debug("syncPodDelete podDeleteThreadPoolTaskExecutor " +
                            "Delete Event stats " +
                            "syncPodDeleteLoopRunTimes:{} " +
                            "syncPodUpdateCurLoopSucceededCount:{} syncPodUpdateCurLoopFailedCount:{} " +
                            "syncPodUpdatePoolSize:{} syncPodUpdateActiveCount:{} syncPodUpdateMaxPoolSize:{} " +
                            "syncPodUpdateQueueSize:{} syncPodUpdateCompletedTaskCount:{}",
                    loopRunTimes,
                    curLoopSucceededCount, curLoofailedCount,
                    podDeleteThreadPoolTaskExecutor.getPoolSize(),
                    podDeleteThreadPoolTaskExecutor.getActiveCount(),
                    podDeleteThreadPoolTaskExecutor.getMaxPoolSize(),
                    podDeleteThreadPoolTaskExecutor.getThreadPoolExecutor().getQueue().size(),
                    podDeleteThreadPoolTaskExecutor.getThreadPoolExecutor().getCompletedTaskCount());
            if (podDeleteEventSyncCycleIntervalMillsecond > 0) {
                try {
                    Thread.sleep(podDeleteEventSyncCycleIntervalMillsecond);
                } catch (InterruptedException e) {
                    LOGGER.error("syncPodDelete sleep exception:{}", e);
                }
            }
        }
    }

    public class SyncPod implements Runnable {
        private StateMachine stateMachine;

        private SyncPodEvent pod;

        public SyncPod() {
        }

        public SyncPod(SyncPodEvent pod, StateMachine stateMachine) {
            this.pod = pod;
            this.stateMachine = stateMachine;
        }

        @Override
        public void run() {
            BceInternalRequest.setThreadRequestId(PodUtils.createLongUuid());
            boolean retry = true;
            String podName = pod.getPodName();
            try {
                retry = addAndUpdatePodContainer(pod);
            } catch (Exception e) {
                LOGGER.error("failed to sync status of pod {}, exception: {}", podName, e);
            } finally {
                if (pod.getPodEventType() == PodEventType.UPDATE) {
                    if (retry) {
                        if (updateRequeueRecord.containsKey(podName) &&
                                updateRequeueRecord.get(podName) > podUpdateEventSyncRetryLimit) {
                            LOGGER.warn("pod {} updateRequeueRecord reach max limit,cancel it", podName);
                            updateRequeueRecord.remove(podName);
                        } else {
                            updateWorkQueue.addAfter(pod, Duration.ofSeconds(podUpdateEventRequeueDelayTimeSecond));
                            if (updateRequeueRecord.containsKey(podName)) {
                                updateRequeueRecord.put(podName,
                                        updateRequeueRecord.get(podName) + 1);
                            } else {
                                updateRequeueRecord.put(podName, 1);
                            }
                        }
                    } else {
                        if (updateRequeueRecord.containsKey(podName)) {
                            updateRequeueRecord.remove(podName);
                        }
                    }
                } else if (pod.getPodEventType() == PodEventType.ADD) {
                    if (retry) {
                        if (addRequeueRecord.containsKey(podName) &&
                                addRequeueRecord.get(podName) > podAddEventSyncRetryLimit) {
                            LOGGER.warn("pod {} addRequeueRecord reach max limit,cancel it", podName);
                            addRequeueRecord.remove(podName);
                        } else {
                            addWorkQueue.addAfter(pod, Duration.ofSeconds(podAddEventRequeueDelayTimeSecond));
                            if (addRequeueRecord.containsKey(podName)) {
                                addRequeueRecord.put(podName,
                                        addRequeueRecord.get(podName) + 1);
                            } else {
                                addRequeueRecord.put(podName, 1);
                            }
                        }
                    } else {
                        if (addRequeueRecord.containsKey(podName)) {
                            addRequeueRecord.remove(podName);
                        }
                    }
                }
            }
        }

        /**
         * @Description 添加并更新Pod容器，包括状态机事件触发、条件更新、资源回收等操作
         * @Param podEvent SyncPodEvent 包含Pod名称、命名空间等信息
         * @Return boolean 如果Pod状态或者资源版本未更新则返回false，否则返回true，表示需要重试
         * @Throws Exception 无
         */
        public boolean addAndUpdatePodContainer(SyncPodEvent podEvent) {
            boolean retry = false;
            if (podEvent == null || podEvent.getPodName() == null || podEvent.getNamespace() == null) {
                return false;
            }
            String podId = podEvent.getPodName();
            String namespace = podEvent.getNamespace();
            String userId = namespace;
            String podEventType = podEvent.getPodEventType().toString();
            LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{} userId:{}",
                    podEventType, podId, userId);
            try {
                PodPO podPO = podDao.getPodById(userId, podId);
                if (podPO == null) {
                    LOGGER.error("addAndUpdatePodContainer podEventType {} not find podPO by  podId {} ",
                            podEventType, podId);
                    return false; // 返回false，不重试
                }
                if (podPO.getPreemptStatus() != null
                        && podPO.getPreemptStatus().equals(PreemptStatus.PREEMPTED.toString())) {
                    LOGGER.debug("addAndUpdatePodContainer podEventType {} podPO is PREEMPTED,  podId {}",
                            podEventType, podId);
                    return false; // 返回false，不重试
                }

                String podCurrentStatus = podPO.getStatus();
                long podOldResourceVersion = podPO.getResourceVersion();
                // 从informer 中获取最新pod
                V1Pod pod = k8sService.getPod(namespace, podId);
                if (pod == null || pod.getMetadata() == null || pod.getStatus() == null) {
                    LOGGER.error("addAndUpdatePodContainer podEventType {} not find pod {}  in k8s cluster",
                            podEventType, podId);
                    return false; // 返回false，不重试
                }

                // 计费的时候依赖 pod uuid，所以提前设置
                if (podPO.getPodUuid() == null || podPO.getPodUuid().isEmpty() || podPO.getPodUuid().equals(podPO.getPodId())) {
                    podPO.setPodUuid(pod.getMetadata().getUid());
                    podDao.updatePodUuid(podPO);
                }

                long podNewResourceVersion = Long.parseLong(pod.getMetadata().getResourceVersion());
                if (podNewResourceVersion <= podOldResourceVersion) {
                    LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{}  k8s resourceVersion:{} less than " +
                                    "db resourceVersion:{} and skip sync pod add or update event",
                            podEventType, podId, podNewResourceVersion, podOldResourceVersion);
                    // informer pod k8s resourceVersion <= db resourceVersion 直接返回false
                    return false; // 返回false，不重试
                }

                // 实例状态同步时，禁止更新 终态(Failed/Success) 的实例，避免实例状态又变回 Pending 等中间态
                if (checkPodIsFinalStatus(podCurrentStatus)) {
                    LOGGER.warn("addAndUpdatePodContainer podEventType {} pod {}  has entered the final status {}, Pod" +
                                    " status changes are forbidden",
                            podEventType, podId, podCurrentStatus);
                    return false; // 返回false，不重试
                }

                String podPhase = pod.getStatus().getPhase() == null ? "" : pod.getStatus().getPhase().toLowerCase();

                LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{}  k8s podNewResourceVersion:{} db " +
                        "podOldResourceVersion:{} k8s podPhase:{} podStatus:{}",
                        podEventType, podId, podNewResourceVersion,
                        podOldResourceVersion, podPhase, JsonUtil.toJSON(pod.getStatus()));

                if (podMigrateService.isMigratePod(pod)) {
                    LOGGER.warn("addAndUpdatePodContainer podEventType:{} pod {}:{}  is Migrate pod, ignore sync pod " +
                                    "and container status ",
                            podEventType, pod.getMetadata().getNamespace(), podId);
                    return false; // 返回false，不重试
                }
                if (podPO.getStatus().equalsIgnoreCase(BciStatus.PENDING.getStatus()) &&
                        (podPO.getPodId().equalsIgnoreCase(podPO.getPodUuid()) ||
                                StringUtils.isEmpty(podPO.getResourceUuid()))) {
                    // db中的pod还是pending状态,只是影响container更新pod_uuid,其他都可以更新,因此这里返回true,
                    // 下次继续更新一次,而不是直接返回,导致pod的状态和conditions都无法更新;
                    retry = true; // 返回true会重试
                }

                // 获取eni创建失败信息
                Map<String, String> annotations = pod.getMetadata().getAnnotations();
                if (annotations != null && annotations.size() > 0) {
                    String eniFailed = annotations.get(ENI_CREATE_FAILED);
                    if (!StringUtils.isEmpty(eniFailed)) {
                        LOGGER.error("addAndUpdatePodContainer podEventType {} order {} podId {}  create eni failed ," +
                                        "delete it",
                                podEventType, podPO.getOrderId(), podId);
                        // TODO: eni 失败，pod phase置为pending，防止vk重复创建pod
                        // 暂时设置为Failed状态,后续ENI问题解决后再修改为Pending
                        podPhase = BciStatus.FAILED.getStatus();
                        LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{}  ENI_CREATE_FAILED podPhase:{}",
                                podEventType, podId, podPhase);
                        // 回收pod资源
                        if (podPO.getResourceRecycleTimestamp() == 0L) {
                            long recycleTimestamp = System.currentTimeMillis();
                            podPO.setResourceRecycleTimestamp(recycleTimestamp);
                            podPO.setResourceRecycleReason(ResourceRecycleReason.ENI_CREATED_FAIL.toString());
                        }
                    }
                }

                // 获取sidecar 失败信息
                if (annotations != null && annotations.size() > 0) {
                    String failedSidecar = annotations.get(SIDECAR_START_FAILED);
                    if (!StringUtils.isEmpty(failedSidecar)) {
                        LOGGER.error("addAndUpdatePodContainer podEventType {} order {} podId {}  sidecar container {}" +
                                        " start failed , delete it",
                                podEventType, podPO.getOrderId(), podId, failedSidecar);
                        podPhase = BciStatus.FAILED.getStatus();
                        LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{} SIDECAR_START_FAILED " +
                                        "podPhase:{}",
                                podEventType, podId, podPhase);
                        if (podPO.getResourceRecycleTimestamp() == 0L) {
                            long recycleTimestamp = System.currentTimeMillis();
                            podPO.setResourceRecycleTimestamp(recycleTimestamp);
                            podPO.setResourceRecycleReason(ResourceRecycleReason.SIDECAR_START_FAIL.toString());
                        }
                    }
                }

                // 更新zone && subnet
                if (annotations != null && annotations.size() > 0) {
                    String zoneId = annotations.get(PodConstants.BCI_INTERNAL_PREFIX + "ZoneID");
                    String logicZone = annotations.get(PodConstants.BCI_INTERNAL_PREFIX + "LogicZone");
                    String subnetUuID = annotations.get("cross-vpc-eni.cce.io/subnetUuid");
                    if (!StringUtils.isEmpty(zoneId) && !zoneId.equals(podPO.getZoneId())) {
                        podPO.setZoneId(zoneId);
                    }
                    if (!StringUtils.isEmpty(subnetUuID) && !subnetUuID.equals(podPO.getSubnetUuid())) {
                        podPO.setSubnetUuid(subnetUuID);
                    }
                    if (!StringUtils.isEmpty(logicZone) && !logicZone.equals(podPO.getLogicalZone())) {
                        podPO.setLogicalZone(logicZone);
                    }
                }

                // 处理pod 被controller 主动删除的逻辑
                boolean controllerAutoDeletePod = setControllerAutoDeleteRecycleReasonIfPossible(annotations, podPO);

                // 处理tc-pod-xxx 被 image cache controller 主动标记资源回收的逻辑
                boolean tcSucceedPod = setTcSucceedRecycleReasonIfPossible(annotations, podPO);

                LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{} " +
                            "podPhase:{} controllerAutoDeletePod:{} tcSucceedPod:{}",
                        podEventType, podId, podPhase, controllerAutoDeletePod, tcSucceedPod);

                // 消除内置容器对pod phase的影响，主要是面向Job类Pod（restartPolicy!=Always），因为内置容器的存在导致无法terminated的场景
                podPhase = getPodPhaseAfterRemovingBciInternalContainer(podPO, pod, podPhase);
                LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{}  after " +
                        "getPodPhaseAfterRemovingBciInternalContainer " +
                        "podPhase:{}",
                        podEventType, podId, podPhase);
                // 消除ds功能容器和ds容器对pod phase的影响，主要是面向Deployment类Pod（restartPolicy=Always），
                // 因为ds容器和ds功能容器的存在，导致phase迟迟处于pending以及从running变为pending的情况。
                // bci容器分类见https://console.cloud.baidu-int.com/devops/icafe/issue/s-k8s-1895/show?source=copy-shortcut
                podPhase = getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(podPO, pod, podPhase);
                LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{}  after " +
                        "getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer podPhase:{}",
                        podEventType, podId, podPhase);

                // 判断Pod是否处于终态
                if (isFinalStatePod(podPO.getRestartPolicy(), podPhase) && !controllerAutoDeletePod && !tcSucceedPod) {
                    // 回收pod资源
                    // 注意:当Pod处于终态时,忽略Pod当前的资源回收时间和原因,强行覆盖当前的回收时间和回收原因
//                    if (podPO.getResourceRecycleTimestamp() == 0L) {
                        long oldResourceRecycleTimestamp = podPO.getResourceRecycleTimestamp();
                        String oldResourceRecycleReason = podPO.getResourceRecycleReason();
                        long recycleTimestamp = System.currentTimeMillis();
                        podPO.setResourceRecycleTimestamp(recycleTimestamp);
                        podPO.setResourceRecycleReason(ResourceRecycleReason.JOB_POD_COMPLETE.toString());
                        LOGGER.debug("addAndUpdatePodContainer withFinalStatePod podEventType:{} podId:{} " +
                                        "podPhase:{} oldResourceRecycleTimestamp:{} oldResourceRecycleReason:{} " +
                                        "and update newResourceRecycleTimestamp:{} newResourceRecycleReason:{}",
                                podEventType, podId, podPhase,
                                oldResourceRecycleTimestamp, oldResourceRecycleReason,
                                podPO.getResourceRecycleTimestamp(),
                                podPO.getResourceRecycleReason());
//                    }
                }

                StateMachineEvent stateMachineEvent = getK8SPodEventStatusEventType(podPO, pod, podPhase);
                LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{} podPhase:{} stateMachineEvent:{}",
                        podEventType, podId, podPhase, stateMachineEvent.toString());

                // 检查Pod是否被K8S主动驱逐
                // K8S主动驱逐Pod的话 Pod.metadata.annotations.bci_internal_evictNotReadyNodePod会被设置为evicted
                // 并且Pod.metadata.deletionTimestamp也会被设置
                if (annotations != null && annotations.size() > 0) {
                    String bciInternalEvictNotReadyNodePod = annotations.get(BCI_INTERNAL_EVICTNOTREADYNODEPOD_ANNOTATION_KEY);
                    if (!StringUtils.isEmpty(bciInternalEvictNotReadyNodePod) &&
                            bciInternalEvictNotReadyNodePod.equalsIgnoreCase(BCI_INTERNAL_EVICTNOTREADYNODEPOD_REASON) &&
                            pod.getMetadata().getDeletionTimestamp() != null) {
                        stateMachineEvent = StateMachineEvent.K8S_POD_EVICTED;
                        LOGGER.debug("addAndUpdatePodContainer podEventType:{} pod:{} evicted node NotReady"
                                        + " podCurrentStatus:{}, bciInternalEvictNotReadyNodePod:{},"
                                        + " oldResourceVersion:{}, newResourceVersion:{}"
                                        + " creationTimestamp:{}, deletionTimestamp:{}",
                                podEventType, podId,
                                podCurrentStatus, bciInternalEvictNotReadyNodePod,
                                podOldResourceVersion, podNewResourceVersion,
                                pod.getMetadata().getCreationTimestamp(), pod.getMetadata().getDeletionTimestamp());
                        podPhase = getPodNextStatusWhenK8SPodDeletePod(
                                podPO, PodEventType.UPDATE, K8SDeletePodType.K8S_EVICT_POD);
                        LOGGER.debug("addAndUpdatePodContainer podEventType:{} podId:{} K8S_POD_EVICTED podPhase:{}",
                                podEventType, podId, podPhase);
                    }
                }
                
                // TODO: 状态更新时刻的获取逻辑需要修改
                Timestamp statusUpdateTime = new Timestamp(new Date().getTime());
                LOGGER.debug("addAndUpdatePodContainer podEventType:{} [call billing] chargeStatus in " +
                        "updatePodContainer for podId:{}, podUuid:{}",
                        podEventType, podId, podPO.getPodUuid());
                chargeStatus(podPhase, podPO, statusUpdateTime, podNewResourceVersion);
                String newStatus = BciStatus.getStatus(podPhase);
                podPO.setStatus(newStatus);
                String newInternalIp = pod.getStatus().getPodIP() == null ? "" : pod.getStatus().getPodIP();
                podPO.setInternalIp(newInternalIp);
                // 更新IPv6辅助ip
                if (annotations != null && annotations.size() > 0) {
                    String newInternalIPv6 = annotations.get(PodConstants.BCI_INTERNAL_PREFIX + "PodIPv6");
                    if (!StringUtils.isEmpty(newInternalIPv6)) {
                        podPO.setInternalIPv6(newInternalIPv6);
                    }
                }
                List<ContainerPO> containerPOS = syncContainer(podPO, pod, podEvent.getPodEventType());
                // 更新pod conditions
                List<PodCondition> newConditions = new ArrayList<>();
                try {
                    newConditions = buildBciPodConditions(pod, podPO, containerPOS);
                } catch (Exception e) {
                    LOGGER.error("addAndUpdatePodContainer podEventType:{} build podId:{} error:{}",
                            podEventType, podId, e);
                }
                boolean processPodFailStrategyResult = processPodFailStrategy(podPO, pod, newConditions, containerPOS);
                LOGGER.info("addAndUpdatePodContainer podEventType:{} begin k8sPodEventStatusTrigger pod:{}, " +
                                "stateMachineEvent:{},"
                        + " oldResourceVersion:{}, newResourceVersion:{},"
                        + " podCurrentStatus:{}, podNextStatus:{}"
                        + " processPodFailStrategyResult:{}",
                        podEventType, podId, stateMachineEvent,
                        podOldResourceVersion, podNewResourceVersion,
                        podCurrentStatus, podPO.getStatus(), processPodFailStrategyResult);
                boolean updateRet = k8sPodEventStatusTrigger(
                        podPO, stateMachineEvent, podNewResourceVersion, newStatus, JsonUtil.toJSON(newConditions));
                if (!updateRet) {
                    LOGGER.warn("addAndUpdatePodContainer podEventType:{} k8sPodEventStatusTrigger failed, pod:{}, " +
                                    "stateMachineEvent:{},"
                            + " oldResourceVersion:{}, newResourceVersion:{},"
                            + " podCurrentStatus:{}, podNextStatus:{}",
                            podEventType, podId, stateMachineEvent,
                            podOldResourceVersion, podNewResourceVersion,
                            podCurrentStatus, podPO.getStatus());
                    return true;
                }
                if (!CollectionUtils.isEmpty(containerPOS)) {
                    containerDao.batchUpdate(containerPOS);
                }
            } catch (Exception e) {
                // 这里加argus报警
                LOGGER.error("addAndUpdatePodContainer podEventType:{} failed to react with pod:{}, exception " +
                        "detail:{}", podEventType, podId, e);
                return true;
            }
            return retry;
        }

        public boolean processPodFailStrategy(PodPO podPO, V1Pod pod, List<PodCondition> conditions, List<ContainerPO> containerPOS) {
            String podId = podPO.getPodId();
            V1PodStatus podStatus = pod.getStatus();
            if (podStatus == null) {
                return false;
            }
            // 1. 判断pod是否处于Pending状态
            String podStatusPhase = pod.getStatus().getPhase() == null ? "" : pod.getStatus().getPhase();
            if (!BciStatus.STATUS_PENDING.equalsIgnoreCase(podStatusPhase)) {
                return false;
            }
            // 2. 判断Pod 是否设置了pod-fail-strategy Annotation
            String podFailStrategy = PodUtils.getPodAnnotationValueByKey(pod,
                    PodConstants.BCI_FAIL_STRATEGY_ANNOTATION_KEY);
            if (StringUtils.isEmpty(podFailStrategy)) {
                return false;
            }
            // 2.1 判断podFailStrategy == "fail-fast"
            if (!BciFailStrategyConstant.FAIL_FAST.equals(podFailStrategy)) {
                return false;
            }
            // 3. 检查Pod pod.spec.nodeName为空
            if (!StringUtils.isEmpty(pod.getSpec().getNodeName())) {
                LOGGER.debug("processPodFailStrategy podId:{} pod.spec.nodeName is not empty", podId);
                return false;
            }
            // 4. 检查Pod pod.status.podIP为空
            if (!StringUtils.isEmpty(pod.getStatus().getPodIP())) {
                LOGGER.debug("processPodFailStrategy podId:{} pod.status.podIP is not empty", podId);
                return false;
            }
            // 5. 检查Pod pod.status.conditions是否包含如下item:
//            status:
//              conditions:
//              - lastProbeTime: null
//                lastTransitionTime: "2025-02-27T09:36:54Z"
//                message: '0/77 nodes are available: 1 node(s) had taint {node.kubernetes.io/unreachable:
//                 }, that the pod didn''t tolerate, 2 node(s) were unschedulable, 29 node(s) didn''t
//                 match Pod''s node affinity, 33 Insufficient cpu, 33 Insufficient memory, 6 node(s)
//                 had taint {node-role.kubernetes.io/master: }, that the pod didn''t tolerate,
//                 6 node(s) had taint {node.kubernetes.io/not-ready: }, that the pod didn''t tolerate.'
//                reason: Unschedulable
//                status: "False"
//                type: PodScheduled
//                phase: Pending
            List<V1PodCondition> podConditions = podStatus.getConditions();
            if (CollectionUtils.isEmpty(podConditions)) {
                LOGGER.debug("processPodFailStrategy podId:{} podConditions is empty", podId);
                return false;
            }
            for (V1PodCondition podCondition : podConditions) {
                if (podCondition.getType().equalsIgnoreCase("PodScheduled") &&
                        podCondition.getStatus().equalsIgnoreCase("False") &&
                        podCondition.getReason().equalsIgnoreCase("Unschedulable")) {
                    // 设置VK Pod status字段如下:
//                    "status": {
//                        "conditions": [
//                            {
//                                    "lastProbeTime": "2023-03-30T18:11:31Z",
//                                    "lastTransitionTime": "2023-03-30T18:11:31Z",
//                                    "message": "Create BCI failed because the specified instance is out of stock. %s",
//                                    "reason": "ContainerGroup.NoStock",
//                                    "status": "False",
//                                    "type": "ContainerInstanceCreated"
//                            }
//                        ],
//                        "Reason":"ContainerInstanceScheduleFailed",
//                        "phase": "Pending"
//                    }
                    PodCondition podAddFailStrategyCondition = genPodFailStrategyCondition(podCondition);
                    conditions.add(podAddFailStrategyCondition);
                    LOGGER.debug("processPodFailStrategy podId:{}, podAddFailStrategyCondition:{}",
                            JsonUtil.toJSON(podAddFailStrategyCondition));
                    // 将 Pod.status.phase 和 pod.status.reason字段透传给VK
                    boolean result = addPodFailStrategyPhaseAndReasonToPodStatus(podPO, pod, containerPOS);
                    LOGGER.debug("processPodFailStrategy call addPodFailStrategyPhaseAndReasonToPodStatus podId:{}, result:{}", podId, result);
                    return true;
                }
            }
            return false;
        }

        public boolean addPodFailStrategyPhaseAndReasonToPodStatus(PodPO podPO, V1Pod pod,
                                                                   List<ContainerPO> containerPOS) {
            String podId = podPO.getPodId();
            if (CollectionUtils.isEmpty(containerPOS)) {
                LOGGER.warn("addPodFailStrategyPhaseAndReasonToPodStatus podId:{}, containerPOS is empty", podId);
                return false;
            }
            ContainerPO firstWorkloadContainer = null;
            String containerName = "";
            for (ContainerPO containerPO : containerPOS) {
                if (ContainerType.WORKLOAD.getType().equals(containerPO.getContainerType())) {
                    firstWorkloadContainer = containerPO;
                    break;
                }
            }
            if (firstWorkloadContainer == null) {
                LOGGER.warn("addPodFailStrategyPhaseAndReasonToPodStatus podId:{}, firstWorkloadContainer is null " +
                                "will ignore set pod status reason and phase", podId);
                return false;
            }
            containerName = firstWorkloadContainer.getName();
            String newCurrentState = genNewContainerCurrentState(podPO, pod, firstWorkloadContainer);
            firstWorkloadContainer.setCurrentState(newCurrentState);
            LOGGER.debug("addPodFailStrategyPhaseAndReasonToPodStatus podId:{}, containerName:{}, currentStatus:{}",
                    podId, containerName, newCurrentState);
            return true;
        }

        public String genNewContainerCurrentState(PodPO podPO, V1Pod pod, ContainerPO containerPO) {
            String podId = podPO.getPodId();
            String containerName = containerPO.getName();
            PodStatus podStatus = new PodStatus();
            ContainerCurrentState newContainerCurrentState = new ContainerCurrentState();
            String currentState = containerPO.getCurrentState();
            LOGGER.debug("genNewContainerCurrentState podId:{}, containerName:{}, oldCurrentState:{}",
                    podId, containerName, currentState);
            podStatus.setReason(PodStatusReasonConstant.CONTAINER_INSTANCE_SCHEDULE_FAILED);
            podStatus.setPhase(podPO.getStatus());
            podStatus.setMessage(PodConditionDetail.NO_STOCK_CONDITION.getMessage());
            if (StringUtils.isEmpty(currentState)) {
                LOGGER.debug("genNewContainerCurrentState currentState is empty podId:{}, containerName:{}, " +
 "oldCurrentState:{}", podId, containerName, currentState);
                newContainerCurrentState.setDetailStatus(JsonUtil.toJSON(podStatus));
            } else {
                LOGGER.debug("genNewContainerCurrentState currentState is not empty podId:{}, containerName:{}, " +
                        "oldCurrentState:{}", podId, containerName, currentState);
                ContainerCurrentState oldCurrentState = JsonUtil.fromJSON(currentState, ContainerCurrentState.class);
                podStatus.setContainerDetailStatus(oldCurrentState.getDetailStatus());
                newContainerCurrentState.setDetailStatus(JsonUtil.toJSON(podStatus));
                newContainerCurrentState.setState(oldCurrentState.getState());
                newContainerCurrentState.setContainerStartTime(oldCurrentState.getContainerStartTime());
                newContainerCurrentState.setContainerFinishTime(oldCurrentState.getContainerFinishTime());
                newContainerCurrentState.setExitCode(oldCurrentState.getExitCode());
            }
            LOGGER.debug("genNewContainerCurrentState podId:{}, containerName:{}, newCurrentState:{}",
                    podId, containerName, JsonUtil.toJSON(newContainerCurrentState));
            return JsonUtil.toJSON(newContainerCurrentState);
        }

        public PodCondition genPodFailStrategyCondition(V1PodCondition podCondition) {
            PodCondition podAddFailStrategyCondition = new PodCondition();
            if (podCondition.getLastTransitionTime() != null) {
                podAddFailStrategyCondition.setLastTransitionTime(Util.convertOffsetDateTimeToDate(podCondition.getLastTransitionTime()));
            } else {
                podAddFailStrategyCondition.setLastTransitionTime(new Date());
            }
            if (podCondition.getLastProbeTime() != null) {
                podAddFailStrategyCondition.setLastProbeTime(Util.convertOffsetDateTimeToDate(podCondition.getLastProbeTime()));
            } else {
                podAddFailStrategyCondition.setLastProbeTime(podAddFailStrategyCondition.getLastTransitionTime());
            }
            podAddFailStrategyCondition.setReason(PodConditionDetail.NO_STOCK_CONDITION.getReason());
            podAddFailStrategyCondition.setMessage(PodConditionDetail.NO_STOCK_CONDITION.getMessage());
            podAddFailStrategyCondition.setStatus("False");
            podAddFailStrategyCondition.setType(PodConditionTypeConstant.CONTAINER_INSTANCE_CREATED);
            return podAddFailStrategyCondition;
        }

        /**
         * 判断Pod是否处于最终状态
         * */
        boolean checkPodIsFinalStatus(String podStatus) {
            if (BciStatus.FAILED.getStatus().equalsIgnoreCase(podStatus) ||
                    BciStatus.SUCCEEDED.getStatus().equalsIgnoreCase(podStatus)) {
                return true;
            }
            return false;
        }

        private StateMachineEvent getK8SPodEventStatusEventType(PodPO podPO, V1Pod k8sPod, String k8sPodPhase) {
            String k8sPodStatusPhase = k8sPod.getStatus().getPhase() == null ? k8sPodPhase :
                    k8sPod.getStatus().getPhase();
            StateMachineEvent event;
            switch(k8sPodStatusPhase) {
                case BciStatus.STATUS_PENDING:
                    event = StateMachineEvent.K8S_POD_PENDING;
                    break;
                case BciStatus.STATUS_RUNNING:
                    event = StateMachineEvent.K8S_POD_RUNNING;
                    break;
                case BciStatus.STATUS_SUCCEEDED:
                    event = StateMachineEvent.K8S_POD_SUCCEEDED;
                    break;
                case BciStatus.STATUS_FAILED:
                    event = StateMachineEvent.K8S_POD_FAILED;
                    break;
                case BciStatus.STATUS_UNKNOWN:
                    event = StateMachineEvent.K8S_POD_UNKNOWN;
                    break;
                default:
                    event = StateMachineEvent.K8S_POD_UNKNOWN;
                    break;
            }
            return event;
        }

        boolean k8sPodEventStatusTrigger(PodPO podPO, StateMachineEvent event,
                                         long newResourceVersion, String newStatus,
                                         String newConditions) {
            String podId = podPO.getPodId();
            String userId = podPO.getUserId();
            K8SPodEventStatusContext k8sPodEventStatusContext = new K8SPodEventStatusContext(
                    podId, userId, newResourceVersion, newStatus, newConditions);
            if (!stateMachine.trigger(podPO, event, k8sPodEventStatusContext)) {
                LOGGER.warn("k8sPodEventStatusTrigger: pod state machine trigger failed, podId:{} event:{}",
                        podId, event.toString());
                return false;
            }
            return true;
        }

        public boolean isFinalStatePod(String restartPolicy, String podPhase) {
            return !restartPolicy.equalsIgnoreCase(PodRestartPolicyConstant.ALWAYS) &&
                    (podPhase.equalsIgnoreCase(BciStatus.SUCCEEDED.getStatus()) || podPhase.equalsIgnoreCase(BciStatus.FAILED.getStatus()));
        }

        public String getPodPhaseAfterRemovingBciInternalContainer(PodPO podPO, V1Pod pod, String originalPodPhase) {
            String kubeproxy = null;
            if (pod.getMetadata() != null && pod.getMetadata().getAnnotations() != null) {
                String sidecarNamesStr = pod.getMetadata().getAnnotations().get(ANNOTATION_SIDECAR_NAME);
                if (sidecarNamesStr != null) {
                    String[] sidecarNames = sidecarNamesStr.split(",");
                    for (String sidecarName : sidecarNames) {
                        if (sidecarName.startsWith("kube-proxy-")) {
                            kubeproxy = sidecarName;
                        }
                    }
                }
            }

            String podIgnoreExitCodeContainers = PodUtils.getPodAnnotationValueByKey(pod, PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY);
            List<String> podIgnoreExitCodeContainersList = new ArrayList<>();
            if (podIgnoreExitCodeContainers != null && !StringUtils.isEmpty(podIgnoreExitCodeContainers)) {
                podIgnoreExitCodeContainersList = Arrays.asList(podIgnoreExitCodeContainers.split(","));
            }

            String finalPodPhase = originalPodPhase;
            if (!podPO.getRestartPolicy().equalsIgnoreCase(PodRestartPolicyConstant.ALWAYS)
                    && !originalPodPhase.equalsIgnoreCase(BciStatus.SUCCEEDED.getName())
                    && !originalPodPhase.equalsIgnoreCase(BciStatus.FAILED.getName())) {
                int userContainerCount = 0;
                int stoppedCount = 0;
                int failedCount = 0;
                if (pod.getStatus() != null && pod.getStatus().getContainerStatuses() != null) {
                    for (V1ContainerStatus status : pod.getStatus().getContainerStatuses()) {
                        // bci internal container, just skip it.
                        if (status.getName().startsWith(PodConstants.BCI_CONTAINER_PREFIX)
                                || org.apache.commons.lang.StringUtils.equals(kubeproxy, status.getName())) {
                            continue;
                        }
                        // bci ignore exit code container, just skip it.
                        // 忽略用户自定义的容器退出码(状态)
                        if (podIgnoreExitCodeContainersList.contains(status.getName())) {
                            continue;
                        }
                        userContainerCount++;
                        if (isSuccessed(status.getState())) {
                            stoppedCount++;
                        }
                        if (isFailed(status.getState())) {
                            stoppedCount++;
                            failedCount++;
                        }
                    }
                    // all user container are terminated.
                    if (userContainerCount == stoppedCount) {
                        if (failedCount == 0) {
                            finalPodPhase = BciStatus.SUCCEEDED.getName();
                        } else if (podPO.getRestartPolicy().equalsIgnoreCase(PodRestartPolicyConstant.NEVER)) {
                            finalPodPhase = BciStatus.FAILED.getName();
                        }
                    }
                }   
            }
            return finalPodPhase;
        }

        public String getPodPhaseAfterRemovingBciDsAndDsFunctionalContainer(PodPO podPO, V1Pod pod, 
                                                                            String originalPodPhase) {
            String finalPodPhase = originalPodPhase;
            if (pod == null || pod.getStatus() == null || pod.getStatus().getContainerStatuses() == null 
                || pod.getStatus().getConditions() == null 
                || pod.getMetadata() == null || pod.getMetadata().getAnnotations() == null 
                || pod.getSpec() == null || pod.getSpec().getContainers() == null) {
                return finalPodPhase;
            }
            Set<String> dsContainers = Util.getListFromString(
                pod.getMetadata().getAnnotations().getOrDefault(PodConstants.BCI_DS_CONTAINER_NAMES, ""));
            Set<String> dsFunctionalContainers = Util.getListFromString(
                pod.getMetadata().getAnnotations().getOrDefault(PodConstants.BCI_DS_FUNCTIONAL_CONTAINER_NAMES, ""));
            boolean podInitialized = false;
            for (V1PodCondition condition : pod.getStatus().getConditions()) {
                if ("Initialized".equals(condition.getType()) && "True".equals(condition.getStatus())) {
                    podInitialized = true;
                    break;
                }
            }
            
            // 目前只有always实例支持注入ds容器，所以pod phase优化只针对always实例。
            // 面向的场景是：实例phase是pending，但是如果没有ds容器和ds功能容器，实例phase应该是running。
            // 具体优化规则（和1.20.8版本的kubelet判定一个pod为running的逻辑保持一致）：
            //    Pod已经初始化，且剔除ds容器和ds功能容器后，剩余容器的数量等于spec中预期容器数量，且剩余容器没有waiting，全是running或terminated
            // 注：容器的ApiStatus中没有Unknown状态，所以此处计算Pod Phase的时候，不像kubelet一样考虑Unknown状态
            // 剔除ds容器和ds功能容器后，pod spec中剩余的容器数量
            int specContainerCount = 0;
            // 剔除ds容器和ds功能容器后，pod status中剩余的容器数量
            int statusContainerCount = 0;
            // 剔除ds容器和ds功能容器后，pod status中剩余的waiting容器数量
            int statusWaitingCount = 0;
            if (podPO.getRestartPolicy().equalsIgnoreCase(PodRestartPolicyConstant.ALWAYS)
                && originalPodPhase.equalsIgnoreCase(BciStatus.PENDING.getName())) {
                for (V1Container c : pod.getSpec().getContainers()) {
                    // bci ds container, just skip it.
                    if (dsContainers.contains(c.getName())) {
                        continue;
                    }
                    // bci ds functional container, just skip it.
                    if (dsFunctionalContainers.contains(c.getName())) {
                        continue;
                    }
                    specContainerCount += 1;
                }
                for (V1ContainerStatus status : pod.getStatus().getContainerStatuses()) {
                    // bci ds container, just skip it.
                    if (dsContainers.contains(status.getName())) {
                        continue;
                    }
                    // bci ds functional container, just skip it.
                    if (dsFunctionalContainers.contains(status.getName())) {
                        continue;
                    }
                    statusContainerCount += 1;
                    if (status.getState().getWaiting() != null) {
                        statusWaitingCount += 1;
                    }
                }
                if (podInitialized && specContainerCount == statusContainerCount && statusWaitingCount == 0) {
                    finalPodPhase = BciStatus.RUNNING.getName();
                }
            }
            return finalPodPhase;
        }

        private String genePodUpdateTrigger(PodPO pod) {
            Map<String, String> m = new HashMap<>();
            String extra = pod.getExtra();
            if (StringUtils.isEmpty(extra)) {
                m.put(UPDATE_TRIGGER, String.valueOf(1));
            } else {
                m = JsonConvertUtil.fromJSON(extra, Map.class);
                if (m.containsKey(UPDATE_TRIGGER)) {
                    m.put(UPDATE_TRIGGER, String.valueOf(Integer.valueOf(m.get(UPDATE_TRIGGER)) + 1));
                } else {
                    m.put(UPDATE_TRIGGER, String.valueOf(1));
                }
            }
            return JsonConvertUtil.toJSON(m);
        }

        /**
         * 生成pod condition，隐藏由于注入sidecar 或 initContainer 导致的notReady
         *
         * @param pod
         * @param containerPOS
         * @return
         */
        public List<PodCondition> buildBciPodConditions(V1Pod pod, PodPO podPO, List<ContainerPO> containerPOS) {
            List<V1PodCondition> conditions = pod.getStatus().getConditions();
            // 拷贝一份
            List<PodCondition> copyConditions = new ArrayList<>();
            if (CollectionUtils.isEmpty(conditions)) {
                return mergeConditions(copyConditions, podPO);
            }
            // 只存储原生的k8s condition
            Map<String, String> k8sConditionTypeMap = new HashMap<>();
            k8sConditionTypeMap.put("ContainersReady", "1");
            k8sConditionTypeMap.put("Initialized", "1");
            k8sConditionTypeMap.put("Ready", "1");
            k8sConditionTypeMap.put("PodScheduled", "1");


            for (V1PodCondition condition : conditions) {
                if (!k8sConditionTypeMap.containsKey(condition.getType())) {
                    continue;
                }
                PodCondition copyCondition = new PodCondition();
                copyCondition.setReason(condition.getReason());
                copyCondition.setMessage(condition.getMessage());
                copyCondition.setType(condition.getType());
                if (condition.getLastProbeTime() != null) {
                    copyCondition.setLastProbeTime(Date.from(condition.getLastProbeTime().toInstant()));
                }
                if (condition.getLastTransitionTime() != null) {
                    copyCondition.setLastTransitionTime(Date.from(condition.getLastTransitionTime().toInstant()));
                }
                copyCondition.setStatus(condition.getStatus());
                copyConditions.add(copyCondition);
            }

            // 内置container的condition同步
            try {
                // 判断bci内置的sidecar和init 容器是否失败,如果失败则设置pod的condition信息为相应状态
                // 先判断eni annotation, 再判断镜像下载的init container,再判断sidecar;
                // 获取eni创建失败信息
                Map<String, String> annotations = pod.getMetadata().getAnnotations();
                if (annotations != null && annotations.size() > 0) {
                    String createEniFailedMessage = annotations.get(ENI_CREATE_FAILED);
                    if (!StringUtils.isEmpty(createEniFailedMessage)) {
                        setPodCondition(copyConditions, "Ready", "False",
                                PodConstants.POD_ALLOCATE_IP_ERROR, createEniFailedMessage);
                        setPodCondition(copyConditions, "ContainersReady", "False",
                                PodConstants.POD_ALLOCATE_IP_ERROR, createEniFailedMessage);
                        setPodCondition(copyConditions, "Initialized", "False",
                                PodConstants.POD_ALLOCATE_IP_ERROR, createEniFailedMessage);
                        return mergeConditions(copyConditions, podPO);
                    }
                }
                // 判断镜像下载init container是否正常
                List<V1ContainerStatus> initContainerStatuses = pod.getStatus().getInitContainerStatuses();
                if (initContainerStatuses != null) {
                    boolean hasUserInitContainer = false;
                    ImagePullStatus imagePullStatus = ImagePullStatus.PULL_SUCCESS;
                    String containerNamePullingImageFailed = "";
                    for (int i = 0; i < initContainerStatuses.size(); i++) {
                        String initContainerName = initContainerStatuses.get(i).getName();
                        if (initContainerName.startsWith(
                                PodConstants.BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX)) {
                            boolean ready = initContainerStatuses.get(i).getReady();
                            V1ContainerState state = initContainerStatuses.get(i).getState();
                            V1ContainerState lastState = initContainerStatuses.get(i).getLastState();
                            int restartCount = initContainerStatuses.get(i).getRestartCount();
                            // 下载失败
                            if (state != null && state.getTerminated() != null) {
                                V1ContainerStateTerminated terminated = state.getTerminated();
                                if (terminated.getExitCode() == null || terminated.getExitCode() == 0) {
                                    // 镜像下载成功
                                    continue;
                                } else {
                                    // 镜像下载失败
                                    LOGGER.error("pod {} image download init container {} failed",
                                            pod.getMetadata().getName(), initContainerName);
                                    imagePullStatus = ImagePullStatus.PULL_ERROR;
                                    containerNamePullingImageFailed = getFailedContainerName(initContainerName,
                                            pod.getMetadata().getAnnotations());
                                }
                            } else {
                                // 根据当前 state 无法判定状态，只能看一下 laststate
                                if (lastState != null && lastState.getTerminated() != null) {
                                    V1ContainerStateTerminated terminated = lastState.getTerminated();
                                    if (terminated.getExitCode() != null && terminated.getExitCode() != 0) {
                                        // 镜像下载失败
                                        LOGGER.error("pod {} image download init container {} failed",
                                                pod.getMetadata().getName(), initContainerName);
                                        imagePullStatus = ImagePullStatus.PULL_ERROR;
                                        containerNamePullingImageFailed = getFailedContainerName(initContainerName,
                                                pod.getMetadata().getAnnotations());
                                    } else {
                                        imagePullStatus = ImagePullStatus.PULLING;
                                        containerNamePullingImageFailed = getFailedContainerName(initContainerName,
                                                pod.getMetadata().getAnnotations());
                                    }
                                } else {
                                    imagePullStatus = ImagePullStatus.PULLING;
                                    containerNamePullingImageFailed = getFailedContainerName(initContainerName,
                                            pod.getMetadata().getAnnotations());
                                }
                            }
                        } else {
                            hasUserInitContainer = true;
                        }
                    }
                    if (imagePullStatus == ImagePullStatus.PULL_ERROR) {
                        if (hasUserInitContainer) {
                            setPodCondition(copyConditions, "Initialized", "False",
                                    "ContainersNotReady",
                                    "failed to pull image of container " + containerNamePullingImageFailed);
                            setPodCondition(copyConditions, "ContainersReady", "False",
                                    "initialized not ready", "initialized not ready");
                            setPodCondition(copyConditions, "Ready", "False",
                                    "initialized not ready", "initialized not ready");
                        } else {
                            setPodCondition(copyConditions, "Initialized", "True", null, null);
                            setPodCondition(copyConditions, "ContainersReady", "False", "ContainersNotReady",
                                    "failed to pull image of container " + containerNamePullingImageFailed);
                            setPodCondition(copyConditions, "Ready", "False", "ContainersNotReady",
                                    "failed to pull image of container " + containerNamePullingImageFailed);
                        }
                    } else if (imagePullStatus == ImagePullStatus.PULLING) {
                        if (hasUserInitContainer) {
                            setPodCondition(copyConditions, "Initialized", "False",
                                    "ContainersNotReady",
                                    "pulling image of container " + containerNamePullingImageFailed);
                            setPodCondition(copyConditions, "ContainersReady", "False",
                                    "initialized not ready", "initialized not ready");
                            setPodCondition(copyConditions, "Ready", "False",
                                    "initialized not ready", "initialized not ready");
                        } else {
                            setPodCondition(copyConditions, "Initialized", "True", null, null);
                            setPodCondition(copyConditions, "ContainersReady", "False", "ContainersNotReady",
                                    "pulling image of container " + containerNamePullingImageFailed);
                            setPodCondition(copyConditions, "Ready", "False", "ContainersNotReady",
                                    "pulling image of container " + containerNamePullingImageFailed);
                        }
                    }
                    if (imagePullStatus != ImagePullStatus.PULL_SUCCESS) {
                        return mergeConditions(copyConditions, podPO);
                    }
                }
                SidecarStatus sidecarStatus = SidecarStatus.INITIALIZE_SUCCESS;
                if (pod.getStatus() != null && pod.getStatus().getContainerStatuses() != null) {
                    for (V1ContainerStatus containerStatus : pod.getStatus().getContainerStatuses()) {
                        String contName = containerStatus.getName();
                        // 计算 sidecar 执行情况
                        if (org.apache.commons.lang.StringUtils.equals(contName, PodConstants.BCI_POST_START_SIDECAR)) {
                            // waiting 状态，说明 sidecar 还在执行
                            V1ContainerState state = containerStatus.getState();
                            if (state == null || state.getWaiting() != null) {
                                sidecarStatus = SidecarStatus.INITIALIZING;
                            }
                        }
                    }
                }
                if (hasFailedSidecar(pod)) {
                    setPodCondition(copyConditions, "ContainersReady", "False",
                            "InternalError", "bci internal component init failed");
                    setPodCondition(copyConditions, "Ready", "False",
                            "InternalError", "bci internal component init failed");
                } else if (sidecarStatus == SidecarStatus.INITIALIZING) {
                    setPodCondition(copyConditions, "ContainersReady", "False",
                            "ContainerCreating", "bci internal component initializing");
                    setPodCondition(copyConditions, "Ready", "False",
                            "ContainerCreating", "bci internal component initializing");
                }
                // 内置容器过滤
                filterBciInternalContainers(copyConditions);
            } catch (Exception e) {
                LOGGER.error("build Pod Conditions:{} error:{}", pod.getMetadata().getName(), e);
            }

            List<PodCondition> afterIgnoreConditions = processIgnoreNotReadyContainerConditions(copyConditions, pod);
            return  mergeConditions(afterIgnoreConditions, podPO);
        }

        public List<PodCondition> processIgnoreNotReadyContainerConditions(List<PodCondition> podStatusConditions,
                V1Pod pod) {

            if (podStatusConditions == null || podStatusConditions.isEmpty() || pod == null) {
                LOGGER.debug("processIgnoreNotReadyContainerConditions: check input invalid");
                return podStatusConditions;
            }

            String podName = getPodName(pod);
            List<String> podIgnoreNotReadyContainersList = getIgnoreNotReadyContainersList(pod);
            List<String> podNotReadyContainersList = getNotReadyContainersList(pod);

            // 如果没有配置忽略的容器或者没有not ready的容器，直接返回
            if (podIgnoreNotReadyContainersList.isEmpty() || podNotReadyContainersList.isEmpty()) {
                LOGGER.info(
                        "processIgnoreNotReadyContainerConditions: {} podIgnoreNotReadyContainers or podNotReadyContainersList is empty",
                        podName);
                return podStatusConditions;
            } else {
                LOGGER.info(
                        "processIgnoreNotReadyContainerConditions:{} podIgnoreNotReadyContainers:{},podNotReadyContainersList:{},podConditions:{}",
                        podName, podIgnoreNotReadyContainersList, podNotReadyContainersList, podStatusConditions);

            }

            // 所有not ready的容器是否都在可忽略的not ready 容器列表中
            boolean allPresent = podIgnoreNotReadyContainersList.containsAll(podNotReadyContainersList);
            if (allPresent) {
                // 全部都在，则将ContainersReady和Ready的状态设置为True
                if (podStatusConditions.stream().anyMatch(condition -> "ContainersReady".equals(condition.getType())
                        && "False".equals(condition.getStatus()))) {
                    setPodCondition(podStatusConditions, "ContainersReady", "True", null, null);
                }
                if (podStatusConditions.stream().anyMatch(
                        condition -> "Ready".equals(condition.getType()) && "False".equals(condition.getStatus()))) {
                    setPodCondition(podStatusConditions, "Ready", "True", null, null);
                }
            } else {
                List<String> notIgnoredContainers = new ArrayList<>(podNotReadyContainersList);
                notIgnoredContainers.removeAll(podIgnoreNotReadyContainersList);
                if (!notIgnoredContainers.isEmpty()) {
                    LOGGER.debug("processIgnoreNotReadyContainerConditions: pod {} not ignored containers:{}", podName,
                            notIgnoredContainers);

                    String message = "containers with unready status: [" + String.join(",", notIgnoredContainers) + "]";
                    if (podStatusConditions.stream()
                            .anyMatch(condition -> "ContainersReady".equals(condition.getType()) &&
                                    "False".equals(condition.getStatus()))) {
                        setPodCondition(podStatusConditions, "ContainersReady", "False", "ContainersNotReady", message);
                    }
                    if (podStatusConditions.stream().anyMatch(condition -> "Ready".equals(condition.getType())
                            && "False".equals(condition.getStatus()))) {
                        setPodCondition(podStatusConditions, "Ready", "False", "ContainersNotReady", message);
                    }
                }
            }
            LOGGER.debug("processIgnoreNotReadyContainerConditions after: pod {} {}", podName, podStatusConditions);
            return podStatusConditions;
        }

        private String getPodName(V1Pod pod) {
            if (pod == null || pod.getMetadata() == null || pod.getMetadata().getName() == null) {
                return "";
            }
            return pod.getMetadata().getName();
        }

        private List<String> getIgnoreNotReadyContainersList(V1Pod pod) {
            String podIgnoreNotReadyContainers = PodUtils.getPodAnnotationValueByKey(pod,
                    PodConstants.BCI_IGNORE_NOT_READY_CONTAINERS_ANNOTATION_KEY);
            List<String> podIgnoreNotReadyContainersList = new ArrayList<>();
            if (podIgnoreNotReadyContainers != null && !StringUtils.isEmpty(podIgnoreNotReadyContainers)) {
                podIgnoreNotReadyContainersList = Arrays.asList(podIgnoreNotReadyContainers.split(","));
            }
            return podIgnoreNotReadyContainersList;
        }

        private List<String> getNotReadyContainersList(V1Pod pod) {
            List<String> podNotReadyContainersList = new ArrayList<>();
            if (pod.getStatus() != null && pod.getStatus().getContainerStatuses() != null) {
                for (V1ContainerStatus containerStatus : pod.getStatus().getContainerStatuses()) {
                    String contName = containerStatus.getName();
                    if (!containerStatus.getReady()) {
                        podNotReadyContainersList.add(contName);
                    }
                }
            }
            return podNotReadyContainersList;
        }

        public List<PodCondition> mergeConditions(List<PodCondition> podStatusConditions, PodPO podPO) {
            List<PodCondition> podPOConditions = new ArrayList<>();    // 数据库podPO的conditions列表
            String podPOConditionsStr = podPO.getConditions();
            if (StringUtils.isEmpty(podPOConditionsStr)){
                return podStatusConditions;
            } else {
                podPOConditions = JsonUtil.toList(podPOConditionsStr, PodCondition.class);
            }
            List<PodCondition> mergedConditions = new ArrayList<>();   // 合并后的列表
            mergedConditions.addAll(podStatusConditions);
            for (PodCondition condition : podPOConditions) {
                if (!containsType(mergedConditions, condition.getType())) {
                    mergedConditions.add(condition);
                }
            }
            return mergedConditions;
        }

        public boolean containsType(List<PodCondition> list, String type) {
            for (PodCondition condition : list) {
                if (condition.getType().equals(type)) {
                    return true;
                }
            }
            return false;
        }

        public void filterBciInternalContainers(List<PodCondition> conditions) {
            for (PodCondition condition : conditions) {
                if (!org.apache.commons.lang.StringUtils.equals("False", condition.getStatus())) {
                    continue;
                }
                String msg = condition.getMessage();
                String messagePrefix = "";
                if (org.apache.commons.lang.StringUtils.isEmpty(msg)) {
                    continue;
                } else if (msg.startsWith(INCOMPLETE_STATUS_PREFIX)) {
                    messagePrefix = INCOMPLETE_STATUS_PREFIX;
                } else if (msg.startsWith(UNREADY_STATUS_PREFIX)) {
                    messagePrefix = UNREADY_STATUS_PREFIX;
                } else if (msg.startsWith(UNKNOWN_STATUS_PREFIX)) {
                    messagePrefix = UNKNOWN_STATUS_PREFIX;
                } else {
                    continue;
                }
                String[] splits = msg.split("\\[");
                if (splits.length != 2) {
                    continue;
                }
                String containerStr = splits[1].replaceAll("\\]", "");
                List<String> userVisibleContainers = new ArrayList<String>();
                for (String cName : containerStr.split(" ")) {
                    if (!cName.startsWith(PodConstants.BCI_CONTAINER_PREFIX)) {
                        userVisibleContainers.add(cName);
                    }
                }
                containerStr = String.join(" ", userVisibleContainers);
                condition.setMessage(String.format("%s[%s]", messagePrefix, containerStr));
            }
        }

        public boolean hasFailedSidecar(V1Pod pod) {
            Map<String, String> annotations = pod.getMetadata().getAnnotations();
            if (annotations != null && annotations.size() > 0) {
                String sidecarFailed = annotations.get(SIDECAR_START_FAILED);
                if (sidecarFailed != null && !"".equals(sidecarFailed)) {
                    return true;
                }
            }
            return false;
        }

        public String getEniCreateFailedMessage(V1Pod pod) {
            if (pod == null || pod.getMetadata() == null) {
                return null;
            }
            Map<String, String> annotations = pod.getMetadata().getAnnotations();
            if (MapUtils.isEmpty(annotations)) {
                return null;
            }
            String eniCreateFailedMessage = annotations.get(ENI_CREATE_FAILED);
            if (!StringUtils.isEmpty(eniCreateFailedMessage)) {
                return eniCreateFailedMessage;
            }
            return null;
        }

        public void setPodCondition(List<PodCondition> conditions, String conditionType, String status, String reason,
                                    String message) {
            for (PodCondition condition : conditions) {
                if (condition.getType().equals(conditionType)) {
                    condition.setStatus(status);
                    condition.setReason(reason);
                    condition.setMessage(message);
                    condition.setLastTransitionTime(new Timestamp(new Date().getTime()));
                }
            }
        }

        public void setPodConditions(List<PodCondition> conditions, String message) {
            // 设置Ready和ContainersReady的status为False,message设置相应语句
            for (int j = 0; j < conditions.size(); j++) {
                if (conditions.get(j).getType().equals("Ready")) {
                    conditions.get(j).setStatus("False");
                    conditions.get(j).setReason("ContainersNotReady");
                    conditions.get(j).setMessage(message);
                    conditions.get(j).setLastTransitionTime(new Timestamp(new Date().getTime()));
                } else if (conditions.get(j).getType().equals("ContainersReady")) {
                    conditions.get(j).setStatus("False");
                    conditions.get(j).setReason("ContainersNotReady");
                    conditions.get(j).setMessage(message);
                    conditions.get(j).setLastTransitionTime(new Timestamp(new Date().getTime()));
                } else if (conditions.get(j).getType().equals("Initialized")) {
                    conditions.get(j).setStatus("False");
                    conditions.get(j).setReason("ContainersNotReady");
                    conditions.get(j).setMessage(message);
                    conditions.get(j).setLastTransitionTime(new Timestamp(new Date().getTime()));
                }
            }

        }

        private void chargeStatus(String podPhase, PodPO podPO, Timestamp statusUpdateTime,
                                  long podNewResourceVersion) {
            if (podPO.getStatus().equalsIgnoreCase(podPhase)) {
                return;
            }
            String currentChargeStatus = ChargeStatus.getStatus(podPhase);
            if (podPO.getCpt1()) {
                currentChargeStatus = PodUtils.getChargeStatusOfCpt1Instance(
                    podPhase, podPO.getDelayReleaseDurationMinute(), podPO.isDelayReleaseSucceeded(), 
                    podPO.getResourceRecycleTimestamp(), podPO.getResourceRecycleComplete(), 
                    podPO.getResourceRecycleReason());
            }

            PodChargeStatus podChargeStatus = new PodChargeStatus();
            podChargeStatus.setPodUuid(podPO.getPodUuid());
            podChargeStatus.setPreviousState(podPO.getStatus());
            podChargeStatus.setCurrentState(podPhase);
            podChargeStatus.setChargeState(currentChargeStatus);
            podChargeStatus.setResourceVersion(podNewResourceVersion);
            podChargeStatus.setCpt1SyncState(podPO.getIntCpt1Mode());
            podChargeStatus.setCreatedTime(statusUpdateTime);
            podChargeStatus.setUpdateTime(statusUpdateTime);
            // todo 虽然设置了时间，但是sql里是current time，后面改计费的时候再看。。。
            podChargeStatusDao.insert(podChargeStatus);
            if (podPO.getCpt1()) {
                billingResourceSyncManager.doSync(podPO, podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                        podPO.getPodUuid(), podNewResourceVersion, currentChargeStatus, 0);
                LOGGER.debug("[call billing] finish insert billing sync task in chargeStatus1, id {}, account {}, pod {}, resource version {}, " +
                                "current status {}", podChargeStatus.getId(), podPO.getRealChargeAccountId(),
                        podPO.getPodUuid(), podNewResourceVersion, currentChargeStatus);
            }
        }

        public List<ContainerPO> syncContainer(PodPO podPO, V1Pod pod, PodEventType podEventType) {
            String podId = podPO.getPodId();
            String podStatus = podPO.getStatus();
            List<ContainerPO> containers = containerDao.listByPodId(podId);
            if (CollectionUtils.isEmpty(containers)) {
                containers = containerDao.listByPodId(podPO.getPodUuid());
            }
            if (CollectionUtils.isEmpty(containers)) {
                LOGGER.debug("syncContainer not found container by podId:{} and podUuid:{}", podPO.getPodId(),
                        podPO.getPodUuid());
                return containers;
            }

            // 记录内置的第三方镜像下载容器的状态
            boolean hasUserInitContainer = false;
            boolean allInitContainersSuccessed = true;
            ImagePullStatus imagePullStatus = ImagePullStatus.PULL_SUCCESS;
            String containerNamePullingImageFailed = "";

            Map<String, V1ContainerStatus> containerMap = new HashMap<>();
            // initContainer
            if (pod.getStatus() != null && pod.getStatus().getInitContainerStatuses() != null) {
                for (V1ContainerStatus containerStatus : pod.getStatus().getInitContainerStatuses()) {
                    String containerName = containerStatus.getName();
                    containerMap.put(containerName, containerStatus);
                    if (!isSuccessed(containerStatus.getState())) {
                        allInitContainersSuccessed = false;
                    }
                    // 获取镜像下载状态
                    if (containerName.startsWith(PodConstants.BCI_IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX)) {
                        V1ContainerState state = containerStatus.getState();
                        V1ContainerState lastState = containerStatus.getLastState();
                        if (state != null && state.getTerminated() != null) {
                            V1ContainerStateTerminated terminated = state.getTerminated();
                            if (terminated.getExitCode() == null || terminated.getExitCode() == 0) {
                                // 镜像下载成功
                                continue;
                            } else {
                                // 镜像下载失败
                                imagePullStatus = ImagePullStatus.PULL_ERROR;
                                containerNamePullingImageFailed = getFailedContainerName(containerName,
                                        pod.getMetadata().getAnnotations());
                            }
                        } else {
                            // 根据当前 state 无法判定状态，只能看一下 laststate
                            if (lastState != null && lastState.getTerminated() != null) {
                                V1ContainerStateTerminated terminated = lastState.getTerminated();
                                if (terminated.getExitCode() != null && terminated.getExitCode() != 0) {
                                    // 镜像下载失败
                                    imagePullStatus = ImagePullStatus.PULL_ERROR;
                                    containerNamePullingImageFailed = getFailedContainerName(containerName,
                                            pod.getMetadata().getAnnotations());
                                } else {
                                    imagePullStatus = ImagePullStatus.PULLING;
                                }
                            } else {
                                imagePullStatus = ImagePullStatus.PULLING;
                            }
                        }
                    } else {
                        hasUserInitContainer = true;
                    }
                }
            }
            // workload container
            SidecarStatus sidecarStatus = SidecarStatus.INITIALIZE_SUCCESS;
            if (pod.getStatus() != null && pod.getStatus().getContainerStatuses() != null) {
                for (V1ContainerStatus containerStatus : pod.getStatus().getContainerStatuses()) {
                    String contName = containerStatus.getName();
                    containerMap.put(contName, containerStatus);
                    // 计算 sidecar 执行情况
                    if (org.apache.commons.lang.StringUtils.equals(contName, PodConstants.BCI_POST_START_SIDECAR)) {
                        // waiting 状态，说明 sidecar 还在执行
                        V1ContainerState state = containerStatus.getState();
                        if (state == null || state.getWaiting() != null) {
                            sidecarStatus = SidecarStatus.INITIALIZING;
                        }
                    }
                }
            }
            if (hasFailedSidecar(pod)) {
                sidecarStatus = SidecarStatus.INITIALIZE_FAILED;
            }

            // eni是否创建失败
            String eniCreateFailedMessage = getEniCreateFailedMessage(pod);

            String podIgnoreExitCodeContainers = PodUtils.getPodAnnotationValueByKey(pod, PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY);
            List<String> podIgnoreExitCodeContainersList = new ArrayList<>();
            if (!StringUtils.isEmpty(podIgnoreExitCodeContainers)) {
                podIgnoreExitCodeContainersList = Arrays.asList(podIgnoreExitCodeContainers.split(","));
            }

            for (ContainerPO container : containers) {
                try {
                    String containerName = container.getName();
                    container.setPodUuid(pod.getMetadata().getUid());
                    if (containerMap.containsKey(containerName)) {
                        V1ContainerStatus containerStatus = containerMap.get(containerName);
                        container.setRestartCount(containerStatus.getRestartCount());
                        if (podEventType ==
                                PodEventType.DELETE && podIgnoreExitCodeContainersList.contains(containerName)) {
                            // 什么也不做,Pod删除时,不更新忽略退出码的container的currentState
                        } else {
                            V1ContainerState optimizedState = optimizeContainerState(imagePullStatus, hasUserInitContainer,
                                    containerNamePullingImageFailed, allInitContainersSuccessed, sidecarStatus,
                                    containerStatus.getState(), container.getName(), eniCreateFailedMessage);
                            ContainerCurrentState currentState = getContainerCurrentState(optimizedState);
                            boolean processPodIgnoreExitCodeContainers = processPodIgnoreExitCodeContainers(podPO, container, pod, currentState);
                            LOGGER.debug("syncContainer processPodIgnoreExitCodeContainers podId:{} podStatus:{} container:{} " +
                                            "result:{}",
                                    podId, podStatus, containerName, processPodIgnoreExitCodeContainers);
                            container.setCurrentState(JsonUtil.toJSON(
                                    PodUtils.convertBean(currentState, ContainerCurrentState.class)));
                        }
                        ContainerPreviousState previousStat = getContainerPreviousState(containerStatus.getLastState());
                        container.setPreviousState(JsonUtil.toJSON(
                                PodUtils.convertBean(previousStat, ContainerPreviousState.class)));

                        if (containerStatus.getReady() != null) {
                            container.setReady(containerStatus.getReady().toString());
                        }
                        if (containerStatus.getStarted() != null) {
                            container.setStarted(containerStatus.getStarted().toString());
                        }
                        if (StringUtils.isEmpty(containerMap.get(container.getName()).getContainerID())) {
                            container.setContainerUuid("");
                        } else {
                            container.setContainerUuid(containerMap.get(container.getName()).getContainerID());
                        }
                        if (StringUtils.isEmpty(containerMap.get(container.getName()).getImageID())) {
                            container.setImageID("");
                        } else {
                            container.setImageID(containerMap.get(container.getName()).getImageID());
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("sync pod {} container {} error:{}", podPO.getPodId(), container.getName(), e);
                }
            }
            return containers;
        }

        public boolean processPodIgnoreExitCodeContainers(PodPO podPO, ContainerPO containerPO, V1Pod pod,
                                                          ContainerCurrentState currentState) {
            // restartPolicy != Alawys && podStatus处于终态(Failed or Succeeded) && 容器被用户设置为忽略退出状态 &&
            // 容器当前的currentState != terminated状态
//                        processPodIgnoreExitCodeContainers();
//                        设置容器currentState为:
//                            state:
//                              terminated:
//                                exitCode: 0
//                                finishedAt: "2025-02-12T09:10:39Z"
//                                reason: Completed
//                                message: Force this container to be success(137, Error, )
//                                startedAt: "2025-02-12T09:09:39Z"

            String podId = podPO.getPodId();
            String containerName = containerPO.getName();

            String podIgnoreExitCodeContainers = PodUtils.getPodAnnotationValueByKey(pod, PodConstants.BCI_IGNORE_EXIT_CODE_CONTAINERS_ANNOTATION_KEY);
            List<String> podIgnoreExitCodeContainersList = new ArrayList<>();
            if (!StringUtils.isEmpty(podIgnoreExitCodeContainers)) {
                podIgnoreExitCodeContainersList = Arrays.asList(podIgnoreExitCodeContainers.split(","));
            }

            if (!podIgnoreExitCodeContainersList.contains(containerName)) {
                LOGGER.debug("processPodIgnoreExitCodeContainers podId:{} container:{} not in ignore list:{}",
                        podId, containerName, podIgnoreExitCodeContainers);
                return false;
            }

            // 判断重启策略, restartPolicy == Alawys, 不做处理
            if (podPO.getRestartPolicy().equalsIgnoreCase(PodRestartPolicyConstant.ALWAYS)) {
                LOGGER.debug("processPodIgnoreExitCodeContainers podId:{} restartPolicy is always", podId);
                return false;
            }

            // 判断容器当前状态(是否处于终态),非终态不做处理
            if (!checkPodIsFinalStatus(podPO.getStatus())) {
                LOGGER.debug("processPodIgnoreExitCodeContainers podId:{} podStatus:{} not in final status",
                        podId, podPO.getStatus());
                return false;
            }

            // 判断容器的currentState状态,如果已经处于terminated状态,则不做处理
            if (currentState.getState().equals(ContainerStatus.CONTAINER_STATE_SUCCEEDED) ||
                    currentState.getState().equals(ContainerStatus.CONTAINER_STATE_FAILED)) {
                LOGGER.debug("processPodIgnoreExitCodeContainers podId:{} container:{} currentState:{} is terminated",
                        podId, containerName, currentState.getState());
                return false;
            }

            currentState.setState(ContainerStatus.CONTAINER_STATE_SUCCEEDED);
            currentState.setExitCode(0);
            currentState.setContainerFinishTime(new Date());
            String detailStatus = BCI_IGNORE_EXIT_CODE_CONTAINER_REASON;
            detailStatus += reasonAndMessageDelimiter;
            detailStatus += BCI_IGNORE_EXIT_CODE_CONTAINER_MESSAGE;
            currentState.setDetailStatus(detailStatus);
            return true;
        }

        public V1ContainerState optimizeContainerState(ImagePullStatus imagePullStatus, boolean hasUserInitContainer,
                                                       String containerNamePullingImageFailed,
                                                       boolean allInitContainersSuccessed,
                                                       SidecarStatus sidecarStatus, V1ContainerState currentState,
                                                       String containerName,
                                                       String eniCreateFailedMessage) {
            // 不对 running 和 terminated 的容器做状态优化
            if (currentState != null && (currentState.getRunning() != null || currentState.getTerminated() != null)) {
                return currentState;
            }

            // pod 创建eni 失败
            if (eniCreateFailedMessage != null) {
                return new V1ContainerState().waiting(new V1ContainerStateWaiting().reason(
                        PodConstants.POD_ALLOCATE_IP_ERROR).message(eniCreateFailedMessage));
            }
            // 当前容器拉取镜像失败了，容器状态变为 failed to pull image
            if (imagePullStatus == ImagePullStatus.PULL_ERROR
                    && org.apache.commons.lang.StringUtils.equals(containerNamePullingImageFailed, containerName)) {
                return new V1ContainerState().waiting(new V1ContainerStateWaiting().reason(
                        PodConstants.CONTAINER_PULL_IMAGE_ERROR).message("failed to pull image"));
            }
            // 用户没有init容器，内置镜像下载容器还未成功结束，容器状态都是 pulling image
            if ((imagePullStatus == ImagePullStatus.PULLING || imagePullStatus == ImagePullStatus.PULL_ERROR)
                    && !hasUserInitContainer) {
                return new V1ContainerState().waiting(new V1ContainerStateWaiting().reason(
                        PodConstants.CONTAINER_PULLING_IMAGE).message("pulling image"));
            }
            // 所有init容器都执行成功了，才可以优化内置sidecar错误信息。因为 init 容器成功结束以前，不可能开始执行内置sidecar。
            if (allInitContainersSuccessed) {
                if (sidecarStatus == SidecarStatus.INITIALIZE_FAILED) {
                    return new V1ContainerState().waiting(new V1ContainerStateWaiting().reason("InternalError")
                            .message("bci internal component init failed"));
                }
                if (sidecarStatus == SidecarStatus.INITIALIZING) {
                    return new V1ContainerState().waiting(new V1ContainerStateWaiting().reason("ContainerCreating")
                            .message("bci internal component initializing"));
                }
            }
            // container state 无需变化
            return currentState;
        }

        public String getFailedContainerName(String imageDownloadContainerName, Map<String, String> annotations) {
            if (annotations.containsKey(imageDownloadContainerName)) {
                return annotations.get(imageDownloadContainerName);
            }
            if (imageDownloadContainerName.startsWith(IMAGE_DOWNLOAD_WORKLOAD_CONTAINER_PREFIX)) {
                return imageDownloadContainerName.substring(IMAGE_DOWNLOAD_WORKLOAD_CONTAINER_PREFIX.length());
            }
            if (imageDownloadContainerName.startsWith(IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX)) {
                return imageDownloadContainerName.substring(IMAGE_DOWNLOAD_INIT_CONTAINER_PREFIX.length());
            }
            return "";
        }

        public boolean isFailed(V1ContainerState state) {
            if (state != null && state.getTerminated() != null && state.getTerminated().getExitCode() != null) {
                return state.getTerminated().getExitCode() != 0;
            }
            return false;
        }

        public boolean isSuccessed(V1ContainerState state) {
            if (state != null && state.getTerminated() != null && state.getTerminated().getExitCode() != null) {
                return state.getTerminated().getExitCode() == 0;
            }
            return false;
        }

        /**
         * 转换规则参考v1.0：
         * https://console.cloud.baidu-int.com/devops/icode/repos/baidu/serverless-k8s/serverless-virtual
         * -kubelet
         * /blob/master/pkg/novaclient/status.go#L234
         */
        public ContainerCurrentState getContainerCurrentState(V1ContainerState containerState) {
            ContainerCurrentState currentState = new ContainerCurrentState();

            if (containerState == null) {
                currentState.setState(ContainerStatus.CONTAINER_STATE_CREATING);
                currentState.setDetailStatus("ContainerCreating");
                return currentState;
            }

            if (containerState.getRunning() != null) {
                V1ContainerStateRunning running = containerState.getRunning();
                currentState.setContainerStartTime(Date.from(running.getStartedAt().toInstant()));
                currentState.setState(ContainerStatus.CONTAINER_STATE_RUNNING);
            }
            // 需要考虑Pod 被标记忽略退出码的情况,对于这种Pod,删除时不能更新容器的containerState状态
            if (containerState.getTerminated() != null) {
                V1ContainerStateTerminated terminated = containerState.getTerminated();

                String state = ContainerStatus.CONTAINER_STATE_SUCCEEDED;
                if (terminated.getExitCode() != 0) {
                    state = ContainerStatus.CONTAINER_STATE_FAILED;
                }
                currentState.setState(state);
                currentState.setContainerStartTime(Date.from(terminated.getStartedAt().toInstant()));
                currentState.setContainerFinishTime(Date.from(terminated.getFinishedAt().toInstant()));
                currentState.setExitCode(terminated.getExitCode());

                String detailStatus = "";
                if (terminated.getReason() != null) {
                    detailStatus += terminated.getReason();
                }
                if (terminated.getMessage() != null) {
                    detailStatus += reasonAndMessageDelimiter + terminated.getMessage();
                }
                if (detailStatus.startsWith(reasonAndMessageDelimiter)) {
                    detailStatus = detailStatus.substring(reasonAndMessageDelimiter.length());
                }
                currentState.setDetailStatus(detailStatus);
            }
            if (containerState.getWaiting() != null) {
                V1ContainerStateWaiting waiting = containerState.getWaiting();

                String detailStatus = "";
                if (waiting.getReason() != null) {
                    detailStatus += waiting.getReason();
                }
                if (waiting.getMessage() != null) {
                    detailStatus += reasonAndMessageDelimiter + waiting.getMessage();
                }
                if (detailStatus.startsWith(reasonAndMessageDelimiter)) {
                    detailStatus = detailStatus.substring(reasonAndMessageDelimiter.length());
                }
                currentState.setDetailStatus(detailStatus);
                currentState.setState(ContainerStatus.CONTAINER_STATE_CREATING);
            }
            return currentState;
        }

        public ContainerPreviousState getContainerPreviousState(V1ContainerState containerState) {
            ContainerPreviousState previousState = new ContainerPreviousState();
            // translate container previous status, only consider Terminated
            if (containerState.getTerminated() != null) {
                V1ContainerStateTerminated terminated = containerState.getTerminated();

                String state = ContainerStatus.CONTAINER_STATE_SUCCEEDED;
                if (terminated.getExitCode() != 0) {
                    state = ContainerStatus.CONTAINER_STATE_FAILED;
                }
                previousState.setState(state);
                previousState.setContainerStartTime(Date.from(terminated.getStartedAt().toInstant()));
                previousState.setContainerFinishTime(Date.from(terminated.getFinishedAt().toInstant()));
                previousState.setExitCode(terminated.getExitCode());
                String detailStatus = "";
                if (terminated.getReason() != null) {
                    detailStatus += terminated.getReason();
                }
                if (terminated.getMessage() != null) {
                    detailStatus += reasonAndMessageDelimiter + terminated.getMessage();
                }
                if (detailStatus.startsWith(reasonAndMessageDelimiter)) {
                    detailStatus = detailStatus.substring(reasonAndMessageDelimiter.length());
                }
                previousState.setDetailStatus(detailStatus);

            }
            return previousState;
        }
    }

    public class SyncDeletePod implements Runnable {
        private StateMachine stateMachine;
        private V1Pod pod;

        public SyncDeletePod() {
        }

        public SyncDeletePod(V1Pod pod, StateMachine stateMachine) {
            this.pod = pod;
            this.stateMachine = stateMachine;
        }

        @Override
        public void run() {
            BceInternalRequest.setThreadRequestId(PodUtils.createLongUuid());
            boolean retry = true;
            String podName = pod.getMetadata().getName();
            try {
                retry = updateDeletePodContainer(pod);
            } catch (Exception e) {
                LOGGER.error("failed to sync delete of pod {}, exception: {}", podName, e);
            } finally {
                if (retry) {
                    if (deleteRequeueRecord.containsKey(podName) &&
                            deleteRequeueRecord.get(podName) > podDeleteEventSyncRetryLimit) {
                        LOGGER.warn("pod {} deleteRequeueRecord reach max limit,cancel it",
                                podName);
                        deleteRequeueRecord.remove(podName);
                    } else {
                        deleteWorkQueue.addAfter(pod, Duration.ofSeconds(podDeleteEventRequeueDelayTimeSecond));
                        if (deleteRequeueRecord.containsKey(podName)) {
                            deleteRequeueRecord.put(podName,
                                    deleteRequeueRecord.get(podName) + 1);
                        } else {
                            deleteRequeueRecord.put(podName, 1);
                        }
                    }
                } else {
                    if (deleteRequeueRecord.containsKey(podName)) {
                        deleteRequeueRecord.remove(podName);
                    }
                }
            }
        }

        /**
         * @Description 更新删除Pod容器事件，包括用户主动删除和K8S集群异常删除
         * @param pod V1Pod类型，表示要处理的Pod对象
         * @return boolean 返回true表示需要重试，false表示不需要重试
         * @throws Exception 可能出现的异常，由调用方处理
         */
        public boolean updateDeletePodContainer(V1Pod pod) {
            boolean retry = false;
            PodEventType podEventType = PodEventType.DELETE;
            if (pod == null || pod.getMetadata() == null || pod.getStatus() == null) {
                return false;
            }
            try {
                String podId = pod.getMetadata().getName();
                String namespace = pod.getMetadata().getNamespace();
                String userId = namespace;
                long podNewResourceVersion = Long.parseLong(pod.getMetadata().getResourceVersion());
                PodPO podPO = podDao.getPodByIdIgnoreStatus(userId, podId);
                if (podPO == null) {
                    LOGGER.error("not find podPO ignore by podid:{} podEvent:{} newResourceVersion:{}",
                            podId, podEventType, podNewResourceVersion);
                    return false; // 返回false，不重试
                }

                // 如果pod已经被删除，不再处理
                if (podPO.getDeleted() == PodConstants.POD_DELETED) {
                    return false; // 返回false，不重试
                }

                long podOldResourceVersion = podPO.getResourceVersion();
                if (podNewResourceVersion <= podOldResourceVersion) {
                    return false;
                }
                Timestamp podDefaultCommitDeletedTime = Timestamp.valueOf("1971-01-01 08:00:01");
                Timestamp podCommitDeletedmestamp = podPO.getCommitDeletedTime();
                // 判断Pod是否被用户主动删除
                // 如果podCommitDeletedmestamp不等于podDefaultCommitDeletedTime，说明Pod是被用户主动删除
                if (!podCommitDeletedmestamp.equals(podDefaultCommitDeletedTime)) {
                    // Pod被用户主动删除，返回false
                    k8sPodDeletedByUser(podPO, podNewResourceVersion);
                    return false;
                } else {
                    // Pod被底层K8S集群异常删除
                    String podNextStatus = getPodNextStatusWhenK8SPodDeletePod(
                            podPO, podEventType, K8SDeletePodType.K8S_DELETE_POD_UNEXPECTEDLY);
                    // 更新一下container status
                    List<ContainerPO> containerPOS = new SyncPod().syncContainer(podPO, pod, podEventType);
                    if (!CollectionUtils.isEmpty(containerPOS)) {
                        containerDao.batchUpdate(containerPOS);
                    }
                    // 处理被controller 主动删除pod 情况
                    if (setControllerAutoDeleteRecycleReasonIfPossible(pod.getMetadata().getAnnotations(),podPO)) {
                        // controller 主动删除pod，pod phase置为failed，停止计费
                        // controller 只会删除Running + Pending(成功调度到node上的pod)
                        LOGGER.debug("pod {} controllerAutoDeletePod set podPhase Failed", pod.getMetadata().getName());
                        podNextStatus = BciStatus.FAILED.getStatus();
                    }
                    Timestamp statusUpdateTime = new Timestamp(new Date().getTime());
                    // 更改计费状态，停止计费
                    LOGGER.debug("[call billing] chargeStatus in updateDeletePodContainer for pod {}", podPO.getPodUuid());
                    new SyncPod().chargeStatus(podNextStatus, podPO, statusUpdateTime, podNewResourceVersion);
                    k8sPodDeletedByK8SUnexpectedly(podPO, podNewResourceVersion, podNextStatus);
                }
            } catch (Exception e) {
                // 这里加argus报警
                LOGGER.error("updateDeletePodContainer failed to react with delete pod:{} exception detail:{}",
                        pod.getMetadata().getName(), e);
                return true;
            }
            return retry;
        }

        private boolean k8sPodDeletedByUser(PodPO podPO, long newResourceVersion) {
            String podId = podPO.getPodId();
            String userId = podPO.getUserId();
            long podOldResourceVersion = podPO.getResourceVersion();
            long podNewResourceVersion = newResourceVersion;
            String podCurrentStatus = podPO.getStatus();
            LOGGER.info("k8sPodDeletedByUser pod:{} was deleted by user:{} ignore sync pod and container status"
                            + " oldResourceVersion:{}, newResourceVersion:{} podCurrentStatus:{}",
                    podId, userId,
                    podOldResourceVersion, podNewResourceVersion, podCurrentStatus);
            StateMachineEvent event = StateMachineEvent.K8S_POD_DELETED_BY_USER;
            K8SPodDeletedByUserContext eventContext = new K8SPodDeletedByUserContext(podId, userId, podNewResourceVersion);
            if (!stateMachine.trigger(podPO, event, eventContext)) {
                LOGGER.warn("k8sPodDeletedByUser: pod state machine trigger failed, podId:{} event:{}",
                        podId, event.toString());
                return false;
            }
            LOGGER.info("k8sPodDeletedByUser: pod state machine trigger success, podId:{} event:{}",
                    podId, event.toString());
            return true;
        }

        private boolean k8sPodDeletedByK8SUnexpectedly(PodPO podPO, long newResourceVersion, String podNextStatus) {
            String podId = podPO.getPodId();
            String userId = podPO.getUserId();
            long podOldResourceVersion = podPO.getResourceVersion();
            long podNewResourceVersion = newResourceVersion;
            String podCurrentStatus = podPO.getStatus();

            StateMachineEvent event = StateMachineEvent.K8S_POD_DELETED_BY_K8S_UNEXPECTEDLY;
            K8SPodDeletedByK8SUnexpectedlyContext eventContext = new K8SPodDeletedByK8SUnexpectedlyContext(podId, userId,
                    podNewResourceVersion, podNextStatus);
            if (!stateMachine.trigger(podPO, event, eventContext)) {
                LOGGER.warn("k8sPodDeletedByK8SUnexpectedly: pod state machine trigger failed, podId:{} event:{}",
                        podId, event.toString());
                return false;
            }
            return true;
        }
    }

    public enum ImagePullStatus {
        PULLING,
        PULL_SUCCESS,
        PULL_ERROR
    }

    public enum SidecarStatus {
        INITIALIZING,
        INITIALIZE_SUCCESS,
        INITIALIZE_FAILED
    }
}

