package com.baidu.bce.logic.bci.servicev2.webshell;

import com.baidu.bce.logic.bci.daov2.webshell.model.WebshellPO;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sCluster;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.util.WebSocketStreamHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.socket.AbstractWebSocketMessage;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.PingMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * WebShellServerHandler 此类为单例，全局一个
 */
public class WebShellServerHandler extends AbstractWebSocketHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebShellServerHandler.class);

    private ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

    private boolean removeTimeoutSessionStarted = false;

    private ConcurrentHashMap<String, WebShellSession> webSocketMap = new ConcurrentHashMap<>();


    @Autowired
    private WebshellService webshellService;

    @Autowired
    private K8sService k8sService;


    @Value("${token.expire.second:60}")
    private int tokenExpireSecond;

    @Value("${session.expire.second:60}")
    private int sessionExpireSecond;


    /**
     * 关闭无效token
     *
     * @param session
     * @param token
     */
    private void closeInvalidSession(WebSocketSession session, String token) {
        try {
            session.close();
        } catch (IOException e) {
            LOGGER.error("WebShellServer closeInvalidSession token {} err {} ", token, e);
        }
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String token = (String) session.getAttributes().get("token");
        if (token == null) {
            LOGGER.info("WebShellServer session {} accept exec request token null ", session.getId());
            closeInvalidSession(session, token);
            return;
        }
        LOGGER.info("WebShellServer accept exec request token {}", token);

        final WebshellPO webShell = webshellService.queryByToken(token);
        if (webShell == null) {
            // 无效token
            LOGGER.error("WebShellServer accept exec request token {} not found ", token);
            closeInvalidSession(session, token);
            return;
        }
        // 校验token是否过期
        Timestamp createdTime = webShell.getCreatedTime();
        int costTime = (int) (System.currentTimeMillis() - createdTime.getTime()) / 1000;
        if (costTime > tokenExpireSecond) {
            LOGGER.error("WebShellServer accept exec request token {} Expired ", token);
            closeInvalidSession(session, token);
            return;
        }

        // 锁token
        int lockCount = webshellService.lockWebShellToken(token, webShell.getVersion());
        if (lockCount == 0) {
            LOGGER.error("WebShellServer accept exec request token {} lock count =0 ", token);
            closeInvalidSession(session, token);
            return;
        }

        K8sCluster cluster = null;

        try {
            cluster = k8sService.getClusterByUserId(webShell.getUserId());
        } catch (Exception e) {
            LOGGER.error("WebShellServer accept exec request token {} get k8s cluster err {} ", token, e);
            closeInvalidSession(session, token);
            return;
        }

        final WebShellSession bciSession = new WebShellSession();
        bciSession.token = token;
        bciSession.session = session;
        final ApiClient apiClient = cluster.getApiClient();

        bciSession.handler = new WebSocketStreamHandler() {
            @Override
            protected void handleMessage(int stream, InputStream inStream) throws IOException {
                if (stream == (int) WebShellStreamType.ConnectionErrorStream.getType()) {
                    int exitCode = WebShellUtil.parseExitCode(apiClient, inStream);
                    // k8s 进程退出，会进入此逻辑，关闭与前端的连接
                    if (exitCode != 0) {
                        LOGGER.info("WebShellServer handleMessage token {} exitCode {} close session ",
                                bciSession.token, exitCode);
                        // 向前端发送错误信息，退出码
                        try {
//                            if (!bciSession.isShOrBashCommand()) {
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder.append("command terminated with exit code ").append(exitCode);
                            bciSession.sendMessageToFront((int) WebShellStreamType.ErrorStream.getType(),
                                    stringBuilder.toString().getBytes());
//                            }
                        } catch (Exception e) {
                            // ignore
                        }
                    }

                    inStream.close();
                    // Stream ID of `3` delivers the status of exec connection from
                    // kubelet,
                    // closing the connection upon 0 exit-code.
                    this.close();
                    return;
                }
                int available = inStream.available();
                byte[] bytes = new byte[available];
                inStream.read(bytes);
                if (available > 0) {
                    LOGGER.info("WebShellServer handleMessage token {} stream {} message {} ", bciSession.token,
                            stream, new String(bytes));
                    try {
                        bciSession.sendMessageToFront(stream, bytes);
                    } catch (IOException e) {
                        LOGGER.error("WebShellServer token {} send message err {} ",
                                bciSession.token, e);
                        this.close();
                    }
                }
            }


            @Override
            public void failure(Throwable t) {
                LOGGER.error("WebShellServer token {} failure event {} ", bciSession.token, t);
                super.failure(t);
                this.close();
            }

            @Override
            public void close() {
                if (!bciSession.closed) {
                    bciSession.closeSession(bciSession.session, bciSession.token);
                }
                try {
                    super.close();
                } catch (Exception e) {
                    LOGGER.error("WebShellServer token {} super close err {}", bciSession.token, e);
                }
            }
        };

        // 与k8s master 建立websocket 连接
        bciSession.exec = new WebShellExec(webShell, bciSession.handler);
        try {
            bciSession.exec.exec(apiClient);
        } catch (Exception e) {
            LOGGER.error("WebShellServer token {} exec err {} ", token, e);
            closeInvalidSession(session, token);
            return;
        }
        webSocketMap.put(session.getId(), bciSession);
        // 建立连接成功后，更新下心跳时间戳
        bciSession.frontLastMessageTime = System.currentTimeMillis();

        // 开启定时清理无用websocket 定时任务
        if (removeTimeoutSessionStarted) {
            return;
        }
        synchronized (WebShellServerHandler.class) {
            if (removeTimeoutSessionStarted) {
                return;
            }
            scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
                @Override
                public void run() {
                    try {
                        closeTimeoutSessions();
                    } catch (Exception e) {
                        LOGGER.error("WebShellServer closeTimeoutSessions task err {} ", e);
                    }
                }
            }, 10, 5, TimeUnit.SECONDS);
            removeTimeoutSessionStarted = true;
        }
    }


    private AbstractWebSocketMessage parseFrontMessage(byte[] payload) {
        AbstractWebSocketMessage message = null;
        try {
            if (payload.length == 0) {
                return null;
            }

            byte stream = payload[0];
            if (!WebShellStreamType.hasMessageType(stream)) {
                LOGGER.error("webshell decode messageType {} not support ", stream);
                return null;
            }
            // ping 消息
            if (payload.length == 1 && WebShellStreamType.Ping.getType() == stream) {
                message = new PingMessage();
            } else if (payload.length > 1 && (WebShellStreamType.OutputStream.getType() == stream
                    || WebShellStreamType.ResizeStream.getType() == stream)) {
                message = new BinaryMessage(payload);
            }
        } catch (Exception e) {
            LOGGER.error("webshell decode data {} err {}", Arrays.toString(payload), e);
        }
        return message;
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws Exception {
        WebShellSession bciSession = webSocketMap.get(session.getId());
        if (bciSession == null) {
            LOGGER.error("WebShellServer session {} not found in webSocketMap ", session.getId());
            closeInvalidSession(session, "");
            return;
        }
        byte[] payload = message.getPayload().array();
        AbstractWebSocketMessage webSocketMessage = parseFrontMessage(payload);
        if (webSocketMessage == null) {
            LOGGER.error("WebShellServer token {} send message {} parse null, close session ", bciSession.token,
                    message.getPayload());
            bciSession.handler.close();
            return;
        }
        bciSession.frontLastMessageTime = System.currentTimeMillis();
        if (webSocketMessage instanceof PingMessage) {
            LOGGER.info("WebShellServer accept token {} ping ", bciSession.token);
            // 收到前端ping 请求，直接向前端返回pong响应
            // k8s client 初始化会默认1m 向k8s 发起一次ping 请求，此处不必在向k8s ping
            try {
                bciSession.sendPongToFront();
            } catch (IOException e) {
                LOGGER.error("WebShellServer token {} send pong err {} ", bciSession.token, e);
                // 关闭前端和后端k8s 连接
                bciSession.handler.close();
            }
            return;
        }
        if (webSocketMessage instanceof BinaryMessage) {
            byte stream = payload[0];
            byte[] streamPayload = new byte[payload.length - 1];
            System.arraycopy(payload, 1, streamPayload, 0, streamPayload.length);
            LOGGER.info("WebShellServer accept token {} stream {} request {} ", bciSession.token, stream,
                    new String(streamPayload));
            try {
                if (stream == WebShellStreamType.ResizeStream.getType()) {
                    // resize stream
                    bciSession.exec.getResizeStream().write(streamPayload);
                    bciSession.exec.getResizeStream().flush();
                } else if (stream == WebShellStreamType.OutputStream.getType()) {
                    // output stream
                    bciSession.exec.getOutputStream().write(streamPayload);
                    bciSession.exec.getOutputStream().flush();
                }
            } catch (IOException e) {
                LOGGER.error("WebShellServer token {} send payload {} err {} ", bciSession.token, payload, e);
                // 关闭前端和后端k8s 连接
                bciSession.handler.close();
            }
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        WebShellSession bciSession = webSocketMap.get(session.getId());
        if (bciSession == null) {
            LOGGER.error("WebShellServer session {} not found in webSocketMap ", session.getId());
            closeInvalidSession(session, "");
            return;
        }
        // 传递参数失败，decode 失败
        LOGGER.error("WebShellServer handleTransportError {} ", exception);
        // 前端关闭连接，关闭与k8s的连接
        bciSession.exitK8sShProcess();
        bciSession.handler.close();
        webSocketMap.remove(session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        WebShellSession bciSession = webSocketMap.get(session.getId());
        if (bciSession == null) {
            LOGGER.error("WebShellServer session {} not found in webSocketMap ", session.getId());
            closeInvalidSession(session, "");
            return;
        }
        LOGGER.info("WebShellServer token {} close ", bciSession.token);
        // 前端关闭连接，关闭与k8s的连接
        bciSession.exitK8sShProcess();
        bciSession.handler.close();
        webSocketMap.remove(session.getId());
    }


    /**
     * 清理心跳超时的session
     */
    private void closeTimeoutSessions() {
        Iterator<Map.Entry<String, WebShellSession>> iterator = webSocketMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, WebShellSession> entity = iterator.next();
            WebShellSession webShell = entity.getValue();
            int costTime = (int) (System.currentTimeMillis() - webShell.frontLastMessageTime) / 1000;
            if (costTime <= sessionExpireSecond) {
                continue;
            }
            LOGGER.info("WebShellServer close idle token {} ", webShell.token);
            // 超过60s 没有发心跳，关闭socket连接
            webShell.exitK8sShProcess();
            webShell.handler.close();
            iterator.remove();
        }
    }
}
