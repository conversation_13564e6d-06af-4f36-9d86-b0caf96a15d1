package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class TidalTime {

    private int currentYear;
    private int currentMonth;
    private int currentDay;
    private int currentHour;
    private int currentMinute;

    private String tidalStartSubmitTime;
    private String tidalEndSubmitTime;
    private String tidalStartGCTime;

    // 调休
    private List<String> timeOffDayList;
    // 节假日
    private List<String> holidayDayList;

    public String printCurrentDate() {
        return String.format("currentTime : %d-%d-%d %d:%d tidalStartSubmitTime:%s , tidalEndSubmitTime:%s ",
                currentYear,
                currentMonth, currentDay,
                currentHour, currentMinute, tidalStartSubmitTime, tidalEndSubmitTime);
    }
}
