package com.baidu.bce.logic.bci.servicev2.model;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class BidOption {
    private String bidModel; // 抢占实例出价模型， 市场价: "MARKET_PRICE_BID" 自定义："CUSTOM_BID"
    private BigDecimal bidPrice; // 抢占实例出价金额，若是自定义出价，且出价金额小于市场价，则不允许创建。当bidModel='CUSTOM_BID'时才有效。
}