package com.baidu.bce.logic.bci.servicev2.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ImageCachePredeployConfigMap {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageCachePredeployConfigMap.class);
    private String userId;
    private List<String> instanceGroups;
}