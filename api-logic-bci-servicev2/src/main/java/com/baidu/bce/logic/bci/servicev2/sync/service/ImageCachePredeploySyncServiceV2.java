package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.imagecachev2.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.model.ImageCachePO;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.ImageDetailDaoV2;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.model.ImageDetailPO;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sServiceException.ErrorCode;
import com.baidu.bce.logic.bci.servicev2.model.ImageCachePredeployConfigMap;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.google.common.base.Strings;
import io.kubernetes.client.custom.IntOrString;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1Affinity;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1DaemonSet;
import io.kubernetes.client.openapi.models.V1DaemonSetSpec;
import io.kubernetes.client.openapi.models.V1DaemonSetUpdateStrategy;
import io.kubernetes.client.openapi.models.V1HostPathVolumeSource;
import io.kubernetes.client.openapi.models.V1LabelSelector;
import io.kubernetes.client.openapi.models.V1LocalObjectReference;
import io.kubernetes.client.openapi.models.V1NodeAffinity;
import io.kubernetes.client.openapi.models.V1NodeSelector;
import io.kubernetes.client.openapi.models.V1NodeSelectorRequirement;
import io.kubernetes.client.openapi.models.V1NodeSelectorTerm;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1PodTemplateSpec;
import io.kubernetes.client.openapi.models.V1ResourceRequirements;
import io.kubernetes.client.openapi.models.V1RollingUpdateDaemonSet;
import io.kubernetes.client.openapi.models.V1Volume;
import io.kubernetes.client.openapi.models.V1VolumeMount;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.bci.servicev2.constant.ImagePullPolicyConstant.IMAGE_PULL_POLICY_ALWAYS;

@Service
public class ImageCachePredeploySyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageAccelerateSyncServiceV2.class);

    @Autowired
    private K8sService k8sService;

    @Autowired
    private ImageCacheDaoV2 imageCacheDaoV2;

    @Autowired
    private ImageDetailDaoV2 imageDetailDaoV2;

    @Value("${image.cache.predeploy.enable:true}")
    private boolean imageCachePredeployEnable = true;

    @Value("${image.cache.predeploy.enable.users:6c47a952db4444c5a097b41be3f24c94}")
    private String imageCachePredeployEnableUsers;

    @Value("${image.cache.predeploy.initcontainer.image.address:registry.baidubce.com/bci-online-public/check-env-ready-image:1.2}")
    private String imageCachePredeployInitContainerImageAddress;

    private long imageCachePredeploySchedulerRunTimes = 0;

    private static final String IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE = "kube-system";

    private static final String IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME = "image-cache-predeploy-config";

    private static final String IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME_KEY = "predeploy-config.json";

    private static final String IMAGE_CACHE_PREDEPLOY_INIT_CONTAINER_NAME = "image-cache-predeploy-init-container";

    private long imageCachePredeployAllSuccessCount = 0;

    public List<V1Container> genDaemonsetContainers(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetails) {
        List<V1Container> containers = new ArrayList<V1Container>();
        for (ImageDetailPO imageDetail : imageDetails) {
            V1Container container = new V1Container();
            container.setArgs(Arrays.asList("-c" , "sleep 72000"));
            container.setCommand(Arrays.asList("sh"));
            // image
            String image = imageDetail.getAcceImageAddress() + ":" + imageDetail.getAcceImageVersion();
            container.setImage(image);
            // imagePullPolicy
            container.setImagePullPolicy("IfNotPresent");
            // name
            container.setName("image-cache-predeploy-image-detail-index-" + String.valueOf(imageDetail.getId()));
            // resources
            V1ResourceRequirements resources = new V1ResourceRequirements();
            resources.setLimits(new HashMap<String, Quantity>());
            resources.getLimits().put("cpu", new Quantity("500m"));
            resources.getLimits().put("memory", new Quantity("1Gi"));
            resources.setRequests(new HashMap<String, Quantity>());
            resources.getRequests().put("cpu", new Quantity("50m"));
            resources.getRequests().put("memory", new Quantity("100Mi"));
            container.setResources(resources);

            container.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);
            container.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);
            containers.add(container);
        }
        return containers;
    }

    public List<V1Container> genDaemonsetInitContainers(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetails) {
        List<V1Container> containers = new ArrayList<V1Container>();
        V1Container container = new V1Container();
        container.setArgs(Arrays.asList("-c" , "/check.sh"));
        container.setCommand(Arrays.asList("bash"));
        container.setImage(imageCachePredeployInitContainerImageAddress);
        container.setImagePullPolicy("IfNotPresent");
        container.setName(IMAGE_CACHE_PREDEPLOY_INIT_CONTAINER_NAME);
        container.setTerminationMessagePolicy(PodConstants.TERMINATION_MESSAGE_POLICY_FILE);
        container.setTerminationMessagePath(PodConstants.TERMINATION_MESSAGE_PATH_DEFAULT);

        container.setVolumeMounts(new ArrayList<V1VolumeMount>());
        container.getVolumeMounts().add(new V1VolumeMount()
                .mountPath("/var/lib")
                .name("varlib")
                .readOnly(true));
        container.getVolumeMounts().add(new V1VolumeMount()
                .mountPath("/home/<USER>")
                .name("homecce")
                .readOnly(true));
        containers.add(container);
        return containers;
    }

    public List<String> genPredeployInstanceGroups(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetails) throws Exception {
        List<String> predeployInstanceGroups = new ArrayList<String>();
        String userId = imageCachePO.getAccountId();
        // get image-cache-predeploy-config from configmap
        V1ConfigMap imageCachePredeployConfigMap = k8sService.getConfigMap(
                userId,
                IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE,
                IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME);
        if (imageCachePredeployConfigMap == null) {
            throw new Exception("imageCachePredeployConfigMap is null namespace:" + IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE
                    + ", configMapName:" + IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME);
        }

        String predeployConfigJson = imageCachePredeployConfigMap.getData()
                .get(IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME_KEY);
        if (Strings.isNullOrEmpty(predeployConfigJson)) {
            throw new Exception("predeployConfigJson is null or empty namespace:" + IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE
                    + ", configMapName:" + IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME + ", key:" + IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME_KEY);
        }

        LOGGER.debug("genPredeployInstanceGroups namespace:{}, configMapName:{}, configMapKey:{}, configMapData:{}",
                IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE, IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME,
                IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME_KEY, predeployConfigJson);

        List<ImageCachePredeployConfigMap> icpConfigMaps = JsonUtil.toList(predeployConfigJson,
                ImageCachePredeployConfigMap.class);
        if (icpConfigMaps == null || icpConfigMaps.isEmpty()) {
            throw new Exception("icpConfigMaps is null or empty namespace:" + IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE
                    + ", configMapName:" + IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME + ", key:" + IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME_KEY);
        }
        for (ImageCachePredeployConfigMap icpConfigMap : icpConfigMaps) {
            if (icpConfigMap == null) {
                continue;
            }
            String authorizedUserId = icpConfigMap.getUserId();
            if (userId.equalsIgnoreCase(authorizedUserId)) {
                predeployInstanceGroups.addAll(icpConfigMap.getInstanceGroups());
            }
        }
        if (predeployInstanceGroups.isEmpty()) {
            throw new Exception("predeployInstanceGroups is null or empty namespace:" + IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE
                    + ", configMapName:" + IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME + ", key:" + IMAGE_CACHE_PREDEPLOY_CONFIGMAP_NAME_KEY);
        }
        return predeployInstanceGroups;
    }

    public V1Affinity genDaemonsetAffinity(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetails) throws Exception {
        V1Affinity affinity = new V1Affinity();
        V1NodeAffinity nodeAffinity = new V1NodeAffinity();
        V1NodeSelector nodeSelector = new V1NodeSelector();
        nodeAffinity.setRequiredDuringSchedulingIgnoredDuringExecution(nodeSelector);
        V1NodeSelectorTerm nodeSelectorTerm = new V1NodeSelectorTerm();
        nodeSelectorTerm.setMatchExpressions(new ArrayList<V1NodeSelectorRequirement>());
        List<String> predeployInstanceGroups = genPredeployInstanceGroups(imageCachePO, imageDetails);
        nodeSelectorTerm.getMatchExpressions().add(new V1NodeSelectorRequirement()
                .key("instance-group-id")
                .operator("In").
                values(predeployInstanceGroups));
        nodeSelector.setNodeSelectorTerms(Arrays.asList(nodeSelectorTerm));
        affinity.setNodeAffinity(nodeAffinity);
        return affinity;
    }

    public String getImageCachePredeployDaemonsetName(ImageCachePO imageCachePO) {
        String userId = imageCachePO.getAccountId();
        String daemonSetName = "image-cache-predeploy-" + userId + "-" + imageCachePO.getId();
        return daemonSetName;
    }

    public V1DaemonSet createImageCachePredeployDaemonSet(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetails) throws Exception {
        String userId = imageCachePO.getAccountId();
        String imageCacheId = imageCachePO.getImageCacheId();

        V1DaemonSet daemonSet = new V1DaemonSet();
        V1ObjectMeta metadata = new V1ObjectMeta();
        daemonSet.setMetadata(metadata);
        String daemonSetName = getImageCachePredeployDaemonsetName(imageCachePO);
        metadata.setName(daemonSetName);
        metadata.setNamespace(IMAGE_CACHE_PREDEPLOY_DAEMONSET_NAMESPACE);

        metadata.setLabels(new HashMap<String, String>());
        metadata.getLabels().put("app", daemonSetName);
        metadata.getLabels().put("userid", userId);

        metadata.setAnnotations(new HashMap<String, String>());
        metadata.getAnnotations().put("image_cache_name", imageCachePO.getImageCacheName());
        metadata.getAnnotations().put("image_cache_id", imageCacheId);
        metadata.getAnnotations().put("image_cache_owner", imageCachePO.getImageCacheOwner());
        metadata.getAnnotations().put("image_cache_type", imageCachePO.getImageCacheType());
        metadata.getAnnotations().put("image_cache_tc_pod_name", imageCachePO.getTcPodName());

        V1DaemonSetSpec spec = new V1DaemonSetSpec();
        daemonSet.setSpec(spec);
        spec.setRevisionHistoryLimit(10);

        Map<String, String> selectorMatchLabels = new HashMap<String, String>();
        selectorMatchLabels.put("app", daemonSetName);

        spec.setSelector(new V1LabelSelector().matchLabels(selectorMatchLabels));
        V1PodTemplateSpec v1PodTemplateSpec = new V1PodTemplateSpec();
        spec.setTemplate(v1PodTemplateSpec);

        V1DaemonSetUpdateStrategy updateStrategy = new V1DaemonSetUpdateStrategy();
        updateStrategy.setType("RollingUpdate");
        V1RollingUpdateDaemonSet rollingUpdate = new V1RollingUpdateDaemonSet();
        rollingUpdate.setMaxUnavailable(new IntOrString(50));
        updateStrategy.setRollingUpdate(rollingUpdate);

        spec.setUpdateStrategy(updateStrategy);

        V1ObjectMeta metadataTemplate = new V1ObjectMeta();
        metadataTemplate.setLabels(selectorMatchLabels);
        v1PodTemplateSpec.setMetadata(metadataTemplate);

        metadataTemplate.setAnnotations(new HashMap<String, String>());

        int imageDetailIndex = 1;
        for (ImageDetailPO imageDetailPO : imageDetails) {
            String imageDetailIndexStr = String.valueOf(imageDetailIndex);
            long imageDetailId = imageDetailPO.getId();
            metadataTemplate.getAnnotations().put(
                    "image_cache_image_index_" + imageDetailIndexStr + "_image_detail_id",
                    String.valueOf(imageDetailId));
            metadataTemplate.getAnnotations().put(
                    "image_cache_image_index_" + imageDetailIndexStr + "_image_cache_id",
                    imageDetailPO.getImageCacheId());
            metadataTemplate.getAnnotations().put(
                    "image_cache_image_index_" + imageDetailIndexStr + "_image_cache_type",
                    imageDetailPO.getImageCacheType());
            metadataTemplate.getAnnotations().put(
                    "image_cache_image_index_" + imageDetailIndexStr + "_origin_image_address",
                    imageDetailPO.getOriginImageAddress());
            metadataTemplate.getAnnotations().put(
                    "image_cache_image_index_" + imageDetailIndexStr + "_origin_image_version",
                    imageDetailPO.getOriginImageVersion());
            metadataTemplate.getAnnotations().put(
                    "image_cache_image_index_" + imageDetailIndexStr + "_acce_image_address",
                    imageDetailPO.getAcceImageAddress());
            metadataTemplate.getAnnotations().put(
                    "image_cache_image_index_" + imageDetailIndexStr + "_acce_image_version",
                    imageDetailPO.getAcceImageVersion());
            imageDetailIndex++;
        }

        V1PodSpec podSpec = new V1PodSpec();
        v1PodTemplateSpec.setSpec(podSpec);
        // affinity
        V1Affinity affinity = genDaemonsetAffinity(imageCachePO, imageDetails);
        podSpec.setAffinity(affinity);
        // containers
        List<V1Container> containers = genDaemonsetContainers(imageCachePO, imageDetails);
        podSpec.setContainers(containers);
        metadata.getAnnotations().put("image_cache_image_size", String.valueOf(containers.size()));
        podSpec.setDnsPolicy("Default");
        podSpec.setHostNetwork(true);
        podSpec.setHostPID(true);
        podSpec.setImagePullSecrets(Arrays.asList(new V1LocalObjectReference().name("bci-ccr-secret")));
        // initContainers
        List<V1Container> initContainers = genDaemonsetInitContainers(imageCachePO, imageDetails);
        podSpec.setInitContainers(initContainers);
        podSpec.setPriority(0);
        podSpec.setRestartPolicy(IMAGE_PULL_POLICY_ALWAYS);
        podSpec.setTerminationGracePeriodSeconds(30L);
        // volumes
        podSpec.setVolumes(new ArrayList<>());
        List<V1Volume> hostPathVolumes = new ArrayList<>();
        hostPathVolumes.add(new V1Volume()
                .name("varlib")
                .hostPath(new V1HostPathVolumeSource().path("/var/lib")));
        hostPathVolumes.add(new V1Volume()
                .name("homecce")
                .hostPath(new V1HostPathVolumeSource().path("/home/<USER>")));
        podSpec.getVolumes().addAll(hostPathVolumes);
        return daemonSet;
    }

    public boolean imageCachePredeployImpl(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetails) throws Exception {
        String userId = imageCachePO.getAccountId();
        String imageCacheId = imageCachePO.getImageCacheId();
        String imageCacheName = imageCachePO.getImageCacheName();
        V1DaemonSet daemonSet = createImageCachePredeployDaemonSet(imageCachePO, imageDetails);
        if (daemonSet == null) {
            LOGGER.error("createImageCachePredeployDaemonSet failed,"
                            + " userId:{}, imageCacheId:{}, imageCacheName:{}",
                    userId, imageCacheId, imageCacheName);
            return false;
        }

        try {
            LOGGER.debug("imageCachePredeployImpl createDaemonSet"
                    + " userId:{} imageCacheId:{} imageCacheName:{} daemonSet:{}",
                    userId, imageCacheId, imageCacheName, JsonUtil.toJSONString(daemonSet));
            k8sService.createDaemonSetWithSts(userId, daemonSet);
            return true;
        } catch (K8sServiceException e) {
            if (e.getErrorCode() == ErrorCode.K8S_CLUSTER_NOT_EXIST) {
                LOGGER.error("K8sService createDaemonSet K8S_CLUSTER_NOT_EXIST"
                                + " userId:{} imageCacheId:{} imageCacheName:{} error:{}",
                        userId, imageCacheId, imageCacheName, e.getResponseBody());
            } else if (e.getErrorCode() == ErrorCode.DAEMONSET_EXISTED) {
                LOGGER.error("K8sService createDaemonSet CREATE_EXISTED "
                                + " userId:{} imageCacheId:{} imageCacheName:{} error:{}",
                        userId, imageCacheId, imageCacheName, e.getResponseBody());
            } else {
                if (e.getErrorCode() == ErrorCode.CREATE_DAEMONSET_FAILED
                        && StringUtils.isNotEmpty(e.getResponseBody())
                        && JsonUtil.isJson(e.getResponseBody())) {
                    LOGGER.error("K8sService createDaemonSet CREATE_DAEMONSET_FAILED"
                                    + " userId:{} imageCacheId:{} imageCacheName:{} error:{}",
                            userId, imageCacheId, imageCacheName, e.getResponseBody());
                }
            }
        } catch (ApiException e) {
            LOGGER.error("K8sService createDaemonSet CREATE_DAEMONSET_FAILED "
                            + " userId:{} imageCacheId:{} imageCacheName:{} error:{}",
                    userId, imageCacheId, imageCacheName, e.getResponseBody());
        }
        return false;
    }

    public int runImageCachePredeploy() throws Exception {
        int imageCachePredeploySuccessCount = 0;
        int imageCachePredeployFailedCount = 0;
        LOGGER.debug("start runImageCachePredeploy");
        List<String> imageCachePredeployEnableUsers = Arrays.asList(this.imageCachePredeployEnableUsers.split(","));
        if (imageCachePredeployEnableUsers.isEmpty()) {
            LOGGER.info("imageCachePredeployEnableUsers is empty");
            return imageCachePredeploySuccessCount;
        }
        List<ImageCachePO> imageCachePredeployList =
                imageCacheDaoV2.listAllSuccessImageCacheAndNotPredeploy(imageCachePredeployEnableUsers);
        LOGGER.debug("runImageCachePredeploy imageCachePredeployList size:{}, info:{}",
                imageCachePredeployList.size(), JsonUtil.toJSON(imageCachePredeployList));

        for (ImageCachePO imageCachePO : imageCachePredeployList) {
            String userId = imageCachePO.getAccountId();
            String imageCacheId = imageCachePO.getImageCacheId();
            String imageCacheName = imageCachePO.getImageCacheName();
            LOGGER.debug("runImageCachePredeploy start image cache predeploy"
                            + "userId:{} imageCacheName:{} imageCacheId:{}",
                    userId, imageCacheName, imageCacheId);
            if (imageCacheId.isEmpty() || imageCachePO.getProgress() != 100) {
                continue;
            }
            List<ImageDetailPO> imageDetails =
                    imageDetailDaoV2.getImageDetailsByImageCacheId(imageCacheId);
            LOGGER.debug("runImageCachePredeploy run image cache predeploy"
                            + "userId:{} imageCacheName:{} imageCacheId:{} imageDetails.size:{} imageDetails:{}",
                    userId, imageCacheName, imageCacheId, imageDetails.size(), JsonUtil.toJSON(imageDetails));
            if (imageDetails.isEmpty()) {
                LOGGER.warn("runImageCachePredeploy run image cache predeploy not found imageDetails"
                                + " and ignore this image cache userId:{} imageCacheName:{} imageCacheId:{}",
                        userId, imageCacheName, imageCacheId);
                continue;
            }
            boolean imageImagePredeployResult = imageCachePredeployImpl(imageCachePO, imageDetails);
            LOGGER.debug("runImageCachePredeploy end image cache predeploy result:{}"
                            + " userId:{} imageCacheName:{} imageCacheId:{}",
                    imageImagePredeployResult, userId, imageCacheName, imageCacheId);
            if (imageImagePredeployResult) {
                imageCachePredeploySuccessCount++;
                Long utcTimeDiff = 28800000L;
                imageCachePO.setImageCachePredeployTime(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
                String imageCachePredeployDaemonsetName = getImageCachePredeployDaemonsetName(imageCachePO);
                imageCachePO.setImageCachePredeployDaemonsetName(imageCachePredeployDaemonsetName);
                imageCacheDaoV2.updateImageCachePredeployCompleted(imageCachePO);
                LOGGER.debug("runImageCachePredeploy image cache predeploy success"
                                + "userId:{} imageCacheName:{} imageCacheId:{}",
                        userId, imageCacheName, imageCacheId);
            } else {
                imageCachePredeployFailedCount++;
                LOGGER.debug("runImageCachePredeploy image cache predeploy failed"
                                + "userId:{} imageCacheName:{} imageCacheId:{}",
                        userId, imageCacheName, imageCacheId);
            }
        }
        LOGGER.debug("end runImageCachePredeploy imageCachePredeploySuccessCount:{}, imageCachePredeployFailedCount:{}",
                imageCachePredeploySuccessCount, imageCachePredeployFailedCount);
        return imageCachePredeploySuccessCount;
    }

    public void syncImageCachePredeploy() {
        if (!imageCachePredeployEnable) {
            LOGGER.debug("image cache predeploy is disabled");
            return;
        }

        imageCachePredeploySchedulerRunTimes++;
        int imageCachePredeployCurrentLoopSuccessCount = 0;
        LOGGER.debug("start syncImageCachePredeploy runTimes:{}", imageCachePredeploySchedulerRunTimes);
        try {
            imageCachePredeployCurrentLoopSuccessCount =  runImageCachePredeploy();
            imageCachePredeployAllSuccessCount += imageCachePredeployCurrentLoopSuccessCount;
        } catch (Exception e) {
            LOGGER.error("syncImageCachePredeploy runTimes:{} error", imageCachePredeploySchedulerRunTimes, e);
        }
        LOGGER.debug("end syncImageCachePredeploy runTimes:{}"
                        + " imageCachePredeployCurrentLoopSuccessCount:{}"
                        + " imageCachePredeployAllSuccessCount:{}",
                imageCachePredeploySchedulerRunTimes,
                imageCachePredeployCurrentLoopSuccessCount,
                imageCachePredeployAllSuccessCount);
    }
}
