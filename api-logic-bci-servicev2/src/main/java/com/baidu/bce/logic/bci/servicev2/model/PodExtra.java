package com.baidu.bce.logic.bci.servicev2.model;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.kubernetes.client.openapi.models.V1TopologySpreadConstraint;
import lombok.Data;
import lombok.experimental.Accessors;

import com.baidu.bce.logic.bci.daov2.common.model.Label;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PodExtra {
    // 以下字段在podDetail接口不会给vk返回
    private List<Label> metadataLabels;
    private Affinity affinity;
    private List<V1TopologySpreadConstraint> topologySpreadConstraints;
    private Long terminationGracePeriodSeconds;
    private String hostname;
    private PodSecurityContext securityContext;
    // vk 传递的extra信息，通过podDetail接口给vk返回
    private Map<String, String> vkExtras;

    public static PodExtra fromPodPurchaseRequest(PodPurchaseRequest podPurchaseRequest) {
        if (podPurchaseRequest == null) {
            return null;
        }
        PodExtra podExtra = new PodExtra();
        podExtra.setAffinity(podPurchaseRequest.getAffinity());
        podExtra.setTopologySpreadConstraints(podPurchaseRequest.getTopologySpreadConstraints());
        podExtra.setTerminationGracePeriodSeconds(podPurchaseRequest.getTerminationGracePeriodSeconds());
        podExtra.setMetadataLabels(podPurchaseRequest.getMetadataLabels());
        podExtra.setHostname(podPurchaseRequest.getHostname());
        podExtra.setSecurityContext(podPurchaseRequest.getSecurityContext());
        podExtra.setVkExtras(podPurchaseRequest.getExtras());
        return podExtra;
    }
}