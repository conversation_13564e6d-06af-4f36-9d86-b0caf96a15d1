package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.ops.model.OpsTaskPO;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDSpec;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskCRDStatus;
import com.baidu.bce.logic.bci.servicev2.model.OpsTaskContent;
import com.baidu.bce.logic.bci.servicev2.ops.OpsTaskService;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import io.kubernetes.client.extended.workqueue.DefaultRateLimitingQueue;
import io.kubernetes.client.extended.workqueue.RateLimitingQueue;
import io.kubernetes.client.informer.ResourceEventHandler;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

import javax.annotation.PostConstruct;

@Service("opsTaskSyncService")
public class OpsTaskSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpsTaskSyncService.class);
    private RateLimitingQueue<OpsTaskCRDRequest> workQueue;

    @Autowired
    private OpsTaskService opsTaskService;

    @Autowired
    private K8sService k8sService;

    @PostConstruct
    private void postConstruct() {
        LOGGER.info("OpsTaskSyncService postConstruct");
        new Thread(
                new Runnable() {
                    @Override
                    public void run() {
                        workQueue = new DefaultRateLimitingQueue<>();
                        try {
                            syncOpsTasks();
                        } catch (InterruptedException e) {
                            LOGGER.error("OpsTaskSyncService worker exit .", e);
                        }
                    }
                }
        ).start();
    }

    public ResourceEventHandler<OpsTaskCRDRequest> getOpsTaskCRDEventHandler() {
        return new ResourceEventHandler<OpsTaskCRDRequest>() {
            @Override
            public void onAdd(OpsTaskCRDRequest obj) {
                V1ObjectMeta metadata = obj.getMetadata();
                OpsTaskCRDSpec spec = obj.getSpec();
                OpsTaskCRDStatus status = obj.getStatus();
                if (metadata == null || spec == null || status == null) {
                    return;
                }
                workQueue.add(obj);
            }

            @Override
            public void onUpdate(OpsTaskCRDRequest oldObj, OpsTaskCRDRequest newObj) {
                V1ObjectMeta metadata = newObj.getMetadata();
                OpsTaskCRDSpec spec = newObj.getSpec();
                OpsTaskCRDStatus status = newObj.getStatus();
                if (metadata == null || spec == null || status == null) {
                    return;
                }
                workQueue.add(newObj);
            }

            @Override
            public void onDelete(OpsTaskCRDRequest obj, boolean deletedFinalStateUnknown) {

            }
        };
    }


    public void syncOpsTasks() throws InterruptedException {
        LOGGER.info("OpsTaskSyncService start thread for processing ops task event");
        while (!workQueue.isShuttingDown()) {
            OpsTaskCRDRequest opsTask = workQueue.get();
            LOGGER.info("OpsTaskSyncService receive opsTask {} ", opsTask.getMetadata().getName());

            V1ObjectMeta metadata = opsTask.getMetadata();
            OpsTaskCRDSpec spec = opsTask.getSpec();
            OpsTaskCRDStatus status = opsTask.getStatus();
            if (metadata == null || spec == null || status == null) {
                LOGGER.error("OpsTaskSyncService receive opsTask {} is empty ", opsTask.getMetadata().getName());
                continue;
            }

            try {
                OpsTaskPO opsTaskPo = opsTaskService.queryOpsTaskByUuid(spec.getUuid());
                if (opsTaskPo == null) {
                    LOGGER.error("OpsTaskSyncService receive opsTask uuid {} query db empry ", spec.getUuid());
                    continue;
                }

                int taskComplete = 0;
                // crd 是否完成
                if (opsTask.getStatus().isTaskComplete()) {
                    taskComplete = 1;
                }
                if (CollectionUtils.isEmpty(opsTask.getStatus().getTaskContents())) {
                    opsTask.getStatus().setTaskContents(new ArrayList<OpsTaskContent>());
                }
                String taskContents = JsonUtil.toJSON(opsTask.getStatus().getTaskContents());
                LOGGER.info("OpsTaskSyncService syncOpsTasks podId {} uuid {} taskContents {} completed {} ",
                        spec.getPodID(), spec.getUuid(), taskContents, taskComplete);
                int result = opsTaskService.updateOpsTaskContents(taskContents, taskComplete, spec.getUuid(),
                        spec.getPodID());
                if (result <= 0) {
                    LOGGER.warn("OpsTaskSyncService syncOpsTasks podId {} uuid {} update db result  less zero ",
                            spec.getPodID(), spec.getUuid());
                }

                if (taskComplete == 1) {
                    // crd 已经完成，删除crd
                    k8sService.deleteOpsTaskCRD(metadata.getNamespace(), metadata.getName());
                }
            } catch (Exception e) {
                LOGGER.error("OpsTaskSyncService syncOpsTasks podId {} uuid {} err {} ", spec.getPodID(),
                        spec.getUuid());
            } finally {
                workQueue.done(opsTask);
            }
        }
    }
}
