package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BciVolume {

    /**
     * 数据卷类型。取值范围： 
     * EmptyDirVolume：EmptyDir类型的数据卷，表示空目录。
     * NFSVolume：NFS类型的数据卷，表示网络文件系统。
     * ConfigFileVolume：ConfigFile类型的数据卷，表示配置文件。
     * FlexVolume：使用FlexVolume插件扩展存储类型，支持挂载云盘。
     * HostPathVolume：HostPath类型的数据卷，表示主机节点的文件或目录。
     * DiskVolume（不推荐）：云盘数据卷。建议使用FlexVolume挂载云盘。
    */
    private String type;
    // 数据卷名称
    private String name;

    private List<NfsVolume> nfsVolumes;
    private List<EmptyDirVolume> emptyDirVolumes;
    private List<ConfigFileVolume> configFileVolumes;
    private List<PodVolume> podVolumes;
    private List<FlexVolume> flexVolumes;
    private List<HostPathVolume> hostPathVolumes;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PodVolume {
        private String name;
        private String type; // data, empty_dir，rootfs
        private VolumeSource volumeSource;
        private Integer sizeInGB;
        @JsonProperty(value = "fs")
        private FS fs;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class FS {
        @JsonProperty(value = "fs_type")
        private String fsType = "ext4";
        @JsonProperty(value = "mount_flags")
        private List<String> mountFlags;
        @JsonProperty(value = "force_format")
        private Boolean forceFormat = false;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VolumeSource {
        private CDS cds;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CDS {
        private String uuid = "";
        private String name;
        private String type; // 磁盘类型
    }

    public List<NfsVolume> getNfsVolumes() {
        if (this.nfsVolumes == null) {
            this.nfsVolumes = new ArrayList<>();
        }
        return this.nfsVolumes;
    }

    public void setNfsVolumes(List<NfsVolume> nfsVolumes) {
        this.nfsVolumes = nfsVolumes;
    }

    public List<EmptyDirVolume> getEmptyDir() {
        if (this.emptyDirVolumes == null) {
            this.emptyDirVolumes = new ArrayList<>();
        }
        return this.emptyDirVolumes;
    }

    public void setEmptyDir(List<EmptyDirVolume> emptyDirVolumes) {
        this.emptyDirVolumes = emptyDirVolumes;
    }

    public List<ConfigFileVolume> getConfigFile() {
        if (configFileVolumes == null) {
            configFileVolumes = new ArrayList<>();
        }
        return configFileVolumes;
    }

    public void setConfigFile(List<ConfigFileVolume> configFileVolumes) {
        this.configFileVolumes = configFileVolumes;
    }

    public List<FlexVolume> getFlexVolume() {
        if (flexVolumes == null) {
            flexVolumes = new ArrayList<>();
        }
        return flexVolumes;
    }

    public void setFlexVolume(List<FlexVolume> flexVolume) {
        this.flexVolumes = flexVolume;
    }
}
