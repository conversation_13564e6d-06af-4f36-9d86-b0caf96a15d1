package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

@Data
public class BciOpsDetailRecord implements Comparable<BciOpsDetailRecord> {

    private String opsType;
    private String opsStatus;
    private String storageType;
    private String storageContent;
    @JsonIgnore
    private String createTimeStr;

    private long createTime;

    @Override
    public int compareTo(@NotNull BciOpsDetailRecord o) {
        return (int) (o.createTime - this.createTime);
    }
}
