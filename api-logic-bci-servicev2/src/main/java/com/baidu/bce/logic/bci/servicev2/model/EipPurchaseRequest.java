package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.eipv2.model.EipForCreate;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class EipPurchaseRequest extends EipForCreate implements IOrderItem {
    private String serviceType = "EIP";
    private String productType;
    private String routeType;
    private String eipIp;

    public EipPurchaseRequest() {
    }

    public EipPurchaseRequest(CreateContainerGroupRequest request) {
        this.setRouteType(request.getEipRouteType());
        this.setBandwidthInMbps(request.getEipBandwidthInMbps());
        this.setServiceType("EIP");
        this.setProductType(request.getEipPaymentTiming());
        this.setSubProductType(request.getEipBillingMethod());
        this.setAutoRenewTimeUnit(request.getEipAutoRenewTimeUnit());
        this.setAutoRenewTime(request.getEipAutoRenewTime());
        this.setName(request.getEipName());
    }

    @Override
    public String getServiceType() {
        return serviceType;
    }

    @Override
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    @Override
    public String getProductType() {
        return productType;
    }

    @Override
    public void setProductType(String productType) {
        this.productType = productType;
    }
}
