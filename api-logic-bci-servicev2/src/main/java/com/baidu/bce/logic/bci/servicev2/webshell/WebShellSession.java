package com.baidu.bce.logic.bci.servicev2.webshell;

import io.kubernetes.client.util.WebSocketStreamHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class WebShellSession {
    private static final Logger LOGGER = LoggerFactory.getLogger(WebShellSession.class);

    private static Set<String> shSet = new HashSet<>();

    static {
        // 执行命令为以下，前端退出时需要向k8s 发送exit，退出进程
        shSet.add("sh");
        shSet.add("/bin/sh");
        shSet.add("bash");
        shSet.add("/bin/bash");
    }

    public WebSocketSession session;

    public String token;

    public WebShellExec exec;

    public boolean closed = false;

    public long frontLastMessageTime = 0;

    public WebSocketStreamHandler handler;

    /**
     * 关闭与前端的websocket连接
     *
     * @param session
     * @param token
     */
    public void closeSession(WebSocketSession session, String token) {
        if (this.closed) {
            return;
        }
        this.closed = true;
        try {
            session.close();
        } catch (IOException e) {
            LOGGER.error("WebShellServer accept close token {} err {} ", token, e);
        }
    }

    /**
     * 向前端发送响应
     *
     * @param bytes
     * @throws IOException
     */
    public void sendMessageToFront(int stream, byte[] bytes) throws IOException {
        LOGGER.debug("WebShellSession token {} stream {} send resp data {} ", token, stream, Arrays.toString(bytes));
        ByteBuffer allocate = ByteBuffer.allocate(bytes.length + 1);
        allocate.put((byte) stream);
        allocate.put(bytes);
        this.session.sendMessage(new BinaryMessage(allocate.array()));
    }

    /**
     * 向前端发送pong
     *
     * @throws IOException
     */
    public void sendPongToFront() throws IOException {
        byte[] pong = new byte[1];
        pong[0] = WebShellStreamType.Pong.getType();
        this.session.sendMessage(new BinaryMessage(pong));
    }

    public boolean isShOrBashCommand() {
        List<String> commands = parseCommand(exec.getWsUrl());
        if (commands.size() != 1) {
            return false;
        }
        if (shSet.contains(commands.get(0))) {
            return true;
        }
        return false;
    }

    /**
     * 前端主动关闭socket连接 || 心跳超时，向k8s 发送exit ，退出sh 或 bash 进程，不发送sh进程会一直存在
     */
    public void exitK8sShProcess() {
        if (closed) {
            return;
        }
        try {
            // 获取command
            List<String> commands = parseCommand(exec.getWsUrl());
            if (commands.size() != 1) {
                return;
            }
            if (shSet.contains(commands.get(0))) {
                this.exec.getOutputStream().write("exit \n".getBytes());
                this.exec.getOutputStream().flush();
            }
        } catch (Exception e) {
            LOGGER.error("WebShellServer token {} exitK8sProcess err {} ", token, e);
        }
    }

    private List<String> parseCommand(String wsUrl) {
        List<String> result = new ArrayList<>();
        int index = wsUrl.indexOf("&command");
        if (index == -1) {
            return result;
        }
        String[] commands = wsUrl.substring(index).split("command=");
        for (String c : commands) {
            String replace = c.replace("&", "");
            if ("".equals(replace)) {
                continue;
            }

            try {
                // 需要url decode一下
                result.add(URLDecoder.decode(replace, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                // ignore
            }
        }
        return result;
    }
}
