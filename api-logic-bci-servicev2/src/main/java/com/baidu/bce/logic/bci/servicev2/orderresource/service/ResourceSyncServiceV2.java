package com.baidu.bce.logic.bci.servicev2.orderresource.service;

import com.baidu.bce.billing.resourcemanager.model.ResourceDetail;
import com.baidu.bce.billing.resourcemanager.model.ResourceUseStatusInfo;
import com.baidu.bce.billing.resourcemanager.model.ResourceUseStatusType;
import com.baidu.bce.logic.bci.daov2.cceuser.CceUserMapDao;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.chargestatus.PodChargeStatusDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.orderresource.BillingResourceSyncManagerV2;
import com.baidu.bce.logic.bci.servicev2.sync.service.SyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class ResourceSyncServiceV2 extends SyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceSyncServiceV2.class);

    @Autowired
    private BillingResourceSyncManagerV2 billingResourceSyncManager;

    @Autowired
    private LogicalResourceServiceV2 logicalResourceService;

    @Autowired
    private CceUserMapDao cceUserMapDao;

    @Autowired
    private PodChargeStatusDaoV2 podChargeStatusDao;

    public void syncInstanceStatusToBilling() {
        // 1.获取所有cpt1账号
        List<CceUserMap> cceCpt1UserMaps = cceUserMapDao.listCpt1CceUserMaps();
        LOGGER.info(
            "ResourceSyncServiceV2 get cceCpt1UserMaps size {}, list {}", cceCpt1UserMaps.size(), cceCpt1UserMaps);

        // 2.按账号获取Resource状态及账号所有的running的pod
        for (CceUserMap cceCpt1UserMap : cceCpt1UserMaps) {
            // 这个chargeAccountId包含了代付的账号
            String chargeAccountId = cceCpt1UserMap.getUserId();
            LOGGER.info("ResourceSyncServiceV2 begin to sync accountId {} pods", chargeAccountId);
            try {
                // 2.1获取Resource状态
                Map<String, ResourceDetailWithCpt1Status> resourceDetailWithCpt1StatusMap =
                        getAllCpt1ResourceStatus(chargeAccountId);
                LOGGER.debug("ResourceSyncServiceV2 get resourceDetailWithCpt1StatusMap account {}, size {}, " +
                                "nameList {}", chargeAccountId, resourceDetailWithCpt1StatusMap.size(),
                        resourceDetailWithCpt1StatusMap.keySet());

                // 2.1获取账号的非删除的pod，主要考虑少计费需要计费
                Map<String, PodPO> podPOMap = podDao.listCpt1PodByAccount(chargeAccountId);
                LOGGER.debug("ResourceSyncServiceV2 get pods, account {}, size {} uuidList {}",
                        chargeAccountId, podPOMap.size(), podPOMap.keySet());

                // 3.遍历 Resource 获取每个pod的状态是否一致，只处理状态不一致，对于Pod多但是没计费情况不处理
                // 先以 billing 侧资源状态为主
                int i = 0;
                for (Map.Entry<String, ResourceDetailWithCpt1Status> entry :
                    resourceDetailWithCpt1StatusMap.entrySet()) {
                    i += 1;
                    String uuid = entry.getKey();
                    ResourceDetailWithCpt1Status resourceDetailWithCpt1Status = entry.getValue();
                    LOGGER.info("ResourceSyncServiceV2 try to sync billing resource, serial number: {}, account {}, " +
                            "billing resource {}", i, chargeAccountId, resourceDetailWithCpt1Status);
                    PodPO podPO = podPOMap.get(uuid);
                    // 3.1 pod是deleted状态或者没查到，先不删除，报警依赖人工
                    if (null == podPO) {
                        // 目前看只能暂时报警，不能将计费资源改终态
                        LOGGER.error(
                            "ResourceSyncServiceV2 get not in db billing resource {}",
                            resourceDetailWithCpt1Status.getResourceDetail());
                        continue;
                    }
                    // 3.2 pod非deleted的，需要判断状态是否相等
                    String syncStatus = podPO.getInternalStatus();
                    String podChargeStatus = PodConstants.NO_CHARGE;
                    if (PodConstants.BCI_INTERNAL_STATUS_SYNCED.equals(syncStatus)) {
                        podChargeStatus = PodUtils.getChargeStatusOfCpt1Instance(
                            podPO.getStatus(), podPO.getDelayReleaseDurationMinute(), 
                            podPO.isDelayReleaseSucceeded(), podPO.getResourceRecycleTimestamp(),
                            podPO.getResourceRecycleComplete(), podPO.getResourceRecycleReason());
                    }
                
                    String billingChargeStatus = resourceDetailWithCpt1Status.getCpt1Status()
                            == ResourceUseStatusType.USING ? PodConstants.CHARGE : PodConstants.NO_CHARGE;
                    if (!podChargeStatus.equals(billingChargeStatus)) {
                        tryDoSync(chargeAccountId, podPO, podChargeStatus);
                        LOGGER.info("ResourceSyncServiceV2 sync billing resource, serial number: {}, account {}, " +
                                        "billing resource status {}, pod charge status {}",
                                i, chargeAccountId, billingChargeStatus, podChargeStatus);
                    }
                    LOGGER.info("ResourceSyncServiceV2 finish sync billing resource, serial number: {}, account {}, " +
                            "billing resource {}", i, chargeAccountId, resourceDetailWithCpt1Status);
                }
                // 4.报警bci有实例，但是没有billing resource没有
                List<String> noChargePodUuidList = new ArrayList<>();
                for (Map.Entry<String, PodPO> entry : podPOMap.entrySet()) {
                    String podUuid = entry.getKey();
                    if (!resourceDetailWithCpt1StatusMap.containsKey(podUuid)) {
                        noChargePodUuidList.add(podUuid);
                    }
                }
                if (noChargePodUuidList.size() > 0) {
                    LOGGER.error("ResourceSyncServiceV2 get no charge pod List {}", noChargePodUuidList);
                }
            } catch (Exception e) {
                LOGGER.error("ResourceSyncServiceV2 caught error: ", e);
            }
        }
    }

    private void tryDoSync(String accountId, PodPO podPO, String podChargeStatus) {
        String chargeStatus = podChargeStatusDao.getChargeStatusWhichResourceVersionIsNewest(podPO.getPodUuid());
        if (StringUtils.isBlank(chargeStatus) || !chargeStatus.equals(podChargeStatus)) {
            LOGGER.error(
                "ResourceSyncServiceV2 tryDoSync caught diff chargeStatus, podChargeStatus table "
                    + "status {}, pod table status {}",
                chargeStatus,
                podChargeStatus);
        }
        // 以podChargeStatus为准同步一次
        billingResourceSyncManager.doSync(podPO, accountId, podPO.getPodUuid(), podPO.getResourceVersion(),
                podChargeStatus);
    }

    private Map<String, ResourceDetailWithCpt1Status> getAllCpt1ResourceStatus(String accountId) {
        // 获取当前账号所有cpt1资源的资源
        List<ResourceDetail> resourceDetails = logicalResourceService.queryCpt1ResourceListV2(accountId);
        // 获取资源的开关机状态Map
        List<String> nameList = new ArrayList<>(resourceDetails.size());
        Map<String, ResourceDetailWithCpt1Status> map = new HashMap<>(resourceDetails.size());
        for (ResourceDetail resourceDetail : resourceDetails) {
            nameList.add(resourceDetail.getName());
            map.put(resourceDetail.getName(), new ResourceDetailWithCpt1Status(resourceDetail));
        }
        Set<String> pauseResourceName =
            logicalResourceService.queryCpt1PauseResourceStatusInfos(accountId, nameList, PodConstants.SERVICE_TYPE);

        for (String name : pauseResourceName) {
            ResourceDetailWithCpt1Status resourceDetailWithCpt1Status = map.get(name);
            if (null != resourceDetailWithCpt1Status) {
                resourceDetailWithCpt1Status.setCpt1Status(ResourceUseStatusType.PAUSE);
            }
        }
        return map;
    }

    @Data
    public static class ResourceDetailWithCpt1Status extends ResourceUseStatusInfo {
        ResourceUseStatusType cpt1Status = ResourceUseStatusType.USING;
        ResourceDetail resourceDetail;

        public ResourceDetailWithCpt1Status(ResourceDetail resourceDetail) {
            this.resourceDetail = resourceDetail;
        }
    }
}
