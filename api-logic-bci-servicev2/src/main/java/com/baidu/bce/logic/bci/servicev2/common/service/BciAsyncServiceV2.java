package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.asyncwork.sdk.asyncaop.BceAsyncWork;
import com.baidu.bce.asyncwork.sdk.model.Level;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.bci.CceImageClient;
import com.baidu.bce.internalsdk.bci.ContainerManagerClient;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.model.CNImageCacheRequest;
import com.baidu.bce.internalsdk.bci.model.ImageCacheTaskStatus;
import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthorityList;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.servicev2.model.BciQuota;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.util.PodValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class BciAsyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(BciAsyncServiceV2.class);

    @Autowired
    private LogicalPodEipServiceV2 logicalPodEipService;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private LogicalZoneResourceServiceV2 logicalZoneResourceServiceV2;

    @BceAsyncWork(name = "getEipInstanceMapAsync", level = Level.MIDDLE)
    public Map<String, EipInstance> getEipInstanceMapAsync() {
        return logicalPodEipService.getBciEipMap();
    }

    @BceAsyncWork(name = "validateBciParameters", level = Level.MIDDLE)
    public Boolean validateBciParameters(PodPurchaseRequest podPurchaseRequest, BciQuota bciQuota) {
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
        return true;
    }

    @BceAsyncWork(name = "validateAndSetSubnetUuid", level = Level.MIDDLE)
    public Boolean validateAndSetSubnetUuid(PodPurchaseRequest podPurchaseRequest,
                                            Map<String, ZoneMapDetail> zoneMap,
                                            Map<String, SubnetVo> subnetVoMap,
                                            Map<String, VpcVo> vpcVoMap,
                                            boolean isCreateWithEip) {
        podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMap, subnetVoMap, vpcVoMap, isCreateWithEip);
        return true;
    }

    @BceAsyncWork(name = "getZoneResourceDetail", level = Level.MIDDLE)
    public ZoneAndAuthorityList getZoneResourceDetail() {
        return logicalZoneResourceServiceV2.listZoneResourceV2();
    }

    @BceAsyncWork(name = "getImageTags", level = Level.MIDDLE)
    public ImageTags getImageTags(UserImage image, CceImageClient cceImageClient) {
        return cceImageClient.listImageVersions(image.getNamespace(), image.getName());
    }

    @BceAsyncWork(name = "createImageCache", level = Level.MIDDLE)
    public void createImageCache(CNImageCacheRequest request, ContainerManagerClient client) {
        client.createImageCache(request);
    }


    @BceAsyncWork(name = "queryImageCache", level = Level.MIDDLE)
    public ImageCacheTaskStatus queryImageCache(String taskId, ContainerManagerClient client) {
        return client.getImageCache(taskId);
    }
}
