package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class OpsCreateRequest {

    private String podId;
    private String opsType;
    private String opsValue;
    private String bucket;
    private String nodeName;
}
