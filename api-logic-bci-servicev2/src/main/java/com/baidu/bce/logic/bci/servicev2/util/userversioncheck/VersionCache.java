package com.baidu.bce.logic.bci.servicev2.util.userversioncheck;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.lang.StringUtils;

import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.CacheUtil;
import org.springframework.stereotype.Service;

@Service("VersionCache")
public class VersionCache {
    private static final Logger LOGGER = LoggerFactory.getLogger(VersionCheckAspect.class);

    @Value("${user.version.cache.expiration.time.hour:1}")
    private long userVersionCacheExpirationTimeHour;

    private long expirationTime = 60L * 1000 * 60 * 1;

    private CacheUtil<String, Integer> v1UserCache = new CacheUtil<>();
    private CacheUtil<String, Integer> v2UserCache = new CacheUtil<>();

    @Autowired
    PodServiceV2 podService;

    public enum Version {
        ABNORMAL,
        V1,
        V2,
    }

    // only for TEST
    public void addUserToV1Cache(String userId, long expirationTime) {
        v1UserCache.put(userId, 1, expirationTime);
    }
    // only for TEST
    public void addUserToV2Cache(String userId, long expirationTime) {
        v2UserCache.put(userId, 2, expirationTime);
    }

    public void clearVersionCache() {
        v1UserCache.clear();
        v2UserCache.clear();
    }

    public void clearUserVersionCache(String userId) {
        v1UserCache.delete(userId);
        v2UserCache.delete(userId);
    }

    public Version getUserVersion(String accountId) {
        if (StringUtils.isEmpty(accountId)) {
            LOGGER.error("user:{} get version error, because user is empty.", accountId);
            return Version.ABNORMAL;
        }
        try {
            // 1.v1缓存存在 && v2缓存不存在, 则一定为v1用户.
            if (v1UserCache.get(accountId) != null && v2UserCache.get(accountId) == null) {
                LOGGER.debug("user:{} is V1 user, because user is in v1cache.", accountId);
                return Version.V1;
            }
            // 2.v2缓存存在 && v1缓存不存在, 则一定为v2用户.
            if (v2UserCache.get(accountId) != null && v1UserCache.get(accountId) == null) {
                LOGGER.debug("user:{} is V2 user, because user is in v2cache.", accountId);
                return Version.V2;
            }
            // 3.v2缓存存在 && v1缓存存在, 异常.
            if (v1UserCache.get(accountId) != null && v2UserCache.get(accountId) != null) {
                LOGGER.error("user:{} get version error, because user is in v1cache and v2cache.", accountId);
                return Version.ABNORMAL;
            }
            // 4.Cache不存在, 则查询云桥判定是否为v1用户
            if (podService.isV1AccountId().isIsv1()) {
                // v1用户, 需要将用户加入v1UserCache
                v1UserCache.put(accountId, 1, expirationTime);
                LOGGER.debug("user:{} is V1 user, because user is in v1whitelist.", accountId);
                return Version.V1;
            }
            // 5.为V2 用户
            if (podService.addCceClusterForUser(accountId)) {
                v2UserCache.put(accountId, 2, expirationTime);
                LOGGER.debug("user:{} is V2 user, because user is not in v1cache/v1whitelist " +
                        " and add user v2 cceCluster success.", accountId);
                return Version.V2;
            } else {
                LOGGER.error("user:{} get version fail," +
                        " because user is not in v1cache/v1whitelist and add user v2 cceCluster fail.", accountId);
                return Version.ABNORMAL;
            }
        } catch (Exception e) {
            LOGGER.error("user:{} get version error {}, because catch exception.", accountId, e.getMessage());
            return Version.ABNORMAL;
        }
    }
}
