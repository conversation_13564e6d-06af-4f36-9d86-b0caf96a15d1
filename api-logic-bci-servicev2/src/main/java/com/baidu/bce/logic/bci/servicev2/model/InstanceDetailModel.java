package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class InstanceDetailModel extends InstanceModel {
    private Volume volume;
    private List<ContainerDetailModel> containers;
    private List<ContainerDetailModel> initContainers;
    private List<SecurityGroupModel> securityGroups;
    private VpcModel vpc;
    private SubnetModel subnet;
    // private Long terminationGracePeriodSeconds;

    public void buildVolumeFromPodDetail(PodDetail podDetail) {
        Volume volume = new Volume();
        volume.setNfs(podDetail.getNfs());
        volume.setEmptyDir(podDetail.getEmptyDir());
        volume.setConfigFile(podDetail.getConfigFile());
        this.setVolume(volume);
    }

    public void buildContainersFromPodDetail(PodDetail podDetail) {
        List<ContainerDetailModel> containers = new ArrayList<ContainerDetailModel>();
        List<ContainerDetailModel> initContainers = new ArrayList<ContainerDetailModel>();
        if (podDetail.getContainers() != null) {
            for (PodDetail.ContainerDetail container : podDetail.getContainers()) {
                ContainerDetailModel containerDetailModel = new ContainerDetailModel(container);
                if (container.getContainerType().equalsIgnoreCase(ContainerType.INIT.getType())) {
                    initContainers.add(containerDetailModel);
                } else {
                    containers.add(containerDetailModel);
                }
            }
        }
        this.setContainers(containers);
        this.setInitContainers(initContainers);
    }

    public InstanceDetailModel(PodDetail podDetail) {
        super(podDetail);
        buildVolumeFromPodDetail(podDetail);
        buildContainersFromPodDetail(podDetail);
        List<SecurityGroupModel> securityGroupModels = new ArrayList<SecurityGroupModel>();
        if (podDetail.getSecurityGroups() != null && !podDetail.getSecurityGroups().isEmpty()) {
            for (SimpleSecurityGroupVO simpleSecurityGroupVO : podDetail.getSecurityGroups()) {
                SecurityGroupModel securityGroupModel = new SecurityGroupModel(simpleSecurityGroupVO);
                securityGroupModels.add(securityGroupModel);
            }
        } else if (podDetail.getSecurityGroup() != null) {
            SecurityGroupModel securityGroupModel = new SecurityGroupModel(podDetail.getSecurityGroup());
            securityGroupModels.add(securityGroupModel);
        }

        this.setSecurityGroups(securityGroupModels);
        VpcModel vpc = new VpcModel(podDetail.getVpc());
        this.setVpc(vpc);
        SubnetModel subnet = new SubnetModel(podDetail.getSubnet());
        this.setSubnet(subnet);
        // 老接口并没有设置terminationGracePeriodSeconds，所以OpenAPI暂时也不设置
        // this.setTerminationGracePeriodSeconds(podDetail.getTerminationGracePeriodSeconds());
    }
}
