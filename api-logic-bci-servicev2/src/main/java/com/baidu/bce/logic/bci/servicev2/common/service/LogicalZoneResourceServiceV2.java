package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthorityList;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service
public class LogicalZoneResourceServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogicalZoneResourceServiceV2.class);

    @Value("${bce.enable.debug:false}")
    private boolean debug;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private RegionConfiguration regionConfiguration;

    public ZoneAndAuthorityList listZoneResourceV2() {
        return logicPodClientFactory.createZoneClient(getAccountId()).listZoneResourceV2();
    }

}