package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecyleComplete;
import com.baidu.bce.logic.bci.servicev2.constant.StateMachineEvent;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sCluster;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.ConfigFile;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.statemachine.StateMachine;
import com.baidu.bce.logic.bci.servicev2.util.EipUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import io.kubernetes.client.openapi.models.V1Pod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ResourceRecycleSyncServiceV2 extends SyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceRecycleSyncServiceV2.class);

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private EipUtil eipUtil;

    @Value("${job.pod.resource.recycle.enable:true}")
    private boolean jobPodResourceRecycleEnable;

    @Value("${pod.resource.recycle.timeout.tidal:10}")
    private int tidalPodTimeout;

    @Value("${pod.resource.recycle.timeout.ordinary:60}")
    private int ordinaryPodTimeout;

    @Autowired
    private StateMachine stateMachine;

    private long resourceRecycleSchedulerRunTimes = 0;

    public void syncResourceRecycle() {
        syncPodResourceRecycle();
    }

    public void syncPodResourceRecycle() {
        List<PodPO> podPOS = podDao.getResourceRecyclePod();
        ArrayList<String> podIds = getResourceRecyclePodIds(podPOS);
        resourceRecycleSchedulerRunTimes++;
        long resourceRecycleSucceedCount = 0;
        long resourceRecycleExceptionCount = 0;
        LOGGER.debug("start syncPodResourceRecycle runtimes {}, need recyclePods count {} podIds {}",
                resourceRecycleSchedulerRunTimes, podPOS.size(), podIds.toString());
        for (PodPO podPO : podPOS) {
            if (podPO == null) {
                continue;
            }
            String podId = podPO.getPodId();
            String userId = podPO.getUserId();
            LOGGER.debug("syncPodResourceRecycle runtimes {} podId {} userId {}", resourceRecycleSchedulerRunTimes, podId, userId);
            try {
                // 解绑bci实例tag
                podService.unBindTags(podPO);
                LOGGER.debug("syncPodResourceRecycle runtimes {} unbind tags success, podId {} userId {}", resourceRecycleSchedulerRunTimes, podId, userId);
                V1Pod pod = k8sService.tryGetPod(podPO.getUserId(), podPO.getPodId());
                String resourceRecycleReason = podPO.getResourceRecycleReason();
                if (podPO.getDeleted() == 0) {
                    // deleted == 0
                    if (pod == null || pod.getMetadata() == null || pod.getStatus() == null) {
                        LOGGER.debug("syncPodResourceRecycle runtimes {} not find pod {} in k8s cluster, and release eip {}",
                                resourceRecycleSchedulerRunTimes, podPO.getPodId(), podPO.getPublicIp());
                        // 底层的 k8s pod不存在了，需要停止计费。如果此前已经停止计费了，不再重复停止计费。
                        String latestChargeStatus =
                                podChargeStatusDao.getChargeStatusWhichCreatedTimeIsNewest(podPO.getPodUuid());
                        if (!PodConstants.NO_CHARGE.equalsIgnoreCase(latestChargeStatus)) {
                            takeNoCharge(podPO);
                        }
                        // 防止后端k8s异常获取不到pod信息
                        boolean resourceRecycleTimeout =  isResourceRecycleTimeout(podPO);
                        boolean zoneNoResourceSpecification = resourceRecycleReason.equals(ResourceRecycleReason.ZONE_NO_RESOURCE_SPECIFICATION.toString());

                        if (resourceRecycleTimeout || zoneNoResourceSpecification) {
                            if (resourceRecycleTimeout) {
                                LOGGER.debug("syncPodResourceRecycle runtimes {}, pod {} resource recycle timeout, " +
                                "release eip {} and setResourceRecycleComplete",
                        resourceRecycleSchedulerRunTimes, podPO.getPodId(), podPO.getPublicIp());
                            }
                            if (zoneNoResourceSpecification) {
                                LOGGER.debug("syncPodResourceRecycle runtimes {}, pod {} resource recycle reason is zone no resource specification, " +
                                "release eip {} and setResourceRecycleComplete",
                        resourceRecycleSchedulerRunTimes, podPO.getPodId(), podPO.getPublicIp());
                            }

                            if (!eipUtil.recycleEip(podPO)) {
                                LOGGER.error("syncPodResourceRecycle runtimes {} recycle eip failed. eip {} pod {} {}",
                                        resourceRecycleSchedulerRunTimes, podPO.getPublicIp(), podPO.getPodId(), podPO.getInternalIp());
                                continue;
                            }
                            assureConfigMapDeleted(podPO);
                            boolean recycleResult = setResourceRecycleComplete(podPO);
                            if (recycleResult) {
                                resourceRecycleSucceedCount++;
                            }
                            LOGGER.debug("syncPodResourceRecycle runtimes {} resource recycle result {}, pod {}, userId {}",
                                    resourceRecycleSchedulerRunTimes, recycleResult, podId, userId);
                        }
                        continue;

                    }
                    // 根据ResourceRecycleReason判断是否为job类型负载
                    if (resourceRecycleReason.equals(ResourceRecycleReason.JOB_POD_COMPLETE.toString())) {
                        // 若job类型负载未开启删除，则跳过该pod
                        if (!jobPodResourceRecycleEnable) {
                            continue;
                        }
                        int releaseDurationMinute = podPO.getDelayReleaseDurationMinute();
                        boolean releaseSucceed = podPO.isDelayReleaseSucceeded();
                        // 判断pod是否指定延迟删除
                        if (releaseDurationMinute > 0) {
                            // 判断是否达到延迟删除时间
                            int afterMinuteTime =
                                    (int) ((System.currentTimeMillis() - podPO.getResourceRecycleTimestamp()) / (60 * 1000));
                            if (afterMinuteTime < releaseDurationMinute) {
                                // 判断是否对succeed的pod生效延迟删除
                                if (releaseSucceed || podPO.getStatus().equalsIgnoreCase(BciStatus.FAILED.getStatus())) {
                                    LOGGER.debug("syncPodResourceRecycle runtimes {}  need delay release {}, needDelay:{} " +
                                                    "nowDelay:{}",
                                            resourceRecycleSchedulerRunTimes, podPO.getPodId(), releaseDurationMinute
                                            , afterMinuteTime);
                                    continue;
                                }
                            }
                        }
                    } 
                } else if (podPO.getDeleted() == 1) {
                    // deleted == 1 => 必须删除
                    LOGGER.debug("syncPodResourceRecycle runtimes {} begin resource rescycle pod {}",
                            resourceRecycleSchedulerRunTimes, podPO.getPodId());
                    if (pod == null || pod.getMetadata() == null || pod.getStatus() == null) {
                        LOGGER.debug("syncPodResourceRecycle runtimes {} not find pod {} in k8s cluster",
                                resourceRecycleSchedulerRunTimes, podPO.getPodId());
                        // 底层的 k8s pod不存在了，需要停止计费。如果此前已经停止计费了，不再重复停止计费。
                        String latestChargeStatus =
                                podChargeStatusDao.getChargeStatusWhichCreatedTimeIsNewest(podPO.getPodUuid());
                        if (!PodConstants.NO_CHARGE.equalsIgnoreCase(latestChargeStatus)) {
                            takeNoCharge(podPO);
                        }
                        // 此处判断防止后端k8s pod还未创建出来了，k8s不存在不代表资源被清理了。
                        // 此处增加pod创建延迟deadline处理(默认20min)
                        if (isResourceRecycleTimeout(podPO)) {
                            LOGGER.debug("syncPodResourceRecycle runtimes {} pod {} resource recycle timeout, release" +
                                            " eip {} and setResourceRecycleComplete",
                                    resourceRecycleSchedulerRunTimes, podPO.getPodId(), podPO.getPublicIp());
                            if (!eipUtil.recycleEip(podPO)) {
                                LOGGER.error("syncPodResourceRecycle runtimes {} recycle eip failed. eip {} pod {} {}",
                                        resourceRecycleSchedulerRunTimes, podPO.getPublicIp(), podPO.getPodId(),
                                        podPO.getInternalIp());
                                continue;
                            }
                            assureConfigMapDeleted(podPO);
                            // 设置资源回收完成
                            boolean recycleResult = setResourceRecycleComplete(podPO);
                            if (recycleResult) {
                                resourceRecycleSucceedCount++;
                            }
                            LOGGER.debug("syncPodResourceRecycle runtimes {} resource recycle result {}, pod {}, userId {}",
                                    resourceRecycleSchedulerRunTimes, recycleResult, podId, userId);
                        }
                        continue;
                    }
                } else {
                    LOGGER.error("syncPodResourceRecycle runtimes {} pod {} deleted {} invalid",
                            resourceRecycleSchedulerRunTimes, podPO.getPodId(), podPO.getDeleted());
                    continue;
                }
                // 删除k8s pod
                String podName = pod.getMetadata().getName();
                String ns = pod.getMetadata().getNamespace();
                String vkPodName = podPO.getName();
                boolean deletePodResult = k8sService.deletePod(podName, ns);
                LOGGER.debug("syncPodResourceRecycle runtimes {} delete pod [podId {}, PodName {}, ns {}], "
                                + "resource recycle reason {}, delete pod result {}",
                        resourceRecycleSchedulerRunTimes, podName, vkPodName, ns, resourceRecycleReason,
                        deletePodResult);
                // release eip if exist
                LOGGER.debug("syncPodResourceRecycle runtimes {} delete pod {} eip {} internal ip {}",
                        resourceRecycleSchedulerRunTimes, podName, podPO.getPublicIp(), podPO.getInternalIp());
                boolean reclcleEipResult = eipUtil.recycleEip(podPO);
                if (!reclcleEipResult) {
                    LOGGER.error("syncPodResourceRecycle runtimes {} recycle eip failed. eip {} pod {} {}",
                            resourceRecycleSchedulerRunTimes, podPO.getPublicIp(), podPO.getPodId(),
                            podPO.getInternalIp());
                }
                // 删除由BCI创建的bls任务
                deleteBLSTasks(podPO);
                if (deletePodResult && reclcleEipResult) {
                    // 设置资源回收完成
                    boolean recycleResult = setResourceRecycleComplete(podPO);
                    if (recycleResult) {
                        resourceRecycleSucceedCount++;
                    }
                    LOGGER.debug("syncPodResourceRecycle runtimes {} resource recycle result {}, pod {}, userId {}",
                            resourceRecycleSchedulerRunTimes, recycleResult, podId, userId);
                }
                takeNoCharge(podPO);
            } catch (Exception e) {
                // 这里加argus报警
                resourceRecycleExceptionCount++;
                LOGGER.error("failed to syncPodResourceRecycle with exception info runtimes {}, podId {}, userId {}, " +
                                "error {}",
                        resourceRecycleSchedulerRunTimes, podId, userId, e.toString());
            }
        }
        LOGGER.debug("end syncPodResourceRecycle and summary info runtimes {}, need recyclePods count {}, " +
                        "resourceRecycleSucceedCount {}, resourceRecycleExceptionCount {}",
                resourceRecycleSchedulerRunTimes, podPOS.size(), resourceRecycleSucceedCount, resourceRecycleExceptionCount);
    }

    private void assureConfigMapDeleted(PodPO podPO) {
        if (podPO == null) {
            return;
        }
        LOGGER.debug("assureConfigMapDeleted podId: {}", podPO.getPodId());
        String podName = podPO.getPodId();
        String namespace = podPO.getUserId();
        try {
            // 判断configmap是否泄露，如果泄露则删除configmap
            // configmap泄露的场景：k8s因为oom、资源承压驱逐、容器异常退出等原因主动对pod进行删除，但configmap没有删掉。
            List<ConfigFile> configMapList = k8sService.getConfigFilesFromDBPodExtra(podPO);
            if (configMapList == null || CollectionUtils.isEmpty(configMapList)) {
                LOGGER.debug("assureConfigMapDeleted podId: {} configMapList is null", podName);
                return;
            }
            for (int i = 0; i < configMapList.size(); i++) {
                String configMapName = PodUtils.buildConfigMapName(podName, configMapList.get(i).getName());
                K8sCluster k8sCluster = k8sService.getClusterByUserId(namespace);
                k8sCluster.deleteConfigMap(configMapName, namespace);
            }
        } catch (Exception e) {
            LOGGER.error("assureConfigMapDeleted podId: {} delete configmap error", podName, e);
        }
    }

    private boolean setResourceRecycleComplete(PodPO podPO) {
        String podId = podPO.getPodId();
        if (!stateMachine.trigger(podPO, StateMachineEvent.RESOURCE_RECYCLE_COMPLETE, null)) {
            LOGGER.warn("setResourceRecycleComplete: pod state machine trigger failed, podId:{} event:{}",
                    podId, StateMachineEvent.RESOURCE_RECYCLE_COMPLETE.toString());
            return false;
        }
        return true;
    }

    private void takeNoCharge(PodPO podPO) {
        // 对于cpc实例，计费模块会自动检测实例状态变化了，资源回收后自动不再计费。
        // 对于cpt1实例，每一次状态变化需要主动推送，所以这里触发一次计费状态的推送。
        if (podPO.getCpt1()) {
            // 计算 chargeState 的时候，依赖 podPO 中的 ResourceRecycleComplete，只有 ResourceRecycleComplete = 1，chargeState 才是 noCharge。
            // TODO: 后续改造 chargeStatus 函数，不依赖 podPO 透传 ResourceRecycleComplete 等信息。
            int oldResourceRecycleComplete = podPO.getResourceRecycleComplete();
            podPO.setResourceRecycleComplete(ResourceRecyleComplete.COMPLETED);
            LOGGER.debug("[call billing] chargeStatus in takeNoCharge for pod {}", podPO.getPodUuid());
            chargeStatus(podPO.getStatus(), podPO, new Timestamp(new Date().getTime()), false);
            podPO.setResourceRecycleComplete(oldResourceRecycleComplete);
        }
    }

    private ArrayList<String> getResourceRecyclePodIds(List<PodPO> podPOList) {
        ArrayList<String> podIds = new ArrayList<>();
        for (PodPO podPO : podPOList) {
            podIds.add(podPO.getPodId());
        }
        return podIds;
    }

    // 删除由BCI创建的bls任务
    private boolean deleteBLSTasks(PodPO podPO) {
        boolean deleteBLSTaskResult = true;
        String createdBLSTasksID = podPO.getCreatedBLSTasksID();
        if (!StringUtils.isEmpty(createdBLSTasksID)) {
            deleteBLSTaskResult = podService.deleteBLSTasks(podPO);
            if (deleteBLSTaskResult) {
                LOGGER.debug("syncJobPodRecycle delete userId {} BLS Tasks {} success",
                        podPO.getUserId(), podPO.getCreatedBLSTasksID());
            } else {
                LOGGER.error("syncJobPodRecycle delete userId {} BLS Tasks {} failed",
                        podPO.getUserId(), podPO.getCreatedBLSTasksID());
            }
        }
        return deleteBLSTaskResult;
    }

    private boolean isResourceRecycleTimeout(PodPO podPO) {
        long currentTime  = System.currentTimeMillis();
        int durationTime =
                (int) ((currentTime - podPO.getResourceRecycleTimestamp()) / (60 * 1000));
        if (podPO.isTidal() && durationTime > tidalPodTimeout) {
            LOGGER.debug("syncJobPodRecycle type: tidal, " +
                            "resourceRecycleTimeStamp: {}, currentTimeStamp: {}, duration: {} minutes.",
                    podPO.getResourceRecycleTimestamp(), currentTime, durationTime);
            return true;
        } else if (!podPO.isTidal() && durationTime > ordinaryPodTimeout) {
            LOGGER.debug("syncJobPodRecycle type: ordinary, " +
                            "resourceRecycleTimeStamp: {}, currentTimeStamp: {}, duration: {} minutes.",
                    podPO.getResourceRecycleTimestamp(), currentTime, durationTime);
            return true;
        }
        return false;
    }

}
