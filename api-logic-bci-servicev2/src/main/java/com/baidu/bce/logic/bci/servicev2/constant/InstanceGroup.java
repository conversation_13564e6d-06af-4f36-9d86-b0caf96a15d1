package com.baidu.bce.logic.bci.servicev2.constant;

public class InstanceGroup {
    public static final String INSTANCE_GROUP_CM_NS = "kube-system";
    public static final String INSTANCE_GROUP_CM_NAME = "instance-group-config";
    public static final String INSTANCE_GROUP_CM_KEY = "config.json";
    public static final String INSTANCE_GROUP_ACCOUNT_WHITE_LIST_CM_DATA_KEY = "account-white-list";
    public static final String INSTANCE_GROUP_FOR_STARGZ_IMAGE = "stargz-image-node-group";

    public static final String A1024GCGPU  = "baidu.com/a10_24g_cgpu";
    public static final String A10040GCGPU = "baidu.com/a100_40g_cgpu";
    public static final String A10080GCGPU = "baidu.com/a100_80g_cgpu";
    public static final String A3024GCGPU  = "baidu.com/a30_24g_cgpu";
    public static final String RTX3070CGPU = "baidu.com/rtx_3070_cgpu";
    public static final String RTX3080CGPU = "baidu.com/rtx_3080_cgpu";
    public static final String RTX3090CGPU = "baidu.com/rtx_3090_cgpu";
    public static final String RTX4090CGPU = "baidu.com/rtx_4090_cgpu";
    public static final String T416GCGPU   = "baidu.com/t4_16g_cgpu";
    public static final String V10032GCGPU = "baidu.com/v100_32g_cgpu";
    public static final String R200CGPU    = "baidu.com/R200_cgpu";
}
