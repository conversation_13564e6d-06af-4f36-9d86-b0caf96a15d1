package com.baidu.bce.logic.bci.servicev2.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CacheUtil<K, V> {
    private static final Logger LOGGER = LoggerFactory.getLogger(CacheUtil.class);
    private long defaultExpirationTime = 60 * 1000L;
    private final ConcurrentMap<K, CacheItem<V>> cacheMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    public CacheUtil() {
        startCleanupTask();
    }

    public CacheUtil(long defaultExpirationTime) {
        this.defaultExpirationTime = defaultExpirationTime;
        startCleanupTask();
    }

    private void startCleanupTask() {
        executorService.scheduleAtFixedRate(() -> {
            long currentTime = System.currentTimeMillis();
            for (Map.Entry<K, CacheItem<V>> entry : cacheMap.entrySet()) {
                if (currentTime > entry.getValue().getExpiration()) {
                    cacheMap.remove(entry.getKey());
                }
            }
        }, 60, 10, TimeUnit.SECONDS);
    }

    public void setDefaultExpirationTime(long defaultExpirationTime) {
        this.defaultExpirationTime = defaultExpirationTime;
    }

    public long getDefaultExpirationTime() {
        return this.defaultExpirationTime;
    }

    public void put(K key, V value) {
        put(key, value, this.defaultExpirationTime);
    }

    public void put(K key, V value, long expirationTime) {
        long expiration = System.currentTimeMillis() + expirationTime;
        cacheMap.put(key, new CacheItem<>(value, expiration));
    }

    public V get(K key) {
        CacheItem<V> cacheItem = cacheMap.get(key);
        if (cacheItem == null || System.currentTimeMillis() > cacheItem.getExpiration()) {
            cacheMap.remove(key);
            return null;
        }
        return cacheItem.getValue();
    }
    
    public void delete(K key) {
        cacheMap.remove(key);
    }

    public int size() {
        return cacheMap.size();
    }

    public void clear() {
        cacheMap.clear();
    }

    private static class CacheItem<V> {
        private final V value;
        private final long expiration;

        CacheItem(V value, long expiration) {
            this.value = value;
            this.expiration = expiration;
        }

        V getValue() {
            return value;
        }

        long getExpiration() {
            return expiration;
        }
    }
}