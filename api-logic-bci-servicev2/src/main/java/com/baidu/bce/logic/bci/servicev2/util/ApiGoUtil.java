package com.baidu.bce.logic.bci.servicev2.util;

import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Ordering;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

/**
 * 此类主要是调用apiGO生成鉴权信息
 * 生成鉴权文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/HXFtYvbMQj/KMVbG3K2Cd/n6EMjaJlnP3vEu
 * Java 接入apiGo : https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/HXFtYvbMQj/KMVbG3K2Cd/r6IpeFcy9I2hEU
 * 引入pom 文件版本不兼容，因此将鉴权信息单独抽出工具类；
 */
public class ApiGoUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiGoUtil.class);

    private static final String HMAC_AUTH_SIGN_TEMP = "%s\n%s\n%s\n%s\n%s\n%s\n";

    private static final String SIGNED_HEADER_TEMP = "hmac-random:%s";
    private static final ThreadLocal<DateFormat> THREAD_LOCAL_DATE_FORMAT = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
            sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
            return sdf;
        }
    };

    public static Map<String, String> buildHmacSignHeader(String appKey, String secretKey,
                                                          String uri, String method, List<NameValuePair> queryParams,
                                                          boolean encoding) {

        Map<String, String> headerMap = new HashMap<>();
        Assert.hasLength(appKey, "appKey cannot null or empty");
        Assert.hasLength(secretKey, "secretKey cannot null or empty");
        Assert.hasLength(uri, "uri cannot null or empty");
        Assert.hasLength(method, "method cannot null or empty");
        method = StringUtils.upperCase(method);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("receive hmac sign request, appKey: {}, secretKey: {}, uri: {}, method: {}",
                    appKey, secretKey, uri, method);
        }
        String queryString = genCanonicalQueryString(queryParams, encoding);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("gen canonical query string success, queryString: {}", queryString);
        }
        String date = THREAD_LOCAL_DATE_FORMAT.get().format(new Date());
        String random = RandomStringUtils.randomAlphanumeric(32);
        String signedHeaderString = String.format(SIGNED_HEADER_TEMP, random);
        String beforeHashStr =
                String.format(HMAC_AUTH_SIGN_TEMP, method, uri, queryString, appKey, date, signedHeaderString);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("before hash str gen success, beforeHashStr: {}", beforeHashStr);
        }

        Mac sha256Hmac = HmacUtils
                .getInitializedMac(HmacAlgorithms.HMAC_SHA_256,
                        org.apache.commons.codec.binary.StringUtils.getBytesUtf8(secretKey));
        String sign = Base64.encodeBase64String(sha256Hmac
                .doFinal(org.apache.commons.codec.binary.StringUtils.getBytesUtf8(beforeHashStr)));

        headerMap.put("encode_uri_param", Boolean.FALSE.toString());
        headerMap.put("Date", date);
        headerMap.put("hmac-random", random);
        headerMap.put("x-hmac-signature", sign);
        headerMap.put("x-hmac-access-key", appKey);

        return headerMap;
    }

    @SneakyThrows
    public static String encodeUtf8(String raw) {
        return URLEncoder.encode(raw, StandardCharsets.UTF_8.name());
    }

    private static String genCanonicalQueryString(List<NameValuePair> queryParams, boolean encoding) {
        ImmutableListMultimap.Builder<String, String> urlParamsMapBuilder =
                new ImmutableListMultimap.Builder<String, String>()
                        .orderKeysBy(Ordering.natural()).orderValuesBy(Ordering.natural());
        if (!CollectionUtils.isEmpty(queryParams)) {
            for (NameValuePair queryParam : queryParams) {
                String key = encoding ? encodeUtf8(queryParam.getName()) : queryParam.getName();
                String value = encoding ? encodeUtf8(queryParam.getValue()) : queryParam.getValue();
                urlParamsMapBuilder.put(key, value);
            }
        }
        ImmutableListMultimap<String, String> queryParamMap = urlParamsMapBuilder.build();

        StringBuilder canonicalQueryParams = new StringBuilder();
        for (Map.Entry<String, String> entry : queryParamMap.entries()) {
            if (0 != canonicalQueryParams.length()) {
                canonicalQueryParams.append("&");
            }
            canonicalQueryParams.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return canonicalQueryParams.toString();
    }
}
