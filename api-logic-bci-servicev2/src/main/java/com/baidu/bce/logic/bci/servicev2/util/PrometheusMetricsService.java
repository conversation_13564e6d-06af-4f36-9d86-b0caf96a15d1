package com.baidu.bce.logic.bci.servicev2.util;

import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;
import io.prometheus.client.Summary;
import io.prometheus.client.exporter.HTTPServer;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Data
@Component
public class PrometheusMetricsService {

    public enum PodCreatePhase {
        SYNC("sync"),
        ASYNC("async");

        private final String description;

        PodCreatePhase(String description) {
            this.description = description;
        }

        public String toString() {
            return description;
        }
    };

    public enum PodCreateResult {
        SUCCESS("success"),
        FAILED("failed");

        private final String description;

        PodCreateResult(String description) {
            this.description = description;
        }

        public String toString() {
            return description;
        }
    }
    private static final Logger LOGGER = LoggerFactory.getLogger(PrometheusMetricsService.class);

    @Value("${bci.metrics.service.port:8786}")
    private int metricsServerPort;

    private static final Map<String, String> BCI_LOGIC_INTERFACE_MAP;

    private static Set<String> activeVisitors = new HashSet<>();

    static {
        Map<String, String> tempMap = new HashMap<String, String>() {{
            put("BciControllerV2_listPodByPage", "listPod");
            put("BciControllerV2_listPodByMarker", "listPod");
            put("BciControllerV2_listPodLightByMarker", "listPod");
            put("BciControllerV2_listPodByUpdatedTime", "listPod");
            put("BciControllerV2_podDetail", "describePod");
            put("BciControllerV2_podDetailWithLight", "describePod");
            put("BciControllerV2_podBatchDetailWithLight", "describePod");
            put("BciControllerV2_downloadConfigFile", "downloadConfigFile");
            put("BciControllerV2_deletePod", "deletePod");
            put("BciControllerV2_createInstance", "createPod");
            put("BciControllerV2_syncDsContainers", "syncDsContainers");
            put("BciControllerV2_listCCRImages", "listImage");
            put("BciControllerV2_listUserImages", "listImage");
            put("BciControllerV2_dockerHubImageList", "listImage");
            put("BciControllerV2_listOfficialImages", "listImage");
            put("BciControllerV2_logDownload", "downloadLog");
            put("BciControllerV2_bind", "bindEip");
            put("BciControllerV2_unBind", "unbindEip");
            put("BciControllerV2_listPodEventByPage", "listPodEvent");
            put("BciControllerV2_webshell", "webshell");
            put("BciControllerV2_isV2", "getUserVersion");
            put("AdminControllerV2_clearVersionCache", "deleteUserVersionCache");
            put("AdminControllerV2_clearVersionCache", "deleteUserVersionCache");
            put("BciControllerV2_listZone", "listZone");
            put("BciControllerV2_listMetricsByShortIds", "listPodMetrics");
            put("BciControllerV2_listMetricsSummary", "listPodMetrics");
            put("BciOpenApiController_createContainerGroup", "createPod");
            put("BciOpenApiController_describeContainerGroups", "listPod");
            put("BciOpenApiController_describeContainerGroupDetail", "describePod");
            put("BciOpenApiController_deleteContainerGroup", "deletePod");
            put("BciOpenApiController_mDeleteContainerGroup", "deletePod");
            put("BciOpenApiController_updateInstanceConfigFile", "updateConfigFile");
            put("BciInnerApiController_virtualKubeletClusterReport", "vkClusterReport");
            put("BciOpenApiOverviewController_getPreviewPodCapacity", "getPreviewPodCapacity");
            put("BciOpsController_createInstance", "createOpsTask");
            put("BciOpsController_record", "describeOpsTask");
            put("ImageCacheControllerV2_createImageCache", "createImageCache");
            put("ImageCacheControllerV2_descirbeImageCaches", "describeImageCache");
            put("ImageCacheControllerV2_deleteImageCache", "deleteImageCache");
            put("ImageCacheControllerV2_batchDeleteImageCache", "deleteImageCache");
            put("ImageControllerV2_createImageCache", "createImageCache");
            put("ImageControllerV2_getImageCache", "listImageCache");
            put("ReservedInstanceController_listReservedInstanceByPage", "listReservedInstance");
            put("ReservedInstanceController_createReservedInstance", "createReservedInstance");
            put("ReservedInstanceController_listReservedInstanceSpecs", "listReservedInstanceSpecs");
            put("ReservedInstanceController_listPodByReservedInstanceId", "listPodByReservedInstanceId");
            put("TagControllerV2_TagControllerV2", "listTag");
            put("BCMControllerV2_listPods", "bcmListPod");
            put("BCMControllerV2_podDetail", "bcmPodDetail");
            put("BCMControllerV2_podContainers", "bcmPodContainers");
            put("BCMControllerV2_listPodForResGroup", "bcmListPodForResGroup");
        }};
        BCI_LOGIC_INTERFACE_MAP = Collections.unmodifiableMap(tempMap);
    }

    private static final long APPLICATION_START_TIME = System.currentTimeMillis();

    /**
     * 注意:
     *  1. 一定不要修改已有prometheus暴漏的指标名称,因为当前的noah3.0监控系统已经对这些指标进行了监控
     *  2. 一旦修改现有的指标名称可能会导致监控系统无法正常监控和报警
     *  3. 如果有修改需求,建议新增指标并配置对应的监控
     * */

    public static final Gauge APPLICATION_RUNTIME_SECONDS = Gauge.build()
            .name("application_runtime_seconds")
            .help("application runtime seconds")
            .register();
    public static final Counter SYNC_POD_CREATE_SUCCESS_COUNTER = Counter.build()
            .name("sync_pod_create_success")
            .help("sync pod create success")
            .register();

    public static final Counter SYNC_POD_CREATE_FAILED_COUNTER = Counter.build()
            .name("sync_pod_create_failed")
            .help("sync pod create failed")
            .register();

    public static final Counter ASYNC_POD_CREATE_SUCCESS_COUNTER = Counter.build()
            .name("async_pod_create_success")
            .help("async pod create success")
            .register();

    public static final Counter ASYNC_POD_CREATE_FAILED_COUNTER = Counter.build()
            .name("async_pod_create_failed")
            .help("async pod create failed")
            .register();

    public static final Counter POD_CREATE_COUNTER = Counter.build()
            .name("pod_create_metric")
            .help("pod create metric counter")
            .labelNames("user_id", "phase", "result")
            .register();

    public static final Counter HTTP_REQUEST_USER = Counter.build()
            .name("http_request_user")
            .help("http request user")
            .labelNames("request_user")
            .withExemplars()
            .register();

    public static final Counter HTTP_ALL_REQUEST_API = Counter.build()
            .name("http_all_request_api")
            .help("http all request api")
            .register();

    public static final Counter HTTP_REQUEST_API = Counter.build()
            .name("http_request_api")
            .help("http request api")
            .labelNames("request_api")
            .register();

    public static final Counter HTTP_REQUEST_UNIQUE_VISITOR = Counter.build()
            .name("http_request_unique_visitor")
            .help("http request unique visitor")
            .register();

    public static final Counter HTTP_REQUEST_METHOD = Counter.build()
            .name("http_request_method")
            .help("http request method")
            .labelNames("request_method")
            .register();

    public static final Counter HTTP_RESPONSE_STATUS_CODE = Counter.build()
            .name("http_response_status_code")
            .help("http response status code")
            .labelNames("response_status_code")
            .register();

    public static final Counter HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_COUNTER = Counter.build()
            .name("http_response_status_code_distribution_counter")
            .help("http response status code distribution counter")
            .labelNames("response_status_code")
            .register();

    public static final Counter HTTP_REQUEST_API_AND_HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_COUNTER = Counter.build()
            .name("http_request_api_and_http_response_status_code_distribution_counter")
            .help("http request api and http response status code distribution counter")
            .labelNames("request_api", "response_status_code")
            .register();

    public static final Summary HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_AND_RESPONSE_STATUS_CODE_SUMMARY =
            Summary.build()
            .name("http_response_status_code_distribution_and_seconds_latency_histogram_summary")
            .help("http response status code distribution and seconds latency histogram summary")
            .labelNames("response_status_code")
            .quantile(0.2, 0.01)
            .quantile(0.3, 0.01)
            .quantile(0.4, 0.01)
            .quantile(0.5, 0.01)
            .quantile(0.6, 0.01)
            .quantile(0.8, 0.01)
            .quantile(0.9, 0.01)
            .quantile(0.99, 0.01)
            .quantile(0.999, 0.01)
            .register();

    public static final Summary HTTP_REQUEST_API_AND_HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_AND_RESPONSE_STATUS_CODE_SUMMARY =
            Summary.build()
                    .name("http_request_api_and_http_response_status_code_distribution_and_seconds_latency_histogram_summary")
                    .help("http request api and http response status code distribution and seconds latency histogram summary")
                    .labelNames("request_api", "response_status_code")
                    .quantile(0.2, 0.01)
                    .quantile(0.3, 0.01)
                    .quantile(0.4, 0.01)
                    .quantile(0.5, 0.01)
                    .quantile(0.6, 0.01)
                    .quantile(0.8, 0.01)
                    .quantile(0.9, 0.01)
                    .quantile(0.99, 0.01)
                    .quantile(0.999, 0.01)
                    .register();

    public static final Counter HTTP_REQUEST_API_AND_RESPONSE_STATUS_CODE = Counter.build()
            .name("http_request_api_and_response_status_code")
            .help("http request api and response status code")
            .labelNames("request_api", "response_status_code")
            .register();

    public static final Counter HTTP_REQUEST_USER_AND_HTTP_REQUEST_API = Counter.build()
            .name("http_request_user_and_http_request_api")
            .help("http request user and http request api")
            .labelNames("request_user", "request_api")
            .register();

    public static final Counter HTTP_REQUEST_API_AND_HTTP_REQUEST_USER = Counter.build()
            .name("http_request_api_and_http_request_user")
            .help("http request api and http request user")
            .labelNames("request_api", "request_user")
            .register();
    public static final Counter HTTP_REQUEST_USER_AND_HTTP_REQUEST_API_AND_RESPONSE_STATUS_CODE = Counter.build()
            .name("http_request_user_and_http_request_api_and_response_status_code")
            .help("http request user and http request api and response status code")
            .labelNames("request_user", "request_api", "response_status_code")
            .register();

    public static final Summary HTTP_REQUEST_LATENCY_SUMMARY = Summary.build()
            .name("http_request_seconds_latency_summary")
            .help("http request seconds latency summary")
            .quantile(0.2, 0.01)
            .quantile(0.3, 0.01)
            .quantile(0.4, 0.01)
            .quantile(0.5, 0.01)
            .quantile(0.6, 0.01)
            .quantile(0.8, 0.01)
            .quantile(0.9, 0.01)
            .quantile(0.99, 0.01)
            .quantile(0.999, 0.01)
            .register();
    public static final Histogram HTTP_REQUEST_API_AND_HTTP_REQUEST_LATENCY_HISTOGRAM = Histogram.build()
            .name("http_request_api_seconds_latency_histogram")
            .help("http request api seconds latency histogram")
            .labelNames("http_request_api")
            .buckets(0.1, 0.2, 0.5, 0.8, 1.0, 1.5, 2.0, 5.0, 10)
            .register();

    public static final Summary HTTP_REQUEST_API_AND_HTTP_REQUEST_LATENCY_SUMMARY = Summary.build()
            .name("http_request_api_seconds_latency_summary")
            .help("http request api seconds latency summary")
            .labelNames("http_request_api")
            .quantile(0.2, 0.01)
            .quantile(0.3, 0.01)
            .quantile(0.4, 0.01)
            .quantile(0.5, 0.01)
            .quantile(0.6, 0.01)
            .quantile(0.8, 0.01)
            .quantile(0.9, 0.01)
            .quantile(0.99, 0.01)
            .quantile(0.999, 0.01)
            .register();

    public void startPrometheusServer() throws IOException {
        HTTPServer server = new HTTPServer(metricsServerPort);
        LOGGER.debug("Prometheus metrics server started on port:{}", metricsServerPort);
    }

    public void podCreateRecord(String userId, PodCreatePhase phase, PodCreateResult result) {
        if (phase == PodCreatePhase.SYNC) {
            if (result == PodCreateResult.SUCCESS) {
                SYNC_POD_CREATE_SUCCESS_COUNTER.inc();
            } else {
                SYNC_POD_CREATE_FAILED_COUNTER.inc();
            }
        } else {
            if (result == PodCreateResult.SUCCESS) {
                ASYNC_POD_CREATE_SUCCESS_COUNTER.inc();
            } else {
                ASYNC_POD_CREATE_FAILED_COUNTER.inc();
            }
        }
        POD_CREATE_COUNTER.labels(userId, phase.toString(), result.toString()).inc();
    }

    public void requestInfoRecord(String requestId, String userId,
                                  String requestURI, String requestMethod,
                                  String className, String methodName,
                                  double latency,
                                  int responseStatusCode) {

        // 计算从应用程序启动到现在的时间（秒）
        long runtimeSeconds = (System.currentTimeMillis() - APPLICATION_START_TIME) / 1000;
        APPLICATION_RUNTIME_SECONDS.set(runtimeSeconds);

        HTTP_ALL_REQUEST_API.inc();
        HTTP_REQUEST_USER.labels(userId).inc();
        if (activeVisitors.add(userId)) {
            HTTP_REQUEST_UNIQUE_VISITOR.inc();
        }
        HTTP_REQUEST_METHOD.labels(requestMethod).inc();
        HTTP_RESPONSE_STATUS_CODE.labels(String.valueOf(responseStatusCode)).inc();
        String httpResponseStatusCodeDistribution = "2xx";
        String requestApi = methodName;
        String classNameAndMethodName = className + "_" + methodName;
        if (BCI_LOGIC_INTERFACE_MAP.containsKey(classNameAndMethodName)) {
            requestApi = BCI_LOGIC_INTERFACE_MAP.get(classNameAndMethodName);
        }

        // 记录请求延时信息
        HTTP_REQUEST_LATENCY_SUMMARY.observe(latency);
        HTTP_REQUEST_API_AND_HTTP_REQUEST_LATENCY_HISTOGRAM.labels(requestApi).observe(latency);
        HTTP_REQUEST_API_AND_HTTP_REQUEST_LATENCY_SUMMARY.labels(requestApi).observe(latency);
        HTTP_REQUEST_API.labels(requestApi).inc();
        // 记录请求状态码信息
        HTTP_REQUEST_API_AND_RESPONSE_STATUS_CODE.labels(requestApi, String.valueOf(responseStatusCode)).inc();
        HTTP_REQUEST_USER_AND_HTTP_REQUEST_API.labels(userId, requestApi).inc();
        HTTP_REQUEST_API_AND_HTTP_REQUEST_USER.labels(requestApi, userId).inc();
        HTTP_REQUEST_USER_AND_HTTP_REQUEST_API_AND_RESPONSE_STATUS_CODE.labels(userId, requestApi,
                String.valueOf(responseStatusCode)).inc();

        if (responseStatusCode >= 100 && responseStatusCode < 200) {
            httpResponseStatusCodeDistribution = "1xx";
        } else if (responseStatusCode >= 200 && responseStatusCode < 300) {
            httpResponseStatusCodeDistribution = "2xx";
        } else if (responseStatusCode >= 300 && responseStatusCode < 400) {
            httpResponseStatusCodeDistribution = "3xx";
        } else if (responseStatusCode >= 400 && responseStatusCode < 500) {
            httpResponseStatusCodeDistribution = "4xx";
        } else if (responseStatusCode >= 500) {
            httpResponseStatusCodeDistribution = "5xx";
        }
        HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_COUNTER.labels(httpResponseStatusCodeDistribution).inc();
        HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_AND_RESPONSE_STATUS_CODE_SUMMARY.labels(httpResponseStatusCodeDistribution).observe(latency);
        HTTP_REQUEST_API_AND_HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_AND_RESPONSE_STATUS_CODE_SUMMARY.labels(requestApi, httpResponseStatusCodeDistribution).observe(latency);
        if (responseStatusCode == 499) {
            HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_COUNTER.labels("499").inc();
            HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_AND_RESPONSE_STATUS_CODE_SUMMARY.labels("499").observe(latency);
            HTTP_REQUEST_API_AND_HTTP_RESPONSE_STATUS_CODE_DISTRIBUTION_AND_RESPONSE_STATUS_CODE_SUMMARY.labels(requestApi, "499").observe(latency);
        }

    }
}
