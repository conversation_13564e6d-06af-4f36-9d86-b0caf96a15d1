package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.constant.BciSubStatus;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.CreateInstanceContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("CREATE_INSTANCE")
public class CreateInstanceHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(CreateInstanceHandler.class);
    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof CreateInstanceContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        return true;
    }

    @Override
    public boolean execute() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        PodPO podPO = getPodPO();
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        podPO.setSubStatus(BciSubStatus.STATUS_DEFAULT);
        int createResult = podDao.stateMachineCreatePod(podPO, podDaoContext);
        if (createResult <= 0) {
            return false;
        }
        return true;
    }
}
