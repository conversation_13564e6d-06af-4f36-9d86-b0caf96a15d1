package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.internalsdk.bci.constant.EipConstant;
import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.model.QueryEipListResponse;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service("EipUtil")
public class EipUtil {
    @Autowired
    protected PodDaoV2 podDao;

    @Autowired
    private LogicPodClientFactoryV2 logicPodClientFactory;

    private static final Logger LOGGER = LoggerFactory.getLogger(EipUtil.class);

    /*
     * 回收EIP
     * 
     * 返回值：回收成功返回true，回收失败返回false
     *  1. pod没有EIP，返回true
     *  2. EIP解绑/删除成功，返回true
     */
    public boolean recycleEip(PodPO podPO){
        if (StringUtils.isEmpty(podPO.getPublicIp()) || StringUtils.isEmpty(podPO.getUserId())) {
            return true;
        }

        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(podPO.getUserId());
        return recycleEip(logicEipClient, podPO);
    }

    public boolean recycleEip(LogicEipClient logicEipClient, PodPO podPO){
        if (StringUtils.isEmpty(podPO.getPublicIp()) || StringUtils.isEmpty(podPO.getUserId()) ) {
            return true;
        }

        if (logicEipClient == null) {
            return false;
        }

        LOGGER.debug("recycle eip {} pod {} {}", podPO.getPublicIp(), podPO.getPodId(), podPO.getInternalIp());
        if (podPO.getEipStatus() == EipConstant.STATUS_INIT) {
            if (logicEipClient.forceReleaseEipV2(podPO.getPublicIp())) {
                podDao.deleteEip(podPO.getUserId(), podPO.getPodId(), podPO.getPublicIp());
                LOGGER.info("delete eip success. eip {} pod {} {}", 
                    podPO.getPublicIp(), podPO.getPodId(), podPO.getInternalIp());
                return true;
            }
        } else if (podPO.getEipStatus() == EipConstant.STATUS_USER_SPECIFIED) {
            if (unbindEip(logicEipClient, podPO.getPublicIp(), podPO.getInternalIp())) {
                podDao.unbindEip(podPO.getUserId(), podPO.getPodId(), podPO.getPublicIp(), true);
                LOGGER.info("unbind eip success. eip {} pod {} {}", 
                    podPO.getPublicIp(), podPO.getPodId(), podPO.getInternalIp());
                return true;
            }
        } else {
            LOGGER.info("skip eip recycle. eip {} status {} pod {} {}", 
                podPO.getPublicIp(), podPO.getEipStatus(), podPO.getPodId(), podPO.getInternalIp());
            return true;
        }

        return false;
    }

    /*
     * 解绑EIP
     * 
     * 返回值: 解绑成功返回true，解绑失败返回false
     *  1. EIP查询接口异常，返回false
     *  2. EIP不存在，返回true
     *  3. EIP状态为已解绑，返回true
     *  4. EIP状态为已绑定，但 pod ip为空，返回true. 由eip controller完成解绑
     *  5. EIP状态为已绑定，但不是绑定在当前pod上，返回true
     *  6. EIP状态为已绑定，并且是绑定在当前pod上，解绑成功后，返回true
     */
    private boolean unbindEip(LogicEipClient logicEipClient, String eip, String podIp) {
        QueryEipListResponse eipListResponse = logicEipClient.queryEipV2(eip);
        if (eipListResponse == null) {
            return false;
        }

        // eip not exist, or eip already unbind
        if (eipListResponse.getEipList().isEmpty() ||
        !eipListResponse.getEipList().get(0).getStatus().equals(EipConstant.IAAS_STATUS_BINDED)) {
            return true;
        }

        // we need pod ip to check whether eip binded with this pod
        /*
         * 指定相同eip的pod，如果新pod调度失败，pod ip为空。
         * 
         * eip controller会完成解绑操作。
        */
        if (StringUtils.isEmpty(podIp)) {
            LOGGER.debug("unbind eip failed, pod ip is empty. eip {} pod {}", eip, podIp);
            return true;
        }

        // eip isn't binded with this pod
        if (!eipListResponse.getEipList().get(0).getInstanceIp().equals(podIp)) {
            return true;
        }

        // unbind eip, if eip binded with this pod
        logicEipClient.unbindEip(eip);
        return true;
    }
}
