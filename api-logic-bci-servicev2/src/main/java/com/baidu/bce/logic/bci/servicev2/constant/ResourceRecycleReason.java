package com.baidu.bce.logic.bci.servicev2.constant;

public enum ResourceRecycleReason {
    JOB_POD_COMPLETE, // JOB 类型pod运行完成
    SIDECAR_START_FAIL, // 内置Sidecar启动失败
    ENI_CREATED_FAIL, // ENI创建失败
    POD_DELETED_UNEXPECTEDLY, // POD被意外删除
    CONTROLLER_AUTO_DELETE, // controller主动删除，目前只有潮汐pod，后续可能会加入竞价pod
    USER_DELETE_POD, // 用户主动删除pod
    TRANSFER_IMAGE_CACHE_SUCCEED, // 镜像转储执行成功后，tc-pod-xxx 被 controller 主动标记回收

    ORDER_NOT_FOUND_IN_BCI_DB, // 订单在BCI中不存在

    ORDER_HAS_NOT_POD, // 订单没有pod

    ORDER_EXECUTE_CREATE_POD_FAIL, // 订单执行失败

    ORDER_EXECUTE_EXCEPTION, // 订单执行异常

    ORDER_CHECK_CREATE_POD_FAIL, // 订单检查失败

    ORDER_CHECK_TIMEOUT, // 订单超时

    ORDER_UPDATE_ORDER_FAIL, // 订单更新失败
    ORDER_SYNC_STATUS_ABNORMAL, // 订单状态异常

    ORDER_SYNC_EXECUTE_FAIL, // 订单同步执行失败

    ORDER_SYNC_CHECK_FAIL, // 订单同步检查失败

    BILLING_RESOURCE_STATUS_ABNORMAL, // Billing资源状态异常

    POD_NO_RESOURCE_AVAILABLE_LONG_TIME, // 长时间无资源可用
    ZONE_NO_RESOURCE_SPECIFICATION, // 可用区无资源规格

}
