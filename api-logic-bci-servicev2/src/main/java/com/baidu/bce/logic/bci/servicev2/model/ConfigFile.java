package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ConfigFile extends BaseVolume {

    private List<ConfigFileDetail> configFiles;
    private Integer defaultMode;

    public List<ConfigFileDetail> getConfigFiles() {
        return configFiles;
    }

    public void setConfigFiles(List<ConfigFileDetail> configFiles) {
        this.configFiles = configFiles;
    }
}