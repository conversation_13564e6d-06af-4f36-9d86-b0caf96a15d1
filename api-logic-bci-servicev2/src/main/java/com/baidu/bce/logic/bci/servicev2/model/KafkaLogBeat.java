package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class KafkaLogBeat {
    private Management management = new Management();
    private List<Task> tasks = new ArrayList<>();
    private Logging logging = new Logging();
    private int max_procs = 1;
    private Http http = new Http();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Management {
        boolean enabled = false;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Task {
        private Input input = new Input();
        private Map<String, Output> output = new HashMap<>();
        private String task_id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Input {
        private int backoff_factor = 1;
        private String clean_inactive = "96h";
        private String close_inactive = "2s";
        private int harvester_limit = 1;
        private String ignore_older = "72h";
        private String out_type = "KAFKA";
        private List<String> paths = new ArrayList<>();
        private String scan_frequency = "5s";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Output {
        private String compression = "none";
        private List<String> hosts;
        // 10M
        private int max_message_bytes = 10000000;
        private String topic;
        private String version = "********";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Logging {
        private String level = "info";
        private List<String> selectors =  new ArrayList<>(Arrays.asList("*"));
        private boolean to_stderr = true;
        private boolean to_files = false;
        private Map<String, Boolean> metrics = new HashMap<String, Boolean>() {
            {
                put("enabled", false);
            }
        };
        private Map<String, Object> files = new HashMap<String, Object>() {
            {
                put("name", "logbeat");
                put("rotateeverybytes", 10485760);
            }
        };
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Http {
        private boolean enabled = true;
        private String host = "localhost";
        private int port = 5066;
    }
}

