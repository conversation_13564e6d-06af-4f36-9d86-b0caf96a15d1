package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.request.OrderModel;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;

@Data
@Accessors(chain = true)
public class PodListRequest extends ListRequest {

    private String tagOrderBy;
    private String tagOrder;
    private String cceId;

    public PodListRequest(String keyword, String keywordType, String order, 
                          String orderBy, String marker, int maxKeys, String cceId) {
        this.keyword = keyword;
        this.keywordType = keywordType;
        this.cceId = cceId;
        if (StringUtils.isNotEmpty(order) && StringUtils.isNotEmpty(orderBy)) {
            if ("tag".equalsIgnoreCase(orderBy)) {
                this.tagOrderBy = orderBy;
                this.tagOrder = order;
            } else {
                OrderModel orderModel = new OrderModel();
                orderModel.setOrder(order);
                orderModel.setOrderBy(orderBy);
                this.orders = Collections.singletonList(orderModel);
            }
        }
        this.marker = marker;
        this.maxKeys = maxKeys;
    }

    public PodListRequest(String keyword, String keywordType, String order,
                          String orderBy, Integer pageNo, Integer pageSize, String cceId) {
        this.keyword = keyword;
        this.keywordType = keywordType;
        this.cceId = cceId;
        if (StringUtils.isNotEmpty(order) && StringUtils.isNotEmpty(orderBy)) {
            if ("tag".equalsIgnoreCase(orderBy)) {
                this.tagOrderBy = orderBy;
                this.tagOrder = order;
            } else {
                OrderModel orderModel = new OrderModel();
                orderModel.setOrder(order);
                orderModel.setOrderBy(orderBy);
                this.orders = Collections.singletonList(orderModel);
            }
        }
        this.pageNo = pageNo == null ? 1 : pageNo;
        this.pageSize = pageSize == null ? 10 : pageSize;
    }
}
