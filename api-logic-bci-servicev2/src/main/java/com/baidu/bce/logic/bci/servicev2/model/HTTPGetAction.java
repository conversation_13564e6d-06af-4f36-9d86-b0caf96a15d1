package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class HTTPGetAction {
    private String path;
    private int port;
    private String host;
    private String scheme;
    private List<HTTPHeader> httpHeaders;
}