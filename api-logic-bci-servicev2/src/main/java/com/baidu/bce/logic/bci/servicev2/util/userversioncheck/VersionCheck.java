package com.baidu.bce.logic.bci.servicev2.util.userversioncheck;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 判定某个接口必须是v2用户使用，请用此注解修饰.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface VersionCheck {

    VersionCache.Version Version() default VersionCache.Version.V2;
}