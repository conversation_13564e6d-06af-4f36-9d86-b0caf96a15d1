package com.baidu.bce.logic.bci.servicev2.pod;

import com.baidu.bce.logic.bci.daov2.ccecluster.CceClusterDao;
import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.CceUserMapDao;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.servicev2.model.UserInfoRequest;
import com.baidu.bce.logic.bci.servicev2.util.CacheUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class CceClusterService {
    public static final Logger LOGGER = LoggerFactory.getLogger(CceClusterService.class);

    @Autowired
    private CceClusterDao cceClusterDao;

    @Autowired
    private CceUserMapDao cceUserMapDao;


    /**
     * 默认超时时间10分钟
     */
    private static final long DEFAULT_EXPIRATION_TIME = 60 * 1000L * 10;

    private CacheUtil<String, CceUserMap> cceUserMapByUserIdCacheMap = new CacheUtil<>(DEFAULT_EXPIRATION_TIME);

    private CacheUtil<String, List<CceCluster>> cceClustersByUserIdCacheMap = new CacheUtil<>(DEFAULT_EXPIRATION_TIME);


    private CacheUtil<String, CceCluster> cceClusterByCceIdCacheMap = new CacheUtil<>(DEFAULT_EXPIRATION_TIME);

    public boolean createCceCluster(CceCluster cceCluster) {
        cceClusterDao.insert(cceCluster);
        return true;
    }

    public CceCluster getCceClusterByCceId(String cceId) {
        CceCluster cceCluster = cceClusterByCceIdCacheMap.get(cceId);
        if (cceCluster != null) {
            return cceCluster;
        }
        cceCluster = cceClusterDao.getCceClusterByCceId(cceId);
        cceClusterByCceIdCacheMap.put(cceId, cceCluster);
        return cceCluster;
    }

    public List<CceCluster> getDefaultCceClusters() {
        return cceClusterDao.getDefaultCceClusters();
    }

    public boolean deleteCceClusterByCceId(String cceId) {
        cceClusterDao.delete(cceId);
        cceClusterByCceIdCacheMap.delete(cceId);
        return true;
    }

    // cceusermap

    public boolean createCceUserMap(CceUserMap cceUserMap) {
        cceUserMapDao.insert(cceUserMap);
        return true;
    }

    public CceUserMap getCceUserMapByUserId(String userId) {
        CceUserMap cceUserMap = cceUserMapByUserIdCacheMap.get(userId);
        if (cceUserMap != null) {
            return cceUserMap;
        }
        cceUserMap = cceUserMapDao.getCceUserMapByUserId(userId);
        cceUserMapByUserIdCacheMap.put(userId, cceUserMap);
        return cceUserMap;
    }

    public boolean deleteCceUserMapByUserId(String userId) {
        cceUserMapDao.delete(userId);
        cceUserMapByUserIdCacheMap.delete(userId);
        return true;
    }

    public List<CceCluster> getCceClustersByUserId(String userId) {
        List<CceCluster> cceClusters = cceClustersByUserIdCacheMap.get(userId);
        if (cceClusters != null) {
            return cceClusters;
        }
        CceUserMap cceUserMap = getCceUserMapByUserId(userId);
        List<String> cceids = Arrays.asList(cceUserMap.getCceIds().split(","));
        cceClusters = new ArrayList<>();
        for (int i = 0; i < cceids.size(); i++) {
            CceCluster cluster = cceClusterDao.getCceClusterByCceId(cceids.get(i));
            if (cluster != null) {
                cceClusters.add(cluster);
            }
        }
        cceClustersByUserIdCacheMap.put(userId, cceClusters);
        return cceClusters;
    }

    public List<CceUserMap> listCceUserMaps() {
        return cceUserMapDao.listCceUserMaps();
    }

    public List<CceUserMap> listActiveCceUserMaps() {
        return cceUserMapDao.listActiveCceUserMaps();
    }

    public void updateUserInfo(String userId, UserInfoRequest userInfo) {
        CceUserMap cceUserMap = cceUserMapDao.getCceUserMapByUserId(userId);
        if (!StringUtils.isEmpty(userInfo.getBlsTaskToken())) {
            cceUserMap.setBlsUserToken(userInfo.getBlsTaskToken());
        }
        if (null != userInfo.getCpt1()) {
            cceUserMap.setCpt1(userInfo.getCpt1());
        }
        cceUserMapDao.updateUserInfo(cceUserMap);
    }
}
