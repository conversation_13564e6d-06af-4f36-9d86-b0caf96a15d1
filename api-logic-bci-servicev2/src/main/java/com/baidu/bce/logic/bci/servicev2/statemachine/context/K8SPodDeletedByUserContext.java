package com.baidu.bce.logic.bci.servicev2.statemachine.context;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class K8SPodDeletedByUserContext extends StateMachineEventContext {
    public K8SPodDeletedByUserContext(String podId, String userId, long newResourceVersion) {
        super(podId, userId, newResourceVersion);
    }
}