package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.baidu.bce.logic.core.user.LogicUserService;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CommonResponse {
    private String requestId;

    public CommonResponse() {
        this.setRequestId();
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId() {
        this.requestId = LogicUserService.getRequestId();
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
}