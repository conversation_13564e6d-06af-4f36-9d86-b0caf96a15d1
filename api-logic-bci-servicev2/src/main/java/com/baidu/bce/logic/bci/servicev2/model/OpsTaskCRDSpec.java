package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpsTaskCRDSpec {

    private String podID;
    private String accountID;
    private String uuid;
    private String nodeName;
    private String opsType;
    private String opsValue;
    private String storageType;
    private String storageParam;
}
