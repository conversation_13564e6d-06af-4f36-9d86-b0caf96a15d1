package com.baidu.bce.logic.bci.servicev2.constant;

public enum ChargeStatus {

    PENDINGCHARGE("pending", "noCharge"),
    RUNNINGCHARGE("running", "charge"),
    FAILEDNOCHARGE("failed", "noCharge"),
    SUCCEEDNOCHARGE("succeeded", "noCharge"),
    UNKNOWNNOCHARGE("unknown", "noCharge"),
    DELETEDNOCHARGE("deleted", "noCharge");

    private String name;
    private String status;


    ChargeStatus(String name, String status) {
        this.name = name;
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public String getStatus() {
        return status;
    }

    public static String getStatus(String name) {
        for (ChargeStatus bciStatus : values()) {
            if (bciStatus.getName().equalsIgnoreCase(name)) {
                return bciStatus.getStatus();
            }
        }
        // 如果是未知状态，没有同步成功，可能会多计费，所以这里默认是noCharge
        return "noCharge";
    }
}
