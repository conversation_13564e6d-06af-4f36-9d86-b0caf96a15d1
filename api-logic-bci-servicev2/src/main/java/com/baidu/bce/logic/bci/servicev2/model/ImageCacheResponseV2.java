package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import java.sql.Timestamp;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ImageCacheResponseV2 {
    private Integer totalCount;
    private List<ImageCacheObj> result = new ArrayList<>();
    private Long pageSize;
    private Long pageNo;
    // 兼容limit和offset查询
    private List<ImageCacheObj> imageCaches = new ArrayList<>();
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class ImageCacheObj {
        private String imageCacheName;
        private String imageCacheId;
        private List<String> originImages;
        private String status;
        private int progress;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Timestamp expiredTime;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Timestamp createdTime;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Timestamp lastestMatchedTime;
    }
}