package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import lombok.Data;

import java.util.List;

@Data
public class PodVpcResponse {
    private SimpleSecurityGroupVO securityGroupVO;
    private List<SimpleSecurityGroupVO> securityGroupVOList;
    private VpcVo vpc;
    private SubnetVo subnet;
}
