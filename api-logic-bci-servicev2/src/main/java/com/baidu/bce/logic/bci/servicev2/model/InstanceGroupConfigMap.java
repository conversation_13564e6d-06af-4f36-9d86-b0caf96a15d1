package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.logic.bci.servicev2.constant.InstanceGroup;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.base.Strings;

import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import io.kubernetes.client.custom.Quantity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class InstanceGroupConfigMap {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceGroupConfigMap.class);
    private String instanceGroupName;
    private String instanceGroupId;
    private int buffer;
    private Double resourceRatio;
    private String chargingType;
    private String matchType = "";
    private List<Map<String, String>> matchResources;
    private String cpuType;
    private List<String> authorizedUsers;
    private InstanceGroupParam params;

    // 检查物理可用区
    public boolean checkPhysicalZone(List<String> physicalZones) {
        boolean isMatched = false;
        for (int i = 0; i < physicalZones.size(); i++) {
            if (instanceGroupName.contains(physicalZones.get(i))) {
                isMatched = true;
                break;
            }
        }
        return isMatched;
    }

    // 检查是否为节点组支持的用户
    public boolean checkAuthorizedUser(String accountId) {
        if (CollectionUtils.isEmpty(authorizedUsers)) {
            return false;
        }
        boolean isAuthorizedUser = false;
        for (int i = 0; i < authorizedUsers.size(); i++) {
            if (authorizedUsers.get(i).equals(accountId)) {
                isAuthorizedUser = true;
                break;
            }
        }
        return isAuthorizedUser;
    }

    // 检查cpu类型
    public boolean checkCPUType(String cpuType) {
        // a. pod不存在CpuType或pod cpu类型为空，默认支持任意cpuType类型的节点组
        // b. pod cpu类型为intel, 则节点组的cpu类型也必须是intel或空
        // c. pod cpu类型为其他字段，则节点组的cpu类型必须强匹配
        if (StringUtils.isNotBlank(cpuType)) {
            if ("intel".equalsIgnoreCase(cpuType)) {
                if (!Strings.isNullOrEmpty(this.cpuType) && !"intel".equalsIgnoreCase(this.cpuType)) {
                    return false;
                }
            } else if (Strings.isNullOrEmpty(this.cpuType)) {
                return false;
            } else if (!cpuType.equalsIgnoreCase(this.cpuType)) {
                return false;
            }
        }
        return true;
    }

    // 匹配pod规格
    public boolean checkResourceMatch(PreviewPodCapacityRequest request, Map<String, Boolean> accountWhiteMap) {
        // 匹配matchType 资源池类型
        if (CollectionUtils.isNotEmpty(request.getTypes())) {
            List<String> podMatchTypes = request.getTypes();
            Collections.sort(podMatchTypes);
            List<String> igMatchTypes = new ArrayList<>();
            if (StringUtils.isNotBlank(this.matchType)) {
                igMatchTypes = Arrays.asList(this.matchType.split(","));
            }
            Collections.sort(igMatchTypes);
            if (!podMatchTypes.equals(igMatchTypes)) {
                return false;
            }
        } else if (StringUtils.isNotBlank(this.matchType)) {
            return false;
        }

        // 匹配资源规格
        if (accountWhiteMap.containsKey(request.getAccountId())) {
            // 模糊资源匹配
            return resourceFuzzyMatch(request);
        } 
        // 精确资源匹配
        return resourceExactMatch(request);
    }

    public boolean resourceFuzzyMatch(PreviewPodCapacityRequest request) {
        Quantity cpu = Quantity.fromString(String.valueOf(request.getCpu()));
        Quantity memory = Quantity.fromString(String.valueOf(request.getMemory()) + "Gi");
        String gpuType = request.getGpuType();
        Quantity gpuCount = Quantity.fromString(String.valueOf(request.getGpuCount()));
        LOGGER.debug("request cpu: {}, memory: {}, gpuType: {} gpuCount: {}", cpu, memory, gpuType, gpuCount);
        for (int i = 0; i < matchResources.size(); i++) {
            if (Strings.isNullOrEmpty(matchResources.get(i).get("cpu"))) {
                continue;
            }
            if (Strings.isNullOrEmpty(matchResources.get(i).get("memory"))) {
                continue;
            }
            Quantity igCpu = Quantity.fromString(matchResources.get(i).get("cpu"));
            Quantity igMemory = Quantity.fromString(matchResources.get(i).get("memory"));
            Quantity igGpuCount = resourcePodGPUCount(matchResources.get(i));
            LOGGER.debug("ig cpu: {}, memory: {}, gpuCount: {}", igCpu, igMemory, igGpuCount);
            if (StringUtils.isNotEmpty(gpuType)) {
                if (matchResources.get(i).containsKey(gpuType) 
                    && Objects.equals(igGpuCount.getFormat(), gpuCount.getFormat()) 
                    && igGpuCount.getNumber().compareTo(gpuCount.getNumber()) >= 0
                    && Objects.equals(igCpu.getFormat(), cpu.getFormat()) 
                    && igCpu.getNumber().compareTo(cpu.getNumber()) >= 0 
                    && Objects.equals(igMemory.getFormat(), memory.getFormat()) 
                    && igMemory.getNumber().compareTo(memory.getNumber()) >= 0) {
                        return true;
                    }
            } else {
                if (igGpuCount == null 
                    && igCpu.getFormat().equals(cpu.getFormat()) 
                    && igCpu.getNumber().compareTo(cpu.getNumber()) >= 0 
                    && igMemory.getFormat().equals(memory.getFormat()) 
                    && igMemory.getNumber().compareTo(memory.getNumber()) >= 0) {
                        return true;
                    }
            }
        }
        return false;
    }

    public Quantity resourcePodGPUCount(Map<String, String> resource) {
        if (resource.containsKey(InstanceGroup.A1024GCGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.A1024GCGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.A1024GCGPU));
        } else if (resource.containsKey(InstanceGroup.A10040GCGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.A10040GCGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.A10040GCGPU));
        } else if (resource.containsKey(InstanceGroup.A10080GCGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.A10080GCGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.A10080GCGPU));
        } else if (resource.containsKey(InstanceGroup.A3024GCGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.A3024GCGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.A3024GCGPU));
        } else if (resource.containsKey(InstanceGroup.RTX3070CGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.RTX3070CGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.RTX3070CGPU));
        } else if (resource.containsKey(InstanceGroup.RTX3080CGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.RTX3080CGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.RTX3080CGPU));
        } else if (resource.containsKey(InstanceGroup.RTX3090CGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.RTX3090CGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.RTX3090CGPU));
        } else if (resource.containsKey(InstanceGroup.T416GCGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.T416GCGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.T416GCGPU));
        } else if (resource.containsKey(InstanceGroup.V10032GCGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.V10032GCGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.V10032GCGPU));
        }else if (resource.containsKey(InstanceGroup.R200CGPU)) {
            if (Strings.isNullOrEmpty(resource.get(InstanceGroup.R200CGPU))) {
                return Quantity.fromString(String.valueOf(0));
            }
            return Quantity.fromString(resource.get(InstanceGroup.R200CGPU));
        }
        return null;
    }

    public boolean resourceExactMatch(PreviewPodCapacityRequest request) {
        Quantity cpu = Quantity.fromString(String.valueOf(request.getCpu()));
        Quantity memory = Quantity.fromString(String.valueOf(request.getMemory()) + "Gi");
        String gpuType = request.getGpuType();
        Quantity gpuCount = Quantity.fromString(String.valueOf(request.getGpuCount()));
        LOGGER.debug("request cpu: {}, memory: {}, gpuType: {} gpuCount: {}", cpu, memory, gpuType, gpuCount);
        for (int i = 0; i < matchResources.size(); i++) {
            if (Strings.isNullOrEmpty(matchResources.get(i).get("cpu"))) {
                continue;
            }
            if (Strings.isNullOrEmpty(matchResources.get(i).get("memory"))) {
                continue;
            }
            Quantity igCpu = Quantity.fromString(matchResources.get(i).get("cpu"));
            Quantity igMemory = Quantity.fromString(matchResources.get(i).get("memory"));
            Quantity igGpuCount = resourcePodGPUCount(matchResources.get(i));
            LOGGER.debug("ig cpu: {}, memory: {}, gpuCount: {}", igCpu, igMemory, igGpuCount);
            if (StringUtils.isNotEmpty(gpuType)) {
                if (matchResources.get(i).containsKey(gpuType) && igGpuCount.equals(gpuCount)
                    && igCpu.equals(cpu) && igMemory.equals(memory)) {
                        return true;
                    }
            } else {
                if (igGpuCount == null && igCpu.equals(cpu) && igMemory.equals(memory)) {
                    return true;
                }
            }
        }
        return false;
    }
}
