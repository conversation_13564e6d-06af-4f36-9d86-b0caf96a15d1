package com.baidu.bce.logic.bci.servicev2.interceptor;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("ResourceAccountConfigV2")
public class ResourceAccountConfig {

    @Value("${bci.charge.resource.account.name:bsc&BCE:BSC;cce&BCE:CCE}")
    private String resourceAccount;

    public Map<String, String> resourceAccountMapping() {
        Map<String, String> mapping = new HashMap<>();
        if (StringUtils.isEmpty(resourceAccount)) {
            return mapping;
        }
        String[] accountArray = resourceAccount.split(";");
        for (String account : accountArray) {
            if (StringUtils.isEmpty(account)) {
                continue;
            }
            String[] paasName = account.split("&");
            if (paasName.length < 2 ) {
                continue;
            }
            mapping.put(paasName[0], paasName[1]);
        }
        return mapping;
    }
}
