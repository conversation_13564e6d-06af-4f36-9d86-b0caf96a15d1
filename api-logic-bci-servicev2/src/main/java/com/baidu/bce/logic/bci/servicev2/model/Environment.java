package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import io.kubernetes.client.openapi.models.V1EnvVarSource;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class Environment {
    private String key;
    private String value;
    // 新增字段
    private V1EnvVarSource valueFrom;
}
