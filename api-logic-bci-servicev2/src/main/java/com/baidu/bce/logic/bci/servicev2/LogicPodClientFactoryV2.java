package com.baidu.bce.logic.bci.servicev2;

import com.baidu.bce.billing.UsagePackageClientFactory;
import com.baidu.bce.billing.proxy.service.v1.LegacyProxyService;
import com.baidu.bce.billing.proxy.service.v1.ProxyClientFactory;
import com.baidu.bce.billing.resourcemanager.client.ResourceClientFactory;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.service.UsagePackageService;
import com.baidu.bce.externalsdk.logical.network.idmapper.ExternalIdMapperClient;
import com.baidu.bce.externalsdk.logical.network.securitygroup.SecurityGroupClient;
import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.CCRClient;
import com.baidu.bce.internalsdk.bci.CceImageClient;
import com.baidu.bce.internalsdk.bci.EniClient;
import com.baidu.bce.internalsdk.bci.CloudTrailClient;
import com.baidu.bce.internalsdk.bci.ContainerManagerClient;
import com.baidu.bce.internalsdk.bci.DockerHubClient;
import com.baidu.bce.internalsdk.bci.EipTimeOutV2Client;
import com.baidu.bce.internalsdk.bci.IAMExtraClient;
import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.BLSClient;
import com.baidu.bce.internalsdk.bci.BcmClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.eip.EipLogicalClient;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.AccessKey;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.OrderClientV2;
import com.baidu.bce.internalsdk.order.PricingClientV3;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.ResourceClientV2;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.UUID;

@Service
public class LogicPodClientFactoryV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogicPodClientFactoryV2.class);

    @Value("${iam.console.username:console}")
    private String consoleUsername;

    @Value("${iam.console.password:console}")
    private String consolePassword;

    @Value("${bce.logical.region:bj}")
    private String region;

    @Value("${httpClient.maxConnTotal:800}")
    private int maxConnTotal;

    @Value("${httpClient.maxConnPerRoute:800}")
    private int maxConnPerRoute;

    @Value("${cloudTrail.host:true}")
    private boolean cloudTrailHost;

    @Value("${bci.resource.account.id}")
    private String resourceAccountID;

    @Value("${bci.eni.hexkey}")
    private String eniHexkey;

    @Autowired
    RegionConfiguration regionConfiguration;

    @Autowired
    private IamLogicService iamLogicService;

    @Autowired
    ResourceClientFactory resourceClientFactory;

    @Autowired
    ProxyClientFactory proxyClientFactory;

    public static final String COMPUTE_TYPE = "compute";
    public static final String CCE_TYPE = "cce";
    public static final String LOGIC_TYPE = "logical";


    public String getAccountId() {
        return LogicUserService.getAccountId();
    }

    public EniClient createEniClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithTokenAndEnabledAccessKey(accountId);
        EniClient eniClient = new EniClient(stsCredential, resourceAccountID, eniHexkey);
        return eniClient;
    }

    /**
     * 创建logicalTag的client
     *
     * @return logicalTag client
     */
    public LogicalTagClient createLogicalTagClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        LogicalTagClient logicalTagClient = new LogicalTagClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        logicalTagClient.setSecurityToken(stsCredential.getSessionToken());
        return logicalTagClient;
    }

    public CceImageClient createCceImageClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        CceImageClient cceImageClient = new CceImageClient(stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey());
        cceImageClient.setSecurityToken(stsCredential.getSessionToken());
        return cceImageClient;
    }

    public CCRClient createCCRClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        CCRClient ccrClient = new CCRClient(stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey());
        ccrClient.setSecurityToken(stsCredential.getSessionToken());
        return ccrClient;
    }


    private String getEndpointFromIam(String serviceType, String region, Token userToken) {
        String endpoint = null;
        if (!CollectionUtils.isEmpty(userToken.getCatalog())) {
            for (Token.Service service : userToken.getCatalog()) {
                if (service.getType().equals(serviceType)) {
                    endpoint = getEndpointFromTokenService(region, service);
                    if (endpoint == null) {
                        endpoint = service.getEndpoints().get(0).getUrl();
                    }
                    break;
                }
            }
        }
        LOGGER.info("Get endpoint[serviceType={}, endpoint={}] from iam.", serviceType, endpoint);
        return endpoint;
    }

    private String getEndpointFromTokenService(String region, Token.Service service) {
        if (!CollectionUtils.isEmpty(service.getEndpoints())) {
            for (Token.Service.Endpoint endpoint : service.getEndpoints()) {
                if (region.equalsIgnoreCase(endpoint.getRegion())) {
                    return endpoint.getUrl();
                }
            }
        }
        return null;
    }

    public ResourceClient createResourceClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        ResourceClient resourceClient = new ResourceClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        resourceClient.setSecurityToken(stsCredential.getSessionToken());
        return resourceClient;
    }

    public ResourceClient createResourceClient() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        return new ResourceClient(accessKey.getAccess(), accessKey.getSecret());
    }

    public ZoneClient createZoneClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        ZoneClient zoneClient = new ZoneClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        zoneClient.setSecurityToken(stsCredential.getSessionToken());
        return zoneClient;
    }

    /**
     * 创建访问用户配置的Client
     *
     * @param accountId
     * @return
     */
    public UserSettingsClient createUserSettingsClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        UserSettingsClient userSettingsClient =
                new UserSettingsClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        userSettingsClient.setSecurityToken(stsCredential.getSessionToken());
        return userSettingsClient;
    }

    public DockerHubClient createDockerHubrClient() {
        return new DockerHubClient();
    }

    public ServiceCatalogOrderClient createServiceCatalogOrderClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        ServiceCatalogOrderClient serviceCatalogOrderClient =
                new ServiceCatalogOrderClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                        accountId);
        serviceCatalogOrderClient.setMaxConnTotal(maxConnTotal);
        serviceCatalogOrderClient.setMaxConnPerRoute(maxConnPerRoute);
        serviceCatalogOrderClient.setSecurityToken(stsCredential.getSessionToken());
        return serviceCatalogOrderClient;
    }

    /**
     * EIP Logic
     *
     * @param accountId
     * @return
     */
    public LogicEipClient createLogicEipClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        return new LogicEipClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public ExternalSubnetClient createExternalSubnetClient(String accountId) {
        if (StringUtils.isEmpty(accountId)) {
            LOGGER.info("accountId is null when create resourceClient, use console aksk instead.");
            AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
            return new ExternalSubnetClient(accessKey.getAccess(), accessKey.getSecret());
        } else {
            StsCredential stsCredential =
                    iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(LogicUserService.getAccountId());
            ExternalSubnetClient externalSubnetClient =
                    new ExternalSubnetClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
            externalSubnetClient.setSecurityToken(stsCredential.getSessionToken());
            return externalSubnetClient;
        }
    }

    public ExternalVpcClient createExternalVpcClient(String accountId) {
        if (StringUtils.isEmpty(accountId)) {
            LOGGER.info("accountId is null when create resourceClient, use console aksk instead.");
            AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
            return new ExternalVpcClient(accessKey.getAccess(), accessKey.getSecret());
        } else {
            StsCredential stsCredential =
                    iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(LogicUserService.getAccountId());
            ExternalVpcClient externalVpcClient =
                    new ExternalVpcClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
            externalVpcClient.setSecurityToken(stsCredential.getSessionToken());
            return externalVpcClient;
        }
    }

    public PodClient createPodClientByAccountId(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithTokenAndEnabledAccessKey(accountId);
        com.baidu.bce.internalsdk.sts.model.Token token = stsCredential.getToken();
        PodClient bccClient =
                new PodClient(getEndpointFromStsToken(COMPUTE_TYPE, regionConfiguration.getCurrentRegion(), token),
                        token.getId(), stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        bccClient.setSecurityToken(stsCredential.getSessionToken());
        return bccClient;
    }

    public EipLogicalClient createEipClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        EipLogicalClient eipLogicalClient =
                new EipLogicalClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(), false);
        eipLogicalClient.setSecurityToken(stsCredential.getSessionToken());
        return eipLogicalClient;
    }

    public EipTimeOutV2Client createEipTimeOutClientV2(String accountId, Integer eipCreateTimeOut) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        EipTimeOutV2Client eipV2Client =
                new EipTimeOutV2Client(stsCredential.getAccessKeyId(),
                        stsCredential.getSecretAccessKey(), false, eipCreateTimeOut);
        eipV2Client.setSecurityToken(stsCredential.getSessionToken());
        return eipV2Client;
    }


    public PodClient createPodClient(String accountId) {

        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithTokenAndEnabledAccessKey(accountId);
        com.baidu.bce.internalsdk.sts.model.Token token = stsCredential.getToken();
        PodClient bccClient =
                new PodClient(getEndpointFromStsToken(LOGIC_TYPE, regionConfiguration.getCurrentRegion(), token),
                        token.getId(), stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        bccClient.setSecurityToken(stsCredential.getSessionToken());
        return bccClient;
    }

    public SecurityGroupClient createSecurityGroupClient(String accountId) {
        if (StringUtils.isEmpty(accountId)) {
            LOGGER.info("accountId is null when create SecurityGroupClient, use console aksk instead.");
            AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
            return new SecurityGroupClient(accessKey.getAccess(), accessKey.getSecret());
        } else {
            StsCredential stsCredential =
                    iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(LogicUserService.getAccountId());
            SecurityGroupClient securityGroupClient =
                    new SecurityGroupClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
            securityGroupClient.setSecurityToken(stsCredential.getSessionToken());
            return securityGroupClient;
        }
    }

    public ChargeResourceService createChargeResourceService(String accoundId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accoundId);
        return resourceClientFactory.createChargeResourceService(
                EndpointManager.getEndpoint("BpResourceManager"), stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey(), stsCredential.getSessionToken());
    }

    public ChargeResourceService createChargeResourceServiceWithBciConsoleToken() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        return resourceClientFactory.createChargeResourceService(
                EndpointManager.getEndpoint("BpResourceManager"), accessKey.getAccess(),
                accessKey.getSecret(), null);
    }

    public IAMClient createIAMClientWithConsoleToken() {
        IAMClient iamClient = new IAMClient();
        iamClient.setxAuthToken(iamClient.getConsoleToken(consoleUsername, consolePassword).getId());
        return iamClient;
    }

    public IAMExtraClient createIAMExtraClient() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        String endpoint = EndpointManager.getInstance().getRegion(regionConfiguration.getCurrentRegion())
                .getEndpoint(IAMExtraClient.SERVICE_NAME);
        return new IAMExtraClient(endpoint, accessKey.getAccess(), accessKey.getSecret());
    }

    private String getEndpointFromStsToken(String serviceType, String region,
                                           com.baidu.bce.internalsdk.sts.model.Token userToken) {
        String endpoint = null;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userToken.getCatalog())) {
            for (com.baidu.bce.internalsdk.sts.model.Token.Service service : userToken.getCatalog()) {
                if (service.getType().equals(serviceType)) {
                    endpoint = getEndpointFromStsTokenService(region, service);
                    if (endpoint == null) {
                        endpoint = service.getEndpoints().get(0).getUrl();
                    }
                    break;
                }
            }
        }
        LOGGER.info("Get endpoint[serviceType={}, endpoint={}] from iam.", serviceType, endpoint);
        return endpoint;
    }

    private String getEndpointFromStsTokenService(String region,
                                                  com.baidu.bce.internalsdk.sts.model.Token.Service service) {
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(service.getEndpoints())) {
            for (com.baidu.bce.internalsdk.sts.model.Token.Service.Endpoint endpoint : service.getEndpoints()) {
                if (region.equalsIgnoreCase(endpoint.getRegion())) {
                    return endpoint.getUrl();
                }
            }
        }
        return null;
    }

    public PricingClientV3 createGlobalPriceClient() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        PricingClientV3 pricingClientV3 = new PricingClientV3(accessKey.getAccess(), accessKey.getSecret());
        return pricingClientV3;
    }

    public CloudTrailClient createCloudTrailClient() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        CloudTrailClient cloudTrailClient = new CloudTrailClient(accessKey.getAccess(), accessKey.getSecret());
        cloudTrailClient.setEnableHost(cloudTrailHost);
        return cloudTrailClient;
    }

    public PodClient createAdminPodClient() {
        Token token = iamLogicService.getConsoleAdminToken();
        LOGGER.info("token_admin: " + token.getCatalog());
        return new PodClient(getEndpointFromIam(COMPUTE_TYPE, region, token), token.getId());
    }

    public OrderClient createOrderClient() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        return new OrderClient(accessKey.getAccess(), accessKey.getSecret());
    }

    public ResourceClientV2 createResourceClientV2() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        return new ResourceClientV2(accessKey.getAccess(), accessKey.getSecret()) {
            @Override
            protected BceInternalRequest createOrderRequest() {
                BceInternalRequest resq = super.createAuthorizedRequest();
                return resq.requestId(UUID.randomUUID().toString());
            }
        };
    }

    public LegacyProxyService createLegacyProxyService() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        return proxyClientFactory.createProxyClient(
                EndpointManager.getEndpoint("ChargeProxy"), accessKey.getAccess(),
                accessKey.getSecret(), "");
    }

    public ContainerManagerClient createContainerManagerClient(String host, String accoundId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accoundId);
        return new ContainerManagerClient(ContainerManagerClient.getCMEndpoint(host), stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey(), stsCredential.getSessionToken());
    }

    public ExternalIdMapperClient createExternalIdMapperClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        ExternalIdMapperClient externalIdMapperClient = new ExternalIdMapperClient(stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey());
        externalIdMapperClient.setSecurityToken(stsCredential.getSessionToken());
        return externalIdMapperClient;
    }

    public BCCClient createBCCClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        BCCClient bccClient = new BCCClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        bccClient.setSecurityToken(stsCredential.getSessionToken());
        return bccClient;
    }

    public BLSClient createBLSClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        BLSClient blsClient = new BLSClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        blsClient.setSecurityToken(stsCredential.getSessionToken());
        return blsClient;
    }

    public BcmClient createBcmClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        BcmClient bcmClient = new BcmClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        bcmClient.setSecurityToken(stsCredential.getSessionToken());
        return bcmClient;
    }

    public UsagePackageService createUsagePackageService(String accountId) {
        String endpoint = EndpointManager.getEndpoint("USAGE_PACKAGE_SERVICE");
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        UsagePackageService usagePackageService = new UsagePackageClientFactory().createUsagePackageClient(
                endpoint, accessKey.getAccess(), accessKey.getSecret(), null);
        return usagePackageService;
    }

    public AutoRenewClient createAutoRenewClient(String accountId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithEnabledAccessKey(accountId);
        // endpoint of AutoRenewClient labels as "auto-renew" in sdk
        return new AutoRenewClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    // 用于在pod下单后更新flavor中物理可用区信息
    public OrderClientV2 createOrderClientV2AsBceService() {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        return new OrderClientV2(accessKey.getAccess(), accessKey.getSecret());
    }
}
