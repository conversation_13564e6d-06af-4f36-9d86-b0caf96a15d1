package com.baidu.bce.logic.bci.servicev2.util;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import io.kubernetes.client.custom.IntOrString;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JsonUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonUtil.class);

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final Yaml yaml = new Yaml();

    public static String toJSON(Object obj) {
        StringWriter writer = new StringWriter();
        try {
            MAPPER.writeValue(writer, obj);
        } catch (JsonGenerationException e) {
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return writer.toString();
    }

    public static class IntOrStringSerializer extends JsonSerializer<IntOrString> {
        @Override
        public void serialize(IntOrString value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value.isInteger()) {
                gen.writeNumber(value.getIntValue());
            } else {
                gen.writeString(value.getStrValue());
            }
        }
    }

    public static String toJSONString(Object obj) {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        try {
            module.addSerializer(IntOrString.class, new IntOrStringSerializer());
            mapper.registerModule(module);
            String json = mapper.writeValueAsString(obj);
            return json;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String jsonStrToYaml(String str) {
        Map<String,Object> map = yaml.load(str);
        return yaml.dumpAsMap(map);
    }

    public static <T> T convertValue(Object obj, Class<T> clazz) {
        try {
            return MAPPER.convertValue(obj, clazz);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T fromJSON(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            return MAPPER.readValue(json, clazz);
        } catch (JsonParseException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (IOException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> toList(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json)) {
            return new ArrayList<>();
        }
        try {
            JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
            return MAPPER.readValue(json, javaType);
        } catch (JsonParseException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (IOException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        }
    }

    public static <T> Map<T, T> toMap(String json, Class<T> clazz) {
        if (org.springframework.util.StringUtils.isEmpty(json)) {
            return new HashMap<>();
        }
        try {
            JavaType javaType = MAPPER.getTypeFactory().constructParametricType(Map.class, String.class, clazz);
            return MAPPER.readValue(json, javaType);
        } catch (JsonParseException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (IOException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        }
    }

    public static boolean isJson(String json) {
        try {
            MAPPER.readTree(json);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
