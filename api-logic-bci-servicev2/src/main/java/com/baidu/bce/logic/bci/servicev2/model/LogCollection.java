package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class LogCollection {
    private String name;
    private Map<String, Object> srcConfig;
    private Map<String, Object> destConfig;
    private List<Tag> tags;
}
