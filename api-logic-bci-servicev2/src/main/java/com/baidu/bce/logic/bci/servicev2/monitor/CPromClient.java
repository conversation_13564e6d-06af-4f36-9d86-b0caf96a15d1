package com.baidu.bce.logic.bci.servicev2.monitor;

import com.baidu.bce.logic.bci.servicev2.model.Metric;
import com.baidu.bce.logic.bci.servicev2.model.MetricRspBody;
import com.baidu.bce.logic.bci.servicev2.model.MetricRspResult;
import com.baidu.bce.logic.bci.servicev2.model.PromMetric;
import com.baidu.bce.logic.bci.servicev2.model.PromRspBody;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CPromClient {
    private static final String SUCCESS = "success";
    private static final String LABEL_POD_NAME = "pod";
    private static final String LABEL_CONTAINER_NAME = "container";
    private static final String LABEL_CONTAINER_ID = "containerId";
    private static final String LABEL_METRIC_NAME = "name";
    private static final List<String> RETAINED_LABEL_LIST = Arrays.asList("__name__", "name", "container", "image",
                                                                          "device", "interface");
    private static final Logger LOGGER = LoggerFactory.getLogger(CPromClient.class);

    @Value("${bce.logical.cprom.metrics:container_cpu_usage_seconds_total,container_memory_working_set_bytes}")
    private String metricNamesStr;

    private List<String> metricNames = null;

    @Value("${bce.logical.cprom.metricsForTopPod:" +
            "container_cpu_usage_seconds_total,container_memory_working_set_bytes," +
            "container_fs_reads_bytes_total,container_fs_writes_bytes_total," +
            "container_network_receive_bytes_total,container_network_receive_packets_total," +
            "container_spec_cpu_quota,container_spec_memory_limit_bytes,container_memory_rss" +
            "container_network_transmit_bytes_total,container_network_transmit_packets_total}")
    private String metricNamesStrForTopPod;

    private List<String> metricNamesForTopPod = null;

    @Value("${bce.logical.cprom.host:localhost}")
    private String host;

    @Value("${bce.logical.cprom.uri:uri}")
    private String uri;

    @Value("${bce.logical.cprom.instanceid:id}")
    private String instanceId;

    @Value("${bce.logical.cprom.token:token}")
    private String token;

    @Value("${bce.logical.cprom.maxPodsPerRequest:100}")
    private int maxPodsPerRequest;

    private SimpleHttpClient httpClient = new SimpleHttpClient();

    private List<String> getMetricNames() {
        if (metricNames == null) {
            metricNames = Arrays.asList(metricNamesStr.split(","));
        }
        return metricNames;
    }

    private List<String> getMetricNamesForTopPod() {
        if (metricNamesForTopPod == null) {
            metricNamesForTopPod = Arrays.asList(metricNamesStrForTopPod.split(","));
        }
        return metricNamesForTopPod;
    }

    public MetricRspBody getMetrics(List<String> podIds) {
        String metricNameExp = String.join("|", getMetricNames());
        return getMetricsFromCprom(podIds, metricNameExp);
    }

    public MetricRspBody getMetricsForTopPod(List<String> podIds) {
        String metricNameExp = String.join("|", getMetricNamesForTopPod());
        return getMetricsFromCprom(podIds, metricNameExp);
    }

    public MetricRspBody getMetricsFromCprom(List<String> podIds, String metricNameExp) {
        MetricRspBody result = new MetricRspBody();
        // considering cprom request time consuming, limit max pods per request
        if (podIds.size() == 0 || podIds.size() > maxPodsPerRequest) {
            LOGGER.warn("getMetricsFromCprom podIds size is invalid, podIds size is {}", podIds.size());
            return result;
        }
        // generate query expression, it's like {pod=~'p-5y9bnjxa|p-cprnnozz', __name__=~"container_memory_working_set_bytes"}
        String podExp = String.join("|", podIds);
        String queryExp = String.format("{pod=~'%s', __name__=~'%s'}", podExp, metricNameExp);

        LOGGER.debug("getMetricsFromCprom queryExp is {}" , queryExp);

        // get metrics from cprom
        String resp = reqCpromWithRetry(queryExp);
        if (StringUtils.isEmpty(resp)) {
            LOGGER.warn("getMetricsFromCprom from cprom failed, resp is empty");
            return result;
        }
        try {
            Gson gson = new Gson();
            PromRspBody promRsp = gson.fromJson(resp, PromRspBody.class);
            if (!StringUtils.equals(SUCCESS, promRsp.getStatus())) {
                LOGGER.warn("getMetricsFromCprom from cprom failed, cprom res status {}", promRsp.getStatus());
                return result;
            }
            if (promRsp.getData() == null || promRsp.getData().getResult() == null) {
                LOGGER.warn("getMetricsFromCprom from cprom failed, result is null");
                return result;
            }
            result.setResult(convertPromMetricsToBciMetrics(promRsp.getData().getResult()));
        } catch (JsonSyntaxException e) {
            LOGGER.warn("getMetricsFromCprom parse metrics from cprom failed, error msg is ", e);
        }

        LOGGER.debug("getMetricsFromCprom result len:{}", JsonUtil.toJSON(result).length());
        return result;
    }

    private List<MetricRspResult> convertPromMetricsToBciMetrics(List<PromMetric> promMetrics) {
        Map<String, MetricRspResult> podMetrics = new HashMap<String, MetricRspResult>();
        for (PromMetric promMetric : promMetrics) {
            Map<String, String> labels = promMetric.getMetric();
            // 过滤掉BCI内置的容器
            if (labels.getOrDefault(LABEL_CONTAINER_NAME, "").startsWith("bci-internal-")) {
                continue;
            }                                                                                                 
            String podShortId = labels.get(LABEL_POD_NAME);
            if (!podMetrics.containsKey(podShortId)) {
                podMetrics.put(podShortId, new MetricRspResult(podShortId, new ArrayList<Metric>()));
            }
            // 指标格式不符合预期，直接忽略
            List<Object> timeAndValue = promMetric.getValue();
            if (timeAndValue == null || timeAndValue.size() != 2) {
                continue;
            }
            // 只保留需要保留的 labels
            Map<String, String> retainedLabels = new HashMap<String, String>();
            for (String retainedLabelKey : RETAINED_LABEL_LIST) {
                // name 字段需要转成 containerId 字段，方便 VK 处理
                if (StringUtils.equals(LABEL_METRIC_NAME, retainedLabelKey)) {
                    retainedLabels.put(LABEL_CONTAINER_ID, labels.getOrDefault(retainedLabelKey, ""));
                } else {
                    retainedLabels.put(retainedLabelKey, labels.getOrDefault(retainedLabelKey, ""));
                }
            }
            Long timeStamp = ((Double) timeAndValue.get(0)).longValue();
            Double value = Double.parseDouble((String) timeAndValue.get(1));
            podMetrics.get(podShortId).getMetrics().add(new Metric(retainedLabels, timeStamp, value));
        }
        return new ArrayList<MetricRspResult>(podMetrics.values());
    }

    private String reqCpromWithRetry(String queryExp) {
        IOException exception = null;
        int i = 0;
        for (i = 0; i < 3; i++) {
            try {
                String resCprom = reqCprom(queryExp);
                return resCprom;
            } catch (IOException e) {
                exception = e;
            }
        }
        LOGGER.warn("reqCpromWithRetry get metrics failed, index:{}, queryExp:{}, error msg is ",
                i, queryExp, exception);
        return null;
    }

    private String reqCprom(String queryExp) throws IOException {
        String url = String.format("%s/%s", host, uri);
        Map<String, String> headerParams = new HashMap<String, String>();
        Map<String, String> bodyParams = new HashMap<String, String>();
        headerParams.put("Authorization", String.format("Bearer %s", token));
        headerParams.put("InstanceId", instanceId);
        bodyParams.put("query", queryExp);
        return httpClient.callPostWithFormUrlEncoded(url, headerParams, bodyParams, 30 * 1000);
    }

    // only used in ut test.
    public void setHttpClient(SimpleHttpClient httpClient) {
        this.httpClient = httpClient;
    }
}
