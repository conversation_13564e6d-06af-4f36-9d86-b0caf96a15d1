package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Metric {
    private Map<String, String> metric;
    private Long timeStamp;
    private Double value;

    public Metric(Map<String, String> metric, Long timeStamp, Double value) {
        this.metric = metric;
        this.timeStamp = timeStamp;
        this.value = value;
    }
}