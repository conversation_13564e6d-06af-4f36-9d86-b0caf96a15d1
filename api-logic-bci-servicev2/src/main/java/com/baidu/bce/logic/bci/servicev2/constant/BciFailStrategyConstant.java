package com.baidu.bce.logic.bci.servicev2.constant;

public class BciFailStrategyConstant {
    public static final String FAIL_BACK = "fail-back";
    public static final String FAIL_OVER = "fail-over";
    public static final String FAIL_FAST = "fail-fast";

    public static boolean isValid(String failStrategy) {
        if (!FAIL_BACK.equals(failStrategy) && !FAIL_OVER.equals(failStrategy) && !FAIL_FAST.equals(failStrategy)) {
            return false;
        }
        return true;
    }
}
