package com.baidu.bce.logic.bci.servicev2.constant;

public enum StateMachineEvent {
    CREATE_INSTANCE, // 新建BCI实例
    DELETE_INSTANCE, // 删除BCI实例
    MIGRATE_INSTANCE, // 迁移BCI实例
    K8S_POD_PENDING, // 底层K8S Pod状态变为Pending
    K8S_POD_RUNNING, // 底层K8 PodS状态变为Running
    K8S_POD_FAILED, // 底层K8S Pod状态变为Failed
    K8S_POD_SUCCEEDED, // 底层K8S Pod状态变为Succeeded
    K8S_POD_UNKNOWN, // 底层K8S Pod状态变为Unknown
    K8S_POD_EVICTED, // 底层K8S Pod被驱逐
    RESOURCE_RECYCLE_COMPLETE, // 清理完k8s资源和EIP资源
    INSTANCE_META_CLEAR, // 实例元信息被清除
    ORDER_FAILED, // 订单失败
    RESCHEDULING_DELETE_K8S_POD, // 重调度前的K8S Pod已经删除
    K8S_POD_DELETED_BY_USER, // 底层K8S Pod被用户主动删除
    K8S_POD_DELETED_BY_K8S_UNEXPECTEDLY, // 底层K8S Pod被K8S异常删除
    K8S_POD_NO_RESOURCE_AVAILABLE, // 底层K8S Pod没有可用资源
}
