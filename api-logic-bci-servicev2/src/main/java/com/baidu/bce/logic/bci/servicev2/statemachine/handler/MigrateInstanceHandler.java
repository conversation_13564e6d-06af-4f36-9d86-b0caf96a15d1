package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.BciSubStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecyleComplete;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.MigrateInstanceContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
@Service("MIGRATE_INSTANCE")
public class MigrateInstanceHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(MigrateInstanceHandler.class);
    private static final List<String> ALLOWED_STATUS = Arrays.asList(
            BciStatus.PENDING.getStatus(),
            BciStatus.RUNNING.getStatus(),
            BciStatus.RESCHEDULING.getStatus()
    );

    private static final List<String> ALLOWED_RESCHEDULING_SUB_STATUS = Arrays.asList(
            BciSubStatus.RESCHEDULING_DELETED.getSubStatus(),
            BciSubStatus.RESCHEDULING_CREATING.getSubStatus()
    );

    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof MigrateInstanceContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        StateMachineContext context = getContext();
        PodPO podPO = context.getPodPO();
        if (!ALLOWED_STATUS.contains(podPO.getStatus())) {
            return false;
        }
        if (BciStatus.RESCHEDULING.getStatus().equalsIgnoreCase(podPO.getStatus())) {
            if (!ALLOWED_RESCHEDULING_SUB_STATUS.contains(podPO.getSubStatus())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean execute() {
        StateMachineContext context = getContext();
        PodPO podPO = context.getPodPO();
        String status = podPO.getStatus();
        switch (status) {
            case BciStatus.STATUS_PENDING:
                return executePending();
            case BciStatus.STATUS_RUNNING:
                return executeRunning();
            case BciStatus.STATUS_RESCHEDULING:
                return executeRescheduling();
            default:
                return false;
        }
    }

    /**
     * 1. 更新pod状态为Rescheduling
     * 2. 更新pod子状态ToBeDeleted
     * 3. 更新pod的bci资源版本号
     * @return
     */
    public boolean executePending() {
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        StateMachineContext context = getContext();
        PodPO podPO = context.getPodPO();
        podPO.setStatus(BciStatus.RESCHEDULING.getStatus());
        podPO.setSubStatus(BciSubStatus.RESCHEDULING_TOBEDELETED.getStatus());
        podPO.setResourceRecycleTimestamp(0L);
        podPO.setResourceRecycleComplete(ResourceRecyleComplete.INCOMPLETE);
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        int updatePodRet = podDao.stateMachineUpdatePod(podPO, podDaoContext);
        if (updatePodRet == 0) {
            return false;
        }
        return true;
    }

    /**
     * 和pending状态处理逻辑一致
     * @return
     */
    public boolean executeRunning() {
        return executePending();
    }

    /**
     * 和pending状态处理逻辑一致
     * @return
     */
    public boolean executeRescheduling() {
        return executePending();
    }

    
}
