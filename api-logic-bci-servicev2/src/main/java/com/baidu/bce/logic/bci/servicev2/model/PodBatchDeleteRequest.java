package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceIDGetter;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PodBatchDeleteRequest implements AuthorizationResourceIDGetter {
    private List<DeletePod> deletePods;

    private Boolean relatedReleaseFlag = Boolean.FALSE;

    public PodBatchDeleteRequest() {
        
    }

    public PodBatchDeleteRequest(String podId, Boolean relatedReleaseFlag) {
        DeletePod deletePod = new DeletePod();
        deletePod.setPodId(podId);
        List<DeletePod> deletePods = new ArrayList<DeletePod>();
        deletePods.add(deletePod);
        this.deletePods = deletePods;
        this.relatedReleaseFlag = relatedReleaseFlag;
    }

    public PodBatchDeleteRequest(MDeleteContainerGroupRequest request) {
        this.deletePods = new ArrayList<DeletePod>();
        for (String instanceId : request.getInstanceIds()) {
            DeletePod deletePod = new DeletePod();
            deletePod.setPodId(instanceId);
            this.deletePods.add(deletePod);
        }
        this.relatedReleaseFlag = request.getRelatedReleaseFlag();
    }

    @Override
    public List<String> getPodIDs() {
        List<String> result = new ArrayList<String>();
        if (deletePods == null || deletePods.size() == 0) {
            return result;
        }
        for (DeletePod deletePod : deletePods) {
            result.add(deletePod.getPodId());
        }
        return result;
    }
}
