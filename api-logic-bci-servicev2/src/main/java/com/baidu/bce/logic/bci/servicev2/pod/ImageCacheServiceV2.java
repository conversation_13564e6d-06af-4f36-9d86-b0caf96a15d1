package com.baidu.bce.logic.bci.servicev2.pod;

import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.response.SubnetMapResponse;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.imageaccelerate.ImageAccelerateDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.model.ImageCachePO;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.ImageDetailDaoV2;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.model.ImageDetailPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalResourceServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.BciStatus;
import com.baidu.bce.logic.bci.servicev2.constant.ImageCacheGcStrategy;
import com.baidu.bce.logic.bci.servicev2.constant.ImageCacheOwner;
import com.baidu.bce.logic.bci.servicev2.constant.ImageCacheSnapshotterType;
import com.baidu.bce.logic.bci.servicev2.constant.ImageCacheStatus;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant.EipSubProductType;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.constant.ResourceRecycleReason;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.BatchDelImageCacheRequest;
import com.baidu.bce.logic.bci.servicev2.model.BciOrderExtra;
import com.baidu.bce.logic.bci.servicev2.model.CreateImageCacheRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateImageCacheRequest.ImageInfo;
import com.baidu.bce.logic.bci.servicev2.model.CreateImageCacheResponse;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDSpec;
import com.baidu.bce.logic.bci.servicev2.model.ImageCacheResponseV2;
import com.baidu.bce.logic.bci.servicev2.model.ImageCacheResponseV2.ImageCacheObj;
import com.baidu.bce.logic.bci.servicev2.model.ImageRegistrySecret;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PodValidator;
import com.baidu.bce.logic.bci.servicev2.util.Validator;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.utils.BeanCopyUtil;
import com.baidu.bce.logic.core.utils.UUIDUtil;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.FlavorItem;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1HostPathVolumeSource;
import io.kubernetes.client.openapi.models.V1LocalObjectReference;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1Volume;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.bci.servicev2.constant.BciOrderConstant.BCI_ORDER_ITEM_KEY_PREFIX;
import static com.baidu.bce.logic.bci.servicev2.constant.ImagePullPolicyConstant.IMAGE_PULL_POLICY_ALWAYS;

@Service("ImageCacheServiceV2")
public class ImageCacheServiceV2 extends BaseServiceV2 {

    public static final Logger LOGGER = LoggerFactory.getLogger(PodImageServiceV2.class);

    @Autowired
    private ImageAccelerateDaoV2 imageAccelerateDao;

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private Validator validator;

    @Autowired
    private ImageCacheDaoV2 imageCacheDaoV2;

    @Autowired 
    private ImageDetailDaoV2 imageDetailDaoV2;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private PodServiceV2 podServiceV2;  
    
    @Autowired
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Autowired
    private CommonUtilsV2 commonUtils;

    @Autowired
    private LogicalResourceServiceV2 logicalResourceServiceV2;

    @Value("${image.accelerate.crd.apiVersion:image.bci.cloud.baidu.com/v1}")
    private String imageAccelerateCrdApiVersion;

    @Value("${image.accelerate.crd.kind:ImageCache}")
    private String imageAccelerateCrdKind;

    @Value("${image.accelerate.defaultMaxCnt:50}")
    private int defaultMaxCnt;

    @Value("${image.accelerate.defaultRetentionDay:30}")
    private int defaultRetentionDay;

    @Value("${bci.cpt1.region:true}")
    private Boolean regionCpt1;

    private static final String LOG_CREATE_PREFIX = "[create bci] ";

    private static final String CPT1_SUB_PRODUCT_TYPE = "cpt1SubProductType";
    
    public static final String ANNOTATION_INIT_NAME = "bci_internal_initContainer";
    private static final String BCI_IMAGE_CACHE_BEST_MATCH_CDS_SNAPSHOT_ID 
        = "image.bci.cloud.baidu.com/best-match-cds-snapshot-id";
    private static final String BCI_CDS_INIT_CONTAINER_IMAGE_ADDRESS =
        "registry.baidubce.com/bci-image-cache-public/init-container-image-cds:2.0.1";
    private static final String BCI_CDS_INIT_CONTAINER_NAME = "init-container-image-cds";
    private static final String BCI_EPHEMERAL_STORAGE_TYPE_KEY = "image.bci.cloud.baidu.com/ephemeral-storage-type";
    private static final String BCI_EPHEMERAL_STORAGE_QUOTA_KEY = "image.bci.cloud.baidu.com/ephemeral-storage-quota";
    private static final String BCI_IMAGE_CACHE_SNAPSHOTTER_TYPE = "image.bci.cloud.baidu.com/snapshotter";

    private static final int TRANSFER_IMAGE_POD_DEFAULT_EIP_BANDWIDTH_IN_MBPS = 100;

    // 查询用户空间下的镜像缓存条目
    public ImageCacheResponseV2 listImageCaches(Long pageSize, Long pageNo) {
        
        // 参数校验
        if (pageSize < 1 || pageSize > 1000) {
            String message = "The pageSize is invalid, a valid pageSize must be between 1 and 1000.";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        if (pageNo < 1) {
            String message = "The pageNo is invalid, a valid pageNo must be greater than 0.";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        // 获取用户空间下的镜像加速条目,根据offset和limit值查询数据库
        ImageCacheResponseV2 resp = new ImageCacheResponseV2();
        String accountId = getAccountId();
        Long limit = pageSize;
        Long offset = (pageNo - 1) * pageSize;
        // 获取用户空间下的镜像加速条目,根据offset和limit值查询数据库
        List<ImageCachePO> imageCachePOs = imageCacheDaoV2.listImageCachePOs(accountId, limit, offset);
        for (ImageCachePO imageCachePO : imageCachePOs) {
            ImageCacheObj imageCacheObj = new ImageCacheObj();
            imageCacheObj.setImageCacheId(imageCachePO.getImageCacheId());
            imageCacheObj.setImageCacheName(imageCachePO.getImageCacheName());
            Long utcTimeDiff = 28800000L;
            Long curCreatedTime = imageCachePO.getCreatedAt().getTime();
            imageCacheObj.setCreatedTime(new Timestamp(curCreatedTime - utcTimeDiff));
            Long curUpdatedTime = imageCachePO.getUpdatedAt().getTime();
            imageCacheObj.setLastestMatchedTime(new Timestamp(curUpdatedTime - utcTimeDiff));
            Date expiredTime =
                DateUtils.addDays(new Timestamp(curUpdatedTime - utcTimeDiff), imageCachePO.getRetentionDay());
            imageCacheObj.setExpiredTime(new Timestamp(expiredTime.getTime()));
            List<String> originImages = new ArrayList<>();
            List<ImageDetailPO> imageDetailPOs =
                imageDetailDaoV2.getImageDetailsByImageCacheIdForQuery(imageCachePO.getImageCacheId());
            for (ImageDetailPO imageDetailPO : imageDetailPOs) {
                originImages.add(imageDetailPO.getOriginImageAddress() + ":" + imageDetailPO.getOriginImageVersion());
            }
            imageCacheObj.setOriginImages(originImages);
            imageCacheObj.setProgress(imageCachePO.getProgress());
            imageCacheObj.setStatus(imageCachePO.getStatus());
            if (imageCachePO.getStatus().equals(ImageCacheStatus.PARTIAL.getName())) {
                imageCacheObj.setStatus(ImageCacheStatus.SUCCESS.getName());
            }
            resp.getImageCaches().add(imageCacheObj);
        }
        resp.setPageSize(pageSize);
        resp.setPageNo(pageNo);
        // TODO，quota和used数量并发控制
        int totalCount = 0;
        List<ImageCachePO> allImageCachePOs = imageCacheDaoV2.listAllValidImageCachesByAccountId(accountId);
        if (allImageCachePOs != null) {
            totalCount = allImageCachePOs.size();
        }
        resp.setTotalCount(totalCount);
        return resp;
    }

    // 新建镜像缓存
    public CreateImageCacheResponse createImageCache(CreateImageCacheRequest request) {
        CreateImageCacheResponse response = new CreateImageCacheResponse();
        // 检查用户账户是否有效
        String accountId = getAccountId();
        checkUserId(accountId);

        // 镜像缓存创建逻辑：
        // 1.解析、校验镜像缓存创建参数，落库。
        // 镜像缓存计费逻辑：
        // 1.解析POD和订单参数，创建订单、pod参数落库。2.等到订单回调execute方法，执行创建逻辑。3.同步转储pod状态，计费。
        ImageAccelerateCRDRequest crdRequest = new ImageAccelerateCRDRequest();
        ImageCachePO imageCachePO = new ImageCachePO();
        List<ImageDetailPO> imageDetailPOs = new ArrayList<>();
    
        String tcPodSuffix = UUIDUtil.generateShortUuid().toLowerCase();
        
        // String tcPodName = "p-tc-" +  request.getImageCacheName() + "-" + tcPodSuffix;
        String tcPodName = "p-tc-" + tcPodSuffix;
        validateAndSetImageCacheRequest(request, crdRequest, tcPodName);
        // 解析镜像缓存落库参数
        praseRequestToDbObj(request, ImageCacheOwner.USER.getName(), imageCachePO, imageDetailPOs, 
            tcPodName, accountId);
        
        // 镜像缓存参数落库,唯一索引保证不重复创建
        try {
            saveImageCacheToDb(imageCachePO, imageDetailPOs);
        } catch (DataAccessException e) {
            LOGGER.error("insert image cache po to db error", e);
            throw new CommonExceptions.RequestInvalidException("image cache already exist");
        }   

        PodPO podPO = new PodPO();
        List<ContainerPO> containerPOs = new ArrayList<>();
        setPodAndContainerPO(request, accountId, podPO, containerPOs, tcPodName);
        // pod和container参数落库
        List<String> instanceIds = commonUtils.savePodCreate2DB(podPO, containerPOs);
        
        CreateOrderRequest<CreateNewTypeOrderItem> createNewTypeOrderRequest = new CreateOrderRequest<>();
        PodExtraPO podExtraPO = new PodExtraPO();
        setCreateOrderRequestParams(createNewTypeOrderRequest, podPO, crdRequest, podExtraPO);
        commonUtils.savePodExtraCreate2DB(podExtraPO);
        OrderUuidResult orderUuidResult;
        try {
            // 创建订单
            orderUuidResult = podServiceV2.submitCreateOrderToServiceCatalog(createNewTypeOrderRequest, LOG_CREATE_PREFIX);
        } catch (Exception e) {
            LOGGER.error("create order error,rollback, {}", e);
            // 删除pod
            podDao.deletePod(accountId, podPO.getPodId());
            podExtraDao.deletePodExtra(accountId, podPO.getPodId());
            // 更新镜像缓存为failed
            imageCachePO.setStatus(ImageCacheStatus.FAILED.getName());
            // 更新镜像详情deleted字段为true
            List<ImageDetailPO> imageDetails =
                imageDetailDaoV2.getImageDetailsByImageCacheId(imageCachePO.getImageCacheId());
            for (ImageDetailPO imageDetail : imageDetails) {
                imageDetail.setDeleted(true);
            }
            updateImageCacheStatus(imageCachePO, imageDetails);
            throw new CommonExceptions.InternalServerErrorException();
        }
        podDao.updateOrderId(instanceIds, accountId, orderUuidResult.getOrderId());
        podExtraDao.updateOrderId(instanceIds, accountId, orderUuidResult.getOrderId());
        response.setImageCacheId(imageCachePO.getImageCacheId());
        return response;
    }

    public void batchDelImageCache(BatchDelImageCacheRequest request) {
         if (request == null || CollectionUtils.isEmpty(request.getImageCacheIds())) {
            LOGGER.error("delete imc is empty");
            throw new CommonExceptions.RequestInvalidException("request is invalid");
        }

        List<String> deleteImcs = request.getImageCacheIds();
        for (String imc : deleteImcs) {
            deleteImageCache(imc);
        }
    }

    // 删除镜像缓存
    public  void deleteImageCache(String imageCacheId) {
        // 删除镜像缓存有好几种情况，需要删除的资源包括镜像缓存CRD，订单绑定的资源状态，转储POD
        // 第一，数据落库后，订单执行器还未执行回调，需要修改订单为FAILED,pod和container置为deleted。
        // 第二，数据落库，订单执行器执行了回调，需要删除crd，需要解绑资源状态，pod和container置为deleted。
        // 第三，数据落库，镜像已经创建完毕，pod已经回收，需要删除crd，解绑订单资源状态。
        // TO DO,podinbuild 的逻辑是拿了一批 podPO 轮流处理，有可能出现这里删除，但那边还要处理的情况。
        if (imageCacheId == null || imageCacheId.isEmpty()) {
             LOGGER.error("imageCacheId  is empty");
            throw new CommonExceptions.RequestInvalidException("imageCacheId is empty");
        }
        // 删除之前先查询是否存在
        String accountId = getAccountId();
        checkUserId(accountId);
        ImageCachePO imageCachePO = imageCacheDaoV2.getAllImageCacheById(imageCacheId, accountId);
        if (imageCachePO == null ) {
            String message = "The imageCacheId is invalid, can not find imageCache by this imageCacheId.";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        // 先删除crd，再从数据库删除。
        k8sService.deleteImageAccCRD(accountId, imageCachePO.getImageCacheName());
        List<ImageDetailPO> imagdeDetailPOs = imageDetailDaoV2.getImageDetailsByImageCacheId(imageCacheId);
        String suffix = "-deleted-" + UUIDUtil.generateShortUuid().toLowerCase();
        String deletedImageCacheName = imageCachePO.getImageCacheName() + suffix;
        String deletedImageCacheId = imageCachePO.getImageCacheId() + suffix;
        imageCachePO.setImageCacheName(deletedImageCacheName);
        imageCachePO.setImageCacheId(deletedImageCacheId);
        imageCachePO.setDeleted(true);
        Long utcTimeDiff = 28800000L;
        Timestamp curTime = new Timestamp(System.currentTimeMillis() + utcTimeDiff);
        imageCachePO.setDeletedAt(curTime);
        for (ImageDetailPO imageDetailPO : imagdeDetailPOs) {
            imageDetailPO.setDeleted(true);
            imageDetailPO.setDeletedAt(curTime);
            imageDetailPO.setImageCacheId(deletedImageCacheId);
        }
        updateImageCacheStatus(imageCachePO, imagdeDetailPOs); 
        if (imageCachePO.getTcPodName().contains("auto-create")) {
            // 自动创建的tc pod，无需删除
            return;
        }
        PodPO podPO = getPodPOByPodId(imageCachePO.getTcPodName());
        if (podPO != null && BciStatus.PENDING.getStatus().equals(podPO.getStatus())) {
            // 订单置为failed
            String orderId = podPO.getOrderId();
            OrderClient orderClient = logicPodClientFactory.createOrderClient();
            Order bciOrder;
            try {
                if (StringUtils.isEmpty(orderId)) {
                    // orderID 是空
                    LOGGER.debug("orderID:{} is empty", orderId);
                    return;
                }
                bciOrder = orderClient.get(orderId);
                if (bciOrder == null || !orderId.equals(bciOrder.getUuid())) {
                    LOGGER.error("sync order error: Query order return null or bciOrder.getUuid() not match with " +
                                    "orderId, order id is {}",
                            orderId);
                    return;
                }
            } catch (Exception e) {
                LOGGER.error("sync order error, order {}, exception: {}", orderId, e);
                return;
            }
            ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.FAILURE);
            podNewOrderExecutorServiceV2.updateOrderToFailedStatus(accountId, orderClient, bciOrder, executionResult,
                ResourceRecycleReason.ORDER_HAS_NOT_POD.toString(),
                "The order is empty because has no available pods.");
            
            // 删除pod,只处理pending未调度的情况
            podDao.deletePod(imageCachePO.getAccountId(), podPO.getPodId());
        }  else if (podPO != null && BciStatus.RUNNING.getStatus().equals(podPO.getStatus())) {
            destoryTcPodResource(imageCachePO);
        }
    }

    // 解绑订单资源状态
    public void destoryTcPodResource(ImageCachePO imageCachePO) {
        PodPO podPO = getPodPOByPodId(imageCachePO.getTcPodName());
        if (podPO != null) {
            logicalResourceServiceV2.deleteResourceByName(imageCachePO.getAccountId(), podPO.getPodUuid(), PodConstants.SERVICE_TYPE);
            // 删除pod
            podDao.deletePod(imageCachePO.getAccountId(), podPO.getPodId());
        }
    }

    // 校验账户名是否有效
     private void checkUserId(String userId) {
        if (userId == null || userId.isEmpty()) {
            throw new CommonExceptions.RequestInvalidException("userId is null or empty: " + userId);
        }
        CceUserMap userMap = cceClusterService.getCceUserMapByUserId(userId);
        if (userMap == null) {
            throw new CommonExceptions.RequestInvalidException("userId is error, not find CceUserMap: " + userId);
        }
        List<CceCluster> cceClusters = cceClusterService.getCceClustersByUserId(userId);
        if (cceClusters == null || cceClusters.size() == 0) {
            throw new CommonExceptions.RequestInvalidException("userId is error, not find cceCluster: " + userId);
        }
    }

    // 校验创建镜像缓存参数是否合法，并设置crd参数。
    public void validateAndSetImageCacheRequest(CreateImageCacheRequest request, ImageAccelerateCRDRequest crdRequest, 
        String tcPodName) {
        // 参数校验部分
        if (request.getImageCacheName().isEmpty() || request.getImageCacheName().equals("")) {
            String message = "The imageCacheName is invalid, a valid imageCacheName length is [2,128], support" + 
                " letters, numbers or hyphens (-), and cannot start or end with a hyphen(-).";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        String  pattern = "^[a-zA-Z0-9]{1}[a-zA-Z0-9-]{0,126}[a-zA-Z0-9]{1}$";
        boolean isMatch = Pattern.matches(pattern, request.getImageCacheName());
        if (!isMatch) {
            String message = "The imageCacheName is invalid, a valid imageCacheName length is [2,128], support" + 
                " letters, numbers or hyphens (-), and cannot start or end with a hyphen(-).";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        // 校验eipIp和needEip字段，不能同时为true和非空。
        if (request.getEipIp() != null && !request.getEipIp().isEmpty() && request.isNeedEip()) {
            String message = "eipIp not empty,needEIp true, invalid";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        // 校验eip创建参数
        if (request.isNeedEip()) {
            if (request.getEipName() == null || request.getEipName().isEmpty()) {
                String message = "eipName is empty,invalid";
                throw new CommonExceptions.RequestInvalidException(message);
            }
            if (request.getEipBandwidthInMbps() <= 0 || request.getEipBandwidthInMbps() > 200) {
                String message = "eipBandwidthInMbps should be in (0,200]";
                throw new CommonExceptions.RequestInvalidException(message);
            } else if (request.getEipBandwidthInMbps() == 0 && (request.getEipIp() == null || request.getEipIp().isEmpty())) {
                request.setEipBandwidthInMbps(TRANSFER_IMAGE_POD_DEFAULT_EIP_BANDWIDTH_IN_MBPS);
            }
            if (request.getEipBillingMethod() == null || 
                (!request.getEipBillingMethod().equals(EipSubProductType.BANDWIDTH) && 
                !request.getEipBillingMethod().equals(EipSubProductType.NETRAFFIC))) {
                String message = "eipBillingMethod invalid,should be ByBandwidth or ByTraffic";
                throw new CommonExceptions.RequestInvalidException(message);
            }
            if (request.getEipRouteType() == null || 
                !request.getEipRouteType().equals("BGP")) {
                String message = "eipRouteType invalid,should be BGP";
                throw new CommonExceptions.RequestInvalidException(message);
            }
        }
        // 校验临时存储空间
        if (request.getTemporaryStorageSize() <= 0 || request.getTemporaryStorageSize() > 2048) {
            String message = "temporaryStorageSize invalid,should be in [1,2048]";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        // 校验保留时间
        if (request.getRetentionDay() <= 0 || request.getRetentionDay() > Integer.MAX_VALUE) {
            // String message = "retentionDay should greater than i,less than 2147483647";
            // throw new CommonExceptions.RequestInvalidException(message);
            request.setRetentionDay(defaultRetentionDay);
        }
        // 校验镜像参数
        if (request.getOriginImages() == null) {
            String message = "The origin images is invalid,can not be empty";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        if (request.getOriginImages().size() < 1 || request.getOriginImages().size() > 20) {
            String message = "The origin images size in invalid,shoud be in[1,20]";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        Set<String> originImagesSet = new HashSet<>();
        for (ImageInfo imageInfo : request.getOriginImages()) {
            if (imageInfo.getOriginImageAddress() == null || imageInfo.getOriginImageVersion() == null ||
                imageInfo.getOriginImageAddress().length() == 0 || imageInfo.getOriginImageVersion().length() == 0) {
                throw new CommonExceptions.RequestInvalidException(String.format("Invalid image %s:%s", 
                    imageInfo.getOriginImageAddress(), imageInfo.getOriginImageVersion()));
            }
            String originImage = imageInfo.getOriginImageAddress() + imageInfo.getOriginImageVersion();
            if (originImagesSet.contains(originImage)) {
                String message = "The image " + originImage + " has been dupliacated, shoule be unique.";
                throw new CommonExceptions.RequestInvalidException(message);
            } else {
                originImagesSet.add(originImage);
            }
        }
        // 子网，安全组基本校验
        if (request.getSubnetId() == null || request.getSubnetId().trim().isEmpty()) {
            String message = "The subnetId is invalid, can not be empty";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        if (request.getSecurityGroupId() == null || request.getSecurityGroupId().trim().isEmpty()) {
            String message = "The securityGroupId is invalid, can not be empty";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        // 校验是否配置eliminationStrategy，且参数合法
        if (request.getEliminationStrategy() != null && !"".equals(request.getEliminationStrategy())) {
            if  (!request.getEliminationStrategy().equals(ImageCacheGcStrategy.LRU.getName())) {
                String message = "The eliminationStrategy is invalid, only support LRU";
                throw new CommonExceptions.RequestInvalidException(message);
            }
        }
        // 校验安全组id
        List<String> securityGroups = Arrays.asList(request.getSecurityGroupId().trim().split(","));
        boolean isNormal = podValidator.isNormalSecurityGroupId(securityGroups);
        if (isNormal) {
            List<SimpleSecurityGroupVO> securityGroupVOs = podService.getSecurityGroup(request.getSecurityGroupId());
            if (securityGroupVOs == null || securityGroupVOs.size() == 0) {
                String message = "The securityGroupId is invalid, can not find securityGroup by this id";
                throw new CommonExceptions.RequestInvalidException(message);
            }
        }
        // 校验子网id
        List<String> subnetIds = Arrays.asList(request.getSubnetId().split(","));
        SubnetMapResponse subnetMapResponse = podValidator.getSubnets(subnetIds);
        Map<String, SubnetVo> subnetMap = subnetMapResponse.getSubnetMap();
        Map<String, SubnetVo> newSubnetMap = new HashMap<>();
        for (String key : subnetMap.keySet()) {
            newSubnetMap.put(subnetMap.get(key).getShortId(), subnetMap.get(key));
        }
        // 判断子网和安全组是不是在同一个vpc下
        if (isNormal){
            podValidator.validateSubnetSecurityGroupInSameVpc(request.getSecurityGroupId(), request.getSubnetId());
        }
        
        if (newSubnetMap.get(request.getSubnetId()) == null || 
            !request.getZoneName().equals(newSubnetMap.get(request.getSubnetId()).getAz())) {
            String message = "The logical zone and subnet should belong to the same zone.";
            throw new CommonExceptions.RequestInvalidException(message);
        }
        ZoneMapDetail zoneMapDetail = validator.getZone(newSubnetMap.get(request.getSubnetId()).getAz());
        // 回写subnetUuid到request中
        request.setSubnetUuid(newSubnetMap.get(request.getSubnetId()).getSubnetUuid());
        // 获取cidr
        String vpcCidr = newSubnetMap.get(request.getSubnetId()).getCidr();
        List<String> originImages = new ArrayList<>();
        for (ImageInfo imageInfo : request.getOriginImages()) {
            originImages.add(imageInfo.getOriginImageAddress() + ":" + imageInfo.getOriginImageVersion());
        }
        // 校验是否超出最大缓存数目限制
        Map<Boolean, ImageCachePO> result = preCheckImageCacheNum(getAccountId());
        if (result.containsKey(false)) {
            String message = "The imageCacheCnt reached limit,cant not create new imageCache.";
            throw new CommonExceptions.RequestInvalidException(message);
        } else if (result.containsKey(true) && result.get(true) != null) {
            deleteImageCache(result.get(true).getImageCacheId());
        }
        // 设置镜像缓存参数
        setImageCacheCrdRequest(getAccountId(), request.getImageCacheName(), request.isNeedEip(), 
            zoneMapDetail.getLogicalZone(), zoneMapDetail.getPhysicalZone(), zoneMapDetail.getZoneId(), 
            request.getSubnetId(), request.getSecurityGroupId(), vpcCidr, null, originImages, 
            request.getTemporaryStorageSize(), request.getImageRegistrySecrets(), tcPodName, request.isAutoMatchImageCache(), 
            request.getEipRouteType(), request.getEipBandwidthInMbps(), request.getEipBillingMethod(),
            request.getEipIp(), crdRequest);
    }

    // 解析参数为crdRequest
    public void setImageCacheCrdRequest(String accountId, String imageCacheName, boolean needEip, String logicalZone, 
        String physicalZone, String zoneId, String subnetId, String securityGroupId, String vpcCidr, 
        String zoneSubnets, List<String> originImages, int temporaryStorageSize, List<ImageRegistrySecret> imageSecrets, 
        String tcPodName, boolean autoMatchImageCache, String eipRouteType, int eipBandwidthInMbps, String eipBillingMethod, 
        String eipIp, ImageAccelerateCRDRequest crdRequest) {
        // 设置创建镜像缓存参数
        V1ObjectMeta objectMeta = new V1ObjectMeta();
        // 转移计费场景需要使用转移计费账户创建订单，真实账户创建imc。
        objectMeta.setNamespace(accountId);
        objectMeta.setName(imageCacheName);
        ImageAccelerateCRDSpec imageAccelerateCRDSpec = new ImageAccelerateCRDSpec();
        String eipName = "";
        String eipSubName = imageCacheName.length() > 48 ? imageCacheName.substring(0, 48) : imageCacheName;
        if (needEip) {
            eipName = "eip-for-" + eipSubName;
        }
        ImageAccelerateCRDSpec.BciUserInfo bciUserInfo =
            new ImageAccelerateCRDSpec.BciUserInfo(accountId, logicalZone, physicalZone, zoneId, true, needEip, 
                eipName, eipRouteType, eipBandwidthInMbps, eipBillingMethod, eipIp,
                accountId, subnetId, securityGroupId, vpcCidr, zoneSubnets, tcPodName);
        Map<String, String> annotations = new HashMap<>();
        if (autoMatchImageCache) {
            Map<Boolean, ImageCachePO> bestMatchImageCache = findBestMatchCdsImageCache(originImages, accountId);
            if (bestMatchImageCache.containsKey(false) && bestMatchImageCache.get(false) != null) {
                annotations.put(BCI_IMAGE_CACHE_BEST_MATCH_CDS_SNAPSHOT_ID,
                    bestMatchImageCache.get(false).getCdsSnapShotId());
            }
        } 
        annotations.put(BCI_IMAGE_CACHE_SNAPSHOTTER_TYPE, ImageCacheSnapshotterType.STARGZ.getName());
        annotations.put(BCI_EPHEMERAL_STORAGE_QUOTA_KEY, Integer.toString(temporaryStorageSize));
        if (annotations != null && annotations.size() > 0) {
            objectMeta.setAnnotations(annotations);
        }
        imageAccelerateCRDSpec.setBciUser(bciUserInfo).setImages(originImages).setAuths(imageSecrets);
        crdRequest.setApiVersion(imageAccelerateCrdApiVersion).setKind(imageAccelerateCrdKind)
            .setMetadata(objectMeta).setSpec(imageAccelerateCRDSpec);
    }

    // 解析参数到数据库对象中
    public void praseRequestToDbObj(CreateImageCacheRequest request, String imageCacheOwner, 
        ImageCachePO imageCachePO, List<ImageDetailPO> imageDetailPOs, String tcPodName, String accountId) {
        List<String> originImages = new ArrayList<>();
        for (ImageInfo imageInfo : request.getOriginImages()) {
            originImages.add(imageInfo.getOriginImageAddress() + ":" + imageInfo.getOriginImageVersion());
        }
        Collections.sort(originImages);
        String originImageAddressAndTagsToString = String.join(",", originImages);
        String imageCacheId = accountId + 
            DigestUtils.md5DigestAsHex(originImageAddressAndTagsToString.getBytes(StandardCharsets.UTF_8));
        imageCachePO.setImageCacheId(imageCacheId);
        imageCachePO.setImageCacheName(request.getImageCacheName());
        imageCachePO.setAccountId(accountId);
        imageCachePO.setImageCacheOwner(imageCacheOwner);
        imageCachePO.setImageCacheType(ImageCacheSnapshotterType.STARGZ.getName());
        imageCachePO.setTemporaryStorageSize(request.getTemporaryStorageSize());
        imageCachePO.setRetentionDay(request.getRetentionDay());
        imageCachePO.setSubnetId(request.getSubnetId());
        imageCachePO.setSecurityGroupId(request.getSecurityGroupId());
        imageCachePO.setZoneSubnets(request.getZoneSubnets());
        imageCachePO.setTcPodName(tcPodName);
        if (request.getEliminationStrategy() != null && !"".equals(request.getEliminationStrategy())) {
            imageCachePO.setEliminationStrategy(request.getEliminationStrategy());
        }
        List<ImageRegistrySecret> imageCacheSecrets = request.getImageRegistrySecrets();
        if (imageCacheSecrets == null || imageCacheSecrets.isEmpty()) {
            ImageRegistrySecret imageNoneRegistrySecret = new ImageRegistrySecret();
            imageNoneRegistrySecret.setServer("none").setUserName("none").setPassword("none");
            imageCacheSecrets = new ArrayList<>();
            imageCacheSecrets.add(imageNoneRegistrySecret);
        }
        imageCachePO.setImageSecrets(JsonUtil.toJSON(imageCacheSecrets));
        // eni和eip设置
        imageCachePO.setEnableEni(1);
        if (request.isNeedEip()) {
            imageCachePO.setNeedEip(1);
        } else {
            imageCachePO.setNeedEip(0);
        }
        imageCachePO.setStatus(ImageCacheStatus.CREATING.getName());
        imageCachePO.setProgress(0);
        imageCachePO.setDeleted(false);
        imageCachePO.setCdsSnapShotId("");
        // 设置镜像detail
        for (String originImage : originImages) {
            ImageDetailPO imageDetail = new ImageDetailPO();
            imageDetail.setImageCacheId(imageCacheId);
            imageDetail.setAccountId(accountId);
            imageDetail.setImageCacheType(ImageCacheSnapshotterType.STARGZ.getName());
            imageDetail.setDeleted(false);
            String[] originImageSplit = originImage.split(":");
            if (originImageSplit.length == 2) {
                imageDetail.setOriginImageAddress(originImageSplit[0]);
                imageDetail.setOriginImageVersion(originImageSplit[1]);
            } else {
                LOGGER.error("invalid image address {}", originImage);
                return;
            }
            imageDetailPOs.add(imageDetail);
        }
    }

    // 事务操作
    @Transactional(rollbackFor = DataAccessException.class)
    public void saveImageCacheToDb(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetailPOs) {
        imageCacheDaoV2.insertImageCachePO(imageCachePO);
        for (ImageDetailPO imageDetail : imageDetailPOs) {
            imageDetailDaoV2.insertImageDetailPO(imageDetail);
        }
    }

    public List<V1Container> getAndSetCanAcceContainersForCcr(List<V1Container> containers, String accountId) {
        // 完全匹配则符合，否则不使用缓存。
        List<V1Container> applyAcceContainers = new ArrayList<>();
        for (V1Container container : containers) {
            String originImage = container.getImage();
            if (originImage != null
                && !"".equals(originImage)
                && container.getImagePullPolicy() != null
                && !container.getImagePullPolicy().equals("Never")) {
                String[] originImageSplit = originImage.split(":");
                String originImageAddress = originImageSplit.length == 2 ? originImageSplit[0] : "";
                String originImageVersion = originImageSplit.length == 2 ? originImageSplit[1] : "";
                if ("latest".equals(originImageVersion)) {
                    // 设置镜像下载策略为always，走init-container下载
                    container.setImagePullPolicy(IMAGE_PULL_POLICY_ALWAYS);
                    continue;
                } else {
                    // 查询数据库,加速地址存在说明已经success，creating状态的加速地址为空
                    List<ImageDetailPO> imageDetails =
                        imageDetailDaoV2.getImageDetailByImageCacheTypeAndImageAddressTag(
                            originImageAddress, originImageVersion, accountId);
                    if (imageDetails != null && imageDetails.size() > 0) {
                        applyAcceContainers.add(container);
                        // 替换镜像加速地址
                        container.setImage(imageDetails.get(0).getAcceImageAddress()
                            + ":" + imageDetails.get(0).getAcceImageVersion());
                        // 设置镜像下载策略为Always,直接下载
                        container.setImagePullPolicy(IMAGE_PULL_POLICY_ALWAYS);
                    } else {
                        // 设置镜像下载策略为always，走init-container下载
                        container.setImagePullPolicy(IMAGE_PULL_POLICY_ALWAYS);
                        continue;
                    }
                    for (ImageDetailPO imageDetail : imageDetails) {
                        // 更新缓存最近使用时间
                        ImageCachePO imageCachePO =
                            imageCacheDaoV2.getImageCacheById(imageDetail.getImageCacheId(), accountId);
                        if (imageCachePO == null) {
                            continue;
                        } 
                        Long utcTimeDiff = 28800000L;
                        imageCachePO.setUpdatedAt(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
                        imageCacheDaoV2.updateImageCache(imageCachePO);
                    }
                }
            }
        }
        return applyAcceContainers;
    }

    // amd透明化，查询是否支持amd
    public List<String> getImageSupportCpuType(List<V1Container> containers, String accountId) {
        List<String> imageCpuTypes = new ArrayList<>();
        for (V1Container container : containers) {
            String originImage = container.getImage();
            if (originImage != null
                && !"".equals(originImage)
                && container.getImagePullPolicy() != null
                && !container.getImagePullPolicy().equals("Never")) {
                String[] originImageSplit = originImage.split(":");
                String originImageAddress = originImageSplit.length == 2 ? originImageSplit[0] : "";
                String originImageVersion = originImageSplit.length == 2 ? originImageSplit[1] : "";
                if ("latest".equals(originImageVersion)) {
                    continue;
                } else {
                    // 查询数据库,加速地址存在说明已经success，creating状态的不会更新加速地址
                    List<ImageDetailPO> imageDetails =
                        imageDetailDaoV2.getImageDetailByImageCacheTypeAndImageAddressTag(
                            originImageAddress, originImageVersion, accountId);
                    if (imageDetails != null && imageDetails.size() > 0) {
                        ImageCachePO imageCachePO =
                            imageCacheDaoV2.getImageCacheById(imageDetails.get(0).getImageCacheId(), accountId);
                        if (imageCachePO.getScanDone() == 1) {
                            Gson gson = new Gson();
                            Type type = new TypeToken<Map<String, List<String>>>() {}.getType();
                            Map<String, List<String>> resultMap = gson.fromJson(imageCachePO.getCpuTypes(), type);
                            List<String> cpuTypeList = resultMap.get(originImage);
                            imageCpuTypes.add(JsonUtil.toJSON(cpuTypeList));
                        }
                    }
                }
            }
        }
        return  imageCpuTypes;
    }

    public void setPodSpecCcrImageCache(V1PodSpec spec) {
        V1LocalObjectReference v1LocalObjectReference = new V1LocalObjectReference();
        v1LocalObjectReference.setName("bci-ccr-secret");
        spec.setImagePullSecrets(Collections.singletonList(v1LocalObjectReference));
    }

     public List<V1Volume> genCcrImageCacheVolume(Boolean isV3) {
        // 设置ccr类型缓存volume
        List<V1Volume> volumes = new ArrayList<>();
        V1Volume volume1 = new V1Volume();
        volume1.setName("containerd-socket");
        V1HostPathVolumeSource v1HostPathVolumeSource = new V1HostPathVolumeSource();
        v1HostPathVolumeSource.setPath("/var/run/containerd/");
        volume1.setHostPath(v1HostPathVolumeSource);
        // 不管是不是v3，都需要设置volume1
        volumes.add(volume1);
        // v3需要设置vloume2
        if (isV3) {
            V1Volume volume2 = new V1Volume();
            volume2.setName("katadata");
            V1HostPathVolumeSource v1HostPathVolumeSource2 = new V1HostPathVolumeSource();
            v1HostPathVolumeSource2.setPath("/home/<USER>/kata-data");
            volume2.setHostPath(v1HostPathVolumeSource2);
            volumes.add(volume2);
        }
        return volumes;
    }

    public Map<String, String> genCommonAnnotationsForImageCache(List<V1Container> initContainers) {
        Map<String, String> commonAnnotations = new HashMap<>();
        List<String> initConNames = new ArrayList<>();
        for (V1Container container : initContainers) {
            initConNames.add(container.getName());
        }
        commonAnnotations.put(ANNOTATION_INIT_NAME, String.join(",", initConNames));
        return commonAnnotations;
    }

    public List<V1Container> deepCopy(List<V1Container> containers) {
        List<V1Container> originContainers = new ArrayList<>();
        for (V1Container container : containers) {
            Gson gson = new Gson();
            V1Container copyContainer = gson.fromJson(gson.toJson(container), V1Container.class);
            originContainers.add(copyContainer);
        }
        return containers;
    }

    public void setImageInitContainers(V1ObjectMeta metadata, V1PodSpec spec, List<V1Container> imageInitContainers,
        List<V1Container> bciInternalInitContainers, BciOrderExtra orderExtra) {
        Map<String, String> imageInitConAnnotations = new HashMap<>();
        // 设置imageInitAnnotation
        if (imageInitConAnnotations != null && imageInitConAnnotations.size() > 0) {
            metadata.getAnnotations().putAll(imageInitConAnnotations);
        }
        // 设置initContainer
        spec.getInitContainers().addAll(imageInitContainers);
        // 设置volume
        List<V1Volume> volumes = genCcrImageCacheVolume(
            orderExtra.isV3());
        spec.getVolumes().addAll(volumes);
        bciInternalInitContainers.addAll(imageInitContainers);
    }

    public void setImageInitContainersAnnotation(V1ObjectMeta metadata, V1PodSpec spec, 
        List<V1Container> bciInternalInitContainers, List<V1Container> initContainers) {
        // bciInternalInitContainers annotation设置
        Map<String, String> imageCacheAnnotations = genCommonAnnotationsForImageCache(bciInternalInitContainers);
        metadata.getAnnotations().putAll(imageCacheAnnotations);
        // 设置initconainer
        spec.getInitContainers().addAll(initContainers);
    }

    public Map<Boolean, ImageCachePO> findBestMatchCdsImageCache(List<String> originImageAddressAndTags,
        String accountId) {
        if (originImageAddressAndTags == null || originImageAddressAndTags.size() == 0) {
            Map<Boolean, ImageCachePO> result = new HashMap<>();
            result.put(false, null);
            return result;
        }
        List<ImageCachePO> imageCaches = new ArrayList<>();
        Map<String, List<String>> allMatchedImageCaches = 
            getAllMatchedCdsImageCache(originImageAddressAndTags, accountId);
        for (Map.Entry<String, List<String>> matchedImageCache : allMatchedImageCaches.entrySet()) {
            ImageCachePO imageCachePO = imageCacheDaoV2.getImageCacheById(matchedImageCache.getKey(), accountId);
            if (imageCachePO != null) {
                float suitability =
                    calculateCdsImageCacheSuitability(originImageAddressAndTags, matchedImageCache.getValue());
                imageCachePO.setSuitability(suitability);
                imageCaches.add(imageCachePO);
            } else {
                LOGGER.error("GET IMAGE CACHE BY {} FAIL", matchedImageCache.getKey());
                continue;
            }
        }
        // 排序，选择出最匹配度的缓存，根据匹配度、创建时间排序
        Collections.sort(imageCaches);
        if (imageCaches.size() == 0) {
            Map<Boolean, ImageCachePO> result = new HashMap<>();
            result.put(false, null);
            return result;
        }
        ImageCachePO bestMatchImageCache = imageCaches.get(imageCaches.size() - 1);
        if (bestMatchImageCache != null) {
            Map<Boolean, ImageCachePO> result = new HashMap<>();
            // 更新最新使用时间
            Long utcTimeDiff = 28800000L;
            bestMatchImageCache.setUpdatedAt(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
            imageCacheDaoV2.updateImageCache(bestMatchImageCache);
            result.put(false, bestMatchImageCache);
            return result;
        } else {
            Map<Boolean, ImageCachePO> result = new HashMap<>();
            result.put(false, null);
            return result;
        }
    }
    
    // 计算CDS镜像缓存匹配程度
    public float calculateCdsImageCacheSuitability(
        List<String> originImageAddressAndTags, List<String> matchedImageAddressAndTags) {
        int lenOfExist = matchedImageAddressAndTags.size();
        int lenOfOrigin = originImageAddressAndTags.size();
        // 计算匹配度
        int lenOfMatched = 0;
        for (String image : originImageAddressAndTags) {
            if (matchedImageAddressAndTags.contains(image)) {
                lenOfMatched = lenOfMatched + 1;
            }
        }
        if (lenOfExist == 0) {
            return 0;
        }
        float imageCacheSuitability =
            ((float) lenOfMatched / (float) lenOfOrigin) * ((float) lenOfMatched / (float) lenOfExist) + lenOfMatched;
        imageCacheSuitability = (float) (Math.round(imageCacheSuitability * 100)) / 100;
        return imageCacheSuitability;
    }

    // 获取所有匹配的列表
    public Map<String, List<String>> getAllMatchedCdsImageCache(
        List<String> originImageAddressAndTags, String accountId) {
        // 获取所有匹配的镜像列表,用于创建缓存加速
        Map<String, List<String>> imageCacheMap = new HashMap<>();
        for (String originImageAddressAndTag : originImageAddressAndTags) {
            String[] originImageSplit = originImageAddressAndTag.split(":");
            String originImageAddress = originImageSplit[0];
            String originImageVersion = originImageSplit[1];
            List<ImageDetailPO> imageDetails =
                imageDetailDaoV2.getImageDetailByOriginImageAddress(originImageAddress, accountId);
            if (imageDetails != null) {
                for (ImageDetailPO imageDetail : imageDetails) {
                    String imageCacheId = imageDetail.getImageCacheId();
                    ImageCachePO imageCachePO = imageCacheDaoV2.getImageCacheById(imageCacheId, accountId);
                    if (imageCachePO != null
                        && (imageCachePO.getStatus().equals(ImageCacheStatus.PARTIAL.getName())
                            || imageCachePO.getStatus().equals(ImageCacheStatus.SUCCESS.getName()))) {
                        if (imageCacheMap.containsKey(imageCacheId)) {
                            continue;
                        } else {
                            List<ImageDetailPO> imageDetailsById =
                                imageDetailDaoV2.getImageDetailsByImageCacheId(imageCacheId);
                            List<String> originImages = new ArrayList<>();
                            for (ImageDetailPO image : imageDetailsById) {
                                originImages.add(image.getOriginImageAddress() + ":" + image.getOriginImageVersion());
                            }
                            imageCacheMap.put(imageCacheId, originImages);
                        }
                    }
                }
            } else {
                continue;
            }
        }
        return imageCacheMap;
    }

    // 获取需要加速的镜像
    public List<String> getCanAcceImages(List<V1Container> containers) {
        // 获取非lastest、非never的镜像，只有满足这种要求的镜像才可以加速。
        List<String> originImageAddressAndTags = new ArrayList<>();
        for (V1Container container : containers) {
            String originImage = container.getImage();
            if (originImage != null
                && !"".equals(originImage)
                && container.getImagePullPolicy() != null
                && !container.getImagePullPolicy().equals("Never")) {
                String[] originImageSplit = originImage.split(":");
                String originImageAddress = originImageSplit.length == 2 ? originImageSplit[0] : "";
                String originImageVersion = originImageSplit.length == 2 ? originImageSplit[1] : "";
                if ("latest".equals(originImageVersion)) {
                    continue;
                } else {
                    originImageAddressAndTags.add(originImageAddress + ":" + originImageVersion);
                }
            }
        }
        return originImageAddressAndTags;
    }

    // 更新镜像表和镜像缓存表
    @Transactional
    public void updateImageCacheStatus(ImageCachePO imageCachePO, List<ImageDetailPO> imageDetailPOs) {
        Long utcTimeDiff = 28800000L;
        imageCachePO.setUpdatedAt(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
        imageCacheDaoV2.updateImageCache(imageCachePO);
        if (imageDetailPOs != null) {
            for (ImageDetailPO imageDetailPO : imageDetailPOs) {
                imageDetailPO.setUpdatedAt(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
                imageDetailDaoV2.updateImageDetail(imageDetailPO);
            }
        }
    }

    // 查询数据库中同一用户下的镜像缓存数量
    public int getImageCacheCntInDbByAccountId(String accountId) {
        List<ImageCachePO> imageCachePOs = imageCacheDaoV2.listAllValidImageCachesByAccountId(accountId);
        if (imageCachePOs == null) {
            return 0;
        }
        return imageCachePOs.size();
    }

    // 获取某个用户的conf配置的最大镜像缓存数目
    public int getImageCacheCntInConfByAccountId(String accountId) {
        return defaultMaxCnt;
    } 
   
    // 校验镜像缓存配置，查看是否可以新建镜像缓存。
    public Map<Boolean, ImageCachePO> preCheckImageCacheNum(String accountId) {
        int imageCacheCntInDb = getImageCacheCntInDbByAccountId(accountId);
        int imageCacheCntInConf = getImageCacheCntInConfByAccountId(accountId);
        if (imageCacheCntInDb >= imageCacheCntInConf) {
            // 查看是否可以gc一个最久未被使用的缓存
            ImageCachePO imageCachePO = imageCacheDaoV2.getLeastRecentlyUsedImageCache(accountId);
            if (imageCachePO == null) {
                Map<Boolean, ImageCachePO> result = new HashMap<>();
                result.put(false, imageCachePO);
                return result;
            }
            Map<Boolean, ImageCachePO> result = new HashMap<>();
            result.put(true, imageCachePO);
            return result;
        }
        Map<Boolean, ImageCachePO> result = new HashMap<>();
        result.put(true, null);
        return result;
    }

    // 设置pod和container参数
    public void setPodAndContainerPO(CreateImageCacheRequest request, String accountId, 
        PodPO podPO, List<ContainerPO> containerPOs, String tcPodName) {
        String shortId = tcPodName;
        podPO.setCceUuid("");
        podPO.setMemory(4);
        podPO.setvCpu(2);
        podPO.setCpuType("");
        podPO.setGpuType("");
        podPO.setGpuCount(0);
        podPO.setRestartPolicy("OnFailure");
        podPO.setName(request.getImageCacheName());
        podPO.setPodIndex(1);
        podPO.setPodId(shortId);
        podPO.setPodUuid(shortId);
        podPO.setStatus(BciStatus.PENDING.getStatus());
        podPO.setInternalStatus(PodConstants.BCI_INTERNAL_STATUS_UNSYNC);
        podPO.setDelayReleaseDurationMinute(600);
        podPO.setDelayReleaseSucceeded(true);
        podPO.setTags("[]");
        podPO.setUserId(accountId);
        podPO.setSecurityGroupUuid(request.getSecurityGroupId());
        podPO.setSubnetUuid(request.getSubnetUuid());
        podPO.setProductType("BCI");
        // podPO.setCreatedTime(new Timestamp(new Date().getTime()));
        // podPO.setUpdatedTime(new Timestamp(new Date().getTime()));
        podPO.setApplication(null);
        podPO.setEnableLog(0);
        podPO.setChargeSource(LogicalConstant.CHARGE_SOURCE_USER);
        podPO.setDeleted(0);
        podPO.setCpt1(true);
        podPO.setTidal(false);
        podPO.setApplication("default");
        podPO.setDelayReleaseDurationMinute(0);
        podPO.setDelayReleaseSucceeded(false);
        ContainerPO containerPO = new ContainerPO();
        containerPO.setName("image-transfer");
        containerPO.setPodUuid(shortId);
        containerPO.setUserId(accountId);
        containerPO.setContainerType("workload");
        containerPO.setArgs("");
        containerPO.setCommands("");
        containerPO.setCpu(2);
        containerPO.setImageName("");
        containerPO.setImageVersion("");
        containerPO.setImageAddress("");
        containerPO.setMemory(4);
        containerPO.setEnvs("");
        containerPO.setImagePullPolicy("");
        containerPO.setPorts("");
        containerPO.setVolumeMounts("");
        containerPO.setWorkingDir("");
        containerPO.setLivenessProbe("");
        containerPO.setReadinessProbe("");
        containerPO.setStartupProbe("");
        containerPO.setLifecycle("");
        containerPO.setStdin(false);
        containerPO.setStdinOnce(false);
        containerPO.setTty(false);
        containerPO.setSecurityContext("");
        // 解析参数为pod、container创建参数，落库
        containerPOs.add(containerPO);
    }

    public void setCreateOrderRequestParams(CreateOrderRequest<CreateNewTypeOrderItem> createNewTypeOrderRequest, 
        PodPO podPO, ImageAccelerateCRDRequest crdRequest, PodExtraPO podExtraPO) {
        // 创建订单
        List<CreateNewTypeOrderItem> createNewTypeOrderItems = new ArrayList<>();
        CreateNewTypeOrderItem bciCreateNewTypeOrderItem = new CreateNewTypeOrderItem();
        bciCreateNewTypeOrderItem.setPaymentMethod(null);
        bciCreateNewTypeOrderItem.setPurchaseOrder(0);
        // 当前billing仅支持后付费
        bciCreateNewTypeOrderItem.setProductType("postpay");
        bciCreateNewTypeOrderItem.setServiceType(PodConstants.SERVICE_TYPE);
        bciCreateNewTypeOrderItem.setSubProductType(CPT1_SUB_PRODUCT_TYPE);
        
        Flavor flavor = new Flavor();
        Flavor.FlavorItem item0 = new Flavor.FlavorItem();
        item0.setName(LogicalConstant.CPU);
        item0.setValue(String.valueOf(2));
        item0.setScale(new BigDecimal(1));
        flavor.add(item0);
    
        Flavor.FlavorItem item1 = new Flavor.FlavorItem();
        item1.setName(LogicalConstant.MEMORY);
        item1.setValue(String.valueOf(4));
        item1.setScale(new BigDecimal(1));
        flavor.add(item1);

        Set<FlavorItem> flavorItems = BeanCopyUtil.copySetCollection(new HashSet<Object>(flavor));
        bciCreateNewTypeOrderItem.setFlavor(new LinkedHashSet<FlavorItem>(flavorItems));
        bciCreateNewTypeOrderItem.setKey(BCI_ORDER_ITEM_KEY_PREFIX + podPO.getPodId());
        // 未使用
        bciCreateNewTypeOrderItem.setTime(null);
        bciCreateNewTypeOrderItem.setTimeUnit(null);
        bciCreateNewTypeOrderItem.setReleaseTime(null);
        // 每个item对应一个pod
        bciCreateNewTypeOrderItem.setCount(1);
        // 将crd放在extra中
        String extra = "";
        BciOrderExtra orderExtra = new BciOrderExtra();
        orderExtra.setImageCacheCrdReq(crdRequest);
        orderExtra.setImageCacheReq(true);
        orderExtra.setUserId(getAccountId());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            extra = objectMapper.writeValueAsString(orderExtra);
        } catch (IOException e) {
            throw new PodExceptions.JsonTransforException();
        }
        podExtraPO.setPodId(podPO.getPodId());
        podExtraPO.setUserId(podPO.getUserId());
        podExtraPO.setOrderExtra(extra);
//        bciCreateNewTypeOrderItem.setExtra(extra);
        createNewTypeOrderItems.add(bciCreateNewTypeOrderItem);

        createNewTypeOrderRequest.setOrderType(OrderType.NEW.name());
        createNewTypeOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createNewTypeOrderRequest.setTotal(1);
        createNewTypeOrderRequest.setItems(createNewTypeOrderItems);
        // 未使用参数
        createNewTypeOrderRequest.setTicketId(null);
        createNewTypeOrderRequest.setPaymentMethod(null);
    }
}