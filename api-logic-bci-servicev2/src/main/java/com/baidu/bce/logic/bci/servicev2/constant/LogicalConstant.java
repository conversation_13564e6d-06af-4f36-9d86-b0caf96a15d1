package com.baidu.bce.logic.bci.servicev2.constant;

import java.util.HashMap;
import java.util.Map;

public class LogicalConstant {

    public static final String PHYSICAL_ZONE = "physicalZone";
    public static final String BILLING_PHYSICAL_ZONE = "physical_zone";
    public static final String LOGICAL_ZONE = "logicalZone";

    public static final String PRODUCT_TYPE_PREPAY = "prepay";
    public static final String PRODUCT_TYPE_POSTPAY = "postpay";

    public static final String API_POD_PREFIX = "p";
    public static final String CONTAINER_GROUP = "pod";

    public static final String SUBNET_ID = "subnetId";
    public static final String CPU = "CpuRunTime";
    public static final String MEMORY = "RamRunTime";
    public static final String DISK = "EmptyDir";
    public static final String GPU_NAME = "GPU";
    public static final String GPU = "bcc_gpu";


    public static final String NPU_TYPE = "KUNLUN-R200";
    public static final String NPU_NAME = "NPU";
    public static final String NPU = "bcc_npu";

    public static final String IP_VERSION_4 = "4";
    public static final String IP_VERSION_6 = "6";

    public static final int DIVIDE_SCALE = 7;

    public static final String DEFAULT = "default";
    public static final String BILLING_SERVICE_NAME = "billing";

    public static final Map<String, String> POD_STATUS_MAP = new HashMap<>();
    public static final Map<String, String> POD_RESTART_POLICY_MAP = new HashMap<>();

    public static final String RESOURCE_ACCOUNT_ID = "resource-account-id";
    public static final String CHARGE_APPLICATION = "charge-application";
    public static final String RESOURCE_ACCOUNT_ACCESSKEY = "resource-account-accesskey";
    public static final String CHARGE_AUTHORIZATION = "charge-authorization";
    public static final String CHARGE_SOURCE_USER = "user";
    public static final String CHARGE_SOURCE_BSC = "bsc";

    public static final String LABEL_ACCOUNT_ID = "AccountID";
    public static final String LABEL_POD_ID = "podId";
    public static final String LABEL_ORDER_ID = "orderId";
    public static final String LABEL_INSTANCE_TYPE = "instanceType";

    /**
     * white list feature type
     * */
    public static final String ENABLE_BCI_1_0 = "EnableBCI1.0";
    public static final String ENABLE_BCI_3_0 = "EnableBCI3.0";
    public static final String ENABLE_PFS = "EnablePFS";
    public static final String ENABLE_RESERVED_INSTANCE_POSTPAY = "EnableReservedInstancePostpay";
    public static final String DISPLAY_ORIGINAL_CPU_MODEL = "DisplayOriginalCPUModel";
    public static final String ENABLE_CPU_TYPE = "EnableCPUType";
    public static final String ENABLE_POD_RESOURCE_LIMIT = "EnablePodResourceLimit";

    static {
        POD_STATUS_MAP.put(BciStatus.PENDING.getStatus().toUpperCase(), "创建中");
        POD_STATUS_MAP.put(BciStatus.FAILED.getStatus().toUpperCase(), "失败");
        POD_STATUS_MAP.put(BciStatus.RUNNING.getStatus().toUpperCase(), "运行中");
        POD_STATUS_MAP.put(BciStatus.SUCCEEDED.getStatus().toUpperCase(), "成功");
        POD_STATUS_MAP.put(BciStatus.UNKNOWN.getStatus().toUpperCase(), "未知");

        POD_RESTART_POLICY_MAP.put("ALWAYS", "总是重启");
        POD_RESTART_POLICY_MAP.put("ONFAILURE", "失败重启");
        POD_RESTART_POLICY_MAP.put("NEVER", "从不重启");
    }

    public static class EipProductType {
        public static final String PREPAY = "Prepaid";
        public static final String POSTPAY = "Postpaid";
    }

    public static class EipSubProductType {
        /**
         * console
         */
        public static final String BANDWIDTH = "ByBandwidth";
        public static final String NETRAFFIC = "ByTraffic";
        /**
         * // BANDWIDTH_PREPAID：预付费按带宽结算
         * // TRAFFIC_POSTPAID_BY_HOUR：流量按小时后付费
         * // BANDWIDTH_POSTPAID_BY_HOUR：带宽按小时后付费
         */
        // public static final String BANDWIDTH_PREPAID = "BANDWIDTH_PREPAID";
        // public static final String TRAFFIC_POSTPAID_BY_HOUR = "TRAFFIC_POSTPAID_BY_HOUR";
        // public static final String BANDWIDTH_POSTPAID_BY_HOUR = "BANDWIDTH_POSTPAID_BY_HOUR";
    }

    public static class EipRouteType {
        public static final String BGP = "BGP";
        public static final String BGP_S = "BGP_S";
        public static final String CMC = "ChinaMobile";
        public static final String CUC = "ChinaTelcom";
        public static final String CTC = "ChinaUnicom";
    }

    public static class ReservedInstanceScope {
        public static final String REGION = "REGION";
        public static final String AZ = "AZ";
    }

    public static class ReservedInstancePurchaseMode {
        public static final String FULLY_PREPAY = "FullyPrepay";
        public static final String PART_PREPAY = "PartPrepay";
        public static final String POSTPAY = "PostPay";
    }

    public static class ReservedInstanceTimeUnit {
        public static final String DAY = "DAY";
        public static final String MONTH = "MONTH";
        public static final String YEAR = "YEAR";
    }

    public static class ReservedInstanceSubServiceType {
        public static final String GPU_GENERIC = "GPU-generic";
        public static final String CPU_GENERIC = "CPU-generic";
    }
    
    public static final Map<String, String> RESERVED_INSTANCE_FIELD_MAP = new HashMap<>();
    static {
        RESERVED_INSTANCE_FIELD_MAP.put("createdTime", "created_time");
        RESERVED_INSTANCE_FIELD_MAP.put("name", "name");
        RESERVED_INSTANCE_FIELD_MAP.put("reservedInstanceId", "reserved_instance_id");
        RESERVED_INSTANCE_FIELD_MAP.put("reservedSpec", "reserved_spec");
    }
}
