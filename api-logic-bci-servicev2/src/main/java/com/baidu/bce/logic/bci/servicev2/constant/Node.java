package com.baidu.bce.logic.bci.servicev2.constant;

public class Node {
    public static final String NODE_LABEL_INSTANCE_GROUP_ID = "instance-group-id";
    public static final String NODE_LABEL_STATE_MACHINE = "bci-node-state-machine";
    public static final String NODE_LABEL_TENANT_LOCK = "tenant-lock";

    public static final String NODE_STATE_MACHINE_EMPTY = "";
    public static final String NODE_STATE_MACHINE_INIT = "init";
    public static final String NODE_STATE_MACHINE_RESERVE = "reserve";
    public static final String NODE_STATE_MACHINE_RUNPOD = "runPod";
    public static final String NODE_STATE_MACHINE_PODEXIT = "podExit";
    public static final String NODE_STATE_MACHINE_COOL = "cool";
    public static final String NODE_STATE_MACHINE_NOTREADY = "notReady";
}
