package com.baidu.bce.logic.bci.servicev2.orderexecute;

import com.baidu.bce.billing.model.PackageInfo;
import com.baidu.bce.billing.service.UsagePackageService;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.Order.Item;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceIds;
import com.baidu.bce.internalsdk.order.model.ResourceMapping;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstanceSpec;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.common.CommonUtilsV2;
import com.baidu.bce.logic.bci.servicev2.common.service.LogicalTagServiceV2;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.model.ReservedInstanceExtra;
import com.baidu.bce.logic.bci.servicev2.reservedinstance.ReservedInstanceService;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.pricing.model.domain.chargeitem.ChargeItem;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.sdk.renew.model.AutoRenewCreateRequest;
import com.baidu.bce.sdk.renew.model.AutoRenewRules;
import com.baidu.bce.sdk.renew.model.ListAutoRenewRequest;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
@Service
public class ReservedInstanceOrderExecutorService {
    @Autowired
    protected LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    private LogicalTagServiceV2 logicalTagService;
    
    @Autowired
    private ReservedInstanceDao reservedInstanceDao;

    @Autowired
    private ReservedInstanceService reservedInstanceService;

    @Autowired
    protected RegionConfiguration regionConfiguration;
    
    @Autowired
    private CommonUtilsV2 commonUtils;

    private static final Logger LOGGER = LoggerFactory.getLogger(PodNewOrderExecutorServiceV2.class);
    private static final int RETRY_NUM = 3;
    private static final int ORDER_TIMEOUT_MINUTE = 60;

    public ExecutionResult execute(OrderClient orderClient, ResourceClient resourceClient,
            Order order) {
        LOGGER.debug("[ReservedInstance][order callback]orderID {} status READY_FOR_CREATE", order.getUuid());
        ExecutionResult result = new ExecutionResult(ExecutionStatus.CREATING);
        try {
            Boolean isRenewOrder = false;
            Boolean isFailedOrder = false;
            
            Long currentTs = System.currentTimeMillis();
            currentTs = currentTs - currentTs % 60000;
            Timestamp currentLocalTime = new Timestamp(currentTs + 8 * 3600 * 1000);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<ReservedInstancePO> reservedInstancePOsAfterRenew = new ArrayList<>();
            List<String> resourceUuidsToUpdateOrderId = new ArrayList<>();
            for (Item item : order.getItems()) {
                List<String> resourceUuids = item.getResourceIds();
                if (resourceUuids == null) {
                    continue;
                }
                for (String resourceUuid : resourceUuids) {
                    ReservedInstancePO instancePO = 
                            reservedInstanceDao.getReservedInstanceByResourceUuid(resourceUuid);
                    if (instancePO != null) {
                        // 续费订单
                        isRenewOrder = true;
                        if (!order.getUuid().equals(instancePO.getOrderId())) {
                            if (instancePO.getPurchaseMode().equals(
                                    LogicalConstant.ReservedInstancePurchaseMode.POSTPAY)) {
                                if (!commonUtils.checkWhiteList(LogicalConstant.ENABLE_RESERVED_INSTANCE_POSTPAY, 
                                        regionConfiguration.getCurrentRegion(), instancePO.getAccountId())) {
                                    LOGGER.debug("[ReservedInstance][renew callback]check {} " + 
                                            "ENABLE_RESERVED_INSTANCE_POSTPAY failed", 
                                            instancePO.getReservedInstanceId());
                                    isFailedOrder = true;
                                    break;
                                }
                            }
                            // 还没处理供需匹配 & 过期时间 & 状态修改
                            Timestamp expireTimeAfterRenew;
                            String statusAfterRenew;
                            Timestamp effectiveTimeAfterRenew = instancePO.getEffectiveTime();
                            if (instancePO.getExpireTime().after(currentLocalTime)) {
                                // 对于未过期的预留实例券，续费后的失效时间 = 原失效时间 + 续费时间
                                expireTimeAfterRenew = ReservedInstancePO.calcExpireTime(
                                        instancePO.getExpireTime(), item.getTimeUnit(), 
                                        item.getTime().intValue());
                                if (instancePO.getEffectiveTime().after(currentLocalTime)) {
                                    // 对于未生效的预留实例券，续费后的状态为 INACTIVE
                                    statusAfterRenew = ReservedInstancePO.Status.INACTIVE;
                                } else {
                                    // 对于已生效的预留实例券，续费后的状态为 ACTIVE
                                    statusAfterRenew = ReservedInstancePO.Status.ACTIVE;
                                }
                            } else {
                                // billing 允许对过期若干天内的预留实例券续费
                                // 对于已过期的预留实例券，续费后的失效时间 = 当前时间 + 续费时间
                                expireTimeAfterRenew = ReservedInstancePO.calcExpireTime(currentLocalTime,
                                        item.getTimeUnit(), item.getTime().intValue());
                                // 对于已过期的预留实例券，续费后的状态为 ACTIVE
                                statusAfterRenew = ReservedInstancePO.Status.ACTIVE;
                                // 对于已过期的预留实例券，续费后的生效时间 = 当前时间
                                effectiveTimeAfterRenew = currentLocalTime;
                            }

                            // 续费判断供需时需求应取考虑续费后的预留实例券（即剔除未续费前的预留实例券）
                            // todo 待升级供需匹配规则时需要考虑到时如何剔除未计算续费前的预留实例券
                            String spec = instancePO.getReservedSpec();
                            ReservedInstanceSpec reservedInstanceSpec = reservedInstanceDao.getSpecBySpecName(spec);
                            CreateReservedInstanceRequest requestCheck = new CreateReservedInstanceRequest();
                            requestCheck.setReservedSubServiceType(reservedInstanceSpec.getDeductInstanceFamily());
                            requestCheck.setScope(instancePO.getScope());
                            requestCheck.setPhysicalZone(instancePO.getPhysicalZone());
                            requestCheck.setEffectiveTime(effectiveTimeAfterRenew);
                            requestCheck.setReservedInstanceCount(instancePO.getReservedInstanceCount());
                            requestCheck.setReservedSpec(instancePO.getReservedSpec());
                            requestCheck.setRenewDeadline(format.format(expireTimeAfterRenew));
                            LOGGER.debug("[ReservedInstance][order callback]requestCheck {}", requestCheck);
                            /*
                             * 在续费时不再检测stock配置
                             * 之前这个逻辑存在的原因是：考虑到当前没有库存管理，所以自动续费时需要检查用户的预留实例quota
                             * 现状：开启自动续费的预留实例场景下，资源均已交付给用户，不需要重新给用户准备node资源，因此可以在自动续费时不检查stock配置。
                            if (!reservedInstanceService.checkSupplyAndDemand(requestCheck, 
                                    instancePO.getAccountId())) {
                                LOGGER.debug("[ReservedInstance][renew callback]check {} supply and demand failed",
                                        instancePO.getReservedInstanceId());
                                isFailedOrder = true;
                                break;
                            }
                            */

                            // 生效时间和过期时间基于当前时间计算,billing过期续费采用的也为当前时间（订单时间）
                            ReservedInstancePO reservedInstancePOAfterRenew = new ReservedInstancePO();
                            reservedInstancePOAfterRenew.setResourceUuid(resourceUuid);
                            reservedInstancePOAfterRenew.setExpireTime(expireTimeAfterRenew);
                            reservedInstancePOAfterRenew.setStatus(statusAfterRenew);
                            reservedInstancePOAfterRenew.setEffectiveTime(effectiveTimeAfterRenew);
                            reservedInstancePOsAfterRenew.add(reservedInstancePOAfterRenew);
                            resourceUuidsToUpdateOrderId.add(resourceUuid);
                        }
                    }
                }
                if (isFailedOrder) {
                    break;
                }
            }

            if (isRenewOrder) {
                // 续费订单
                UpdateOrderRequest request = new UpdateOrderRequest();
                request.setStatus(OrderStatus.CREATED);
                if (isFailedOrder) {
                    request.setStatus(OrderStatus.CREATE_FAILED);
                }
                Order updatedOrder = bestEffortUpdateOrder(orderClient, order.getUuid(), request);
                if (updatedOrder == null) {
                    return result;
                }
                if (resourceUuidsToUpdateOrderId.size() > 0) {
                    reservedInstanceDao.updateOrderIdByResourceUuid(resourceUuidsToUpdateOrderId, order.getUuid());
                }
                if (reservedInstancePOsAfterRenew.size() > 0) {
                    reservedInstanceDao.updateExpireTimeAndEffectiveTimeAndStatusByResourceUuid(
                            reservedInstancePOsAfterRenew);
                }
            } else {
                // 新购订单
                for (Item item : order.getItems()) {
                    // 解析 extra 可能会有异常抛出，资源预留前都解析一次
                    parseExtra(item.getExtra());    
                }

                List<ReservedInstancePO> reservedInstancePOList = 
                        reservedInstanceDao.listReservedInstancesByOrderId(order.getUuid());
                boolean isInvalidEffectiveTime = false;
                for (ReservedInstancePO reservedInstancePO : reservedInstancePOList) {
                    if (reservedInstancePO.getEffectiveTime().before(currentLocalTime)) {
                        LOGGER.info("[ReservedInstance][OrderExecute]change order {} status to failed, " +
                                "because effective time {} before {}", order.getUuid(), 
                                reservedInstancePO.getEffectiveTime(), currentLocalTime);
                        // 供需匹配中生效时间比当前时间早
                        isInvalidEffectiveTime = true;
                        break;
                    }
                }

                // 用户付费时间晚于预留实例券生效时间时，将订单标记成 CREATE_FAILED；
                // 不然将订单更新成创建中
                UpdateOrderRequest request = new UpdateOrderRequest();
                request.setStatus(OrderStatus.CREATING);
                if (isInvalidEffectiveTime) {
                    request.setStatus(OrderStatus.CREATE_FAILED);
                }
                bestEffortUpdateOrder(orderClient, order.getUuid(), request);

                if (!isInvalidEffectiveTime) {
                    // 有效订单更新预留实例券状态为创建中
                    for (ReservedInstancePO reservedInstancePO : reservedInstancePOList) {
                        reservedInstancePO.setStatus(ReservedInstancePO.Status.CREATING);
                    }
                    reservedInstanceDao.batchUpdateStatus(reservedInstancePOList);
                }
            }
        } catch (Exception e) {
            LOGGER.error("[ReservedInstance][OrderExecute] failed, exception is {}", e);
            return result;
        } finally {
            orderClient.unlock(order.getUuid());
        }
        LOGGER.debug("[ReservedInstance][order callback]orderID {} end", order.getUuid());
        return result;
    }

    public ExecutionResult check(OrderClient orderClient, ResourceClient resourceClient,
            Order order) {
        LOGGER.debug("[ReservedInstance][order callback]orderID {}, status CREATING", order.getUuid());
        ExecutionResult result = new ExecutionResult(ExecutionStatus.SUCCESS);
        try {
            // TODO: 检查资源预留完成
            // 获取订单关联的所有预留实例券
            List<ReservedInstancePO> reservedInstancePOList = 
                    reservedInstanceDao.listReservedInstancesByOrderId(order.getUuid());
            // 如果没绑定billing的预留实例券实体信息，才向billing发起创建请求
            Boolean usagePackageCreated = false;
            for (ReservedInstancePO reservedInstancePO : reservedInstancePOList) {
                // 由于同一个订单的预留实例券实体由一个请求统一创建，只要有一个绑定了就说明已经创建过
                if (!reservedInstancePO.getReservedInstanceUuid().isEmpty()) {
                    usagePackageCreated = true;
                    break;
                }
            }

            boolean isFailedOrder = false;
            if (!usagePackageCreated) {
                // 由于创建券可能反复失败导致卡订单执行，创建billing的预留实例券实体前，先检查订单是否超时，超时则失败
                if (isOrderTimeout(order)) {
                    LOGGER.info("[ReservedInstance][OrderExecute]change order {} status to failed, " +
                            "because order timeout", order.getUuid());
                    isFailedOrder = true;
                } else {
                    // 创建billing的预留实例券实体
                    List<PackageInfo> packageInfoList = createUsagePackage(order);
                    // 会将packageInfoList中packageName填写到reservedInstancePOList对应条目reservedInstanceUuid字段
                    pairUsagePackageWithReservdInstancePO(order.getUuid(), packageInfoList, reservedInstancePOList);
                }
            }

            if (isFailedOrder) {
                UpdateOrderRequest request = new UpdateOrderRequest();
                request.setStatus(OrderStatus.CREATE_FAILED);
                bestEffortUpdateOrder(orderClient, order.getUuid(), request);
            } else {
                // 更新订单到完成
                UpdateOrderRequest request = new UpdateOrderRequest();
                request.setStatus(OrderStatus.CREATED);
                List<ResourceMapping> list = new ArrayList<>();
                request.setResources(list);
                // 设定预留资源券实体与 billing usage package 映射关系
                for (ReservedInstancePO reservedInstancePO : reservedInstancePOList) {
                    if (!reservedInstancePO.getReservedInstanceUuid().isEmpty()) {
                        // 更新资源映射
                        ResourceMapping resourceMapping = new ResourceMapping();
                        resourceMapping.setId(reservedInstancePO.getReservedInstanceUuid());
                        resourceMapping.setKey(reservedInstancePO.getReservedInstanceId());
                        resourceMapping.setShortId(reservedInstancePO.getReservedInstanceId());
                        resourceMapping.setStatus(ResourceStatus.RUNNING);
                        list.add(resourceMapping);
                    }
                }
                Order updatedOrder = bestEffortUpdateOrder(orderClient, order.getUuid(), request);
                if (updatedOrder == null) {
                    return result;
                }
                Resources resources = getResources(updatedOrder.getResourceIds(), resourceClient);
                for (Resource resource : resources) {
                    for (int i = 0; i != reservedInstancePOList.size(); i++) {
                        ReservedInstancePO reservedInstancePO = reservedInstancePOList.get(i);
                        if (resource.getOrderItemKey().equals(reservedInstancePO.getReservedInstanceId())) {
                            reservedInstancePO.setResourceUuid(resource.getUuid());
                            break;
                        }
                    }
                }
                reservedInstanceDao.batchUpdateResourceUuid(reservedInstancePOList);
            }
        } catch (Exception e) {
            // 创建失败, 之后再创建,不检查超时,不更新订单,后端实现出创建幂等性
            LOGGER.error("create ReservedInstance[package] error, orderId:{}, error: {}", order.getUuid(), e);
            return new ExecutionResult(ExecutionStatus.FAILURE);
        } finally {
            orderClient.unlock(order.getUuid());
        }
        LOGGER.debug("[ReservedInstance][order callback]orderID {} end", order.getUuid());
        return result;
    }

    public void createAutoRenewRule(List<ReservedInstancePO> reservedInstanceList, Order order) {
        if (reservedInstanceList.size() == 0) {
            return;
        }
        AutoRenewClient autoRenewClient = logicPodClientFactory.createAutoRenewClient(order.getAccountId());
        for (Item item : order.getItems()) {
            String reservedInstanceUuid = "";
            ReservedInstancePO reservedInstancePO = null;
            for (ReservedInstancePO reservedInstance : reservedInstanceList) {
                if (reservedInstance.getReservedInstanceId().equals(item.getKey())) {
                    reservedInstanceUuid = reservedInstance.getReservedInstanceUuid();
                    reservedInstancePO = reservedInstance;
                    break;
                }
            } 
            if (reservedInstanceUuid.isEmpty() || reservedInstancePO == null) {
                continue;
            }
            ReservedInstanceExtra reservedInstanceExtra = parseExtra(item.getExtra());
            if (reservedInstanceExtra.isAutoRenew()) {
                // 查询是否已经建了自动续费规则
                boolean hasAutoRenewRule = false;
                try {
                    hasAutoRenewRule = reservedInstanceHasAutoRenewRule(item, order.getAccountId(), 
                            reservedInstanceUuid);
                } catch (Exception e) {
                    LOGGER.error("[ReservedInstance]getAutoRenewRules error, resourceUuid:{}, error:{}",
                            reservedInstanceUuid, e);
                    continue;
                }

                if (!hasAutoRenewRule) {
                    AutoRenewCreateRequest autoRenewCreateRequest = new AutoRenewCreateRequest();
                    autoRenewCreateRequest.setAccountId(order.getAccountId());
                    autoRenewCreateRequest.setServiceType(item.getServiceType());
                    autoRenewCreateRequest.setRegion(item.getRegion());
                    autoRenewCreateRequest.setRenewTime(reservedInstanceExtra.getAutoRenewTimePeriod());
                    autoRenewCreateRequest.setRenewTimeUnit(reservedInstanceExtra.getAutoRenewTimeUnit());
                    List<String> serviceIds = new ArrayList<>();
                    serviceIds.add(reservedInstanceUuid);
                    autoRenewCreateRequest.setServiceIds(serviceIds);
                    // 创建自动续费规则，等待下次检查预留实例券是否绑定了自动续费规则
                    autoRenewClient.createAutoRenewRule(autoRenewCreateRequest);
                } else {
                    // 如果已经绑定了自动续费规则，更新预留实例券状态为INACTIVE
                    reservedInstanceDao.updateStatus(reservedInstancePO.getAccountId(), 
                            reservedInstancePO.getReservedInstanceId(), ReservedInstancePO.Status.INACTIVE);
                }

            } else {
                // 如果没有开启自动续费，更新预留实例券状态为INACTIVE
                reservedInstanceDao.updateStatus(reservedInstancePO.getAccountId(), 
                            reservedInstancePO.getReservedInstanceId(), ReservedInstancePO.Status.INACTIVE);
            }
        }
    }

    private boolean reservedInstanceHasAutoRenewRule(Item item, String accountId, String reservedInstanceUuid) {
        AutoRenewClient autoRenewClient = logicPodClientFactory.createAutoRenewClient(accountId);
        ListAutoRenewRequest req = new ListAutoRenewRequest();
        req.setAccountId(accountId);
        req.setServiceType(item.getServiceType());
        req.setBegin(0);
        req.setLimit(1);
        req.setServiceId(reservedInstanceUuid);
        req.setRegion(item.getRegion());
        AutoRenewRules autoRenewRules = autoRenewClient.getAutoRenewRules(req);
        return CollectionUtils.isNotEmpty(autoRenewRules);
    }

    /**
     * 绑定订单中的资源标签到预留实例券上
     *
     * @param reservedInstanceList 预留实例列表
     * @param order               订单信息
     */
    public void bindTags(List<ReservedInstancePO> reservedInstanceList, Order order) {
        if (reservedInstanceList.size() == 0) {
            return;
        }
        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(order.getAccountId());
        for (Item item : order.getItems()) {
            ReservedInstanceExtra reservedInstanceExtra = parseExtra(item.getExtra());
            List<Tag> tags = reservedInstanceExtra.getTags();
            String reservedInstanceUuid = "";
            ReservedInstancePO reservedInstancePO = null;
            for (ReservedInstancePO reservedInstance : reservedInstanceList) {
                if (reservedInstance.getReservedInstanceId().equals(item.getKey())) {
                    reservedInstanceUuid = reservedInstance.getReservedInstanceUuid();
                    reservedInstancePO = reservedInstance;
                    break;
                }
            } 
            if (reservedInstanceUuid.isEmpty() || reservedInstancePO == null) {
                continue;
            }
            
            if (tags != null && !tags.isEmpty()) {
                // 查询是否已经绑定了tag
                boolean hasTags = false;
                try {
                    hasTags = logicalTagService.hasTags(reservedInstanceUuid, order.getAccountId());
                } catch (Exception e) {
                    LOGGER.error("[ReservedInstance]get tags error, resourceUuid:{}, error:{}",
                            reservedInstanceUuid, e);
                    continue;
                }

                if (!hasTags) {
                    List<AssignResource> assignResources = new ArrayList<>();
                    AssignResource assignResource = new AssignResource();
                    assignResource.setRegion(regionConfiguration.getCurrentRegion());
                    assignResource.setResourceId(reservedInstancePO.getReservedInstanceId());
                    assignResource.setResourceUuid(reservedInstanceUuid);
                    assignResource.setServiceType(PodConstants.SERVICE_TYPE);
                    assignResource.setTags(tags);
                    assignResources.add(assignResource);

                    try {
                        CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                        createAndAssignTagRequest.setResources(assignResources);
                        tagClient.createAndAssignTag(createAndAssignTagRequest);
                    } catch (BceException bce) {
                        throw new BceInternalResponseException(bce.getMessage(), bce.getHttpStatus(), bce.getCode());
                    }
                }
            }
        }
    }
    /**
     * 更新订单，若失败重试三次
     *
     * @param client
     * @param orderId
     * @param updateOrderRequest
     */
    private Order bestEffortUpdateOrder(OrderClient client, String orderId, UpdateOrderRequest updateOrderRequest) {
        int count = RETRY_NUM;
        boolean flag = false;
        Order order = null;
        while (count-- > 0) {
            try {
                order = client.update(orderId, updateOrderRequest);
                flag = true;
                break;
            } catch (Exception ex) {
                LOGGER.error("Update order status error, retry {}, orderId is {}, exception is {}", count, orderId, ex);
            }
        }
        if (!flag) {
            return null;
        }
        return order;
    }

    private ReservedInstanceExtra parseExtra(String extraString) {
        ReservedInstanceExtra reservedInstanceExtra = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            reservedInstanceExtra = objectMapper.readValue(extraString, ReservedInstanceExtra.class);
        } catch (IOException e) {
            LOGGER.error("[ReservedInstance]parse extra failed, exception is {}", e);
            throw new BceException(e.getMessage());
        }
        return reservedInstanceExtra;
    }

    private Resources getResources(List<String> resourceUuids, ResourceClient resourceClient) {
        ResourceIds resourceIds = new ResourceIds();
        resourceIds.setResourceIds(resourceUuids);
        Resources resources = new Resources();
        int tryTime = 3;
        while (tryTime-- > 0) {
            try {
                resources = resourceClient.getResourcesByIds(resourceIds);
                if (resources.size() == resourceUuids.size()) {
                    break;
                } else {  // 可能主从同步导致查询不到，之后重试
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        LOGGER.debug("getResourcesByIds,exception:{}", e);
                    }
                }
            } catch (Exception e) {
                LOGGER.debug("getResourcesByIds.exception:{},resourceUuids:{}",
                        e.getMessage(), Arrays.toString(resourceUuids.toArray()));
                throw e;
            }
        }
        return resources;        
    }

    private void pairUsagePackageWithReservdInstancePO(String orderId, List<PackageInfo> packageInfoList, 
            List<ReservedInstancePO> reservedInstancePOList) {
        for (PackageInfo packageInfo : packageInfoList) {
            ReservedInstancePO reservedInstancePOToPair = null;
            for (int i = 0; i != reservedInstancePOList.size(); i++) {
                ReservedInstancePO reservedInstancePO = reservedInstancePOList.get(i);
                if (reservedInstancePO.getReservedInstanceId().equals(packageInfo.getOrderItemKey())) {
                    reservedInstancePO.setReservedInstanceUuid(packageInfo.getPackageName().toString());
                    reservedInstancePOToPair = reservedInstancePO;
                    break;
                }
            }
            if (reservedInstancePOToPair == null) {
                LOGGER.debug("check ReservedInstance[package] for accountId:{}, order:{}, packageId:{} " +
                        "failed, ReservedInstancePO to pair not found", packageInfo.getAccountId(), orderId,
                        packageInfo.getPackageName().toString());
                continue;
            }
        }
        reservedInstanceDao.batchUpdateUuid(reservedInstancePOList);
        reservedInstanceDao.batchUpdateStatus(reservedInstancePOList);
    }

    public boolean isReservedInstanceOrder(Order order) {
        return reservedInstanceDao.listReservedInstancesByOrderId(order.getUuid()).size() > 0;
    }

    public boolean isOrderTimeout(Order order) {
        Date baseTime = order.getUpdateTime();

        Calendar retryEndTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        retryEndTime.setTime(baseTime);
        retryEndTime.add(Calendar.MINUTE, ORDER_TIMEOUT_MINUTE);
        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        return now.after(retryEndTime);
    }

    public List<PackageInfo> createUsagePackage(Order order) {
        // 根据预留实例券订单生成量包订单
        com.baidu.bce.internalsdk.order.model.order.Order usagePackageOrder = JsonUtil.fromJSON(JsonUtil.toJSON(order),
        com.baidu.bce.internalsdk.order.model.order.Order.class);
        // 对每个量包设置shortId为BCI预留实例券id (reserved_instance_id), 此id记录在订单项key里
        for (int itemIdx = 0; itemIdx < usagePackageOrder.getItems().size(); itemIdx++) {
            ChargeItem chargeItem = new ChargeItem();
            chargeItem.setName("assignShortId");
            chargeItem.setValue(String.format("[\"%s\"]", usagePackageOrder.getItems().get(itemIdx).getKey()));
            chargeItem.setScale(new BigDecimal("1"));
            usagePackageOrder.getItems().get(itemIdx).getFlavor().add(chargeItem);
        }
        UsagePackageService usagePackageService = 
                logicPodClientFactory.createUsagePackageService(order.getAccountId());
        LOGGER.debug("create ReservedInstance[package] for accountId:{}, order:{} start", 
                order.getAccountId(), order.getUuid());
        List<PackageInfo> packageInfoList = usagePackageService.create(usagePackageOrder);
        LOGGER.debug("create ReservedInstance[package] for accountId:{}, order:{} finish, packageId:{}", 
                order.getAccountId(), order.getUuid());
        return packageInfoList;
    }
}