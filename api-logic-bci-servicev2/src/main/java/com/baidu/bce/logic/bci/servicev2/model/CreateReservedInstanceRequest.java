package com.baidu.bce.logic.bci.servicev2.model;

import java.sql.Timestamp;
import java.util.List;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CreateReservedInstanceRequest {
    private String name;
    private String scope;
    private String logicalZone = "";
    private String physicalZone = "";
    private boolean reserveResource = true;
    private String purchaseMode;
    private String reservedSubServiceType = "";
    private String reservedSpec;
    private int reservedInstanceCount;
    private String reservedTimeUnit;
    private int reservedTimePeriod;
    private boolean autoRenew = false;
    private String autoRenewTimeUnit;
    private int autoRenewTimePeriod;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp effectiveTime;
    private String renewDeadline;
    private List<Tag> tags;

    
}
