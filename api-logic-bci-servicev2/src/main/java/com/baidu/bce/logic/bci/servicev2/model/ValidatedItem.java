package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bcc.sdk.model.flavor.ZoneResourceDetail;
import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Accessors(chain = true)
public class ValidatedItem {
    private PodPurchaseRequest podPurchaseRequest;
    private EipPurchaseRequest eipPurchaseRequest;

    private String from;
    private String clientToken;
    private String productType;
    private String accessKey;
    private List<String> whiteList;

    private Set<PaymentModel> bciPaymentModel;
    private int bciPurchaseOrder;
    private Set<PaymentModel> eipPaymentModel;
    private int eipPurchaseOrder;
    // 存储多个zone信息(logicalZone、physicalZone、subnetUuid等)
    private Map<String, ZoneMapDetail> zoneMap;
    // 存储多个subnet详细信息
    private Map<String, SubnetVo> subnetVoMap;
    // 存储多个vpc详细信息
    private Map<String, VpcVo> vpcVoMap;
    // 需要存储多个zone的资源信息(cds资源、bcc资源)
    private Map<String, ZoneResourceDetail> zoneResourceDetails;
    // 资源向上取整的标记
    private Boolean roundUpFlag;
    // 资源向上取整后的结果
    private Map<String, Float> roundUpResult;
    // 标记是否是BciV3
    private Boolean isBciV3;
    // 标记pod中是否存在DaemensSetContainer
    private Boolean hasDsContainer;
    // pod中的annotations
    private Map<String, Object> podAnnotationsMap = new HashMap<>();

    public Object getAnnotationsValueByKey(String key) {
        return podAnnotationsMap.get(key);
    }
}
