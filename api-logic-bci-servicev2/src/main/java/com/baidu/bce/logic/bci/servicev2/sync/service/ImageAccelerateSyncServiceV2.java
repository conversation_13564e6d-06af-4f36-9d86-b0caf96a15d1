package com.baidu.bce.logic.bci.servicev2.sync.service;

import com.baidu.bce.logic.bci.daov2.imageaccelerate.ImageAccelerateDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.ImageCacheDaoV2;
import com.baidu.bce.logic.bci.daov2.imagecachev2.model.ImageCachePO;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.ImageDetailDaoV2;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.model.ImageDetailPO;
import com.baidu.bce.logic.bci.servicev2.constant.ImageCacheStatus;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.model.ImageAccelerateCRDResponse;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.pod.ImageCacheServiceV2;
import com.baidu.bce.logic.core.utils.UUIDUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service
public class ImageAccelerateSyncServiceV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageAccelerateSyncServiceV2.class);

    @Autowired
    private K8sService k8sService;

    @Autowired
    protected ImageAccelerateDaoV2 imageAccelerateDao;

    @Autowired
    private ImageCacheDaoV2 imageCacheDaoV2;

    @Autowired
    private PodNewOrderExecutorServiceV2 podNewOrderExecutorService;

    @Autowired
    private ImageDetailDaoV2 imageDetailDaoV2;

    @Autowired
    private ImageCacheServiceV2 imageCacheServiceV2;

    @Value("${image.accelerate.gc.minute.interval:43200}")
    private int imageAccelerateGcMinuteInterval;

    @Value("${image.accelerate.gc.disabled.users:1d460bfea2814f1a964dff051212c8d1,6c47a952db4444c5a097b41be3f24c94}")
    private String imageAccelerateGcDisabledUsers;

    @Value("${image.accelerate.failed.record.gc.minute.interval:1440}")
    private int imageAccelerateFailedRecordGcMinuteInterval;

    private long gcImageCacheSchedulerRunTimes = 0;

    // 获取处于creating状态的，需要回收状态
    public List<ImageCachePO> getAllNeedSyncImageCache(String status) {
        ImageCachePO imageCachePO = new ImageCachePO();
        imageCachePO.setStatus(status);
        List<ImageCachePO> imageCachePOs = imageCacheDaoV2.getImageCacheByStatus(imageCachePO);
        return imageCachePOs;
    }

    // 同步逻辑
    public void syncImageCacheCrd() {
        // 获取需要同步的镜像缓存
        List<ImageCachePO> imageCachePOs = getAllNeedSyncImageCache(ImageCacheStatus.CREATING.getName());
        // 如果没有需要同步的缓存项
        LOGGER.debug("image cache pos is {}", imageCachePOs);
        if (imageCachePOs != null && imageCachePOs.size() > 0) {
            for (ImageCachePO imageCache : imageCachePOs) {
                LOGGER.debug("image cache po is {}", imageCache);
                String nameSpace = imageCache.getAccountId();
                String imageCacheName = imageCache.getImageCacheName();
                // 先看是否超时，如果超时直接置为failed，并删除crd。
                Long utcTimeDiff = 28800000L;
                Long curUtcTimeStamp = System.currentTimeMillis() + utcTimeDiff;
                Long createGmt8TimeStamp = imageCache.getCreatedAt().getTime();
                LOGGER.debug("curUtcTimeStamp - curUtcTimeStamp is {}", (curUtcTimeStamp - createGmt8TimeStamp));
                int afterMinuteTime = (int) (curUtcTimeStamp - createGmt8TimeStamp) / (60 * 1000);
                LOGGER.debug("afterMinuteTime is {}", afterMinuteTime);
                // 超时回收
                if (afterMinuteTime > imageAccelerateFailedRecordGcMinuteInterval) {
                    // 镜像制作超过24小时将会被超时回收
                    LOGGER.debug("image cache {} ceation exceeds 24 hours and needs to be timed out for recycling", imageCacheName);
                    // 删除缓存crd
                    k8sService.deleteImageAccCRD(nameSpace, imageCacheName);
                    // 更新镜像缓存为fail
                    imageCache.setStatus(ImageCacheStatus.FAILED.getName());
                    // 更新镜像详细deleted的为true
                    List<ImageDetailPO> imageDetails =
                        imageDetailDaoV2.getImageDetailsByImageCacheId(imageCache.getImageCacheId());
                    for (ImageDetailPO imageDetail : imageDetails) {
                        imageDetail.setDeleted(true);
                    }
                    imageCacheServiceV2.updateImageCacheStatus(imageCache, imageDetails);
                    // 销毁订单转储资源
                    if (!imageCache.getImageCacheName().startsWith("auto-create")) {
                        imageCacheServiceV2.destoryTcPodResource(imageCache);
                    }
                    LOGGER.debug("end image cache {} recycling", imageCacheName);
                    // 超时gc逻辑完成，进入下一次循环处理逻辑
                    continue;
                }
                LOGGER.debug("nameSpace is {}, imageCacheName is {}", nameSpace, imageCacheName);
                ImageAccelerateCRDResponse imageAccelerateCRDResponse = new ImageAccelerateCRDResponse();
                imageAccelerateCRDResponse = k8sService.getImageAccCRD(nameSpace, imageCacheName);
                LOGGER.debug("imageAccelerateCRDResponse is {}", imageAccelerateCRDResponse);
                if (imageAccelerateCRDResponse == null) {
                    // 考虑是否需要报错
                    continue;
                }
                ImageCachePO imageCachePO = imageCacheDaoV2.getImageCacheByName(imageCacheName, nameSpace);
                String imageCacheId = imageCachePO.getImageCacheId();
                ImageAccelerateCRDResponse.ImageAccelerateCRDStatus imageCacheStatus =
                    imageAccelerateCRDResponse.getStatus();
                if (imageCacheStatus == null) {
                    // 考虑是否需要报错
                    continue;
                }
                Map<String, String> imageDetailMap = imageCacheStatus.getImageMirrors();
                Map<String, String> imageDetailStatusMap = imageCacheStatus.getImageStatus();
                Map<String, Integer> imageDetailProgressMap = imageCacheStatus.getImageProgress();
                // 先看CcrReady字段,CcrReady 字段为true,不需要check,所有缓存已经ready。
                boolean isCcrReady = imageAccelerateCRDResponse.getStatus().isCcrReady();
                String phase = imageAccelerateCRDResponse.getStatus().getPhase();
                if (isCcrReady) {
                    // ccr类型缓存和cds类型缓存都需要修改image detail表
                    // 更新镜像缓存和镜像信息
                    List<ImageDetailPO> imageDetails = imageDetailDaoV2.getImageDetailsByImageCacheId(imageCacheId);
                    if (imageDetails == null
                        || imageCacheStatus.getImageMirrors() == null
                        || imageDetails.size() != imageCacheStatus.getImageMirrors().size()) {
                        // 考虑是否需要报错
                        continue;
                    }
                    imageCachePO.setStatus(ImageCacheStatus.SUCCESS.getName());
                    imageCachePO.setProgress(100);
                    imageCachePO.setCdsSnapShotId(imageCacheStatus.getCdsSnapshotID());
                    boolean imageLost = false;
                    for (ImageDetailPO imageDetail : imageDetails) {
                        String originImage =
                            imageDetail.getOriginImageAddress() + ":" + imageDetail.getOriginImageVersion();
                        if (imageDetailMap.containsKey(originImage)) {
                            String acceImage = imageDetailMap.get(originImage);
                            String[] acceImageSplit = acceImage.split(":");
                            String acceImageAddress = acceImageSplit[0];
                            String acceImageVersion = acceImageSplit[1];
                            imageDetail.setAcceImageAddress(acceImageAddress);
                            imageDetail.setAcceImageVersion(acceImageVersion);
                        } else {
                            // 数据库中的镜像不在CRD当中（理论上不应该存在这种情况）
                            LOGGER.error("image {} does not exist", originImage);
                            imageLost = true;
                            break;
                        }
                    }
                    if (imageLost) {
                        LOGGER.error("sync imc {} failed", imageCachePO);
                        // 暂时不删除缓存crd, 用于排查
                        // k8sService.deleteImageAccCRD(nameSpace, imageCacheName);
                        // 更新镜像缓存为fail
                        imageCachePO.setStatus(ImageCacheStatus.FAILED.getName());
                        // 更新镜像详细deleted的为true
                        for (ImageDetailPO imageDetail : imageDetails) {
                            imageDetail.setDeleted(true);
                        }
                    }
                    imageCacheServiceV2.updateImageCacheStatus(imageCachePO, imageDetails);
                    // 销毁订单转储资源
                    if (!imageCache.getImageCacheName().startsWith("auto-create")) {
                        imageCacheServiceV2.destoryTcPodResource(imageCache);
                    }
                    if (!imageLost){
                        // 更新数据库，删除同名冗余镜像details数据
                        for (ImageDetailPO imageDetail : imageDetails) {
                            List<ImageDetailPO> allImageDetailRecord =
                                    imageDetailDaoV2.getImageDetailByImageAddressTagOrderByIdDesc(
                                            imageDetail.getOriginImageAddress(),
                                            imageDetail.getOriginImageVersion(),
                                            imageDetail.getAccountId());
                            if (allImageDetailRecord.size() < 2) {
                                continue;
                            }
                            for (int i = 1; i < allImageDetailRecord.size(); i++) {
                                ImageDetailPO imageDetailToDelete = allImageDetailRecord.get(i);
                                imageDetailToDelete.setDeleted(true);
                                Timestamp timestamp = new Timestamp(System.currentTimeMillis() + utcTimeDiff);
                                imageDetailToDelete.setUpdatedAt(timestamp);
                                imageDetailToDelete.setDeletedAt(timestamp);
                                imageDetailDaoV2.updateImageDetail(imageDetailToDelete);
                            }
                        }
                    }
                } else {
                        if ("Failed".equals(phase)) {
                             // 删除缓存crd
                            k8sService.deleteImageAccCRD(nameSpace, imageCacheName);
                            // 更新镜像缓存为fail
                            imageCachePO.setStatus(ImageCacheStatus.FAILED.getName());
                            // 更新镜像详细deleted的为true
                            List<ImageDetailPO> imageDetails =
                                imageDetailDaoV2.getImageDetailsByImageCacheId(imageCacheId);
                            for (ImageDetailPO imageDetail : imageDetails) {
                                imageDetail.setDeleted(true);
                            }
                            imageCacheServiceV2.updateImageCacheStatus(imageCachePO, imageDetails);
                            // 销毁订单转储资源
                                if (!imageCache.getImageCacheName().startsWith("auto-create")) {
                                imageCacheServiceV2.destoryTcPodResource(imageCache);
                            }
                            continue;
                        } 
                        // 查看是否有fail的镜像，如果有，则置为fail，并删除镜像缓存crd。
                        LOGGER.debug("image cache status is {}", imageDetailStatusMap);
                        boolean hasImageFailed = false;
                        for (Map.Entry<String, String> imageStatus : imageDetailStatusMap.entrySet()) {
                            if (imageStatus.getValue().equals("Failed")) {
                                // 删除缓存crd
                                k8sService.deleteImageAccCRD(nameSpace, imageCacheName);
                                // 更新镜像缓存为fail
                                imageCachePO.setStatus(ImageCacheStatus.FAILED.getName());
                                // 更新镜像详细deleted的为true
                                List<ImageDetailPO> imageDetails =
                                    imageDetailDaoV2.getImageDetailsByImageCacheId(imageCacheId);
                                for (ImageDetailPO imageDetail : imageDetails) {
                                    imageDetail.setDeleted(true);
                                }
                                imageCacheServiceV2.updateImageCacheStatus(imageCachePO, imageDetails);
                                // 销毁订单转储资源
                                 if (!imageCache.getImageCacheName().startsWith("auto-create")) {
                                    imageCacheServiceV2.destoryTcPodResource(imageCache);
                                }
                                hasImageFailed = true;
                                break;
                            }
                        }
                        if (!hasImageFailed) {
                            // 获取实时进度并更新
                            if (imageDetailProgressMap.size() == 0) {
                                break;
                            }
                            int progress = 0;
                            for (Map.Entry<String, Integer> imageProgress : imageDetailProgressMap.entrySet()) {
                                progress = progress + imageProgress.getValue();
                            }
                            progress = progress / imageDetailProgressMap.size();
                            imageCachePO.setProgress(progress);
                            imageCacheServiceV2.updateImageCacheStatus(imageCachePO, null);
                        }
                }
            }
        }
    }

    // 镜像gc逻辑
    public void gcImageCache() {
        gcImageCacheSchedulerRunTimes++;
        LOGGER.debug("start gcImageCache runTimes {}", gcImageCacheSchedulerRunTimes);
        // 获取需要同步的镜像缓存
        List<ImageCachePO> succImageCachePOs = getAllNeedSyncImageCache(ImageCacheStatus.SUCCESS.getName());
        List<ImageCachePO> failImageCachePOs = getAllNeedSyncImageCache(ImageCacheStatus.FAILED.getName());
        List<ImageCachePO> creatingImageCachePOs = getAllNeedSyncImageCache(ImageCacheStatus.CREATING.getName());
        List<String> imageAccelerateGcDisabledUsersList =  Arrays.asList(imageAccelerateGcDisabledUsers.split(","));
        List<ImageCachePO> imageCachePOs = new ArrayList<>();
        for (ImageCachePO imageCachePO : succImageCachePOs) {
            if (!imageAccelerateGcDisabledUsersList.contains(imageCachePO.getAccountId())) {
                imageCachePOs.add(imageCachePO);
            }
        }
        for (ImageCachePO imageCachePO : failImageCachePOs) {
            if (!imageAccelerateGcDisabledUsersList.contains(imageCachePO.getAccountId())) {
                imageCachePOs.add(imageCachePO);
            }
        }
        for (ImageCachePO imageCachePO : creatingImageCachePOs) {
            if (!imageAccelerateGcDisabledUsersList.contains(imageCachePO.getAccountId())) {
                imageCachePOs.add(imageCachePO);
            }
        }

        // 如果没有需要同步的缓存项
        LOGGER.debug("gcImageCache runTimes {} imageCachePOs size {}",
                gcImageCacheSchedulerRunTimes, imageCachePOs.size());
        if (imageCachePOs == null || imageCachePOs.size() == 0) {
            LOGGER.debug("end gcImageCache runTimes {}", gcImageCacheSchedulerRunTimes);
            return;
        }
        
        for (Iterator<ImageCachePO> iterator = imageCachePOs.iterator(); iterator.hasNext();) {
            ImageCachePO imageCache = iterator.next();
            // 是否超时，如果超时直接置为deleted，并删除crd。
            Long utcTimeDiff = 28800000L;
            Long curUtcTimeStamp = System.currentTimeMillis() + utcTimeDiff;
            Long updateGmt8TimeStamp = imageCache.getUpdatedAt().getTime();
            int afterMinuteTime = (int) ((curUtcTimeStamp - updateGmt8TimeStamp) / (60 * 1000));
            if (afterMinuteTime > imageAccelerateGcMinuteInterval) {
                LOGGER.debug("gcImageCache runTimes {} gc imageCacheName {} afterMinuteTime is {}",
                        gcImageCacheSchedulerRunTimes,
                        imageCache.getImageCacheName(),
                        afterMinuteTime);
                updateImageCacheStatus(imageCache);
                iterator.remove();
            }
        }
        
        HashMap<String, List<ImageCachePO>> imageCacheMap = new HashMap<>();
        for (ImageCachePO imageCache : imageCachePOs) {
            String accountId = imageCache.getAccountId();
            if (imageCacheMap.containsKey(accountId)) {
                List<ImageCachePO> imageCachePOList = imageCacheMap.get(accountId);
                imageCachePOList.add(imageCache);
            } else {
                List<ImageCachePO> imageCachePOList = new ArrayList<>();
                imageCachePOList.add(imageCache);
                imageCacheMap.put(accountId, imageCachePOList);
            }
        }
        // 对imageCacheMap遍历
        Iterator<String> iterator = imageCacheMap.keySet().iterator();
        while (iterator.hasNext()) {
            String accountId = iterator.next();
            List<ImageCachePO> imageCacheList = imageCacheMap.get(accountId);
            int maxImageCacheNumber = imageCacheServiceV2.getImageCacheCntInConfByAccountId(accountId);
            int imageCacheToDeleteNum = imageCacheList.size() - maxImageCacheNumber;
            if (imageCacheToDeleteNum > 0) {
                Collections.sort(imageCacheList, new Comparator<ImageCachePO>() {
                    @Override
                    public int compare(ImageCachePO o1, ImageCachePO o2) {
                        return o1.getUpdatedAt().compareTo(o2.getUpdatedAt());
                    }
                });
                for (int i = 0; i != imageCacheToDeleteNum; i++) {
                    ImageCachePO imageCache = imageCacheList.get(i);
                    LOGGER.debug("gcImageCache runTimes {} Number of ImageCache limit {} and gc imageName {}",
                            gcImageCacheSchedulerRunTimes, maxImageCacheNumber, imageCache.getImageCacheName());
                    updateImageCacheStatus(imageCache);
                }
            }
        }
        LOGGER.debug("end gcImageCache runTimes {}", gcImageCacheSchedulerRunTimes);
    }

    private void updateImageCacheStatus(ImageCachePO imageCache) {
        Long utcTimeDiff = 28800000L;
        String nameSpace = imageCache.getAccountId();
        String imageCacheName = imageCache.getImageCacheName();
        // 删除缓存crd
        k8sService.deleteImageAccCRD(nameSpace, imageCacheName);
        imageCache.setImageCacheName(imageCacheName + "deleted" + UUIDUtil.generateShortUuid().toLowerCase());
        imageCache.setDeleted(true);
        imageCache.setUpdatedAt(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
        imageCache.setDeletedAt(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
        // 更新镜像详细deleted的为true
        List<ImageDetailPO> imageDetails =
                imageDetailDaoV2.getImageDetailsByImageCacheId(imageCache.getImageCacheId());
        for (ImageDetailPO imageDetail : imageDetails) {
            imageDetail.setDeleted(true);
            imageDetail.setDeletedAt(new Timestamp(System.currentTimeMillis() + utcTimeDiff));
        }
        imageCacheServiceV2.updateImageCacheStatus(imageCache, imageDetails);
    }
}
