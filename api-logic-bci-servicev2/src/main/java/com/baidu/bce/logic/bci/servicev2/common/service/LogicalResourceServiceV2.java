package com.baidu.bce.logic.bci.servicev2.common.service;

import com.baidu.bce.billing.resourcemanager.model.ResourceDetail;
import com.baidu.bce.billing.resourcemanager.model.ResourceIdentify;
import com.baidu.bce.billing.resourcemanager.model.ResourceStatus;
import com.baidu.bce.billing.resourcemanager.model.ResultStatus;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.request.DeleteRequest;
import com.baidu.bce.billing.resourcemanager.service.request.FilterPauseResRequest;
import com.baidu.bce.billing.resourcemanager.service.request.ResourceListRequest;
import com.baidu.bce.billing.resourcemanager.service.request.UseStatusRequest;
import com.baidu.bce.billing.resourcemanager.service.response.ResourceQueryResponse;
import com.baidu.bce.billing.resourcemanager.service.response.Response;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.ExtendedGetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.GetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.bci.servicev2.LogicPodClientFactoryV2;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class LogicalResourceServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogicalResourceServiceV2.class);

    @Autowired
    LogicPodClientFactoryV2 logicPodClientFactory;

    @Autowired
    RegionConfiguration regionConfiguration;

    @Value("${bcc.billing.deleteResourceV2:true}")
    boolean deleteResourceV2;

    private static final int QUERY_RESOURCE_MAX_SIZE_ONE_ROUND = 200;

    private static final int QUERY_PAUSE_RESOURCE_MAX_SIZE_ONE_ROUND = 100;

    /**
     * 根据对象id，查询对象在billing中对应的resource
     *
     * @param serviceType
     * @param objectId
     * @return
     */
    public Resource queryResource(ServiceType serviceType, String objectId) {
        ResourceClient resourceClient = logicPodClientFactory.createResourceClient();
        ExtendedGetResourcesRequest extendedGetResourcesRequest = new ExtendedGetResourcesRequest();
        extendedGetResourcesRequest.setName(objectId);
        extendedGetResourcesRequest.setServiceType(serviceType.getName());
        extendedGetResourcesRequest.setRegion(regionConfiguration.getCurrentRegion());
        Resources resources = resourceClient.queryList(extendedGetResourcesRequest);
        if (resources == null || resources.isEmpty()) {
            throw new PodExceptions.ResourceNotExistException();
        }
        return resources.get(0);
    }


    /**
     * 从Billing的Resource表中删除
     */
    public void deleteResourceByName(String accountId, String name, String serviceType) {
        LOGGER.debug("deleteResourceByName delete with resourceV2 accountId: {}, name: {}, serviceType:{}, " +
                        "deleteResourceV2:{}",
                accountId, name, serviceType, deleteResourceV2);
        deleteResourceByNameV2(accountId, name, serviceType);
    }

    public void deleteResourceByNameV2(String accountId, String name, String serviceType) {
        ChargeResourceService chargeResourceService =
                logicPodClientFactory.createChargeResourceService(accountId);
        ResourceIdentify resourceIdentify = new ResourceIdentify();
        resourceIdentify.setServiceType(serviceType);
        resourceIdentify.setName(name);
        resourceIdentify.setRegion(regionConfiguration.getCurrentRegion());
        DeleteRequest deleteRequest = new DeleteRequest();
        deleteRequest.setIdentify(resourceIdentify);
        deleteRequest.setAccountId(accountId);

        try {
            chargeResourceService.destroy(deleteRequest);
        } catch (BceInternalResponseException e) {
            if (e.getHttpStatus() != 404) {
                LOGGER.debug("LogicalResourceService.deleteResourceByName caught exception: ", e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
    }

    public void destroyResourceByNameV2(String accountId, String name, String serviceType) {
        deleteResourceByNameV2(accountId, name, serviceType);
    }

    public void pauseResourceByNameV2(String accountId, String name, String serviceType) {
        ChargeResourceService chargeResourceService =
                logicPodClientFactory.createChargeResourceService(accountId);
        ResourceIdentify resourceIdentify = new ResourceIdentify();
        resourceIdentify.setServiceType(serviceType);
        resourceIdentify.setName(name);
        resourceIdentify.setRegion(regionConfiguration.getCurrentRegion());
        UseStatusRequest pauseRequest = new UseStatusRequest();
        pauseRequest.setIdentify(resourceIdentify);
        pauseRequest.setAccountId(accountId);

        try {
            Response response = chargeResourceService.pause(pauseRequest);
            if (!response.getStatus().equals(ResultStatus.SUCCESS)) {
                // 资源状态如果是终态已经不具备目前的操作流程，可以算本次操作成功
                Resource resource = getResourceUnExistedReturnNull(accountId, name, serviceType);
                if (null == resource) {
                    return;
                }
                if (isFinalStatus(resource)) {
                    LOGGER.warn("resource in final state in billing can not change status, " +
                            "accountId {}, name {}", accountId, name);
                    return;
                }
                throw new PodExceptions.InternalServerErrorException();
            }
        } catch (BceInternalResponseException e) {
            LOGGER.error("LogicalResourceService.pauseResourceByNameV2 caught exception: ", e);
            throw new PodExceptions.InternalServerErrorException();
        }
    }

    public void startResourceByNameV2(String accountId, String name, String serviceType) {
        ChargeResourceService chargeResourceService =
                logicPodClientFactory.createChargeResourceService(accountId);
        ResourceIdentify resourceIdentify = new ResourceIdentify();
        resourceIdentify.setServiceType(serviceType);
        resourceIdentify.setName(name);
        resourceIdentify.setRegion(regionConfiguration.getCurrentRegion());
        UseStatusRequest startRequest = new UseStatusRequest();
        startRequest.setIdentify(resourceIdentify);
        startRequest.setAccountId(accountId);

        try {
            Response response = chargeResourceService.start(startRequest);
            if (!response.getStatus().equals(ResultStatus.SUCCESS)) {
                // 资源状态如果是终态已经不具备目前的操作流程，可以算本次操作成功
                Resource resource = getResourceUnExistedReturnNull(accountId, name, serviceType);
                if (null == resource) {
                    return;
                }
                if (isFinalStatus(resource)) {
                    LOGGER.warn("resource in final state in billing can not change status, " +
                            "accountId {}, name {}", accountId, name);
                    return;
                }
                throw new PodExceptions.InternalServerErrorException();
            }
        } catch (BceInternalResponseException e) {
            LOGGER.error("LogicalResourceService.startResourceByNameV2 caught exception: ", e);
            throw new PodExceptions.InternalServerErrorException();
        }
    }

    public Set<String> queryCpt1PauseResourceStatusInfos(String accountId, List<String> nameList,
                                                         String serviceType) {

        ChargeResourceService chargeResourceService =
                logicPodClientFactory.createChargeResourceServiceWithBciConsoleToken();

        Set<String> allPauseRes = new HashSet<>();

        // 批量获取
        List<List<String>> nameLists = Lists.partition(nameList, QUERY_PAUSE_RESOURCE_MAX_SIZE_ONE_ROUND);

        for (List<String> names : nameLists) {
            FilterPauseResRequest filterPauseResRequest = new FilterPauseResRequest();
            filterPauseResRequest.setServiceType(serviceType);
            filterPauseResRequest.setRegion(regionConfiguration.getCurrentRegion());
            filterPauseResRequest.setNameList(names);

            try {
                List<String> pauseRes = chargeResourceService.filterPauseRes(filterPauseResRequest);
                if (null != pauseRes) {
                    allPauseRes.addAll(pauseRes);
                }
            } catch (BceInternalResponseException e) {
                LOGGER.error("queryCpt1ResourceStatusInfos caught exception: ", e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
        return allPauseRes;
    }

    public List<ResourceDetail> queryResourceListV2(String accountId) {
        ChargeResourceService chargeResourceService =
                logicPodClientFactory.createChargeResourceServiceWithBciConsoleToken();

        List<ResourceDetail> accountAllResource = new ArrayList<>();

        // 批量获取
        boolean isGetAll = false;
        int begin = 0;
        int limit = QUERY_RESOURCE_MAX_SIZE_ONE_ROUND;

        while (!isGetAll) {
            ResourceListRequest resourceListRequest = new ResourceListRequest();
            resourceListRequest.setAccountId(accountId);
            resourceListRequest.setRegion(regionConfiguration.getCurrentRegion());
            resourceListRequest.setResourceStatus(ResourceStatus.RUNNING);
            resourceListRequest.setServiceType(PodConstants.SERVICE_TYPE);
            resourceListRequest.setBegin(begin);
            resourceListRequest.setLimit(limit);

            try {
                ResourceQueryResponse response = chargeResourceService.queryList(resourceListRequest);
                if (response.getCode() != 200) {
                    LOGGER.error("chargeResourceService.queryList caught failed result: {}", response);
                    throw new PodExceptions.InternalServerErrorException();
                }
                List<ResourceDetail> resourceDetails = response.getResourceDetails();
                if (resourceDetails.size() < QUERY_RESOURCE_MAX_SIZE_ONE_ROUND) {
                    isGetAll = true;
                }

                accountAllResource.addAll(resourceDetails);
                begin += QUERY_RESOURCE_MAX_SIZE_ONE_ROUND;
                limit += QUERY_RESOURCE_MAX_SIZE_ONE_ROUND;

            } catch (BceInternalResponseException e) {
                LOGGER.error("chargeResourceService.queryList caught exception: ", e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
        return accountAllResource;
    }

    public List<ResourceDetail> queryCpt1ResourceListV2(String accountId) {
        List<ResourceDetail> result = new ArrayList<>();
        List<ResourceDetail> resourceDetails = queryResourceListV2(accountId);
        if (resourceDetails.size() <= 0) {
            return result;
        }
        result = new ArrayList<>(resourceDetails.size());
        for (ResourceDetail resourceDetail : resourceDetails) {
            if (resourceDetail.getSubProductType().equals(PodServiceV2.CPT1_SUB_PRODUCT_TYPE)) {
                result.add(resourceDetail);
            }
        }
        return result;
    }

    public Resource getResource(String accountId, String name, String serviceType) {
        ResourceClient resourceClient = logicPodClientFactory.createResourceClient();
        GetResourcesRequest getResourcesRequest = new GetResourcesRequest();
        getResourcesRequest.setAccountId(accountId);
        getResourcesRequest.setName(name);
        getResourcesRequest.setRegion(regionConfiguration.getCurrentRegion());
        getResourcesRequest.setServiceType(serviceType);

        Resource resource = null;
        Resources resources = resourceClient.list(getResourcesRequest);
        if (resources != null && resources.size() > 0) {
            for (Resource resourceTmp : resources) {
                if (resourceTmp.getName().contains(name) &&
                        resourceTmp.getStatus() != com.baidu.bce.internalsdk.order.model.ResourceStatus.DESTROYED) {
                    resource = resourceTmp;
                    break;
                }
            }
        }

        if (null == resource) {
            LOGGER.warn("instance resource in not exist");
            throw new PodExceptions.ResourceNotExistException();
        }

        return resource;
    }

    private Resource getResourceUnExistedReturnNull(String accountId, String name, String serviceType) {
        Resource resource;
        try {
            resource = getResource(accountId, name, serviceType);
        } catch (PodExceptions.ResourceNotExistException e) {
            LOGGER.warn("pod not exist name: {}, error: ", name, e);
            return null;
        }
        return resource;
    }

    private boolean isFinalStatus(Resource resource) {
        if (resource.getStatus().equals(com.baidu.bce.internalsdk.order.model.ResourceStatus.DESTROYED)) {
            return true;
        }
        return false;
    }

}
