package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.Order.Item;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.internalsdk.order.model.TimeUnit;
import com.baidu.bce.logic.bci.servicev2.constant.PodConstants;
import org.apache.commons.lang.StringUtils;

import java.util.LinkedList;
import java.util.List;

public abstract class BciAbstractDetailBuilder {
    protected final Order order;

    public BciAbstractDetailBuilder(Order order) {
        this.order = order;
    }

    public Order build(String itemKey) {
        String[] supportServiceTypes = StringUtils.split(this.serviceType(), ';');
        Boolean isMatch = false;
        String[] items = supportServiceTypes;
        int length = supportServiceTypes.length;

        for (int orderItem = 0; orderItem < length; ++orderItem) {
            String serviceType = items[orderItem];
            if (serviceType.equalsIgnoreCase(this.order.getServiceType())) {
                isMatch = true;
                break;
            }
        }

        if (!isMatch) {
            throw new BceInternalResponseException("", 400, "ServiceTypeNotMatch");
        } else {
            LinkedList<Item> orderItems = new LinkedList<>();

            for (Item item : this.order.getItems()) {
                if (this.isSameItemKeyOrNull(itemKey, item) && this.isSameServiceType(item)) {
                    this.updateOrderItem(item, this.order);
                    orderItems.add(item);
                }
            }

            this.order.setItems(orderItems);
            return this.order;
        }
    }

    public Order build() {
        return this.build((String) null);
    }

    private boolean isSameServiceType(Item orderItem) {
        return StringUtils.isNotEmpty(orderItem.getServiceType())
                && PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType());
    }

    private boolean isSameItemKeyOrNull(String itemKey, Item orderItem) {
        return StringUtils.isEmpty(itemKey) || itemKey.equalsIgnoreCase(orderItem.getKey());
    }

    private void updateOrderItem(Item orderItem, Order order) {
        orderItem.setUnitPriceShow(this.unitPriceShow(orderItem));
        orderItem.setUnitCount(this.unitCount(orderItem));
        orderItem.setTimeUnit(this.timeUnit(orderItem));
        orderItem.setChargeType(this.chargeType(orderItem));
        orderItem.setConfiguration(this.configuration(orderItem));
        if ("postpay" .equalsIgnoreCase(orderItem.getProductType()) && orderItem.getPaymentMethod() != null) {
            orderItem.getPaymentMethod().setDiscountRate(order.getDiscount());
        }

        if (StringUtils.isEmpty(orderItem.getServiceType())) {
            orderItem.setServiceType(order.getServiceType());
        }

        if (StringUtils.isEmpty(orderItem.getProductType())) {
            orderItem.setProductType(order.getProductType());
        }
    }

    protected abstract String serviceType();

    protected String itemServiceType() {
        return this.serviceType();
    }

    protected String unitPriceShow(Item item) {
        return "-";
    }

    protected int unitCount(Item item) {
        return item.getCount();
    }

    protected String timeUnit(Item item) {
        return item.getTimeUnit() != null ? item.getTimeUnit() :
                (this.order.getType() == OrderType.COMPENSATE_TIME ? TimeUnit.DAY.name() : TimeUnit.MONTH.name());
    }

    protected List<String> chargeType(Item item) {
        return new LinkedList();
    }

    protected List<String> configuration(Item item) {
        return new LinkedList();
    }

    protected String logicalZone(Item item) {
        return "";
    }
}
