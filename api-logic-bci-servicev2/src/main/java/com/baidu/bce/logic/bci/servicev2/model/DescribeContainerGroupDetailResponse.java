package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class DescribeContainerGroupDetailResponse {
    InstanceDetailModel instance;
    public DescribeContainerGroupDetailResponse(InstanceDetailModel instance) {
        this.instance = instance;
    }
}