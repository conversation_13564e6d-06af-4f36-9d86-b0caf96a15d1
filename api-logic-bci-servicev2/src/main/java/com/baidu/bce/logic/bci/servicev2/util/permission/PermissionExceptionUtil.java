package com.baidu.bce.logic.bci.servicev2.util.permission;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

public class PermissionExceptionUtil {

    /**
     * 无权限 401
     */
    public static class PermissionDenyException extends BceException {
        public PermissionDenyException() {
            super("bci permission deny.",
                    HttpStatus.ERROR_PERMISSION_DENY, "PermissionDeny");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PFSPermissionDenyException extends BceException {
        public PFSPermissionDenyException() {
            super("current user does not support PFS.",
                    HttpStatus.ERROR_PERMISSION_DENY, "PermissionDeny");
            setRequestId(LogicUserService.getRequestId());
        }
    }
}
