package com.baidu.bce.logic.bci.servicev2.exception;

import com.baidu.bce.logic.bci.servicev2.constant.BciHttpStatus;
import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

public class PodExceptions extends CommonExceptions {

    public static class ResourceAndPONotEqualException extends BceException {
        public ResourceAndPONotEqualException() {
            super("order resource in billing and PO in logic db not equal", HttpStatus.ERROR_INPUT_INVALID,
                    "Order.ResourceAndPONotEqualException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * 无效的自动续费时长 400
     */
    public static class InvalidAutoRenewTimeException extends BceException {
        public InvalidAutoRenewTimeException() {
            super("Eip autoRenewTime is invalid.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.EipAutoRenewTimeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidZoneException extends BceException {
        public InvalidZoneException(String zone) {
            super("Resource in " + zone + " has sold out", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidZoneException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class JsonTransforException extends BceException {
        public JsonTransforException() {
            super("Order extra to json failed.", HttpStatus.ERROR_INPUT_INVALID, "InternalException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * create eip fail
     */
    public static class CreateEipException extends BceException {
        public CreateEipException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.CreateEipFailed");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * eip超出额度 413
     */
    public static class EipQuotaExceedLimitException extends BceException {
        public EipQuotaExceedLimitException() {
            super("The number of eip will exceed the limit.",
                    HttpStatus.ERROR_TOO_MANY, "Instance.EipQuotaLimitExceeded");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * 无效的自动续费单元 400
     */
    public static class InvalidAutoRenewTimeUnitException extends BceException {
        public InvalidAutoRenewTimeUnitException() {
            super("Eip autoRenewTimeUnit is invalid.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.EipAutoRenewTimeUnitInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidateZoneException extends BceException {
        public InvalidateZoneException() {
            super("Invalidate zone name.", HttpStatus.ERROR_INPUT_INVALID, "Pod.InvalidateZoneException");
            setRequestId(LogicUserService.getRequestId());
        }
    }


    public static class PermissionDenyException extends BceException {
        public PermissionDenyException() {
            super("Permission denied. Please contact the administrator.", HttpStatus.ERROR_OPERATION_DENY);
        }
    }

    /**
     * cds磁盘超额 413
     */
    public static class ExceedLimitException extends BceException {
        public ExceedLimitException() {
            super("Number of pod exceeds limit.", HttpStatus.ERROR_TOO_MANY, "Pod.PodQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * 容器组id不允许为空 400
     */
    public static class PodIdIsEmptyException extends BceException {
        public PodIdIsEmptyException() {
            super("Pod id must be provided.", HttpStatus.ERROR_INPUT_INVALID, "Pod.PodIdIsEmpty");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * Pending Pod数量超额 429
     * 触发bci创建Pod限流策略
     */
    public static class PendingPodQuotaExceededLimit extends BceException {
        public PendingPodQuotaExceededLimit() {
            super("Pending Pod size exceeds total capacity quota.", BciHttpStatus.HTTP_STATUS_TOO_MANY_REQUESTS,
                    "Too Many Requests");
            setRequestId(LogicUserService.getRequestId());
        }
    }


    public static class EipOperationDeniedException extends BceException {
        public EipOperationDeniedException() {
            super("Account has no permission to bind EIP.", HttpStatus.ERROR_OPERATION_DENY,
                    "Pod.EipOperationDenied");
            setRequestId(LogicUserService.getRequestId());
        }
    }
    /*
    public static class NameInvalidException extends BceException {
        public NameInvalidException() {
            super("Pod name is invalid.", HttpStatus.ERROR_INPUT_INVALID, "Pod.NameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }*/

    public static class NameInvalidException extends BceException {
        public NameInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.NameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class LogicalZoneInvalidException extends BceException {
        public LogicalZoneInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.LogicalZoneInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class SecurityGroupInvalidException extends BceException {
        public SecurityGroupInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.SecurityGroupInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class SubnetInvalidException extends BceException {
        public SubnetInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.SubnetInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeNameInvalidException extends BceException {
        public VolumeNameInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.VolumeNameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeAttributeInvalidException extends BceException {
        public VolumeAttributeInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.VolumeAttributeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class RestartPolicyInvalidException extends BceException {
        public RestartPolicyInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.RestartPolicyInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class TagInvalidException extends BceException {
        public TagInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.TagInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class GPUTypeInvalidException extends BceException {
        public GPUTypeInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.GPUTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class GPUCountInvalidException extends BceException {
        public GPUCountInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.GPUCountInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EipCountInvalidException extends BceException {
        public EipCountInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.EipCountInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EipAutoRenewTimeInvalidException extends BceException {
        public EipAutoRenewTimeInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.EipAutoRenewTimeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EipBandwidthInMbpsInvalidException extends BceException {
        public EipBandwidthInMbpsInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.EipBandwidthInMbpsInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EipRouteTypeInvalidException extends BceException {
        public EipRouteTypeInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.EipRouteTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EipAreadyInUseException extends BceException {
        public EipAreadyInUseException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.EipAreadyInUse");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EipNotAvailableException extends BceException {
        public EipNotAvailableException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.EipNotAvailable");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ImageRegistryException extends BceException {
        public ImageRegistryException() {
            super("Pod ImageRegistry is invalid.", HttpStatus.ERROR_INPUT_INVALID, "Pod.ImageRegistryInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerImageAddressException extends BceException {
        public ContainerImageAddressException(String address) {
            super("The container imageAddress [" + address +
                    "] is invalid. Only images from CCR/CCE/DockerHub are supported.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerImageAddressInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
        public ContainerImageAddressException() {
            super("The container imageAddress is invalid, " +
                    "a valid imageAddress length must be between 1 and 1024.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerImageAddressInvalid");
        }
    }

    public static class ContainerException extends BceException {
        public ContainerException(String msg) {
            super(msg, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerNameException extends BceException {
        public ContainerNameException(String msg) {
            super(msg, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerNameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerImagePullPolicyInvalid extends BceException {
        public ContainerImagePullPolicyInvalid(String msg) {
            super(msg, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerImagePullPolicyInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerProbeException extends BceException {
        public ContainerProbeException() {
            super("The container probe is invalid. Only exec/tcpSocket/httpGet are supported.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerProbeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContianerLogCollectionException extends BceException {
        public ContianerLogCollectionException(String msg) {
            super(msg, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerLogCollectionException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerEnvironmentException extends BceException {
        public ContainerEnvironmentException() {
            super("The container environment is invalid." +
                    " Only status.podIP or status.podIPs are supported.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerEnvironmentInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerTypeException extends BceException {
        public ContainerTypeException(String containerName) {
            super("The container containerType is invalid. Only init/workload/ds-workload are supported.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }

        public ContainerTypeException() {
            super("The container containerType is invalid. No workload container.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerImageNameException extends BceException {
        public ContainerImageNameException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerImageNameInvalid");
        }
    }

    public static class ContainerImageVersionException extends BceException {
        public ContainerImageVersionException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerImageVersionInvalid");
        }
    }

    public static class ContainerWorkingDirException extends BceException {
        public ContainerWorkingDirException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerWorkingDirInvalid");
        }
    }

    public static class ContainerCommandsException extends BceException {
        public ContainerCommandsException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerCommandsInvalid");
        }
    }

    public static class CpuTypeInvalidException extends BceException {
        public CpuTypeInvalidException() {
            super("Pod CpuType is invalid. Only intel/amd are supported.", 
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.CpuTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PreviewPodCapacityTypeInvalidException extends BceException {
        public PreviewPodCapacityTypeInvalidException() {
            super("PreviewPodCapacity Pod Type is invalid. Only tidal/pfs are supported.", 
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.PreviewPodCapacityTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class CpuTypePermissionDenyException extends BceException {
        public CpuTypePermissionDenyException() {
            super("Current user does not in EnableCPUType white list", 
                    HttpStatus.ERROR_PERMISSION_DENY, "Pod.CpuTypePermissionDeny");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class DnsConfigException extends BceException {

        public DnsConfigException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.DnsConfigInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class CoreDumpException extends BceException {
        public CoreDumpException(String errorMessage) {
            super(errorMessage, HttpStatus.ERROR_INPUT_INVALID, "Pod.CoredumpInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class TidalException extends BceException {
        public TidalException(String errorMessage) {
            super(errorMessage, HttpStatus.ERROR_INPUT_INVALID, "Pod.TidalSubmitTimeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class NfsQuotaExceededLimit extends BceException {
        public NfsQuotaExceededLimit() {
            super("Nfs size exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.NfsQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class LocalDiskAndCDSMixed extends BceException {
        public LocalDiskAndCDSMixed() {
            super("Can not mix up local disk and CDS.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.LocalDiskAndCDSMixed");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class RootFSVolumeExceededLimit extends BceException {
        public RootFSVolumeExceededLimit() {
            super("There can only be one rootfs.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.RootFSVolumeExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EmptyDirQuotaExceededLimit extends BceException {
        public EmptyDirQuotaExceededLimit() {
            super("EmptyDir volume exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.EmptyDirQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ConfigFileQuotaExceededLimit extends BceException {
        public ConfigFileQuotaExceededLimit() {
            super("PodConfigFile size exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.PodQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class DataVolumeQuotaExceededLimit extends BceException {
        public DataVolumeQuotaExceededLimit() {
            super("Data volume exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.DataVolumeQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidVolumeSize extends BceException {
        public InvalidVolumeSize() {
            super("Invalid volume size.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidVolumeSize");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodCreatedFailed extends BceException {
        public PodCreatedFailed() {
            super("Fail to create pod", HttpStatus.ERROR_TOO_MANY,
                    "Pod.PodCreatedFailed");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /*
    public static class VolumeNameInvalidException extends BceException {
        public VolumeNameInvalidException(String name) {
            super("More than one volumes named " + name, HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeNameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }*/

    public static class MountsNoVolumeException extends BceException {
        public MountsNoVolumeException(String name) {
            super("The volume mount [" + name + "] does not match any volume.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeMountsInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeNoMountsException extends BceException {
        public VolumeNoMountsException() {
            super("Some volumes has no matched mount (no need for rootfs).", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumesInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeAlreadyExistException extends BceException {
        public VolumeAlreadyExistException() {
            super("New added volumes are already existed in pod.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumesInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class MountsHostPathWithoutReadOnlyException extends BceException {
        public MountsHostPathWithoutReadOnlyException(String path) {
            super("Mount HostPathVolume [" + path + "] must be readonly.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeMountsInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ResourceNotExistException extends BceException {
        public ResourceNotExistException() {
            super("The specified object is not found or resource do not exist.",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "NoSuchObject");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerCPUMemoryRatioInvalidException extends BceException {
        public ContainerCPUMemoryRatioInvalidException(String name) {
            super("Unsupported resource requirements for container " + name + ". Please consult the doc at " +
                            "https://cloud.baidu.com/doc/CCE/s/Jk9cxgekd#%E9%85%8D%E7%BD%AEcpu%E5%92%8Cmemory%E8%B5" +
                            "%84%E6%BA%90"
                    , HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerCPUMemoryRatioInvalidException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerCPUInvalidException extends BceException {
        public ContainerCPUInvalidException(String name) {
            super("Unsupported resource requirements for container " + name + ". Please consult the doc at " +
                            "https://cloud.baidu.com/doc/CCE/s/Jk9cxgekd#%E9%85%8D%E7%BD%AEcpu%E5%92%8Cmemory%E8%B5" +
                            "%84%E6%BA%90"
                    , HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerCPUInvalidException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodContainerMountTypeInvalidException extends BceException {
        public PodContainerMountTypeInvalidException() {
            super("The volume mount type is invalid.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerMountTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodContainerMountInvalidException extends BceException {
        public PodContainerMountInvalidException() {
            super("Volume mount name invalid.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.PodContainerMountNameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class UserPriceConfigurationNotExist extends BceException {
        public UserPriceConfigurationNotExist() {
            super("User's price configuration for postpay does not exist.", HttpStatus.ERROR_INPUT_INVALID,
                    "Bbc.UserPriceConfigurationNotExist");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodChargeModeNotSupported extends BceException {
        public PodChargeModeNotSupported() {
            super("Pod charge mode not supported. Please contact support.", HttpStatus.ERROR_OPERATION_NOT_AVAILABLE,
                    "Pod.PodChargeModeNotSupported");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class OperationNotAvailable extends BceException {
        public OperationNotAvailable() {
            super("Operation not available. Please confirm your permission.", HttpStatus.ERROR_OPERATION_NOT_AVAILABLE);
        }
    }

    public static class GetServerFailed extends BceException {
        public GetServerFailed() {
            super("GetServer failed", 500);
        }
    }

    public static class CreateImageCacheFailed extends BceException {
        public CreateImageCacheFailed() {
            super("CreateImageCache failed", 500);
        }
    }

    public static class InvalidDiskSize extends BceException {
        public InvalidDiskSize() {
            super("Invalid disk size for resource unit.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidDiskSize");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ResourceGroupException extends BceException {
        public ResourceGroupException() {
            super("resource group is empty.", HttpStatus.ERROR_COMPUTER,
                    "Pod.ResourceGroupException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class LeakagePodInfoIsEmptyException extends BceException {
        public LeakagePodInfoIsEmptyException() {
            super("Pod info must be provided.", HttpStatus.ERROR_INPUT_INVALID, "Pod.LeakagePodInfoIsEmpty");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerNumExceededLimit extends BceException {
        public ContainerNumExceededLimit(Integer number, Integer limit) {
            super("Container number: " + number + ", exceeds limit: " + limit, HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerNumExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodCpuMemSpecInvalid extends BceException {
        public PodCpuMemSpecInvalid() {
            super("pod cpu and memory must in valid specification");
            setRequestId(LogicUserService.getRequestId());
        }

        public PodCpuMemSpecInvalid(String msg) {
            super(msg, HttpStatus.ERROR_INPUT_INVALID, "Pod.CpuMemSpecInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodAnnotationInvalid extends BceException {
        public PodAnnotationInvalid() {
            super("pod annotation param must in valid specification");
            setRequestId(LogicUserService.getRequestId());
        }

        public PodAnnotationInvalid(String msg) {
            super(msg, HttpStatus.ERROR_INPUT_INVALID, "Pod.AnnotationParamInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }
    public static class VolumeNotFound extends BceException {
        public VolumeNotFound() {
            super("Volume(s) not found", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeNotFound");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class UnsupportedCDSPayment extends BceException {
        public UnsupportedCDSPayment(String payment) {
            super("Unsupported data volume payment: " + payment, HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.UnsupportedCDSPayment");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeNotAvailable extends BceException {
        public VolumeNotAvailable() {
            super("Volume(s) not available", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeNotAvailable");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidVolumeSource extends BceException {
        public InvalidVolumeSource() {
            super("Only DATA volume can use existed cds", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidVolumeSource");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeInvalidZone extends BceException {
        public VolumeInvalidZone(String volumeZone, String otherVoluneZone) {
            super("Volume(s) zone: " + volumeZone + " is different with other volume zone: " + otherVoluneZone,
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.VolumeInvalidZone");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeInvalidZoneWithSubnet extends BceException {
        public VolumeInvalidZoneWithSubnet(String volumeZone) {
            super("Volume(s) zone: " + volumeZone + " is different with subnet zones: ",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.VolumeInvalidZone");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class MountPathDuplicated extends BceException {
        public MountPathDuplicated(String mountPath) {
            super("Invalid mount path: " + mountPath + ", duplicated.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerMountPathInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class SubPathDuplicated extends BceException {
        public SubPathDuplicated() {
            super("Invalid subPath duplicated. subPath and subPathExpr cannot appear at the same time",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ContainerSubPathInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ClusterNotFoundException extends BceException {
        public ClusterNotFoundException() {
            super("User cluster is not found", HttpStatus.ERROR_INPUT_INVALID,
                    "ClusterNotFoundException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class NamespaceNotFoundException extends BceException {
        public NamespaceNotFoundException() {
            super("User cluster namespace is not found", HttpStatus.ERROR_INPUT_INVALID,
                    "Webshell.NamespaceNotFoundException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodNotExistException extends BceException {
        public PodNotExistException(String podId) {
            super("podId:" + podId + " is not found", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.PodNotExistException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodNotRunningException extends BceException {
        public PodNotRunningException(String podId) {
            super("podId:" + podId + " is not running", HttpStatus.ERROR_INPUT_INVALID,
                    "Webshell.PodNotRunningException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerNotExistException extends BceException {
        public ContainerNotExistException(String podId, String containerName) {
            super("podId:" + podId + " , containerName " + containerName + " is not found",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "Webshell.ContainerNotExistException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerNotRunningException extends BceException {
        public ContainerNotRunningException(String podId, String containerName) {
            super("podId:" + podId + " , containerName " + containerName + " is not running",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "Webshell.ContainerNotRunningException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerCommandInvalidException extends BceException {
        public ContainerCommandInvalidException(String podId, String containerName) {
            super("podId:" + podId + " , containerName " + containerName + " webshell command invalid",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "Webshell.ContainerCommandInvalidException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerSecurityContextCapInvalidException extends BceException {
        public ContainerSecurityContextCapInvalidException(String containerName, String capName) {
            super("The container securityContext is invalid, capability [" + capName + "] is forbidden.",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerSecurityContextCapInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class DelayReleaseDurationMinuteException extends BceException {
        public DelayReleaseDurationMinuteException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.DelayReleaseDurationMinuteInvalid");
        }
    }

    public static class ProductTypeInvalid extends BceException {
        public ProductTypeInvalid(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.ProductTypeInvalid");
        }
    }

    public static class HostPathInvalidException extends BceException {
        public HostPathInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.HostPathInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }
    public static class NoEnoughStock extends BceException {
        public NoEnoughStock() {
            super("Sales of this resource with specified features are temporarily suspended in the specified region;" +
            "please check the instance type features and try again later.\t", HttpStatus.ERROR_OPERATION_DENY,
                    "Pod.NoEnoughStock");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodResourceTagInvalidException extends BceException {
        public PodResourceTagInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "Pod.PodResourceTagInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodResourceTagExceedLimitException extends BceException {
        public PodResourceTagExceedLimitException(String message, int maxTagLength) {
            super(message + maxTagLength, HttpStatus.ERROR_INPUT_INVALID, "Pod.PodResourceTagLimitExceeded");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class CreateInstanceDuplicateKeyException extends BceException {
        public CreateInstanceDuplicateKeyException() {
            super("The pod create parameters error",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.CreateInstanceDuplicateKeyException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class CreateRepeatedInvocationException extends BceException {
        public CreateRepeatedInvocationException() {
            super("Pod creation request received, no need to be repeated create",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.CreateRepeatedInvocationException");
            setRequestId(LogicUserService.getRequestId());
        }
    }
}
