package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.PriceType;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

public class BciOrderDetailBuilder extends BciAbstractDetailBuilder {

    public static final Logger LOGGER = LoggerFactory.getLogger(BciOrderDetailBuilder.class);
    BciOrderExtra orderExtra = new BciOrderExtra();

    public BciOrderDetailBuilder(Order order) {
        super(order);
        getOrderExtra();
    }

    @Override
    protected String serviceType() {
        return "BCI;BCI__EIP";
    }

    public void getOrderExtra() {
        String extra = order.getItems().get(0).getExtra();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            orderExtra = objectMapper.readValue(extra, BciOrderExtra.class);
        } catch (IOException e) {
            LOGGER.error("订单extra解析失败");
        }
    }

    @Override
    protected List<String> chargeType(Order.Item item) {
        String type = item.getProductType().equalsIgnoreCase("postpay") ? PriceType.CPT1.name() : PriceType.CPT2.name();
        return Arrays.asList(type);
    }

    @Override
    protected List<String> configuration(Order.Item item) {
        return new LinkedList<>();
    }

    @Override
    protected String itemServiceType() {
        return ServiceType.BBC.name();
    }

    /**
     * 显示可用区域 (从Item的Flavor里取出logicalZone)
     */
    @Override
    protected String logicalZone(Order.Item item) {
        return orderExtra.getLogicalZone();
    }
}

