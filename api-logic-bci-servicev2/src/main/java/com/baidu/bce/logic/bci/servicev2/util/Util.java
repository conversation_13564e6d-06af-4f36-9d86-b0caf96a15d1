package com.baidu.bce.logic.bci.servicev2.util;

import com.baidu.bce.logic.bci.servicev2.util.UtilException.ErrorCode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class Util {
    private static final Logger LOGGER = LoggerFactory.getLogger(Util.class);
    public static Set<String> getListFromString(String str) {
        Set<String> result = new HashSet<>();
        if (StringUtils.isEmpty(str)) {
            return result;
        }
        result.addAll(Arrays.asList(str.trim().split(",")));
        return result;
    }

    public static String saveListToString(Collection<String> input) {
        return String.join(",", input);
    }

    public static Map<String, String> getMapFromString(String str) throws UtilException {
        Map<String, String> result = new HashMap<>();
        if (StringUtils.isEmpty(str)) {
            return result;
        }
        String[] pairs = str.trim().split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.trim().split(":");
            if (keyValue.length == 2) {
                result.put(keyValue[0], keyValue[1]);
            } else {
                throw new UtilException(ErrorCode.PARSE_MAP_ERROR, str + " is invalid when parsing it to an map.");
            }
        }
        return result;
    }

    public static String saveMapToString(Map<String, String> input) {
        List<String> pairs = new ArrayList<>();
        for (String pairKey : input.keySet()) {
            pairs.add(String.join(":", Arrays.asList(pairKey, input.get(pairKey))));
        }
        return String.join(",", pairs);
    }

    public static Map<String, String> convertListToMap(Collection<String> keys, String value) {
        Map<String, String> result = new HashMap<>();
        for (String key : keys) {
            result.put(key, value);
        }
        return result;
    }

    public static Map<String, Object> convertAnnotationToMap(String annotations) {
        Map<String, Object> annotationMap = new HashMap<>();
        if (annotations == null || "".equals(annotations)) {
            return annotationMap;
        }

        ObjectMapper objectMapper = new ObjectMapper();

        try {
            annotationMap = objectMapper.readValue(annotations, Map.class);
        } catch (Exception e) {
            LOGGER.error("Unmarshal pod Annotation {} err {} ", annotations, e);
        }
        return annotationMap;
    }

    public static Date convertOffsetDateTimeToDate(OffsetDateTime offsetDateTime) {
        // 将 OffsetDateTime 转换为 Instant，然后再转换为 Date
        return Date.from(offsetDateTime.toInstant());
    }
}