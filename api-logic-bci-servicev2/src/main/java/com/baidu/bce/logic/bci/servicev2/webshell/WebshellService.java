package com.baidu.bce.logic.bci.servicev2.webshell;

import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.common.model.WebShell;
import com.baidu.bce.logic.bci.daov2.container.ContainerDaoV2;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import com.baidu.bce.logic.bci.daov2.pod.PodDaoV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.webshell.WebshellDao;
import com.baidu.bce.logic.bci.daov2.webshell.model.WebshellPO;
import com.baidu.bce.logic.bci.servicev2.constant.LogicalConstant;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.interceptor.ResourceAccountSetting;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sCluster;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import com.baidu.bce.logic.core.user.LogicUserService;
import io.kubernetes.client.openapi.models.V1ContainerStatus;
import io.kubernetes.client.openapi.models.V1Namespace;
import io.kubernetes.client.openapi.models.V1Pod;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.List;
import java.util.UUID;

import static com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2.WEBSHELL_LOG_PREFIX;

@Service("webshellService")
public class WebshellService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebshellService.class);

    @Autowired
    private WebshellDao webshellDao;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private ContainerDaoV2 containerDao;

    @Autowired
    private PodDaoV2 podDao;

    @Value("${webShell.url:''}")
    private String webShellUrl;


    public String getAccountId() {
        return LogicUserService.getAccountId();
    }

    public WebshellPO queryByToken(String token) {
        return webshellDao.queryByToken(token);
    }

    public int lockWebShellToken(String token, int version) {
        return webshellDao.lockWebShellToken(token, version);
    }

    /**
     * 获取webshell token & ws url
     *
     * @param webShell
     * @return
     */
    public String handleLaunchContainerWebShell(WebShell webShell) {
        String podId = webShell.getPodId();
        // 从db
        PodPO podDO = podDao.getPodById(podId);
        if (podDO == null) {
            throw new PodExceptions.PodNotExistException(webShell.getPodId());
        }

        if (ResourceAccountSetting.isUnifiedCharge()) {
            // 统一计费，匹配charge source
            if (!podDO.getChargeSource().equalsIgnoreCase(ResourceAccountSetting.getApplication())) {
                LOGGER.warn(WEBSHELL_LOG_PREFIX + "pod webshell failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        } else {
            // 根据charge source 过滤列表, 不展示统一计费的资源
            if (!podDO.getChargeSource().equalsIgnoreCase(LogicalConstant.CHARGE_SOURCE_USER)) {
                LOGGER.warn(WEBSHELL_LOG_PREFIX + "pod webshell failed, pod {} is unified charge", podId);
                throw new PodExceptions.PodNotExistException(podId);
            }
        }

        // 从k8s中查询pod
        String accountId = getAccountId();
        K8sCluster cluster = null;
        try {
            // 不存在，方法内会抛异常
            cluster = k8sService.getClusterByUserId(accountId);
        } catch (Exception e) {
            throw new PodExceptions.ClusterNotFoundException();
        }

        // 获取cceID
        List<CceCluster> cceCluster = cceClusterService.getCceClustersByUserId(accountId);
        if (CollectionUtils.isEmpty(cceCluster)) {
            throw new PodExceptions.ClusterNotFoundException();
        }

        V1Namespace namespace = cluster.getNameSpace(accountId);

        if (namespace == null) {
            throw new PodExceptions.NamespaceNotFoundException();
        }


        V1Pod pod = k8sService.getPod(accountId, podId);
        if (pod == null) {
            throw new PodExceptions.PodNotExistException(podId);
        }
        // PodPhase Running 校验
        if (pod.getStatus() != null && !"Running".equals(pod.getStatus().getPhase())) {
            throw new PodExceptions.PodNotRunningException(podId);
        }

        List<ContainerPO> containers = containerDao.listByPodId(podDO.getPodUuid());
        if (CollectionUtils.isEmpty(containers)) {
            throw new PodExceptions.ContainerNotExistException(webShell.getPodId(), webShell.getContainerName());
        }

        boolean hasContainer = false;
        for (ContainerPO containerPO : containers) {
            if (containerPO.getName().equals(webShell.getContainerName())) {
                hasContainer = true;
                break;
            }
        }
        if (!hasContainer) {
            throw new PodExceptions.ContainerNotExistException(webShell.getPodId(), webShell.getContainerName());
        }

        List<V1ContainerStatus> containerStatuses = pod.getStatus().getContainerStatuses();

        if (CollectionUtils.isEmpty(containerStatuses)) {
            throw new PodExceptions.ContainerNotRunningException(webShell.getPodId(), webShell.getContainerName());
        }

        boolean hasContainerStatus = false;

        // container 是否是running
        for (V1ContainerStatus containerStatus : containerStatuses) {
            if (containerStatus.getName() != null && containerStatus.getName().equals(webShell.getContainerName())) {
                hasContainerStatus = true;
                if (containerStatus.getState() == null ||
                        containerStatus.getState().getRunning() == null) {
                    throw new PodExceptions.ContainerNotRunningException(webShell.getPodId(),
                            webShell.getContainerName());
                }
            }
        }

        if (!hasContainerStatus) {
            throw new PodExceptions.ContainerNotRunningException(webShell.getPodId(),
                    webShell.getContainerName());
        }

        String k8sExecUrl = k8sExecUrl(accountId, webShell);
        LOGGER.info("webshell accountId {} podId {} container {} webshell {} ", accountId,
                webShell.getPodId(), webShell.getContainerName(), k8sExecUrl);

        String token = UUID.randomUUID().toString();
        WebshellPO webshellPo = new WebshellPO();
        webshellPo.setContainerName(webShell.getContainerName());
        webshellPo.setToken(token);
        webshellPo.setCceId(cceCluster.get(0).getCceId());
        webshellPo.setPodId(webShell.getPodId());
        webshellPo.setUserId(accountId);
        webshellPo.setWsUrl(k8sExecUrl);
        webshellPo.setVersion(0);
        webshellPo.setDeleted(0);
        webshellDao.insert(webshellPo);
        return webShellUrl + token;
    }

    public String k8sExecUrl(String accountId, WebShell webShell) {
        if (CollectionUtils.isEmpty(webShell.getCommand())) {
            throw new PodExceptions.ContainerCommandInvalidException(webShell.getPodId(),
                    webShell.getContainerName());
        }
        String[] encodedCommand = new String[webShell.getCommand().size()];
        for (int i = 0; i < webShell.getCommand().size(); i++) {
            try {
                encodedCommand[i] = URLEncoder.encode(webShell.getCommand().get(i), "UTF-8");
            } catch (Exception ex) {
                LOGGER.error("webshell accountId {} podId {} container {} command {} is Invalid", accountId,
                        webShell.getPodId(), webShell.getContainerName(), webShell.getCommand().toString());
                throw new PodExceptions.ContainerCommandInvalidException(webShell.getPodId(),
                        webShell.getContainerName());
            }
        }
        return "/api/v1/namespaces/"
                + accountId
                + "/pods/"
                + webShell.getPodId()
                + "/exec?"
                + "stdin="
                + webShell.getStdin()
                + "&stdout="
                + webShell.getStdout()
                + "&stderr="
                + webShell.getStderr()
                + "&tty="
                + webShell.getTty()
                + "&container=" + webShell.getContainerName()
                + "&command="
                + org.apache.commons.lang3.StringUtils.join(encodedCommand, "&command=");
    }
}
