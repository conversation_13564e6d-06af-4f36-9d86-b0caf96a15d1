package com.baidu.bce.logic.bci.servicev2.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class PodContainerSyncThreadPoolConfigurationV2 {
    @Value("${pod.container.sync.thread.pool.core.size:100}")
    private Integer threadPoolCoreSize;
    @Value("${pod.container.sync.thread.pool.max.size:500}")
    private Integer threadPoolMaxSize;
    @Value("${pod.container.sync.thread.pool.queue.capacity:100000}")
    private Integer threadPoolQueueCapacity;
    @Value("${pod.container.sync.thread.keepalive.seconds:60}")
    private Integer threadKeepAliveSeconds;

    @Bean(name = "podContainerSyncThreadPoolTaskExecutorV2")
    public ThreadPoolTaskExecutor podContainerSyncThreadPoolTaskExecutorV2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(threadPoolQueueCapacity);
        executor.setKeepAliveSeconds(threadKeepAliveSeconds);

        String threadNamePrefix = "BciV2-Pod-Container-Sync-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);
        return executor;
    }
}
