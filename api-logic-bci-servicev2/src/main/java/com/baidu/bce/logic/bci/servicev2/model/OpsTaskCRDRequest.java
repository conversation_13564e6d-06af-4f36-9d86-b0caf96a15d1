package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.kubernetes.client.common.KubernetesObject;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OpsTaskCRDRequest implements KubernetesObject {

    public static final String OPS_TASK_CRD_GROUP = "ops.bci.cloud.baidu.com";
    public static final String OPS_TASK_CRD_VERSION = "v1";
    public static final String OPS_TASK_CRD_PLURAL = "bciopstasks";
    public static final String OPS_TASK_CRD_KIND = "BciOpsTask";

    private String apiVersion = OPS_TASK_CRD_GROUP + "/" + OPS_TASK_CRD_VERSION;
    private String kind = OPS_TASK_CRD_KIND;
    private V1ObjectMeta metadata;
    private OpsTaskCRDSpec spec;
    private OpsTaskCRDStatus status;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OpsTaskCRDRequest that = (OpsTaskCRDRequest) o;
        return Objects.equals(apiVersion, that.apiVersion) &&
                Objects.equals(kind, that.kind) &&
                Objects.equals(metadata, that.metadata) &&
                Objects.equals(spec, that.spec) &&
                Objects.equals(status, that.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(apiVersion, kind, metadata, spec, status);
    }
}
