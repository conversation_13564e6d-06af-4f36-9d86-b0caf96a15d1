package com.baidu.bce.logic.bci.servicev2.model.inner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class VirtualKubeletClusterReportResponse {

    public VirtualKubeletClusterReportResponse() {
        this.clusters = new ArrayList<>();
    }

    private List<VirtualKubeletClusterCheckResultInfo> clusters;
}
