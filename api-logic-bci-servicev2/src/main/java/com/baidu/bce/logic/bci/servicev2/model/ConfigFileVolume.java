package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ConfigFileVolume extends BaseVolume {

    private List<ConfigFileToPath> configFiles;
    // ConfigFileVolume默认的权限
    private Integer defaultMode;
}