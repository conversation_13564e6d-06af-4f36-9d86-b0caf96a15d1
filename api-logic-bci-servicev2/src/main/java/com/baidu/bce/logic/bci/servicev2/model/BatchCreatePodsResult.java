package com.baidu.bce.logic.bci.servicev2.model;

import io.kubernetes.client.openapi.models.V1Pod;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


@Data
@Accessors(chain = true)
public class BatchCreatePodsResult {
    private ArrayList<V1Pod> succeedPods = new ArrayList<>();
    private ArrayList<V1Pod> failedPods = new ArrayList<>();;
    private ArrayList<V1Pod> existPods = new ArrayList<>();;

    private Map<V1Pod, String> failedPodsReasons = new HashMap<>();

    public BatchCreatePodsResult() {
    }

    public ArrayList<V1Pod> getSucceedPods() {
        return succeedPods;
    }
    
    public ArrayList<V1Pod> getFailedPods() {
        return failedPods;
    }

    public ArrayList<V1Pod> getExistPods() {
        return existPods;
    }

    public void addSucceedPod(V1Pod pod) {
        succeedPods.add(pod);
    }

    public void addFailedPod(V1Pod pod) {
        failedPods.add(pod);
    }

    public void addExistPod(V1Pod pod) {
        existPods.add(pod);
    }
}
