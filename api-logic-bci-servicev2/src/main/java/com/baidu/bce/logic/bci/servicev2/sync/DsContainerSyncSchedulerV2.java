package com.baidu.bce.logic.bci.servicev2.sync;

import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.baidu.bce.logic.bci.servicev2.sync.service.DsContainerSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.sync.service.SyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;


@EnableScheduling
@Configuration("DsContainerSyncSchedulerV2")
@Profile("default")
public class DsContainerSyncSchedulerV2 extends SyncServiceV2 {
    /** 每隔5秒执行一次 */
    private static final int SYNC_PERIOD_MS = 5 * 1000;
    private String schedulerName = "DsContainerSyncSchedulerV2.runDsContainerSyncTask";

    @Autowired
    private DsContainerSyncServiceV2 dsContainerSyncServiceV2;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    // 启动立即执行一次
    @Scheduled(fixedDelay = SYNC_PERIOD_MS)
    public void runDsContainerSyncTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        dsContainerSyncServiceV2.syncDsContainers();
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }
}