package com.baidu.bce.logic.bci.servicev2.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class OrderSyncAccelerateThreadPoolConfigurationV2 {
    @Value("${order.sync.accelerate.thread.pool.core.size:100}")
    private Integer threadPoolCoreSize;
    @Value("${order.sync.accelerate.thread.pool.max.size:1000}")
    private Integer threadPoolMaxSize;
    @Value("${order.sync.accelerate.thread.pool.queue.capacity:100000}")
    private Integer threadPoolQueueCapacity;
    @Value("${order.sync.accelerate.thread.keepalive.seconds:60}")
    private Integer threadKeepAliveSeconds;

    @Bean(name = "orderSyncAccelerateThreadPoolTaskExecutorV2")
    public ThreadPoolTaskExecutor orderSyncAccelerateThreadPoolTaskExecutorV2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolCoreSize);
        executor.setMaxPoolSize(threadPoolMaxSize);
        executor.setQueueCapacity(threadPoolQueueCapacity);
        executor.setKeepAliveSeconds(threadKeepAliveSeconds);

        String threadNamePrefix = "BciV2-Order-Sync-Accelerate-Thread-";
        executor.setThreadNamePrefix(threadNamePrefix);
        return executor;
    }
}
