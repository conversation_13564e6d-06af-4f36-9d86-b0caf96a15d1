package com.baidu.bce.logic.bci.servicev2.sync;

import com.baidu.bce.logic.bci.servicev2.scheduler.SchedulerStatistics;
import com.baidu.bce.logic.bci.servicev2.sync.service.PodInBuildSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.sync.service.SyncServiceV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import javax.annotation.PostConstruct;

@EnableScheduling
@Configuration("PodInBuildSyncSchedulerV2")
@Profile("default")
public class PodInBuildSyncSchedulerV2 extends SyncServiceV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodInBuildSyncSchedulerV2.class);

    private static final int FIX_DELAY_ONE = 3000;

    private String schedulerName = "PodInBuildSyncSchedulerV2.runScheduledTask";

    @Autowired
    private PodInBuildSyncServiceV2 podInBuildSyncService;

    @Autowired
    private SchedulerStatistics schedulerStatistics;

    @Autowired
    @Qualifier("syncPodInDBScheduler")
    private ThreadPoolTaskScheduler syncPodInDBScheduler;

    @PostConstruct
    public void initSchedulerStatistics() {
        schedulerStatistics.registerScheduler(schedulerName);
    }

    // 同步pod状态, 使用min(updated_time) 作为since 获取全量的更新结果，把podInfo里的since设置为pod的updated_time.
    @Scheduled(fixedDelay = FIX_DELAY_ONE)
    public void runScheduledTask() {
        schedulerStatistics.beforeSchedulerRun(schedulerName);
        syncPodInDBScheduler.execute(podInBuildSyncService::syncPodInBuild);
        schedulerStatistics.afterSchedulerRun(schedulerName);
    }
}
