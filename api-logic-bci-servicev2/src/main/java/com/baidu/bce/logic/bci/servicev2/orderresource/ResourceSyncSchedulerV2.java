package com.baidu.bce.logic.bci.servicev2.orderresource;

import com.baidu.bce.logic.bci.servicev2.orderresource.service.ResourceSyncServiceV2;
import com.baidu.bce.logic.bci.servicev2.sync.service.SyncServiceV2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;

@EnableScheduling
@Configuration("ResourceSyncSchedulerV2")
@Profile("default")
public class ResourceSyncSchedulerV2 extends SyncServiceV2 {
    private static final int FIX_DELAY_ONE_TIME_MS = 3000;

    @Autowired
    private ResourceSyncServiceV2 resourceSyncService;

//    // 启动立即执行一次
//    @Scheduled(fixedDelay = FIX_DELAY_ONE_TIME_MS)
//    public void runResourceStatusSyncTask() {
//        resourceSyncService.syncResourceStatusWithDB();
//    }
}
