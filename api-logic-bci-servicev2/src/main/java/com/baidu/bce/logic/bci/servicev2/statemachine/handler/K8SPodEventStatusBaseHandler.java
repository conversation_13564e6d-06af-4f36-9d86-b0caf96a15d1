package com.baidu.bce.logic.bci.servicev2.statemachine.handler;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.K8SPodEventStatusContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineContext;
import com.baidu.bce.logic.bci.servicev2.statemachine.context.StateMachineEventContext;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class K8SPodEventStatusBaseHandler extends StateMachineEventAbstractHandler {
    public static final Logger LOGGER = LoggerFactory.getLogger(K8SPodEventStatusBaseHandler.class);
    @Override
    public boolean checkEventContext() {
        if (!baseCheckEventContext()) {
            return false;
        }
        StateMachineContext context = getContext();
        StateMachineEventContext eventContext = context.getEventContext();
        if (eventContext == null) {
            return true;
        }
        if (eventContext instanceof K8SPodEventStatusContext) {
            return true;
        }
        return false;
    }

    @Override
    public boolean check() {
        return true;
    }

    @Override
    public boolean execute() {
        PodPO podPO = getPodPO();
        String podId = getPodId();
        StateMachinePodDaoContext podDaoContext = generateStateMachinePodDaoContext();
        K8SPodEventStatusContext eventContext = (K8SPodEventStatusContext) getContext().getEventContext();
        podPO.setResourceVersion(eventContext.getNewResourceVersion());
        podPO.setConditions(eventContext.getNewConditions());
        podPO.setBciResourceVersion(getNextBciResourceVersion());
        int podEventStatusResult = podDao.stateMachineK8SPodEventStatus(podPO, podDaoContext);
        if (podEventStatusResult == 0) {
            return false;
        }
        return true;
    }
}

