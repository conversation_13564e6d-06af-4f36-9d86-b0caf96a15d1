package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class Probe {
    private ExecAction exec;
    private HTTPGetAction httpGet;
    private TCPSocketAction tcpSocket;
    private GRPCAction grpc;
    private int initialDelaySeconds;
    private int timeoutSeconds;
    private int periodSeconds;
    private int successThreshold;
    private int failureThreshold;
    private long terminationGracePeriodSeconds;
}