package com.baidu.bce.logic.bci.servicev2.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;
import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ContainerDetailModel {
    private String name = "";
    private String image = "";
    private float cpu;
    private float memory;
    private float gpu = 0f;
    private String workingDir = "";
    private String imagePullPolicy = "";
    private List<String> commands = new ArrayList<>();
    private List<String> args = new ArrayList<>();
    private List<Port> ports = new ArrayList<>();
    private List<VolumeMount> volumeMounts = new ArrayList<>();
    private List<Environment> envs = new ArrayList<>();
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updateTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deleteTime;
    private ContainerStatus currentState;
    private ContainerStatus previousState;
    private Boolean ready;
    private int restartCount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public class ContainerStatus {
        private String state;
        private String reason;
        private String message;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Date startTime;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Date finishTime;
        private String detailStatus;
        private int exitCode;

        public ContainerStatus(ContainerPreviousState status) {
            this.setState(status.getState());
            this.setStartTime(status.getContainerStartTime());
            this.setFinishTime(status.getContainerFinishTime());
            this.setDetailStatus(status.getDetailStatus());
            this.setExitCode(status.getExitCode());
        }

        public ContainerStatus(ContainerCurrentState status) {
            this.setState(status.getState());
            this.setStartTime(status.getContainerStartTime());
            this.setFinishTime(status.getContainerFinishTime());
            this.setDetailStatus(status.getDetailStatus());
            this.setExitCode(status.getExitCode());
        }
    }

    public ContainerDetailModel(PodDetail.ContainerDetail container) {
        this.setName(container.getName());
        this.setImage(container.getImageAddress() + ":" + container.getImageVersion());
        this.setCpu(container.getCpu());
        this.setMemory(container.getMemory());
        this.setGpu(container.getGpuCount());
        this.setWorkingDir(container.getWorkingDir());
        this.setImagePullPolicy(container.getImagePullPolicy());
        this.setCommands(container.getCommands());
        this.setArgs(container.getArgs());
        this.setPorts(container.getPorts());
        List<VolumeMount> volumeMounts = new ArrayList<VolumeMount>();
        if (container.getVolumeMounts() == null) {
            for (VolumeMounts volume : container.getVolumeMounts()) {
                volumeMounts.add(new VolumeMount(volume));
            }
        }
        this.setVolumeMounts(volumeMounts);
        this.setEnvs(container.getEnvs());
        this.setCreateTime(container.getCreatedTime());
        this.setUpdateTime(container.getUpdatedTime());
        this.setDeleteTime(container.getDeletedTime());
        PodDetail.ContainerStatus containerStatus = container.getStatus();
        if (containerStatus != null) {
            ContainerStatus previousState = null;
            if (containerStatus.getPreviousState() != null) {
                previousState = new ContainerStatus(containerStatus.getPreviousState());
            }
            this.setPreviousState(previousState);

            ContainerStatus currentState = null;
            if (containerStatus.getCurrentState() != null) {
                currentState = new ContainerStatus(containerStatus.getCurrentState());
            }
            this.setCurrentState(currentState);
            this.setReady(containerStatus.isReady());
            this.setRestartCount(containerStatus.getRestartCount());
        }
    }
}