package com.baidu.bce.logic.bci.servicev2.util.iamauthorization;

public class AuthorizationConstant {
    public static final String DEFAULT_CREATE_RESOURCE = "*";
    public static final String ALLOW_PERMISSION = "ALLOW";
    public static final String DENY_PERMISSION = "DENY";
    public static final String SERVICE_BCI = "bce:bci";
    public static final String TYPE_PREFIX_POD = "pod/";
    public static final String BCI_OPERATE = "OPERATE";
    public static final String BCI_READ = "READ";
    public static final String BCI_CONTROL = "FULL_CONTROL";
    public static final String RESOURCE_OWNER_FOR_CREATING = "USE_REQUESTER_ACCOUNTID";
}