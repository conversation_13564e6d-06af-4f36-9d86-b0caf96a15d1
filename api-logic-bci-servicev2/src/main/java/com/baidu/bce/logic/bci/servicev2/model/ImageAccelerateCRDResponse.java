package com.baidu.bce.logic.bci.servicev2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.util.ArrayList;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImageAccelerateCRDResponse {
    private Metadata metadata;
    private ImageAccelerateCRDSpec spec;
    private ImageAccelerateCRDStatus status;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Metadata {
        private String name;
        private String namespace;
        private String resourceVersion;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageAccelerateCRDStatus {
        private Map<String, String> imageMirrors;
        private Map<String, String> imageStatus;
        private Map<String, Integer> imageProgress;
        // 镜像缓存状态变化：初始ready和ccrReady都为false，ready(cds ready)/ccrReady(ccr not ready)为中间态
        // ready(true)/ccrReady(true)为终态
        private boolean ready;
        private boolean ccrReady;
        private String cdsSnapshotID;
        private Map<String, ArrayList<String>> imageCPUTypes;
        private boolean imageScanDone;
        private String phase;
    }
}
