#!/bin/sh
APPLICATION=../bin/api-logic-bci.jar
SPRING_CONFIG_FILE=../conf/application.properties
ENDPOINT_FILE=file:../conf/endpoint.json
LOGBACK_FILE_PATH=../conf/logback.xml
MONITOR_DUMP_FILE=../monitor/logic_bci.data.bvar
SUPERVISE=../bin/supervise
SUPERVISE_CONF=../conf/supervise.conf
SUPERVISE_STATUS=../status/api-logic-bci
MAX_MEMORY=64000M
MAX_PERM_MEMORY=6000M
OPENJDK_JAVA_PATH=/usr/lib/jvm/jre-1.6.0-openjdk.x86_64/bin/java
USE_JDK8="use_jdk8=true"

JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:ParallelGCThreads=10 -Xmx$MAX_MEMORY -XX:MaxPermSize=$MAX_PERM_MEMORY -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintHeapAtGC -Xloggc:gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=20M"

BASE_LOG_CONFIG_OPTS="-Dlogging.config=$LOGBACK_FILE_PATH"

JAVA_LOG_OPTS="$BASE_LOG_CONFIG_OPTS"

function checkApplicationExist() {
    if [ ! -f $APPLICATION ]; then
        echo "Cannot find $APPLICATION"
        echo "The file is absent or does not have execute permission"
        echo "This file is needed to run this program"
        exit 1
    fi
}

function initJDKEnv() {
      if [ `grep -c "$USE_JDK8" $SPRING_CONFIG_FILE` -ne '0' ];then
        export JAVA_HOME=/home/<USER>/jdk1.8.0_130
        export PATH=$JAVA_HOME/bin:$PATH
      fi
}

function checkJDKEnv() {
    if [ -z "$JAVA_HOME" -a -z "$JRE_HOME" ]; then
        echo "Neither the JAVA_HOME nor the JRE_HOME environment variable is defined"
        echo "At least one of these environment variable is needed to run this program"
        exit 1
    fi
}

function getPorts() {
    IFS=$' \t\n\r' read -d '' -r -a tmp < debug.conf
    SERVER_PORT=${tmp[0]}
    DEBUG_PORT=${tmp[1]}
}

function initSupervise() {
    if [ ! -f $SUPERVISE ]; then
        echo "Cannot find $SUPERVISE"
        echo "The file is absent or does not have execute permission"
        echo "This file is needed to run this program"
        exit 1
    fi
    if [ ! -f $SUPERVISE_CONF ]; then
        echo "Cannot find $SUPERVISE_CONF"
        echo "This file is needed to run this program"
        exit 1
    fi
    if [ ! -d $SUPERVISE_STATUS ]; then
        mkdir -p $SUPERVISE_STATUS
    fi
}

function main() {
    checkApplicationExist
    initJDKEnv
    checkJDKEnv
    initSupervise
    CMD_OPTS="-Dbvar.dump.file=$MONITOR_DUMP_FILE -Dspring.config.location=$SPRING_CONFIG_FILE -Dendpoint.config=$ENDPOINT_FILE -Dfile.encoding=UTF-8"
    if [[ ${isDebug} ]]; then
        getPorts
        DEBUG_OPTS="-Dserver.port=${SERVER_PORT} -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=${DEBUG_PORT}"
        START_CMD="nohup java ${DEBUG_OPTS} ${CMD_OPTS} ${JAVA_LOG_OPTS} ${JAVA_OPTS} -jar $APPLICATION > /dev/null 2>&1 &"
        $SUPERVISE -p $SUPERVISE_STATUS -F $SUPERVISE_CONF -f "$START_CMD"
    else
          START_CMD="nohup java ${CMD_OPTS} ${JAVA_LOG_OPTS} ${JAVA_OPTS} -jar $APPLICATION > /dev/null 2>&1 &"
          $SUPERVISE -p $SUPERVISE_STATUS -F $SUPERVISE_CONF -f "$START_CMD"
    fi
}
while getopts "d" arg; do
case "${arg}" in
    d)
        isDebug=true
        ;;
    ?)
        echo "Unknown option"
        exit 1
        ;;
    esac
done
main
