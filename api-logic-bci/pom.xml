<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>api-logic-bci-root</artifactId>
        <groupId>com.baidu.bce</groupId>
        <version>${api-logic-bci-version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api-logic-bci</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-controller</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-controllerv2</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-web-framework-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-servicev2</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-order-executor</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
