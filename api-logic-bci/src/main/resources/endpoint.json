{"regions": [{"region": "default", "services": [{"service": "Messages", "endpoint": "http://gzbh-sandbox20-6271.gzbh.baidu.com:8681/v1"}, {"service": "IAM", "endpoint": "http://cq01-bce-46-84-11.cq01.baidu.com:8235/v3"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "STS", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8586/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test199.nmg02.baidu.com:8003/orders"}, {"service": "OrderV2", "endpoint": "http://order-sandbox.baidu-int.com/v2"}, {"service": "Resource", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/resources"}, {"service": "Bus", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8985/v1"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "BCM_SERVICE", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ENI", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}, {"service": "LogicalTag", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8777"}, {"service": "ChargeProxy", "endpoint": "http://nmg02-bce-test7.nmg02zz.baidu.com:8990"}, {"service": "DOCKER_HUB", "endpoint": "https://hub.docker.com"}, {"service": "CCE_SERVICE", "endpoint": "http://yq01-cce-caas-qa01-test03.epc.baidu.com:8694"}, {"service": "LOGIC_EIP", "endpoint": "http://eip.bce-testinternal.baidu.com"}, {"service": "cloud-trail", "endpoint": "http://************:8981/v1"}, {"service": "BLS_MASTER", "endpoint": "https://gzbh-sandbox74-store-2620.gzbh:8185"}, {"service": "BLS_SERVICE", "endpoint": "http://gzbh-sandbox74-store-2620.gzbh:8085"}, {"service": "USAGE_PACKAGE_SERVICE", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8666"}, {"service": "auto-renew", "endpoint": "http://renew.internal-qasandbox.baidu-int.com:8986"}]}, {"region": "bj", "services": [{"service": "Messages", "endpoint": "http://gzbh-sandbox20-6271.gzbh.baidu.com:8681/v1"}, {"service": "IAM", "endpoint": "http://cq01-bce-46-84-11.cq01.baidu.com:8235/v3"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "STS", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8586/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test199.nmg02.baidu.com:8003/orders"}, {"service": "OrderV2", "endpoint": "http://order-sandbox.baidu-int.com/v2"}, {"service": "Bus", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8985/v1"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "BCM_SERVICE", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ENI", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ApiService", "endpoint": "http://api.bcetest.baidu.com/v1"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}, {"service": "LogicalTag", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8777"}, {"service": "ChargeProxy", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8990"}, {"service": "DOCKER_HUB", "endpoint": "https://hub.docker.com"}, {"service": "CCE_SERVICE", "endpoint": "http://yq01-cce-caas-qa01-test03.epc.baidu.com:8694"}, {"service": "LOGIC_EIP", "endpoint": "http://eip.bce-testinternal.baidu.com"}, {"service": "cloud-trail", "endpoint": "http://************:8981/v1"}, {"service": "BLS_MASTER", "endpoint": "https://bls.bj.baidubce.com:8185"}, {"service": "BLS_SERVICE", "endpoint": "http://bls.bj.baidubce.com:8085"}, {"service": "USAGE_PACKAGE_SERVICE", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8666"}, {"service": "auto-renew", "endpoint": "http://renew.internal-qasandbox.baidu-int.com:8986"}]}, {"region": "gz", "services": [{"service": "Messages", "endpoint": "http://gzbh-sandbox20-6271.gzbh.baidu.com:8681/v1"}, {"service": "IAM", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:35357/v3"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/orders"}, {"service": "Resource", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/resources"}, {"service": "Bus", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8985/v1"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://cq02-inf-f02-bec78.cq02.baidu.com:9412/index.php/json-api/v2"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ENI", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}, {"service": "LogicalTag", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8777"}, {"service": "ChargeProxy", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8990"}, {"service": "DOCKER_HUB", "endpoint": "https://hub.docker.com"}, {"service": "CCE_SERVICE", "endpoint": "http://yq01-cce-caas-qa01-test03.epc.baidu.com:8694"}, {"service": "LOGIC_EIP", "endpoint": "http://eip.bce-testinternal.baidu.com"}, {"service": "cloud-trail", "endpoint": "http://************:8981/v1"}, {"service": "BLS_MASTER", "endpoint": "https://bls.gz.baidubce.com:8185"}, {"service": "BLS_SERVICE", "endpoint": "http://bls.gz.baidubce.com:8085"}, {"service": "USAGE_PACKAGE_SERVICE", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8666"}, {"service": "auto-renew", "endpoint": "http://renew.internal-qasandbox.baidu-int.com:8986"}]}, {"region": "hk", "services": [{"service": "Messages", "endpoint": "http://gzbh-sandbox20-6271.gzbh.baidu.com:8681/v1"}, {"service": "IAM", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:35357/v3"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/orders"}, {"service": "Resource", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/resources"}, {"service": "Bus", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8985/v1"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ENI", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}, {"service": "ChargeProxy", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8990"}, {"service": "DOCKER_HUB", "endpoint": "https://hub.docker.com"}, {"service": "CCE_SERVICE", "endpoint": "http://yq01-cce-caas-qa01-test03.epc.baidu.com:8694"}, {"service": "LOGIC_EIP", "endpoint": "http://eip.bce-testinternal.baidu.com"}, {"service": "cloud-trail", "endpoint": "http://************:8981/v1"}, {"service": "BLS_MASTER", "endpoint": "https://bls.hkg.baidubce.com:8185"}, {"service": "BLS_SERVICE", "endpoint": "http://bls.bj.baidubce.com:8085"}, {"service": "USAGE_PACKAGE_SERVICE", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8666"}, {"service": "auto-renew", "endpoint": "http://renew.internal-qasandbox.baidu-int.com:8986"}]}]}