server-host=qasandbox.bcetest.baidu.com
server.port=8784
#bci.fakeorder.enable=true #测试需要
#bci.ephemeral_quota.enable=false #跳过单机对磁盘限制功能
#bci.lxcfs.enable=false #跳过单机开启lxcfs
# bci.eni.enable=true #pod绑定eni
bci.eni.hexkey=WG8ugAEcU726q90B
bci.resource.account.id=b9ca12ddf6064c5eab7961d32496d564
bci.cpt1.region=true

bce.asyncwork.enable:true
pod.bus.enabled:true

swagger.start=false
swagger.app.docs= http://${server-host}:${server.port}

bce_plat_web_framework.is_web_application=false
iam.signature.headers=host;x-bce-accesskey;x-bce-console-rpc-id;x-bce-date;x-bce-request-id;x-bce-secretkey;

rsa.public.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI8qaFHtgi9yltIA3qYy2OU/91AC+yLVJlMLa0AR6eUae1V8hXjXFocWwWSreN9pzjFoEJXiGPj9jsE6Bh5FXpcCAwEAAQ==
rsa.private.key=MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAjypoUe2CL3KW0gDepjLY5T/3UAL7ItUmUwtrQBHp5Rp7VXyFeNcWhxbBZKt432nOMWgQleIY+P2OwToGHkVelwIDAQABAkAqLsaaDS8kp9DEg7kWozjBI33NN3OjqcYnBeBN+nk+JYRGtUWVYEhmR7b2GgTeXcolTuLmB00v9JsmbfvYDoKRAiEAzTqLkcj94YLjLPRilXODpz7F72QbLqFW3RU03279ET0CIQCylVAVPvoWn381wcBnbvCDOyn9EbfP+FVVBAHwb1nEYwIgDFTyQgZTyxM0V2Uv708LoCcTebkIMRscvxghHzPqHPkCIFYO+j6i0KXiSs0/B1dQ8Ppsonlf9nJ0O7ryaXTSVDH9AiB8DU3YJbKE1pY0jabmvO127JTbs52m3LVdmSxVddG8Og==

#=================== login & access ===================#
login.url=http://localhost:8088/uc/login
cookie.domain=localhost
login.cookie.md5.key=19920908
login.urls.not.need.auth=/asset/**;/dep/**;/esl.js;/swagger/**
login.urls.need.auth=/**
passport.appid=1240
passport.session.endpoint=http://localhost:8088/passport
uc.app.id=285
uc.server=localhost:8888

iam.access.failed.jump.url=http://localhost:8088/user/mockaccess?redirect=http://localhost:8080
iam.console.username=bci
iam.console.password=Bol9kmMkhqr0xHmMjxrvwcQueHL7nHSk
iam.access.paths=/**
iam.access.exclude.paths=
iam.sts.rolename=BceServiceRole_bci
pod.sts.policy.id=9b468c32be2e405ba60380e2d2389ee0
pod.sts.service.account.id=c3dfeaf0a5234f4fa11f995bfc1005d7

iam.permission.is.need.check.valid=false
bce.plat.iam.access=false
#======================================================#

#=================== logging config ===================#
logging.requestId_urlPattern=/*
logging.log_pattern= %d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } [%t] --- %logger [%F:%L] : [%X{x-bce-request-id}][%X{currentUser}] %m%n}
logging.has_console_appender= true

logging.has_web_debug_appender= true
logging.web_debug_path= /debug
logging.web_debug.level= INFO

### logback rolling log, uncomment to open appender ###
logging.info_log_file_path:  ../log/info/api-logic-bci.info.log
logging.error_log_file_path: ../log/error/api-logic-bci.error.log
logging.warn_log_file_path:  ../log/warn/api-logic-bci.warn.log
logging.debug_log_file_path: ../log/debug/api-logic-bci.debug.log

### access log, uncomment to open appender ###
logging.access_debug_uri_prefix:/
logging.access_log_file_path: ../log/access/api-logic-bci.access.log
logging.access_debug_log_file_path: ../log/access_debug/api-logic-bci.access_debug.log
#======================================================#

#================ passport modify ================#
passport.passgateEndpoint:http://***********:8300/passgate
passport.app.username:bceplat
passport.app.password:bceplat

#=================== logical database config begin ===================#
database.enable=false
database.logic.enable=true
database.mybatis.enable=true
database.isEmbedded=false
database.logic.url=*******************************************************************************************************************
database.logic.username=bcetest
database.logic.password=bcetest
database.script=
database.logic.mapper.locations=classpath*:*Mapper.xml

#=================== database config end ===================#

bce.logical.region=bj
usersettings.default.region=bj
region.defaultRegion=bj
region.currentRegion=bj

#================log monitor=================#
monitor.latencyRecorder.enable=true
bce.enable.debug=true
bce.logic.enable.debug=true
#=================zone init==========================#
bce.logical.pz.default=AZONE-nmg02

bcc.postpay.quota=20
instance.postpay.bj.quota=400
instance.postpay.gz.quota=500

#==================== download image begin ====================#
download.image.address=registry.baidubce.com/wenzt-test/thirdparty-registry:latest
image.initContainer.download.enable=true
image.accelerate.enable=false
image.accelerate.gc.minute.interval=43200
image.accelerate.failed.record.gc.minute.interval=1440
image.accelerate.crd.apiVersion=image.bci.cloud.baidu.com/v1
image.accelerate.crd.kind=ImageCache

#==================== job pod recycle ====================#
job.pod.resource.recycle.minute.interval=2

#=================== httpClient  ===================#
httpClient.maxConnTotal=800
httpClient.maxConnPerRoute=800

#=================== tomcat thread pool  ===================#
tomcat.maxThreads=300
tomcat.minSpareThreads=300

#=================== function enable  ===================#
snapshot.create.enable=true

#=================== status read from backend or local enable  ===================#
bce.status.read.local=true
spring.velocity.checkTemplateLocation=false

#======================plat log ============================#
plat.log.module.name: logic-bci
plat.log.format.request.in: true
plat.log.format.request.out:true
#===========================account user=================================#
bce.logical.permissionvertify.enabled:true


#===========================webshell url sandbox===================================#
webShell.url:ws://10.221.50.25:8784/api/logical/bci/v2/pod/containerwebshell/establish/
token.expire.second:60
session.expire.second:60


#=========================== batch create pod ===============================#
batch.create.success.rate=0.9

#====================iam authorization config====================#
bce.logical.iamauthorization.enabled=false

#=========================== order ===============================#
order.automatic.execute.enable=false


#============================bcc accountId========================#
bci.bcc.accountId:00dc1b52d8354d9193536e4dd2c41ae6

#==================== monitor metric config ===============================#
bce.logical.cprom.metrics=container_cpu_cfs_periods_total,container_cpu_cfs_throttled_periods_total,container_cpu_cfs_throttled_seconds_total,container_cpu_load_average_10s,container_cpu_system_seconds_total,container_cpu_usage_seconds_total,container_cpu_user_seconds_total,container_file_descriptors,container_fs_inodes_free,container_fs_inodes_total,container_fs_io_current,container_fs_io_time_seconds_total,container_fs_io_time_weighted_seconds_total,container_fs_limit_bytes,container_fs_read_seconds_total,container_fs_reads_bytes_total,container_fs_reads_merged_total,container_fs_reads_total,container_fs_sector_reads_total,container_fs_sector_writes_total,container_fs_usage_bytes,container_fs_write_seconds_total,container_fs_writes_bytes_total,container_fs_writes_merged_total,container_fs_writes_total,container_last_seen,container_memory_cache,container_memory_failcnt,container_memory_failures_total,container_memory_mapped_file,container_memory_max_usage_bytes,container_memory_rss,container_memory_swap,container_memory_usage_bytes,container_memory_working_set_bytes,container_network_receive_bytes_total,container_network_receive_errors_total,container_network_receive_packets_dropped_total,container_network_receive_packets_total,container_network_transmit_bytes_total,container_network_transmit_errors_total,container_network_transmit_packets_dropped_total,container_network_transmit_packets_total,container_processes,container_scrape_error,container_sockets,container_spec_cpu_period,container_spec_cpu_quota,container_spec_cpu_shares,container_spec_memory_limit_bytes,container_spec_memory_reservation_limit_bytes,container_spec_memory_swap_limit_bytes,container_start_time_seconds,container_tasks_state,container_threads,container_threads_max,container_ulimits_soft
bce.logical.cprom.host=https://cprom.bd.baidubce.com
bce.logical.cprom.uri=select/prometheus/api/v1/query
bce.logical.cprom.instanceid=cprom-fxxxxx
bce.logical.cprom.token=xxx.xxx.xxx
bce.logical.cprom.maxPodsPerRequest=100

#============================gpu specification========================#
gpu.specification=Nvidia A100 Nvswitch-80g|1|14:120,Nvidia A100 Nvswitch-80g|2|28:240,Nvidia A100 Nvswitch-80g|4|56:480,Nvidia A100 Nvswitch-80g|8|112:960,Nvidia A100 Nvswitch-40g|8|112:896,Nvidia A10 PCIE|1|8:36;18:74;28:112;38:150,Nvidia A10 PCIE|2|30:118,Nvidia A10 PCIE|4|62:240,Nvidia A30 PCIE|1|28:112,Nvidia A30 PCIE|2|56:224,Nvidia A30 PCIE|4|112:476,KUNLUN-R200|1|16:64,KUNLUN-R200|2|32:128,KUNLUN-R200|4|64:256,Nvidia V100 Nvlink-32g|1|10:80,Nvidia V100 Nvlink-32g|2|20:160,Nvidia V100 Nvlink-32g|4|40:320,Nvidia V100 Nvlink-32g|8|80:640,Nvidia T4 PCIE|1|20:80,Nvidia T4 PCIE|2|40:160,Nvidia T4 PCIE|4|80:320,Nvidia RTX 3070|1|10:40,Nvidia RTX 3070|2|20:80,Nvidia RTX 3070|4|40:160,Nvidia RTX 3070|8|80:320,Nvidia RTX 3080|1|10:40,Nvidia RTX 3080|2|20:80,Nvidia RTX 3080|4|40:160,Nvidia RTX 3080|8|80:320,Nvidia RTX 3090|1|8:36,Nvidia RTX 3090|2|18:74,Nvidia RTX 3090|4|38:150,Nvidia RTX 3090|8|78:302,Nvidia RTX 4090|8|128:476,Nvidia RTX 4090|8|128:978

# TO DO,智星云临时支持,支持手动管理镜像缓存后,需去除本部分代码
#============================image not ready then create pod fail white list========================#
image.accelerate.checkImageAccelerateReadyOrNotForTheseAccounts=b0cf8fadf1cb4a1191c16e94303ed759

#==============================kubelet proxy sidecar image================================#
bci.dspod.sidecar.kubeletProxy.image=registry.baidubce.com/bci-online-public/sidecar/kubelet-proxy:************

#=================== bci hostpath whitelist ========================#
bci.hostpath.whitelist=/var/log/pods,/noah,/home/<USER>/opt/compiler,/usr/lib64,/etc/localtime,/etc/nsswitch.conf,/var/run/kubelet-proxy