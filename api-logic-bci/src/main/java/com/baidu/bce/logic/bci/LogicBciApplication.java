package com.baidu.bce.logic.bci;

import com.baidu.bce.logic.bci.servicev2.util.PrometheusMetricsService;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.plat.webframework.BceServiceApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.TimeZone;

@Configuration
@EnableAutoConfiguration
@ComponentScan(basePackages = {"com.baidu.bce.*"}, basePackageClasses = {RegionConfiguration.class})
public class LogicBciApplication {

    @Autowired
    private PrometheusMetricsService prometheusMetricsService;
    @PostConstruct
    void started() {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        initializeMetrics();
    }

    private void initializeMetrics() {
        try {
            prometheusMetricsService.startPrometheusServer();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public static void main(String[] args) {
        BceServiceApplication.run(LogicBciApplication.class, args);
    }
}
