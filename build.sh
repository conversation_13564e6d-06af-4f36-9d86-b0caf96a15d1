#!/usr/bin/env bash
if [ $# -eq 0 ] ; then
  mvn -Dspring.profiles.active=test clean package -U
else
  if [ $1 = "-q" ] ; then
    mvn -Dmaven.test.skip=true -T 1C clean package -U
  else
    echo "build.sh [-q]"
    exit 0
  fi
fi

if [ $? -ne 0 ] ; then
  echo "mvn package error"
  exit -1
fi

rm -rf output
mkdir output

mkdir -p output/bin
mkdir -p output/conf
mkdir -p output/lib
mkdir -p output/status/api-logic-bci

cp -r api-logic-bci/supervise/* output
cp -r api-logic-bci/deploy/bin output

cp api-logic-bci/target/api-logic-bci-version.jar output/lib/api-logic-bci.jar
cp api-logic-bci/target/api-logic-bci-version.jar output/bin/api-logic-bci.jar

cp api-logic-bci/src/main/resources/*.properties output/conf
cp api-logic-bci/src/main/resources/endpoint.json output/conf
