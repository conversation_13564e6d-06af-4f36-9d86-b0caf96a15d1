package com.baidu.bce.logic.bci.controllerv2.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.ArrayList;

import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceIDGetter;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BciEipBind implements AuthorizationResourceIDGetter {
    private String podId;
    private String eip;

    public String getPodId() {
        return podId;
    }

    public void setPodId(String podId) {
        this.podId = podId;
    }

    public String getEip() {
        return eip;
    }

    public void setEip(String eip) {
        this.eip = eip;
    }

    @Override
    public List<String> getPodIDs() {
        List<String> result = new ArrayList<String>();
        result.add(podId);
        return result;
    }

    @Override
    public String toString() {
        return "BciEipBind{"
                + "podId='" + podId + '\''
                + ", eip='" + eip + '\''
                + '}';
    }
}
