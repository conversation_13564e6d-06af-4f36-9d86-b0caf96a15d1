package com.baidu.bce.logic.bci.controllerv2;


import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import com.baidu.bce.logic.bci.servicev2.model.UserInfoRequest;
import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.pod.CceClusterService;
import com.baidu.bce.logic.bci.servicev2.pod.PodLogService;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController("CceClusterControllerV2")
@RequestMapping({CceClusterController.BCI_BASE_URL_COMPATIBLE, CceClusterController.BCI_BASE_URL_V2})
public class CceClusterController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CceClusterController.class);

    public static final String BCI_BASE_URL_COMPATIBLE = "/api/logical/bci/v2";
    public static final String BCI_BASE_URL_V2 = "/v2";

    @Autowired
    private CceClusterService cceClusterService;

    @Autowired
    private PodLogService podLogService;

    @RequestMapping(value = "/cceCluster", method = RequestMethod.POST)
    @ApiOperation(value = "创建cecluster")
    public boolean createCceCluster(@RequestBody CceCluster cceCluster) {
        try {
            boolean res = cceClusterService.createCceCluster(cceCluster);
            return res;
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return false;
    }

    @RequestMapping(value = "/cceCluster/{cceId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询cecluster")
    public CceCluster getCceCluster(@PathVariable String cceId) {
        try {
            CceCluster cceCluster = cceClusterService.getCceClusterByCceId(cceId);
            return cceCluster;
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return null;
    }

    @RequestMapping(value = "/cceCluster/{cceId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除cecluster")
    public boolean deleteCceCluster(@PathVariable String cceId) {
        try {
            boolean res = cceClusterService.deleteCceClusterByCceId(cceId);
            return res;
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return false;
    }


    @RequestMapping(value = "/cceUserMap", method = RequestMethod.POST)
    @ApiOperation(value = "创建cec user 映射")
    public boolean createCceUserMap(@RequestBody CceUserMap cceUserMap) {
        try {
            boolean res = cceClusterService.createCceUserMap(cceUserMap);
            return res;
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return false;
    }

    @RequestMapping(value = "/cceUserMap/{userId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询用户的cceUserMap")
    public CceUserMap getCceUserMap(@PathVariable String userId) {
        try {
            CceUserMap cceUserMap = cceClusterService.getCceUserMapByUserId(userId);
            return cceUserMap;
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return null;
    }

    @RequestMapping(value = "/cceUserMap/{userId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除cceUserMap")
    public boolean deleteCceUserMap(@PathVariable String userId) {
        try {
            boolean res = cceClusterService.deleteCceUserMapByUserId(userId);
            return res;
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return false;
    }


    @RequestMapping(value = "/cceCluster/user/{userId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询用户所属的cce集群")
    public List<CceCluster> getCceClustersByUserId(@PathVariable String userId) {
        try {
            List<CceCluster> cceClusters = cceClusterService.getCceClustersByUserId(userId);
            return cceClusters;
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return null;
    }

    @RequestMapping(value = "/userinfo", method = RequestMethod.PUT)
    @ApiOperation(value = "更新用户信息")
    public void updateUserInfo(@RequestBody UserInfoRequest userInfo) {
        try {
            String accountId = userInfo.getAccountId();
            if (StringUtils.isEmpty(accountId)) {
                accountId = LogicUserService.getAccountId();
            }
            if (!StringUtils.isEmpty(userInfo.getBlsTaskToken())) {
                podLogService.updateSecret(accountId, userInfo.getBlsTaskToken());
            }
            cceClusterService.updateUserInfo(accountId, userInfo);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return;
    }
}
