package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.model.BatchDelImageCacheRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateImageCacheRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateImageCacheResponse;
import com.baidu.bce.logic.bci.servicev2.model.DeleteImageCacheResponse;
import com.baidu.bce.logic.bci.servicev2.model.ImageCacheResponseV2;
import com.baidu.bce.logic.bci.servicev2.pod.ImageCacheServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceID;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCheck;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("ImageCacheControllerV2")
@RequestMapping({ImageCacheControllerV2.BCI_OPENAPI_IMAGECACHE_URL_V2})
public class ImageCacheControllerV2 {
        
    private static final Logger LOGGER = LoggerFactory.getLogger(BciOpenApiController.class);
    
    public static final String BCI_OPENAPI_IMAGECACHE_URL_V2 = "/v2/imageCache";
    
    @Autowired 
    private ImageCacheServiceV2 imageCacheService;

    @ApiOperation(value = "OpenAPI:批量查询镜像缓存")
    @RequestMapping(method = RequestMethod.GET)
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                permissions = {AuthorizationConstant.BCI_READ})
    @VersionCheck()
    public ImageCacheResponseV2 descirbeImageCaches(
        @RequestParam(required = false, defaultValue = "-1") Long limit,
        @RequestParam(required = false, defaultValue = "-1") Long offset,
        @RequestParam(required = false, defaultValue = "10") Long pageSize,
        @RequestParam(required = false, defaultValue = "1") Long pageNo) {
        ImageCacheResponseV2 resp = null;
        // 除了显式指定limit和offset，否则走pageNo和pageSize接口
        if (limit != -1 && offset != -1) {
            try {
                if (limit <= 0) {
                    String message = "The limit is invalid, a valid limit must be between 1 and 1000.";
                    throw new CommonExceptions.RequestInvalidException(message);
                }
                if (offset < 0) {
                    String message = "The offset is invalid,  a valid offset must be greater than -1.";
                    throw new CommonExceptions.RequestInvalidException(message);
                }
                pageSize = limit;
                pageNo = (offset / pageSize) + 1;
                resp = imageCacheService.listImageCaches(pageSize, pageNo);
            } catch (BceException e) {
                LogicPodExceptionHandler.handle(e);
            } catch (Exception e) {
                LogicPodExceptionHandler.handle(e);
            }
            resp.setImageCaches(resp.getResult());
            return resp;
        }

        try {
            resp = imageCacheService.listImageCaches(pageSize, pageNo);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return resp;
    }

    @ApiOperation(value = "OpenAPI:创建镜像缓存")
    @RequestMapping(method = RequestMethod.POST)
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                permissions = {AuthorizationConstant.BCI_CONTROL})
    public CreateImageCacheResponse createImageCache(
        @RequestBody @Valid @AuthorizationResourceID CreateImageCacheRequest request) {
        CreateImageCacheResponse resp = new CreateImageCacheResponse();
         try {
            resp = imageCacheService.createImageCache(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return resp;
    }
   
    // TO DO NO_RESOURCE_ID->INSTRING
    @ApiOperation(value = "OpenAPI:删除镜像缓存")
    @RequestMapping(value="/{imageCacheId}", method = RequestMethod.DELETE)
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                   permissions = {AuthorizationConstant.BCI_CONTROL})
    public DeleteImageCacheResponse deleteImageCache(
        @PathVariable @AuthorizationResourceID String imageCacheId) {
        DeleteImageCacheResponse resp = new DeleteImageCacheResponse() ;
        try {
            imageCacheService.deleteImageCache(imageCacheId);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return resp;
    }

    // 批量删除接口
    @ApiOperation(value = "OpenAPI:批量删除镜像缓存")
    @RequestMapping(value = "/batchDel", method = RequestMethod.POST)
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
                   permissions = {AuthorizationConstant.BCI_CONTROL})
    public DeleteImageCacheResponse batchDeleteImageCache(
        @RequestBody @Valid @AuthorizationResourceID BatchDelImageCacheRequest request) {
        DeleteImageCacheResponse resp = new DeleteImageCacheResponse() ;
        try {
            imageCacheService.batchDelImageCache(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return resp;
    }
}
