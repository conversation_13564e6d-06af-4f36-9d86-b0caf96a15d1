package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.model.CacheStatus;
import com.baidu.bce.logic.bci.servicev2.model.ImageCacheRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageCacheResponse;
import com.baidu.bce.logic.bci.servicev2.pod.PodImageServiceV2;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController("ImageControllerV2")
@RequestMapping({ImageControllerV2.BCI_BASE_URL_COMPATIBLE, ImageControllerV2.BCI_BASE_URL_V2})
public class ImageControllerV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageControllerV2.class);

    public static final String BCI_BASE_URL_COMPATIBLE = "/api/logical/bci/v2/pod";
    public static final String BCI_BASE_URL_V2 = "/v2/pod";

    public static final String IMAGE_CACHE = "/imageCache";

    @Autowired
    private PodImageServiceV2 imageService;

    @RequestMapping(value = IMAGE_CACHE, method = RequestMethod.POST)
    @ApiOperation(value = "image cache")
    public ImageCacheResponse createImageCache(@RequestBody @Valid ImageCacheRequest request) {
        ImageCacheResponse response = new ImageCacheResponse();
        try {
            response = imageService.createImageCache(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = IMAGE_CACHE + "/{taskId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询镜像缓存任务")
    public CacheStatus getImageCache(@PathVariable String taskId) {
        CacheStatus cacheStatus = new CacheStatus();
        try {
            cacheStatus = imageService.getImageCache(taskId);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return cacheStatus;
    }
}
