package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.log.LogUtil;
import com.baidu.bce.logic.bci.servicev2.model.BciCreateResponse;
import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupRequestExtra;
import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupResponse;
import com.baidu.bce.logic.bci.servicev2.model.DeleteContainerGroupResponse;
import com.baidu.bce.logic.bci.servicev2.model.DescribeContainerGroupDetailResponse;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.InstanceDetailModel;
import com.baidu.bce.logic.bci.servicev2.model.InstanceModel;
import com.baidu.bce.logic.bci.servicev2.model.MDeleteContainerGroupRequest;
import com.baidu.bce.logic.bci.servicev2.model.MDeleteContainerGroupResponse;
import com.baidu.bce.logic.bci.servicev2.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodDetail;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.UpdateConfigFileRequest;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PrometheusMetricsService;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceID;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCheck;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.FROM_OPENAPI;
import static com.baidu.bce.logic.bci.servicev2.log.PodOperation.createPod;

@RestController("BciOpenApiController")
@RequestMapping({BciOpenApiController.BCI_OPENAPI_BASE_URL_V2})
public class BciOpenApiController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BciOpenApiController.class);

    public static final String BCI_OPENAPI_BASE_URL_V2 = "/v2/instance";

    public static final String BCI_BATCH_DELETE_API_URL = "/batchDel";

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private PrometheusMetricsService prometheusMetricsService;
    private static class FilterMap extends HashMap<String, String> {
    }

    @RequestMapping(method = RequestMethod.POST)
    @ApiOperation(value = "OpenAPI:创建一个BCI实例（即容器组）")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
                   permissions = {AuthorizationConstant.BCI_CONTROL})
    @VersionCheck()
    public CreateContainerGroupResponse createContainerGroup(
        @RequestBody @Valid @AuthorizationResourceID CreateContainerGroupRequest request,
        @RequestParam(required = false, defaultValue = "") String clientToken) {
        String from = FROM_OPENAPI;
        CreateContainerGroupRequestExtra requestExtra = new CreateContainerGroupRequestExtra(from, clientToken);
        LOGGER.debug("openapi createContainerGroup request is {}, request json data is {}, requestExtra is {}",
                request, JsonUtil.toJSON(request), requestExtra);
        // 判断安全组列表是否为null
        if (request.getSecurityGroupIds() == null || request.getSecurityGroupIds().size() == 0) {
            throw new PodExceptions.SecurityGroupInvalidException("The security groups should be specified.");
        }
        // 判断安全组列表中是否存在""
        for (String securityGroupId : request.getSecurityGroupIds()) {
            if (StringUtils.isEmpty(securityGroupId.trim())) {
                throw new PodExceptions.SecurityGroupInvalidException("The security groups is invalid.");
            }
        }
        // 校验GPU类型
        if (StringUtils.isNotEmpty(request.getGpuType())) {
            if (!request.getGpuType().equals("Nvidia A10 PCIE") && !request.getGpuType().equals("Nvidia RTX 4090")) {
                throw new PodExceptions.GPUTypeInvalidException("GpuType is invalid. Only 'Nvidia A10 PCIE/Nvidia RTX 4090' is supported.");
            }
        }
        BaseCreateOrderRequestVo<IOrderItem> baseCreateOrderRequest = request.convertToBaseCreateOrderRequest();
        LOGGER.debug(
                "openapi createContainerGroup convertToBaseCreateOrderRequest baseCreateOrderRequest json data is {}",
                JsonUtil.toJSON(baseCreateOrderRequest));
        CreateContainerGroupResponse response = null;
        BciCreateResponse bciCreateResponse = null;
        PrometheusMetricsService.PodCreateResult podCreateResult = PrometheusMetricsService.PodCreateResult.SUCCESS;
        try {
            bciCreateResponse = podService.createPod(baseCreateOrderRequest, requestExtra);
            LogUtil.operationLog(bciCreateResponse, createPod, bciCreateResponse.getPodIds(),
                    bciCreateResponse.getOrderId());
            response = new CreateContainerGroupResponse(bciCreateResponse);
        } catch (BceException e) {
            podCreateResult = PrometheusMetricsService.PodCreateResult.FAILED;
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            podCreateResult = PrometheusMetricsService.PodCreateResult.FAILED;
            LogicPodExceptionHandler.handle(e);
        } finally {
            prometheusMetricsService.podCreateRecord(podService.getAccountId(),
                    PrometheusMetricsService.PodCreatePhase.SYNC,
                    podCreateResult);
        }
        LOGGER.debug("openapi createContainerGroup response is {}, response json data is {}, requestExtra is {}",
            response, JsonUtil.toJSON(response), requestExtra);
        return response;
    }

    @ApiOperation(value = "OpenAPI:查询BCI实例列表")
    @RequestMapping(method = RequestMethod.GET)
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
                   permissions = {AuthorizationConstant.BCI_READ})
    @VersionCheck()
    public LogicMarkerResultResponse<InstanceModel> describeContainerGroups(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String keywordType,
            @RequestParam(required = false, defaultValue = "") String marker,
            @RequestParam(required = false, defaultValue = "10") Integer maxKeys) {
        String from = FROM_OPENAPI;
        LOGGER.debug("openapi describeContainerGroups request param keyword is {}, "
            + "keywordType is {}, marker is {}, maxKeys is {}, from is {}",
            keyword, keywordType, marker, maxKeys, from);
        String order = "";
        String orderBy = "";
        String cceId = "";
        String filters = "";
        PodListRequest listRequest =
            new PodListRequest(keyword, keywordType, order, orderBy, marker, maxKeys, cceId);
        LOGGER.debug("openapi describeContainerGroups pod list [marker], request data is {}", listRequest);
        LogicMarkerResultResponse<InstanceModel> response = new LogicMarkerResultResponse<InstanceModel>();
        LogicMarkerResultResponse<PodPO> markerResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }
            markerResultResponse = podService.listPodsWithMarkerByMultiKey(listRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        // 标注v2类型pod
        Collection<PodPO> podsV2 = markerResultResponse.getResult();
        List<InstanceModel> result = new ArrayList<InstanceModel>();
        Iterator<PodPO> itV2 = podsV2.iterator();
        while (itV2.hasNext()) {
            PodPO podV2 = itV2.next();
            podV2.setV2(true);
            InstanceModel instanceModel = new InstanceModel(podV2);
            result.add(instanceModel);
        }
        response.setMarker(markerResultResponse.getMarker());
        response.setIsTruncated(markerResultResponse.getIsTruncated());
        response.setNextMarker(markerResultResponse.getNextMarker());
        response.setMaxKeys(markerResultResponse.getMaxKeys());
        response.setResult(result);
        LOGGER.debug("openapi describeContainerGroups response is {}, response json data is {}, from is {}",
            response, JsonUtil.toJSON(response), from);
        return response;
    }

    @RequestMapping(value = "/{instanceId}", method = RequestMethod.GET)
    @ApiOperation(value = "OpenAPI:查询BCI实例详情")
    @VersionCheck()
    @Authorization(resourceLocation = Authorization.ResourceLocation.IN_STRING, 
                   permissions = {AuthorizationConstant.BCI_READ})
    public DescribeContainerGroupDetailResponse describeContainerGroupDetail(
        @PathVariable @AuthorizationResourceID String instanceId) {
        String from = FROM_OPENAPI;
        LOGGER.debug("openapi DescribeContainerGroupDetailResponse instanceId is {}, from is {}",
            instanceId, from);
        PodDetail podDetail = null;
        try {
            podDetail = podService.podDetail(instanceId);
        } catch (BceException e) {
            LOGGER.error("get podDetail BceException:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LOGGER.error("get podDetail error:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        }
        if (podDetail == null) {
            return null;
        }
        podDetail.setV2(true);
        InstanceDetailModel instanceDetailModel = new InstanceDetailModel(podDetail);
        DescribeContainerGroupDetailResponse response = new DescribeContainerGroupDetailResponse(instanceDetailModel);
        LOGGER.debug("openapi DescribeContainerGroupDetailResponse instanceId is {}, "
            + "reponse is {}, response json data is {}, from is {}",
            instanceId, response, JsonUtil.toJSON(response), from);
        return response;
    }

    @ApiOperation(value = "OpenAPI:删除BCI实例，删除后其使用的物理资源都被收回，比如EIP等资源")
    @RequestMapping(value="/{instanceId}", method = RequestMethod.DELETE)
    @VersionCheck()
    @Authorization(resourceLocation = Authorization.ResourceLocation.IN_STRING,
                   permissions = {AuthorizationConstant.BCI_CONTROL})
    public DeleteContainerGroupResponse deleteContainerGroup(
        @PathVariable @AuthorizationResourceID String instanceId,
        @RequestParam(required = false, defaultValue = "false") Boolean relatedReleaseFlag) {
        String from = FROM_OPENAPI;
        LOGGER.debug("openapi deleteContainerGroup instanceId is {}, from is {}",
            instanceId, from);
        PodBatchDeleteRequest podBatchDeleteRequest = new PodBatchDeleteRequest(instanceId, relatedReleaseFlag);
        DeleteContainerGroupResponse response = new DeleteContainerGroupResponse();
        try {
            podService.deletePod(podBatchDeleteRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        LOGGER.debug(
            "openapi deleteContainerGroup instanceId is {}, response is {}, response json data is {}, from is {}",
            instanceId, response, JsonUtil.toJSON(response), from);
        return response;
    }

    @ApiOperation(value = "OpenAPI:批量删除BCI实例，删除后其使用的物理资源都被收回，比如EIP等资源")
    @RequestMapping(value = BCI_BATCH_DELETE_API_URL, method = RequestMethod.POST)
    @VersionCheck()
    @Authorization(resourceLocation = Authorization.ResourceLocation.IN_CLASS_GETTER,
                   permissions = {AuthorizationConstant.BCI_CONTROL})
    public MDeleteContainerGroupResponse mDeleteContainerGroup(
        @RequestBody @Valid @AuthorizationResourceID MDeleteContainerGroupRequest request) {
        String from = FROM_OPENAPI;
        LOGGER.debug("openapi mDeleteContainerGroup request is {}, request json data is {}, from is {}",
                    request, JsonUtil.toJSON(request), from);
        PodBatchDeleteRequest podBatchDeleteRequest = new PodBatchDeleteRequest(request);
        MDeleteContainerGroupResponse response = new MDeleteContainerGroupResponse();
        try {
            podService.deletePod(podBatchDeleteRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        LOGGER.debug(
            "openapi mDeleteContainerGroup response is {}, response json data is {}, from is {}",
            response, JsonUtil.toJSON(response), from);
        return response;
    }

    @ApiOperation(value = "OpenAPI:更新BCI实例对应的configfile")
    @RequestMapping(value="/{instanceId}/configfile", method = RequestMethod.PUT)
    @Authorization(resourceLocation = Authorization.ResourceLocation.IN_STRING,
            permissions = {AuthorizationConstant.BCI_CONTROL})
    public void updateInstanceConfigFile(@PathVariable @AuthorizationResourceID String instanceId,
                                         @RequestBody @Valid UpdateConfigFileRequest request) {
        String from = FROM_OPENAPI;
        LOGGER.debug("openapi updateInstanceConfigFile podId {} request is {}, request json data is {}, from is {}",
                instanceId, request, JsonUtil.toJSON(request), from);
        try {
            podService.updateConfigFile(instanceId, request);
            podService.updatePodExtraInfo(instanceId, request.getExtras());
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }
}