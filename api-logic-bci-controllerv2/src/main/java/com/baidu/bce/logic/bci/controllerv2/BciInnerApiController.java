package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.inner.virtualkubelet.VirtualKubeletClusterReportService;
import com.baidu.bce.logic.bci.servicev2.model.inner.VirtualKubeletClusterReportRequest;
import com.baidu.bce.logic.bci.servicev2.model.inner.VirtualKubeletClusterReportResponse;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.FROM_INNERAPI;

@RestController("BciInnerApiController")
@RequestMapping({BciInnerApiController.BCI_INNERAPI_BASE_URL_V2})
public class BciInnerApiController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BciInnerApiController.class);

    public static final String BCI_INNERAPI_BASE_URL_V2 = "/inner/v2";

    public static final String VIRTUAL_KUBELET_CLUSTER_REPORT_API_URL = "/cluster/report";

    @Autowired
    private VirtualKubeletClusterReportService vkClusterReportService;

    @ApiOperation(value = "InnerAPI:VK(Virtual Kubelet)自主上报用户K8S集群Pod信息接口")
    @RequestMapping(value = VIRTUAL_KUBELET_CLUSTER_REPORT_API_URL, method = RequestMethod.POST)
    public VirtualKubeletClusterReportResponse virtualKubeletClusterReport(
            @RequestBody @Valid VirtualKubeletClusterReportRequest request) {
        String from = FROM_INNERAPI;
        LOGGER.debug("innerapi virtualKubeletClusterReport request is {}, request json data is {}, from is {}",
                request, JsonUtil.toJSON(request), from);
        VirtualKubeletClusterReportResponse response = null;
        try {
            response = vkClusterReportService.clusterReport(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        LOGGER.debug("innerapi virtualKubeletClusterReport response is {}, response json data is {}, from is {}",
                response, JsonUtil.toJSON(response), from);
        return response;
    }
}
