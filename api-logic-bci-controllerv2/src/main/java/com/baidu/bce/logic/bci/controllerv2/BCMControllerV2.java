package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.model.BCMListPodRequest;
import com.baidu.bce.logic.bci.servicev2.model.BCMListPodResponse;
import com.baidu.bce.logic.bci.servicev2.model.BCMPodContainerResponse;
import com.baidu.bce.logic.bci.servicev2.model.BCMPodDetailRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodForBCM;
import com.baidu.bce.logic.bci.servicev2.pod.BCMPodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceID;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController("BCMControllerV2")
@RequestMapping({BCMControllerV2.BASE_URL_V2})
public class BCMControllerV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(BCMControllerV2.class);


    public static final String BASE_URL_V2 = "/v2/bcm";

    @Autowired
    private BCMPodServiceV2 bcmPodService;

    @RequestMapping(value = "/pod/list", method = RequestMethod.POST)
    @ApiOperation(value = "容器组列表: BCM 侧边栏使用")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                   permissions = {AuthorizationConstant.BCI_READ})
    public BCMListPodResponse listPods(@RequestBody BCMListPodRequest request) {
        BCMListPodResponse pageResultResponse = null;
        try {
            pageResultResponse = bcmPodService.listPods(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    @RequestMapping(value = "/pod/detail", method = RequestMethod.POST)
    @ApiOperation(value = "查询容器组详情")
    @Authorization(resourceLocation = Authorization.ResourceLocation.IN_CLASS_GETTER, 
                   permissions = {AuthorizationConstant.BCI_READ})
    public PodForBCM podDetail(@RequestBody @AuthorizationResourceID BCMPodDetailRequest request) {
        PodForBCM podDetail = null;
        try {
            podDetail = bcmPodService.podDetail(request.getId());
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return podDetail;
    }

    @RequestMapping(value = "/pod/containers", method = RequestMethod.POST)
    @ApiOperation(value = "指标查看")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                   permissions = {AuthorizationConstant.BCI_READ})
    public BCMPodContainerResponse podContainers(@RequestBody BCMListPodRequest request) {
        BCMPodContainerResponse response = null;
        try {
            response = bcmPodService.listPodContainer(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = "/resgroup/pod/list", method = RequestMethod.POST)
    @ApiOperation(value = "容器组列表,bcm 实例组使用")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                   permissions = {AuthorizationConstant.BCI_READ})
    public BCMListPodResponse listPodForResGroup(@RequestBody BCMListPodRequest request) {
        BCMListPodResponse pageResultResponse = null;
        try {
            pageResultResponse = bcmPodService.listPods(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }
}
