package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.model.BciOpsDetailResponse;
import com.baidu.bce.logic.bci.servicev2.model.OpsCreateRequest;
import com.baidu.bce.logic.bci.servicev2.ops.OpsTaskService;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;


@RestController("BciOpsControllerV2")
@RequestMapping({BciOpsController.BCI_BASE_OPS_URL_COMPATIBLE})
public class BciOpsController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BciOpsController.class);
    public static final String BCI_BASE_OPS_URL_COMPATIBLE = "/v2/";

    public static final String BCI_OPS_TASK_CREATE = "opstask";

    public static final String BCI_OPS_TASK_RECORD = "opsrecord";

    @Autowired
    private OpsTaskService opsTaskService;

    @RequestMapping(value = BCI_OPS_TASK_CREATE, method = RequestMethod.POST)
    @ApiOperation(value = "创建容器组运维任务")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
            permissions = {AuthorizationConstant.BCI_OPERATE})
    public void createInstance(@RequestBody OpsCreateRequest request) {
        LOGGER.info("create opsTask, request data is {} ", request);
        try {
            opsTaskService.createOpsTask(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(value = BCI_OPS_TASK_RECORD, method = RequestMethod.GET)
    @ApiOperation(value = "容器组运维任务详情")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
            permissions = {AuthorizationConstant.BCI_READ})
    public BciOpsDetailResponse record(@RequestParam(required = false) String podId,
                                       @RequestParam(required = false) String opsType) {
        LOGGER.info("opsTask detail podId {} opsType {} ", podId, opsType);
        BciOpsDetailResponse record = null;
        try {
            record = opsTaskService.record(podId, opsType);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return record;
    }
}
