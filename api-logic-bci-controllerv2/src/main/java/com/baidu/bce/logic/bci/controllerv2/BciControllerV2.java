package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.internalsdk.bci.model.CCRImage;
import com.baidu.bce.internalsdk.bci.model.DockerHubImage;
import com.baidu.bce.internalsdk.bci.model.OfficialImage;
import com.baidu.bce.internalsdk.bci.model.PodEventPO;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.internalsdk.bci.model.WebshellUrl;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bcc.sdk.model.common.IDListRequest;
import com.baidu.bce.logic.bci.controllerv2.model.BciEipBind;
import com.baidu.bce.logic.bci.daov2.common.model.WebShell;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.podmigration.dao.PodMigrationDaoV2;
import com.baidu.bce.logic.bci.servicev2.charge.service.PodChargePushServiceV2;
import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.k8s.K8sService;
import com.baidu.bce.logic.bci.servicev2.log.LogUtil;
import com.baidu.bce.logic.bci.servicev2.model.BatchDescribeContainerGroupDetailRequest;
import com.baidu.bce.logic.bci.servicev2.model.BatchDescribeContainerGroupDetailResponse;
import com.baidu.bce.logic.bci.servicev2.model.BciQuota;
import com.baidu.bce.logic.bci.servicev2.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupRequestExtra;
import com.baidu.bce.logic.bci.servicev2.model.DescribeContainerGroupDetailRequestExtra;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.LeakagePodDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.MetricRspBody;
import com.baidu.bce.logic.bci.servicev2.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodDetail;
import com.baidu.bce.logic.bci.servicev2.model.PodForDisplay;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodListResponse;
import com.baidu.bce.logic.bci.servicev2.model.PodNumberRequest;
import com.baidu.bce.logic.bci.servicev2.model.PodNumberResponse;
import com.baidu.bce.logic.bci.servicev2.model.PodVpcResponse;
import com.baidu.bce.logic.bci.servicev2.model.SyncDsContainersRequest;
import com.baidu.bce.logic.bci.servicev2.model.UserVersion;
import com.baidu.bce.logic.bci.servicev2.model.BciCreateResponse;
import com.baidu.bce.logic.bci.servicev2.monitor.PromMetricServiceV2;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.PrometheusMetricsService;
import com.baidu.bce.logic.bci.servicev2.util.PodUtils;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceID;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache.Version;
import com.baidu.bce.logic.bci.servicev2.webshell.WebshellService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import okhttp3.Response;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.FROM_API;
import static com.baidu.bce.logic.bci.servicev2.log.PodOperation.createPod;
import static com.baidu.bce.logic.bci.servicev2.log.PodOperation.deletePod;
import static com.baidu.bce.logic.bci.servicev2.log.PodOperation.updatePod;

@RestController("BciControllerV2")
@RequestMapping({BciControllerV2.BCI_BASE_URL_COMPATIBLE, BciControllerV2.BCI_BASE_URL_V2})
public class BciControllerV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(BciControllerV2.class);
    public static final String BCI_BASE_URL_COMPATIBLE = "/api/logical/bci/v2/pod";
    public static final String BCI_BASE_URL_V2 = "/v2/pod";
    public static final String LIST_BY_PAGE_PARAM = "manner=page";
    public static final String LIST_BY_MARKER_PARAM = "manner=marker";
    public static final String LIST_BY_PAGE = "/list";

    public static final String LIST_BY_MARKER_LIGHT = "/list/light";

    public static final String LIST_BY_UPDATED_TIME = "/list/updatedTime";
    public static final String LIST_POD_EVENT_BY_PAGE = "/listPodEvent";
    public static final String BCI_USER_IMAGE_LIST = "/userImage";
    public static final String BCI_DOCKER_HUB_IMAGE_LIST = "/dockerHubImage";
    public static final String BCI_OFFICIAL_IMAGE_LIST = "/officialImage";
    public static final String BCI_CCR_IMAGE_LIST = "/ccrImage";
    public static final String BCI_DELETE_API_URL = "/delete";
    public static final String BCI_PUSH_LOG_API_URL = "/pushLog";
    public static final String BCI_LIST_DOWNLOAD = "/download";
    public static final String BCI_LOG = "/log";
    public static final String BCI_EIP_BIND = "/eip/bind";
    public static final String BCI_EIP_UNBIND = "/eip/unbind";
    public static final String BCI_QUOTA = "/quota";
    public static final String BCI_DOCKER_HUB_IMAGE_TAG = "/image/tags";
    public static final String BCI_POD_CREATE = "/create";
    public static final String BCI_CONFIG_FILE_DOWNLOAD = "/configFile/download";
    public static final String LIST_BY_UUID = "/listPodsByUuids";
    public static final String LIST_METRICS_BY_SHORT_ID = "/listMetricsByShortIds";

    public static final String LIST_METRICS_SUMMARY = "/listMetricsSummary";

    public static final String BCI_WEBSHELL = "/webshell";
    public static final String IMAGE_CACHE = "/imageCache";
    public static final String BCI_POD_SYNC_DS_CONTAINERS = "/dsContainers";

    public static final String BCI_POD_BATCH_DESCRIBE = "/describe";

    public static final String BCI_POD_DESCRIBE_LIGHT = "/describe/light";

    public static final String LIST_BY_MIGRATION_UUID = "/migration/{migrationUuid}";
    public static final String BCI_POD_DESCRIBE_DELETED = "/describe/deleted";

    @Autowired
    private PodServiceV2 podService;

    @Autowired
    private WebshellService webshellService;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PromMetricServiceV2 promMetricService;

    @Autowired
    private PodChargePushServiceV2 podChargePushServiceV2;

    @Autowired
    private VersionCache versionCache;

    @Autowired
    private PrometheusMetricsService prometheusMetricsService;

    @Autowired
    private PodMigrationDaoV2 podMigrationDaoV2;
    // Setter for unit tests to inject mock without ReflectionTestUtils
    public void setPodMigrationDaoV2(PodMigrationDaoV2 podMigrationDaoV2) {
        this.podMigrationDaoV2 = podMigrationDaoV2;
    }

    private static class FilterMap extends HashMap<String, String> {}

    @RequestMapping(value = LIST_BY_PAGE, method = RequestMethod.GET, params = LIST_BY_PAGE_PARAM)
    @ApiOperation(value = "容器组列表: 通过pageNo方式分页")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public LogicPageResultResponse<PodPO> listPodByPage(
        @RequestParam(required = false) String cceId,
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String keywordType,
        @RequestParam(required = false) String order,
        @RequestParam(required = false) String orderBy,
        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
        @RequestParam(required = false, defaultValue = "") String filters) {
        PodListRequest listRequest = new PodListRequest(keyword, keywordType, order, orderBy, pageNo, pageSize, cceId);
        LOGGER.debug("list bci[page], request data is {}", listRequest);

        LogicPageResultResponse<PodPO> pageResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }
            pageResultResponse = podService.listPodsWithPageByMultiKey(listRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        // 标注v2类型pod
        Collection<PodPO> podsV2 = pageResultResponse.getResult();
        Iterator<PodPO> itV2 = podsV2.iterator();
        while (itV2.hasNext()) {
            PodPO podV2 = itV2.next();
            podV2.setV2(true);
        }

        // todo merge v1版本的的pod,涉及到dao重命名,后面再做(xww)
        //        LogicPageResultResponse<com.baidu.bce.logic.bci.dao.pod.model.PodPO> pageResultResponseV1 =
        //                bciController.listPodByPage(cceId, keyword, keywordType, order, orderBy, pageNo, pageSize, filters);
        //
        //        Collection<com.baidu.bce.logic.bci.dao.pod.model.PodPO> podsV1 = pageResultResponseV1.getResult();
        //        LOGGER.debug("list bciv1, response item size is {}", podsV1.size());
        //
        //        pageResultResponse.setTotalCount(
        //                pageResultResponse.getResult().size() + pageResultResponseV1.getResult().size());
        //
        //        Iterator<com.baidu.bce.logic.bci.dao.pod.model.PodPO> itV1 = podsV1.iterator();
        //        while (itV1.hasNext()) {
        //            com.baidu.bce.logic.bci.dao.pod.model.PodPO podV1 = itV1.next();
        //            PodPO podv2 = new PodPO();
        //            PropertyUtils.copyProperties(podv2, podV1);
        //            pageResultResponse.getResult().add(podv2);
        //        }

        return pageResultResponse;
    }

    @RequestMapping(value = LIST_BY_PAGE, method = RequestMethod.GET, params = LIST_BY_MARKER_PARAM)
    @ApiOperation(value = "容器组列表: 通过marker方式分页")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public LogicMarkerResultResponse<PodPO> listPodByMarker(
        @RequestParam(required = false) String cceId,
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String keywordType,
        @RequestParam(required = false) String order,
        @RequestParam(required = false) String orderBy,
        @RequestParam(required = false, defaultValue = "") String marker,
        @RequestParam(required = false, defaultValue = "10") Integer maxKeys,
        @RequestParam(required = false, defaultValue = "") String filters) {
        PodListRequest listRequest = new PodListRequest(keyword, keywordType, "", "", marker, maxKeys, cceId);
        LOGGER.debug("list bci[marker], request data is {}", listRequest);

        LogicMarkerResultResponse<PodPO> markerResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }
            markerResultResponse = podService.listPodsWithMarkerByMultiKey(listRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        // 标注v2类型pod
        Collection<PodPO> podsV2 = markerResultResponse.getResult();
        Iterator<PodPO> itV2 = podsV2.iterator();
        while (itV2.hasNext()) {
            PodPO podV2 = itV2.next();
            podV2.setV2(true);
        }
        return markerResultResponse;
    }

    /**
     * @Description: 根据标记获取容器组列表（精简版）
     * @Param {String} cceId - CCE实例ID，可选参数，默认为空字符串
     * @Param {String} keyword - 关键词，可选参数，默认为空字符串
     * @Param {String} keywordType - 关键词类型，可选参数，默认为空字符串
     * @Param {String} order - 排序方式，可选参数，默认为空字符串
     * @Param {String} orderBy - 排序依据，可选参数，默认为空字符串
     * @Param {String} marker - 标记，必填参数
     * @Param {Integer} maxKeys - 每页最大条目数，必填参数，默认值为10
     * @Param {String} filters - 过滤条件，可选参数，默认为空字符串
     * @Param {String} from - 请求来源，可选参数，默认为FROM_API
     * @Return {LogicMarkerResultResponse<PodPO>} - 包含结果和下一次请求的标记的LogicMarkerResultResponse对象
     * @Throws {BceException} - BCE异常
     * @Throws {Exception} - 其他异常
     */
    @RequestMapping(value = LIST_BY_MARKER_LIGHT, method = RequestMethod.GET, params = LIST_BY_MARKER_PARAM)
    @ApiOperation(value = "容器组列表: 通过marker方式分页-精简版")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public LogicMarkerResultResponse<PodPO> listPodLightByMarker(
        @RequestParam(required = false) String cceId,
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String keywordType,
        @RequestParam(required = false) String order,
        @RequestParam(required = false) String orderBy,
        @RequestParam(required = false, defaultValue = "") String marker,
        @RequestParam(required = false, defaultValue = "10") Integer maxKeys,
        @RequestParam(required = false, defaultValue = "") String filters,
        @RequestParam(required = false, defaultValue = FROM_API) String from) {
        PodListRequest listRequest = new PodListRequest(keyword, keywordType, "", "", marker, maxKeys, cceId);
        LOGGER.debug(
            "api listPodLightByMarker request param cceId:{}, keyword:{}, keywordType:{}, order:{},"
                + " orderBy:{}, marker:{}, maxKeys:{}, filters:{}, from:{}, listRequest:{}",
            cceId,
            keyword,
            keywordType,
            order,
            orderBy,
            marker,
            maxKeys,
            filters,
            from,
            JsonUtil.toJSON(listRequest));
        LogicMarkerResultResponse<PodPO> markerResultResponse = null;
        try {
            listRequest.setFilterMap(new FilterMap());
            markerResultResponse = podService.listPodsLightWithMarkerByMultiKey(listRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        LOGGER.debug(
            "api listPodLightByMarker request param cceId:{}, keyword:{}, keywordType:{}, order:{},"
                + " orderBy:{}, marker:{}, maxKeys:{}, filters:{}, from:{},"
                + " response.size:{}, response.isTruncated:{}, response.nextMarker:{}",
            cceId,
            keyword,
            keywordType,
            order,
            orderBy,
            marker,
            maxKeys,
            filters,
            from,
            markerResultResponse.getResult().size(),
            markerResultResponse.getIsTruncated(),
            markerResultResponse.getNextMarker());
        return markerResultResponse;
    }

    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    @RequestMapping(value = LIST_BY_UPDATED_TIME, method = RequestMethod.GET)
    @ApiOperation(value = "容器组列表: updated time 大于给定时间的 pod 列表")
    public LogicPageResultResponse<PodPO> listPodByUpdatedTime(
        @RequestParam(required = true) long since,
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String keywordType) {

        LogicPageResultResponse<PodPO> pageResultResponse = null;
        try {
            pageResultResponse = podService.listPodsByUpdatedTime(keywordType, keyword, since);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        // 标注v2类型pod
        Collection<PodPO> podsV2 = pageResultResponse.getResult();
        Iterator<PodPO> itV2 = podsV2.iterator();
        while (itV2.hasNext()) {
            PodPO podV2 = itV2.next();
            podV2.setV2(true);
        }

        // todo merge v1版本的的pod,涉及到dao重命名,后面再做(xww)
        //        LogicPageResultResponse<com.baidu.bce.logic.bci.dao.pod.model.PodPO> pageResultResponseV1 =
        //                bciController.listPodByUpdatedTime(since, keyword, keywordType);
        //        Collection<com.baidu.bce.logic.bci.dao.pod.model.PodPO> podsV1 = pageResultResponseV1.getResult();
        //        LOGGER.debug("list bciv1 pods by update time, response item size is {}", podsV1.size());
        //
        //        Iterator<com.baidu.bce.logic.bci.dao.pod.model.PodPO> itV1 = podsV1.iterator();
        //
        //        while (itV1.hasNext()) {
        //            com.baidu.bce.logic.bci.dao.pod.model.PodPO podV1 = itV1.next();
        //            PodPO podv2 = new PodPO();
        //            PropertyUtils.copyProperties(podv2, podV1);
        //            pageResultResponse.getResult().add(podv2);

        //        }
        return pageResultResponse;
    }

    /**
     * @Description: 查询容器组详情接口，返回PodDetail对象
     * 注意：函数名不要改变，如果需改变，同步修改Authorization校验逻辑中的函数名
     * @Param {String} podId - 容器组ID，必填参数，字符串类型，来源于路径变量
     * @Param {String} from - 请求参数，可选，默认为空字符串，用于指定从何处获取资源的附加信息
     * @Return {PodDetail} podDetail - 容器组详情对象，包括容器组基本信息和容器组实例信息等，可能为null
     * @Throws {LogicPodExceptionHandler} - 当发生业务异常时抛出此异常
     * @ReturnType {PodDetail} 返回值是一个PodDetail对象，包括容器组基本信息和容器组实例信息等
     */
    @RequestMapping(value = "/{podId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询容器组详情")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_STRING,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public PodDetail podDetail(
        @PathVariable @AuthorizationResourceID String podId,
        @RequestParam(required = false, defaultValue = "") String from) {
        PodDetail podDetail = null;
        try {
            DescribeContainerGroupDetailRequestExtra extra = new DescribeContainerGroupDetailRequestExtra(from);
            podDetail = podService.podDetail(podId, extra);
        } catch (BceException e) {
            LOGGER.error("get podDetail BceException:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LOGGER.error("get podDetail error:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        }
        if (podDetail == null) {
            return null;
        }
        podDetail.setV2(true);
        return podDetail;
    }

    /**
     * @Description: 查询容器组详情-精简版接口实现方法
     * @Param {string} podId - 容器组ID，必填参数，字符串类型，来源于路径变量
     * @Param {string} from - 请求来源，可选参数，默认值为FROM_API，字符串类型
     * @Return {PodDetail} - 返回值为PodDetail类型，包括容器组的基本信息和容器列表等详细信息
     * @Throws {LogicPodExceptionHandler} - 当发生业务逻辑错误时抛出此异常
     * @Throws {BceException} - 当发生BCE服务异常时抛出此异常
     * @Throws {Exception} - 其他未知异常时抛出此异常
     */
    @RequestMapping(value = "/{podId}" + BCI_POD_DESCRIBE_LIGHT, method = RequestMethod.GET)
    @ApiOperation(value = "查询容器组详情-精简版")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_STRING,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public PodDetail podDetailWithLight(
        @PathVariable @AuthorizationResourceID String podId,
        @RequestParam(required = false, defaultValue = FROM_API) String from) {
        PodDetail podDetail = null;
        try {
            LOGGER.debug("api podDetailWithLight request param podId:{}, from:{}", podId, from);
            String detailLevel = DescribeContainerGroupDetailRequestExtra.DetailLevel.LIGHT.getLevel();
            DescribeContainerGroupDetailRequestExtra extra =
                new DescribeContainerGroupDetailRequestExtra(from, detailLevel);
            podDetail = podService.podDetail(podId, extra);
        } catch (BceException e) {
            LOGGER.error("get podDetail BceException:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LOGGER.error("get podDetail error:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        }
        LOGGER.debug(
            "api podDetailWithLight request param podId:{}, from:{} response:{}",
            podId,
            from,
            JsonUtil.toJSON(podDetail));
        return podDetail;
    }

    /**
     * @Description: 查询容器组详情-包含delete实例接口实现方法
     * @Param {string} podId - 容器组ID，必填参数，字符串类型，来源于路径变量
     * @Param {string} from - 请求来源，可选参数，默认值为FROM_API，字符串类型
     * @Return {PodDetail} - 返回值为PodDetail类型，包括容器组的基本信息和容器列表等详细信息
     * @Throws {LogicPodExceptionHandler} - 当发生业务逻辑错误时抛出此异常
     * @Throws {BceException} - 当发生BCE服务异常时抛出此异常
     * @Throws {Exception} - 其他未知异常时抛出此异常
     */
    @RequestMapping(value = "/{podId}" + BCI_POD_DESCRIBE_DELETED, method = RequestMethod.GET)
    @ApiOperation(value = "查询容器组详情-精简版-包含delete实例")
    @Authorization(
            resourceLocation = Authorization.ResourceLocation.IN_STRING,
            permissions = {AuthorizationConstant.BCI_READ}
    )
    public PodDetail podDetailWithDeleted(
            @PathVariable @AuthorizationResourceID String podId,
            @RequestParam(required = false, defaultValue = FROM_API) String from) {
        PodDetail podDetail = null;
        try {
            LOGGER.debug("api podDetailWithLight request param podId:{}, from:{}", podId, from);
            String detailLevel = DescribeContainerGroupDetailRequestExtra.DetailLevel.DELETED.getLevel();
            DescribeContainerGroupDetailRequestExtra extra =
                    new DescribeContainerGroupDetailRequestExtra(from, detailLevel);
            podDetail = podService.podDetail(podId, extra);
        } catch (BceException e) {
            LOGGER.error("get podDetail BceException:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LOGGER.error("get podDetail error:{}", e.toString());
            LogicPodExceptionHandler.handle(e);
        }
        LOGGER.debug(
                "api podDetailWithLight request param podId:{}, from:{} response:{}",
                podId,
                from,
                JsonUtil.toJSON(podDetail));
        return podDetail;
    }

    /**
     * @Description: 批量查询容器组详情，返回结果为精简版（不包含容器日志）
     * @Param {BatchDescribeContainerGroupDetailRequest} request - 请求参数，包含需要查询的容器组ID列表和其他可选参数
     * @Param {String} from - 调用来源标记，默认值为FROM_API，可选值为FROM_API、FROM_WEB
     * @Return {BatchDescribeContainerGroupDetailResponse} response - 返回结果，包含每个容器组对应的详情信息和错误码等信息
     * @Throws {BceException} e - 如果调用服务出现异常，则抛出BceException异常
     * @Throws {Exception} e - 如果调用服务出现异常，则抛出Exception异常
     * @ReturnType {BatchDescribeContainerGroupDetailResponse} 返回类型为BatchDescribeContainerGroupDetailResponse
     */
    @RequestMapping(value = BCI_POD_BATCH_DESCRIBE, method = RequestMethod.POST)
    @ApiOperation(value = "批量-查询容器组详情-精简版")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public BatchDescribeContainerGroupDetailResponse podBatchDetailWithLight(
        @RequestBody @Valid @AuthorizationResourceID BatchDescribeContainerGroupDetailRequest request,
        @RequestParam(required = false, defaultValue = FROM_API) String from) {
        LOGGER.debug("api podDetailWithLight request json body:{}, from:{}", JsonUtil.toJSON(request), from);
        BatchDescribeContainerGroupDetailResponse response = null;
        try {
            response = podService.podBatchDetail(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        if (response == null) {
            return null;
        }
        LOGGER.debug("api podDetailWithLight response.result.size:{}, from:{}", response.getResult().size(), from);
        return response;
    }

    @RequestMapping(value = "/vpcInfos/{podId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取容器组关联的网络信息")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_STRING,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public EdpResultResponse<PodVpcResponse> getVpcAndSubnetByPodId(
        @PathVariable @AuthorizationResourceID String podId) {
        Assert.notNull(podId, "please input query parameters!");
        EdpResultResponse<PodVpcResponse> result = new EdpResultResponse<>();
        result.setResult(podService.getVpcAndSubnetByPodId(podId));

        return result;
    }

    @RequestMapping(
        value = BCI_CONFIG_FILE_DOWNLOAD,
        method = RequestMethod.GET,
        produces = MediaType.APPLICATION_OCTET_STREAM_VALUE
    )
    @ApiOperation(value = "下载configFile")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_STRING,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public byte[] downloadConfigFile(
        @RequestParam @AuthorizationResourceID String podId,
        @RequestParam String name,
        @RequestParam String path,
        HttpServletResponse servletResponse) {

        byte[] download = null;
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String date = df.format(new Date());
        try {

            String fileName = URLEncoder.encode(path + "/configFile", "UTF-8");
            LOGGER.debug("downLoad peerConn file name is {}", fileName);
            servletResponse.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName);
            servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
            download = podService.downloadConfigFile(podId, name, path);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return download;
    }

    @ApiOperation(value = "删除容器组")
    @RequestMapping(value = BCI_DELETE_API_URL, method = RequestMethod.POST)
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_CLASS_GETTER,
        permissions = {AuthorizationConstant.BCI_CONTROL}
    )
    public void deletePod(
        @RequestBody @Valid @AuthorizationResourceID PodBatchDeleteRequest deleteRequest,
        @RequestParam(required = false, defaultValue = "") String from) {
        LogUtil.operationLog(deleteRequest.getDeletePods(), deletePod, "");
        try {
            LOGGER.debug("receive delete pods request:{}", deleteRequest.getPodIDs());
            podService.deletePod(deleteRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(value = BCI_POD_CREATE, method = RequestMethod.POST)
    @ApiOperation(value = "创建容器组")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_CONTROL}
    )
    public BciCreateResponse createInstance(
        @RequestBody BaseCreateOrderRequestVo<IOrderItem> request,
        @RequestParam(required = false, defaultValue = "") String from,
        @RequestParam(required = false, defaultValue = "") String clientToken) {
        // from = FROM_API;
        CreateContainerGroupRequestExtra requestExtra = new CreateContainerGroupRequestExtra(from, clientToken);
        LOGGER.debug(
            "api createInstance request is {}, request json data is {}, requestExtra is {}, from is {}",
            request,
            JsonUtil.toJSON(request),
            JsonUtil.toJSON(requestExtra), from);
        LogUtil.operationLog(request.getItems(), createPod, "");

        BciCreateResponse bciCreateResponse = null;
        PrometheusMetricsService.PodCreateResult podCreateResult = PrometheusMetricsService.PodCreateResult.SUCCESS;
        try {
            bciCreateResponse = podService.createPod(request, requestExtra);
            LogUtil.operationLog(
                bciCreateResponse, createPod, bciCreateResponse.getPodIds(), bciCreateResponse.getOrderId());
        } catch (BceException e) {
            podCreateResult = PrometheusMetricsService.PodCreateResult.FAILED;
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            podCreateResult = PrometheusMetricsService.PodCreateResult.FAILED;
            LogicPodExceptionHandler.handle(e);
        } finally {
            prometheusMetricsService.podCreateRecord(
                podService.getAccountId(), PrometheusMetricsService.PodCreatePhase.SYNC, podCreateResult);
        }
        LOGGER.debug(
            "api createInstance response is {}, response json data is {}, requestExtra is {}, from is {}",
            bciCreateResponse,
            JsonUtil.toJSON(bciCreateResponse),
            JsonUtil.toJSON(requestExtra), from);
        return bciCreateResponse;
    }

    @RequestMapping(value = "/{podID}/" + BCI_POD_SYNC_DS_CONTAINERS, method = RequestMethod.PUT)
    @ApiOperation(value = "增删改ds容器")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_STRING,
        permissions = {AuthorizationConstant.BCI_OPERATE}
    )
    public void syncDsContainers(
        @PathVariable @AuthorizationResourceID String podID,
        @RequestBody SyncDsContainersRequest request,
        @RequestParam(required = false, defaultValue = "") String from) {
        LogUtil.operationLog(request, updatePod, "");
        try {
            podService.syncDsContainers(podID, request);
            LogUtil.operationLog("end to sync ds containers", updatePod, "");
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(
        value = BCI_LIST_DOWNLOAD,
        method = RequestMethod.GET,
        produces = MediaType.APPLICATION_OCTET_STREAM_VALUE
    )
    @ApiOperation(value = "下载资源列表")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public byte[] download(
        @RequestParam(required = false) String cceId,
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String keywordType,
        @RequestParam(required = false) String order,
        @RequestParam(required = false) String orderBy,
        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
        @RequestParam(required = false, defaultValue = "") String filters,
        HttpServletResponse servletResponse) {

        PodListRequest listRequest = new PodListRequest(keyword, keywordType, order, orderBy, pageNo, pageSize, cceId);
        LOGGER.debug("list bci[page], request data is {}", listRequest);

        byte[] download = null;
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String date = df.format(new Date());
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }

            String fileName = URLEncoder.encode("pod_" + date + "_list.csv", "UTF-8");
            LOGGER.debug("downLoad peerConn file name is {}", fileName);
            // 这个header 由console来加  此处加的话 到console取不到
            servletResponse.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName);
            servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
            download = podService.download(listRequest);
        } catch (BceException e) {
            LOGGER.error("List bci error", e);
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return download;
    }

    @RequestMapping(
        value = BCI_CCR_IMAGE_LIST,
        method = RequestMethod.GET,
        params = {"pageNo"}
    )
    @ApiOperation(value = "用户CCR镜像列表, pageNo分页方式,支持搜索,排序")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public LogicPageResultResponse<CCRImage> listCCRImages(
        @RequestParam(required = false, defaultValue = "") String keyword,
        @RequestParam(required = false, defaultValue = "") String keywordType,
        @RequestParam(required = false, defaultValue = "desc") String order,
        @RequestParam(required = false, defaultValue = "createTime") String orderBy,
        @RequestParam(required = true, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug(
            "list images of user[{}] by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}",
            keywordType,
            keyword,
            order,
            orderBy,
            pageNo,
            pageSize);

        LogicPageResultResponse<CCRImage> result = null;
        try {
            result = podService.listCCRImage(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }

    @RequestMapping(
        value = BCI_USER_IMAGE_LIST,
        method = RequestMethod.GET,
        params = {"pageNo"}
    )
    @ApiOperation(value = "用户镜像列表, pageNo分页方式,支持搜索,排序")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public LogicPageResultResponse<UserImage> listUserImages(
        @RequestParam(required = false, defaultValue = "") String keyword,
        @RequestParam(required = false, defaultValue = "") String keywordType,
        @RequestParam(required = false, defaultValue = "desc") String order,
        @RequestParam(required = false, defaultValue = "createTime") String orderBy,
        @RequestParam(required = true, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug(
            "list images of user[{}] by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}",
            keywordType,
            keyword,
            order,
            orderBy,
            pageNo,
            pageSize);

        LogicPageResultResponse<UserImage> result = null;
        try {
            result = podService.listUserImage(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }

    @RequestMapping(
        value = BCI_DOCKER_HUB_IMAGE_LIST,
        method = RequestMethod.GET,
        params = {"pageNo"}
    )
    public LogicPageResultResponse<DockerHubImage> dockerHubImageList(
        @RequestParam(required = false, defaultValue = "") String keyword,
        @RequestParam(required = false, defaultValue = "name") String keywordType,
        @RequestParam(required = false, defaultValue = "asc") String order,
        @RequestParam(required = false, defaultValue = "name") String orderBy,
        @RequestParam(required = true, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug(
            "list dockerhub images by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}",
            keywordType,
            keyword,
            order,
            orderBy,
            pageNo,
            pageSize);

        LogicPageResultResponse<DockerHubImage> result = null;
        try {
            result = podService.listDockerHubImages(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }

    @RequestMapping(
        value = BCI_DOCKER_HUB_IMAGE_TAG,
        method = RequestMethod.GET,
        params = {"pageNo", "name"}
    )
    public LogicPageResultResponse<String> dockerHubImageTags(
        @RequestParam(required = true, defaultValue = "") String name,
        @RequestParam(required = true, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LogicPageResultResponse<String> result = null;
        try {
            result = podService.listDockerHubImageTags(name, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }

    @RequestMapping(
        value = BCI_OFFICIAL_IMAGE_LIST,
        method = RequestMethod.GET,
        params = {"pageNo"}
    )
    @ApiOperation(value = "给页面提供的百度云官方镜像列表, pageNo分页方式,支持搜索,排序")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public LogicPageResultResponse<OfficialImage> listOfficialImages(
        @RequestParam(required = false, defaultValue = "") String keyword,
        @RequestParam(required = false, defaultValue = "") String keywordType,
        @RequestParam(required = false, defaultValue = "asc") String order,
        @RequestParam(required = false, defaultValue = "repository") String orderBy,
        @RequestParam(required = true, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug(
            "list baidu official images by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}",
            keywordType,
            keyword,
            order,
            orderBy,
            pageNo,
            pageSize);

        LogicPageResultResponse<OfficialImage> result = null;
        try {
            result = podService.listOfficialImage(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }

    /**
     * 下载日志
     * 根据提供的podID和containerName，下载容器的日志
     *
     * @param podID Pod的唯一标识
     * @param containerName 容器的名称
     * @param limitBytes 限制返回的字节数
     * @param tailLines 返回日志的最后N行
     * @param sinceTime 返回相对于当前时间之前的日志，例如"2023-01-01T12:00:00Z"
     * @param sinceSeconds 返回相对于当前时间之前的日志（以秒为单位）
     * @param timestamps 是否在每个日志条目前加上时间戳
     * @param previous 返回之前的日志条目而不是之后的
     * @param follow 持续返回日志
     * @param servletResponse HttpServletResponse对象，用于向客户端发送响应
     */
    @RequestMapping(
        value = "/{podID}/{containerName}" + BCI_LOG,
        method = RequestMethod.GET,
        produces = MediaType.APPLICATION_OCTET_STREAM_VALUE
    )
    @ApiOperation(value = "下载日志")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_STRING,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public void logDownload(
        @PathVariable @AuthorizationResourceID String podID,
        @PathVariable String containerName,
        @RequestParam(required = false) Integer limitBytes,
        @RequestParam(required = false) Integer tailLines,
        @RequestParam(required = false) String sinceTime,
        @RequestParam(required = false) Integer sinceSeconds,
        @RequestParam(required = false) Boolean timestamps,
        @RequestParam(required = false) Boolean previous,
        @RequestParam(required = false) Boolean follow,
        HttpServletResponse servletResponse) {

        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String date = df.format(new Date());

        try {
            String fileName = URLEncoder.encode("pod_log_" + date + ".log", "UTF-8");
            LOGGER.debug("downLoad peerConn file name is {}", fileName);
            // 这个header 由console来加  此处加的话 到console取不到
            servletResponse.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
            PodPO podPO = podService.getPodPO(podID);
            ContainerCurrentState state = podService.checkContainerPurchasedAndGetState(podPO, containerName);
            String namespace = podPO.getUserId();
            Response response =
                k8sService.getPodLogWithResponse(
                    podID,
                    namespace,
                    containerName,
                    follow,
                    null,
                    limitBytes,
                    null,
                    previous,
                    sinceSeconds,
                    tailLines,
                    timestamps);
            response = PodUtils.rectifyContainerLogResponse(containerName, state, response);

            byte[] buff = new byte[1];
            BufferedInputStream bis = null;
            OutputStream os = null;
            try {
                os = servletResponse.getOutputStream();
                bis = new BufferedInputStream(response.body().byteStream());
                int i = bis.read(buff);
                while (i != -1) {
                    os.write(buff, 0, buff.length);
                    os.flush();
                    i = bis.read(buff);
                }
            } catch (IOException e) {
                if (bis != null) {
                    bis.close();
                }
            } finally {
                if (os != null) {
                    os.close();
                }
                if (bis != null) {
                    bis.close();
                }
            }
        } catch (BceException e) {
            LOGGER.error("get pod log error", e);
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LOGGER.error("get pod log error", e);
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(value = BCI_EIP_BIND, method = RequestMethod.POST)
    @ApiOperation(value = "绑定EIp")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_CLASS_GETTER,
        permissions = {AuthorizationConstant.BCI_OPERATE}
    )
    public EdpResultResponse<Object> bind(@RequestBody @AuthorizationResourceID BciEipBind bciEipBind) {

        EdpResultResponse<Object> response = new EdpResultResponse<Object>();
        try {
            podService.bindEipToPod(bciEipBind.getEip(), bciEipBind.getPodId());
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = BCI_EIP_UNBIND, method = RequestMethod.POST)
    @ApiOperation(value = "解绑绑EIp")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_CLASS_GETTER,
        permissions = {AuthorizationConstant.BCI_OPERATE}
    )
    public EdpResultResponse<Object> unBind(@RequestBody @AuthorizationResourceID BciEipBind bciEipBind) {

        EdpResultResponse<Object> response = new EdpResultResponse<>();
        try {
            podService.unBindEipFromPod(bciEipBind.getEip(), bciEipBind.getPodId());
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = BCI_QUOTA, method = RequestMethod.GET)
    @ApiOperation(value = "获取虚拟机相关的配额和使用情况")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public BciQuota bciQuota(@RequestParam(required = false) boolean needGlobalQuota) {
        BciQuota result = new BciQuota();

        try {
            result = podService.getBciQuota(needGlobalQuota);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return result;
    }

    @RequestMapping(value = LIST_BY_UUID, method = RequestMethod.POST)
    @ApiOperation(value = "BCI列表")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_ID_LIST,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public PodListResponse listServersByUuids(
        @RequestBody @AuthorizationResourceID IDListRequest listInstancesRequest) {
        PodListResponse result = new PodListResponse();
        List<PodPO> list = null;

        try {
            list = podService.getPods(listInstancesRequest.getIds());
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        List<PodForDisplay> resultList = new ArrayList<>();
        if (list != null) {
            for (PodPO podPO : list) {
                PodForDisplay poddisplay = PodUtils.convertBean(podPO, PodForDisplay.class);
                poddisplay.setV2(true);
                resultList.add(poddisplay);
            }
        }

        result.setResult(resultList);
        return result;
    }

    /**
     * 容器组事件列表查询接口
     * 通过pageNo方式分页获取容器组事件列表
     *
     * @param cceId 云容器引擎ID，用于筛选事件
     * @param keyword 关键字，用于模糊查询事件
     * @param keywordType 关键字类型，如事件名称、事件类型等
     * @param order 排序字段
     * @param orderBy 排序方式，升序或降序
     * @param pageNo 页码，默认值为1
     * @param pageSize 每页数量，默认值为1000
     * @param filters 过滤条件，JSON格式字符串
     * @return 分页查询结果，包含事件列表和分页信息
     * @throws BceException 云服务异常
     * @throws Exception 其他异常
     */
    @RequestMapping(value = LIST_POD_EVENT_BY_PAGE, method = RequestMethod.GET, params = LIST_BY_PAGE_PARAM)
    @ApiOperation(value = "容器组事件列表: 通过pageNo方式分页")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public LogicPageResultResponse<PodEventPO> listPodEventByPage(
        @RequestParam(required = false) String cceId,
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String keywordType,
        @RequestParam(required = false) String order,
        @RequestParam(required = false) String orderBy,
        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
        @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
        @RequestParam(required = false, defaultValue = "") String filters) {
        PodListRequest listRequest = new PodListRequest(keyword, keywordType, order, orderBy, pageNo, pageSize, cceId);
        LOGGER.debug("listPodEvent bci[page], request data is {}", listRequest);

        LogicPageResultResponse<PodEventPO> pageResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("listPodEvent bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }
            pageResultResponse = podService.listPodEventsWithPageByMultiKey(listRequest);
        } catch (BceException e) {
            LOGGER.error("listPodEvent bci error", e);
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    @RequestMapping(value = BCI_WEBSHELL, method = RequestMethod.POST)
    @ApiOperation(value = "webshell URL")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_WEBSHELL_REQ,
        permissions = {AuthorizationConstant.BCI_OPERATE}
    )
    public EdpResultResponse<WebshellUrl> webshell(@RequestBody @AuthorizationResourceID WebShell webShell) {
        EdpResultResponse<WebshellUrl> response = new EdpResultResponse<>();
        WebshellUrl webshellUrl = new WebshellUrl();
        try {
            LOGGER.info(
                "webshell param:podId is {} , tty is {} , stdin is {} , command is {}",
                webShell.getPodId(),
                webShell.getTty(),
                webShell.getStdin(),
                webShell.getCommand());
            String url = webshellService.handleLaunchContainerWebShell(webShell);
            webshellUrl.setUrl(url);
            response.setResult(webshellUrl);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @ApiOperation(value = "删除泄漏的容器")
    @RequestMapping(value = "/leakage/delete", method = RequestMethod.POST)
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IN_CLASS_GETTER,
        permissions = {AuthorizationConstant.BCI_CONTROL}
    )
    public void deleteLeakagePod(@RequestBody @Valid @AuthorizationResourceID LeakagePodDeleteRequest deleteRequest) {
        try {
            podService.deleteLeakagePod(deleteRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(value = "/podNumber", method = RequestMethod.POST)
    @ApiOperation(value = "pod 数量")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public PodNumberResponse podNumber(@RequestBody PodNumberRequest request) {
        PodNumberResponse podNumber = new PodNumberResponse();
        try {
            podNumber = podService.getPodNumberBySubnetId(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return podNumber;
    }

    @RequestMapping(value = "/userVersion", method = RequestMethod.GET)
    @ApiOperation(value = "查看用户是v1还是v2的用户")
    public UserVersion isV2() {
        UserVersion userVersion = new UserVersion();
        String accountId = podService.getAccountId();
        userVersion.setAccountID(accountId);
        Version version = versionCache.getUserVersion(accountId);
        if (version == Version.V2) {
            userVersion.setIsv2(true);
            userVersion.setIsv1(false);
            return userVersion;
        } else if (version == Version.V1) {
            userVersion.setIsv2(false);
            userVersion.setIsv1(true);
            return userVersion;
        } else {
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    @RequestMapping(value = "/zones", method = RequestMethod.GET)
    @ApiOperation(value = "查看用户可用的zone")
    public List<ZoneMapDetail> listZone() {
        return podService.listZone();
    }

    @RequestMapping(value = LIST_METRICS_BY_SHORT_ID, method = RequestMethod.POST)
    @ApiOperation(value = "获取Pod指标信息")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IGNORE_UNAVAILABLE_RESOURCE_FROM_ID_LIST,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public MetricRspBody listMetricsByShortIds(@RequestBody @AuthorizationResourceID IDListRequest request) {
        String from = request.getFrom();
        LOGGER.debug("api listMetricsByShortIds request json data is {}, from is {}", JsonUtil.toJSON(request), from);
        List<String> availablePodIds = podService.getAvailablePodIdsFromList(request.getIds());
        LOGGER.debug(
            "api listMetricsByShortIds request podIds {} availablePodIds {}",
            JsonUtil.toJSON(request.getIds()),
            JsonUtil.toJSON(availablePodIds));
        MetricRspBody response = promMetricService.getMetricsForTopPod(availablePodIds);
        LOGGER.debug(
            "api listMetricsByShortIds response json data size {}, from is {}", response.getResult().size(), from);
        return response;
    }

    @RequestMapping(value = LIST_METRICS_SUMMARY, method = RequestMethod.POST)
    @ApiOperation(value = "获取Pod指标信息汇总,目前只支持topPod的指标汇总")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.IGNORE_UNAVAILABLE_RESOURCE_FROM_ID_LIST,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public MetricRspBody listMetricsSummary(@RequestBody @AuthorizationResourceID IDListRequest request) {
        String from = request.getFrom();
        LOGGER.debug("api listMetricsSummary request json data is {}, from is {}", JsonUtil.toJSON(request), from);
        List<String> availablePodIds = podService.getAvailablePodIdsFromList(request.getIds());
        LOGGER.debug(
            "api listMetricsSummary request podIds {} availablePodIds {}",
            JsonUtil.toJSON(request.getIds()),
            JsonUtil.toJSON(availablePodIds));
        MetricRspBody response = promMetricService.getMetricsForTopPod(availablePodIds);
        LOGGER.debug(
            "api listMetricsSummary response json data size {}, from is {}", response.getResult().size(), from);
        return response;
    }

    @RequestMapping(value = LIST_BY_MIGRATION_UUID, method = RequestMethod.GET)
    @ApiOperation(value = "通过migrationUuid查询容器组迁移状态")
    @Authorization(
        resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
        permissions = {AuthorizationConstant.BCI_READ}
    )
    public PodListResponse listPodsByMigrationUuid(@PathVariable String migrationUuid) {
        PodListResponse result = new PodListResponse();
        try {
            java.util.List<com.baidu.bce.logic.bci.daov2.podmigration.model.PodMigrationPO> records =
                    podMigrationDaoV2.findByMigrationUuid(migrationUuid);
            java.util.List<String> podIds = new java.util.ArrayList<>();
            if (records != null) {
                for (com.baidu.bce.logic.bci.daov2.podmigration.model.PodMigrationPO po : records) {
                    podIds.add(po.getPodId());
                }
            }

            java.util.List<PodForDisplay> resultList = new java.util.ArrayList<>();
            if (!podIds.isEmpty()) {
                java.util.List<com.baidu.bce.logic.bci.daov2.pod.model.PodPO> pods = podService.getPods(podIds);
                if (pods != null) {
                    for (com.baidu.bce.logic.bci.daov2.pod.model.PodPO podPO : pods) {
                        PodForDisplay podDisplay = PodUtils.convertBean(podPO, PodForDisplay.class);
                        podDisplay.setV2(true);
                        resultList.add(podDisplay);
                    }
                }
            }
            result.setResult(resultList);
        } catch (Exception e) {
            com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler.handle(e);
        }
        return result;
    }
}
