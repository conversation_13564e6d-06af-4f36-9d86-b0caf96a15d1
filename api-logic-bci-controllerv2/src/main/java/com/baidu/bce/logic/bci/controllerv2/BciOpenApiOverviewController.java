package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.model.PreviewPodCapacityRequest;
import com.baidu.bce.logic.bci.servicev2.model.PreviewPodCapacityResponse;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationResourceID;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.baidu.bce.logic.bci.servicev2.constant.PodConstants.FROM_OPENAPI;

@RestController("BciOpenApiOverviewController")
@RequestMapping({BciOpenApiOverviewController.BCI_OPENAPI_OVERVIEW_BASE_URL_V2})
public class BciOpenApiOverviewController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BciOpenApiController.class);

    public static final String BCI_OPENAPI_OVERVIEW_BASE_URL_V2 = "/v2/overview";

    @Autowired
    private PodServiceV2 podService;

    @ApiOperation(value = "OpenAPI: 预览Pod容量")
    @RequestMapping(params = "action=instanceCapacityPreview", method = RequestMethod.POST)
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
                   permissions = {AuthorizationConstant.BCI_CONTROL})
    public PreviewPodCapacityResponse getPreviewPodCapacity(
        @RequestBody @Valid @AuthorizationResourceID PreviewPodCapacityRequest request,
        @RequestParam(required = false, defaultValue = "") String clientToken) {
        String from = FROM_OPENAPI;
        LOGGER.debug("getPreviewPodCapacity request is {}, request json data is {}, from is {}",
                request, JsonUtil.toJSON(request), from);
        PreviewPodCapacityResponse response = null;
        try {
            response = podService.previewPodCapacity(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        LOGGER.debug("getPreviewPodCapacity response is {}, response json data is {}, from is {}",
            response, JsonUtil.toJSON(response), from);
        return response;
    }
}
