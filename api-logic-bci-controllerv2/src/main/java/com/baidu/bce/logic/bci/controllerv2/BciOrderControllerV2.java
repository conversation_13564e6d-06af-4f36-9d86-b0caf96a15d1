package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.externalsdk.logical.network.common.model.OrderDetailRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.order.model.order.OrderType;
import com.baidu.bce.logic.bci.controllerv2.model.OrderUpdateRequest;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.OrderRenewResponse;
import com.baidu.bce.logic.bci.servicev2.model.OrderRenewRequest.OrderRenewRequestItem;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.pod.PodOrderServiceV2;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.reservedinstance.ReservedInstanceService;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.bci.servicev2.model.OrderRenewRequest;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateRenewTypeOrderItem;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller("BciOrderControllerV2")
@RequestMapping("/api/bci/v2/order")
public class BciOrderControllerV2 {

    @Autowired
    PodOrderServiceV2 podOrderService;

    @Autowired
    PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Autowired
    protected RegionConfiguration regionConfiguration;

    @Autowired
    private PodServiceV2 podServiceV2;

    private static final Logger LOGGER = LoggerFactory.getLogger(BciOrderControllerV2.class);

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.POST, produces = {"application/json"})
    @ApiOperation(value = "BCI订单详情")
    public EdpResultResponse<Order> detail(@RequestBody OrderDetailRequest request) {
        if (StringUtils.isEmpty(request.getUuid())) {
            throw new PodExceptions.RequestInvalidException();
        }

        EdpResultResponse<Order> response = new EdpResultResponse<Order>();

        try {
            Order order = podOrderService.getBciOrderDetail(request);
            response.setResult(order);
        } catch (BceInternalResponseException ex) {
            throw new PodExceptions.InternalServerErrorException();
        }

        return response;
    }

    @ResponseBody
    @RequestMapping(value = "/renew", method = RequestMethod.POST, produces = {"application/json"})
    @ApiOperation(value = "续费BCI订单,当前仅支持预留资源包")
    public OrderRenewResponse renew(@RequestBody OrderRenewRequest request) {
        // TODO: 
        //     1、当前供需匹配需认为配置Quota后，才能进行续费，在过期续费场景下难以做到严格匹配。暂时建议用户在预留
        //        实例券过期前续费，或者过期后重新购买新预留实例券。
        //     2、当前预留实例券过期场景下，续费计算的生效时间、失效时间以及供需匹配基于当前时间，billing实际处理时
        //        使用的将是订单时间，可能存在秒级别差异。后续可考虑特殊配置订单执行器，使其能处理续费逻辑。
        LOGGER.debug("[Order][Renew]request is {}", request);
        OrderRenewResponse response = new OrderRenewResponse();
        OrderRenewResponse.OrderRenewResult result = response.new OrderRenewResult();
        response.setResult(result);
        OrderRenewResponse.Message msg = response.new Message();
        try {
            CreateOrderRequest<CreateRenewTypeOrderItem>  createOrderRequest = new CreateOrderRequest<>();
            createOrderRequest.setOrderType(OrderType.RENEW.name());
            createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
            createOrderRequest.setTotal(request.getItems().size());
            createOrderRequest.setPaymentMethod(request.getPaymentMethod());
            List<CreateRenewTypeOrderItem> items = new ArrayList<>();
            for (OrderRenewRequestItem orderRenewRequestItem : request.getItems()) {
                CreateRenewTypeOrderItem item = new CreateRenewTypeOrderItem();
                item.setResourceUuid(orderRenewRequestItem.getConfig().getUuid());
                item.setTime(orderRenewRequestItem.getConfig().getDuration());
                // 续费管理页面请求bci续费接口传入的 timeUnit 是 "month"，但是后台接口需要传入 "MONTH"，所以这里做了转换
                item.setTimeUnit(orderRenewRequestItem.getConfig().getTimeUnit().toUpperCase());
                item.setServiceType(orderRenewRequestItem.getConfig().getServiceType());
                item.setProductType("prepay");
                item.setSubProductType("ReservedPackage");
                item.setPaymentMethod(orderRenewRequestItem.getPaymentMethod());
                items.add(item);
            }
            createOrderRequest.setItems(items);
            OrderUuidResult orderUuidResult;
            orderUuidResult = podServiceV2.submitCreateOrderToServiceCatalog(createOrderRequest, "[Order][Renew]");
            LOGGER.debug("[Order][Renew]create renew order result of {} is {}", request, orderUuidResult.toString());
            result.setOrderId(orderUuidResult.getOrderId());
            response.setSuccess(true);
            response.setResult(result);
        } catch (Exception e) {
            LOGGER.error("[Order][Renew]failed to renew order, exception is {}", e);
            response.setSuccess(false);
            msg.setGlobal(e.getMessage());
            response.setMessage(msg);
            response.setCode("Exception");
        }

        return response;
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.PUT, produces = {"application/json"})
    @ApiOperation(value = "更新BCI订单状态")
    public EdpResultResponse<Order> update(@RequestBody OrderUpdateRequest request) {
        if (StringUtils.isEmpty(request.getOrderId())) {
            throw new PodExceptions.RequestInvalidException();
        }

        EdpResultResponse<Order> response = new EdpResultResponse<Order>();

        try {
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.valueOf(request.getStatus()));
            podNewOrderExecutorServiceV2.updateOrderStatus(request.getOrderId(), updateOrderRequest);

            OrderDetailRequest orderDetailRequest = new OrderDetailRequest();
            orderDetailRequest.setKey("bci");
            orderDetailRequest.setUuid(request.getOrderId());
            Order order = podOrderService.getBciOrderDetail(orderDetailRequest);
            response.setResult(order);
        } catch (BceInternalResponseException ex) {
            throw new PodExceptions.InternalServerErrorException();
        }


        return response;
    }
}
