package com.baidu.bce.logic.bci.controllerv2;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstanceSpec;
import com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceResponse;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.reservedinstance.ReservedInstanceService;
import com.baidu.bce.logic.bci.servicev2.util.JsonUtil;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("ReservedInstanceCnotroller")
@RequestMapping({ReservedInstanceController.BCI_RESERVED_INSTANCE_BASE_URL})
public class ReservedInstanceController {
    public static final String BCI_RESERVED_INSTANCE_BASE_URL = "/v2/reservedInstance";
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ReservedInstanceController.class);

    @Autowired
    private ReservedInstanceService reservedInstanceService;

    private static class FilterMap extends HashMap<String, String> {
    }
    
    @RequestMapping(method = RequestMethod.GET)
    @ApiOperation(value = "预留实例券列表: 通过pageNo方式分页")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
                   permissions = {AuthorizationConstant.BCI_READ})
    public LogicPageResultResponse<ReservedInstancePO> listReservedInstanceByPage(
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String keyword,
            @RequestParam(required = false, defaultValue = "") String keywordType,
            @RequestParam(required = false, defaultValue = "") String filters) {
        ListRequest listRequest = new ListRequest();
        List<OrderModel> orderModelList = new ArrayList<OrderModel>();
        OrderModel orderModel = new OrderModel();
        orderModel.setOrder(order);
        orderModel.setOrderBy(orderBy);
        orderModelList.add(orderModel);
        listRequest.setOrders(orderModelList);
        listRequest.setPageNo(pageNo);
        listRequest.setPageSize(pageSize);
        listRequest.setKeyword(keyword);
        listRequest.setKeywordType(keywordType);
        LOGGER.debug("list ReservedInstance[page], request data is {}", listRequest);

        LogicPageResultResponse<ReservedInstancePO> pageResultResponse = null;
        FilterMap filterMap = new FilterMap();
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.debug("list ReservedInstance[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
            }
            // 将keyword和keywordType放入filterMap中，以便后续一起校验合法性，映射数据库字段
            if (!listRequest.getKeywordType().isEmpty() && 
                    !listRequest.getKeyword().isEmpty()) {
                filterMap.put(listRequest.getKeywordType(), 
                        listRequest.getKeyword());
            }
            listRequest.setFilterMap(filterMap);
            pageResultResponse = reservedInstanceService.listReservedInstancesWithPageByMultiKey(listRequest);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return pageResultResponse;
    }

    @RequestMapping(method = RequestMethod.POST)
    @ApiOperation(value = "创建预留实例券")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                   permissions = {AuthorizationConstant.BCI_CONTROL})
    public CreateReservedInstanceResponse createReservedInstance(
            @RequestBody BaseCreateOrderRequestVo<CreateReservedInstanceRequest> request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create ReservedInstance, request is {}, request json data is {}, from is {}",
                request, JsonUtil.toJSON(request), from);
        CreateReservedInstanceResponse response = null;
        try {
            // 入口处遍历所有请求，未配置 effectiveTime 的将此字段设置为当前时间
            if (request.getItems() != null) {
                for (int i = 0; i != request.getItems().size(); i++) {
                    if (request.getItems().get(i).getConfig().getEffectiveTime() == null) {
                        request.getItems().get(i).getConfig().setEffectiveTime(new Timestamp(
                                System.currentTimeMillis() + 8 * 3600 * 1000));
                    }
                    if (request.getItems().get(i).getConfig().getName() == null) {
                        request.getItems().get(i).getConfig().setName("");
                    }
                } 
                response = reservedInstanceService.createReservedInstance(request);
                LOGGER.debug("create ReservedInstance succ, response is {}", response);
            }
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }
    @RequestMapping(value = "/specs", method = RequestMethod.GET)
    @ApiOperation(value = "预留实例券规格列表")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
                   permissions = {AuthorizationConstant.BCI_READ})
    public LogicPageResultResponse<ReservedInstanceSpec> listReservedInstanceSpecs() {
        return reservedInstanceService.listReservedInstanceSpecs();
    }

    @RequestMapping(value = "/deduction", method = RequestMethod.GET)
    @ApiOperation(value = "抵扣明细预测：根据预留实例券ID")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID,
            permissions = {AuthorizationConstant.BCI_READ})
    public LogicPageResultResponse<PodPO> listPodByReservedInstanceId(
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false) String reservedInstanceId) {
        PodListRequest listRequest =
                new PodListRequest("", "", order, orderBy, pageNo, pageSize, "");
        LOGGER.debug("listPodByReservedInstanceId[page], request data is {}", listRequest);

        LogicPageResultResponse<PodPO> pageResultResponse = null;
        try {
            pageResultResponse = reservedInstanceService.getPodByReservedInstanceId(reservedInstanceId, listRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        // 标注v2类型pod
        Collection<PodPO> podsV2 = pageResultResponse.getResult();
        Iterator<PodPO> itV2 = podsV2.iterator();
        while (itV2.hasNext()) {
            PodPO podV2 = itV2.next();
            podV2.setV2(true);
        }

        return pageResultResponse;
    }
}