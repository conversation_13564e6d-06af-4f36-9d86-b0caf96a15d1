package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.servicev2.model.TagListPodResponse;
import com.baidu.bce.logic.bci.servicev2.model.TagListPodRequest;
import com.baidu.bce.logic.bci.servicev2.pod.TagPodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.Authorization;
import com.baidu.bce.logic.bci.servicev2.util.iamauthorization.AuthorizationConstant;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController("TagControllerV2")
@RequestMapping({TagControllerV2.BASE_URL_V2})
public class TagControllerV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(TagControllerV2.class);


    public static final String BASE_URL_V2 = "/v2/tag";

    @Autowired
    private TagPodServiceV2 tagPodService;

    @RequestMapping(value = "/resources", method = RequestMethod.POST)
    @ApiOperation(value = "容器组列表: Tag 绑定容器实例使用")
    @Authorization(resourceLocation = Authorization.ResourceLocation.NO_RESOURCE_ID, 
                   permissions = {AuthorizationConstant.BCI_READ})

    public TagListPodResponse listPods(@RequestBody TagListPodRequest request) {
        TagListPodResponse pageResultResponse = null;
        try {
            pageResultResponse = tagPodService.listPods(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }
}
