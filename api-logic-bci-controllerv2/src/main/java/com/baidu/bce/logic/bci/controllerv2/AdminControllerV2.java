package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.wordnik.swagger.annotations.ApiOperation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController("AdminControllerV2")
@RequestMapping({AdminControllerV2.BCI_ADMIN_BASE_URL})
public class AdminControllerV2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageControllerV2.class);

    public static final String BCI_ADMIN_BASE_URL = "/api/logical/bci/v2/admin";
    public static final String USER_VERSION = "/userVersion/clear";

    @Autowired
    private VersionCache versionCache;

    @RequestMapping(value = USER_VERSION, method = RequestMethod.POST)
    @ApiOperation(value = "清理版本缓存")
    public void clearVersionCache() {
        try {
            versionCache.clearVersionCache();
            LOGGER.debug("clear version cache success");
        } catch (Exception e) {
            LOGGER.error("clear version cache error:{}", e.getMessage());
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    @RequestMapping(value = USER_VERSION + "/{userId}", method = RequestMethod.POST)
    @ApiOperation(value = "清理某个用户版本缓存")
    public void clearUserVersionCache(@PathVariable String userId) {
        try {
            versionCache.clearUserVersionCache(userId);
            LOGGER.debug("clear user {} version cache success.", userId);
        } catch (Exception e) {
            LOGGER.error("clear user {} version cache error:{}", userId, e.getMessage());
            throw new CommonExceptions.InternalServerErrorException();
        }
    }
}
