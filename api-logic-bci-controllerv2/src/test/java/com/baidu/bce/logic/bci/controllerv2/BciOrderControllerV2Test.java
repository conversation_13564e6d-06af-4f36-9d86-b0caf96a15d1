package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.reservedinstance.ReservedInstanceService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import com.baidu.bce.logic.bci.servicev2.model.OrderRenewRequest;
import com.baidu.bce.logic.bci.servicev2.model.OrderRenewResponse;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateRenewTypeOrderItem;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class BciOrderControllerV2Test {
    @InjectMocks
    private BciOrderControllerV2 controller = new BciOrderControllerV2();

    @Mock
    private PodServiceV2 podService;

    @Mock
    protected RegionConfiguration regionConfiguration;

    @Mock
    private ReservedInstanceDao reservedInstanceDao;

    @Mock
    private ReservedInstanceService reservedInstanceService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testRenew() {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ReservedInstancePO reservedInstancePO = new ReservedInstancePO();
            reservedInstancePO.setEffectiveTime(new Timestamp(format.parse("2023-05-01 00:00:00").getTime()));
            reservedInstancePO.setExpireTime(new Timestamp(format.parse("2023-06-01 00:00:00").getTime()));
            reservedInstancePO.setPhysicalZone("gz");
            reservedInstancePO.setReservedInstanceCount(1);
            reservedInstancePO.setReservedSpec("***");
            CreateReservedInstanceRequest requestCheck = new CreateReservedInstanceRequest();
            requestCheck.setEffectiveTime(new Timestamp(format.parse("2023-05-01 00:00:00").getTime()));
            requestCheck.setRenewDeadline("2023-07-01 00:00:00");
            requestCheck.setPhysicalZone("gz");
            requestCheck.setReservedInstanceCount(1);
            requestCheck.setReservedSpec("***");

            OrderUuidResult orderUuidResult = new OrderUuidResult();
            orderUuidResult.setOrderId("orderid");
            Mockito.when(podService.submitCreateOrderToServiceCatalog(
                    Matchers.<CreateOrderRequest<CreateRenewTypeOrderItem>>anyObject(),
                    Matchers.<String>anyObject())).thenReturn(orderUuidResult);
            Mockito.when(regionConfiguration.getCurrentRegion()).thenReturn("bj");
            Mockito.when(reservedInstanceDao.
                    getReservedInstanceByResourceUuid("1")).thenReturn(reservedInstancePO);
            Mockito.when(reservedInstanceService.checkSupplyAndDemand(requestCheck,"")).thenReturn(true);
            OrderRenewRequest req = new OrderRenewRequest();
            List<OrderRenewRequest.OrderRenewRequestItem> items = new ArrayList<>();
            OrderRenewRequest.OrderRenewRequestItem item = new OrderRenewRequest.OrderRenewRequestItem();
            item.setConfig(new OrderRenewRequest.OrderRenewRequestItemConfig());
            items.add(item);
            req.setItems(items);
            req.getItems().get(0).getConfig().setUuid("1");
            req.getItems().get(0).getConfig().setTimeUnit("month");
            req.getItems().get(0).getConfig().setDuration(1);
            OrderRenewResponse resp = controller.renew(req);

            reservedInstancePO.setExpireTime(new Timestamp(System.currentTimeMillis()-1000*3600*24));
            Mockito.when(reservedInstanceDao.
                    getReservedInstanceByResourceUuid("2")).thenReturn(reservedInstancePO);
            req.getItems().get(0).getConfig().setUuid("2");
            resp = controller.renew(req);
            
            reservedInstancePO.setEffectiveTime(new Timestamp(System.currentTimeMillis()+1000*3600*24));
            reservedInstancePO.setExpireTime(new Timestamp(System.currentTimeMillis()+1000*3600*24*7));
            Mockito.when(reservedInstanceDao.
                    getReservedInstanceByResourceUuid("3")).thenReturn(reservedInstancePO);
            req.getItems().get(0).getConfig().setUuid("3");
            resp = controller.renew(req);

        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}
