package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.UserVersion;
import com.baidu.bce.logic.bci.servicev2.model.PodDetail;
import com.baidu.bce.logic.bci.servicev2.model.DescribeContainerGroupDetailRequestExtra;
import com.baidu.bce.logic.bci.servicev2.model.BciCreateResponse;
import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupRequestExtra;
import com.baidu.bce.logic.bci.servicev2.model.IOrderItem;
import com.baidu.bce.logic.bci.servicev2.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.servicev2.model.ContainerPurchase;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache.Version;
import com.baidu.bce.logic.bci.servicev2.util.PrometheusMetricsService;
import com.baidu.bce.logic.bci.servicev2.model.InstanceModel;
import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.core.exception.CommonExceptions.InternalServerErrorException;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BciControllerTest {

    @InjectMocks
    private BciControllerV2 bciController = new BciControllerV2();

    //    @Mock
//    private com.baidu.bce.logic.bci.controller.BciController bciControllerv1;
    @Mock
    private PodServiceV2 podService;

    @Mock
    private VersionCache versionCache;

    @Mock
    private PrometheusMetricsService prometheusMetricsService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testIsV2() {
        // mock
        when(podService.getAccountId()).thenReturn("user");
        when(versionCache.getUserVersion("user")).thenReturn(Version.V2);
        try {
            UserVersion version = bciController.isV2();
            assertEquals(version.isIsv2(), true);
        } catch (Exception e) {
            assertEquals(false, true);
            assertEquals(e.getMessage(), "");
        }

        when(versionCache.getUserVersion("user")).thenReturn(Version.V1);
        try {
            UserVersion version = bciController.isV2();
            assertEquals(version.isIsv2(), false);
        } catch (Exception e) {
            assertEquals(false, true);
            assertEquals(e.getMessage(), "");
        }

        when(versionCache.getUserVersion("user")).thenReturn(Version.ABNORMAL);
        try {
            UserVersion version = bciController.isV2();
            assertEquals(true, false);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "Internal service occurs error.");
        }
    }

    @Test
    public void listPodByPageTest() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {

//        LogicPageResultResponse<com.baidu.bce.logic.bci.dao.pod.model.PodPO> podPOsV1 =
//                new LogicPageResultResponse<>();
//        podPOsV1.setTotalCount(1);
//        podPOsV1.setResult(createPodPOV1());
//
//        Mockito.when(bciControllerv1.listPodByPage("cceid", "keyword", "keywordType",
//                "order", "orderby", 1, 10, "")).thenReturn(podPOsV1);

        LogicPageResultResponse<PodPO> podPOsV2 = new LogicPageResultResponse<>();
        podPOsV2.setTotalCount(1);
        podPOsV2.setResult(createPodPOV2());
        Mockito.when(podService.listPodsWithPageByMultiKey(Matchers.<PodListRequest>anyObject())).thenReturn(podPOsV2);


        LogicPageResultResponse<PodPO> res = bciController.listPodByPage("cceid", "keyword", "keywordType",
                "order", "orderby", 1, 10, "");

        assertEquals(res.getResult().size(), 1);
        assertEquals(res.getResult().iterator().next().isV2(), true);
        assertEquals(res.getResult().iterator().next().getName(), "podv2");
    }

    @Test
    public void listPodByMarkerTest() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        LogicMarkerResultResponse<PodPO> podPOsV2 = new LogicMarkerResultResponse<>();
        podPOsV2.setResult(createPodPOV2());
        Mockito.when(podService.listPodsWithMarkerByMultiKey(
                Matchers.<PodListRequest>anyObject())).thenReturn(podPOsV2);


        LogicMarkerResultResponse<PodPO> res = bciController.listPodByMarker("", "", "",
                "", "", "", -10, "");

        assertEquals(res.getResult().size(), 1);
        assertEquals(res.getResult().iterator().next().isV2(), true);
        assertEquals(res.getResult().iterator().next().getName(), "podv2");

        try {
            LogicMarkerResultResponse<PodPO> res1 = bciController.listPodByMarker("", "", "",
                "", "", "", -10, "test");
            assertEquals(true, false);
        } catch (InternalServerErrorException e) {
            assertEquals(true, true);
        }

        try {
            Mockito.when(podService.listPodsWithMarkerByMultiKey(
                Matchers.<PodListRequest>anyObject())).thenThrow(new BceException(""));
            LogicMarkerResultResponse<PodPO> res1 = bciController.listPodByMarker("", "", "",
                    "", "", "", -10, "");
            assertEquals(true, false);
        } catch (BceException e) {
            assertEquals(true, true);
        }
    }

    @Test
    public void listPodByUpdatedTimeTest() {

//        LogicPageResultResponse<com.baidu.bce.logic.bci.dao.pod.model.PodPO> podPOsV1 =
//                new LogicPageResultResponse<>();
//        podPOsV1.setTotalCount(1);
//        podPOsV1.setResult(createPodPOV1());
//
//        Mockito.when(bciControllerv1.listPodByPage("cceid", "keyword", "keywordType",
//                "order", "orderby", 1, 10, "")).thenReturn(podPOsV1);

        LogicPageResultResponse<PodPO> podPOsV2 = new LogicPageResultResponse<>();
        podPOsV2.setTotalCount(1);
        podPOsV2.setResult(createPodPOV2());
        Mockito.when(podService.listPodsByUpdatedTime(" ", " ", 1)).thenReturn(podPOsV2);


        LogicPageResultResponse<PodPO> res = bciController.listPodByUpdatedTime(1, " ", " ");

        assertEquals(res.getResult().size(), 1);
        assertEquals(res.getResult().iterator().next().isV2(), true);
        assertEquals(res.getResult().iterator().next().getName(), "podv2");
    }

    @Test
    public void testPodDetailWithDeleted() {
        // Test case 1: Normal case - should return PodDetail successfully
        String podId = "test-pod-id";
        String from = "api";
        PodDetail mockPodDetail = createMockPodDetail();
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenReturn(mockPodDetail);
        
        PodDetail result = bciController.podDetailWithDeleted(podId, from);
        
        assertNotNull(result);
        assertEquals(mockPodDetail.getPodId(), result.getPodId());
        assertEquals(mockPodDetail.getName(), result.getName());
    }

    @Test
    public void testPodDetailWithDeletedWithBceException() {
        // Test case 2: BceException should be handled
        String podId = "test-pod-id";
        String from = "api";
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenThrow(new BceException("Service unavailable"));
        
        try {
            bciController.podDetailWithDeleted(podId, from);
            assertEquals(true, false); // Should not reach here
        } catch (BceException e) {
            assertEquals(true, true); // Expected exception
        }
    }

    @Test
    public void testPodDetailWithDeletedWithGeneralException() {
        // Test case 3: General Exception should be handled
        String podId = "test-pod-id";
        String from = "api";
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenThrow(new RuntimeException("Internal error"));
        
        try {
            bciController.podDetailWithDeleted(podId, from);
            assertEquals(true, false); // Should not reach here
        } catch (Exception e) {
            assertEquals(true, true); // Expected exception
        }
    }

    @Test
    public void testPodDetailWithDeletedWithNullReturn() {
        // Test case 4: Service returns null
        String podId = "test-pod-id";
        String from = "api";
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenReturn(null);
        
        PodDetail result = bciController.podDetailWithDeleted(podId, from);
        
        assertNull(result);
    }

    @Test
    public void createInstanceTest() {
        testCreateInstanceSuccess();
        testCreateInstanceBceException();
        testCreateInstanceGeneralException();
        //testCreateInstanceWithDefaults();
        testCreateInstanceNullRequest();
    }

    private void testCreateInstanceSuccess() {
        // Test case 1: Successful creation
        BaseCreateOrderRequestVo<IOrderItem> request = createValidCreateInstanceRequest();
        String from = "api";
        String clientToken = "test-client-token";

        BciCreateResponse mockResponse = createMockBciCreateResponse();
        when(podService.getAccountId()).thenReturn("test-account-id");
        when(podService.createPod(Matchers.any(BaseCreateOrderRequestVo.class),
                                  Matchers.any(CreateContainerGroupRequestExtra.class)))
                .thenReturn(mockResponse);

        BciCreateResponse result = bciController.createInstance(request, from, clientToken);

        assertNotNull(result);
        assertEquals(mockResponse.getOrderId(), result.getOrderId());
        assertEquals(mockResponse.getPodIds(), result.getPodIds());
    }

    private void testCreateInstanceBceException() {
        // Test case 2: BceException handling
        BaseCreateOrderRequestVo<IOrderItem> request = createValidCreateInstanceRequest();
        String from = "api";
        String clientToken = "test-client-token";

        when(podService.getAccountId()).thenReturn("test-account-id");
        when(podService.createPod(Matchers.any(BaseCreateOrderRequestVo.class),
                                  Matchers.any(CreateContainerGroupRequestExtra.class)))
                .thenThrow(new BceException("Service error"));

        try {
            bciController.createInstance(request, from, clientToken);
            assertEquals("Should have thrown BceException", true, false);
        } catch (BceException e) {
            assertEquals("Service error", e.getMessage());
        }
    }

    private void testCreateInstanceGeneralException() {
        // Test case 3: General Exception handling - should be converted to InternalServerErrorException
        BaseCreateOrderRequestVo<IOrderItem> request = createValidCreateInstanceRequest();
        String from = "api";
        String clientToken = "test-client-token";

        when(podService.getAccountId()).thenReturn("test-account-id");
        when(podService.createPod(Matchers.any(BaseCreateOrderRequestVo.class),
                                  Matchers.any(CreateContainerGroupRequestExtra.class)))
                .thenThrow(new RuntimeException("Internal error"));

        try {
            bciController.createInstance(request, from, clientToken);
            assertEquals("Should have thrown InternalServerErrorException", true, false);
        } catch (InternalServerErrorException e) {
            // Expected - LogicPodExceptionHandler converts RuntimeException to InternalServerErrorException
            assertEquals(true, true);
        } catch (Exception e) {
            assertEquals("Unexpected exception type: " + e.getClass().getName(), true, false);
        }
    }

    private void testCreateInstanceWithDefaults() {
        // Test case 4: Default parameters
        BaseCreateOrderRequestVo<IOrderItem> request = createValidCreateInstanceRequest();
        BciCreateResponse mockResponse = createMockBciCreateResponse();

        when(podService.getAccountId()).thenReturn("test-account-id");
        when(podService.createPod(Matchers.any(BaseCreateOrderRequestVo.class),
                                  Matchers.any(CreateContainerGroupRequestExtra.class)))
                .thenReturn(mockResponse);

        BciCreateResponse resultWithDefaults = bciController.createInstance(request, "", "");

        assertNotNull(resultWithDefaults);
        assertEquals(mockResponse.getOrderId(), resultWithDefaults.getOrderId());
    }

    private void testCreateInstanceNullRequest() {
        // Test case 5: Null request handling
        String from = "api";
        String clientToken = "test-client-token";

        when(podService.getAccountId()).thenReturn("test-account-id");

        try {
            bciController.createInstance(null, from, clientToken);
            assertEquals("Should have thrown exception for null request", true, false);
        } catch (Exception e) {
            // Expected - null request should cause an exception
            assertEquals(true, true);
        }
    }

    @Test
    public void testPodDetailWithDeletedWithDefaultFrom() {
        // Test case 5: Test with default from parameter
        String podId = "test-pod-id";
        PodDetail mockPodDetail = createMockPodDetail();
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenReturn(mockPodDetail);
        
        PodDetail result = bciController.podDetailWithDeleted(podId, "api");
        
        assertNotNull(result);
        assertEquals(mockPodDetail.getPodId(), result.getPodId());
    }

    public List<com.baidu.bce.logic.bci.daov2.pod.model.PodPO> createPodPOV1() {
        com.baidu.bce.logic.bci.daov2.pod.model.PodPO podv1 = new com.baidu.bce.logic.bci.daov2.pod.model.PodPO();
        podv1.setName("podv1");
        List<com.baidu.bce.logic.bci.daov2.pod.model.PodPO> ans = new ArrayList<>();
        ans.add(podv1);
        return ans;

    }

    public List<PodPO> createPodPOV2() {
        PodPO podv2 = new PodPO();
        podv2.setV2(true);
        podv2.setName("podv2");
        List<PodPO> ans = new ArrayList<PodPO>();
        ans.add(podv2);
        return ans;
    }

    public List<InstanceModel> createInstanceModelV2() {
        InstanceModel podv2 = new InstanceModel();
        podv2.setInstanceName("podv2");
        List<InstanceModel> ans = new ArrayList<InstanceModel>();
        ans.add(podv2);
        return ans;
    }

    private PodDetail createMockPodDetail() {
        PodDetail podDetail = new PodDetail();
        podDetail.setPodId("test-pod-id");
        podDetail.setName("test-pod-name");
        podDetail.setStatus("running");
        podDetail.setCpu(2.0f);
        podDetail.setMemory(4.0f);
        podDetail.setUserId("test-user");
        podDetail.setCreatedTime(new Timestamp(System.currentTimeMillis()));
        podDetail.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
        return podDetail;
    }

    private BaseCreateOrderRequestVo<IOrderItem> createValidCreateInstanceRequest() {
        BaseCreateOrderRequestVo<IOrderItem> request = new BaseCreateOrderRequestVo<>();

        // Create PodPurchaseRequest
        PodPurchaseRequest podPurchaseRequest = new PodPurchaseRequest();
        podPurchaseRequest.setName("test-pod");
        podPurchaseRequest.setServiceType("BCI");
        podPurchaseRequest.setProductType("postpay");
        podPurchaseRequest.setSubProductType("");
        podPurchaseRequest.setRestartPolicy("Always");
        podPurchaseRequest.setCceId("test-cce-id");
        podPurchaseRequest.setVpcId("test-vpc-id");
        podPurchaseRequest.setSubnetId("test-subnet-id");
        podPurchaseRequest.setZoneId("test-zone");

        // Create container
        ContainerPurchase container = new ContainerPurchase();
        container.setName("test-container");
        container.setImageAddress("nginx:latest");
        container.setCpu(1.0f);
        container.setMemory(1.0f);

        List<ContainerPurchase> containers = new ArrayList<>();
        containers.add(container);
        podPurchaseRequest.setContainerPurchases(containers);

        // Create request item
        BaseCreateOrderRequestVo.Item<IOrderItem> item = new BaseCreateOrderRequestVo.Item<>();
        item.setConfig(podPurchaseRequest);

        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = new ArrayList<>();
        items.add(item);
        request.setItems(items);

        return request;
    }

    private BciCreateResponse createMockBciCreateResponse() {
        BciCreateResponse response = new BciCreateResponse();
        response.setOrderId("test-order-id");

        List<String> podIds = new ArrayList<>();
        podIds.add("test-pod-id-1");
        podIds.add("test-pod-id-2");
        response.setPodIds(podIds);

        return response;
    }
}