package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.model.UserVersion;
import com.baidu.bce.logic.bci.servicev2.model.PodDetail;
import com.baidu.bce.logic.bci.servicev2.model.DescribeContainerGroupDetailRequestExtra;
import com.baidu.bce.logic.bci.servicev2.pod.PodServiceV2;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache;
import com.baidu.bce.logic.bci.servicev2.util.userversioncheck.VersionCache.Version;
import com.baidu.bce.logic.bci.servicev2.model.InstanceModel;
import com.baidu.bce.logic.bci.servicev2.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.core.exception.CommonExceptions.InternalServerErrorException;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.plat.webframework.exception.BceException;

import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;


public class BciControllerTest {

    @InjectMocks
    private BciControllerV2 bciController = new BciControllerV2();

    //    @Mock
//    private com.baidu.bce.logic.bci.controller.BciController bciControllerv1;
    @Mock
    private PodServiceV2 podService;

    @Mock
    private VersionCache versionCache;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testIsV2() {
        // mock
        when(podService.getAccountId()).thenReturn("user");
        when(versionCache.getUserVersion("user")).thenReturn(Version.V2);
        try {
            UserVersion version = bciController.isV2();
            assertEquals(version.isIsv2(), true);
        } catch (Exception e) {
            assertEquals(false, true);
            assertEquals(e.getMessage(), "");
        }

        when(versionCache.getUserVersion("user")).thenReturn(Version.V1);
        try {
            UserVersion version = bciController.isV2();
            assertEquals(version.isIsv2(), false);
        } catch (Exception e) {
            assertEquals(false, true);
            assertEquals(e.getMessage(), "");
        }

        when(versionCache.getUserVersion("user")).thenReturn(Version.ABNORMAL);
        try {
            UserVersion version = bciController.isV2();
            assertEquals(true, false);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "Internal service occurs error.");
        }
    }

    @Test
    public void listPodByPageTest() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {

//        LogicPageResultResponse<com.baidu.bce.logic.bci.dao.pod.model.PodPO> podPOsV1 =
//                new LogicPageResultResponse<>();
//        podPOsV1.setTotalCount(1);
//        podPOsV1.setResult(createPodPOV1());
//
//        Mockito.when(bciControllerv1.listPodByPage("cceid", "keyword", "keywordType",
//                "order", "orderby", 1, 10, "")).thenReturn(podPOsV1);

        LogicPageResultResponse<PodPO> podPOsV2 = new LogicPageResultResponse<>();
        podPOsV2.setTotalCount(1);
        podPOsV2.setResult(createPodPOV2());
        Mockito.when(podService.listPodsWithPageByMultiKey(Matchers.<PodListRequest>anyObject())).thenReturn(podPOsV2);


        LogicPageResultResponse<PodPO> res = bciController.listPodByPage("cceid", "keyword", "keywordType",
                "order", "orderby", 1, 10, "");

        assertEquals(res.getResult().size(), 1);
        assertEquals(res.getResult().iterator().next().isV2(), true);
        assertEquals(res.getResult().iterator().next().getName(), "podv2");
    }

    @Test
    public void listPodByMarkerTest() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        LogicMarkerResultResponse<PodPO> podPOsV2 = new LogicMarkerResultResponse<>();
        podPOsV2.setResult(createPodPOV2());
        Mockito.when(podService.listPodsWithMarkerByMultiKey(
                Matchers.<PodListRequest>anyObject())).thenReturn(podPOsV2);


        LogicMarkerResultResponse<PodPO> res = bciController.listPodByMarker("", "", "",
                "", "", "", -10, "");

        assertEquals(res.getResult().size(), 1);
        assertEquals(res.getResult().iterator().next().isV2(), true);
        assertEquals(res.getResult().iterator().next().getName(), "podv2");

        try {
            LogicMarkerResultResponse<PodPO> res1 = bciController.listPodByMarker("", "", "",
                "", "", "", -10, "test");
            assertEquals(true, false);
        } catch (InternalServerErrorException e) {
            assertEquals(true, true);
        }

        try {
            Mockito.when(podService.listPodsWithMarkerByMultiKey(
                Matchers.<PodListRequest>anyObject())).thenThrow(new BceException(""));
            LogicMarkerResultResponse<PodPO> res1 = bciController.listPodByMarker("", "", "",
                    "", "", "", -10, "");
            assertEquals(true, false);
        } catch (BceException e) {
            assertEquals(true, true);
        }
    }

    @Test
    public void listPodByUpdatedTimeTest() {

//        LogicPageResultResponse<com.baidu.bce.logic.bci.dao.pod.model.PodPO> podPOsV1 =
//                new LogicPageResultResponse<>();
//        podPOsV1.setTotalCount(1);
//        podPOsV1.setResult(createPodPOV1());
//
//        Mockito.when(bciControllerv1.listPodByPage("cceid", "keyword", "keywordType",
//                "order", "orderby", 1, 10, "")).thenReturn(podPOsV1);

        LogicPageResultResponse<PodPO> podPOsV2 = new LogicPageResultResponse<>();
        podPOsV2.setTotalCount(1);
        podPOsV2.setResult(createPodPOV2());
        Mockito.when(podService.listPodsByUpdatedTime(" ", " ", 1)).thenReturn(podPOsV2);


        LogicPageResultResponse<PodPO> res = bciController.listPodByUpdatedTime(1, " ", " ");

        assertEquals(res.getResult().size(), 1);
        assertEquals(res.getResult().iterator().next().isV2(), true);
        assertEquals(res.getResult().iterator().next().getName(), "podv2");
    }

    @Test
    public void testPodDetailWithDeleted() {
        // Test case 1: Normal case - should return PodDetail successfully
        String podId = "test-pod-id";
        String from = "api";
        PodDetail mockPodDetail = createMockPodDetail();
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenReturn(mockPodDetail);
        
        PodDetail result = bciController.podDetailWithDeleted(podId, from);
        
        assertNotNull(result);
        assertEquals(mockPodDetail.getPodId(), result.getPodId());
        assertEquals(mockPodDetail.getName(), result.getName());
    }

    @Test
    public void testPodDetailWithDeletedWithBceException() {
        // Test case 2: BceException should be handled
        String podId = "test-pod-id";
        String from = "api";
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenThrow(new BceException("Service unavailable"));
        
        try {
            bciController.podDetailWithDeleted(podId, from);
            assertEquals(true, false); // Should not reach here
        } catch (BceException e) {
            assertEquals(true, true); // Expected exception
        }
    }

    @Test
    public void testPodDetailWithDeletedWithGeneralException() {
        // Test case 3: General Exception should be handled
        String podId = "test-pod-id";
        String from = "api";
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenThrow(new RuntimeException("Internal error"));
        
        try {
            bciController.podDetailWithDeleted(podId, from);
            assertEquals(true, false); // Should not reach here
        } catch (Exception e) {
            assertEquals(true, true); // Expected exception
        }
    }

    @Test
    public void testPodDetailWithDeletedWithNullReturn() {
        // Test case 4: Service returns null
        String podId = "test-pod-id";
        String from = "api";
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenReturn(null);
        
        PodDetail result = bciController.podDetailWithDeleted(podId, from);
        
        assertNull(result);
    }

    @Test
    public void testPodDetailWithDeletedWithDefaultFrom() {
        // Test case 5: Test with default from parameter
        String podId = "test-pod-id";
        PodDetail mockPodDetail = createMockPodDetail();
        
        when(podService.podDetail(Matchers.eq(podId), Matchers.any(DescribeContainerGroupDetailRequestExtra.class)))
                .thenReturn(mockPodDetail);
        
        PodDetail result = bciController.podDetailWithDeleted(podId, "api");
        
        assertNotNull(result);
        assertEquals(mockPodDetail.getPodId(), result.getPodId());
    }

    public List<com.baidu.bce.logic.bci.daov2.pod.model.PodPO> createPodPOV1() {
        com.baidu.bce.logic.bci.daov2.pod.model.PodPO podv1 = new com.baidu.bce.logic.bci.daov2.pod.model.PodPO();
        podv1.setName("podv1");
        List<com.baidu.bce.logic.bci.daov2.pod.model.PodPO> ans = new ArrayList<>();
        ans.add(podv1);
        return ans;

    }

    public List<PodPO> createPodPOV2() {
        PodPO podv2 = new PodPO();
        podv2.setV2(true);
        podv2.setName("podv2");
        List<PodPO> ans = new ArrayList<PodPO>();
        ans.add(podv2);
        return ans;
    }

    public List<InstanceModel> createInstanceModelV2() {
        InstanceModel podv2 = new InstanceModel();
        podv2.setInstanceName("podv2");
        List<InstanceModel> ans = new ArrayList<InstanceModel>();
        ans.add(podv2);
        return ans;
    }

    private PodDetail createMockPodDetail() {
        PodDetail podDetail = new PodDetail();
        podDetail.setPodId("test-pod-id");
        podDetail.setName("test-pod-name");
        podDetail.setStatus("running");
        podDetail.setCpu(2.0f);
        podDetail.setMemory(4.0f);
        podDetail.setUserId("test-user");
        podDetail.setCreatedTime(new Timestamp(System.currentTimeMillis()));
        podDetail.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
        return podDetail;
    }
}