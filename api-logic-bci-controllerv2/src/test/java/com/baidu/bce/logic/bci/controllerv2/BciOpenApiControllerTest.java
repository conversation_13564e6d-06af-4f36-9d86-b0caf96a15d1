package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.servicev2.exception.PodExceptions;
import com.baidu.bce.logic.bci.servicev2.model.CreateContainerGroupRequest;
import com.baidu.bce.logic.bci.servicev2.model.ImageRegistryCredential;
import com.baidu.bce.logic.core.utils.UUIDUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class BciOpenApiControllerTest {
    @Test
    public void createContainerGroupSecurityGroupTest() {
        BciOpenApiController bciOpenApiController = new BciOpenApiController();
        String clientToken = "";
        CreateContainerGroupRequest request = new CreateContainerGroupRequest();
        request.setName("test");
        request.setImageRegistryCredentials(new ArrayList<ImageRegistryCredential>());
        try {
            request.setSecurityGroupIds(null);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups should be specified.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups should be specified.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            securityGroupIds.add("");
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups is invalid.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            securityGroupIds.add(" ");
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups is invalid.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            securityGroupIds.add("sg-test");
            securityGroupIds.add("");
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups is invalid.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            securityGroupIds.add("sg-test");
            securityGroupIds.add("");
            securityGroupIds.add("");
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups is invalid.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            securityGroupIds.add("sg-test");
            securityGroupIds.add("");
            securityGroupIds.add("sg-test");
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups is invalid.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            securityGroupIds.add("");
            securityGroupIds.add("sg-test");
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups is invalid.", e.getMessage());
        }
        try {
            List<String> securityGroupIds = new ArrayList<>();
            securityGroupIds.add("");
            securityGroupIds.add("");
            securityGroupIds.add("sg-test");
            request.setSecurityGroupIds(securityGroupIds);
            bciOpenApiController.createContainerGroup(request, clientToken);
            Assert.assertTrue(false);
        } catch (PodExceptions.SecurityGroupInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.SecurityGroupInvalid", e.getCode());
            Assert.assertEquals("The security groups is invalid.", e.getMessage());
        }
    }

    @Test
    public void createContainerGroupGpuTypeTest() {
        BciOpenApiController bciOpenApiController = new BciOpenApiController();
        CreateContainerGroupRequest request = new CreateContainerGroupRequest();
        request.setName("GpuTypeTest");
        List<String> securityGroupIds = new ArrayList<>();
        securityGroupIds.add("securityGroupIds");
        request.setSecurityGroupIds(securityGroupIds);
        request.setImageRegistryCredentials(new ArrayList<ImageRegistryCredential>());

        try {
            request.setGpuType(null);
            bciOpenApiController.createContainerGroup(request, UUIDUtil.generateShortUuid());
            Assert.assertTrue(false);
        } catch (Exception e) {
            Assert.assertTrue(true);
//            Assert.assertEquals("Internal service occurs error.", e.getMessage());
        }

        try {
            request.setGpuType("");
            bciOpenApiController.createContainerGroup(request, UUIDUtil.generateShortUuid());
            Assert.assertTrue(false);
        } catch (Exception e) {
            Assert.assertTrue(true);
//            Assert.assertEquals("Internal service occurs error.", e.getMessage());
        }

        try {
            request.setGpuType(" ");
            bciOpenApiController.createContainerGroup(request, UUIDUtil.generateShortUuid());
            Assert.assertTrue(false);
        } catch (PodExceptions.GPUTypeInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.GPUTypeInvalid", e.getCode());
            Assert.assertEquals("GpuType is invalid. Only 'Nvidia A10 PCIE/Nvidia RTX 4090' is supported.", e.getMessage());
        }

        try {
            request.setGpuType("Nvidia A10 PCIEx");
            bciOpenApiController.createContainerGroup(request, UUIDUtil.generateShortUuid());
            Assert.assertTrue(false);
        } catch (PodExceptions.GPUTypeInvalidException e) {
            Assert.assertTrue(true);
            Assert.assertEquals("Pod.GPUTypeInvalid", e.getCode());
            Assert.assertEquals("GpuType is invalid. Only 'Nvidia A10 PCIE/Nvidia RTX 4090' is supported.", e.getMessage());
        }
    }
}