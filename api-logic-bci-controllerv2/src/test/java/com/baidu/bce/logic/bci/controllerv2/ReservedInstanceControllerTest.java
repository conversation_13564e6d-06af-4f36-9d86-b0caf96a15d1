package com.baidu.bce.logic.bci.controllerv2;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstanceSpec;
import com.baidu.bce.logic.bci.servicev2.model.PodListRequest;
import com.baidu.bce.logic.bci.servicev2.reservedinstance.ReservedInstanceService;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceRequest;
import com.baidu.bce.logic.bci.servicev2.model.CreateReservedInstanceResponse;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;

import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class ReservedInstanceControllerTest {
    @InjectMocks
    private ReservedInstanceController controller = new ReservedInstanceController();

    @Mock
    private ReservedInstanceService reservedInstanceService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testListReservedInstanceByPage() {
        LogicPageResultResponse<ReservedInstancePO> resp = new LogicPageResultResponse<>();
        ReservedInstancePO reservedInstancePO = new ReservedInstancePO();
        reservedInstancePO.setId(1);
        List<ReservedInstancePO> reservedInstancePOList = new ArrayList<>();
        reservedInstancePOList.add(reservedInstancePO);
        resp.setTotalCount(1);
        resp.setResult(reservedInstancePOList);
        Mockito.when(reservedInstanceService.listReservedInstancesWithPageByMultiKey(
                Matchers.<ListRequest>anyObject())).thenReturn(resp);
        LogicPageResultResponse<ReservedInstancePO> res = controller.listReservedInstanceByPage(
                "desc", "createdTime", 1, 10, "r-zzoh7r1m", "reservedInstanceId", "[{\"keywordType\":\"reservedInstanceId\", \"keyword\": \"r-zzoh7r1m\"}]");
        assertEquals(res.getResult().size(), 1);
    }

    @Test
    public void testCreateReservedInstance() {
        CreateReservedInstanceResponse resp = new CreateReservedInstanceResponse();
        List<String> reservedInstanceIds = new ArrayList<>();
        reservedInstanceIds.add("r-test");
        resp.setReservedInstanceId(reservedInstanceIds);
        resp.setOrderId("orderid");
        Mockito.when(reservedInstanceService.createReservedInstance(
                Matchers.<BaseCreateOrderRequestVo<CreateReservedInstanceRequest>>any())).thenReturn(resp);
        BaseCreateOrderRequestVo<CreateReservedInstanceRequest> baseCreateOrderRequestVo = 
                new BaseCreateOrderRequestVo<>();
        BaseCreateOrderRequestVo.Item<CreateReservedInstanceRequest> baseCreateOrderRequestVoItem = 
                new BaseCreateOrderRequestVo.Item<>();
        CreateReservedInstanceRequest createReservedInstanceRequest = new CreateReservedInstanceRequest();
        createReservedInstanceRequest.setEffectiveTime(null);
        baseCreateOrderRequestVoItem.setConfig(createReservedInstanceRequest);
        List<BaseCreateOrderRequestVo.Item<CreateReservedInstanceRequest>> reqList = new ArrayList<>();
        reqList.add(baseCreateOrderRequestVoItem);
        baseCreateOrderRequestVo.setItems(reqList);
        CreateReservedInstanceResponse res = controller.createReservedInstance(baseCreateOrderRequestVo, "");
        assertEquals(res.getOrderId(), "orderid");
    }

    @Test
    public void testListReservedInstanceSpecs() {
        LogicPageResultResponse<ReservedInstanceSpec> resp = new LogicPageResultResponse<>();
        ReservedInstanceSpec reservedInstanceSpec = new ReservedInstanceSpec();
        reservedInstanceSpec.setId(1);
        List<ReservedInstanceSpec> reservedInstanceSpecs = new ArrayList<>();
        reservedInstanceSpecs.add(reservedInstanceSpec);
        resp.setTotalCount(1);
        resp.setResult(reservedInstanceSpecs);
        Mockito.when(reservedInstanceService.listReservedInstanceSpecs()).thenReturn(resp);
        LogicPageResultResponse<ReservedInstanceSpec> res = controller.listReservedInstanceSpecs();
        assertEquals(res.getResult().size(), 1);
    }

    @Test
    public void testListPodByReservedInstanceId() {
        PodListRequest listRequest =
                new PodListRequest("", "", "", "", 1, 10, "");
        LogicPageResultResponse<PodPO> podPOsV2 = new LogicPageResultResponse<>();
        podPOsV2.setTotalCount(1);
        podPOsV2.setResult(createPodPOV2());
        Mockito.when(reservedInstanceService.getPodByReservedInstanceId("aaa",
                listRequest)).thenReturn(podPOsV2);
        LogicPageResultResponse<PodPO> res = controller.listPodByReservedInstanceId(
                "", "", 1, 10, "aaa");

        assertEquals(res.getResult().size(), 1);
        assertEquals(res.getResult().iterator().next().isV2(), true);
        assertEquals(res.getResult().iterator().next().getName(), "podv2");
    }

    public List<PodPO> createPodPOV2() {
        PodPO podv2 = new PodPO();
        podv2.setV2(true);
        podv2.setName("podv2");
        List<PodPO> ans = new ArrayList<PodPO>();
        ans.add(podv2);
        return ans;
    }
}
