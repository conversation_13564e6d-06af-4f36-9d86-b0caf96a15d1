#!/bin/bash

echo "Starting test execution..."
cd "$(dirname "$0")"

echo "Compiling test classes..."
mvn clean test-compile

echo "Running createInstanceTest..."
mvn test -Dtest=BciControllerTest#createInstanceTest -Dmaven.test.failure.ignore=true

echo "Checking test results..."
if [ -d "target/surefire-reports" ]; then
    echo "Test reports found:"
    ls -la target/surefire-reports/
    echo "Test results:"
    cat target/surefire-reports/TEST-*.xml 2>/dev/null || echo "No XML reports found"
else
    echo "No surefire-reports directory found"
fi

echo "Test execution completed."
