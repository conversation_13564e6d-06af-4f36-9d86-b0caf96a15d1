package com.baidu.bce.logic.bci.sdk.model.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IDListRequest {

    /**
     * id列表
     */
    @NotEmpty(message = "ID列表不能为空")
    private List<String> ids;

    /**
     * 请求来源[api,console]
     */
    private String from;

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public IDListRequest withIds(List<String> ids) {
        this.ids = ids;
        return this;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "IDListRequest{"
                + "ids=" + ids
                + ", from='" + from + '\''
                + '}';
    }
}