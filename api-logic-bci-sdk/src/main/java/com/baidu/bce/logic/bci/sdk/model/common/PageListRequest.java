package com.baidu.bce.logic.bci.sdk.model.common;

import java.util.List;

/**
 *
 * 通用的Page方式数据列表请求
 */
public class PageListRequest {

    /**
     * 关键字类型
     */
    protected String keywordType;

    /**
     * 子关键字类型
     */
    protected String subKeywordType;

    /**
     * 关键字
     */
    protected String keyword;

    /**
     * 页码
     */
    protected int pageNo = 1;

    /**
     * 每页数据项数目
     */
    protected int pageSize = 100;

    /**
     * 排序方式，升序或降序
     */
    protected String order;

    /**
     * 按某列排序
     */
    protected String orderBy;

    /**
     * 过滤字段list
     */
    private List<Filter> filters;

    public String getKeywordType() {
        return keywordType;
    }

    public void setKeywordType(String keywordType) {
        this.keywordType = keywordType;
    }

    public String getSubKeywordType() {
        return subKeywordType;
    }

    public void setSubKeywordType(String subKeywordType) {
        this.subKeywordType = subKeywordType;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public List<Filter> getFilters() {
        return filters;
    }

    public void setFilters(List<Filter> filters) {
        this.filters = filters;
    }

    @Override
    public String toString() {
        return "PageListRequest{"
                + "keywordType='" + keywordType + '\''
                + ", subKeywordType='" + subKeywordType + '\''
                + ", keyword='" + keyword + '\''
                + ", pageNo=" + pageNo
                + ", pageSize=" + pageSize
                + ", order='" + order + '\''
                + ", orderBy='" + orderBy + '\''
                + ", filters=" + filters
                + '}';
    }

    public static class Filter {
        private String keyword;
        private String keywordType;
        private String subKeywordType;

        public String getKeyword() {
            return keyword;
        }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }

        public String getKeywordType() {
            return keywordType;
        }

        public void setKeywordType(String keywordType) {
            this.keywordType = keywordType;
        }

        public String getSubKeywordType() {
            return subKeywordType;
        }

        public void setSubKeywordType(String subKeywordType) {
            this.subKeywordType = subKeywordType;
        }

        @Override
        public String toString() {
            return "Filter{"
                    + "keyword='" + keyword + '\''
                    + ", keywordType='" + keywordType + '\''
                    + ", subKeywordType='" + subKeywordType + '\''
                    + '}';
        }
    }
}
