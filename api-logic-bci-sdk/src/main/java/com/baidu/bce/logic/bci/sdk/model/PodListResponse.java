package com.baidu.bce.logic.bci.sdk.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PodListResponse {
    private String orderBy = "";
    private String order = "";
    private int pageNo = 1;
    private int pageSize = 0;
    private int totalCount = 0;
    protected List<PodForDisplay> result ;

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<PodForDisplay> getResult() {
        return result;
    }

    public void setResult(List<PodForDisplay> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "PodListResponse{"
               + "orderBy='" + orderBy + '\''
               + ", order='" + order + '\''
               + ", pageNo=" + pageNo
               + ", pageSize=" + pageSize
               + ", totalCount=" + totalCount
               + ", result=" + result
               + '}';
    }
}
