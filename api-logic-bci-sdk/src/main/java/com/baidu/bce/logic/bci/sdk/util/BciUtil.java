package com.baidu.bce.logic.bci.sdk.util;

import com.baidu.bce.logic.bci.sdk.model.common.PageListRequest;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wangshance on 18/7/19.
 */
public class BciUtil extends Util {

    public static String filter2String(List<PageListRequest.Filter> filterList) {
        String filterMapStr = "";
        Map<String, String> filterMap = null;
        if (filterList != null && filterList.size() > 0) {
            filterMap = new HashMap<>();
            for (PageListRequest.Filter filter : filterList) {
                if ("tag".equalsIgnoreCase(filter.getKeywordType())) {
                    String tagKey = StringUtils.defaultIfEmpty(filter.getSubKeywordType(), "");
                    String tagValue = StringUtils.defaultIfEmpty(filter.getKeyword(), "");
                    filterMap.put("tag", tagKey + "__" + tagValue);
                    continue;
                }
                filterMap.put(filter.getKeywordType(), filter.getKeyword());
            }
        }
        if (filterMap != null) {
            filterMapStr = toJSON(filterMap);
        }
        return filterMapStr;
    }
}
