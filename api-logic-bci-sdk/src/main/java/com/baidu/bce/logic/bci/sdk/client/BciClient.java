package com.baidu.bce.logic.bci.sdk.client;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.logic.bci.sdk.model.PodPageListRequest;
import com.baidu.bce.logic.bci.sdk.model.PodListResponse;
import com.baidu.bce.logic.bci.sdk.model.PodForDisplay;
import com.baidu.bce.logic.bci.sdk.model.common.IDListRequest;
import com.baidu.bce.logic.bci.sdk.util.BciUtil;
import endpoint.EndpointManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BciClient extends BceClient {

    private static final String SERVICE_NAME = "BCI";
    private static final String BASE_URL = "/api/logical/bci/v1/pod";
    public static final String LIST = "/list";
    public static final String LIST_BY_UUID = "/listPodsByUuids";

    public BciClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public BciClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
    }

    public BciClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public BciClient(String endpoint) {
        super(endpoint);
    }

    public BceInternalRequest createInternalRequest() {
        return createAuthorizedRequestWithSignedHeaders(Arrays.asList(BceConstant.HOST, BceConstant.X_BCE_DATE));
    }

    public PodListResponse getPods(PodPageListRequest request) {
        Map paramMap = new HashMap();
        paramMap.put("manner", "page");
        if (StringUtils.isNotEmpty(request.getKeyword()) && StringUtils.isNotEmpty(request.getKeywordType())) {
            paramMap.put("keyword", StringEscapeUtils.escapeJava(request.getKeyword()));
            paramMap.put("keywordType", request.getKeywordType());
        }

        if (request.getOrder() != null && request.getOrderBy() != null) {
            paramMap.put("order", request.getOrder());
            paramMap.put("orderBy", request.getOrderBy());
        } else {
            paramMap.put("order", "desc");
            paramMap.put("orderBy", "createTime");
        }

        if ((request.getPageNo() > 0) && (request.getPageSize() > 0)) {
            paramMap.put("pageNo", request.getPageNo());
            paramMap.put("pageSize", request.getPageSize());
        } else {
            paramMap.put("pageNo", 1);
            paramMap.put("pageSize", 100);
        }

        String filterMapStr = BciUtil.filter2String(request.getFilters());

        paramMap.put("filterMapStr", filterMapStr);

        return getPods(paramMap);
    }

    private PodListResponse getPods(Map queryParamMap) {
        PodListResponse response = createInternalRequest().path(BASE_URL + LIST)
                .queryParams(queryParamMap).get(PodListResponse.class);

        if (response != null && CollectionUtils.isNotEmpty(response.getResult())) {
            for (PodForDisplay pod : response.getResult()) {
                if (pod != null && org.apache.commons.lang.StringUtils.isNotBlank(pod.getDescription())) {
                    pod.setDescription(StringEscapeUtils.unescapeJava(pod.getDescription()));
                }
            }
        }
        return response;
    }

    public List<PodForDisplay> listPodsByPodUuids(List<String> podUuids) {
        IDListRequest request = new IDListRequest().withIds(podUuids);
        PodListResponse result = createInternalRequest()
                .path(BASE_URL + LIST_BY_UUID).post(Entity.json(request), PodListResponse.class);
        if (CollectionUtils.isEmpty(podUuids)) {
            return new ArrayList<>();
        }
        return result.getResult();
    }
}
