package com.baidu.bce.logic.bci.sdk.model.common;

/**
 * Created by wangshance on 18/7/19.
 */
public class Tag {
    private String tagKey;
    private String tagValue;

    public String getTagKey() {
        return tagKey;
    }

    public void setTagKey(String tagKey) {
        this.tagKey = tagKey;
    }

    public String getTagValue() {
        return tagValue;
    }

    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    @Override
    public String toString() {
        return "Tag{"
                + "tagKey='" + tagKey + '\''
                + ", tagValue='" + tagValue + '\''
                + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Tag tag = (Tag) o;

        if (tagKey != null ? !tagKey.equals(tag.tagKey) : tag.tagKey != null) {
            return false;
        }
        return tagValue != null ? tagValue.equals(tag.tagValue) : tag.tagValue == null;
    }

    @Override
    public int hashCode() {
        int result = tagKey != null ? tagKey.hashCode() : 0;
        result = 31 * result + (tagValue != null ? tagValue.hashCode() : 0);
        return result;
    }
}
