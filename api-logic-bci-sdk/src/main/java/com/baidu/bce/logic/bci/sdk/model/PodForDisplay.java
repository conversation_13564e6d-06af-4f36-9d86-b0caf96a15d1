package com.baidu.bce.logic.bci.sdk.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.bci.sdk.model.common.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.sql.Timestamp;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PodForDisplay implements Cloneable {

    private String name = "";
    private String podId = "";
    private String podUuid = "";
    private String status = "";
    @JsonProperty(value = "vCpu")
    private float vCpu = 0;
    private float memory = 0;
    private String eipUuid = "";
    private String publicIp = "";
    private int bandwidthInMbps = 0;
    private String cceUuid = "";
    private String internalIp = "";
    private String securityGroupUuid;
    private String restartPolicy = "";
    private String orderId = "";
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    private String description = "";
    private String region = "";
    private String userId = "";
    private String resourceUuid = "";
    private String taskStatus = "";
    @JsonProperty(value = "tags")
    private List<Tag> podTags;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String subnetUuid;
    private String zoneId = "";
    private String logicalZone = "";
    private String subnetType = "";

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPodId() {
        return podId;
    }

    public void setPodId(String podId) {
        this.podId = podId;
    }

    public String getPodUuid() {
        return podUuid;
    }

    public void setPodUuid(String podUuid) {
        this.podUuid = podUuid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public float getvCpu() {
        return vCpu;
    }

    public void setvCpu(float vCpu) {
        this.vCpu = vCpu;
    }

    public float getMemory() {
        return memory;
    }

    public void setMemory(float memory) {
        this.memory = memory;
    }

    public String getEipUuid() {
        return eipUuid;
    }

    public void setEipUuid(String eipUuid) {
        this.eipUuid = eipUuid;
    }

    public String getPublicIp() {
        return publicIp;
    }

    public void setPublicIp(String publicIp) {
        this.publicIp = publicIp;
    }

    public int getBandwidthInMbps() {
        return bandwidthInMbps;
    }

    public void setBandwidthInMbps(int bandwidthInMbps) {
        this.bandwidthInMbps = bandwidthInMbps;
    }

    public String getCceUuid() {
        return cceUuid;
    }

    public void setCceUuid(String cceUuid) {
        this.cceUuid = cceUuid;
    }

    public String getInternalIp() {
        return internalIp;
    }

    public void setInternalIp(String internalIp) {
        this.internalIp = internalIp;
    }

    public String getSecurityGroupUuid() {
        return securityGroupUuid;
    }

    public void setSecurityGroupUuid(String securityGroupUuid) {
        this.securityGroupUuid = securityGroupUuid;
    }

    public String getRestartPolicy() {
        return restartPolicy;
    }

    public void setRestartPolicy(String restartPolicy) {
        this.restartPolicy = restartPolicy;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Timestamp getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }

    public Timestamp getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Timestamp getDeletedTime() {
        return deletedTime;
    }

    public void setDeletedTime(Timestamp deletedTime) {
        this.deletedTime = deletedTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getResourceUuid() {
        return resourceUuid;
    }

    public void setResourceUuid(String resourceUuid) {
        this.resourceUuid = resourceUuid;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public List<Tag> getPodTags() {
        return podTags;
    }

    public void setPodTags(List<Tag> podTags) {
        this.podTags = podTags;
    }

    public String getSubnetUuid() {
        return subnetUuid;
    }

    public void setSubnetUuid(String subnetUuid) {
        this.subnetUuid = subnetUuid;
    }

    public String getZoneId() {
        return zoneId;
    }

    public void setZoneId(String zoneId) {
        this.zoneId = zoneId;
    }

    public String getLogicalZone() {
        return logicalZone;
    }

    public void setLogicalZone(String logicalZone) {
        this.logicalZone = logicalZone;
    }

    public String getSubnetType() {
        return subnetType;
    }

    public void setSubnetType(String subnetType) {
        this.subnetType = subnetType;
    }

    @Override
    public String toString() {
        return "PodForDisplay{"
                + "name='" + name + '\''
                + ", podId='" + podId + '\''
                + ", podUuid='" + podUuid + '\''
                + ", status='" + status + '\''
                + ", vCpu=" + vCpu
                + ", memory=" + memory
                + ", eipUuid='" + eipUuid + '\''
                + ", publicIp='" + publicIp + '\''
                + ", bandwidthInMbps=" + bandwidthInMbps
                + ", cceUuid='" + cceUuid + '\''
                + ", internalIp='" + internalIp + '\''
                + ", securityGroupUuid='" + securityGroupUuid + '\''
                + ", restartPolicy='" + restartPolicy + '\''
                + ", orderId='" + orderId + '\''
                + ", createdTime=" + createdTime
                + ", updatedTime=" + updatedTime
                + ", deletedTime=" + deletedTime
                + ", description='" + description + '\''
                + ", region='" + region + '\''
                + ", userId='" + userId + '\''
                + ", resourceUuid='" + resourceUuid + '\''
                + ", taskStatus='" + taskStatus + '\''
                + ", podTags=" + podTags
                + ", subnetUuid='" + subnetUuid + '\''
                + ", zoneId='" + zoneId + '\''
                + ", logicalZone='" + logicalZone + '\''
                + ", subnetType='" + subnetType + '\''
                + '}';
    }
}