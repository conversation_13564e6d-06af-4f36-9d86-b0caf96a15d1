<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>api-logic-bci-root</artifactId>
    <packaging>pom</packaging>
    <version>${api-logic-bci-version}</version>

    <properties>
        <api-logic-bci-version>version</api-logic-bci-version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <parent>
        <groupId>com.baidu.bce</groupId>
        <artifactId>bce-plat-web-framework-parent</artifactId>
        <version>1.0.8.5</version>
    </parent>

    <modules>
        <module>api-logic-bci</module>
        <module>api-logic-bci-internalsdk</module>
        <module>api-logic-bci-dao</module>
        <module>api-logic-bci-service</module>
        <module>api-logic-bci-controller</module>
        <module>api-logic-bci-sdk</module>
        <module>api-logic-bci-controllerv2</module>
        <module>api-logic-bci-servicev2</module>
        <module>api-logic-bci-daov2</module>
        <module>api-logic-bci-order-executor</module>

    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.11</version>
                <executions>
                    <execution>
                        <phase>none</phase>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <!--Restores original classes as they were before offline instrumentation.-->
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.20</version>
                <configuration>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                    <argLine>-Dfile.encoding=UTF-8 -Xms512m -Xmx1024m -XX:MaxPermSize=512m</argLine>
                </configuration>
            </plugin>

            <!-- <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <formats>
                        <format>xml</format>
                    </formats>
                    <check></check>
                    <instrumentation>
                        <includes>
                            <include>
                                com/baidu/bce/logic/bci/service/**/*Service.class
                            </include>
                            <include>
                                com/baidu/bce/logic/bci/service/**/*Validator.class
                            </include>
                        </includes>
                    </instrumentation>
                </configuration>
            </plugin> -->
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>baidu-nexus</id>
            <url>http://maven.scm.baidu.com:8081/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>Baidu_Local</id>
            <url>http://maven.scm.baidu.com:8081/nexus/content/repositories/Baidu_Local</url>
        </repository>
        <snapshotRepository>
            <id>Baidu_Local_Snapshots</id>
            <url>http://maven.scm.baidu.com:8081/nexus/content/repositories/Baidu_Local_Snapshots</url>
        </snapshotRepository>
    </distributionManagement>
    <dependencies>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
            <scope>test</scope>
            <version>0.8.11</version>
        </dependency>
    </dependencies>

</project>
