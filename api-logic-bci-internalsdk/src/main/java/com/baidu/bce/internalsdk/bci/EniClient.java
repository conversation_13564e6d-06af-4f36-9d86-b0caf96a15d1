package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.bci.model.CreateEniRequest;
import com.baidu.bce.internalsdk.bci.model.CreateEniResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEniResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEniSelfResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEniListResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEniListRequest;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.Entity;

import org.apache.commons.lang3.StringUtils;
import endpoint.EndpointManager;

import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Date;
import java.util.TimeZone;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

public class EniClient extends BceClient {
    private static final String SERVICE_NAME = "ENI";
    private static final String RESOURCE_TYPE = "bci";
    private StsCredential stsCredential;
    private String resourceAccountID;
    private String eniHexkey;

    public EniClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public EniClient(StsCredential stsCredential) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey());
        this.stsCredential = stsCredential;
    }

    public EniClient(StsCredential stsCredential, String bciResourceAccountID, String eniHexkey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey());
        this.stsCredential = stsCredential;
        this.resourceAccountID = bciResourceAccountID;
        this.eniHexkey = eniHexkey;
    }

    public void setStsCredential(StsCredential stsCredential) {
        this.stsCredential = stsCredential;
    }

    private BceInternalRequest createInternalRequestWithSignedHeaders() {
        BceInternalRequest bceInternalRequest = super.createAuthorizedRequestWithSignedHeaders(
            Arrays.asList(BceConstant.HOST, BceConstant.X_BCE_DATE));

        bceInternalRequest.token(stsCredential.getToken().getId());
        bceInternalRequest.securityToken(stsCredential.getSessionToken());
        bceInternalRequest.authorization(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(BceConstant.DATETIME_FORMAT);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        String accountIdWithEncryptionDate = resourceAccountID + "/" + simpleDateFormat.format(new Date());
        String encryptedResourceAccountId = AesCryptoUtil.encrypt(accountIdWithEncryptionDate, eniHexkey);

        bceInternalRequest.header("resource-accountId", encryptedResourceAccountId);
        bceInternalRequest.header("resource-source", RESOURCE_TYPE);
        
        return bceInternalRequest;
    }

    // get an ENI instance detail
    public QueryEniResponse queryEni(String eniId) {
        QueryEniResponse response = createInternalRequestWithSignedHeaders()
            .path("/v1/eni/" + eniId) // eni-1w307dsscfiu
            .get(QueryEniResponse.class);
        return response;
    }

    // crate an ENI instance
    public CreateEniResponse createEni() {
        List<CreateEniRequest.PrivateIpItem> privateIpSet = new ArrayList<>();
        privateIpSet.add(new CreateEniRequest.PrivateIpItem().setPrimary(true));
        privateIpSet.add(new CreateEniRequest.PrivateIpItem().setPrimary(false));
        privateIpSet.add(new CreateEniRequest.PrivateIpItem().setPrimary(false));

        List<String> securityGroupIDs = new ArrayList<>();
        securityGroupIDs.add("g-7v1iuaxvri0h");

        CreateEniRequest request = new CreateEniRequest();
        request.setName("bci_3ip_sts_test");
        request.setDescription("create by bci test");
        request.setSubnetId("sbn-r107hfd7a45n");
        request.setPrivateIpSet(privateIpSet);
        request.setSecurityGroupIds(securityGroupIDs);

        CreateEniResponse response = createInternalRequestWithSignedHeaders()
            .path("/v1/eni")
            .post(Entity.json(request), CreateEniResponse.class);
        return response;
    }

    // search ENI instance list
    public QueryEniListResponse queryEniList(QueryEniListRequest request) {
        if (StringUtils.isEmpty(request.getVpcId())) {
            return null;
        }
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("vpcId", request.getVpcId());
        if (StringUtils.isNoneEmpty(request.getPrivateIpAddress())) {
            paramMap.put("privateIpAddress", request.getPrivateIpAddress());
        }
        QueryEniListResponse response = createInternalRequestWithSignedHeaders()
            .path("/v1/eni")
            .queryParams(paramMap)
            .get(QueryEniListResponse.class);
        return response;
    }

    // attach EIP to ENI privateIp
    public void attachEip(String eniId, String privateIp, String eipIp) {
        Map<String, String> urlMap = new HashMap<String, String>();
        urlMap.put("bind", "");

        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("privateIpAddress", privateIp);
        paramMap.put("publicIpAddress", eipIp);
        createInternalRequestWithSignedHeaders()
            .path("/v1/eni/" + eniId)
            .queryParams(urlMap)
            .put(Entity.json(paramMap));
    }

    // get eni {instanceId, publicIp}, by private ip
    public QueryEniSelfResponse getEniByPrivateIp(String vpcId, String privateIp) {
        QueryEniListRequest queryEniListRequest = new QueryEniListRequest();
        queryEniListRequest.setVpcId(vpcId);
        queryEniListRequest.setPrivateIpAddress(privateIp);
        QueryEniListResponse queryEniListResponse = queryEniList(queryEniListRequest);
        if (queryEniListResponse == null || queryEniListResponse.getEnis().isEmpty()) {
            return null;
        }
        for (QueryEniResponse eniInstance : queryEniListResponse.getEnis()) {
            for (QueryEniResponse.PrivateIpItem ipItem : eniInstance.getPrivateIpSet()) {
                if (ipItem.getPrivateIpAddress().equals(privateIp)) {
                    QueryEniSelfResponse res = new QueryEniSelfResponse();
                    res.setEniId(eniInstance.getEniId());
                    res.setPublicIp(ipItem.getPublicIpAddress());
                    return res;
                }
            }
        }
        return null;
    }
    
}
