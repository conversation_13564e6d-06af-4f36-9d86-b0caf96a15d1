package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)

public class QueryAllBlsTaskResponse {
    public QueryAllBlsTaskResponse() {}
    private int  totalSize;
    private int  offset;
    private int  size;
    private List<TaskDetail> tasks = new ArrayList<>();;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class TaskDetail {
        public TaskDetail() {}
        private String id;
        private String name;
        private TaskStatus status;
        private Date creationDateTime;
        private String type;
        private String taskVersion;
        private List<Tag> tags = new ArrayList<>();;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Accessors(chain = true)
    public enum TaskStatus {
        Initializing,
        Running,
        Paused,
        Stopping,
        Stopped,
        Abnormal;
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class Tag {
        public Tag() {}
        private String tagKey;
        private String tagValue;
    }
}
