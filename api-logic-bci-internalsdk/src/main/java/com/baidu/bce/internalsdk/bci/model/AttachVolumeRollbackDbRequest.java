package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Data
@Accessors(chain = true)
public class AttachVolumeRollbackDbRequest {
    private String instanceUuid; // 传短ID
    private List<String> diskIds;
}
