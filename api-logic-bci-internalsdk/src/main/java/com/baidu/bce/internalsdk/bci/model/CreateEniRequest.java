package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CreateEniRequest {
    private String name;
    private String description;
    private String subnetId;
    private List<PrivateIpItem> privateIpSet;
    private List<String> securityGroupIds;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class PrivateIpItem {
        private Boolean primary;
        private String privateIpAddress;
    }
}
