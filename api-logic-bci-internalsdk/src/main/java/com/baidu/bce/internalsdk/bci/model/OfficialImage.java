package com.baidu.bce.internalsdk.bci.model;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class OfficialImage {

    private String name;
    private String description;
    private String address;
    private List<String> tags;
    private String icon;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public List<String> getTags() {
        if (tags == null) {
            tags = new ArrayList<>();
        }
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Override
    public String toString() {
        return "OfficialImage{"
                + "name='" + name + '\''
                + ", description='" + description + '\''
                + ", address='" + address + '\''
                + ", tags=" + tags
                + ", icon='" + icon + '\''
                + '}';
    }

    public static Comparator<OfficialImage> getComparator(String compareBy) {
        Comparator<OfficialImage> comparator;
        switch (compareBy) {
            case "address":
                comparator = new Comparator<OfficialImage>() {
                    @Override
                    public int compare(OfficialImage o1, OfficialImage o2) {
                        return o1.getAddress().compareTo(o2.getAddress());
                    }
                };
                break;
            // 其他情况/默认情况 按照createTime排序
            default:
                comparator = new Comparator<OfficialImage>() {
                    @Override
                    public int compare(OfficialImage o1, OfficialImage o2) {
                        return o1.getName().compareTo(o2.getName());
                    }
                };
        }
        return comparator;
    }
}
