package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ResourceGroupRequest {
    @JsonProperty(value = "user_id")
    private String userId;
    @JsonProperty(value = "physical_zone")
    private String physicalZone;
    // nat,emr,scs
    private String source = "bci";
    // BCC
    @JsonProperty(value = "resource_type")
    private String resourceType = "BCI";
}