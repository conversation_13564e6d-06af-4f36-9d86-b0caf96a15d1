package com.baidu.bce.internalsdk.bci;

import java.util.Arrays;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.plat.cloudtrail.model.QueryEventsRequest;
import com.baidu.bce.plat.cloudtrail.model.TrailPageResponse;

import endpoint.EndpointManager;

public class CloudTrailClient extends BceClient {

    private static final Logger log = LoggerFactory.getLogger(CloudTrailClient.class);

    public static final String SERVICE_NAME = "cloud-trail";
    private static final String HOST_NAME = "gwgp-fv29ewwddnp.i.bdcloudapi.com";
    private boolean enableHost = true;

    public CloudTrailClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public CloudTrailClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public CloudTrailClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    private BceInternalRequest getBceInternalRequest() {
        BceInternalRequest bceInternalRequest = super.createAuthorizedRequestWithSignedHeaders(
                Arrays.asList("host", "x-bce-date"));
        if (enableHost) {
            return bceInternalRequest.host(HOST_NAME);
        }
        return bceInternalRequest;
    }

    public void setEnableHost(boolean enableHost) {
        this.enableHost = enableHost;
    }

    /**
     *
     * @param request
     * @return
     */
    public TrailPageResponse queryEvents(QueryEventsRequest request) {
        return this.getBceInternalRequest().path("/query_events").post(Entity.json(request),
                TrailPageResponse.class);
    }


}
