package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class BidInstanceEventsResponse {
    @JsonProperty(value = "events")
    private List<BidEvent> events;
    private int pageNo;
    private int pageSize;
    private int totalsize;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class BidEvent {
        private String instanceId;
        private String instanceUuid;
        private String eventTime;
        private String action;
    }
}
