package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class QueryEipListResponse {
// See https://cloud.baidu.com/doc/EIP/s/Pjwvz30qy
    private String marker;
    private boolean isTruncated;
    private int maxKeys;
    private List<QueryEipResponse> eipList = new ArrayList<>();
}
