package com.baidu.bce.internalsdk.bci.model;

import java.util.Collection;
import java.util.LinkedList;

/**
 * 一般页面显示, 带页码的分页方式返回结果 分页相关 给页面返回结果, 模板使用方式
 */
public class PageNoPageResult<T> {

    // 排序的key
    private String orderBy = "";
    // 升序asc 或 降序desc 排序
    private String order = "";
    // 页码
    private int pageNo = 1;
    // 一页条目数量
    private int pageSize = 0;
    // 总条目数量
    private int totalCount = 0;
    // 返回结果
    private Collection<T> result = new LinkedList<>();

    public PageNoPageResult() {
    }

    public PageNoPageResult(String orderBy, String order, int pageNo, int pageSize,
                            int totalCount, Collection<T> result) {
        this.orderBy = orderBy;
        this.order = order;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.result = result;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public Collection<T> getResult() {
        return result;
    }

    public void setResult(Collection<T> result) {
        this.result = result;
    }

    public PageNoPageResult withResult(Collection<T> result) {
        this.result = result;
        return this;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("PageNoPageResult{");
        sb.append("orderBy='").append(orderBy).append('\'');
        sb.append(", order='").append(order).append('\'');
        sb.append(", pageNo=").append(pageNo);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", totalCount=").append(totalCount);
        sb.append(", result=").append(result);
        sb.append('}');
        return sb.toString();
    }
}
