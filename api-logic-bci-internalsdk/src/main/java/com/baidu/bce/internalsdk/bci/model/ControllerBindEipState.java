package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ControllerBindEipState {
    private String eip;
    private String nodeName;
    private String eniID;
    private String state;
    private int createRetrytimes;
    private int deleteRetrytimes;
}

