package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TransactionDetailResponse {
    @JsonProperty("transaction_id")
    private String transactionId;

    private String status;

    private String action;

    @JsonProperty("servers")
    private List<ServersResponse> lstServer;

    @JsonProperty("volumes")
    private List<DiskResult> volumes;

    @JsonProperty("errmsg")
    private String errMsg;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("subnet_uuid")
    private String subnetUuid;

    @JsonProperty("physics_az")
    private String physicalZone;

    public static class DiskResult {
        private String id;
        private String size;
    }
}