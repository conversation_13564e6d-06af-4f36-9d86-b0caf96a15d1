package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class DockerHubImageResponse {
    private String orderBy = "";
    private String order = "";
    private int pageNo = 1;
    private int pageSize = 0;
    private int count = 0;
    private List<DockerHubImage> results = new ArrayList<>();
}
