package com.baidu.bce.internalsdk.bci.model;

import java.util.Date;
import java.util.List;

import com.baidu.bce.plat.cloudtrail.model.EventDetail;
import com.baidu.bce.plat.cloudtrail.model.ResourceInfo;
import com.baidu.bce.plat.cloudtrail.model.UserIdentity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PodEventPO {
    private String eventVersion;
    private String eventId;
    private String eventType;
    private String eventSource;
    private String eventName;
    private long eventTimeInMilliseconds = System.currentTimeMillis();
    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'",
            timezone = "UTC"
    )
    private Date eventTime;
    private UserIdentity userIdentity;
    private String userIpAddress;
    private String userAgent;
    private String regionId;
    private String requestId;
    private String orderId;
    private String apiVersion;
    private String description;
    private String errorCode;
    private String errorMessage;
    private EventDetail eventDetail;
    private List<ResourceInfo> resources;
    private boolean success;

    public String getEventVersion() {
        return eventVersion;
    }

    public void setEventVersion(String eventVersion) {
        this.eventVersion = eventVersion;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventSource() {
        return eventSource;
    }

    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public long getEventTimeInMilliseconds() {
        return eventTimeInMilliseconds;
    }

    public void setEventTimeInMilliseconds(long eventTimeInMilliseconds) {
        this.eventTimeInMilliseconds = eventTimeInMilliseconds;
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    public UserIdentity getUserIdentity() {
        return userIdentity;
    }

    public void setUserIdentity(UserIdentity userIdentity) {
        this.userIdentity = userIdentity;
    }

    public String getUserIpAddress() {
        return userIpAddress;
    }

    public void setUserIpAddress(String userIpAddress) {
        this.userIpAddress = userIpAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public EventDetail getEventDetail() {
        return eventDetail;
    }

    public void setEventDetail(EventDetail eventDetail) {
        this.eventDetail = eventDetail;
    }

    public List<ResourceInfo> getResources() {
        return resources;
    }

    public void setResources(List<ResourceInfo> resources) {
        this.resources = resources;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("PodEventPO{");
        sb.append("eventVersion='").append(eventVersion).append('\'');
        sb.append(", eventId='").append(eventId).append('\'');
        sb.append(", eventType='").append(eventType).append('\'');
        sb.append(", eventSource='").append(eventSource).append('\'');
        sb.append(", eventName='").append(eventName).append('\'');
        sb.append(", eventTimeInMilliseconds=").append(eventTimeInMilliseconds);
        sb.append(", eventTime=").append(eventTime);
        sb.append(", userIdentity=").append(userIdentity);
        sb.append(", userIpAddress='").append(userIpAddress).append('\'');
        sb.append(", userAgent='").append(userAgent).append('\'');
        sb.append(", regionId='").append(regionId).append('\'');
        sb.append(", requestId='").append(requestId).append('\'');
        sb.append(", orderId='").append(orderId).append('\'');
        sb.append(", apiVersion='").append(apiVersion).append('\'');
        sb.append(", description='").append(description).append('\'');
        sb.append(", errorCode='").append(errorCode).append('\'');
        sb.append(", errorMessage='").append(errorMessage).append('\'');
        sb.append(", eventDetail=").append(eventDetail);
        sb.append(", resources=").append(resources);
        sb.append(", success=").append(success);
        sb.append('}');
        return sb.toString();
    }
}
