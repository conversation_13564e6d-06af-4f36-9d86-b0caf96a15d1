package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(
        ignoreUnknown = true
)
@JsonRootName("volume")
public class Volume implements Serializable {
    private String id;
    private String status;
    private String name;
    private String description;
    @JsonProperty("availability_zone")
    private String availabilityZone;
    @JsonProperty("volume_type")
    private String volumeType;
    @JsonProperty("snapshot_id")
    private String snapshotId;
    private List<VolumeAttachment> attachments;
    @JsonProperty("cmk_uuid")
    private String cmkUuid;
    private Map<String, String> metadata;
    @JsonProperty("created_at")
    @JsonFormat(
            shape = Shape.STRING,
            pattern = "yyyy-MM-dd'T'HH:mm:ss.FFF",
            timezone = "UTC"
    )
    private Timestamp createdAt;
    private Integer size;
    @JsonProperty("user_id")
    private String userId;

    public Volume() {
    }

    @JsonRootName("volumeAttachment")
    @JsonIgnoreProperties(
            ignoreUnknown = true
    )
    public static class VolumeAttachment implements Serializable {
        private String id;
        @JsonProperty("volume_id")
        private String volumeId;
        @JsonProperty("server_id")
        private String serverId;
        @JsonProperty("volumeId")
        private String volumeIdCopy;
        @JsonProperty("serverId")
        private String serverIdCopy;
        private String device;

        public VolumeAttachment() {
        }
    }
}