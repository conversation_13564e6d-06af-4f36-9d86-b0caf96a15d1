package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ServersResponse {
    private String fixip;
    private String floatingip;
    private String id;
    private String name;
    private String adminPass;
    private Map<String, String> metadata;
    @JsonProperty("bci_cds")
    private List<ServerBciCds> bciCds;

    @Data
    public static class ServerBciCds extends ServerCds {
        @JsonProperty(value = "block")
        private String block;
        @JsonProperty(value = "fs")
        private BciVolume.FS fs;
        @JsonProperty(value = "volume_source")
        private BciVolume.VolumeSource volumeSource;
        @JsonProperty(value = "type")
        private String type;
    }
}
