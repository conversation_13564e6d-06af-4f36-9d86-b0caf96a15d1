package com.baidu.bce.internalsdk.bci.constant;

public class EipConstant {
    /*
     * BCI内部自定义的EIP状态
     * 0 初始默认状态
     * 1 已删除
     * 2 已解绑
     * 3 用户创建POD时指定的EIP
     * 4 用户创建POD时指定的EIP，已解绑
     */
    public static final int STATUS_INIT = 0;
    public static final int STATUS_DELETED = 1;
    public static final int STATUS_UNBINDED = 2;
    public static final int STATUS_USER_SPECIFIED = 3;
    public static final int STATUS_USER_SPECIFIED_UNBINDED = 4;

    /*
     * IaaS的EIP状态
     * https://cloud.baidu.com/doc/EIP/s/Mjwvz2wnn#eipstatus
     */
    public static final String IAAS_STATUS_CREATING = "creating";       /* 创建中 */ 
    public static final String IAAS_STATUS_AVAILABLE = "available";     /* 可用 */ 
    public static final String IAAS_STATUS_BINDED = "binded";           /* 已绑定 */ 
    public static final String IAAS_STATUS_BINDING = "binding";         /* 绑定中 */ 
    public static final String IAAS_STATUS_UNBINDING = "unbinding";     /* 解绑中 */ 
    public static final String IAAS_STATUS_UPDATING = "updating";       /* 更新中 */ 
    public static final String IAAS_STATUS_PAUSED = "paused";           /* 已暂停 */ 
    public static final String IAAS_STATUS_UNAVAILABLE = "unavailable"; /* 暂不可用 */ 

    /*
     * 有EIP的POD, 添加一条Finaliezers
     * 用于eip-controller能确保完成EIP回收的检查，即使informer丢失事件
    */
    public static final String POD_FINALIZER = "bci.cloud.baidu.com/eip";
    
}
