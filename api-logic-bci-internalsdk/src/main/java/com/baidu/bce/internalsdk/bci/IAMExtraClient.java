package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.iam.DecryptRequest;
import com.baidu.bce.internalsdk.bci.model.iam.DecryptResponse;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.Entity;
import endpoint.EndpointManager;

public class IAMExtraClient extends BceClient {
    public static final String SERVICE_NAME = "IAM";

    public IAMExtraClient(String ak, String sk) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), ak, sk);
    }

    public IAMExtraClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public IAMExtraClient(String endpoint) {
        super(endpoint);
    }

//    public EncryptResponse encrypt(IamEncryptRequest encryptRequest) {
//        return createAuthorizedRequest().path("/BCE-CRED/ciphers").keyOnlyQueryParam("encrypt")
//                .post(Entity.json(encryptRequest), IamEncryptResponse.class);
//    }

    public DecryptResponse decrypt(DecryptRequest decryptRequest) {
        return createAuthorizedRequest().path("/BCE-CRED/ciphers").keyOnlyQueryParam("decrypt")
                .post(Entity.json(decryptRequest), DecryptResponse.class);
    }
}