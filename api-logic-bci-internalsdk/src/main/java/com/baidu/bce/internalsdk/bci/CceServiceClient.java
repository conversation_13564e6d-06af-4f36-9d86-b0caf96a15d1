package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.DockerHubImageResponse;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

public class CceServiceClient extends BceClient {
    private static final String SERVICE_NAME = "CCE_SERVICE";

    public CceServiceClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public CceServiceClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    private BceInternalRequest createCceRequest() {
        return super.createAuthorizedRequest();
    }


    public DockerHubImageResponse listDockerHubImage(String keyword, String keywordType) {
        BceInternalRequest request = createCceRequest().path("/v1/image/dockerhub")
                .queryParam("pageNo", 1);
        if (StringUtils.isNotBlank(keywordType) && StringUtils.isNotBlank(keyword)) {
            request.queryParam("keywordType", keywordType).queryParam("keyword", keyword);
        }
        return request.get(DockerHubImageResponse.class);
    }
}
