package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AvailableResourceResponse {
    private List<Result> results ;

    @Data
    public static class Result {
        @JsonProperty("collection_at")
        private Long collectionAt;

        @JsonProperty("resource_unit_id")
        private String resourceUnitId;

        @JsonProperty("updated_at")
        private Long updatedAt;

        @JsonProperty("available_number")
        private int availableNumber;
    }
}