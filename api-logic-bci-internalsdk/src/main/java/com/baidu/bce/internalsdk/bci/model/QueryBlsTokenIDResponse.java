package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.joda.time.DateTime;

import java.util.Map;
import java.util.HashMap;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class QueryBlsTokenIDResponse {
    private String id;
    private String token;
    private String description;
    private DateTime creationDateTime;
    private Map<String, String> installCommands = new HashMap<>();
        
}
