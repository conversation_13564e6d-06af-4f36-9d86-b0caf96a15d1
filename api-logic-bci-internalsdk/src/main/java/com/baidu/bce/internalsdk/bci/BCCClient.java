package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.AttachVolumeRollbackDbRequest;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeUpdateDbRequest;
import com.baidu.bce.internalsdk.bci.model.BccInstanceListRequest;
import com.baidu.bce.internalsdk.bci.model.BccInstanceListResponse;
import com.baidu.bce.internalsdk.bci.model.BccSpecStockRequest;
import com.baidu.bce.internalsdk.bci.model.BccSpecStockResponse;
import com.baidu.bce.internalsdk.bci.model.BidInstanceEventsRequest;
import com.baidu.bce.internalsdk.bci.model.BidInstanceEventsResponse;
import com.baidu.bce.internalsdk.bci.model.QueryVolumesRequest;
import com.baidu.bce.internalsdk.bci.model.QueryVolumesResponse;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import endpoint.EndpointManager;

public class BCCClient extends BceClient {
    private static final String SERVICE_NAME = "LOGICAL_BCC";

    public BCCClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public BCCClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    private BceInternalRequest createBCCRequest() {
        return super.createAuthorizedRequest();
    }

    /******************************* volume ********************************/


    public QueryVolumesResponse getVolumes(QueryVolumesRequest queryVolumesRequest) {
        BceInternalRequest request = createBCCRequest().path("/api/logical/bcc/v1/cinder/queryVolumesByIds");
        return request.post(Entity.json(queryVolumesRequest), QueryVolumesResponse.class);
    }

    public void attachVolume(AttachVolumeUpdateDbRequest attachVolumeRequest) {
        BceInternalRequest request = createBCCRequest().path("/api/logical/bcc/v1/cinder/attach/volume/updateDb");
        request.post(Entity.json(attachVolumeRequest));
    }

    public void rollbackVolume(AttachVolumeRollbackDbRequest rollbackRequest) {
        if (rollbackRequest == null) {
            return;
        }
        BceInternalRequest request = createBCCRequest().path("/api/logical/bcc/v1/cinder/detach/volume/rollBackDb");
        request.post(Entity.json(rollbackRequest));
    }

    /******************************* bid ********************************/
    public BidInstanceEventsResponse getBccBidInstanceEvents(BidInstanceEventsRequest bidInstanceEventsRequest) {
        BceInternalRequest request = createBCCRequest().path("/api/logical/bcc/v1/instance/bid/events");
        return request.post(Entity.json(bidInstanceEventsRequest), BidInstanceEventsResponse.class);
    }

    public BccInstanceListResponse getBccInstanceList(BccInstanceListRequest bccInstanceListRequest) {
        BceInternalRequest request = createBCCRequest().path("/api/logical/bcc/v1/instance");
        return request.post(Entity.json(bccInstanceListRequest), BccInstanceListResponse.class);
    }

    /******************************* stock ********************************/
    // 查询可用库存 
    // 接口说明 http://gollum.baidu.com/BceDocumentation/BccOpenAPI#%E9%80%9A%E8%BF%87spec%E6%9F%A5%E8%AF%A2%E5%8F%AF%E8%B4%AD%E4%B9%B0bcc%E5%A5%97%E9%A4%90%E5%BA%93%E5%AD%98%EF%BC%88%E4%B8%8D%E5%85%AC%E5%BC%80%EF%BC%89
    public BccSpecStockResponse getBccSpecStock(BccSpecStockRequest bccSpecStockRequest) {
        BceInternalRequest request = createBCCRequest().path("/api/logical/bcc/v1/instance/getAvailableStockWithSpec");
        return request.post(Entity.json(bccSpecStockRequest), BccSpecStockResponse.class);
    }
}