package com.baidu.bce.internalsdk.bci.model;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class BcmListEventsResponse {
    @JsonProperty(value = "content")
    private java.util.List<BcmEvent> content;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class BcmEvent {
        private String accountId;
        private String serviceName;
        private String region;
        private String resourceType;
        private String resourceId;
        private String eventId;
        private String eventType;
        private String eventLevel;
        private String eventAlias;
        private Date timestamp;
        private String content;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class BcmEventContent {
        private String info;
        private String advice;
        private String reason;
        private String message;
        private String fieldPath;
    }

    private int pageNumber;
    private int pageSize;
    private int pageElements;
    private int totalPages;
    private int totalElements;
}