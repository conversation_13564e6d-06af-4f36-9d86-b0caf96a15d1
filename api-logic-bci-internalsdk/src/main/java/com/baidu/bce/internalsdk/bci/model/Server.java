package com.baidu.bce.internalsdk.bci.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonRootName("server")
@JsonIgnoreProperties(ignoreUnknown = true)
public class Server implements Serializable {

    public static final class Addresses implements Serializable {

        public static final class Address implements Serializable {

            @JsonProperty("OS-EXT-IPS-MAC:mac_addr")
            private String macAddr;

            private String version;

            private String addr;

            @JsonProperty("OS-EXT-IPS:type")
            private String type;

            public String getMacAddr() {
                return macAddr;
            }

            public String getVersion() {
                return version;
            }

            public String getAddr() {
                return addr;
            }

            public String getType() {
                return type;
            }

            public void setVersion(String version) {
                this.version = version;
            }

            public void setAddr(String addr) {
                this.addr = addr;
            }

            public void setType(String type) {
                this.type = type;
            }

            public void setMacAddr(String macAddr) {
                this.macAddr = macAddr;
            }
        }

        private Map<String, List<Address>> addresses = new HashMap<String, List<Address>>();

        @JsonAnySetter
        public void add(String key, List<Address> value) {
            addresses.put(key, value);
        }

        /**
         * @return the ip address List Map
         */
        public Map<String, List<Address>> getAddresses() {
            return addresses;
        }

        /* (non-Javadoc)
         * @see java.lang.Object#toString()
         */
        @Override
        public String toString() {
            return "Addresses List Map [" + addresses + "]";
        }

    }

    public static final class Fault {

        private Integer code;

        private String message;

        private String details;

        private Calendar created;

        /**
         * @return the code
         */
        public Integer getCode() {
            return code;
        }

        /**
         * @return the message
         */
        public String getMessage() {
            return message;
        }

        /**
         * @return the details
         */
        public String getDetails() {
            return details;
        }

        /**
         * @return the created
         */
        public Calendar getCreated() {
            return created;
        }

        /* (non-Javadoc)
         * @see java.lang.Object#toString()
         */
        @Override
        public String toString() {
            return "Fault [code=" + code + ", message=" + message + ", details=" + details + ", created=" + created
                    + "]";
        }

    }

    public static final class Image {
        private String id;
        private List<Link> links;
        @JsonProperty("image_os_lang")
        private String osLang;
        @JsonProperty("image_os_name")
        private String osName;
        @JsonProperty("image_image_type")
        private String imageType;
        @JsonProperty("image_os_version")
        private String osVersion;
        @JsonProperty("image_os_arch")
        private String osArch;
        @JsonProperty("image_os_type")
        private String osType;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public List<Link> getLinks() {
            return links;
        }

        public void setLinks(List<Link> links) {
            this.links = links;
        }

        public String getOsLang() {
            return osLang == null ? "" : osLang;
        }

        public void setOsLang(String osLang) {
            this.osLang = osLang;
        }

        public String getOsName() {
            return osName == null ? "" : osName;
        }

        public void setOsName(String osName) {
            this.osName = osName;
        }

        public String getImageType() {
            return imageType == null ? "" : imageType;
        }

        public void setImageType(String imageType) {
            this.imageType = imageType;
        }

        public String getOsVersion() {
            return osVersion == null ? "" : osVersion;
        }

        public void setOsVersion(String osVersion) {
            this.osVersion = osVersion;
        }

        public String getOsArch() {
            return osArch == null ? "x86" : osArch;
        }

        public void setOsArch(String osArch) {
            this.osArch = osArch;
        }

        public String getOsType() {
            return osType;
        }

        public void setOsType(String osType) {
            this.osType = osType;
        }
    }

    public static final class VirtualDisk {

        private Integer share;

        @JsonProperty("device_name")
        private String deviceName;

        @JsonProperty("volume_size")
        private Integer volumeSize;

        private String path;

        private String type;

        private String id;

        public Integer getShare() {
            return share;
        }

        public void setShare(Integer share) {
            this.share = share;
        }

        public String getDeviceName() {
            return deviceName;
        }

        public void setDeviceName(String deviceName) {
            this.deviceName = deviceName;
        }

        public Integer getVolumeSize() {
            return volumeSize;
        }

        public void setVolumeSize(Integer volumeSize) {
            this.volumeSize = volumeSize;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }

    public static final class Subnet {
        private String cidr;
        private String id;

        public String getCidr() {
            return cidr;
        }

        public void setCidr(String cidr) {
            this.cidr = cidr;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }

    public static final class VolumesAttached {

        @JsonProperty("image_id")
        private String imageId;

        @JsonProperty("boot_index")
        private Integer bootIndex;

        @JsonProperty("volume_id")
        private String volumeId;

        public String getImageId() {
            return imageId;
        }

        public void setImageId(String imageId) {
            this.imageId = imageId;
        }

        public Integer getBootIndex() {
            return bootIndex;
        }

        public void setBootIndex(Integer bootIndex) {
            this.bootIndex = bootIndex;
        }

        public String getVolumeId() {
            return volumeId;
        }

        public void setVolumeId(String volumeId) {
            this.volumeId = volumeId;
        }

        @Override
        public String toString() {
            return "VolumesAttached{"
                    + "imageId='" + imageId + '\''
                    + ", bootIndex=" + bootIndex
                    + ", volumeId='" + volumeId + '\''
                    + '}';
        }
    }

    @JsonProperty("os-extended-volumes:volumes_attached")
    private List<VolumesAttached> volumesAttacheds;

    @JsonProperty("virtual_disks")
    private List<VirtualDisk> virtualDisks;

    private List<Subnet> subnets;

    private String id;

    private String name;

    private Addresses addresses;

    private List<Link> links;

//    private Image image; // nova 返回的是string,解析会报错

    private Flavor flavor;

    private String accessIPv4;

    private String accessIPv6;

    @JsonProperty("config_drive")
    private String configDrive;

    private String status;

    private Integer progress;

    private Fault fault;

    @JsonProperty("tenant_id")
    private String tenantId;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("key_name")
    private String keyName;

    private String hostId;

    private String updated;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp created;

    private Map<String, String> metadata;

    @JsonProperty("security_groups")
    private List<SecurityGroup> securityGroups;

    @JsonProperty("OS-EXT-STS:task_state")
    private String taskState;

    @JsonProperty("OS-EXT-STS:power_state")
    private String powerState;

    @JsonProperty("OS-EXT-STS:vm_state")
    private String vmState;

    @JsonProperty("OS-EXT-SRV-ATTR:host")
    private String host;

    @JsonProperty("OS-EXT-SRV-ATTR:instance_name")
    private String instanceName;

    @JsonProperty("OS-EXT-SRV-ATTR:hypervisor_hostname")
    private String hypervisorHostname;

    @JsonProperty("OS-DCF:diskConfig")
    private String diskConfig;

    @JsonProperty("OS-EXT-AZ:availability_zone")
    private String availabilityZone;

    @JsonProperty("OS-SRV-USG:launched_at")
    private String launchedAt;

    @JsonProperty("OS-SRV-USG:terminated_at")
    private String terminatedAt;

    private String uuid;

    private String adminPass;

    @JsonProperty("hypervisor_dedicated")
    private String hypervisorDedicated;

    @JsonProperty("sys_disk")
    private Integer sysDisk;



    public Integer getSysDisk() {
        return sysDisk;
    }

    public void setSysDisk(Integer sysDisk) {
        this.sysDisk = sysDisk;
    }

    /**
     * @return the id
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setMetadata(Map<String, String> metadata) {
        this.metadata = metadata;
    }

    public void setAdminPass(String adminPass) {
        this.adminPass = adminPass;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @return the addresses
     */
    public Addresses getAddresses() {
        return addresses;
    }

    public void setAddresses(Addresses addresses) {
        this.addresses = addresses;
    }

    /**
     * @return the links
     */
    public List<Link> getLinks() {
        return links;
    }

    /**
     * @return the flavor
     */
    public Flavor getFlavor() {
        return flavor;
    }

    /**
     * @param flavor the flavor to set
     */
    public void setFlavor(Flavor flavor) {
        this.flavor = flavor;
    }

    /**
     * @return the accessIPv4
     */
    public String getAccessIPv4() {
        return accessIPv4;
    }

    /**
     * @return the accessIPv6
     */
    public String getAccessIPv6() {
        return accessIPv6;
    }

    /**
     * @return the configDrive
     */
    public String getConfigDrive() {
        return configDrive;
    }

    /**
     * @return the status
     */
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return the progress
     */
    public Integer getProgress() {
        return progress;
    }

    /**
     * @return the fault
     */
    public Fault getFault() {
        return fault;
    }

    /**
     * @return the tenantId
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * @return the userId
     */
    public String getUserId() {
        return userId;
    }

    /**
     * @return the keyName
     */
    public String getKeyName() {
        return keyName;
    }

    /**
     * @return the hostId
     */
    public String getHostId() {
        return hostId;
    }

    /**
     * @return the updated
     */
    public String getUpdated() {
        return updated;
    }

    /**
     * @return the created
     */
    public Timestamp getCreated() {
        return created;
    }

    public void setCreated(Timestamp created) {
        this.created = created;
    }

    /**
     * @return the metadata
     */
    public Map<String, String> getMetadata() {
        return metadata;
    }

    /**
     * @return the securityGroups
     */
    public List<SecurityGroup> getSecurityGroups() {
        return securityGroups;
    }

    /**
     * @return the taskState
     */
    public String getTaskState() {
        return taskState;
    }

    /**
     * @return the powerState
     */
    public String getPowerState() {
        return powerState;
    }

    /**
     * @return the vmState
     */
    public String getVmState() {
        return vmState;
    }

    /**
     * @return the host
     */
    public String getHost() {
        return host;
    }

    /**
     * @return the instanceName
     */
    public String getInstanceName() {
        return instanceName;
    }

    /**
     * @return the hypervisorHostname
     */
    public String getHypervisorHostname() {
        return hypervisorHostname;
    }

    /**
     * @return the diskConfig
     */
    public String getDiskConfig() {
        return diskConfig;
    }

    /**
     * @return the availabilityZone
     */
    public String getAvailabilityZone() {
        return availabilityZone;
    }

    /**
     * @return the launchedAt
     */
    public String getLaunchedAt() {
        return launchedAt;
    }

    /**
     * @return the terminatedAt
     */
    public String getTerminatedAt() {
        return terminatedAt;
    }

    /**
     * @return the uuid
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * @return the adminPass
     */
    public String getAdminPass() {
        return adminPass;
    }

    public String getHypervisorDedicated() {
        return hypervisorDedicated;
    }

    public void setHypervisorDedicated(String hypervisorDedicated) {
        this.hypervisorDedicated = hypervisorDedicated;
    }

    public void setLinks(List<Link> links) {
        this.links = links;
    }

    public void setAccessIPv4(String accessIPv4) {
        this.accessIPv4 = accessIPv4;
    }

    public void setAccessIPv6(String accessIPv6) {
        this.accessIPv6 = accessIPv6;
    }

    public void setConfigDrive(String configDrive) {
        this.configDrive = configDrive;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public void setFault(Fault fault) {
        this.fault = fault;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public void setUpdated(String updated) {
        this.updated = updated;
    }

    public void setSecurityGroups(List<SecurityGroup> securityGroups) {
        this.securityGroups = securityGroups;
    }

    public void setTaskState(String taskState) {
        this.taskState = taskState;
    }

    public void setPowerState(String powerState) {
        this.powerState = powerState;
    }

    public void setVmState(String vmState) {
        this.vmState = vmState;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public void setHypervisorHostname(String hypervisorHostname) {
        this.hypervisorHostname = hypervisorHostname;
    }

    public void setDiskConfig(String diskConfig) {
        this.diskConfig = diskConfig;
    }

    public void setAvailabilityZone(String availabilityZone) {
        this.availabilityZone = availabilityZone;
    }

    public void setLaunchedAt(String launchedAt) {
        this.launchedAt = launchedAt;
    }

    public void setTerminatedAt(String terminatedAt) {
        this.terminatedAt = terminatedAt;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public List<VirtualDisk> getVirtualDisks() {
        return virtualDisks;
    }

    public void setVirtualDisks(List<VirtualDisk> virtualDisks) {
        this.virtualDisks = virtualDisks;
    }

    public List<Subnet> getSubnets() {
        return subnets;
    }

    public void setSubnets(List<Subnet> subnets) {
        this.subnets = subnets;
    }


    public List<VolumesAttached> getVolumesAttacheds() {
        return volumesAttacheds;
    }

    public void setVolumesAttacheds(List<VolumesAttached> volumesAttacheds) {
        this.volumesAttacheds = volumesAttacheds;
    }

    @Override
    public String toString() {
        return "Server{"
                + "volumesAttacheds=" + volumesAttacheds
                + ", virtualDisks=" + virtualDisks
                + ", subnets=" + subnets
                + ", id='" + id + '\''
                + ", name='" + name + '\''
                + ", addresses=" + addresses
                + ", links=" + links
                + ", flavor=" + flavor
                + ", accessIPv4='" + accessIPv4 + '\''
                + ", accessIPv6='" + accessIPv6 + '\''
                + ", configDrive='" + configDrive + '\''
                + ", status='" + status + '\''
                + ", progress=" + progress
                + ", fault=" + fault
                + ", tenantId='" + tenantId + '\''
                + ", userId='" + userId + '\''
                + ", keyName='" + keyName + '\''
                + ", hostId='" + hostId + '\''
                + ", updated='" + updated + '\''
                + ", created=" + created
                + ", metadata=" + metadata
                + ", securityGroups=" + securityGroups
                + ", taskState='" + taskState + '\''
                + ", powerState='" + powerState + '\''
                + ", vmState='" + vmState + '\''
                + ", host='" + host + '\''
                + ", instanceName='" + instanceName + '\''
                + ", hypervisorHostname='" + hypervisorHostname + '\''
                + ", diskConfig='" + diskConfig + '\''
                + ", availabilityZone='" + availabilityZone + '\''
                + ", launchedAt='" + launchedAt + '\''
                + ", terminatedAt='" + terminatedAt + '\''
                + ", uuid='" + uuid + '\''
                + ", adminPass='" + adminPass + '\''
                + ", hypervisorDedicated='" + hypervisorDedicated + '\''
                + ", sysDisk=" + sysDisk
                + '}';
    }
}
