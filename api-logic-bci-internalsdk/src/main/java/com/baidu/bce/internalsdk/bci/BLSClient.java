package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.CreateBLSTaskRequest;
import com.baidu.bce.internalsdk.bci.model.CreateBLSTaskResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBLSTaskResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsAgentResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTaskNameResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTokenIDResponse;
import com.baidu.bce.internalsdk.bci.model.QueryBlsTokenResponse;
import com.baidu.bce.internalsdk.bci.model.CreateBlsTokenResponse;
import com.baidu.bce.internalsdk.bci.model.QueryAllBlsTaskResponse;
import com.baidu.bce.internalsdk.bci.model.CreateBlsTokenRequest;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import endpoint.EndpointManager;

import java.util.Arrays;

public class BLSClient extends BceClient {
    private static final String SERVICE_NAME = "BLS_SERVICE";

    public BLSClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    private BceInternalRequest createBLSRequestWithSignedHeaders() {
        BceInternalRequest bceInternalRequest =
            super.createAuthorizedRequestWithSignedHeaders(Arrays.asList("host", "x-bce-date"));
        return bceInternalRequest;
    }

    /******************************* registry ********************************/
    public CreateBLSTaskResponse createLogTask(CreateBLSTaskRequest request) {
        return createBLSRequestWithSignedHeaders()
            .path("/task")
            .post(Entity.json(request), CreateBLSTaskResponse.class);
    }

    public void deleteLogTask(String taskId) {
        createBLSRequestWithSignedHeaders().path("/task/" + taskId + "/stop").post();
    }

    public QueryBLSTaskResponse queryLogTask(String taskId) {
        return createBLSRequestWithSignedHeaders().path("/task/" + taskId).get(QueryBLSTaskResponse.class);
    }

    public QueryBlsTaskNameResponse getBlsTaskDetailByName(String blsTaskName) {
        return createBLSRequestWithSignedHeaders()
            .path("/task/util/checkTaskName?taskName=" + blsTaskName)
            .get(QueryBlsTaskNameResponse.class);
    }

    public QueryBlsTokenResponse queryAllBlsTokens() {
        return createBLSRequestWithSignedHeaders().path("/userToken").get(QueryBlsTokenResponse.class);
    }

    public CreateBlsTokenResponse createBlsToken(CreateBlsTokenRequest request) {
        return createBLSRequestWithSignedHeaders()
            .path("/userToken")
            .post(Entity.json(request), CreateBlsTokenResponse.class);
    }

    public QueryBlsTokenIDResponse queryBlsTokenInfoByID(String blsTokenID) {
        return createBLSRequestWithSignedHeaders().path("/userToken/" + blsTokenID).get(QueryBlsTokenIDResponse.class);
    }

    public QueryAllBlsTaskResponse queryAllBlsTask() {
        return createBLSRequestWithSignedHeaders().path("/task").get(QueryAllBlsTaskResponse.class);
    }

    public QueryBlsAgentResponse queryBlsAgent(String blsTaskID) {
        return createBLSRequestWithSignedHeaders()
            .path("/task/" + blsTaskID + "/host")
            .get(QueryBlsAgentResponse.class);
    }
}
