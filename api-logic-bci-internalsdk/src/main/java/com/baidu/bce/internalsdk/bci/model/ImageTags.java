package com.baidu.bce.internalsdk.bci.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ImageTags {
    private String namespace;
    private String repository;
    private List<Tags> tags;

    @Data
    @Accessors(chain = true)
    public static class Tags {
        private String name;
        private String digest;
        private String description;

    }
}
