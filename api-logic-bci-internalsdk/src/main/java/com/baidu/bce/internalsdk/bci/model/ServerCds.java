package com.baidu.bce.internalsdk.bci.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.sql.Timestamp;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ServerCds {
    private String id;
    private String name;
    private String count;
    private String size;
    private String description;
    @JsonProperty("snapshot_id")
    private String snapshotId;
    @JsonProperty("volume_type")
    private String volumeType;

    @JsonProperty("boot_index")
    private int bootIndex;

    // /dev/vdb
    private String device;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    @JsonProperty("created_time")
    private Timestamp createdTime;

    @JsonProperty("attachment_id")
    private String attachmentId;

    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }

    public String getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(String volumeType) {
        this.volumeType = volumeType;
    }

    public int getBootIndex() {
        return bootIndex;
    }

    public void setBootIndex(int bootIndex) {
        this.bootIndex = bootIndex;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Timestamp getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ServerCds{"
                + "id='" + id + '\''
                + ", name='" + name + '\''
                + ", count='" + count + '\''
                + ", size='" + size + '\''
                + ", description='" + description + '\''
                + ", snapshotId='" + snapshotId + '\''
                + ", volumeType='" + volumeType + '\''
                + ", bootIndex=" + bootIndex
                + ", device='" + device + '\''
                + ", createdTime=" + createdTime
                + ", attachmentId='" + attachmentId + '\''
                + ", status='" + status + '\''
                + '}';
    }
}
