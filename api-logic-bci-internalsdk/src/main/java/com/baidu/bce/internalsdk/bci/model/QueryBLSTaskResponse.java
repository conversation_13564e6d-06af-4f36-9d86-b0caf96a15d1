package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class QueryBLSTaskResponse {
    private TaskDetailGroup task;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public class TaskDetailGroup {
        private TaskDetail status;
        private BLSTaskConfig config;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class TaskDetail {
        private String id;
        private String name;
        private TaskType type;
        private TaskStatus status;
        private Date creationDateTime;
        private String taskVersion;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Accessors(chain = true)
    public enum TaskType {
        Log;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Accessors(chain = true)
    public enum TaskStatus {
        Initializing,
        Running,
        Paused,
        Stopping,
        Stopped,
        Abnormal;
    }
}

