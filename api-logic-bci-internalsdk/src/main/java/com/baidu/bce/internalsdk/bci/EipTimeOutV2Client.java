package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.EipResponse;
import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.eipv2.EipV2Client;
import com.baidu.bce.internalsdk.eipv2.model.EipInstanceCreateModel;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;


public class EipTimeOutV2Client extends EipV2Client {
    private static final String SERVICE_NAME = "EIP";
    private boolean isHostWithPort = false;
    private Integer eipCreateTimeOut = 25000;

    public EipTimeOutV2Client(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public EipTimeOutV2Client(String accessKey, String secretKey, boolean isHostWithPort, Integer eipCreateTimeOut) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
        this.isHostWithPort = isHostWithPort;
        this.eipCreateTimeOut = eipCreateTimeOut;
    }

    public EipTimeOutV2Client(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public BceInternalRequest createEipRequestTimedOut() {
        BceClientConfig becClientConfig = new BceClientConfig()
                .withReadTimeout(eipCreateTimeOut).withMaxConnTotal(400).withMaxConnPerRoute(400);
        BceInternalRequest bceInternalRequest = BceInternalClient.request(this.endpoint, becClientConfig)
                .authorization(this.accessKey, this.secretKey);
        if (!StringUtils.isEmpty(this.securityToken)) {
            bceInternalRequest.securityToken(this.securityToken);
        }
        bceInternalRequest.authorization(accessKey, secretKey,
                Arrays.asList(BceConstant.HOST, BceConstant.X_BCE_DATE));
        return bceInternalRequest;
    }

    public EipResponse createEipWithLongTimeOut(EipInstanceCreateModel createModel) {
        return this.createEipRequestTimedOut().path("/eip")
                .post(Entity.json(createModel), EipResponse.class);
    }


}
