package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;


@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Servers {
    private int count;

    @JsonProperty("subnet_id")
    private String subnetId;

    @JsonProperty("create_floatingip")
    private boolean createFloatingIp;
    @JsonProperty("flavor")
    private ServerFlavor serverFlavor;
    @JsonProperty("ebs")
    private List<ServerCds> serverCds;
    private String imageRef;
    private String name;
    @JsonProperty("push_log")
    private boolean pushLog;
    private String adminPass;
    @JsonProperty("ssh_keys")
    private SshKeys sshKeys;
    @JsonProperty("metadata")
    private Map<String, String> metadata;
    @JsonProperty("security_groups")
    private List<SecurityGroup> securityGroups;

    @JsonProperty(value = "os:scheduler_hints")
    private OsSchedulerHints osSchedulerHints;

    private List<String> nfs;

    @JsonProperty(value = "empty_dir")
    private List<String> emptyDir;

    @JsonProperty(value = "config_file")
    private List<PodConfigFile> configFile;

    @JsonProperty(value = "restart_policy")
    private String restartPolicy;

    private List<MapEntry> labels;

    private List<Container> containers;

    @JsonProperty(value = "bci_volumes")
    private List<BciVolume> bciVolumes;

    @JsonProperty(value = "bci_data_volumes")
    private List<BciVolume> bciDataVolumes;
}
