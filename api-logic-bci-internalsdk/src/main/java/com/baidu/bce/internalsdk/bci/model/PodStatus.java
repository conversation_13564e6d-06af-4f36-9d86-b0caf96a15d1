package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class PodStatus {

    @JsonProperty(value = "pod_uuid")
    private String podUuid;
    private String status;
    private long since;
    @JsonProperty(value = "container_status")
    private List<ContainerStatus> containerStatus;


    @Data
    public static class ContainerStatus {
        @JsonProperty(value = "container_id")
        private String containerId;
        @JsonProperty(value = "previous_state")
        private PreviousState previousState;
        @JsonProperty(value = "current_state")
        private CurrentState currentState;
        @JsonProperty(value = "restart_count")
        private int restartCount;
        @JsonProperty(value = "container_name")
        private String name;
    }
}
