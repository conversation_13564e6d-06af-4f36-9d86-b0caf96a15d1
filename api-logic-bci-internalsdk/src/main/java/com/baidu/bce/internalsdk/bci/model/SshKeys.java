package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * 密钥对对象，用于绑定和解绑
 * 对虚机所执行的密钥对更新包括：添加、删除、替换
 * 其中替换即是删除原有的并添加新的密钥对，故三个操作合为一个
 */
public class SshKeys {
    // 待添加的密钥对列表
    @JsonProperty("add")
    private List<String> toAddPublicKeys = new ArrayList<>();
    // 待删除的密钥对列表
    @JsonProperty("del")
    private List<String> toDeletePublicKeys = new ArrayList<>();

    public List<String> getToAddPublicKeys() {
        return toAddPublicKeys;
    }

    public void setToAddPublicKeys(List<String> toAddPublicKeys) {
        this.toAddPublicKeys = toAddPublicKeys;
    }

    public SshKeys withToAddPublicKeys(List<String> toAddPublicKeys) {
        this.toAddPublicKeys = toAddPublicKeys;
        return this;
    }

    public List<String> getToDeletePublicKeys() {
        return toDeletePublicKeys;
    }

    public void setToDeletePublicKeys(List<String> toDeletePublicKeys) {
        this.toDeletePublicKeys = toDeletePublicKeys;
    }

    public SshKeys withToDeletePublicKeys(List<String> toDeletePublicKeys) {
        this.toDeletePublicKeys = toDeletePublicKeys;
        return this;
    }

    @Override
    public String toString() {
        return "SshKeys{"
                + "toAddPublicKeys=" + toAddPublicKeys
                + ", toDeletePublicKeys=" + toDeletePublicKeys
                + '}';
    }
}
