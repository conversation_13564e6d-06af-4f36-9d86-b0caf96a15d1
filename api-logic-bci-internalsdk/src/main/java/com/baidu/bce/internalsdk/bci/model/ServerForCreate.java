package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;


public class ServerForCreate {

    private String source;

    private String action;

    @JsonProperty("transaction_id")
    private String transactionId;

    @JsonProperty("servers")
    private List<Servers> list;

    @JsonProperty("availability_zone")
    private String availableZone;

    private String type;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public List<Servers> getList() {
        return list;
    }

    public void setList(List<Servers> list) {
        this.list = list;
    }

    public String getAvailableZone() {
        return availableZone;
    }

    public void setAvailableZone(String availableZone) {
        this.availableZone = availableZone;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ServerForCreate{"
                + "source='" + source + '\''
                + ", action='" + action + '\''
                + ", transactionId='" + transactionId + '\''
                + ", list=" + list
                + ", availableZone='" + availableZone + '\''
                + ", type='" + type + '\''
                + '}';
    }
}
