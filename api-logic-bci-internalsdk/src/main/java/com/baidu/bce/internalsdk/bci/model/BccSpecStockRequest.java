package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class BccSpecStockRequest {
    @JsonProperty("spec")
    private String spec ;

    @JsonProperty("deploySetIds")
    private String[] deploySetIds ;  
}

