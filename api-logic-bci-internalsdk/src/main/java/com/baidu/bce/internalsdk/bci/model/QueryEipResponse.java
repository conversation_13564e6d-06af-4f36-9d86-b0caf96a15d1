package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class QueryEipResponse {
// See https://cloud.baidu.com/doc/EIP/s/Mjwvz2wnn#eipmodel
    private String name;
    private String eip;     // ip address
    private String eipId;   // short id
    private String status;
    private String instanceType;
    private String instanceId;
    private String routeType;
    private String instanceIp;
    private int bandwidthInMbps; 
    private String paymentTiming;
    private String billingMethod;
    private String createTime;
    private String expireTime;
    private String shareGroupId;
    private String eipInstanceType;
    private String region;
}
