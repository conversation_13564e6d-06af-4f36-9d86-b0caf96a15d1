package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.CCRImageResponse;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import endpoint.EndpointManager;

public class CCRClient extends BceClient {
    private static final String SERVICE_NAME = "CCR";

    public CCRClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public CCRClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    private BceInternalRequest createCceRequest() {
        return super.createAuthorizedRequest();
    }

    /******************************* registry ********************************/


    public CCRImageResponse listUserImage() {
        BceInternalRequest request = createCceRequest().path("/v1/ccr/repositories/user");
        return request.get(CCRImageResponse.class);
    }
}