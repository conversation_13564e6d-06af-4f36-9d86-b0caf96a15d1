package com.baidu.bce.internalsdk.bci.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

public class PreviousState {
    private String state;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    @JsonProperty(value = "container_start_time")
    private Date containerStartTime;
    @JsonProperty(value = "exit_code")
    private int exitCode;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    @JsonProperty(value = "container_finish_time")
    private Date containerFinishTime;
    @JsonProperty(value = "detail_status")
    private String detailStatus;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getContainerStartTime() {
        return containerStartTime;
    }

    public void setContainerStartTime(Date containerStartTime) {
        this.containerStartTime = containerStartTime;
    }

    public int getExitCode() {
        return exitCode;
    }

    public void setExitCode(int exitCode) {
        this.exitCode = exitCode;
    }

    public Date getContainerFinishTime() {
        return containerFinishTime;
    }

    public void setContainerFinishTime(Date containerFinishTime) {
        this.containerFinishTime = containerFinishTime;
    }

    public String getDetailStatus() {
        return detailStatus;
    }

    public void setDetailStatus(String detailStatus) {
        this.detailStatus = detailStatus;
    }
}
