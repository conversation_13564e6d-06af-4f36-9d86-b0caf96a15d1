package com.baidu.bce.internalsdk.bci;

import java.util.Arrays;

import com.baidu.bce.internalsdk.bci.model.BcmEventRequestWrapper;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.bci.model.BcmListEventsRequest;
import com.baidu.bce.internalsdk.bci.model.BcmListEventsResponse;

import com.baidu.bce.internalsdk.core.Entity;
import endpoint.EndpointManager;

import java.util.Map;
import java.util.HashMap;

public class BcmClient extends BceClient {
    public static final String SERVICE_NAME = "BCM_SERVICE";

    public BcmClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    private BceInternalRequest getBceInternalRequest() {
        BceInternalRequest bceInternalRequest = super.createAuthorizedRequestWithSignedHeaders(
                Arrays.asList("host", "x-bce-date"));
        return bceInternalRequest;
    }

    /**
     *
     * @param request
     * @return
     */
    public BcmListEventsResponse listEvents(BcmListEventsRequest request) {
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("pageNo", String.valueOf(request.getPageNo()));
        paramMap.put("pageSize", String.valueOf(request.getPageSize()));
        paramMap.put("startTime", request.getStartTime().toInstant().toString());
        paramMap.put("endTime", request.getEndTime().toInstant().toString());
        paramMap.put("accountId", request.getAccountId());
        paramMap.put("serviceName", "BCE_BCI");
        paramMap.put("resourceId", request.getResourceId());

        return this.getBceInternalRequest()
            .path("/event-api/v1/bce-event/list")
            .queryParams(paramMap).get(BcmListEventsResponse.class);
    }

    /**
     * 将事件推送到BCM服务器。
     *
     * @param bcmEvent 待推送的事件对象
     * @return 返回BCM返回的事件列表响应对象
     */
    public BcmListEventsResponse pushEvents(BcmEventRequestWrapper bcmEvent) {
        String accountId = bcmEvent.getAccountId();
        return this.getBceInternalRequest()
                .path(String.format("/event-api/v1/accounts/%s/services/BCE_BCI/events", accountId))
                .post(Entity.json(bcmEvent), BcmListEventsResponse.class);
    }
}
