package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ServerFlavor {
    private int cpu;
    private int memory;
    private int disk;
    private int ephemeral;
    @JsonProperty("volume_type")
    private String volumeType;

    public int getCpu() {
        return cpu;
    }

    public void setCpu(int cpu) {
        this.cpu = cpu;
    }

    public int getMemory() {
        return memory;
    }

    public void setMemory(int memory) {
        this.memory = memory;
    }

    public int getDisk() {
        return disk;
    }

    public void setDisk(int disk) {
        this.disk = disk;
    }

    public int getEphemeral() {
        return ephemeral;
    }

    public void setEphemeral(int ephemeral) {
        this.ephemeral = ephemeral;
    }

    public String getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(String volumeType) {
        this.volumeType = volumeType;
    }

    @Override
    public String toString() {
        return "ServerFlavor{"
                + "cpu=" + cpu
                + ", memory=" + memory
                + ", disk=" + disk
                + ", ephemeral=" + ephemeral
                + ", volumeType='" + volumeType + '\''
                + '}';
    }
}
