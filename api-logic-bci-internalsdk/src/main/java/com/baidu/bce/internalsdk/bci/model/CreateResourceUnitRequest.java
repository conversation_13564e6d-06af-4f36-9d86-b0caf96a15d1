package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateResourceUnitRequest {

    @JsonProperty("resource_unit")
    private ResourceUnit resourceUnit;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResourceUnit {
        private String id;
        @JsonProperty("available_zone")
        private String availableZone;
        @JsonProperty("group_id")
        private String groupId = "bci";
        private Integer cpu;
        private Integer memory;
        // disks 是数据盘，我们 emptyDir 应该是系统盘
        private List<Disks> disks;
        @JsonProperty("required_tags")
        private List<String> requiredTags;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Disks {
        private String type;
        @JsonProperty("capacity_gb")
        private int capacityGB;
        private boolean share;
    }
}