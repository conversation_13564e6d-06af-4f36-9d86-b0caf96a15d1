package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class BcmEventRequestWrapper {
    private String accountId;
    private String serviceName;
    private String region;
    private String resourceType;
    private String resourceId;
    private String eventId;
    private String eventType;
    private String eventLevel;
    private String eventAlias;
    private String eventAliasEn;
    private String timestamp;
    private String content;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Content {
        private String info;
        private String advice;
        private String reason;
        private String message;
        private String fieldPath;
    }
}