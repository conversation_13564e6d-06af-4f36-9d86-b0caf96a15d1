package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Comparator;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CCRImage {

    private String repositoryId;
    private String repositoryName;
    private String type;
    private Long projectId;
    private String projectName;
    private String description;
    private String location;
    private String address;
    private int pullCount;
    private int startCount;
    private int tagsCount;
    private Date createTime;
    private Date updateTime;
    private String dockerPullCmd;

    public static Comparator<CCRImage> getComparator(String compareBy) {
        Comparator<CCRImage> comparator;
        switch (compareBy) {
            case "name":
                comparator = new Comparator<CCRImage>() {
                    @Override
                    public int compare(CCRImage o1, CCRImage o2) {
                        return o1.getRepositoryName().compareTo(o2.getRepositoryName());
                    }
                };
                break;
            // 其他情况/默认情况 按照createTime排序
            default:
                comparator = new Comparator<CCRImage>() {
                    @Override
                    public int compare(CCRImage o1, CCRImage o2) {
                        return o1.getCreateTime().compareTo(o2.getCreateTime());
                    }
                };
        }
        return comparator;
    }
}
