package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class PodConfigFile {

    private String name;
    @JsonProperty(value = "conf_item")
    private List<MapEntry> configItem;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<MapEntry> getConfigItem() {
        return configItem;
    }

    public void setConfigItem(List<MapEntry> configItem) {
        this.configItem = configItem;
    }

    @Override
    public String toString() {
        return "PodConfigFile{"
                + "name='" + name + '\''
                + ", configItem=" + configItem
                + '}';
    }
}
