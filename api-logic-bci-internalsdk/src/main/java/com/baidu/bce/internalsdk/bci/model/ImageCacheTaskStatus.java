package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImageCacheTaskStatus {
    private String status;
    private String digest;
    private String reason;
    private Timestamp lastTransitionTime;


    public enum TaskStatus {
        Waiting,
        Pulling,
        Pulled,
        Failed
    }
}