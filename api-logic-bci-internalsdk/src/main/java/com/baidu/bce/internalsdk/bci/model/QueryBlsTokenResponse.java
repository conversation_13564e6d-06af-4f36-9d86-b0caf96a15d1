package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.ArrayList;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class QueryBlsTokenResponse {
    private int totalSize;
    private int offset;
    private int size;
    private List<TokenDetail> userTokens = new ArrayList<>();

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class TokenDetail {
        private String id;
        private String token;
        private String description;
    }
}
