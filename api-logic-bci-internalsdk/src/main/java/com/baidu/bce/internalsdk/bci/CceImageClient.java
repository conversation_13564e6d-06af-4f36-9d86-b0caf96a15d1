package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.DockerHubImageResponse;
import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.OfficialImageResponse;
import com.baidu.bce.internalsdk.bci.model.UserImageResponse;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

public class CceImageClient extends BceClient {
    private static final String SERVICE_NAME = "CCE_IMAGE";

    public CceImageClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public CceImageClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    private BceInternalRequest createCceRequest() {
        return super.createAuthorizedRequest();
    }

    /******************************* registry ********************************/


    public UserImageResponse listUserImage() {
        BceInternalRequest request = createCceRequest().path("/images/repositories");
        return request.get(UserImageResponse.class);
    }

    public OfficialImageResponse listOfficialImage(String keyword, String keywordType) {

        BceInternalRequest request = createCceRequest().path("/official/images");
        if (StringUtils.isNotBlank(keywordType) && StringUtils.isNotBlank(keyword)) {
            if (keywordType.equalsIgnoreCase("name")) {
                keywordType = "repository";
            }
            request.queryParam("keywordType", keywordType).queryParam("keyword", keyword);
        }
        return request.get(OfficialImageResponse.class);
    }

    public DockerHubImageResponse listDockerHubImage(String keyword, String keywordType) {

        BceInternalRequest request = createCceRequest().path("/docker/images");
        if (StringUtils.isNotBlank(keywordType) && StringUtils.isNotBlank(keyword)) {
            request.queryParam("keywordType", keywordType).queryParam("keyword", keyword);
        }
        return request.get(DockerHubImageResponse.class);
    }

    public ImageTags listImageVersions(String namespace, String repository) {
        return createCceRequest().path("/images/tags").queryParam("namespace", namespace)
                .queryParam("repository", repository)
                .get(ImageTags.class);
    }
}