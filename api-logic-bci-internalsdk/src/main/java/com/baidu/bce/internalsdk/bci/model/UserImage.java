package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class UserImage {

    private long id;
    private String name;
    private String namespace;
    @JsonProperty("public")
    private Boolean isPublic;
    @JsonProperty("created_at")
    private Date createTime;
    @JsonProperty("updated_at")
    private Date updateTime;
    private List<String> tags;
    private String address;

    public static Comparator<UserImage> getComparator(String compareBy) {
        Comparator<UserImage> comparator;
        switch (compareBy) {
            case "name":
                comparator = new Comparator<UserImage>() {
                    @Override
                    public int compare(UserImage o1, UserImage o2) {
                        return o1.getName().compareTo(o2.getName());
                    }
                };
                break;
            // 其他情况/默认情况 按照createTime排序
            default:
                comparator = new Comparator<UserImage>() {
                    @Override
                    public int compare(UserImage o1, UserImage o2) {
                        return o1.getCreateTime().compareTo(o2.getCreateTime());
                    }
                };
        }
        return comparator;
    }
}
