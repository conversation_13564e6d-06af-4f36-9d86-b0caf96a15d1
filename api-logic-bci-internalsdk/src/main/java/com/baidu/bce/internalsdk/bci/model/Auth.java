package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Auth {
    private String name;
    private String password;
    private String auth;
    @JsonProperty(value = "server_address")
    private String serverAddress;
    private String identity;
    @JsonProperty(value = "registry_token")
    private String registryToken;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAuth() {
        return auth;
    }

    public void setAuth(String auth) {
        this.auth = auth;
    }

    public String getServerAddress() {
        return serverAddress;
    }

    public void setServerAddress(String serverAddress) {
        this.serverAddress = serverAddress;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getRegistryToken() {
        return registryToken;
    }

    public void setRegistryToken(String registryToken) {
        this.registryToken = registryToken;
    }

    @Override
    public String toString() {
        return "Auth{"
                + "name='" + name + '\''
                + ", password='" + password + '\''
                + ", auth='" + auth + '\''
                + ", serverAddress='" + serverAddress + '\''
                + ", identity='" + identity + '\''
                + ", registryToken='" + registryToken + '\''
                + '}';
    }
}
