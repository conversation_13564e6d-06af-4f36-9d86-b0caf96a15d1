package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;

/**
 * Created by huping on 2019-07-18
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("os-getVNCConsole")
public class WebShellRequest {

    private String type;

    @JsonProperty(value = "webshell_exec_args")
    private ExecArgs args ;

    public WebShellRequest(String type, ExecArgs args) {
        this.type = type;
        this.args = args;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public ExecArgs getArgs() {
        return args;
    }

    public void setArgs(ExecArgs args) {
        this.args = args;
    }

    @Override
    public String toString() {
        return "WebShellRequest{" +
                "type='" + type + '\'' +
                ", args=" + args +
                '}';
    }
}
