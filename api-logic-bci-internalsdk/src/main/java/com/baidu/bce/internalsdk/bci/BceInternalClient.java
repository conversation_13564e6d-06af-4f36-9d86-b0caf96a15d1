package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.X509HostnameVerifier;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.glassfish.jersey.apache.connector.ApacheClientProperties;
import org.glassfish.jersey.apache.connector.ApacheConnectorProvider;
import org.glassfish.jersey.client.ClientConfig;
import org.glassfish.jersey.client.ClientProperties;
import org.glassfish.jersey.client.RequestEntityProcessing;
import org.glassfish.jersey.filter.LoggingFilter;
import org.glassfish.jersey.jackson.JacksonFeature;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.ClientRequestContext;
import javax.ws.rs.client.ClientRequestFilter;
import javax.ws.rs.ext.ContextResolver;
import java.io.IOException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

public abstract class BceInternalClient {

    private static ObjectMapper DEFAULT_MAPPER;
    private static ObjectMapper WRAPPED_MAPPER;

    private static Logger logger = Logger.getLogger(BceInternalClient.class.getName());

    private static Map<BceClientConfig, Client> clientMap = new ConcurrentHashMap<>();
    private static Timer timer = new Timer(true);

    static {
        initialize();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                for (Client client : clientMap.values()) {
                    PoolingHttpClientConnectionManager connManager = (PoolingHttpClientConnectionManager) client
                            .getConfiguration().getProperty(ApacheClientProperties.CONNECTION_MANAGER);
                    // when server close connection,tcp status is close_wait in client,because
                    // client don't close connection,so make cleaner thread.
                    connManager.closeExpiredConnections();
                }
            }
        }, 500, 10000);
    }

    private static void initialize() {
        try {
            DEFAULT_MAPPER = new ObjectMapper();
            DEFAULT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            DEFAULT_MAPPER.enable(SerializationFeature.INDENT_OUTPUT);
            DEFAULT_MAPPER.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
            DEFAULT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

            WRAPPED_MAPPER = new ObjectMapper();
            WRAPPED_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            WRAPPED_MAPPER.enable(SerializationFeature.INDENT_OUTPUT);
            WRAPPED_MAPPER.enable(SerializationFeature.WRAP_ROOT_VALUE);
            WRAPPED_MAPPER.enable(DeserializationFeature.UNWRAP_ROOT_VALUE);
            WRAPPED_MAPPER.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
            WRAPPED_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static BceInternalRequest request(String endpoint) {
        BceClientConfig becClientConfig = new BceClientConfig()
                .withReadTimeout(15000)
                // it is max concurrency to endpoint
                .withMaxConnTotal(400)
                // this equals maxConnection,because it can be removed when connection reached limited
                .withMaxConnPerRoute(400);
        return request(endpoint, becClientConfig);
    }

    public static BceInternalRequest request(String endpoint, BceClientConfig config) {
        if (!clientMap.containsKey(config)) {
            Client client = generateNewClient(config);
            clientMap.put(config, client);
        }
        BceInternalRequest request = new BceInternalRequest(clientMap.get(config));
        return request.endpoint(endpoint);
    }

    private static Client generateNewClient(BceClientConfig bceClientConfig) {
        ClientConfig clientConfig = getClientConfig(bceClientConfig);
        Client client = ClientBuilder.newBuilder()
                .withConfig(clientConfig)
                .build();

        client.property(ClientProperties.REQUEST_ENTITY_PROCESSING, RequestEntityProcessing.BUFFERED);
        client.property(ClientProperties.CONNECT_TIMEOUT, 5000);
        client.property(ClientProperties.READ_TIMEOUT, bceClientConfig.getReadTimeout());
        client.register(new JacksonFeature()).register(new ContextResolver<ObjectMapper>() {
            @Override
            public ObjectMapper getContext(Class<?> type) {
                return type.getAnnotation(JsonRootName.class) == null ? DEFAULT_MAPPER : WRAPPED_MAPPER;
            }
        });
        client.register(new ClientRequestFilter() {
            @Override
            public void filter(ClientRequestContext requestContext) throws IOException {
                requestContext.getHeaders().remove("Content-Language");
                requestContext.getHeaders().remove("Content-Encoding");
            }
        });
        LoggingFilter filter = new LoggingFilter(logger, true);
        client.register(filter);

        for (Class<?> messageBodyReaderClass : bceClientConfig.getMessageBodyReaderClasses()) {
            client.register(messageBodyReaderClass);
        }
        for (Class<?> messageBodyWriterClass : bceClientConfig.getMessageBodyWriterClasses()) {
            client.register(messageBodyWriterClass);
        }

        return client;
    }

    private static SSLContext getSSLContext() {
        TrustManager[] trustAllCerts = new TrustManager[]{ new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] arg0, String arg1)
                    throws CertificateException {
            }
            @Override
            public void checkServerTrusted(X509Certificate[] arg0, String arg1)
                    throws CertificateException {
            }
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        } };
        try {
            final SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
            logger.fine("add ssl context");
            return sslContext;
        } catch (java.security.GeneralSecurityException ex) {
            logger.warning(ex.toString());
            throw new RuntimeException(ex.getMessage(), ex);
        }
    }

    private static ClientConfig getClientConfig(BceClientConfig bceClientConfig) {
        // use apache connector to sent PATCH HttpMethod
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.connectorProvider(new ApacheConnectorProvider());

        // 1. set connection pool，default connection Manager has bug, not close
        // 2. baidu bgw close idle connetion > 90s, connection pool must drop idl connection itself to fix TCP retransmition bug
        // 3. ? keepalive
        PoolingHttpClientConnectionManager connectionManager =
                new PoolingHttpClientConnectionManager(
                        getRegistryForConn(),
                        null,
                        null,
                        null,
                        60,
                        TimeUnit.SECONDS);
        connectionManager.setMaxTotal(bceClientConfig.getMaxConnTotal());
        connectionManager.setDefaultMaxPerRoute(bceClientConfig.getMaxConnPerRoute());
        clientConfig.property(ApacheClientProperties.CONNECTION_MANAGER, connectionManager);

        return clientConfig;
    }

    private static Registry<ConnectionSocketFactory> getRegistryForConn() {

        final X509HostnameVerifier allHostsValid = new X509HostnameVerifier() {
            @Override
            public void verify(String s, SSLSocket sslSocket) throws IOException {
            }

            @Override
            public void verify(String s, X509Certificate x509Certificate) throws SSLException {
            }

            @Override
            public void verify(String s, String[] strings, String[] strings2) throws SSLException {
            }

            @Override
            public boolean verify(String host, SSLSession sslSession) {
                return true;
            }
        };

        // Create a registry of custom connection socket factories for supported
        // protocol schemes.
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(getSSLContext(),
                        new String[] {"SSLv3", "TLSv1", "TLSv1.1", "TLSv1.2"}, null,  allHostsValid))
                .build();
        logger.info("Registry https: SSLv3, TLSv1, TLSv1.1, TLSv1.2");
        return socketFactoryRegistry;
    }
}
