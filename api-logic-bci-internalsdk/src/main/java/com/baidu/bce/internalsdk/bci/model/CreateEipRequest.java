package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CreateEipRequest {
    private String name;
    // 创建eip个数。此参数只对批量创建的接口有效
    private int count;
    // EIP线路类型，包含标准BGP（BGP）和增强BGP（BGP_S），默认标准BGP。
    private String routeType;
    // 公网带宽，单位为Mbps
    private int bandwidthInMbps;
    // 订单信息
    private Billing billing;

    // 支持创建 EIP同时开通自动续费，取值为 month 获 year （默认 month）
    private String autoRenewTimeUnit;
    // 支持创建 EIP同时开通自动续费，根据autoRenewTimeUnit的取值有不同的范围，month 为1到9，year 为1到3。
    private int autoRenewTime;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class Billing {
        // 付款时间，预支付（Prepaid）和后支付（Postpaid）
        private String paymentTiming;
        // 计费方式，按流量（ByTraffic）、按带宽（ByBandwidth）
        private String billingMethod;
        // 保留信息，支付方式为后支付时不需要设置，预支付时必须设置
        private Reservation reservation;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        @Accessors(chain = true)
        public static class Reservation {
            private int reservationLength;
            private String reservationTimeUnit;
        }
    }

}
