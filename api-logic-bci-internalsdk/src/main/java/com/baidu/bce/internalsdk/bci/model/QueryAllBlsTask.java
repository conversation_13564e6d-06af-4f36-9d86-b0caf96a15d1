package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)

public class QueryAllBlsTask {
    private int offset;
    private int size;
    private String taskIdPattern;
    private String  taskNamePattern;
    private String  orderBy;
    private String  orderDir;
    private TaskStatus taskStatus;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Accessors(chain = true)
    public enum TaskStatus {
        Initializing,
        Running,
        Paused,
        Stopping,
        Stopped,
        Abnormal;
    }

}
