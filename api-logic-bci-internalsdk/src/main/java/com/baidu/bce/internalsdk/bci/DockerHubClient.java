package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.DockerHubImageResponse;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageTagResponse;
import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import endpoint.EndpointManager;

public class DockerHubClient extends BceInternalClient {
    private static final String SERVICE_NAME = "DOCKER_HUB";

    public DockerHubClient() {
    }

    private BceInternalRequest createCceRequest() {
        BceClientConfig becClientConfig = new BceClientConfig()
                .withReadTimeout(50000)
                // it is max concurrency to endpoint
                .withMaxConnTotal(400)
                // this equals maxConnection,because it can be removed when connection reached limited
                .withMaxConnPerRoute(400);
        return request(EndpointManager.getEndpoint(SERVICE_NAME), becClientConfig);
    }

    public DockerHubImageResponse listDockerHubImage(int page, int pageSize) {
        BceInternalRequest request = createCceRequest().path("/v2/repositories/library/")
                .queryParam("page", page).queryParam("page_size", pageSize);
        return request.get(DockerHubImageResponse.class);
    }

    public DockerHubImageTagResponse listDockerHubImageTag(String name, int page, int pageSize) {
        BceInternalRequest request = createCceRequest().path("/v2/repositories/library/" + name + "/tags")
                .queryParam("page", page).queryParam("page_size", pageSize);
        return request.get(DockerHubImageTagResponse.class);
    }
}
