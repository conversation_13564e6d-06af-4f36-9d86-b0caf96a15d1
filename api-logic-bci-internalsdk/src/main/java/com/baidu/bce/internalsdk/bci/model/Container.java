package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class Container {

    private List<Port> ports;

    private List<Mount> mounts;

    private String name;
    private Image image;
    private float cpu;
    private int memory;
    private List<MapEntry> envs;
    private List<String> command;
    private List<String> args;
    @JsonProperty(value = "working_dir")
    private String workingDir;


    public List<Port> getPorts() {
        return ports;
    }

    public void setPorts(List<Port> ports) {
        this.ports = ports;
    }

    public List<Mount> getMounts() {
        return mounts;
    }

    public void setMounts(List<Mount> mounts) {
        this.mounts = mounts;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Image getImage() {
        return image;
    }

    public void setImage(Image image) {
        this.image = image;
    }

    public float getCpu() {
        return cpu;
    }

    public void setCpu(float cpu) {
        this.cpu = cpu;
    }

    public int getMemory() {
        return memory;
    }

    public void setMemory(int memory) {
        this.memory = memory;
    }

    public List<MapEntry> getEnvs() {
        return envs;
    }

    public void setEnvs(List<MapEntry> envs) {
        this.envs = envs;
    }

    public List<String> getCommand() {
        return command;
    }

    public void setCommand(List<String> command) {
        this.command = command;
    }

    public List<String> getArgs() {
        return args;
    }

    public void setArgs(List<String> args) {
        this.args = args;
    }

    public String getWorkingDir() {
        return workingDir;
    }

    public void setWorkingDir(String workingDir) {
        this.workingDir = workingDir;
    }


    @Override
    public String toString() {
        return "Container{"
                + "ports=" + ports
                + ", mounts=" + mounts
                + ", name='" + name + '\''
                + ", image=" + image
                + ", cpu=" + cpu
                + ", memory=" + memory
                + ", envs=" + envs
                + ", command=" + command
                + ", args=" + args
                + ", workingDir='" + workingDir + '\''
                + '}';
    }

    public static class Port {
        private int port;
        private String protocol;

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getProtocol() {
            return protocol;
        }

        public void setProtocol(String protocol) {
            this.protocol = protocol;
        }

        @Override
        public String toString() {
            return "Port{"
                    + "port=" + port
                    + ", protocol='" + protocol + '\''
                    + '}';
        }
    }

    public static class Mount {
        @JsonProperty(value = "container_path")
        private String containerPath;
        @JsonProperty(value = "host_path")
        private String hostPath;
        @JsonProperty(value = "readonly")
        private Boolean readOnly = Boolean.FALSE;

        public String getContainerPath() {
            return containerPath;
        }

        public void setContainerPath(String containerPath) {
            this.containerPath = containerPath;
        }

        public String getHostPath() {
            return hostPath;
        }

        public void setHostPath(String hostPath) {
            this.hostPath = hostPath;
        }

        public Boolean getReadOnly() {
            return readOnly;
        }

        public void setReadOnly(Boolean readOnly) {
            this.readOnly = readOnly;
        }

        @Override
        public String toString() {
            return "Mount{"
                    + "containerPath='" + containerPath + '\''
                    + ", hostPath='" + hostPath + '\''
                    + ", readOnly=" + readOnly
                    + '}';
        }
    }
}
