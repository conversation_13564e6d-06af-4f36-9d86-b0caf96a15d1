package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class BccSpecStockResponse {

    @JsonProperty("bccStocks")
    private List<BccStock> bccStocks ;

    @Data
    public static class BccStock {
        @JsonProperty("logicalZone")
        private String logicalZone;

        @JsonProperty("flavorId")
        private String flavorId;

        @JsonProperty("inventoryQuantity")
        private Integer inventoryQuantity;

        @JsonProperty("updatedTime")
        private String updatedTime;

        @JsonProperty("collectionTime")
        private String collectionTime;
    }
    
}
