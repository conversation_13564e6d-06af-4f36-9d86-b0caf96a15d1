package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class BLSLogStoreResponse {
    private Date creationDateTime;
    private Date lastModifiedTime;
    private String logStoreName;
    private Integer retention;
}
