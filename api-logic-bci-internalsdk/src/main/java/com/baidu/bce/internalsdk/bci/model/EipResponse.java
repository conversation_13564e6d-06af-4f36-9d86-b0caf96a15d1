package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EipResponse {
    private List<SingleEipResponse> eips;

    public List<SingleEipResponse> getEips() {
        return eips;
    }

    public void setEips(List<SingleEipResponse> eips) {
        this.eips = eips;
    }

    public static class SingleEipResponse {
        private String eip;
        private String instanceId;
        private String status;
        private String encryptId;
        private String allocationId;

        public String getEip() {
            return eip;
        }

        public void setEip(String eip) {
            this.eip = eip;
        }

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getEncryptId() {
            return encryptId;
        }

        public void setEncryptId(String encryptId) {
            this.encryptId = encryptId;
        }

        public String getAllocationId() {
            return allocationId;
        }

        public void setAllocationId(String allocationId) {
            this.allocationId = allocationId;
        }

    }

}
