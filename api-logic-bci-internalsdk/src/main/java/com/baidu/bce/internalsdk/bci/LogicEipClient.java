package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.BindEipToBciRequest;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.eipv2.model.EipForCreate;
import com.baidu.bce.internalsdk.eipv2.model.EipListResponse;
import com.baidu.bce.internalsdk.order.model.OrderCreateRequest;
import com.baidu.bce.internalsdk.bci.model.CreateEipRequest;
import com.baidu.bce.internalsdk.bci.model.CreateEipResponse;
import com.baidu.bce.internalsdk.bci.model.CreateEipsResponse;
import com.baidu.bce.internalsdk.bci.model.QueryEipListResponse;

import endpoint.EndpointManager;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class LogicEipClient extends BceClient {

    private static final String SERVICE_NAME = "LOGIC_EIP";
    private static final String BASE_URL = "/api/logical/eip/v1";

    private boolean isHostWithPort = false;

    private int maxRetryCount = 0;

    public LogicEipClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public LogicEipClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
    }

    public LogicEipClient(String accessKey, String secretKey, boolean isHostWithPort) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
        this.isHostWithPort = isHostWithPort;
    }

    public LogicEipClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public final BceInternalRequest createEipRequest() {
        BceInternalRequest bceInternalRequest = createRequest().setIsHostWithPort(this.isHostWithPort)
                .authorization(accessKey, secretKey, Arrays.asList(BceConstant.HOST, BceConstant.X_BCE_DATE));
        if (!StringUtils.isEmpty(securityToken)) {
            bceInternalRequest.securityToken(securityToken);
        }
        return bceInternalRequest;
    }

    public OrderCreateRequest getEipCreateRequestForLogicalBCC(EipForCreate eipForCreate) {
        return createEipRequest().path(BASE_URL + "/union_vm_item")
                .post(Entity.json(eipForCreate), OrderCreateRequest.class);
    }


    public void unbindEip(String eip) {
        createEipRequest().path(BASE_URL + "/unbind/" + eip).post();
    }

    /**
     * 绑定eip
     * @param eip eipRequest
     *
     * @return null
     */
    public void bindEip(String eip, BindEipToBciRequest bindEipRequest) {
        createEipRequest().path(new StringBuilder(BASE_URL).append("/bind/").append(eip).toString())
                .post(Entity.json(bindEipRequest));
    }
    /**
     * 强制释放已绑定bcc的eip，只提供给bcc关联释放，禁止其他方调用
     * @param eip
     */
    public void forceReleaseEip(String eip) {
        createEipRequest().path(BASE_URL + "/force_release/" + eip).post();
    }

    public boolean forceReleaseEipV2(String eip) {
        EipListResponse eipListResponse = queryEip(eip);
        if (eipListResponse == null) {
            return false;
        }
        if (eipListResponse.getEipList().isEmpty()) {
            return true;
        }
        if (eipListResponse.getEipList().get(0).getStatus().equals("creating")) {
            return false;
        }
        forceReleaseEip(eip);
        return true;
    }

    // create an eip instance
    public CreateEipResponse createEip(CreateEipRequest createEipRequest) {
        // CreateEipRequest createEipRequest = new CreateEipRequest();
        // createEipRequest.setName("eip_test_bci2");
        // createEipRequest.setRouteType("BGP_S");
        // createEipRequest.setBandwidthInMbps(6);

        // // CreateEipRequest.Billing billing = createEipRequest.new Billing();
        // CreateEipRequest.Billing billing = new CreateEipRequest.Billing();
        // billing.setPaymentTiming("Postpaid");
        // billing.setBillingMethod("ByBandwidth");
        // createEipRequest.setBilling(billing);

        CreateEipResponse response = createEipRequest()
            .path(BASE_URL + "/create")
            .post(Entity.json(createEipRequest), CreateEipResponse.class);
        return response;
    }

    //  create batch eips
    public CreateEipsResponse createBatchEips(CreateEipRequest createEipRequest) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("action", "batch");
        CreateEipsResponse response = createEipRequest()
            .path(BASE_URL + "/create")
            .queryParams(paramMap)
            .setMaxRetryCount(this.maxRetryCount)
            .post(Entity.json(createEipRequest), CreateEipsResponse.class);
        return response;
    }

    public EipListResponse queryEip(String eipIP) {
        if (StringUtils.isEmpty(eipIP)) {
            return null;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("eip", eipIP);
        return createEipRequest()
            .path(BASE_URL + "/list")
            .queryParams(paramMap)
            .get(EipListResponse.class);
    }
   
    public QueryEipListResponse queryEipV2(String eipIP) {
        if (StringUtils.isEmpty(eipIP)) {
            return null;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("eip", eipIP);
        return createEipRequest()
            .path(BASE_URL + "/list")
            .queryParams(paramMap)
            .get(QueryEipListResponse.class);
    }

    public QueryEipListResponse queryEipBindedV2(String instanceType) {
        if (StringUtils.isEmpty(instanceType)) {
            return null;
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("instanceType", instanceType);
        return createEipRequest()
            .path(BASE_URL + "/list")
            .queryParams(paramMap)
            .get(QueryEipListResponse.class);
    }

}
