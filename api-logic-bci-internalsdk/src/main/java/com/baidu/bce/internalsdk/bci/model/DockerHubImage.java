package com.baidu.bce.internalsdk.bci.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Comparator;

@Data
@Accessors(chain = true)
public class DockerHubImage {
    private String name;
    private String description;
    private String iconBosAddr;

    public static Comparator<DockerHubImage> getComparator(String compareBy) {
        Comparator<DockerHubImage> comparator;
        switch (compareBy) {
            case "description":
                comparator = new Comparator<DockerHubImage>() {
                    @Override
                    public int compare(DockerHubImage o1, DockerHubImage o2) {
                        return o1.getDescription().compareTo(o2.getDescription());
                    }
                };
                break;
            default:
                comparator = new Comparator<DockerHubImage>() {
                    @Override
                    public int compare(DockerHubImage o1, DockerHubImage o2) {
                        return o1.getName().compareTo(o2.getName());
                    }
                };
        }
        return comparator;
    }
}
