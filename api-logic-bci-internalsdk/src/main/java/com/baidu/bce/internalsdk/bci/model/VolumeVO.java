package com.baidu.bce.internalsdk.bci.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class VolumeVO implements Cloneable {
    private Long id = 0L;
    private String volumeId = "";  // Api Service返回用户的id
    private String volumeUuid = ""; // cinder 中保存的volume 的id
    private String serial = "";  // 磁盘序列号，用于磁盘在虚机挂载中的辨识
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp attachTime; // 挂载时间
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp expireTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp extendTime; // 上次变配时间
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime; // 删除时间
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp recycleTime; // 回收时间
    private String userId = ""; // 用户id
    private String tenantId = ""; // 项目id
    private Integer sizeGb = 0; // 快照大小
    private String device = ""; // 挂载点
    private String instanceUuid = ""; // 实例id
    private Integer instanceType = 0; // 实例类型（普通实例或专属实例）
    private String instanceId = ""; // 实例api id
    private String internalIp = ""; // 实例内网Ip
    private String floatingIp = ""; // 实例floating ip
    private String instanceName = ""; // 实例名称
    private String instanceSpec = ""; // 实例规格
    private String snapshotId = ""; // 基于哪个快照建的
    private String snapshotUuid = ""; // 基于哪个快照建的
    private String name = ""; // 名称
    private String description = ""; // 描述
    private String attachStatus = ""; // 挂载状态
    private String orderUuid = ""; //  订单uuid
    private String resourceUuid = ""; // 资源uuid
    private String productType = ""; //
    private Integer volumeType;  // 磁盘类型, CDS为1, SYSTEM为2, EPHEMERAL为3
    private Integer storageType;  // 存储类型, SSD为1, SATA为2
    private String status = "";
    private String extra = "";
    private String attachmentUuid = "";
    private String source = "";
    private Boolean isSystemVolume;
    private String taskStatus = "";
    // 订单状态，用预付费<==>后付费转换状态
    private String orderStatus = "";
    private List<Tag> tags; // 所绑定的tag信息
    private Integer snapshotNum = 0; // 磁盘当前具有的快照数量
    private String zoneId;
    private String logicalZone = "zoneA";
    private String physicalZone = "zoneA";
    private String instanceStatus = ""; // 实例状态
    private Integer instanceRootGb = 0; // 实例rootGb
    private Boolean instanceRocV2 = Boolean.FALSE; // 是否是rocv2虚机
    private String region;
    private String imageType = "";
    private String osName = "";
    /**
     * true rocv2 后的镜像虚机resize后是否需要脚本手动操作
     */
    private int rocv2RebootResize = 0;
    private Boolean resizeNeedScript = Boolean.FALSE;
    private String application = "";
    private String aspId = "";
    private String aspName = "";
    private int[] timePoints;
    private int[] repeatWeekdays;
    private String timePointsJson;
    private String repeatWeekdaysJson;
    private int retentionDays;
    private String aspStatus = "";
    private String aspTaskStatus = "";
    private int fakeRocv1 = 0;
    private String updatedBy = "";

    private String extendStatus = "";
    private String chainId = "";
    private String chainUuid = "";
    private String chainStatus = "";
    private BigDecimal chainSize;
    private int autoSnapCount;
    private int manualSnapCount;
    private int snapshotTotal;
    private int snapshotManualRatio;

    private String encryptKey = "";

    private int resizeCount;

    @JsonIgnore
    private int cdsDownSizingCnt;
    private Boolean enableAutoRenew = false;
    private Integer autoRenewTime;


    public String handleEmptyValue(String oldValue) {
        return org.apache.commons.lang.StringUtils.isEmpty(oldValue) ? "" : oldValue;
    }

    public Timestamp handleEmptyValue(Timestamp oldValue) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Timestamp defaultTime = null;
        try {
            defaultTime = new Timestamp(sdf.parse("1971-01-02 00:00:01").getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return oldValue == null ? defaultTime : oldValue;
    }
}