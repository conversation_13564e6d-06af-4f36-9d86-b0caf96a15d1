package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class QueryEniResponse {
    private String eniId;
    private String name;
    private String vpcId;
    private String subnetId;
    private String zoneName;
    private String description;
    private String createdTime;
    private String macAddress;
    private String status;
    private String networkInterfaceTrafficMode;
    private List<PrivateIpItem>  privateIpSet = new ArrayList<>();
    private List<String> securityGroupIds = new ArrayList<>();
    private List<String> enterpriseSecurityGroupIds = new ArrayList<>();

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class PrivateIpItem{
        private Boolean primary;
        private String privateIpAddress;
        private String publicIpAddress;
    }
}



