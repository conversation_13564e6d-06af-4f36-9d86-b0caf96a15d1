package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CCRImageResponse {
    private String orderBy = "";
    private String orders = "";
    private int pageNo = 1;
    private int pageSize = 0;
    private int totalCount = 0;
    private List<CCRImage> result = new ArrayList<>();
}
