package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.CNImageCacheRequest;
import com.baidu.bce.internalsdk.bci.model.ImageCacheTaskStatus;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponse;
import com.baidu.bce.internalsdk.core.Entity;
import org.apache.commons.lang.StringUtils;

public class ContainerManagerClient extends BceClient {
    private static final String CM_PORT = "8297";

    public static String getCMEndpoint(String host) {
        return "http://" + host + ":" + CM_PORT;
    }

    public ContainerManagerClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    private BceInternalRequest createCMRequest() {
        BceInternalRequest request = super.createAuthorizedRequest();
        request.setIsHostWithPort(true);
        return request;
    }

    public String getEndpoint() {
        return this.endpoint;
    }

    public BceInternalResponse getPodLog(String podUUID, String containerName, Integer limitBytes,
                                         Integer tailLines, String sinceTime, Integer sinceSeconds,
                                         Boolean timestamps, Boolean previous, Boolean follow) {
        BceInternalRequest request = createCMRequest().path("/api/v1/pod/" + podUUID + "/" + containerName + "/log");

        if (null != limitBytes) {
            request.queryParam("limitBytes", limitBytes);
        }
        if (null != tailLines) {
            request.queryParam("tailLines", tailLines);
        }
        if (StringUtils.isNotEmpty(sinceTime)) {
            request.queryParam("sinceTime", sinceTime);
        }
        if (null != sinceSeconds) {
            request.queryParam("sinceSeconds", sinceSeconds);
        }
        if (null != timestamps) {
            request.queryParam("timestamps", timestamps);
        }
        if (null != previous) {
            request.queryParam("previous", previous);
        }
        if (null != follow) {
            request.queryParam("follow", follow);
        }

        return request.getWitResponse();
    }

    public void createImageCache(CNImageCacheRequest imageCacheRequest) {
        BceInternalRequest request = createCMRequest().path("/api/v1/image/pulltask");
        request.post(Entity.json(imageCacheRequest));
    }

    public ImageCacheTaskStatus getImageCache(String taskId) {
        BceInternalRequest request = createCMRequest().path("/api/v1/image/pulltask/" + taskId);
        return request.get(ImageCacheTaskStatus.class);
    }
}
