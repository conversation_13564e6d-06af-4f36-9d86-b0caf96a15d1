package com.baidu.bce.internalsdk.bci;

import com.baidu.bce.internalsdk.bci.model.AvailableResourceRequest;
import com.baidu.bce.internalsdk.bci.model.AvailableResourceResponse;
import com.baidu.bce.internalsdk.bci.model.CreateResourceUnitRequest;
import com.baidu.bce.internalsdk.bci.model.CreateServersResponse;
import com.baidu.bce.internalsdk.bci.model.PodStatusResponse;
import com.baidu.bce.internalsdk.bci.model.PushLogRequest;
import com.baidu.bce.internalsdk.bci.model.ResourceGroupRequest;
import com.baidu.bce.internalsdk.bci.model.ResourceGroupResponse;
import com.baidu.bce.internalsdk.bci.model.Server;
import com.baidu.bce.internalsdk.bci.model.ServerForCreate;
import com.baidu.bce.internalsdk.bci.model.TransactionDetailResponse;
import com.baidu.bce.internalsdk.bci.model.Volume;
import com.baidu.bce.internalsdk.bci.model.WebShellRequest;
import com.baidu.bce.internalsdk.bci.model.WebshellResponse;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import endpoint.EndpointManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class PodClient extends BceClient {
    private String token;
    private static final String SERVICE_NAME = "BCC";

    private static final Logger LOGGER = LoggerFactory.getLogger(PodClient.class);

    private static final Long UTC_TIME = 8 * 60 * 60 * 1000L;

    public PodClient() {
        super(EndpointManager.getEndpoint(SERVICE_NAME));
    }

    public PodClient(String token, String accessKey, String secretKey) {
        this(EndpointManager.getEndpoint(SERVICE_NAME), token, accessKey, secretKey);

    }

    public PodClient(String endpoint, String token) {
        this(endpoint, token, null, null);
    }

    public PodClient(String endpoint, String token, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
        this.token = token;
    }

    protected BceInternalRequest getBceInternalRequest() {
        return createRequest().token(token).securityToken(this.securityToken);
    }


    /** han 接口 **/
    public CreateServersResponse createServers(ServerForCreate serverForCreate) {
        return getBceInternalRequest().path("/servers").post(Entity.json(serverForCreate), CreateServersResponse.class);
    }

    public TransactionDetailResponse showTransaction(String transactionId) {
        return getBceInternalRequest().path("/transactions/" + transactionId).get(TransactionDetailResponse.class);
    }

    // 获取用户的资源池
    public ResourceGroupResponse queryResourceGroup(ResourceGroupRequest request) {
        return getBceInternalRequest().path("/get_group").post(Entity.json(request), ResourceGroupResponse.class);
    }


    /** nova 接口 **/
    public Server getServer(String serverId) {
        return getBceInternalRequest().path("/servers/" + serverId)
                .get(Server.class);
    }

    public PodStatusResponse getPodStatus(Date date) {

        return getBceInternalRequest().path("/pods/info").queryParam("since", convertToUtcTime(date.getTime()))
                .get(PodStatusResponse.class);
    }

    public void deletePod(String podId) {
        getBceInternalRequest().path("/servers/" + podId).delete();
    }

    public Volume getVolume(String volumeId) {
        return getBceInternalRequest().path("/volumes/" + volumeId).get(Volume.class);
    }

    private Long convertToUtcTime(Long millisecond) {
        // nova 返回的是 大于 since 的结果，如果更新时间正好 等于 since，就不会返回，所以这里额外-1s。
        // console 的时间取整逻辑跟nova不一样，保险起见，所以再 -1s
        return (millisecond - UTC_TIME) / 1000 - 2;
    }

    public WebshellResponse getWebUrl(String podUuid , WebShellRequest request) {
        String url = "/servers/" + podUuid + "/action";
        return getBceInternalRequest().path(url).post(Entity.json(request), WebshellResponse.class);
    }

    public void pushLog(String podUuid) {
        PushLogRequest request = new PushLogRequest().setPushLog("true");
        getBceInternalRequest().path("/servers/" + podUuid + "/action").post(Entity.json(request));
    }

    public AvailableResourceResponse queryBCIAvailableResource(AvailableResourceRequest request) {
        return getBceInternalRequest().path("/os-hypervisors/resource_available_get")
                .post(Entity.json(request), AvailableResourceResponse.class);
    }

    // 创建资源套餐
    public void createResourceUnit(CreateResourceUnitRequest request) {
        getBceInternalRequest().path("/os-hypervisors/resource_unit_create")
                .post(Entity.json(request));
    }
}
