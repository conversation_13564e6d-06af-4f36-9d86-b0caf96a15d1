package com.baidu.bce.internalsdk.bci.model;

import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CreateBLSTaskRequest {
    private String name;
    private BLSTaskConfig config;
    private List<Host> hosts;
    private List<Tag> tags;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class Host {
        private String hostId;
    }
}
