package com.baidu.bce.internalsdk.bci.model;

public class Image {

    private String name;
    private String version;
    private Auth auth;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Auth getAuth() {
        return auth;
    }

    public void setAuth(Auth auth) {
        this.auth = auth;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "Image{"
                + "name='" + name + '\''
                + ", version='" + version + '\''
                + ", auth=" + auth
                + '}';
    }
}
