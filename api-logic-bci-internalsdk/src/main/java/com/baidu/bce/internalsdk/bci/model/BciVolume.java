package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class BciVolume {
    @JsonProperty(value = "name")
    private String name;
    @JsonProperty(value = "type")
    private String type;
    @JsonProperty(value = "size")
    private Integer size;
    @JsonProperty(value = "block")
    private String block;
    @JsonProperty(value = "fs")
    private FS fs;
    @JsonProperty(value = "volume_source")
    private VolumeSource volumeSource;

    private Integer count = 1;
    private String description;
    @JsonProperty(value = "snapshot_id")
    private String snapshotId;
    @JsonProperty(value = "volume_type")
    private String volumeType;
//    @JsonProperty(value = "boot_index")
//    private int bootIndex;
//    @JsonProperty(value = "encrypt_key")
//    private String encryptKey;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class FS {
        @JsonProperty(value = "fs_type")
        private String fsType = "ext4";
        @JsonProperty(value = "mount_flags")
        private List<String> mountFlags;
        @JsonProperty(value = "force_format")
        private Boolean forceFormat = false;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class VolumeSource {
        @JsonProperty(value = "cds")
        private Cds cds;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Accessors(chain = true)
    public static class Cds {
        @JsonProperty(value = "uuid")
        String uuid;
    }
}

