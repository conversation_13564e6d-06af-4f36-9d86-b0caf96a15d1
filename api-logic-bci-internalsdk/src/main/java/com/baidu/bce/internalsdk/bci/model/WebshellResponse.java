package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonRootName;

/**
 * Created by huping on 2019-07-18
 */
@JsonRootName("console")
public class WebshellResponse {
    private String type;

    private String url;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return "WebshellResponse{" +
                "type='" + type + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}
