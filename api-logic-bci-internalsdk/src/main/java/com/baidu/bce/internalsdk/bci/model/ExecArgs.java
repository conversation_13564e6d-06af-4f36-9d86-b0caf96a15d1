package com.baidu.bce.internalsdk.bci.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by huping on 2019-07-18
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExecArgs {
    @JsonProperty(value = "container_id")
    private String containerId;
    private boolean tty = true;
    private boolean stdin = true;
    private boolean stdout = true;
    private boolean stderr = false;
    private List<String> command =  new ArrayList<>();

    public ExecArgs(String containerId, boolean tty, boolean stdin, List<String> command) {
        this.containerId = containerId;
        this.tty = tty;
        this.stdin = stdin;
        this.command = command;
    }

    public String getContainerId() {
        return containerId;
    }

    public void setContainerId(String containerId) {
        this.containerId = containerId;
    }

    public boolean isTty() {
        return tty;
    }

    public void setTty(boolean tty) {
        this.tty = tty;
    }

    public boolean isStdin() {
        return stdin;
    }

    public void setStdin(boolean stdin) {
        this.stdin = stdin;
    }

    public boolean isStdout() {
        return stdout;
    }

    public void setStdout(boolean stdout) {
        this.stdout = stdout;
    }

    public boolean isStderr() {
        return stderr;
    }

    public void setStderr(boolean stderr) {
        this.stderr = stderr;
    }

    public List<String> getCommand() {
        return command;
    }

    public void setCommand(List<String> command) {
        this.command = command;
    }

    @Override
    public String toString() {
        return "ExecArgs{" +
                "containerId='" + containerId + '\'' +
                ", tty=" + tty +
                ", stdin=" + stdin +
                ", stdout=" + stdout +
                ", stderr=" + stderr +
                ", command=" + command +
                '}';
    }
}
