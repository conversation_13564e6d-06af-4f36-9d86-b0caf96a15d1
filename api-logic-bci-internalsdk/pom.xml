<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>api-logic-bci-root</artifactId>
        <groupId>com.baidu.bce</groupId>
        <version>${api-logic-bci-version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api-logic-bci-internalsdk</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-eipv2</artifactId>
            <version>1.0.308.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-billing</artifactId>
            <version>1.0.2045.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.plat.cloudtrail</groupId>
            <artifactId>bce-plat-web-framework-cloudtrail</artifactId>
            <version>*******-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>
