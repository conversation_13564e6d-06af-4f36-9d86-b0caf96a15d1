Global:
  version: 2.0

Default:
  profile: [dev]

Profiles:
  - profile:
    name: dev
    mode: AGENT
    environment:
      cluster: DECK_CENTOS6U3_K3
    build:
      command: sh build.sh -q
    artifacts:
      release: true

  - profile:
    name: mvndeploy
    mode: AGENT
    environment:
      cluster: CMC_STANDARD
    tools:
      - maven: 3.0.4
      - oraclejdk: 1.8.0
    build:
      command: sh build-deploy.sh $AGILE_RELEASE_VERSION
    artifacts:
      release: false
