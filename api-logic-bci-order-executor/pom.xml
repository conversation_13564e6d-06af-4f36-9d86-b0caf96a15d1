<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>api-logic-bci-root</artifactId>
        <groupId>com.baidu.bce</groupId>
        <version>${api-logic-bci-version}</version>
    </parent>

    <artifactId>api-logic-bci-order-executor</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-service</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-servicev2</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>

        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-web-framework-iam</artifactId>
            <version>1.3.11.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-core</artifactId>
            <version>1.0.34.5</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-console-trail-service</artifactId>
            <version>1.0.12.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>plat-user-log-sdk</artifactId>
            <version>1.0.54.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-zone-sdk</artifactId>
            <version>1.0.7.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-vpc-external-sdk</artifactId>
            <version>1.0.772.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-internalsdk</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-sdk</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-quota-sdk</artifactId>
            <version>1.0.0.126668</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-user-settings-sdk</artifactId>
            <version>1.0.7.1</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.4</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-executor</artifactId>
            <version>1.0.0.65643</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-billing</artifactId>
            <version>1.0.2180.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-sts</artifactId>
            <version>1.2.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-servicecatalog-sdk</artifactId>
            <version>1.0.147.2</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>1.6.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-osp-internal-sdk-crm</artifactId>
            <version>1.0.0.156126</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-ses</artifactId>
            <version>1.0.0.34272</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-tag-sdk</artifactId>
            <version>1.0.45.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.plat.messages</groupId>
            <artifactId>sdk</artifactId>
            <version>1.0.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>plat-log-trace-sdk</artifactId>
            <version>1.0.42.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-aop</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-order-executor-sdk</artifactId>
            <version>1.0.52.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-service-bus-sdk</artifactId>
            <version>1.0.7.1</version>
        </dependency>

        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-image-sdk</artifactId>
            <version>1.0.177.3</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bci-dao</artifactId>
            <version>${api-logic-bci-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-bcc-sdk</artifactId>
            <version>1.0.990.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>bp-resource-manager-client</artifactId>
            <version>2.0.108.765</version>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <version>1.6.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>1.6.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-web-framework-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-asyncwork-sdk</artifactId>
            <version>*******</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-eip-sdk</artifactId>
            <version>********</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baidu.bce</groupId>
                    <artifactId>bce-logical-asyncwork-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>bp-proxy-client</artifactId>
            <version>2.0.98.8f725f8</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>bp-auditing-sdk</artifactId>
            <version>2.0.228.124648</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-zone-sdk</artifactId>
            <version>*********</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>sp-usage-package-client</artifactId>
            <version>2.0.ReservedPackage.2023022414</version>
        </dependency>
    </dependencies>
</project>
