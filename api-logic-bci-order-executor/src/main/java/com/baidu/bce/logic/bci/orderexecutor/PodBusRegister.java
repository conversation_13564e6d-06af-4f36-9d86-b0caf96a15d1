package com.baidu.bce.logic.bci.orderexecutor;

import com.baidu.bce.internalsdk.iam.model.AccessKey;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.plat.webframework.endpoint.SDKEndpointConfiguration;
import com.baidu.bce.plat.webframework.iam.service.IAMService;
import com.baidu.bce.service.bus.sdk.BusClient;
import com.baidu.bce.service.bus.sdk.util.BusConstants;
import com.baidu.bce.service.bus.sdk.util.InetAddressHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnExpression("${pod.bus.enabled:false}")
public class PodBusRegister implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodBusRegister.class);

    @Value("${pod.bus.registered:true}")
    private boolean podBusRegistered;

    @Value("${pod.eip.bus.registered:true}")
    private boolean podEipBusRegistered;

    @Value("${server.port}")
    private String port;

    @Value(("${bce.logical.region}"))
    private String region;

    @Autowired
    private IAMService iamService;

    @Autowired
    private IamLogicService iamLogicService;

    @Autowired
    private SDKEndpointConfiguration sdkEndpointConfiguration;

    @Override
    public void afterPropertiesSet() throws Exception {
        AccessKey accessKey = iamLogicService.getEnabledConsoleAccessKey();
        BusClient busClient = new BusClient(accessKey.getAccess(), accessKey.getSecret());

        String endpoint = InetAddressHelper.getHostAddress() + ":" + port;
        if (podBusRegistered) {
            busClient.registerService(BusConstants.SERVICE_TYPE_CONSOLE, "BCI",
                    endpoint, region, null);
            LOGGER.info("Register success : {}.", "console, BCI, " + endpoint);
        } else {
            busClient.unregisterService(BusConstants.SERVICE_TYPE_CONSOLE, "BCI", endpoint);
            LOGGER.info("Unregister success : {}.", "console, BCI, " + endpoint);
        }

        if (podEipBusRegistered) {
            busClient.registerService(BusConstants.SERVICE_TYPE_CONSOLE, "BCI__EIP", endpoint,
                    region, null);
            LOGGER.info("Register success : {}.", "console, BCI__EIP, " + endpoint);
        } else {
            busClient.unregisterService(BusConstants.SERVICE_TYPE_CONSOLE, "BCI__EIP", endpoint);
            LOGGER.info("Unregister success : {}.", "console, BCI__EIP, " + endpoint);
        }
    }

}
