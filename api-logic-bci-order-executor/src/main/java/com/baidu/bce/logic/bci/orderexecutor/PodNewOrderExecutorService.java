package com.baidu.bce.logic.bci.orderexecutor;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.service.orderexecute.PodNewOrderExecutorServiceV1;
import com.baidu.bce.logic.bci.servicev2.orderexecute.PodNewOrderExecutorServiceV2;
import com.baidu.bce.logic.bci.servicev2.orderexecute.ReservedInstanceOrderExecutorService;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.service.OrderExecutorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Service
public class PodNewOrderExecutorService implements OrderExecutorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PodNewOrderExecutorService.class);


    @Autowired
    private PodNewOrderExecutorServiceV1 podNewOrderExecutorServiceV1;

    @Autowired
    public PodNewOrderExecutorServiceV2 podNewOrderExecutorServiceV2;

    @Autowired
    private ReservedInstanceOrderExecutorService reservedInstanceOrderExecutorService;

    @Value("${bci.fakeorder.enable:false}")
    private boolean enableFakeOrder;

    @Override
    public ExecutionResult execute(OrderClient orderClient, ResourceClient resourceClient, Order order) {
        LOGGER.debug("PodNewOrderExecutorService order execute callback, orderid is:{}", order.getUuid());
        if (enableFakeOrder) {
            LOGGER.debug("PodNewOrderExecutorService enable fake order executor, so ignore order system execute " +
                            "callback, orderid is: {}",
                    order.getUuid());
            return null;
        }

        if (isV1Order(order)) {
            LOGGER.debug("PodNewOrderExecutorService order execute callback, orderid is:{},it's v1 order",
                    order.getUuid());
            return podNewOrderExecutorServiceV1.execute(orderClient, resourceClient, order);
        } else if (isReservedInstanceOrder(order)) {
            return reservedInstanceOrderExecutorService.execute(orderClient, resourceClient, order);
        } else {
            // 不是v1的订单,都走v2
            ExecutionResult executionResult = new ExecutionResult();
            LOGGER.debug("PodNewOrderExecutorService order execute callback, orderid is:{},it's v2 order",
                    order.getUuid());
            LOGGER.debug("PodNewOrderExecutorService v2 order process execute with actrator begin. order:{}",
                    order.getUuid());
//            order.getAccountId(): 主账号ID
//            order.getUserId(): 子账号ID
//            String accountId = order.getAccountId();
            podNewOrderExecutorServiceV2.fillOrderItemExtraWithPodExtra(order);
            boolean isImageCacheReq = podNewOrderExecutorServiceV2.checkIsImageCacheRequest(order);
            if (isImageCacheReq) {
                executionResult = podNewOrderExecutorServiceV2.executeCreateImcWithoutLock(orderClient,
                        resourceClient, order, PodNewOrderExecutorServiceV2.ORDER_SYSTEM_CALLBACK);
            } else {
                executionResult = podNewOrderExecutorServiceV2.executeWithoutLock(orderClient,
                        resourceClient, order, PodNewOrderExecutorServiceV2.ORDER_SYSTEM_CALLBACK);
            }
            LOGGER.debug("PodNewOrderExecutorService v2 order process execute with actrator end. order:{}",
                    order.getUuid());
            return executionResult;
        }
    }

    @Override
    public ExecutionResult check(OrderClient orderClient, ResourceClient resourceClient, Order order) {
        LOGGER.debug("PodNewOrderExecutorService order check callback, orderid is:{}", order.getUuid());
        if (enableFakeOrder) {
            LOGGER.debug("PodNewOrderExecutorService enable fake order executor, so ignore order system check " +
                            "callback, orderid is:{}",
                    order.getUuid());
            return null;
        }
        if (isV1Order(order)) {
            LOGGER.debug("PodNewOrderExecutorService order check callback, orderid is:{},it's v1 order",
                    order.getUuid());
            return podNewOrderExecutorServiceV1.check(orderClient, resourceClient, order);
        } else if (isReservedInstanceOrder(order)) {
            return reservedInstanceOrderExecutorService.check(orderClient, resourceClient, order);
        } else {
            LOGGER.debug("PodNewOrderExecutorService order check callback, orderid is:{},it's v2 order",
                    order.getUuid());
            LOGGER.debug("PodNewOrderExecutorService v2 order process check with actrator begin. order:{}",
                    order.getUuid());
//            order.getAccountId(): 主账号ID
//            order.getUserId(): 子账号ID
//            String accountId = order.getAccountId();
            podNewOrderExecutorServiceV2.fillOrderItemExtraWithPodExtra(order);
            ExecutionResult executionResult = new ExecutionResult();
            boolean isImageCacheReq = podNewOrderExecutorServiceV2.checkIsImageCacheRequest(order);
            if (isImageCacheReq) {
                executionResult = podNewOrderExecutorServiceV2.checkImcCreateWithoutLock(orderClient,
                    resourceClient, order, PodNewOrderExecutorServiceV2.ORDER_SYSTEM_CALLBACK);
            } else {
                executionResult = podNewOrderExecutorServiceV2.checkWithoutLock(orderClient,
                    resourceClient, order, PodNewOrderExecutorServiceV2.ORDER_SYSTEM_CALLBACK);
            }
            LOGGER.debug("PodNewOrderExecutorService v2 order process check with actrator end. order:{}",
                    order.getUuid());
            return executionResult;
        }
    }

    public boolean isV1Order(Order order) {
        return podNewOrderExecutorServiceV1.isV1Order(order.getUuid());
    }

    public boolean isReservedInstanceOrder(Order order) {
        if (order.getSubProductType().equals("ReservedPackage")) {
            return true;
        }
        return false;
    }
}