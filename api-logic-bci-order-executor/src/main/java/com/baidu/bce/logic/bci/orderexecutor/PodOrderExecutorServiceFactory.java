package com.baidu.bce.logic.bci.orderexecutor;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.order.executor.sdk.service.OrderExecutorService;
import com.baidu.bce.order.executor.sdk.service.OrderExecutorServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PodOrderExecutorServiceFactory implements OrderExecutorServiceFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodOrderExecutorServiceFactory.class);

    @Autowired
    private OrderExecutorService bciNewOrderExecutorService;

    @Override
    public OrderExecutorService createOrderExecutorService(Order order) throws Exception {
        OrderType type = order.getType();
        LOGGER.info("Order Type : {}.", type);
        switch (type) {
            case NEW:
                return bciNewOrderExecutorService;
            case RENEW:
                return bciNewOrderExecutorService;
            default:
                throw new Exception("The orderexecute type is unsupported : " + type + ".");
        }
    }

}