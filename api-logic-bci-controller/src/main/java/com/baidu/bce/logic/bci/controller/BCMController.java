package com.baidu.bce.logic.bci.controller;

import com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.service.model.BCMListPodRequest;
import com.baidu.bce.logic.bci.service.model.BCMListPodResponse;
import com.baidu.bce.logic.bci.service.model.BCMPodContainerResponse;
import com.baidu.bce.logic.bci.service.model.BCMPodDetailRequest;
import com.baidu.bce.logic.bci.service.model.PodForBCM;
import com.baidu.bce.logic.bci.service.pod.BCMPodService;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({BCMController.BASE_URL_V1})
public class BCMController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BCMController.class);


    public static final String BASE_URL_V1 = "/v1/bcm";

    @Autowired
    private BCMPodService bcmPodService;

    @RequestMapping(value = "/pod/list", method = RequestMethod.POST)
    @ApiOperation(value = "容器组列表: BCM 侧边栏使用")
    public BCMListPodResponse listPods(@RequestBody BCMListPodRequest request) {
        BCMListPodResponse pageResultResponse = null;
        try {
            pageResultResponse = bcmPodService.listPods(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    @RequestMapping(value = "/pod/detail", method = RequestMethod.POST)
    @ApiOperation(value = "查询容器组详情")
    public PodForBCM podDetail(@RequestBody BCMPodDetailRequest request) {
        PodForBCM podDetail = null;
        try {
            podDetail = bcmPodService.podDetail(request.getId());
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return podDetail;
    }

    @RequestMapping(value = "/pod/containers", method = RequestMethod.POST)
    @ApiOperation(value = "指标查看")
    public BCMPodContainerResponse podContainers(@RequestBody BCMListPodRequest request) {
        BCMPodContainerResponse response = null;
        try {
            response = bcmPodService.listPodContainer(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = "/resgroup/pod/list", method = RequestMethod.POST)
    @ApiOperation(value = "容器组列表,bcm 实例组使用")
    public BCMListPodResponse listPodForResGroup(@RequestBody BCMListPodRequest request) {
        BCMListPodResponse pageResultResponse = null;
        try {
            pageResultResponse = bcmPodService.listPods(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }
}
