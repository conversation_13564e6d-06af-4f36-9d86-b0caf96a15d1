package com.baidu.bce.logic.bci.controller;

import com.baidu.bce.internalsdk.bci.model.CCRImage;
import com.baidu.bce.internalsdk.bci.model.DockerHubImage;
import com.baidu.bce.internalsdk.bci.model.OfficialImage;
import com.baidu.bce.internalsdk.bci.model.PodEventPO;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.internalsdk.bci.model.WebshellUrl;
import com.baidu.bce.internalsdk.core.BceInternalResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.bcc.sdk.model.common.IDListRequest;
import com.baidu.bce.logic.bci.controller.model.BciEipBind;
import com.baidu.bce.logic.bci.dao.common.model.WebShell;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.service.log.LogUtil;
import com.baidu.bce.logic.bci.service.model.BciCreateResponse;
import com.baidu.bce.logic.bci.service.model.BciQuota;
import com.baidu.bce.logic.bci.service.model.IOrderItem;
import com.baidu.bce.logic.bci.service.model.LeakagePodDeleteRequest;
import com.baidu.bce.logic.bci.service.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.service.model.PodDetail;
import com.baidu.bce.logic.bci.service.model.PodForDisplay;
import com.baidu.bce.logic.bci.service.model.PodListRequest;
import com.baidu.bce.logic.bci.service.model.PodListResponse;
import com.baidu.bce.logic.bci.service.model.PodNumberRequest;
import com.baidu.bce.logic.bci.service.model.PodNumberResponse;
import com.baidu.bce.logic.bci.service.model.PodPushLogRequest;
import com.baidu.bce.logic.bci.service.model.PodVpcResponse;
import com.baidu.bce.logic.bci.service.pod.PodService;
import com.baidu.bce.logic.bci.service.util.PodUtils;
import com.baidu.bce.logic.bci.service.util.permission.IdPermission;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import static com.baidu.bce.logic.bci.service.log.PodOperation.createPod;

@RestController
@RequestMapping({BciController.BCI_BASE_URL_COMPATIBLE, BciController.BCI_BASE_URL_V1})
public class BciController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BciController.class);

    public static final String BCI_BASE_URL_COMPATIBLE = "/api/logical/bci/v1/pod";

    public static final String BCI_BASE_URL_V1 = "/v1/pod";
    public static final String LIST_BY_PAGE_PARAM = "manner=page";
    public static final String LIST_BY_PAGE = "/list";
    public static final String LIST_BY_UPDATED_TIME = "/list/updatedTime";
    public static final String LIST_POD_EVENT_BY_PAGE = "/listPodEvent";
    public static final String BCI_USER_IMAGE_LIST = "/userImage";
    public static final String BCI_DOCKER_HUB_IMAGE_LIST = "/dockerHubImage";
    public static final String BCI_OFFICIAL_IMAGE_LIST = "/officialImage";
    public static final String BCI_CCR_IMAGE_LIST = "/ccrImage";
    public static final String BCI_DELETE_API_URL = "/delete";
    public static final String BCI_PUSH_LOG_API_URL = "/pushLog";
    public static final String BCI_LIST_DOWNLOAD = "/download";
    public static final String BCI_LOG = "/log";
    public static final String BCI_EIP_BIND = "/eip/bind";
    public static final String BCI_EIP_UNBIND = "/eip/unbind";
    public static final String BCI_QUOTA = "/quota";
    public static final String BCI_DOCKER_HUB_IMAGE_TAG = "/image/tags";
    public static final String BCI_POD_CREATE = "/create";
    public static final String BCI_CONFIG_FILE_DOWNLOAD = "/configFile/download";
    public static final String LIST_BY_UUID = "/listPodsByUuids";
    public static final String BCI_WEBSHELL = "/webshell";
    public static final String IMAGE_CACHE = "/imageCache";

    @Autowired
    private PodService podService;

    private static class FilterMap extends HashMap<String, String> {
    }

    @RequestMapping(value = LIST_BY_PAGE, method = RequestMethod.GET, params = LIST_BY_PAGE_PARAM)
    @ApiOperation(value = "容器组列表: 通过pageNo方式分页")
    public LogicPageResultResponse<PodPO> listPodByPage(
            @RequestParam(required = false) String cceId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String keywordType,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filters) {
        PodListRequest listRequest =
                new PodListRequest(keyword, keywordType, order, orderBy, pageNo, pageSize, cceId);
        LOGGER.debug("list bci[page], request data is {}", listRequest);

        LogicPageResultResponse<PodPO> pageResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }
            pageResultResponse = podService.listPodsWithPageByMultiKey(listRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    @RequestMapping(value = LIST_BY_UPDATED_TIME, method = RequestMethod.GET)
    @ApiOperation(value = "容器组列表: updated time 大于给定时间的 pod 列表")
    public LogicPageResultResponse<PodPO> listPodByUpdatedTime(
            @RequestParam(required = true) long since,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String keywordType) {

        LogicPageResultResponse<PodPO> pageResultResponse = null;
        try {
            pageResultResponse = podService.listPodsByUpdatedTime(keywordType, keyword, since);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    // 暂时去除鉴权逻辑，
    @RequestMapping(value = "/{podId}", method = RequestMethod.GET)
//    @PermissionVertify(service = {PermissionConstant.SERVICE_BCI}, permission = {PermissionConstant.BCI_READ})
    @ApiOperation(value = "查询容器组详情")
    public PodDetail podDetail(@PathVariable @IdPermission String podId,
                               @RequestParam(required = false, defaultValue = "") String from) {
        PodDetail podDetail = null;
        try {
            podDetail = podService.podDetail(podId);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return podDetail;
    }

    @RequestMapping(value = "/vpcInfos/{podId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取容器组关联的网络信息")
    public EdpResultResponse<PodVpcResponse> getVpcAndSubnetByPodId(
            @PathVariable @IdPermission String podId) {
        Assert.notNull(podId, "please input query parameters!");
        EdpResultResponse<PodVpcResponse> result = new EdpResultResponse<>();
        result.setResult(podService.getVpcAndSubnetByPodId(podId));

        return result;
    }
    /**
     * 下载配置文件
     * 根据podId、name、path下载配置文件，并设置响应头以便浏览器下载
     * 
     * @param podId pod的唯一标识
     * @param name 配置文件的名称
     * @param path 配置文件的路径
     * @param servletResponse HttpServletResponse对象，用于设置响应头
     * @return 返回下载的配置文件内容
     * @throws 无
     */

    @RequestMapping(value = BCI_CONFIG_FILE_DOWNLOAD, method = RequestMethod.GET,
            produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ApiOperation(value = "下载configFile")
    public byte[] downloadConfigFile(@RequestParam String podId,
                                     @RequestParam String name,
                                     @RequestParam String path,
                           HttpServletResponse servletResponse) {

        byte[] download = null;
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String date = df.format(new Date());
        try {

            String fileName = URLEncoder.encode(path + "/configFile", "UTF-8");
            LOGGER.debug("downLoad peerConn file name is {}", fileName);
            servletResponse.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName);
            servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
            download =  podService.downloadConfigFile(podId, name, path);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return download;
    }

    @ApiOperation(value = "删除容器组")
//    @PermissionVertify(service = {PermissionConstant.SERVICE_BCI}, permission = {PermissionConstant.BCI_READ})
    @RequestMapping(value = BCI_DELETE_API_URL, method = RequestMethod.POST)
    public void deletePod(@RequestBody @Valid @IdPermission PodBatchDeleteRequest deleteRequest,
                             @RequestParam(required = false, defaultValue = "") String from) {
        try {
            podService.deletePod(deleteRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "开启推送日志")
//    @PermissionVertify(service = {PermissionConstant.SERVICE_BCI}, permission = {PermissionConstant.BCI_READ})
    @RequestMapping(value = BCI_PUSH_LOG_API_URL, method = RequestMethod.PUT)
    public void pushLog(@RequestBody @Valid @IdPermission PodPushLogRequest request,
                          @RequestParam(required = false, defaultValue = "") String from) {
        try {
            podService.pushLog(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(value = BCI_POD_CREATE, method = RequestMethod.POST)
    @ApiOperation(value = "创建容器组")
    public BciCreateResponse createInstance(@RequestBody BaseCreateOrderRequestVo<IOrderItem> request,
                                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create pod, request data is {}, from is {}", request, from);
        LogUtil.operationLog(request.getItems(), createPod, "");
        BciCreateResponse bciCreateResponse = null;
        try {

            bciCreateResponse = podService.createPod(request, from);
            LogUtil.operationLog(bciCreateResponse, createPod, bciCreateResponse.getPodIds(),
                    bciCreateResponse.getOrderId());
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return bciCreateResponse;
    }
    /**
      * 下载资源列表
      * 该函数用于处理资源列表下载请求，支持分页、过滤和关键字搜索，返回CSV格式的字节流。
      * 
      * @param cceId 云企业ID，用于筛选资源
      * @param keyword 搜索关键字
      * @param keywordType 搜索关键字的类型
      * @param order 排序字段
      * @param orderBy 排序方式
      * @param pageNo 页码，默认为1
      * @param pageSize 每页数量，默认为1000
      * @param filters 过滤条件，JSON格式字符串
      * @param servletResponse HttpServletResponse对象，用于设置响应头
      * @return 返回CSV格式的字节流
      * @throws BceException 抛出BceException异常，当操作BCE服务时发生错误
      * @throws Exception 抛出Exception异常，当其他未知错误发生时
      */

    @RequestMapping(value = BCI_LIST_DOWNLOAD, method = RequestMethod.GET,
            produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ApiOperation(value = "下载资源列表")
    public byte[] download(@RequestParam(required = false) String cceId,
                           @RequestParam(required = false) String keyword,
                           @RequestParam(required = false) String keywordType,
                           @RequestParam(required = false) String order,
                           @RequestParam(required = false) String orderBy,
                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                           @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
                           @RequestParam(required = false, defaultValue = "") String filters,
                           HttpServletResponse servletResponse) {

        PodListRequest listRequest =
                new PodListRequest(keyword, keywordType, order, orderBy, pageNo, pageSize, cceId);
        LOGGER.debug("list bci[page], request data is {}", listRequest);

        byte[] download = null;
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String date = df.format(new Date());
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }

            String fileName = URLEncoder.encode("pod_" + date + "_list.csv", "UTF-8");
            LOGGER.debug("downLoad peerConn file name is {}", fileName);
            // 这个header 由console来加  此处加的话 到console取不到
            servletResponse.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName);
            servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
            download =  podService.download(listRequest);
        } catch (BceException e) {
            LOGGER.error("List bci error", e);
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return download;
    }
    /**
      * 用户CCR镜像列表查询
      * 分页方式查询用户CCR镜像列表，支持搜索和排序
      * 
      * @param keyword 搜索关键字
      * @param keywordType 搜索关键字类型
      * @param order 排序方式，默认为desc
      * @param orderBy 排序字段，默认为createTime
      * @param pageNo 分页页码，必填
      * @param pageSize 分页大小，默认为1000
      * @return CCR镜像列表分页结果
      * @throws BceException 抛出BceException异常
      * @throws Exception 抛出其他异常
      */

    @RequestMapping(value = BCI_CCR_IMAGE_LIST, method = RequestMethod.GET, params = { "pageNo" })
    @ApiOperation(value = "用户CCR镜像列表, pageNo分页方式,支持搜索,排序")
    public LogicPageResultResponse<CCRImage> listCCRImages(
            @RequestParam(required = false, defaultValue = "") String keyword,
            @RequestParam(required = false, defaultValue = "") String keywordType,
            @RequestParam(required = false, defaultValue = "desc") String order,
            @RequestParam(required = false, defaultValue = "createTime") String orderBy,
            @RequestParam(required = true, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug("list images of user[{}] by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}", keywordType, keyword, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<CCRImage> result = null;
        try {
            result = podService.listCCRImage(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }
    /**
      * 用户镜像列表查询
      * 支持分页、搜索、排序功能
      * 
      * @param keyword 搜索关键词
      * @param keywordType 关键词类型
      * @param order 排序方式
      * @param orderBy 排序字段
      * @param pageNo 当前页码
      * @param pageSize 每页条数
      * @return 用户镜像列表分页结果
      * @throws 无
      */

    @RequestMapping(value = BCI_USER_IMAGE_LIST, method = RequestMethod.GET, params = { "pageNo" })
    @ApiOperation(value = "用户镜像列表, pageNo分页方式,支持搜索,排序")
    public LogicPageResultResponse<UserImage> listUserImages(
            @RequestParam(required = false, defaultValue = "") String keyword,
            @RequestParam(required = false, defaultValue = "") String keywordType,
            @RequestParam(required = false, defaultValue = "desc") String order,
            @RequestParam(required = false, defaultValue = "createTime") String orderBy,
            @RequestParam(required = true, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug("list images of user[{}] by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}", keywordType, keyword, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<UserImage> result = null;
        try {
            result = podService.listUserImage(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }

    /**
      * 获取Docker Hub镜像列表
      * 通过分页查询Docker Hub镜像，支持按关键字、关键字类型、排序方式、排序字段进行筛选
      * 
      * @param keyword 关键字，用于筛选镜像名称或镜像描述
      * @param keywordType 关键字类型，指定关键字是筛选镜像名称还是镜像描述
      * @param order 排序方式，升序或降序
      * @param orderBy 排序字段，指定按哪个字段进行排序
      * @param pageNo 当前页码，用于分页查询
      * @param pageSize 每页显示的记录数，用于分页查询
      * @return 分页查询结果，包含Docker Hub镜像列表和分页信息
      * @throws BceException 抛出BceException异常，如果查询过程中遇到业务异常
      * @throws Exception 抛出Exception异常，如果查询过程中遇到其他异常
      */
    @RequestMapping(value = BCI_DOCKER_HUB_IMAGE_LIST, method = RequestMethod.GET, params = { "pageNo" })
    public LogicPageResultResponse<DockerHubImage> dockerHubImageList(
            @RequestParam(required = false, defaultValue = "") String keyword,
            @RequestParam(required = false, defaultValue = "name") String keywordType,
            @RequestParam(required = false, defaultValue = "asc") String order,
            @RequestParam(required = false, defaultValue = "name") String orderBy,
            @RequestParam(required = true, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug("list dockerhub images by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}", keywordType, keyword, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<DockerHubImage> result = null;
        try {
            result = podService.listDockerHubImages(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }
    /**
      * 获取Docker Hub镜像标签列表
      * 通过指定的镜像名称、页码和页面大小，获取Docker Hub镜像标签列表的功能
      * 
      * @param name 镜像名称，非必须，默认为空字符串
      * @param pageNo 页码，必须，默认为1
      * @param pageSize 页面大小，非必须，默认为1000
      * @return LogicPageResultResponse<String>，包含镜像标签列表的结果对象
      * @throws BceException 如果调用过程中发生BceException异常
      * @throws Exception 如果调用过程中发生其他异常
      */

    @RequestMapping(value = BCI_DOCKER_HUB_IMAGE_TAG, method = RequestMethod.GET, params = { "pageNo","name" })
    public LogicPageResultResponse<String> dockerHubImageTags(
            @RequestParam(required = true, defaultValue = "") String name,
            @RequestParam(required = true, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LogicPageResultResponse<String> result = null;
        try {
            result = podService.listDockerHubImageTags(name, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }
    /**
      * 获取百度云官方镜像列表
      * 根据分页号获取百度云官方镜像列表，支持搜索和排序
      * 
      * @param keyword 搜索关键词
      * @param keywordType 关键词类型
      * @param order 排序方式
      * @param orderBy 排序字段
      * @param pageNo 分页号
      * @param pageSize 分页大小
      * @return 官方镜像列表的分页结果
      * @throws BceException 百度云异常
      * @throws Exception 其他异常
      */

    @RequestMapping(value = BCI_OFFICIAL_IMAGE_LIST, method = RequestMethod.GET, params = { "pageNo" })
    @ApiOperation(value = "给页面提供的百度云官方镜像列表, pageNo分页方式,支持搜索,排序")
    public LogicPageResultResponse<OfficialImage> listOfficialImages(
            @RequestParam(required = false, defaultValue = "") String keyword,
            @RequestParam(required = false, defaultValue = "") String keywordType,
            @RequestParam(required = false, defaultValue = "asc") String order,
            @RequestParam(required = false, defaultValue = "repository") String orderBy,
            @RequestParam(required = true, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize) {
        LOGGER.debug("list baidu official images by pageNo: keywordType: {}, keyword: {}, order: {}, orderBy: {}, "
                + "pageNo: {}, pageSize: {}", keywordType, keyword, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<OfficialImage> result = null;
        try {
            result = podService.listOfficialImage(keyword, keywordType, order, orderBy, pageNo, pageSize);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return result;
    }

    @RequestMapping(value = "/{podID}/{containerName}" + BCI_LOG, method = RequestMethod.GET,
            produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ApiOperation(value = "下载日志")
    public void logDownload(@PathVariable @IdPermission String podID,
                              @PathVariable String containerName,
                              @RequestParam(required = false) Integer limitBytes,
                              @RequestParam(required = false) Integer tailLines,
                              @RequestParam(required = false) String sinceTime,
                              @RequestParam(required = false) Integer sinceSeconds,
                              @RequestParam(required = false) Boolean timestamps,
                              @RequestParam(required = false) Boolean previous,
                              @RequestParam(required = false) Boolean follow,
                              HttpServletResponse servletResponse) {

        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String date = df.format(new Date());
        try {
            String fileName = URLEncoder.encode("pod_log_" + date + ".log", "UTF-8");
            LOGGER.debug("downLoad peerConn file name is {}", fileName);
            // 这个header 由console来加  此处加的话 到console取不到
            servletResponse.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
            BceInternalResponse response =  podService.logDownload(podID, containerName, limitBytes,
                    tailLines, sinceTime, sinceSeconds, timestamps, previous, follow);

            byte[] buff = new byte[1];
            BufferedInputStream bis = null;
            OutputStream os = null;
            try {
                os = servletResponse.getOutputStream();
                bis = new BufferedInputStream(response.getInputStream());
                int i = bis.read(buff);
                while (i != -1) {
                    os.write(buff, 0, buff.length);
                    os.flush();
                    i = bis.read(buff);
                }
            } catch (IOException e) {
                if (bis != null) {
                    bis.close();
                }
            } finally {
                if (os != null) {
                    os.close();
                }
                if (bis != null) {
                    bis.close();
                }
            }
        } catch (BceException e) {
            LOGGER.error("get pod log error", e);
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LOGGER.error("get pod log error", e);
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(value = BCI_EIP_BIND, method = RequestMethod.POST)
    @ApiOperation(value = "绑定EIp")
    public EdpResultResponse<Object> bind(@RequestBody BciEipBind bciEipBind) {

        EdpResultResponse<Object> response = new EdpResultResponse<Object>();
        try {
            podService.bindEipToPod(bciEipBind.getEip(), bciEipBind.getPodId());
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = BCI_EIP_UNBIND, method = RequestMethod.POST)
    @ApiOperation(value = "解绑绑EIp")
    public EdpResultResponse<Object> unBind(@RequestBody BciEipBind bciEipBind) {

        EdpResultResponse<Object> response = new EdpResultResponse<>();
        try {
            podService.unBindEipFromPod(bciEipBind.getEip());
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = BCI_QUOTA, method = RequestMethod.GET)
    @ApiOperation(value = "获取虚拟机相关的配额和使用情况")
    public BciQuota bciQuota(@RequestParam(required = false) boolean needGlobalQuota) {
        BciQuota result = new BciQuota();

        try {
            result = podService.getBciQuota(needGlobalQuota);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return result;
    }

    @RequestMapping(value = LIST_BY_UUID, method = RequestMethod.POST)
    @ApiOperation(value = "BCI列表")
    public PodListResponse listServersByUuids(@RequestBody IDListRequest listInstancesRequest) {
        PodListResponse result = new PodListResponse();
        List<PodPO> list = null;

        try {
            list = podService.getPods(listInstancesRequest.getIds());
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        List<PodForDisplay> resultList = new ArrayList<>();
        if (list != null) {
            for (PodPO podPO : list) {
                resultList.add(PodUtils.convertBean(podPO, PodForDisplay.class));
            }
        }

        result.setResult(resultList);
        return result;
    }
    
    /**
      * 容器组事件列表查询接口
      * 通过pageNo方式分页查询容器组事件列表，支持多种筛选条件
      *
      * @param cceId 集群ID，非必填
      * @param keyword 关键字，非必填
      * @param keywordType 关键字类型，非必填
      * @param order 排序字段，非必填
      * @param orderBy 排序方式，非必填
      * @param pageNo 页码，默认为1
      * @param pageSize 每页数量，默认为1000
      * @param filters 过滤条件，非必填
      * @return 分页查询结果
      * @throws BceException 业务异常
      * @throws Exception 其他异常
      */
    @RequestMapping(value = LIST_POD_EVENT_BY_PAGE, method = RequestMethod.GET, params = LIST_BY_PAGE_PARAM)
    @ApiOperation(value = "容器组事件列表: 通过pageNo方式分页")
    public LogicPageResultResponse<PodEventPO> listPodEventByPage(
            @RequestParam(required = false) String cceId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String keywordType,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filters) {
        PodListRequest listRequest =
                new PodListRequest(keyword, keywordType, order, orderBy, pageNo, pageSize, cceId);
        LOGGER.debug("listPodEvent bci[page], request data is {}", listRequest);

        LogicPageResultResponse<PodEventPO> pageResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filters)) {
                filters = StringEscapeUtils.unescapeHtml(filters).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("listPodEvent bci[page], after unescapeHtml filterMaperStr is : {}", filters);
                List<LinkedHashMap<String, String>> filterlist = JsonConvertUtil.fromJSON(filters, List.class);
                FilterMap filterMap = new FilterMap();
                for (LinkedHashMap<String, String> key : filterlist) {
                    filterMap.put(key.get("keywordType"), key.get("keyword"));
                }
                listRequest.setFilterMap(filterMap);
            }
            pageResultResponse = podService.listPodEventsWithPageByMultiKey(listRequest);
        } catch (BceException e) {
            LOGGER.error("listPodEvent bci error", e);
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }


    @RequestMapping(value = BCI_WEBSHELL, method = RequestMethod.POST)
    @ApiOperation(value = "webshell URL")
    public EdpResultResponse<WebshellUrl> webshell(@RequestBody WebShell webShell) {
        EdpResultResponse<WebshellUrl> response = new EdpResultResponse<>();
        WebshellUrl webshellUrl = new WebshellUrl();
        try {
            LOGGER.info("webshell param:podId is {} , tty is {} , stdin is {} , command is {}" ,
                    webShell.getPodId() , webShell.getTty() ,
                    webShell.getStdin() , webShell.getCommand());
            String url = podService.getWebshellUrl(webShell);
            webshellUrl.setUrl(url);
            response.setResult(webshellUrl);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }


    @ApiOperation(value = "删除泄漏的容器")
//    @PermissionVertify(service = {PermissionConstant.SERVICE_BCI}, permission = {PermissionConstant.BCI_READ})
    @RequestMapping(value = "/leakage/delete", method = RequestMethod.POST)
    public void deleteLeakagePod(@RequestBody @Valid @IdPermission LeakagePodDeleteRequest deleteRequest) {
        try {
            podService.deleteLeakagePod(deleteRequest);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
    }

    @RequestMapping(value = "/podNumber", method = RequestMethod.POST)
    @ApiOperation(value = "pod 数量")
    public PodNumberResponse podNumber(@RequestBody PodNumberRequest request) {
        PodNumberResponse podNumber = new PodNumberResponse();
        try {
            podNumber = podService.getPodNumberBySubnetId(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        return podNumber;
    }
}
