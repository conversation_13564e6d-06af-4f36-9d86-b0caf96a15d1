package com.baidu.bce.logic.bci.controller.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BciEipBind {
    private String podId;
    private String eip;

    public String getPodId() {
        return podId;
    }

    public void setPodId(String podId) {
        this.podId = podId;
    }

    public String getEip() {
        return eip;
    }

    public void setEip(String eip) {
        this.eip = eip;
    }


    @Override
    public String toString() {
        return "BciEipBind{"
                + "podId='" + podId + '\''
                + ", eip='" + eip + '\''
                + '}';
    }
}
