package com.baidu.bce.logic.bci.controller;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.service.model.AvailableResourceForm;
import com.baidu.bce.logic.bci.service.model.AvailableResourceVo;
import com.baidu.bce.logic.bci.service.model.ResourceUnitForm;
import com.baidu.bce.logic.bci.service.model.ResourceUnitVo;
import com.baidu.bce.logic.bci.service.pod.AvailableResourceService;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping({AvailableResourceController.BCI_BASE_URL_COMPATIBLE, AvailableResourceController.BCI_BASE_URL_V1})
public class AvailableResourceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AvailableResourceController.class);

    public static final String BCI_BASE_URL_COMPATIBLE = "/api/logical/bci/v1/pod";
    public static final String BCI_BASE_URL_V1 = "/v1/pod";

    public static final String AVAILABLE_RESOURCE = "/resource";


    @Autowired
    private AvailableResourceService  availableResourceService;

    @RequestMapping(value = AVAILABLE_RESOURCE + "/available", method = RequestMethod.POST)
    @ApiOperation(value = "query available resource")
    public AvailableResourceVo queryAvailableResource(@RequestBody @Valid AvailableResourceForm form) {
        AvailableResourceVo response = new AvailableResourceVo();
        try {
            response = availableResourceService.queryAvailableResource(form);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = AVAILABLE_RESOURCE + "/unit", method = RequestMethod.POST)
    @ApiOperation(value = "create resource unit")
    public ResourceUnitVo createResourceUnit(@RequestBody @Valid ResourceUnitForm form) {
        ResourceUnitVo resourceUnitVo = new ResourceUnitVo();
        try {
            resourceUnitVo = availableResourceService.createResourceUnit(form);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return resourceUnitVo;
    }
}
