package com.baidu.bce.logic.bci.controller;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.service.model.CacheStatus;
import com.baidu.bce.logic.bci.service.model.ImageCacheRequest;
import com.baidu.bce.logic.bci.service.model.ImageCacheResponse;
import com.baidu.bce.logic.bci.service.pod.PodImageService;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping({ImageController.BCI_BASE_URL_COMPATIBLE, ImageController.BCI_BASE_URL_V1})
public class ImageController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImageController.class);

    public static final String BCI_BASE_URL_COMPATIBLE = "/api/logical/bci/v1/pod";
    public static final String BCI_BASE_URL_V1 = "/v1/pod";

    public static final String IMAGE_CACHE = "/imageCache";

    @Autowired
    private PodImageService imageService;

    @RequestMapping(value = IMAGE_CACHE, method = RequestMethod.POST)
    @ApiOperation(value = "image cache")
    public ImageCacheResponse createImageCache(@RequestBody @Valid ImageCacheRequest request) {
        ImageCacheResponse response = new ImageCacheResponse();
        try {
            response = imageService.createImageCache(request);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(value = IMAGE_CACHE + "/{taskId}", method = RequestMethod.GET)
    @ApiOperation(value = "查询镜像缓存任务")
    public CacheStatus getImageCache(@PathVariable String taskId) {
        CacheStatus cacheStatus = new CacheStatus();
        try {
            cacheStatus = imageService.getImageCache(taskId);
        } catch (BceException e) {
            LogicPodExceptionHandler.handle(e);
        } catch (BceInternalResponseException e) {
            LogicPodExceptionHandler.handle(e);
        }
        return cacheStatus;
    }
}
