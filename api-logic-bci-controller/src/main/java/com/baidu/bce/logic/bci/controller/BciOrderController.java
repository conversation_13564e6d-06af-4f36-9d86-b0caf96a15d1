package com.baidu.bce.logic.bci.controller;

import com.baidu.bce.externalsdk.logical.network.common.model.OrderDetailRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.bci.service.pod.PodOrderService;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/api/bci/order")
public class BciOrderController {

    @Autowired
    PodOrderService podOrderService;

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.POST, produces = {"application/json"})
    @ApiOperation(value = "BCI订单详情")
    public EdpResultResponse<Order> detail(@RequestBody OrderDetailRequest request) {
        if (StringUtils.isEmpty(request.getUuid())) {
            throw new PodExceptions.RequestInvalidException();
        }

        EdpResultResponse<Order> response = new EdpResultResponse<Order>();

        try {
            Order order = podOrderService.getBciOrderDetail(request);
            response.setResult(order);
        } catch (BceInternalResponseException ex) {
            throw new PodExceptions.InternalServerErrorException();
        }

        return response;
    }
}
