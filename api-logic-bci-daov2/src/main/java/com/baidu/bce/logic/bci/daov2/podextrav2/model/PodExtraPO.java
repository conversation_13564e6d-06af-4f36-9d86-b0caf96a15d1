package com.baidu.bce.logic.bci.daov2.podextrav2.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PodExtraPO implements Cloneable {
    private long id;
    private String podId = "";
    private String userId = "";
    private String orderId = "";
    private String resourceUuid = "";
    private String orderExtra = "";
    private int deleted = 0;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
}