package com.baidu.bce.logic.bci.daov2.imagecache;

import com.baidu.bce.logic.bci.daov2.imagecache.mapper.ImageCacheMapperV2;
import com.baidu.bce.logic.bci.daov2.imagecache.model.ImageCachePO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("imageCacheDaoV2")
public class ImageCacheDaoV2 {

    @Autowired
    ImageCacheMapperV2 imageCacheMapper;

    public void insert(ImageCachePO imageCachePO) {
        imageCacheMapper.insert(imageCachePO);
    }

    public void updateTaskStatus(String taskId, String status) {
        imageCacheMapper.updateTaskStatus(taskId, status);
    }

    public List<ImageCachePO> queryTask(String taskId, String userId) {
        return imageCacheMapper.getTaskById(taskId, userId);
    }
    public int getTaskById(String taskId) {
        return imageCacheMapper.getByTaskId(taskId);
    }
}
