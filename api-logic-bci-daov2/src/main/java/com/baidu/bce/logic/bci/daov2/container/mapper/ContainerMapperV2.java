package com.baidu.bce.logic.bci.daov2.container.mapper;

import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ContainerMapperV2 {

    String INSERT_CONTAINER = "insert into t_container_v2 (pod_uuid, name, container_uuid, image_name, image_version, "
            + "image_address, cpu, memory, working_dir, image_pull_policy, commands, args, ports, volume_mounts, envs, "
            + "user_id, previous_state, current_state, restart_count, "
            + "container_type, liveness_probe, readiness_probe, startup_probe, gpu_type, gpu_count, image_id, " 
            + "lifecycle, stdin, stdin_once, tty, security_context, ds_container_version, " +
            "created_time)";


    @Insert(INSERT_CONTAINER + " values "
            + "#repeat($_parameter.containers $container \",\")"
            + "(@{container.podUuid}, @{container.name}, @{container.containerUuid}, @{container.imageName}, "
            + "@{container.imageVersion}, @{container.imageAddress}, @{container.cpu},@{container.memory}, "
            + "@{container.workingDir}, @{container.imagePullPolicy},@{container.commands}, @{container.args}, "
            + "@{container.ports}, @{container.volumeMounts}, @{container.envs}, @{container.userId}, "
            + "@{container.previousState}, @{container.currentState}, @{container.restartCount}, "
            + "@{container.containerType},@{container.livenessProbe},@{container.readinessProbe}, "
            + "@{container.startupProbe}, @{container.gpuType}, @{container.gpuCount}, @{container.imageID}, " 
            + "@{container.lifecycle}, @{container.stdin}, @{container.stdinOnce}, @{container.tty}, @{container.securityContext}, " 
            + "@{container.dsContainerVersion}, UTC_TIMESTAMP())"
            + "#end")
    void batchInsert(@Param("containers") List<ContainerPO> containers);

    String QUERY_CONTAINER = "SELECT id, pod_uuid, name, container_uuid, image_name, image_version, image_address, " +
            "cpu, memory, working_dir, image_pull_policy, commands, args, ports, volume_mounts, envs, user_id, " +
            "previous_state, current_state, restart_count, ready, started, created_time, updated_time, " +
            "container_type, liveness_probe, readiness_probe, startup_probe, gpu_type, gpu_count, image_id, " +
            "lifecycle, stdin, stdin_once, tty, security_context, ds_container_version " +
            " FROM t_container_v2 ";

    String QUERY_CONTAINER_LIGHT = "SELECT id, pod_uuid, name, container_uuid, image_name, image_version, image_address, " +
            "cpu, memory, working_dir, image_pull_policy, commands, args, ports, envs, user_id, " +
            "previous_state, current_state, restart_count, ready, started, created_time, updated_time, " +
            "container_type, liveness_probe, readiness_probe, startup_probe, gpu_type, gpu_count, image_id, " +
            "lifecycle, stdin, stdin_once, tty, security_context, ds_container_version " +
            " FROM t_container_v2 ";
    @Select(QUERY_CONTAINER + " where pod_uuid = @{podId} and deleted = 0")
    List<ContainerPO> listByPodId(@Param("podId") String podId);

    @Select(QUERY_CONTAINER_LIGHT + " where pod_uuid = @{podId} and deleted = 0")
    List<ContainerPO> listContainersLightByPodId(@Param("podId") String podId);

    @Select(QUERY_CONTAINER_LIGHT + " where pod_uuid = @{podId}")
    List<ContainerPO> listContainersWithDeletedByPodId(@Param("podId") String podId);

    @Select(QUERY_CONTAINER_LIGHT
            + " where user_id = @{accountId} and deleted = 0 "
            + " #if($_parameter.podUuids && $_parameter.podUuids.size() > 0)"
            + "     and  "
            + "     #in($_parameter.podUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + " #end ")
    List<ContainerPO> listContainersLightByPodUuids(
            @Param("accountId")String accountId,
            @Param("podUuids") List<String> podUuids);

    @Update("update t_container_v2 set pod_uuid = @{containerPO.podUuid}, updated_time = UTC_TIMESTAMP(), "
            + "image_name = @{containerPO.imageName}, image_version = @{containerPO.imageVersion}, "
            + "image_address = @{containerPO.imageAddress}, "
            + "previous_state = @{containerPO.previousState}, current_state = @{containerPO.currentState}, "
            + "restart_count = @{containerPO.restartCount}, container_uuid = @{containerPO.containerUuid}, "
            + "ready = @{containerPO.ready}, started = @{containerPO.started},image_id = @{containerPO.imageID}, "
            + "ds_container_version = @{containerPO.dsContainerVersion} where id = @{containerPO.id}")
    void update(@Param("containerPO") ContainerPO containerPO);

    @Update("update t_container_v2 set deleted = 1, deleted_time = UTC_TIMESTAMP() where deleted = 0 AND "
            + "user_id = @{accountId} and pod_uuid = @{podId}")
    void deleteContainerById(@Param("accountId")String accountId, @Param("podId") String podId);

    @Update("update t_container_v2 set deleted = 1, deleted_time = UTC_TIMESTAMP() where deleted = 0 AND "
            + "user_id = @{accountId} and pod_uuid = @{podUuid}")
    void deleteContainerByUuid(@Param("accountId")String accountId, @Param("podUuid") String podUuid);

    @Update("#if($_parameter.ids && $_parameter.ids.size() > 0)"
          + "  update t_container_v2 set deleted = 1, deleted_time = UTC_TIMESTAMP() where deleted = 0 "
          + "  AND user_id = @{accountId} AND "
          + "    #in($_parameter.ids $id \" id \" ) "
          + "         @{id}"
          + "    #end"
          + "#end ")
    void batchDeleteContainersByIds(@Param("accountId") String accountId, @Param("ids") List<Long> ids);

    @Update("update t_container_v2 set pod_uuid = @{podUuid}, updated_time = UTC_TIMESTAMP() where pod_uuid = @{podId}")
    void updateContainersByPodId(@Param("podId")String podId, @Param("podUuid")String podUuid);

    @Select("select container_uuid from t_container_v2 where pod_uuid = @{podUuid} and name = @{name} LIMIT 1")
    String queryContainerUuid(@Param("podUuid")String podUuid, @Param("name")String name);


    @Select(  QUERY_CONTAINER
            + " where user_id = @{accountId}"
            + " #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "     and  "
            + "     #in($_parameter.podIds $podId \" pod_uuid \" ) "
            + "         @{podId}"
            + "     #end"
            + " #end ")
    List<ContainerPO> listPodContainerByPodIds(@Param("accountId") String accountId,
                                                @Param("podIds") List<String> podIds);

    @Update("update t_container_v2 set current_state = @{currentState}, updated_time = utc_timestamp() "
            + " where user_id = @{userId} and pod_uuid = @{podUuid}")
    int updateContainerCurrentStateByPodUuid(@Param("userId") String userId,
                                             @Param("podUuid") String podUuid,
                                             @Param("currentState") String currentState);
}
