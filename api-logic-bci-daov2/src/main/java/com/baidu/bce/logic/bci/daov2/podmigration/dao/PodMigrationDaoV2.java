package com.baidu.bce.logic.bci.daov2.podmigration.dao;

import com.baidu.bce.logic.bci.daov2.podmigration.mapper.PodMigrationMapper;
import com.baidu.bce.logic.bci.daov2.podmigration.model.PodMigrationPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("podMigrationDaoV2")
public class PodMigrationDaoV2 {

    @Autowired
    private PodMigrationMapper podMigrationMapper;

    public void insert(String podId, String migrationUuid) {
        podMigrationMapper.insert(podId, migrationUuid);
    }

    public List<PodMigrationPO> findByPodId(String podId) {
        return podMigrationMapper.selectByPodId(podId);
    }

    // 查询指定 migrationUuid 的记录
    public List<PodMigrationPO> findByMigrationUuid(String migrationUuid) {
        return podMigrationMapper.selectByMigrationUuid(migrationUuid);
    }
}
