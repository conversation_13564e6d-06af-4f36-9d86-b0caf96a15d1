package com.baidu.bce.logic.bci.daov2.container.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.bci.dao.container.model.ContainerPOForBcm;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class ContainerPO implements ContainerPOForBcm {

    private long id;
    private String podUuid = "";
    private String name = "";
    // 新增字段
    private String containerType = "";
    private String containerUuid = "";
    private String imageName = "";
    private String imageVersion = "";
    private String imageID = "";
    private String imageAddress = "";
    private float cpu;
    private float memory;
    private String gpuType;
    private float gpuCount;
    private String workingDir = "";
    private String imagePullPolicy = "";
    private String commands = "";
    private String args = "";
    private String ports = "";
    private String volumeMounts = "";
    private String envs = "";
    private String userId = "";
    private String previousState = "";
    private String currentState = "";
    private int restartCount = 0;
    private String ready;
    private String started;
    private String extra;
    private int deleted = 0;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    // 新增字段
    private String livenessProbe;
    private String readinessProbe;
    private String startupProbe;
    private String lifecycle;
    private boolean stdin = false;
    private boolean stdinOnce = false;
    private boolean tty = false;
    private String securityContext;
    private long dsContainerVersion = 0;

    public long getId() {
        return id;
    }

    public String getStartupProbe() {
        return startupProbe;
    }

    public void setStartupProbe(String startupProbe) {
        this.startupProbe = startupProbe;
    }

    public String getReadinessProbe() {
        return readinessProbe;
    }

    public void setReadinessProbe(String readinessProbe) {
        this.readinessProbe = readinessProbe;
    }

    public String getLivenessProbe() {
        return livenessProbe;
    }

    public void setLivenessProbe(String livenessProbe) {
        this.livenessProbe = livenessProbe;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getPodUuid() {
        return podUuid;
    }

    public void setPodUuid(String podUuid) {
        this.podUuid = podUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public float getCpu() {
        return cpu;
    }

    public void setCpu(float cpu) {
        this.cpu = cpu;
    }

    public float getMemory() {
        return memory;
    }

    public void setMemory(float memory) {
        this.memory = memory;
    }

    public String getGpuType() {
        return gpuType;
    }

    public void setGpuType(String gpuType) {
        this.gpuType = gpuType;
    }

    public float getGpuCount() {
        return gpuCount;
    }

    public void setGpuCount(float gpuCount) {
        this.gpuCount = gpuCount;
    }

    public String getWorkingDir() {
        return workingDir;
    }

    public void setWorkingDir(String workingDir) {
        this.workingDir = workingDir;
    }

    public String getImagePullPolicy() {
        return imagePullPolicy;
    }

    public void setImagePullPolicy(String imagePullPolicy) {
        this.imagePullPolicy = imagePullPolicy;
    }

    public String getCommands() {
        return commands;
    }

    public void setCommands(String commands) {
        this.commands = commands;
    }

    public String getArgs() {
        return args;
    }

    public void setArgs(String args) {
        this.args = args;
    }

    public String getPorts() {
        return ports;
    }

    public void setPorts(String ports) {
        this.ports = ports;
    }

    public String getVolumeMounts() {
        return volumeMounts;
    }

    public void setVolumeMounts(String volumeMounts) {
        this.volumeMounts = volumeMounts;
    }

    public String getEnvs() {
        return envs;
    }

    public void setEnvs(String envs) {
        this.envs = envs;
    }

    public Timestamp getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }

    public Timestamp getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getReady() {
        return ready;
    }

    public void setReady(String ready) {
        this.ready = ready;
    }

    public String getStarted() {
        return started;
    }

    public void setStarted(String started) {
        this.started = started;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public Timestamp getDeletedTime() {
        return deletedTime;
    }

    public void setDeletedTime(Timestamp deletedTime) {
        this.deletedTime = deletedTime;
    }

    public String getImageVersion() {
        return imageVersion;
    }

    public void setImageVersion(String imageVersion) {
        this.imageVersion = imageVersion;
    }

    public String getImageAddress() {
        return imageAddress;
    }

    public void setImageAddress(String imageAddress) {
        this.imageAddress = imageAddress;
    }

    public String getContainerUuid() {
        return containerUuid;
    }

    public void setContainerUuid(String containerUuid) {
        this.containerUuid = containerUuid;
    }

    public String getPreviousState() {
        return previousState;
    }

    public void setPreviousState(String previousState) {
        this.previousState = previousState;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public int getRestartCount() {
        return restartCount;
    }

    public void setRestartCount(int restartCount) {
        this.restartCount = restartCount;
    }

    public String getContainerType() {
        return containerType;
    }

    public void setContainerType(String containerType) {
        this.containerType = containerType;
    }

    @Override
    public String toString() {
        return "ContainerPO{" + "podUuid='" + podUuid + '\'' + ", name='" + name + '\'' + ", containerType='"
                + containerType + '\'' + ", containerUuid='" + containerUuid + '\'' + ", imageName='" + imageName + '\''
                + ", imageVersion='" + imageVersion + '\'' + ", imageAddress='" + imageAddress + '\'' + ", cpu=" + cpu
                + ", memory=" + memory + '\'' + ", gpuType='" + gpuType + ", gpuCount='" + gpuCount + '\''
                + ", workingDir='" + workingDir + '\'' + ", imagePullPolicy='" + imagePullPolicy
                + '\'' + ", commands='" + commands + '\'' + ", args='" + args + '\'' + ", ports='" + ports + '\''
                + ", volumeMounts='" + volumeMounts + '\'' + ", envs='" + envs + '\'' + ", userId='" + userId + '\''
                + ", previousState='" + previousState + '\'' + ", currentState='" + currentState + '\''
                + ", restartCount=" + restartCount + ", deleted=" + deleted + ", createdTime=" + createdTime
                + ", updatedTime=" + updatedTime + ", deletedTime=" + deletedTime + '}';
    }
}
