package com.baidu.bce.logic.bci.daov2.imagecache.mapper;

import com.baidu.bce.logic.bci.daov2.imagecache.model.ImageCachePO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ImageCacheMapperV2 {
    String QUERY_IMAGE_CACHE = "SELECT id, task_id, user_id, status, address, az, " +
            "created_time, updated_time FROM t_image_cache_v2 ";

    String INSERT_IMAGE_CACHE = "insert into t_image_cache_v2 (task_id, user_id, status, address, "
            + "az, created_time, updated_time)";

    @Insert(INSERT_IMAGE_CACHE + " values "
            + "(@{imageCache.taskId}, @{imageCache.userId}, @{imageCache.status}, @{imageCache.address}, "
            + "@{imageCache.az}, @{imageCache.createdTime}, @{imageCache.updatedTime})")
    void insert(@Param("imageCache") ImageCachePO imageCachePO);

    @Select(QUERY_IMAGE_CACHE + " where task_id = @{taskId} and user_id = @{userId}")
    List<ImageCachePO> getTaskById(@Param("taskId") String taskId, @Param("userId") String userId);

    @Select("select count(id) FROM t_image_cache_v2 where task_id = @{taskId}")
    int getByTaskId(@Param("taskId") String taskId);

    @Update("update t_image_cache_v2 set status = @{status} where task_id = @{taskId}")
    void updateTaskStatus(@Param("taskId") String taskId, @Param("status") String status);
}
