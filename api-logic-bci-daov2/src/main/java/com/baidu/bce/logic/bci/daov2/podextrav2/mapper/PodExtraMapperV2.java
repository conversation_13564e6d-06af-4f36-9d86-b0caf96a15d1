package com.baidu.bce.logic.bci.daov2.podextrav2.mapper;

import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.dao.DataAccessException;

import java.util.List;

public interface PodExtraMapperV2 {
    public static final String TABLE_NAME = "t_pod_extra_v2";

    public String QUERY_POD_EXTRA = "select p.id, p.pod_id, p.user_id, p.resource_uuid, p.order_extra, p.deleted,"
            + " p.created_time, p.updated_time, p.deleted_time"
            + " FROM " + TABLE_NAME + " p ";
    public String INSERT_POD_EXTRA = "INSERT INTO " + TABLE_NAME + " (pod_id, user_id, order_id, order_extra) ";

    @Insert(INSERT_POD_EXTRA + " values ("
            + "@{podExtraPO.podId}, @{podExtraPO.userId}, @{podExtraPO.orderId}, @{podExtraPO.orderExtra}"
            + ")")
    int insertPodExtra(@Param("podExtraPO") PodExtraPO podExtraPO) throws DataAccessException;

    @Update("update " + TABLE_NAME + " set order_id = @{orderId} "
            + "#where() "
            + "  user_id = @{userId} "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    int updateOrderId(@Param("podIds") List<String> podIds, @Param("userId") String userId,
                      @Param("orderId") String orderId);

    @Select(QUERY_POD_EXTRA + " where p.pod_id = @{podId} and deleted=0")
    PodExtraPO getPodExtraByPodId(@Param("podId") String podId);

    @Select(QUERY_POD_EXTRA + " where p.pod_id = @{podId}")
    PodExtraPO getPodExtraByPodIdIgnoreStatus(@Param("podId") String podId);

    @Select(QUERY_POD_EXTRA
            + "#where() "
            + "  user_id = @{userId} and deleted=0 "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodExtraPO> getPodExtraByPodIds(
            @Param("userId") String userId,
            @Param("podIds") List<String> podIds);

    @Select(QUERY_POD_EXTRA
            + "#where() "
            + "  user_id = @{userId} "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodExtraPO> getPodExtraByPodIdsIgnoreStatus(
            @Param("userId") String userId,
            @Param("podIds") List<String> podIds);

    @Update("update " + TABLE_NAME + " set deleted = 1, deleted_time = NOW() "
            + " where user_id = @{userId} and pod_id = @{podId} and deleted=0")
    int deletePodExtra(@Param("userId") String userId, @Param("podId") String podId);

    @Update("update " + TABLE_NAME + " set resource_uuid = @{podExtraPO.resourceUuid} "
            + " where user_id = @{podExtraPO.userId} and pod_id = @{podExtraPO.podId} and deleted = 0")
    int updatePodExtraResourceUuid(@Param("podExtraPO")  PodExtraPO podExtraPO);
}