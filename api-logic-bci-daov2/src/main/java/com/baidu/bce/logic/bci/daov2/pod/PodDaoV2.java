package com.baidu.bce.logic.bci.daov2.pod;

import com.baidu.bce.logic.bci.daov2.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.daov2.common.model.PodListModel;
import com.baidu.bce.logic.bci.daov2.pod.mapper.PodMapperV2;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPOWithId;
import com.baidu.bce.logic.bci.daov2.pod.util.PodDBHelper;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.plat.webframework.database.SqlEscape;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public class PodDaoV2 {

    @Autowired
    private PodMapperV2 podMapper;

    private static final String POD_PREFIX = "p-";

    private static final Logger LOGGER = LoggerFactory.getLogger(PodDaoV2.class);

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final Long UTC_TIME = 8 * 60 * 60 * 1000L;

    public List<PodPO> listPodsByMultiKey(String accountId, PodListModel podListModel,
                                          List<PodFilterQueryModel> queryList) {

        // Order by转换
        List<OrderModel> orderModels = podListModel.getOrders();
        // orderBy字段需要返回给fe，不能直接修改
        List<OrderModel> orderModelsTransformed = null;

        Iterator<PodFilterQueryModel> iterator = queryList.iterator();
        while (iterator.hasNext()) {
            PodFilterQueryModel item = iterator.next();
            if (PodDBHelper.FIELD_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_MAP.get(item.getName()));
                if (item.getValue() != null) {
                    item.setValue("%" + SqlEscape.escapeLikePattern(item.getValue()) + "%");
                }
            } else {
                iterator.remove();
            }
        }

        if (orderModels != null && !orderModels.isEmpty()) {
            orderModelsTransformed = new ArrayList<>(orderModels.size());
            for (OrderModel orderModel : orderModels) {
                OrderModel model = new OrderModel();
                model.setOrderBy(PodDBHelper.parseOrderBy(orderModel.getOrderBy()));
                model.setOrder(orderModel.getOrder());
                orderModelsTransformed.add(model);
            }
        }

        return podMapper.listPodsByMultiKey(accountId, podListModel, orderModelsTransformed, queryList);
    }

    /**
     * @Description 根据多个键值和id列表查询PodPO列表
     * @Param accountId String 账户id
     * @Param podListModel PodListModel 分页、排序等参数
     * @Param queryList List<PodFilterQueryModel> 查询条件列表
     * @Param id BigInteger id列表
     * @Return List<PodPO> PodPO列表
     */
    public List<PodPO> listPodsByMultiKeyAndID(String accountId, PodListModel podListModel,
                                               List<PodFilterQueryModel> queryList, BigInteger id) {
        // Order by转换
        List<OrderModel> orderModels = podListModel.getOrders();
        // orderBy字段需要返回给fe，不能直接修改
        List<OrderModel> orderModelsTransformed = null;
        // 遍历查询条件,转换查询条件(增加模糊匹配),将修改后的value存储在queryList中
        // 对于不在PodDBHelper.FIELD_MAP中的查询条件,直接删除
        // 优化点:
        //   1. 从VK传过来的keywordType(cceId) 和 keyword("cce-xxx")可以精确匹配,不需要模糊匹配
//        Iterator<PodFilterQueryModel> iterator = queryList.iterator();
//        while (iterator.hasNext()) {
//            PodFilterQueryModel item = iterator.next();
//            if (PodDBHelper.FIELD_MAP.containsKey(item.getName())) {
//                item.setName(PodDBHelper.FIELD_MAP.get(item.getName()));
//                if (item.getValue() != null) {
//                    item.setValue("%" + SqlEscape.escapeLikePattern(item.getValue()) + "%");
//                }
//            } else {
//                iterator.remove();
//            }
//        }
        // 精确匹配的查询条件
        List<PodFilterQueryModel> queryExactMatchList = new ArrayList<>();
        // 模糊匹配的查询条件
        List<PodFilterQueryModel> queryFuzzyMatchList = new ArrayList<>();
        Iterator<PodFilterQueryModel> iterator = queryList.iterator();
        while (iterator.hasNext()) {
            PodFilterQueryModel item = iterator.next();
            if (PodDBHelper.FIELD_EXACT_MATCH_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_EXACT_MATCH_MAP.get(item.getName()));
                queryExactMatchList.add(item);
                continue;
            }
            if (PodDBHelper.FIELD_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_MAP.get(item.getName()));
                if (item.getValue() != null) {
                    item.setValue("%" + SqlEscape.escapeLikePattern(item.getValue()) + "%");
                }
                queryFuzzyMatchList.add(item);
            } else {
                iterator.remove();
            }
        }

        if (orderModels != null && !orderModels.isEmpty()) {
            orderModelsTransformed = new ArrayList<>(orderModels.size());
            for (OrderModel orderModel : orderModels) {
                OrderModel model = new OrderModel();
                model.setOrderBy(PodDBHelper.parseOrderBy(orderModel.getOrderBy()));
                model.setOrder(orderModel.getOrder());
                orderModelsTransformed.add(model);
            }
        }

        return podMapper.listPodsByMultiKeyAndIdWithMatchList(accountId, podListModel, orderModelsTransformed,
                queryExactMatchList, queryFuzzyMatchList, id);
    }

    /**
     * @Description 根据多个查询条件和ID，列出符合条件的PodPO列表（只包含部分字段）
     * @Param accountId String 账户ID
     * @Param podListModel PodListModel 请求参数，包括分页信息、排序信息等
     * @Param queryList List<PodFilterQueryModel> 查询条件列表，包括字段名、操作符、值等信息
     * @Param id BigInteger ID，用于筛选结果中符合指定ID的PodPO
     * @Return List<PodPO> 返回符合条件的PodPO列表，每个PodPO只包含部分字段
     */
    public List<PodPO> listPodsLightByMultiKeyAndID(String accountId, PodListModel podListModel,
                                               List<PodFilterQueryModel> queryList, BigInteger id) {
        // 遍历查询条件,转换查询条件(增加模糊匹配),将修改后的value存储在queryList中
        // 对于不在PodDBHelper.FIELD_MAP中的查询条件,直接删除
        // 优化点:
        //   1. 从VK传过来的keywordType(cceId) 和 keyword("cce-xxx")可以精确匹配,不需要模糊匹配
        //   2. 从VK传过来的其他查询条件,可以直接转换为模糊匹配

        // 精确匹配的查询条件
        List<PodFilterQueryModel> queryExactMatchList = new ArrayList<>();
        // 模糊匹配的查询条件
        List<PodFilterQueryModel> queryFuzzyMatchList = new ArrayList<>();
        Iterator<PodFilterQueryModel> iterator = queryList.iterator();
        while (iterator.hasNext()) {
            PodFilterQueryModel item = iterator.next();
            if (PodDBHelper.FIELD_EXACT_MATCH_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_EXACT_MATCH_MAP.get(item.getName()));
                queryExactMatchList.add(item);
                continue;
            }
            if (PodDBHelper.FIELD_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_MAP.get(item.getName()));
                if (item.getValue() != null) {
                    item.setValue("%" + SqlEscape.escapeLikePattern(item.getValue()) + "%");
                }
                queryFuzzyMatchList.add(item);
            } else {
                iterator.remove();
            }
        }
        // 从数据库里查询符合条件的pod
        int queryLimit = podListModel.getQueryLimit();
        return podMapper.listPodsLightByMultiKeyAndId(accountId, podListModel,
                queryExactMatchList, queryFuzzyMatchList, id, queryLimit);
    }

    public List<PodPO> listPods(String accountId, List<String> shortIds, String keywordType, String keyword) {
        if (PodDBHelper.FIELD_MAP.containsKey(keywordType)) {
            keywordType = PodDBHelper.FIELD_MAP.get(keywordType);
        } else {
            keywordType = null;
            keyword = null;
        }
        return podMapper.listPods(accountId, shortIds, keywordType, keyword);
    }

    public List<PodPO> listPodsByUpdatedTime(String accountId, Timestamp updatedTime,
                                             String keywordType, String keyword) {
        if (PodDBHelper.FIELD_MAP.containsKey(keywordType)) {
            keywordType = PodDBHelper.FIELD_MAP.get(keywordType);
        } else {
            keywordType = null;
            keyword = null;
        }
        return podMapper.listPodsByUpdatedTime(accountId, updatedTime, keywordType, keyword);
    }

    public List<PodPO> listPodsWhichNotSyncDsContainersToK8S() {
        return podMapper.listPodsWhichNotSyncDsContainersToK8S();
    }

    @Deprecated
    public int queryPodCountByMultiKey(String accountId, PodListModel podListModel,
                                       List<PodFilterQueryModel> queryList) {
        Iterator<PodFilterQueryModel> iterator = queryList.iterator();
        while (iterator.hasNext()) {
            PodFilterQueryModel item = iterator.next();
            if (PodDBHelper.FIELD_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_MAP.get(item.getName()));
                if (item.getValue() != null) {
                    item.setValue("%" + SqlEscape.escapeLikePattern(item.getValue()) + "%");
                }
            } else {
                iterator.remove();
            }
        }
        return podMapper.queryPodCountByMultiKey(accountId, podListModel, queryList);
    }

    public PodPO getPodDetail(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodDetailById(accountId, podId);
        } else {
            return podMapper.getPodDetailByUuid(accountId, podId);
        }
    }

    public PodPO getPodDetailForAllStatus(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodDetailByIdForAllStatus(accountId, podId);
        } else {
            return podMapper.getPodDetailByUuidForAllStatus(accountId, podId);
        }
    }

    /**
     * @Description 获取Pod详情的简单信息，包括ID和UUID两种方式查询。
     * @param accountId 账户ID，用于区分不同的账户。
     * @param podId Pod的ID或UUID，可以是带有前缀"pod-"的ID或者任意字符串。
     * @return PodPO 返回一个Pod对象，包含了Pod的基本信息。如果没有找到相应的Pod，则返回null。
     */
    public PodPO getPodDetailLight(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodDetailLightById(accountId, podId);
        } else {
            return podMapper.getPodDetailLightByUuid(accountId, podId);
        }
    }

    /**
     * @Description 获取Pod详情的简单信息，包括ID和UUID两种方式查询。
     * @param accountId 账户ID，用于区分不同的账户。
     * @param podId Pod的ID或UUID，可以是带有前缀"pod-"的ID或者任意字符串。
     * @return PodPO 返回一个Pod对象，包含了Pod的基本信息。如果没有找到相应的Pod，则返回null。
     */
    public PodPO getPodDetailWithDeleted(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodDetailWithDeletedById(accountId, podId);
        } else {
            return podMapper.getPodDetailWithDeletedByUuid(accountId, podId);
        }
    }

    /**
     * {@inheritDoc}
     * 根据账户ID和Pod ID列表，获取指定账户下的Pod详细信息（仅包含部分字段）。
     *
     * @param accountId 账户ID，不能为空
     * @param podIds Pod ID列表，不能为空，每个ID最多100个字符长度
     * @return 返回一个List类型，包含所有符合条件的Pod详细信息对象，如果没有找到任何结果则返回空List
     * @throws IllegalArgumentException 当accountId或podIds为空时抛出此异常
     */
    public List<PodPO> listPodsDetailLightByIds(String accountId, List<String> podIds) {
        return podMapper.listPodsDetailLightByIds(accountId, podIds);
    }

    public int getPendingPodCountByUserId(String accountId) {
        return podMapper.getPendingPodCountByUserId(accountId);
    }

    public int getResourceRecycleIncompletePodCountByUserId(String accountId) {
        return podMapper.getResourceRecycleIncompletePodCountByUserId(accountId);
    }

    public PodPO getPodById(String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodById(podId);
        } else {
            return podMapper.getPodByUuid(podId);
        }
    }

    public PodPO getPodById(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodByUserIdAndPodId(accountId, podId);
        } else {
            return podMapper.getPodByUserIdAndUuid(accountId, podId);
        }
    }

    public PodPO getPodByIdIgnoreStatus(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodByIdIgnoreStatus(accountId, podId);
        } else {
            return podMapper.getPodByUuidIgnoreStatus(accountId, podId);
        }
    }

    public String queryPodUuid(String podId) {
        return podMapper.queryPodUuid(podId);
    }

    public BigInteger queryID(String podId) {
        return podMapper.queryID(podId);
    }

    public void deletePod(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            podMapper.deletePodById(accountId, podId);
        } else {
            podMapper.deletePodByUuid(accountId, podId);
        }
    }

    public int markResourceRecyclePod(PodPO podPO) {
       return podMapper.markResourceRecyclePod(podPO);
    }

    public void batchInsertPods(List<PodPO> podPOList) {
        podMapper.batchInsertPods(podPOList);
    }

    public List<String> listAllPodUuids(String accountId) {
        return podMapper.listAllPodUuids(accountId);
    }

    public int getCreatedPodInQuota(String accountId) {
        return podMapper.getCreatedPodInQuota(accountId);
    }

    public Map<String, String> podIdMap(String accountId) {
        List<PodPO> podPOList = podMapper.listPodByAccount(accountId);
        Map<String, String> map = new HashMap<>();
        for (PodPO podPO : podPOList) {
            map.put(podPO.getPodId(), podPO.getPodUuid());
        }
        return map;
    }

    public List<PodPO> listByOrderId(String accountId, String orderId) {
        return podMapper.listByOrderId(accountId, orderId);
    }

    public List<PodPO> listByOrderIdIgnoreStatus(String accountId, String orderId) {
        return podMapper.listByOrderIdIgnoreStatus(accountId, orderId);
    }

    public int getPodCountByOrderId(String orderId) {
        return podMapper.getPodCountByOrderId(orderId);
    }

    public int getPodCountByOrderidIgnoreStatus(String orderId) {
        return podMapper.getPodCountByOrderidIgnoreStatus(orderId);
    }

    public Map<String, PodPO> listCpt1PodByAccount(String accountId) {
        List<PodPO> podPOList = podMapper.listCpt1PodByAccount(accountId);
        Map<String, PodPO> map = new HashMap<>(podPOList.size());
        for (PodPO podPO: podPOList) {
            if (StringUtils.isNotBlank(podPO.getPodUuid())) {
                map.put(podPO.getPodUuid(), podPO);
            }
        }
        return map;
    }

    @Transactional
    public void batchUpdateStatus(List<PodPO> podPOList) {
        for (PodPO podPO : podPOList) {
            podMapper.updateOrderInfo(podPO);
        }
    }

    @Transactional
    public void batchUpdateBLSTasksID(List<PodPO> podPOList, String createdLogTasksIdStr) {
        for (PodPO podPO : podPOList) {
            podMapper.updateCreatedBLSTasksID(podPO, createdLogTasksIdStr);
        }
    }

    public void batchMarkDsConatinersSynced(List<PodPO> podPOs) {
        if (podPOs == null || podPOs.size() == 0) {
            return;
        }
        for (PodPO podPO : podPOs) {
            LOGGER.debug("mark ds containers synced: {}", podPO.getPodId());
            podMapper.markDsConatinersSynced(podPO.getUserId(), podPO.getPodId(), 
                                             podPO.getBciResourceVersion() + 1, 
                                             podPO.getBciResourceVersion());
        }
    }
    
    public void updateOrderId(List<String> podIds, String accountId, String orderId) {
        podMapper.updateOrderId(podIds, accountId, orderId);
    }

    public List<PodPO> listBidRunningPods(String preemptStatus, 
                                            String productType, int bidProtectedPeriod) {
        Timestamp beforeTime = new Timestamp(new Date().getTime() - bidProtectedPeriod * 60 * 1000L + UTC_TIME);
        String beforeTime1 = sdf.format(beforeTime.getTime());
        return podMapper.listBidRunningPods(preemptStatus, productType, beforeTime);
    }

    public List<PodPO> listPreemptBidPods(List<String> willBePreemptedInstanceIds, String beforePreemtStatus) {
        return podMapper.listPreemptBidPods(willBePreemptedInstanceIds, beforePreemtStatus);
    }

    public void updatePreemptedStatusByInstanceId(List<String> willBePreemptedInstanceIds,
                                                    String beforePreemtStatus,
                                                    String afterPreemtStatus) {
        podMapper.updatePreemptedStatusByInstanceId(willBePreemptedInstanceIds, 
                                                    beforePreemtStatus, afterPreemtStatus);
    }
    
    public List<PodPO> listToBePreemptBidPods(String preemptStatus, int bidPreemtWaitSec) {
        Timestamp beforeTime = new Timestamp(new Date().getTime() - bidPreemtWaitSec * 1000L + UTC_TIME);
        String beforeTime1 = sdf.format(beforeTime.getTime());
        return podMapper.listToBePreemptBidPods(preemptStatus, beforeTime);
    }

    public void batchUpdatePreemptedPod(List<String> podIds, String status, String preemtStatus) {
        podMapper.batchUpdatePreemptedPod(podIds, status, preemtStatus);
    }

    public List<PodPO> listPreemptedBidPods(String status, String preemptStatus, int bidPreemptedClearHour) {
        Timestamp beforeTime = new Timestamp(new Date().getTime() - bidPreemptedClearHour * 60 * 60 * 1000L + UTC_TIME);
        String beforeTime1 = sdf.format(beforeTime.getTime());
        return podMapper.listPreemptedBidPods(status, preemptStatus, beforeTime);
    }

    public List<PodPO> listByStatus(String status) {
        return podMapper.listByStatus(status);
    }

    public List<PodPO> listByInternalStatus(String status) {
        return podMapper.listByInternalStatus(status);
    }

    @Transactional
    public List<PodPO> listAllPods() {
        // 目前只有推送计费（每隔1分钟）和同步pod、container信息（每隔20秒）时才会调用
        return podMapper.listAllPods();
    }

    public void updatePod(PodPO podPO) {
        podMapper.updatePod(podPO);
    }

    public void updatePodExtras(PodPO podPO) {
        podMapper.updatePodExtras(podPO);
    }

    public void updatePodUuid(PodPO podPO) {
        podMapper.updatePodUuid(podPO);
    }

    /**
     * @Description: 更新 Pod 的客户端令牌。
     * @param podPO PodPO 类型，包含需要更新的 Pod 信息。
     * @return void 无返回值。
     */
    public void updatePodClientToken(PodPO podPO) {
        podMapper.updatePodClientToken(podPO);
    }

    public Timestamp updateSince() {
        return podMapper.updateSince();
    }

    public List<PodPO> listPodPOByIds(List<String> podIds, String accountId) {
        List<PodPO> podsByIds = podMapper.listPodByPodIds(podIds, accountId);
        Set<PodPO> podSet = new HashSet<>(podsByIds);
        if (podIds.size() != podsByIds.size()) {
            List<PodPO> podsByuuids = podMapper.listPodByPodUuids(podIds, accountId);
            podSet.addAll(podsByuuids);
        }
        return new ArrayList<>(podSet);
    }

    public Map<String, Map<String, Long>> countPodWithSubnetId(String accountId, List<String> subnetIds) {
        return podMapper.countPodWithSubnetId(accountId, subnetIds);
    }

    public List<PodPO> getResourceRecyclePod() {
        return podMapper.getResourceRecyclePod();
    }

    public void deleteEip(String accountId, String podId, String eipIp) {
        podMapper.deleteEip(accountId, podId, eipIp);
    }
    
    public void bindEip(String accountId, String podId, String eipIp, int bandwidthInMbps) {
        podMapper.bindEip(accountId, podId, eipIp, bandwidthInMbps);
    }

    public void unbindEip(String accountId, String podId, String eipIp, boolean isUserSpecifiedEip) {
        int status = isUserSpecifiedEip ? 4 : 2;
        podMapper.unbindEip(accountId, podId, eipIp, status);
    }

    public void updateEipsInfo(List<PodPO> podPOS, String accountId) {
        for (PodPO podPO : podPOS) {
            podMapper.updateEipInfo(podPO);
        }
    }

    public void updateNewEipsInfo(List<PodPO> podPOS, String accountId) {
        for (PodPO podPO : podPOS) {
            podMapper.updateNewEipInfo(podPO);
        }
    }
    
    public void updateUnbindedEipsInfo(List<PodPO> podPOS, String accountId) {
        for (PodPO podPO : podPOS) {
            podMapper.updateUnbindedEipInfo(podPO);
        }
    }

    public List<PodPO> getPodsByEip(String accountId, String eipIp, int eipStatus) {
        return podMapper.getPodsByEip(accountId, eipIp, eipStatus);
    }

    public PodPO podIdempotentCheck(PodPO podPO) {
        return podMapper.podIdempotentCheck(podPO);
    }

    public List<PodPOWithId> getActiveEipByUser(String accountId) {
        List<PodPOWithId> pods = new ArrayList<>();
        List<PodPOWithId> partPods = new ArrayList<>();
        long start = 0;
        do {
            try {
                partPods = podMapper.getActiveEipByUser(accountId, start, 500);
                if (partPods.size() > 0) {
                    start = partPods.get(partPods.size() - 1).getId();
                    pods.addAll(partPods);
                }
                // 减轻 mysql 的压力
                Thread.sleep(5);
            } catch (Exception e) {
                LOGGER.warn("getActiveEipByUser accountId:{}, start:{} error", accountId, start, e);
            }
        } while (!partPods.isEmpty());
        return pods;
    }

    public List<PodPO> getPodsByPodIds(List<String> podIds) {
        return podMapper.getPodsByPodIds(podIds);
    }

    public int updatePodConditionsByPodId(String userId, String podId, String conditions) {
        return podMapper.updatePodConditionsByPodId(userId, podId, conditions);
    }

    public int stateMachineCreatePod(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineCreatePod(podPO, context);
    }

    public int stateMachineUpdatePod(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineUpdatePod(podPO, context);
    }

    public int stateMachineDeletePod(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineDeletePod(podPO, context);
    }

    public int stateMachineK8SPodDeletedByUser(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineK8SPodDeletedByUser(podPO, context);
    }

    public int stateMachineK8SPodNoResourceAvailable(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineK8SPodNoResourceAvailable(podPO, context);
    }

    public int stateMachineK8SPodDeletedByK8SUnexpectedly(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineK8SPodDeletedByK8SUnexpectedly(podPO, context);
    }

    public int stateMachineK8SPodEvicted(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineK8SPodEvicted(podPO, context);
    }

    public int stateMachineResourceRecycleComplete(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineResourceRecycleComplete(podPO, context);
    }

    public int stateMachineOrderFailed(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineOrderFailed(podPO, context);
    }

    public int stateMachineK8SPodEventStatus(PodPO podPO, StateMachinePodDaoContext context) {
        return podMapper.stateMachineK8SPodEventStatus(podPO, context);
    }
}
