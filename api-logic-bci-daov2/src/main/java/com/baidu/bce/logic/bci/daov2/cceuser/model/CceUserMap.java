package com.baidu.bce.logic.bci.daov2.cceuser.model;

import com.baidu.bce.internalsdk.core.BceConstant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CceUserMap implements Cloneable {
    private long id;
    private String userId = "";
    private String cceIds = "";
    private String blsUserToken = "";
    // 是否开启秒级计费，默认开启
    private Boolean cpt1 = true;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;


    @JsonIgnore
    private int deleted = 0;

    public CceUserMap() {
    }

    public CceUserMap(String userId, String cceIds) {
        Timestamp currentTime = new Timestamp(new Date().getTime());

        this.userId = userId;
        this.cceIds = cceIds;
        this.createdTime = currentTime;
        this.updatedTime = currentTime;
    }

}