package com.baidu.bce.logic.bci.daov2.webshell.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class WebshellPO {

    private int id;

    private String userId;

    private String cceId;

    private String podId;

    private String containerName;

    private String wsUrl;

    private String token;

    private int version;

    //    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    //    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    //    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;

    @JsonIgnore
    private int deleted;
}
