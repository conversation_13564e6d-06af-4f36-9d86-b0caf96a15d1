package com.baidu.bce.logic.bci.daov2.container;

import com.baidu.bce.logic.bci.daov2.container.mapper.ContainerMapperV2;
import com.baidu.bce.logic.bci.daov2.container.model.ContainerPO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ContainerDaoV2 {

    private static final String POD_PREFIX = "p-";

    @Autowired
    private ContainerMapperV2 containerMapper;

    public void batchInsert(List<ContainerPO> containerPOS) {
        containerMapper.batchInsert(containerPOS);
    }

    public List<ContainerPO> listByPodId(String podId) {
        return containerMapper.listByPodId(podId);
    }

    /**
     * {@inheritDoc}
     *
     * 根据 Pod ID 列出容器的轻量级信息。
     *
     * @param podId Pod ID
     * @return 包含轻量级容器信息的 List，如果没有找到则返回空列表
     */
    public List<ContainerPO> listContainersLightByPodId(String podId) {
        return containerMapper.listContainersLightByPodId(podId);
    }

    /**
     * {@inheritDoc}
     *
     * 根据 Pod ID 列出容器的轻量级信息。
     *
     * @param podId Pod ID
     * @return 包含轻量级容器信息的 List，如果没有找到则返回空列表
     */
    public List<ContainerPO> listContainersWithDeletedByPodId(String podId) {
        return containerMapper.listContainersWithDeletedByPodId(podId);
    }

    /**
     * 根据账户ID和容器组UUID列表，查询轻量级的容器信息。
     *
     * @param accountId 账户ID
     * @param podUuids 容器组UUID列表
     * @return 返回一个List类型，包含所有符合条件的轻量级容器信息（ContainerPO）
     */
    public List<ContainerPO> listContainersLightByPodUuids(String accountId, List<String> podUuids) {
        return containerMapper.listContainersLightByPodUuids(accountId, podUuids);
    }

    public void batchUpdate(List<ContainerPO> containerPOS) {
        for (ContainerPO containerPO : containerPOS) {
            containerMapper.update(containerPO);
        }
    }

    public void deleteContainers(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            containerMapper.deleteContainerById(accountId, podId);
        } else {
            containerMapper.deleteContainerByUuid(accountId, podId);
        }

    }

    public void batchDelete(String accountId, List<Long> containerIds) {
        if (containerIds == null || containerIds.size() == 0) {
            return;
        }
        containerMapper.batchDeleteContainersByIds(accountId, containerIds);
    }

    public void updateContainersPodId(String podId, String podUuid) {
        containerMapper.updateContainersByPodId(podId, podUuid);
    }

    public String queryContainerUuid(String podUuid, String name) {
        return containerMapper.queryContainerUuid(podUuid, name);
    }

    public List<ContainerPO> listContainerByPod(String accountId, List<String> podIds) {
        return containerMapper.listPodContainerByPodIds(accountId, podIds);
    }

    public int updateContainerCurrentStateByPodUuid(String userId, String podUuid, String currentState) {
        return containerMapper.updateContainerCurrentStateByPodUuid(userId, podUuid, currentState);
    }
}
