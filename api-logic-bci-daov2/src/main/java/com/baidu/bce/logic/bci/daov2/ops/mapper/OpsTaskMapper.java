package com.baidu.bce.logic.bci.daov2.ops.mapper;

import com.baidu.bce.logic.bci.daov2.ops.model.OpsTaskPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface OpsTaskMapper {

    String INSERT_OPS_RECORD = "INSERT INTO t_ops_record (user_id, cce_id, pod_id, uuid, ops_type, ops_value, " +
            "storage_type," +
            " " +
            "storage_contents, created_time, updated_time, deleted_time, deleted,completed) ";

    String QUERY_OPS_RECORD = "select user_id, cce_id, pod_id, uuid, ops_type, ops_value, storage_type, " +
            "storage_contents, " +
            "created_time, updated_time, deleted_time, deleted, completed from t_ops_record";

    @Insert(INSERT_OPS_RECORD + "values (@{opsPo.userId},@{opsPo.cceId},@{opsPo.podId},@{opsPo.uuid},@{opsPo" +
            ".opsType}," +
            "@{opsPo.opsValue},@{opsPo.storageType},@{opsPo.storageContents},@{opsPo.createdTime},@{opsPo" +
            ".updatedTime},@{opsPo.deletedTime},@{opsPo.deleted}, @{opsPo.completed} )")
    void insert(@Param("opsPo") OpsTaskPO opsPo);

    @Select(QUERY_OPS_RECORD + " where pod_id = @{podId} and ops_type = @{opsType} and deleted = 0 order by id desc ")
    List<OpsTaskPO> queryOpsRecordByPodIdAndOpsType(@Param("podId") String podId, @Param("opsType") String opsType);

    @Select(QUERY_OPS_RECORD + " where uuid = @{uuid} and deleted = 0 ")
    OpsTaskPO queryOpsTaskByUuid(@Param("uuid") String uuid);

    @Update("update t_ops_record set storage_contents = @{storageContents}, completed = @{completed} where uuid = " +
            "@{uuid} and pod_id = @{podId} and deleted = 0 ")
    int updateOpsTaskContents(@Param("storageContents") String storageContents, @Param("completed") int completed,
                              @Param("uuid") String uuid,
                              @Param("podId") String podId);
}
