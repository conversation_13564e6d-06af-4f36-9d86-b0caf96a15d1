package com.baidu.bce.logic.bci.daov2.podmigration.mapper;

import com.baidu.bce.logic.bci.daov2.podmigration.model.PodMigrationPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

public interface PodMigrationMapper {

    @Insert("INSERT INTO t_pod_migration(pod_id, migration_uuid, creaeted_at) VALUES(@{podId}, @{migrationUuid}, now())")
    void insert(@Param("podId") String podId, @Param("migrationUuid") String migrationUuid);

    @Select("SELECT id, pod_id, migration_uuid, created_at, updated_at FROM t_pod_migration WHERE pod_id = @{podId}")
    List<PodMigrationPO> selectByPodId(@Param("podId") String podId);

    @Select("SELECT id, pod_id, migration_uuid, created_at, updated_at FROM t_pod_migration WHERE migration_uuid = @{migrationUuid}")
    List<PodMigrationPO> selectByMigrationUuid(@Param("migrationUuid") String migrationUuid);
}
