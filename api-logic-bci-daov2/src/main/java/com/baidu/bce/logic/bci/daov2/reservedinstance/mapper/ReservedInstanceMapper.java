package com.baidu.bce.logic.bci.daov2.reservedinstance.mapper;

import java.util.List;

import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.ReservedInstanceDao.ReservedInstanceQueryFilter;
import com.baidu.bce.logic.core.request.OrderModel;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface ReservedInstanceMapper {
    String INSERT_RESERVED_INSTANCE = "INSERT INTO t_reserved_instance(user_id, account_id, name, " + 
            "reserved_instance_id, reserved_instance_uuid, scope, physical_zone, order_id, reserve_resource, " + 
            "purchase_mode, status, deleted, reserved_spec, reserved_instance_count, reserved_time_unit, " +
            "reserved_time_period, auto_renew, auto_renew_time_unit, auto_renew_time_period, effective_time, " +
            "expire_time, deleted_time, logical_zone) ";

    @Insert(INSERT_RESERVED_INSTANCE + "values " +
            "(@{ri.userId}, @{ri.accountId}, @{ri.name}, @{ri.reservedInstanceId}, @{ri.reservedInstanceUuid}, " +
            "@{ri.scope}, @{ri.physicalZone}, @{ri.orderId}, @{ri.reserveResource}, @{ri.purchaseMode}, " +
            "@{ri.status}, @{ri.deleted}, @{ri.reservedSpec}, @{ri.reservedInstanceCount}, @{ri.reservedTimeUnit}, " +
            "@{ri.reservedTimePeriod}, @{ri.autoRenew}, @{ri.autoRenewTimeUnit}, @{ri.autoRenewTimePeriod}, " +
            "@{ri.effectiveTime}, @{ri.expireTime}, @{ri.deletedTime}, @{ri.logicalZone})")
    int insertReservedInstance(@Param("ri") ReservedInstancePO reservedInstancePO);

    @Insert(INSERT_RESERVED_INSTANCE + "values " +
            "#repeat($_parameter.ris $ri \",\")" +
            "   (@{ri.userId}, @{ri.accountId}, @{ri.name}, @{ri.reservedInstanceId}, @{ri.reservedInstanceUuid}, " +
            "   @{ri.scope}, @{ri.physicalZone}, @{ri.orderId}, @{ri.reserveResource}, @{ri.purchaseMode}, " +
            "   @{ri.status}, @{ri.deleted}, @{ri.reservedSpec}, @{ri.reservedInstanceCount}, " +
            "   @{ri.reservedTimeUnit}, @{ri.reservedTimePeriod}, @{ri.autoRenew}, @{ri.autoRenewTimeUnit}, " +
            "   @{ri.autoRenewTimePeriod}, @{ri.effectiveTime}, @{ri.expireTime}, @{ri.deletedTime}, " +
            "   @{ri.logicalZone})" +
            "#end")
    int batchInsertReservedInstance(@Param("ris") List<ReservedInstancePO> reservedInstancePOList);

    @Update("update t_reserved_instance set status = @{status} where deleted = 0 and account_id = " +
            "@{accountId} and reserved_instance_id = @{reservedInstanceId}")
    void updateStatus(@Param("accountId") String accountId,
            @Param("reservedInstanceId") String reservedInstanceId, @Param("status") String status);

    @Update("update t_reserved_instance set reserved_instance_uuid = @{reservedInstanceUuid} where deleted = 0 " +
            "and account_id = @{accountId} and reserved_instance_id = @{reservedInstanceId}")      
    void updateUuid(@Param("accountId") String accountId,
            @Param("reservedInstanceId") String reservedInstanceId,
            @Param("reservedInstanceUuid") String reservedInstanceUuid);

    @Update("update t_reserved_instance set resource_uuid = @{resourceUuid} where deleted = 0 " +
            "and account_id = @{accountId} and reserved_instance_id = @{reservedInstanceId}")      
    void updateResourceUuid(@Param("accountId") String accountId,
            @Param("reservedInstanceId") String reservedInstanceId,
            @Param("resourceUuid") String resourceUuid);

    @Update("update t_reserved_instance set order_id = @{orderId}"
            + "#where() "
            + "  account_id = @{accountId} "
            + "  #if($_parameter.reservedInstanceIds && $_parameter.reservedInstanceIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.reservedInstanceIds $reservedInstanceId \" reserved_instance_id \" ) "
            + "         @{reservedInstanceId}"
            + "     #end"
            + "  #end "
            + "#end ")
    void updateOrderId(@Param("reservedInstanceIds") List<String> reservedInstanceIds,
            @Param("accountId") String accountId, @Param("orderId") String orderId);

    @Update("update t_reserved_instance set order_id = @{orderId}"
            + "#where() "
            + "  #if($_parameter.reservedInstanceUuids && $_parameter.reservedInstanceUuids.size() > 0)"
            + "   and  "
            + "     #in($_parameter.reservedInstanceUuids $reservedInstanceUuid \" reserved_instance_uuid \" ) "
            + "         @{reservedInstanceUuid}"
            + "     #end"
            + "  #end "
            + "#end ")
    void updateOrderIdByUuid(@Param("reservedInstanceUuids") List<String> reservedInstanceUuids,
            @Param("orderId") String orderId);

    @Update("update t_reserved_instance set order_id = @{orderId}"
            + "#where() "
            + "  #if($_parameter.resourceUuids && $_parameter.resourceUuids.size() > 0)"
            + "   and  "
            + "     #in($_parameter.resourceUuids $resourceUuid \" resource_uuid \" ) "
            + "         @{resourceUuid}"
            + "     #end"
            + "  #end "
            + "#end ")
    void updateOrderIdByResourceUuid(@Param("resourceUuids") List<String> resourceUuids,
            @Param("orderId") String orderId);
    // TODO: 分页查询数据&总量，理论可使用以下语句优化，mybatis实现方式待调研
    //      SELECT SQL_CALC_FOUND_ROWS * FROM [table] WHERE ......  limit M, N;
    //      SELECT FOUND_ROWS();
    @Select("SELECT * from t_reserved_instance " +
            "#where() " +
            "  account_id = @{accountId} and deleted = 0 " +
            "  #if($_parameter.queryList && $_parameter.queryList.size() > 0) " +
            "    #repeat($_parameter.queryList $query \" and \" \" and \" ) " +
            "      $check.column(${query.name}) like @{query.value} " +
            "    #end " +
            "  #end " +
            "#end " +
            "#if($_parameter.orders && $_parameter.orders.size()>0) " +
            "  #repeat($_parameter.orders $orderModel \" , \" \" order by  \" ) " +
            "    $check.column(${orderModel.orderBy}) $check.order(${orderModel.order}) " +
            "  #end " +
            "#end " +
            "LIMIT @{offset}, @{pageSize}")
    List<ReservedInstancePO> listReservedInstancesByMultiKey(@Param("accountId") String accountId,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("orders") List<OrderModel> orders,
            @Param("queryList") List<ReservedInstanceQueryFilter> queryList);

    @Select("SELECT count(*) from t_reserved_instance " +
            "#where() " +
            "  account_id = @{accountId} and deleted = 0 " +
            "  #if($_parameter.queryList && $_parameter.queryList.size() > 0) " +
            "    #repeat($_parameter.queryList $query \" and \" \" and \" ) " +
            "      $check.column(${query.name}) like @{query.value} " +
            "    #end " +
            "  #end " +
            "#end " +
            "#if($_parameter.orders && $_parameter.orders.size()>0) " +
            "  #repeat($_parameter.orders $orderModel \" , \" \" order by  \" ) " +
            "    $check.column(${orderModel.orderBy}) $check.order(${orderModel.order}) " +
            "  #end " +
            "#end ")
    int getReservedInstanceNumByMultiKey(@Param("accountId") String accountId,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("orders") List<OrderModel> orders,
            @Param("queryList") List<ReservedInstanceQueryFilter> queryList);
    
    @Select("SELECT * from t_reserved_instance where deleted = 0 and status in ('NEED_PURCHASE', " +
            "'READY_FOR_CREATE', 'CREATING')")
    List<ReservedInstancePO> listReservedInstancesInBuild();

    @Select("SELECT * from t_reserved_instance where deleted = 0 and status in ('INACTIVE', " +
            "'ACTIVE')")
    List<ReservedInstancePO> listCreatedReservedInstances();

    @Select("SELECT * from t_reserved_instance where deleted = 0 and order_id = @{orderId}")
    List<ReservedInstancePO> listReservedInstancesByOrderId(@Param("orderId") String orderId);

    @Select("SELECT * from t_reserved_instance where deleted = 0 and reserved_instance_uuid = @{uuid}")
    ReservedInstancePO getReservedInstanceByReservedInstanceUuid(@Param("uuid") String uuid);

    @Select("SELECT * from t_reserved_instance where deleted = 0 and resource_uuid = @{uuid}")
    ReservedInstancePO getReservedInstanceByResourceUuid(@Param("uuid") String uuid);
    
    @Update("update t_reserved_instance set deleted = 1, status = 'DELETED', deleted_time = now()" +
            " where deleted = 0 and id = @{id}")
    void deleteById(@Param("id") long id);

    @Update("update t_reserved_instance set deleted = 1, status = 'DELETED', deleted_time = now()" +
            " where deleted = 0 and reserved_instance_uuid = @{uuid}")
    void deleteByReservedInstanceUuid(@Param("uuid") String reservedInstanceUuid);

    @Select("select sum(reserved_instance_count) from t_reserved_instance where deleted = 0 and " +
            "reserved_spec = @{spec} and physical_zone = @{az} and account_id = @{accountId} and " +
            "effective_time >= @{startTimeLeftValue} and effective_time <= @{startTimeRightValue} " +
            "and expire_time >= @{endTimeLeftValue} and expire_time <= @{endTimeRightValue}")
    Integer getBciStockDemand(@Param("spec") String spec, @Param("az") String az, @Param("accountId") String userId,
                              @Param("startTimeLeftValue") String startTimeLeftValue, 
                              @Param("startTimeRightValue") String startTimeRightValue, 
                              @Param("endTimeLeftValue") String endTimeLeftValue,
                              @Param("endTimeRightValue") String endTimeRightValue);

    @Update("update t_reserved_instance set expire_time = @{ri.expireTime}, status = @{ri.status}, " + 
           "effective_time = @{ri.effectiveTime} where deleted = 0 and resource_uuid = @{ri.resourceUuid}")
    void updateExpireTimeAndEffectiveTimeAndStatusByResourceUuid(@Param("ri") ReservedInstancePO reservedInstancePO);

    @Select("SELECT * from t_reserved_instance where reserved_instance_id = @{id}")
    ReservedInstancePO getReservedInstanceByReservedInstanceId(@Param("id") String id);
}
