package com.baidu.bce.logic.bci.daov2.bls.model;

import com.baidu.bce.internalsdk.core.BceConstant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BlsInfo implements Cloneable {
    private String userId = "";
    private String blsTaskName = "";
    private String blsTaskId = "";
    private String blsTaskOwner = "";
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;

    @JsonIgnore
    private Boolean deleted = false;

    public BlsInfo() {
    }

    public BlsInfo(String userId, String blsTaskName, String blsTaskId) {
        Timestamp currentTime = new Timestamp(new Date().getTime());
        this.userId = userId;
        this.blsTaskName = blsTaskName;
        this.blsTaskId = blsTaskId;
        this.createdTime = currentTime;
        this.updatedTime = currentTime;
    }

    public enum TaskOwner {
        USER,
        BCI
    }
}
