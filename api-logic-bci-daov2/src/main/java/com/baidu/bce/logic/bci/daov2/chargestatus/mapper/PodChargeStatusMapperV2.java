package com.baidu.bce.logic.bci.daov2.chargestatus.mapper;

import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.sql.Timestamp;
import java.util.List;

public interface PodChargeStatusMapperV2 {

    @Insert("INSERT INTO t_pod_charge_status_v2(pod_uuid, previous_state, current_state, charge_state, "
            + "resource_version, cpt1_sync_state, created_time, update_time) "
            + "select @{podChargeStatus.podUuid,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.previousState,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.currentState,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.chargeState,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.resourceVersion,jdbcType=BIGINT}, "
            + "@{podChargeStatus.cpt1SyncState,jdbcType=TINYINT}, "
            + "@{podChargeStatus.createdTime}, "
            + "@{podChargeStatus.updateTime} "
            + "FROM DUAL WHERE NOT EXISTS( "
            + "SELECT id from t_pod_charge_status_v2 WHERE pod_uuid = @{podChargeStatus.podUuid,jdbcType=VARCHAR} "
            + "AND charge_state = @{podChargeStatus.chargeState,jdbcType=VARCHAR} "
            + "AND created_time = (SELECT MAX(created_time) FROM t_pod_charge_status_v2 "
            + "WHERE pod_uuid = @{podChargeStatus.podUuid,jdbcType=VARCHAR}))")
    @Options(useGeneratedKeys=true, keyProperty="podChargeStatus.id", keyColumn="id")
    int insert(@Param("podChargeStatus") PodChargeStatus podChargeStatus);

    // 仅用于 syncPodInBuild，规避重复插入
    @Insert("INSERT INTO t_pod_charge_status_v2(pod_uuid, previous_state, current_state, charge_state, "
        + "resource_version, cpt1_sync_state, created_time, update_time) "
        + "select @{podChargeStatus.podUuid,jdbcType=VARCHAR}, "
        + "@{podChargeStatus.previousState,jdbcType=VARCHAR}, "
        + "@{podChargeStatus.currentState,jdbcType=VARCHAR}, "
        + "@{podChargeStatus.chargeState,jdbcType=VARCHAR}, "
        + "@{podChargeStatus.resourceVersion,jdbcType=BIGINT}, "
        + "@{podChargeStatus.cpt1SyncState,jdbcType=TINYINT}, "
        + "@{podChargeStatus.createdTime}, "
        + "@{podChargeStatus.updateTime} "
        + "FROM DUAL WHERE NOT EXISTS( "
        + "SELECT id from t_pod_charge_status_v2 WHERE pod_uuid = @{podChargeStatus.podUuid,jdbcType=VARCHAR} "
        + "AND charge_state = @{podChargeStatus.chargeState,jdbcType=VARCHAR} "
        + "AND created_time = (SELECT MAX(created_time) FROM t_pod_charge_status_v2 "
        + "WHERE pod_uuid = @{podChargeStatus.podUuid,jdbcType=VARCHAR})) AND "
        + "NOT EXISTS( SELECT 1 FROM t_pod_v2 WHERE pod_uuid = @{podChargeStatus.podUuid,jdbcType=VARCHAR} AND "
        + "internal_status = \"synced\")")
    @Options(useGeneratedKeys=true, keyProperty="podChargeStatus.id", keyColumn="id")
    int insertWhenPodUnsync(@Param("podChargeStatus") PodChargeStatus podChargeStatus);

    @Select("select id, pod_uuid, previous_state, current_state, charge_state, cpt1_sync_state, created_time, "
            + "update_time from t_pod_charge_status_v2 where created_time >= @{lastChargeTime} and "
            + "created_time < @{chargeTime} "
            + "and pod_uuid != @{sincePlaceHolder} and cpt1_sync_state = 0 "
            + "order by created_time asc")
    List<PodChargeStatus> listCpcChargesByTime(@Param("lastChargeTime") Timestamp lastChargeTime,
                                     @Param("chargeTime") Timestamp chargeTime,
                                     @Param("sincePlaceHolder") String sincePlaceHolder);

    @Select("select id, pod_uuid, previous_state, current_state, charge_state, cpt1_sync_state, created_time, "
            + "update_time from t_pod_charge_status_v2 where pod_uuid = @{sincePlaceHolder} "
            + "order by created_time desc limit 1")
    PodChargeStatus getSinceTime(@Param("sincePlaceHolder") String sincePlaceHolder);

    @Update("update t_pod_charge_status_v2 set created_time = @{createdTime} "
            + " where pod_uuid = @{sincePlaceHolder}")
    void updateSinceTime(@Param("createdTime") Timestamp createdTime,
                         @Param("sincePlaceHolder") String sincePlaceHolder);

    @Select("select max(resource_version) from t_pod_charge_status_v2 " +
            "where pod_uuid = @{uuid} and resource_version > @{resourceVersion}")
    Long getMaxResourceVersion(@Param("uuid") String uuid,
                               @Param("resourceVersion") long resourceVersion);

    @Select("select id, pod_uuid, previous_state, current_state, charge_state, cpt1_sync_state, created_time, "
            + "update_time from t_pod_charge_status_v2 "
            + "where id = @{id}")
    PodChargeStatus queryByID(@Param("id") long id);

    @Select("select id, pod_uuid, previous_state, current_state, charge_state, resource_version, cpt1_sync_state,"
            + " created_time, update_time from t_pod_charge_status_v2 "
            + " where pod_uuid = @{uuid} and resource_version = @{resourceVersion} order by id desc limit 1")
    PodChargeStatus queryByResourceVersion(@Param("uuid") String uuid,
                                           @Param("resourceVersion") long resourceVersion);

    @Update("update t_pod_charge_status_v2 set cpt1_sync_state = @{cpt1SyncState}, update_time = @{updateTime} "
            + " where id = @{id} and resource_version = @{resourceVersion} and"
            + " pod_uuid = @{uuid} and cpt1_sync_state != @{cpt1SyncState}")
    void updateCpt1SyncStateById(@Param("id") long id,
                                 @Param("resourceVersion") long resourceVersion,
                                 @Param("uuid") String uuid,
                                 @Param("cpt1SyncState") int cpt1SyncState,
                                 @Param("updateTime") Timestamp updateTime);

    @Select("select p1.* from t_pod_charge_status_v2 as p1 inner join " +
            "(select pod_uuid, max(id) as max_id from t_pod_charge_status_v2 group by pod_uuid) as p2 " +
            "on p1.id=p2.max_id and p1.cpt1_sync_state= 1;")
    List<PodChargeStatus> listNeedToSyncCpt1Status();

    @Select("select charge_state from t_pod_charge_status_v2 where pod_uuid = @{uuid} and resource_version = "
            + "(select max(resource_version) from t_pod_charge_status_v2 where pod_uuid = @{uuid}) "
            + "order by id desc limit 1;")
    String getChargeStatusWhichResourceVersionIsNewest(@Param("uuid") String uuid);

    @Select("select charge_state from t_pod_charge_status_v2 where pod_uuid = @{uuid} and created_time = "
            + "(select max(created_time) from t_pod_charge_status_v2 where pod_uuid = @{uuid}) "
            + "order by id desc limit 1;")
    String getChargeStatusWhichCreatedTimeIsNewest(@Param("uuid") String uuid);

    @Select("select id, pod_uuid, charge_state, created_time "
            + "from t_pod_charge_status_v2 where created_time >= @{startStamp} and created_time < @{endStamp} "
            + "and pod_uuid = @{podUuid} "
            + "order by created_time asc")
    List<PodChargeStatus> getCpcTestPodChargeStatus1(@Param("podUuid")String podUuid,
                                                     @Param("startStamp")Timestamp startStamp,
                                                     @Param("endStamp")Timestamp endStamp);

    @Select("select id, pod_uuid, charge_state, created_time "
            + "from t_pod_charge_status_v2 where created_time <= @{startStamp}"
            + "and pod_uuid = @{podUuid} "
            + "order by created_time desc")
    List<PodChargeStatus> getCpcTestPodChargeStatus2(@Param("podUuid")String podUuid,
                                                     @Param("startStamp")Timestamp startStamp);
}
