package com.baidu.bce.logic.bci.daov2.imageaccelerate.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import java.sql.Timestamp;

@Data
@Accessors(chain = true)
public class ImageAcceleratePO {
    private long id;
    private String name = "";
    // images: map to json string, map<key: originImageAddress, value: targetImageAddress>
    private String images = "";
    private String orderId = "";
    private String description = "";
    private String accountId = "";
    private String zoneId = "";
    private String physicalZone = "";
    private String logicalZone = "";
    private int enableEni;
    private int needEip;
    private String userId = "";
    private String subnetId = "";
    private String securityGroupIds = "";
    private String vpcCidr = "";
    private String zoneSubnets = "";
    private long usedNum;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    private int deleted = 0;
    private String imageSecrets;

    private String status;
    private int progress;

    private String cdsSnapshotId;
    private String cpuTypes;
    private int scanDone;
}
