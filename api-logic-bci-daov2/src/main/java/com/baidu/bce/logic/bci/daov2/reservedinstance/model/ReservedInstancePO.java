package com.baidu.bce.logic.bci.daov2.reservedinstance.model;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.billing.auditing.sdk.domain.ResourceStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReservedInstancePO implements Cloneable {
    private long id;
    private String userId = "";
    private String accountId = "";
    private String name = "";
    private String resourceUuid = "";             // Billing 资源 uuid
    private String reservedInstanceId = "";       // bci 侧资源包短 id
    private String reservedInstanceUuid = "";     // 在 Billing 建的资源包 uuid
    private String scope = "";
    private String physicalZone = "";
    private String logicalZone = "";
    private String orderId = "";
    private boolean reserveResource = true;
    private String purchaseMode = "";
    private String status = "";
    private boolean deleted = false;
    private String reservedSpec = "";
    private int reservedInstanceCount = 0;
    private String reservedTimeUnit = "";
    private int reservedTimePeriod = 0;
    private boolean autoRenew = false;
    private String autoRenewTimeUnit = "";
    private int autoRenewTimePeriod = 0;
    @JsonProperty(value = "tags")
    private List<Tag> tags;      // 预留实例券在标签服务绑定的标签，由于可能在外部变更，数据库不存储
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp effectiveTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp expireTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp deletedTime;

    public static class Status {
        public static final String READY_FOR_CREATE = "READY_FOR_CREATE";
        public static final String NEED_PURCHASE = "NEED_PURCHASE";
        public static final String CREATING = "CREATING";
        public static final String CREATE_FAILED = "CREATE_FAILED";
        public static final String INACTIVE = "INACTIVE";
        public static final String ACTIVE = "ACTIVE";
        public static final String EXPIRED = "EXPIRED";
        public static final String OVERDUE = "OVERDUE";
    }

    public static Timestamp calcExpireTime(Timestamp ts, String timeUnit, int timePeriod) {
        Calendar c = Calendar.getInstance();
        c.setTime(ts);
        if ("DAY".equals(timeUnit)) {
            c.add(Calendar.DATE, timePeriod);
        } else if ("MONTH".equals(timeUnit)) {
            c.add(Calendar.MONTH, timePeriod);
        } else if ("YEAR".equals(timeUnit)) {
            c.add(Calendar.YEAR, timePeriod);
        } else {
            throw new CommonExceptions.RequestInvalidException(String.format("%s is not a valied time unit",
                    timeUnit));
        }
        
        return new Timestamp(c.getTimeInMillis());
    }

    public static ResourceStatus convertToBillingStatus(String statusStr) {
        if (statusStr.equals(Status.INACTIVE)) {
            return ResourceStatus.RUNNING;
        } else if (statusStr.equals(Status.ACTIVE)) {
            return ResourceStatus.RUNNING;
        } else if (statusStr.equals(Status.EXPIRED)) {
            return ResourceStatus.DESTROYED;
        } else if (statusStr.equals(Status.OVERDUE)) {
            return ResourceStatus.STOPPED;
        }
        return ResourceStatus.RUNNING;
    }
}
