package com.baidu.bce.logic.bci.daov2.chargestatus.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class PodChargeStatus {
    private long id;
    private String podUuid = "";
    private String previousState = "";
    private String currentState = "";
    private String chargeState = "";
    private long resourceVersion; // 对应k8s pod的resource version
    private int cpt1SyncState;    // cpt1计费模式的同步状态，0表示CPC，1未完成，2CPt1同步完成，改为完成需要更改修改时间
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updateTime;
}
