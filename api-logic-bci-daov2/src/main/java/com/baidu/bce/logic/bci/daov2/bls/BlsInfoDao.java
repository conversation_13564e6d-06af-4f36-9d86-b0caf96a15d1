package com.baidu.bce.logic.bci.daov2.bls;

import com.baidu.bce.logic.bci.daov2.bls.mapper.BlsInfoMapper;
import com.baidu.bce.logic.bci.daov2.bls.model.BlsInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.dao.DataAccessException;

import java.util.List;
import java.util.ArrayList;
@Repository("blsInfoDao")
public class BlsInfoDao {
    
    @Autowired
    private BlsInfoMapper blsInfoMapper;

    public BlsInfo select(String blsTaskName, String userId) {
        return blsInfoMapper.getBlsTaskByUserId(blsTaskName, userId);
    }

    public BlsInfo getBlsInfoByTaskId(String blsTaskId, String userId) {
        return blsInfoMapper.getBlsInfoByTaskId(blsTaskId, userId);
    }

    public void update(BlsInfo blsMap) {
        blsInfoMapper.updateBlsTaskMap(blsMap); 
    }

    public void insert(BlsInfo blsMap) throws DataAccessException {
        blsInfoMapper.insertBlsTaskMap(blsMap);
    }

    public void delete(BlsInfo blsMap) {
        blsInfoMapper.deleteBlsTaskMap(blsMap);
    }

    // 批量查询任务是否在数据库中
    public List<BlsInfo> batchSelect(List<String> blsTaskNames, String userId) {
        List<BlsInfo> blsInfos = new ArrayList<>();
        for (String blsTaskName : blsTaskNames) {
            BlsInfo blsInfo =  blsInfoMapper.getBlsTaskByUserId(blsTaskName, userId); 
            blsInfos.add(blsInfo);
        }
        return blsInfos;
    }
}
