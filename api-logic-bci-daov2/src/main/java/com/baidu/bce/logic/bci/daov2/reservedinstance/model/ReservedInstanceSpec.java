package com.baidu.bce.logic.bci.daov2.reservedinstance.model;

import java.sql.Timestamp;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReservedInstanceSpec implements Cloneable {
    private long id;
    // 预留实例券类型，地域级REGION或可用区级AZ
    private String scope;
    // 预留实例券物理可用区，scope 为 REGION 时无效
    private String physicalZone;
    private String name;
    // 抵扣实例类型，可选 CPU-generic，GPU-generic
    private String deductInstanceFamily;
    private double vcpuNum;
    private double memGB;
    private String gpuName;
    private int gpuNum;
    private boolean fullyPrepaySupport;
    private boolean partPrepaySupport;
    private boolean postpaySupport;
    private boolean deleted = false;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_LOCAL_FORMAT)
    private Timestamp deletedTime;

    // 在查询时根据客户可用区映射关系生成
    private String logicalZone = "";
}
