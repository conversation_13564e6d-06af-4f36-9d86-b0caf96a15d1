package com.baidu.bce.logic.bci.daov2.webshell.mapper;

import com.baidu.bce.logic.bci.daov2.webshell.model.WebshellPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface WebshellMapper {

    String INSERT_WEB_SHELL = "INSERT INTO t_webshell_token (user_id, cce_id, pod_id, container_name, ws_url, token, " +
            "version, created_time, updated_time, deleted_time, deleted) ";

    String QUERY_WEB_SHELL = "select user_id, cce_id, pod_id, container_name, ws_url, token, version, " +
            "created_time, " +
            "updated_time, deleted_time, deleted from t_webshell_token";

    @Insert(INSERT_WEB_SHELL + " values " +
            "(@{webshellPo.userId}, @{webshellPo.cceId}, @{webshellPo.podId}, @{webshellPo.containerName}, " +
            "@{webshellPo" +
            ".wsUrl}, @{webshellPo.token}, @{webshellPo.version}, @{webshellPo.createdTime}, @{webshellPo" +
            ".updatedTime}, @{webshellPo.deletedTime}, @{webshellPo.deleted} )")
    void insert(@Param("webshellPo") WebshellPO po);

    @Select(QUERY_WEB_SHELL + " where token = @{token} and deleted = 0")
    WebshellPO getByToken(@Param("token") String token);

    @Update("update t_webshell_token set version = @{version}+1 ,deleted = 1 where token = @{token} and version = " +
            "@{version}")
    int lockWebshellToken(@Param("token") String token, @Param("version") int version);
}
