package com.baidu.bce.logic.bci.daov2.ops.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class OpsTaskPO {
    private int id;
    private String userId;
    private String cceId;
    private String podId;
    private String uuid;
    private String opsType;
    private String opsValue;
    private String storageType;
    private String storageContents;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private Timestamp deletedTime;
    @JsonIgnore
    private int deleted;
    @JsonIgnore
    private int completed;
}
