package com.baidu.bce.logic.bci.daov2.podextrav2;

import com.baidu.bce.logic.bci.daov2.podextrav2.mapper.PodExtraMapperV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.List;

@Repository
public class PodExtraDaoV2 {
    @Autowired
    private PodExtraMapperV2 podExtraMapper;

    private static final String POD_PREFIX = "p-";

    private static final Logger LOGGER = LoggerFactory.getLogger(PodExtraDaoV2.class);

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final Long UTC_TIME = 8 * 60 * 60 * 1000L;

    public int insertPodExtra(PodExtraPO podExtraPO) {
        return podExtraMapper.insertPodExtra(podExtraPO);
    }

    public int updateOrderId(List<String> podIds, String userId, String orderId) {
        return podExtraMapper.updateOrderId(podIds, userId, orderId);
    }

    public PodExtraPO getPodExtraByPodId(String podId) {
        return podExtraMapper.getPodExtraByPodId(podId);
    }

    public PodExtraPO getPodExtraByPodIdIgnoreStatus(String podId) {
        return podExtraMapper.getPodExtraByPodIdIgnoreStatus(podId);
    }

    public List<PodExtraPO> getPodExtraByPodIds(String userId, List<String> podIds) {
        return podExtraMapper.getPodExtraByPodIds(userId, podIds);
    }

    public List<PodExtraPO> getPodExtraByPodIdsIgnoreStatus(String userId, List<String> podIds) {
        return podExtraMapper.getPodExtraByPodIdsIgnoreStatus(userId, podIds);
    }

    public int deletePodExtra(String userId, String podId) {
        return podExtraMapper.deletePodExtra(userId, podId);
    }

    public int deletePodExtra(PodExtraPO podExtraPO) {
        return deletePodExtra(podExtraPO.getUserId(), podExtraPO.getPodId());
    }

    public int updatePodExtraResourceUuid(PodExtraPO podExtraPO) {
        return podExtraMapper.updatePodExtraResourceUuid(podExtraPO);
    }
}
