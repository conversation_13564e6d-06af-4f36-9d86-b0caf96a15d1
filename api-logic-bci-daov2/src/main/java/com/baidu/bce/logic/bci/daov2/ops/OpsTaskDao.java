package com.baidu.bce.logic.bci.daov2.ops;

import com.baidu.bce.logic.bci.daov2.ops.mapper.OpsTaskMapper;
import com.baidu.bce.logic.bci.daov2.ops.model.OpsTaskPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("opsTaskDao")
public class OpsTaskDao {

    @Autowired
    private OpsTaskMapper opsTaskMapper;

    public void insert(OpsTaskPO opsPO) {
        opsTaskMapper.insert(opsPO);
    }

    public List<OpsTaskPO> queryOpsRecordByPodIdAndOpsType(String podId, String opsType) {
        return opsTaskMapper.queryOpsRecordByPodIdAndOpsType(podId, opsType);
    }

    public OpsTaskPO queryOpsTaskByUuid(String uuid) {
        return opsTaskMapper.queryOpsTaskByUuid(uuid);
    }

    public int updateOpsTaskContents(String storageContents, int completed, String uuid, String podId) {
        return opsTaskMapper.updateOpsTaskContents(storageContents, completed, uuid, podId);
    }
}
