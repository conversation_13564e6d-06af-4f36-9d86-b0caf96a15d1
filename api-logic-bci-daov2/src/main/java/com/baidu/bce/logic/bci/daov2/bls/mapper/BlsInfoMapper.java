package com.baidu.bce.logic.bci.daov2.bls.mapper;

import com.baidu.bce.logic.bci.daov2.bls.model.BlsInfo;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.dao.DataAccessException;

public interface BlsInfoMapper {
    String SELECT_BLS_TASK = "select * from t_bls_task_info";

    @Select(SELECT_BLS_TASK + " where user_id = @{userId} and deleted=0 and " +
        "bls_task_name=@{blsTaskName}")
    BlsInfo getBlsTaskByUserId(@Param("blsTaskName") String blsTaskName, @Param("userId") String userId);

    @Select(SELECT_BLS_TASK + " where user_id = @{userId} and deleted=0 and " +
        "bls_task_id=@{blsTaskId}")
    BlsInfo getBlsInfoByTaskId(@Param("blsTaskId") String blsTaskId, @Param("userId") String userId);

    @Update("update t_bls_task_info set bls_task_id = @{blsMap.blsTaskId}, updated_time = UTC_TIMESTAMP() " +
        " where user_id = @{blsMap.userId} and bls_task_name = @{blsMap.blsTaskName} " +
        " and deleted=0")
    void updateBlsTaskMap(@Param("blsMap") BlsInfo blsMap);

    @Insert("insert into t_bls_task_info set bls_task_name = @{blsMap.blsTaskName}, " +
        "created_time = UTC_TIMESTAMP(), updated_time = UTC_TIMESTAMP(), deleted=0," +
        "bls_task_id = @{blsMap.blsTaskId},bls_task_owner = @{blsMap.blsTaskOwner}," +
        "user_id = @{blsMap.userId}" )
    void insertBlsTaskMap(@Param("blsMap") BlsInfo blsMap) throws DataAccessException;
     
    @Delete("update t_bls_task_info set deleted = 1,deleted_time =UTC_TIMESTAMP() " + 
        "where bls_task_id = @{blsMap.blsTaskId} " +
        " and user_id = @{blsMap.userId} ")
    void deleteBlsTaskMap(@Param("blsMap") BlsInfo blsMap);
    
}
