package com.baidu.bce.logic.bci.daov2.cceuser.mapper;

import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


public interface CceUserMapMapper {
    String INSERT_CCE_User = "insert into t_cce_user_map (user_id, cce_ids, cpt1,"
            + "created_time, updated_time)";

    String QUERY_CCE_User = "SELECT id, user_id, cce_ids, bls_user_token, cpt1,"
            + "created_time, updated_time FROM t_cce_user_map ";


    @Insert(INSERT_CCE_User + " values "
            + "(@{cceUser.userId}, @{cceUser.cceIds}, @{cceUser.cpt1}, "
            + "@{cceUser.createdTime}, @{cceUser.updatedTime})")
    void insert(@Param("cceUser") CceUserMap cceUser);

    @Select(QUERY_CCE_User + " where user_id = @{userId}")
    CceUserMap getCceClusterIdByUserId(@Param("userId") String userId);

    @Select(QUERY_CCE_User)
    List<CceUserMap> listCceUserMaps();

    @Select(QUERY_CCE_User + " where deleted = 0")
    List<CceUserMap> listActiveCceUserMaps();

    @Select(QUERY_CCE_User + " where cpt1 = 1")
    List<CceUserMap> listCpt1CceUserMaps();

    @Delete("delete from t_cce_user_map where user_id = @{userId} ")
    int delete(@Param("userId") String userId);

    @Update("update t_cce_user_map set bls_user_token = @{cceUser.blsUserToken}, " +
            "cpt1 = @{cceUser.cpt1}, " +
            "updated_time = UTC_TIMESTAMP() where user_id = @{cceUser.userId}")
    void updateUserInfo(@Param("cceUser") CceUserMap cceUser);

    @Select("select user_id FROM t_cce_user_map where deleted = 0")
    List<CceUserMap> getActiveUsers();
}