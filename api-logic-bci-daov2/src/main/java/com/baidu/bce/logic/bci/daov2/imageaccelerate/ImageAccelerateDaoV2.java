package com.baidu.bce.logic.bci.daov2.imageaccelerate;

import com.baidu.bce.logic.bci.daov2.imageaccelerate.mapper.ImageAccelerateMapperV2;
import com.baidu.bce.logic.bci.daov2.imageaccelerate.model.ImageAcceleratePO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public class ImageAccelerateDaoV2 {
    @Autowired
    private ImageAccelerateMapperV2 imageAccelerateMapperV2;

    public void insertImageAcc(ImageAcceleratePO imageAcceleratePO) throws DataAccessException {
        imageAccelerateMapperV2.insertImageAcc(imageAcceleratePO);
    }

    public List<ImageAcceleratePO> getImageAccByImageAddrAndStatusNotInFailed(String accountId, String image) {
        return imageAccelerateMapperV2.getImageAccByImageAddrAndStatusNotInFailed(accountId, "%" + image + "%");
    }

    public List<ImageAcceleratePO> listAllCreatingImageAcc() {
        return imageAccelerateMapperV2.listAllCreatingImageAcc();
    }

    public List<ImageAcceleratePO> listAllSuccessImageAccAndNotScanDone() {
        return imageAccelerateMapperV2.listAllSuccessImageAccAndNotScanDone();
    }

    public void updateImagesAccImagesById(Long id, String images) {
        imageAccelerateMapperV2.updateImagesAccImagesById(id, images);
    }

    public void updateUsedNum(Long id, Long usedNum) {
        imageAccelerateMapperV2.updateUsedNum(id, usedNum);
    }

    public void updateSecrets(Long id, String imageSecrets) {
        imageAccelerateMapperV2.updateSecrets(id, imageSecrets);
    }

    public void updateEniEip(
        Long id,
        Integer needEipInt,
        Integer enableEniInt,
        String subnetId,
        String securityGroupShortId,
        String vpcCidr,
        String zoneSubnets) {
        imageAccelerateMapperV2.updateEniEip(
            id, needEipInt, enableEniInt, subnetId, securityGroupShortId, vpcCidr, zoneSubnets);
    }

    public void deleteImageAccById(Long id, String name) {
        imageAccelerateMapperV2.deleteImageAccById(id, name);
    }

    // 获取制作中和制作成功的镜像缓存条目项
    public List<ImageAcceleratePO> listValidImageCachesByAccountId(String accountId, Long limit, Long offset) {
        return imageAccelerateMapperV2.listValidImageCachesByAccountId(accountId, limit, offset);
    }

    public void updateImageAccScanDoneByName(String imageAccName, String cpuTypes) {
        imageAccelerateMapperV2.updateImageAccScanDoneByName(imageAccName, cpuTypes);
    }

    public void updateImageAccStatusByName(String imageAccName, String status, int progress) {
        imageAccelerateMapperV2.updateImageAccStatusByName(imageAccName, status, progress);
    }

    public void updateImageAccStatusById(String imageAccName, String status, int progress, Long imageAccId) {
        imageAccelerateMapperV2.updateImageAccStatusById(imageAccName, status, progress, imageAccId);
    }

    public void updateImageAccDetailAndStatus(
        String images, String imageAccName, String status, int progress, String snapshotId, Long imageAccId) {
        imageAccelerateMapperV2.updateImageAccDetailAndStatus(images, imageAccName, status, progress, 
            snapshotId, imageAccId);
    }

    public List<ImageAcceleratePO> listValidImageCaches() {
        return imageAccelerateMapperV2.listValidImageCaches();
    }

    public List<ImageAcceleratePO> listAllValidImageCachesByAccountId(String accountId) {
        return imageAccelerateMapperV2.listAllValidImageCachesByAccountId(accountId);
    }

    public List<ImageAcceleratePO> findBestMatchedCdsSnapshotId(String originImageAddress, String accountId) {
        originImageAddress = "%" + originImageAddress + "%";
        return imageAccelerateMapperV2.findBestMatchedCdsSnapshotId(originImageAddress, accountId);
    }

}
