package com.baidu.bce.logic.bci.daov2.imageaccelerate.mapper;

import com.baidu.bce.logic.bci.daov2.imageaccelerate.model.ImageAcceleratePO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.dao.DataAccessException;

import java.util.List;

public interface ImageAccelerateMapperV2 {

    String INSERT_IMAGE_ACC =
        "INSERT INTO image_accelerate (name, images, order_id, description, account_id, "
            + "zone_id, physical_zone, logical_zone, enable_eni, need_eip, user_id, subnet_id, security_group_ids, "
            + "vpc_cidr, zone_subnets, status, progress, image_secrets, used_num) ";
    String QUERY_IMAGE_ACC =
        "select id, name, images, order_id, description, account_id, zone_id, physical_zone, "
            + "logical_zone, enable_eni, need_eip, user_id, subnet_id, security_group_ids, vpc_cidr, zone_subnets, "
            + "created_time, updated_time, deleted_time, deleted, image_secrets, used_num, "
            + "status, progress, cds_snapshot_id, cpu_types, scan_done from image_accelerate ";

    @Insert(
        INSERT_IMAGE_ACC
            + " values "
            + "(@{imageAcceleratePO.name}, @{imageAcceleratePO.images}, @{imageAcceleratePO.orderId}, "
            + "@{imageAcceleratePO.description}, @{imageAcceleratePO.accountId}, @{imageAcceleratePO.zoneId}, "
            + "@{imageAcceleratePO.physicalZone}, @{imageAcceleratePO.logicalZone}, @{imageAcceleratePO.enableEni}, "
            + "@{imageAcceleratePO.needEip}, @{imageAcceleratePO.userId}, @{imageAcceleratePO.subnetId}, "
            + "@{imageAcceleratePO.securityGroupIds}, @{imageAcceleratePO.vpcCidr}, "
            + "@{imageAcceleratePO.zoneSubnets}, @{imageAcceleratePO.status}, @{imageAcceleratePO.progress}, "
            + "@{imageAcceleratePO.imageSecrets}, @{imageAcceleratePO.usedNum} )")
    void insertImageAcc(@Param("imageAcceleratePO") ImageAcceleratePO imageAcceleratePO) throws DataAccessException;

    @Select(
        QUERY_IMAGE_ACC
            + " where account_id = @{accountId} AND deleted = 0 AND images LIKE @{image} and status !=\"failed\"")
    List<ImageAcceleratePO> getImageAccByImageAddrAndStatusNotInFailed(
        @Param("accountId") String accountId, @Param("image") String image);

    @Select(QUERY_IMAGE_ACC + " where deleted = 0 and status !=\"success\" and status != \"failed\"")
    List<ImageAcceleratePO> listAllCreatingImageAcc();

    @Select(QUERY_IMAGE_ACC + " where deleted = 0 and status =\"success\" and scan_done = 0")
    List<ImageAcceleratePO> listAllSuccessImageAccAndNotScanDone();

    @Update("update image_accelerate set cpu_types = @{cpuTypes}, scan_done = 1 where deleted = 0 AND name = @{name}")
    void updateImageAccScanDoneByName(@Param("name") String name, @Param("cpuTypes") String cpuTypes);

    @Update("update image_accelerate set images = @{images} where deleted = 0 AND id = @{id}")
    void updateImagesAccImagesById(@Param("id") Long id, @Param("images") String images);

    @Update("update image_accelerate set used_num = @{usedNum} where deleted = 0 AND id = @{id}")
    void updateUsedNum(@Param("id") Long id, @Param("usedNum") Long usedNum);

    @Update("update image_accelerate set image_secrets = @{imageSecrets} where deleted = 0 AND id = @{id}")
    void updateSecrets(@Param("id") Long id, @Param("imageSecrets") String imageSecrets);

    @Update(
        "update image_accelerate set need_eip = @{needEipInt}, enable_eni = @{enableEniInt}, "
            + "subnet_id = @{subnetId},  security_group_ids = @{securityGroupShortId}, vpc_cidr = @{vpcCidr}, "
            + " zone_subnets = @{zoneSubnets} where deleted = 0 AND id = @{id}")
    void updateEniEip(
        @Param("id") Long id,
        @Param("needEipInt") Integer needEipInt,
        @Param("enableEniInt") Integer enableEniInt,
        @Param("subnetId") String subnetId,
        @Param("securityGroupShortId") String securityGroupShortId,
        @Param("vpcCidr") String vpcCidr,
        @Param("zoneSubnets") String zoneSubnets);

    @Update(
        "update image_accelerate set name = @{name}, deleted = 1, deleted_time = UTC_TIMESTAMP() "
            + "where deleted = 0 AND id = @{id}")
    void deleteImageAccById(@Param("id") Long id, @Param("name") String name);

    @Select(QUERY_IMAGE_ACC + 
        "where deleted = 0 and account_id=@{accountId} order by created_time desc " 
        + " limit @{limit} offset @{offset}")
    List<ImageAcceleratePO> listValidImageCachesByAccountId(
        @Param("accountId") String accountId, @Param("limit") Long limit, @Param("offset") Long offset);

    @Update("update image_accelerate set status=@{status}, progress=@{progress} where name=@{imageAccName}")
    void updateImageAccStatusByName(
        @Param("imageAccName") String imageAccName, @Param("status") String status, @Param("progress") int progress);

    @Update(
        "update image_accelerate set status=@{status}, progress=@{progress}, name=@{imageAccName} "
        + " where id=@{imageAccId}")
    void updateImageAccStatusById(
        @Param("imageAccName") String imageAccName,
        @Param("status") String status,
        @Param("progress") int progress,
        @Param("imageAccId") Long imageAccId);

    @Update(
        "update image_accelerate set status=@{status}, progress=@{progress}, name=@{imageAccName}, "
        + "images=@{images}, cds_snapshot_id=@{snapshotId} where id=@{imageAccId}")
    void updateImageAccDetailAndStatus(
        @Param("images") String images,
        @Param("imageAccName") String imageAccName,
        @Param("status") String status,
        @Param("progress") int progress,
        @Param("snapshotId") String snapshotId,
        @Param("imageAccId") Long imageAccId);

    @Select(QUERY_IMAGE_ACC + "where deleted = 0 and status != \"failed\"")
    List<ImageAcceleratePO> listValidImageCaches();

    @Select(QUERY_IMAGE_ACC + "where deleted = 0 and account_id=@{accountId} ")
    List<ImageAcceleratePO> listAllValidImageCachesByAccountId(
        @Param("accountId") String accountId);

    @Select(QUERY_IMAGE_ACC + "where deleted = 0 and account_id=@{accountId} and images like @{originImageAddress} " +
        " and status = 'success' order by created_time desc")
    List<ImageAcceleratePO> findBestMatchedCdsSnapshotId(
        @Param ("originImageAddress") String originImageAddress, @Param("accountId") String accountId);
}
