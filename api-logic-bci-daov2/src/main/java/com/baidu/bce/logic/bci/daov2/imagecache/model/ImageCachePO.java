package com.baidu.bce.logic.bci.daov2.imagecache.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.bci.daov2.common.model.ImageCacheStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Data
public class ImageCachePO {
    private long id;
    private String taskId;
    private String userId;
    private ImageCacheStatus status;
    private String address;
    private String az;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;

    public ImageCachePO() {
    }

    public ImageCachePO(String taskId, String userId, ImageCacheStatus status, String address,
                        String az) {
        Timestamp currentTime = new Timestamp(new Date().getTime());

        this.taskId = taskId;
        this.userId = userId;
        this.status = status;
        this.address = address;
        this.az = az;
        this.createdTime = currentTime;
        this.updatedTime = currentTime;
    }
}
