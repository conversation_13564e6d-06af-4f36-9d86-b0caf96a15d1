package com.baidu.bce.logic.bci.daov2.imagecachev2.mapper;

import com.baidu.bce.logic.bci.daov2.imagecachev2.model.ImageCachePO;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.dao.DataAccessException;

import java.util.List;

public interface ImageCacheV2Mapper {
    @Select("select * from t_image_cache where image_cache_id=@{imageCacheId} and account_id=@{accountId} "
        + " and deleted=false and status !=\"failed\"")
    ImageCachePO getImageCacheById(@Param("imageCacheId") String imageCacheId, @Param("accountId") String accountId);

    @Insert("insert into t_image_cache (account_id,image_cache_name,image_cache_id,temporary_storage_size,"
        + "retention_day,image_secrets,status,progress,image_cache_owner,image_cache_type,subnet_id,"
        + "security_group_id,need_eip,enable_eni,tc_pod_name,cds_snap_shot_id,elimination_strategy) values (@{imageCachePO.accountId},"
        + "@{imageCachePO.imageCacheName},@{imageCachePO.imageCacheId}"
        +",@{imageCachePO.temporaryStorageSize},@{imageCachePO.retentionDay},@{imageCachePO.imageSecrets}"
        +",@{imageCachePO.status},@{imageCachePO.progress},@{imageCachePO.imageCacheOwner},"
        + "@{imageCachePO.imageCacheType},@{imageCachePO.subnetId},@{imageCachePO.securityGroupId},"
        + "@{imageCachePO.needEip},@{imageCachePO.enableEni},@{imageCachePO.tcPodName},"
        + "@{imageCachePO.cdsSnapShotId},@{imageCachePO.eliminationStrategy})")
    void insertImageCachePO(@Param("imageCachePO") ImageCachePO imageCachePO) throws DataAccessException;

    @Select("select * from t_image_cache where deleted=false and status=@{imageCachePO.status}")
    List<ImageCachePO> getImageCacheByStatus(@Param("imageCachePO") ImageCachePO imageCachePO);

    @Select("select * from t_image_cache where image_cache_name=@{imageCacheName} "
        + " and account_id=@{accountId} and deleted=false "
        + " and status != \"failed\"")
    ImageCachePO getImageCacheByName(@Param("imageCacheName") String imageCacheName, 
        @Param("accountId") String accountId);
    @Update("update t_image_cache set progress=@{imageCachePO.progress},status=@{imageCachePO.status},"
        + "cds_snap_shot_id=@{imageCachePO.cdsSnapShotId},image_cache_name=@{imageCachePO.imageCacheName},"
        + "created_at=@{imageCachePO.createdAt},deleted_at=@{imageCachePO.deletedAt},"
        + "deleted=@{imageCachePO.deleted},updated_at=@{imageCachePO.updatedAt},"
        + "temporary_storage_size=@{imageCachePO.temporaryStorageSize},"
        + "image_cache_id=@{imageCachePO.imageCacheId} where id=@{imageCachePO.id}")
    void updateImageCache(@Param("imageCachePO") ImageCachePO imageCachePO);


    @Select("select * from t_image_cache where deleted=false and account_id=@{accountId} " 
        + "limit @{limit} offset @{offset}")
    List<ImageCachePO> listImageCachePOs(@Param("accountId") String accountId, 
        @Param("limit") Long limit, @Param("offset") Long offset);

    @Select("select * from t_image_cache where deleted=false and account_id=@{accountId}")
    List<ImageCachePO> listAllValidImageCachesByAccountId(@Param("accountId") String accountId);

    @Select("select * from t_image_cache where deleted=false and account_id=@{accountId} "
        + " and elimination_strategy = \"LRU\" order by updated_at asc limit 1 ")
    ImageCachePO  getLeastRecentlyUsedImageCache(@Param("accountId") String accountId);

    @Select("select * from t_image_cache where image_cache_id=@{imageCacheId} and account_id=@{accountId} "
        + " and deleted=false")
    ImageCachePO getAllImageCacheById(@Param("imageCacheId") String imageCacheId, @Param("accountId") String accountId);

    @Update("update t_image_cache set cpu_types = @{cpuTypes}, scan_done = 1 where deleted = false " 
        + " and image_cache_name = @{name}")
    void updateImageAccScanDoneByName(@Param("name") String name, @Param("cpuTypes") String cpuTypes);

    @Select("select * from t_image_cache where deleted=false and status=\"success\" and scan_done=0")
    List<ImageCachePO> listAllSuccessImageAccAndNotScanDone();

    @Select("select * from t_image_cache "
            + "#where() "
            + "  deleted=0 and status=\"success\" and image_cache_predeploy=0"
            + "  #if($_parameter.userIds && $_parameter.userIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.userIds $userId \" account_id \" ) "
            + "         @{userId}"
            + "     #end "
            + "  #end "
            + "#end ")
    List<ImageCachePO> listAllSuccessImageCacheAndNotPredeploy(@Param("userIds") List<String> userIds);

    @Update("update t_image_cache set "
            + " image_cache_predeploy=1,"
            + " image_cache_predeploy_daemonset_name=@{imageCachePO.imageCachePredeployDaemonsetName},"
            + " image_cache_predeploy_time=@{imageCachePO.imageCachePredeployTime} "
            + " where account_id=@{imageCachePO.accountId} "
            + " and image_cache_id=@{imageCachePO.imageCacheId}"
            + " and image_cache_predeploy=0")
    int updateImageCachePredeployCompleted(@Param("imageCachePO") ImageCachePO imageCachePO);
}