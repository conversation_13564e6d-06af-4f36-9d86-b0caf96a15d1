package com.baidu.bce.logic.bci.daov2.reservedinstance;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.baidu.bce.logic.bci.daov2.reservedinstance.mapper.ReservedInstanceMapper;
import com.baidu.bce.logic.bci.daov2.reservedinstance.mapper.ReservedInstanceSpecMapper;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstancePO;
import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstanceSpec;
import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.plat.webframework.database.SqlEscape;

import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository("ReservedInstanceDao")
public class ReservedInstanceDao {
    @Data
    public class ReservedInstanceQueryFilter {
        private String name;
        private String value;

        public ReservedInstanceQueryFilter(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }
    @Autowired
    private ReservedInstanceMapper reservedInstanceMapper;

    @Autowired
    private ReservedInstanceSpecMapper reservedInstanceSpecMapper;

    // 获取所有预留实例券商品
    public List<ReservedInstanceSpec> listReservedInstanceSpecs() {
        return reservedInstanceSpecMapper.listReservedInstanceSpecs();
    }

    // 获取满足查询条件的预留实例券条目
    public List<ReservedInstancePO> listReservedInstancesByMultiKey(String accountId, ListRequest listRequest) {
        List<OrderModel> orderModels = listRequest.getOrders();
        List<ReservedInstanceQueryFilter> queryFilterList = 
                convertFilterMapToQueryFilterList(listRequest.getFilterMap());
        int queryOffset = (listRequest.getPageNo() - 1) * listRequest.getPageSize();
        return reservedInstanceMapper.listReservedInstancesByMultiKey(accountId, queryOffset,
                listRequest.getPageSize(), orderModels, queryFilterList);
    }
    
    // 获取所有满足查询条件的预留实例券数量，忽略pageSize限制，用于给前端返回总条目数
    public int getReservedInstanceNumByMultiKey(String accountId, ListRequest listRequest) {
        List<OrderModel> orderModels = listRequest.getOrders();
        List<ReservedInstanceQueryFilter> queryFilterList = 
                convertFilterMapToQueryFilterList(listRequest.getFilterMap());
        int queryOffset = (listRequest.getPageNo() - 1) * listRequest.getPageSize();
        return reservedInstanceMapper.getReservedInstanceNumByMultiKey(accountId, queryOffset,
                listRequest.getPageSize(), orderModels, queryFilterList);
    }

    // 包含 'NEED_PURCHASE', 'READY_FOR_CREATE', 'CREATING' 三种状态
    public List<ReservedInstancePO> listReservedInstancesInBuild() {
        return reservedInstanceMapper.listReservedInstancesInBuild();
    }

    // 包含 'INACTIVE', 'ACTIVE' 两种状态
    public List<ReservedInstancePO> listCreatedReservedInstances() {
        return reservedInstanceMapper.listCreatedReservedInstances();
    }

    public List<ReservedInstancePO> listReservedInstancesByOrderId(String orderId) {
        return reservedInstanceMapper.listReservedInstancesByOrderId(orderId);
    }

    // resource uuid 来源于 billing 订单绑定的资源ID
    public ReservedInstancePO getReservedInstanceByReservedInstanceUuid(String uuid) {
        return reservedInstanceMapper.getReservedInstanceByReservedInstanceUuid(uuid);
    }

    public ReservedInstancePO getReservedInstanceByResourceUuid(String uuid) {
        return reservedInstanceMapper.getReservedInstanceByResourceUuid(uuid);
    }
    
    public int insertReservedInstance(ReservedInstancePO reservedInstancePO) {
        return reservedInstanceMapper.insertReservedInstance(reservedInstancePO);
    }

    @Transactional
    public int batchInsertReservedInstance(List<ReservedInstancePO> reservedInstancePOs) {
        return reservedInstanceMapper.batchInsertReservedInstance(reservedInstancePOs);
    }
    
    @Transactional
    public void updateOrderId(List<String> reservedInstanceIds, String accountId, String orderId) {
        reservedInstanceMapper.updateOrderId(reservedInstanceIds, accountId, orderId);
    }
    
    @Transactional
    public void updateOrderIdByUuid(List<String> reservedInstanceUuids, String orderId) {
        reservedInstanceMapper.updateOrderIdByUuid(reservedInstanceUuids, orderId);
    }

    @Transactional
    public void updateOrderIdByResourceUuid(List<String> resourceUuids, String orderId) {
        reservedInstanceMapper.updateOrderIdByResourceUuid(resourceUuids, orderId);
    }

    public void updateStatus(String accountId, String reservedInstanceId, String status) {
        reservedInstanceMapper.updateStatus(accountId, reservedInstanceId, status);
    }

    @Transactional
    public void batchUpdateStatus(List<ReservedInstancePO> reservedInstances) {
        for (ReservedInstancePO reservedInstance : reservedInstances) {
            reservedInstanceMapper.updateStatus(reservedInstance.getAccountId(),
                    reservedInstance.getReservedInstanceId(), reservedInstance.getStatus());
        }
    }

    @Transactional
    public void batchUpdateUuid(List<ReservedInstancePO> reservedInstances) {
        for (ReservedInstancePO reservedInstance : reservedInstances) {
            reservedInstanceMapper.updateUuid(reservedInstance.getAccountId(),
                    reservedInstance.getReservedInstanceId(), reservedInstance.getReservedInstanceUuid());
        }
    }

    @Transactional
    public void batchUpdateResourceUuid(List<ReservedInstancePO> reservedInstances) {
        for (ReservedInstancePO reservedInstance : reservedInstances) {
            reservedInstanceMapper.updateResourceUuid(reservedInstance.getAccountId(),
                    reservedInstance.getReservedInstanceId(), reservedInstance.getResourceUuid());
        }
    }

    @Transactional
    public void updateExpireTimeAndEffectiveTimeAndStatusByResourceUuid(List<ReservedInstancePO> reservedInstancePOs) {
        for (ReservedInstancePO ri : reservedInstancePOs) {
            reservedInstanceMapper.updateExpireTimeAndEffectiveTimeAndStatusByResourceUuid(ri);
        }
    }

    public void delete(long id) {
        reservedInstanceMapper.deleteById(id);
    }

    public void deleteByReservedInstanceUuid(String uuid) {
        reservedInstanceMapper.deleteByReservedInstanceUuid(uuid);
    }

    public List<ReservedInstanceQueryFilter> convertFilterMapToQueryFilterList(Map<String, String> filterMap) {
        List<ReservedInstanceQueryFilter> queryFilterList = new ArrayList<>();
        if (filterMap != null && filterMap.size() > 0) {
            Set<Map.Entry<String, String>> keywordTypeSet = filterMap.entrySet();
            for (Map.Entry<String, String> keywordTypeEntry : keywordTypeSet) {
                String keyword = keywordTypeEntry.getValue();
                String keywordType = keywordTypeEntry.getKey();
                if (StringUtils.isNotEmpty(keyword)) {
                    queryFilterList.add(new ReservedInstanceQueryFilter(keywordType, "%" + 
                            SqlEscape.escapeLikePattern(keyword) + "%"));
                }
            }
        }    
        return queryFilterList;
    }

    public Integer getBciStockDemand(String spec, String az, String userId, String startTime, String endTime) {
        // 按天匹配，忽略具体时间
        DateTimeFormatter parseFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter leftValueFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00");
        DateTimeFormatter rightValueFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd 23:59:59");
        LocalDateTime startTimeInLocal = LocalDateTime.parse(startTime.trim(), parseFormatter);
        LocalDateTime endTimeInLocal = LocalDateTime.parse(endTime.trim(), parseFormatter);
        return reservedInstanceMapper.getBciStockDemand(spec, az, userId, startTimeInLocal.format(leftValueFormatter), 
                startTimeInLocal.format(rightValueFormatter), endTimeInLocal.format(leftValueFormatter),
                endTimeInLocal.format(rightValueFormatter));
    }

    public ReservedInstancePO getReservedInstanceByReservedInstanceId(String id) {
        return reservedInstanceMapper.getReservedInstanceByReservedInstanceId(id);
    }

    public ReservedInstanceSpec getSpecBySpecName(String spec) {
        return reservedInstanceSpecMapper.getSpecBySpecName(spec);
    }
}
