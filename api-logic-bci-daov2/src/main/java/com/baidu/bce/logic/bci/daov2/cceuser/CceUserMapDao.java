package com.baidu.bce.logic.bci.daov2.cceuser;

import com.baidu.bce.logic.bci.daov2.cceuser.mapper.CceUserMapMapper;
import com.baidu.bce.logic.bci.daov2.cceuser.model.CceUserMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository("cceUserMapDao")
public class CceUserMapDao {

    @Autowired
    private CceUserMapMapper cceUserMapMapper;

    public void insert(CceUserMap cceUserMap) {
        cceUserMapMapper.insert(cceUserMap);
    }

    public void delete(String userId) {
        cceUserMapMapper.delete(userId);
    }

    public CceUserMap getCceUserMapByUserId(String userId) {
        return cceUserMapMapper.getCceClusterIdByUserId(userId);
    }

    public List<CceUserMap> listCceUserMaps() {
        return cceUserMapMapper.listCceUserMaps();
    }

    public List<CceUserMap> listActiveCceUserMaps() {
        return cceUserMapMapper.listActiveCceUserMaps();
    }

    public List<CceUserMap> listCpt1CceUserMaps() {
        return cceUserMapMapper.listCpt1CceUserMaps();
    }

    public void updateUserInfo(CceUserMap cceUserMap) {
        cceUserMapMapper.updateUserInfo(cceUserMap);
    }

    public List<CceUserMap> getActiveUsers() {
        return cceUserMapMapper.getActiveUsers();
    }
}
