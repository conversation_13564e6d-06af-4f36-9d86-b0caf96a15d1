package com.baidu.bce.logic.bci.daov2.statemachine.context;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class StateMachinePodDaoContext {
    private String podId;
    private String userId;
    private long resourceVersion;
    private long bciResourceVersion;

    public StateMachinePodDaoContext(String podId, String userId) {
        this.podId = podId;
        this.userId = userId;
    }

    public StateMachinePodDaoContext(String podId, String userId,
                                     long resourceVersion, long bciResourceVersion) {
        this(podId, userId);
        this.resourceVersion = resourceVersion;
        this.bciResourceVersion = bciResourceVersion;
    }
}
