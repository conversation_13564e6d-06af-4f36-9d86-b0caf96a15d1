package com.baidu.bce.logic.bci.daov2.imagedetailv2.model;


import com.baidu.bce.internalsdk.core.BceConstant;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import java.sql.Timestamp;

@Data
@Accessors(chain = true)
public class ImageDetailPO {
    private long id;
    private String accountId = "";
    private String imageCacheId = "";
    private String imageCacheType = "";
    private String originImageAddress = "";
    private String originImageVersion = "";
    private String acceImageAddress = "";
    private String acceImageVersion = "";
    private boolean deleted;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedAt;
}