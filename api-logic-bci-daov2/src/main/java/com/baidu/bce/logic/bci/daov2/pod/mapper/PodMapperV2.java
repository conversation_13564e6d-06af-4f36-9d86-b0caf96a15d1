package com.baidu.bce.logic.bci.daov2.pod.mapper;

import com.baidu.bce.logic.bci.daov2.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.daov2.common.model.PodListModel;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logic.bci.daov2.pod.model.PodPOWithId;
import com.baidu.bce.logic.bci.daov2.statemachine.context.StateMachinePodDaoContext;
import com.baidu.bce.logic.core.request.OrderModel;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public interface PodMapperV2 {

    String QUERY_POD = "select p.pod_id, p.pod_uuid, p.name, p.status, p.sub_status, p.internal_status, p.v_cpu, p.memory, "
            + " p.eip_uuid, p.public_ip, p.product_type, p.gpu_type, p.gpu_count, p.eip_status, "
            + " p.cce_uuid, p.internal_ip, p.internal_ipv6, p.subnet_uuid, p.security_group_uuid, p.restart_policy,"
            + " p.order_id, p.tags, p.description, p.user_id, p.zone_id, p.cpt1,"
            + " p.resource_uuid, p.task_status, p.nfs, p.empty_dir, p.config_file, p.flex_volume, p.pfs, p.bos,"
            + " p.host_path, p.pod_volumes,"
            + " p.created_time, p.updated_time, p.application, p.enable_log, p.charge_source, charge_account_id,"
            + " p.conditions, p.created_bls_tasks_id, p.bcc_instance_id, p.node_name, p.preempt_status, "
            + " p.preempt_status_update_time, p.resource_recycle_timestamp, p.resource_recycle_reason, "
            + " p.resource_recycle_complete, p.cpu_type, p.delay_release_duration_minute, p.delay_release_succeeded, "
            + " p.commit_deleted_time, p.resource_version, p.bci_resource_version, p.is_tidal, p.extra, "
            + " p.ds_containers_version, p.ds_containers_count, p.ds_containers_synced_to_k8s "
            + " FROM t_pod_v2 p ";

    String QUERY_POD_WITH_ID = "select p.id, p.pod_id, p.name, p.status, p.sub_status, p.order_id, p.user_id FROM t_pod_v2 p ";

    String QUERY_POD_DELETE = "select p.pod_id, p.pod_uuid, p.name, p.status, p.sub_status, p.internal_status, p.v_cpu, p.memory, "
            + " p.eip_uuid, p.public_ip, p.product_type, p.gpu_type, p.gpu_count, p.eip_status, "
            + " p.cce_uuid, p.internal_ip, p.internal_ipv6, p.subnet_uuid, p.security_group_uuid, p.restart_policy,"
            + " p.order_id, p.tags, p.description, p.user_id, p.zone_id, p.cpt1,"
            + " p.resource_uuid, p.task_status, p.nfs, p.empty_dir, p.config_file, p.flex_volume, p.pod_volumes,"
            + " p.created_time, p.updated_time, p.application, p.enable_log, p.charge_source, charge_account_id,"
            + " p.conditions, p.created_bls_tasks_id, p.bcc_instance_id, p.node_name, p.preempt_status, "
            + " p.preempt_status_update_time, p.resource_recycle_timestamp, p.resource_recycle_reason, "
            + " p.resource_recycle_complete, p.cpu_type, p.delay_release_duration_minute, "
            + " p.delay_release_succeeded, p.resource_version, p.bci_resource_version, p.deleted, p.is_tidal, "
            + " p.ds_containers_version, p.ds_containers_count, p.ds_containers_synced_to_k8s "
            + " FROM t_pod_v2 p ";

    String QUERY_POD_DETAIL = "select p.pod_id, p.pod_uuid, p.name,"
            + " p.status, p.sub_status, p.v_cpu, p.memory, p.eip_id, p.eip_uuid,"
            + " p.product_type, p.gpu_type, p.gpu_count, p.eip_status, p.eip_actual_status, p.eip_route_type, "
            + " p.public_ip, p.cce_uuid, p.internal_ip, p.internal_ipv6, p.subnet_uuid, p.security_group_uuid, p.restart_policy, "
            + " p.order_id, p.tags, p.description, p.user_id, p.zone_id, p.cpt1, p.eip_pay_method, p.bandwidth_in_mbps,"
            + " p.resource_uuid, p.task_status, p.nfs, p.empty_dir, p.config_file, p.flex_volume, p.pfs, p.bos,"
            + " p.host_path, p.pod_volumes,"
            + " p.created_time, p.updated_time, p.application, p.enable_log, p.charge_source, charge_account_id,"
            + " p.conditions, p.created_bls_tasks_id, p.bcc_instance_id, p.node_name, p.preempt_status, "
            + " p.preempt_status_update_time, p.cpu_type, p.delay_release_duration_minute, "
            + " p.delay_release_succeeded, p.resource_version, p.bci_resource_version, p.is_tidal, "
            + " p.commit_deleted_time, p.resource_recycle_timestamp, p.resource_recycle_reason, p.extra, "
            + " p.ds_containers_version, p.ds_containers_count, p.ds_containers_synced_to_k8s "
            + " FROM t_pod_v2 p ";

    String QUERY_POD_DETAIL_LIGHT = "select p.pod_id, p.pod_uuid, p.name,"
            + " p.status, p.v_cpu, p.memory,"
            + " p.product_type, p.gpu_type, p.gpu_count,"
            + " p.public_ip, p.cce_uuid, p.internal_ip, p.internal_ipv6, p.restart_policy,"
            + " p.order_id, p.tags, p.description, p.user_id, p.zone_id, p.logical_zone, p.cpt1,"
            + " p.resource_uuid, p.task_status,"
            + " p.created_time, p.updated_time, p.application, p.enable_log, p.charge_source, charge_account_id,"
            + " p.conditions, p.created_bls_tasks_id, p.node_name, p.preempt_status, "
            + " p.preempt_status_update_time, p.cpu_type, p.delay_release_duration_minute, "
            + " p.delay_release_succeeded, p.resource_version, p.bci_resource_version, p.is_tidal, "
            + " p.commit_deleted_time, p.resource_recycle_timestamp, p.resource_recycle_reason, p.extra, "
            + " p.ds_containers_version, p.ds_containers_count, p.ds_containers_synced_to_k8s "
            + " FROM t_pod_v2 p ";

    String QUERY_EIP = "select p.id, p.pod_id, p.internal_ip, p.internal_ipv6, p.user_id, p.eip_id, p.public_ip, p.bandwidth_in_mbps, "
            + " p.eip_actual_status FROM t_pod_v2 p ";

    String QUERY_POD_BRIEF = "select p.pod_id, p.user_id, p.status, p.sub_status, p.deleted from t_pod_v2 p ";

    @Select("select pod_id, pod_uuid, name, status, sub_status, v_cpu, memory, eip_uuid, public_ip,"
            + " bandwidth_in_mbps, eip_status,"
            + " cce_uuid, internal_ip, internal_ipv6, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, cpt1, product_type, gpu_type, gpu_count,"
            + " resource_uuid, task_status, nfs, empty_dir, config_file, flex_volume, pfs, bos, host_path, created_time, updated_time, "
            + " application, enable_log, charge_source, charge_account_id, conditions, preempt_status, cpu_type, "
            + " delay_release_duration_minute, delay_release_succeeded, resource_version, bci_resource_version, "
            + " ds_containers_version, ds_containers_count, ds_containers_synced_to_k8s FROM t_pod_v2 "
            + "#where() "
            + "  user_id = @{accountId} and order_id != '' and status != \'unknown\'"
            + "  and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.queryList && $_parameter.queryList.size() > 0) "
            + "    #repeat($_parameter.queryList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) like @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.model.includedUuids && $_parameter.model.includedUuids.size() > 0)"
            + "   and pod_uuid != '' and  "
            + "     #in($_parameter.model.includedUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end "
            + "#if($_parameter.orders && $_parameter.orders.size()>0) "
            + "  #repeat($_parameter.orders $orderModel \" , \" \" order by  \" ) "
            + "    $check.column(${orderModel.orderBy}) $check.order(${orderModel.order}) "
            + "  #end "
            + "#end ")
    List<PodPO> listPodsByMultiKey(@Param("accountId") String accountId,
                                   @Param("model") PodListModel podListModel,
                                   @Param("orders") List<OrderModel> orders,
                                   @Param("queryList") List<PodFilterQueryModel> queryList);

    @Select("select pod_id, pod_uuid, name, status, sub_status, v_cpu, memory, eip_uuid, public_ip,"
            + " bandwidth_in_mbps, eip_status,"
            + " cce_uuid, internal_ip, internal_ipv6, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, cpt1, product_type, gpu_type, gpu_count,"
            + " resource_uuid, task_status, nfs, empty_dir, config_file, flex_volume, created_time, updated_time, "
            + " application, enable_log, charge_source, charge_account_id, conditions, preempt_status, cpu_type, "
            + " delay_release_duration_minute, delay_release_succeeded, resource_version, bci_resource_version, "
            + " ds_containers_version, ds_containers_count, ds_containers_synced_to_k8s FROM t_pod_v2 "
            + "#where() "
            + "  user_id = @{accountId} and order_id != '' and status != \'unknown\'"
            + "  and deleted = 0 and status != \'deleted\' and id >= @{id}"
            + "  #if($_parameter.queryList && $_parameter.queryList.size() > 0) "
            + "    #repeat($_parameter.queryList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) like @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.model.includedUuids && $_parameter.model.includedUuids.size() > 0)"
            + "   and pod_uuid != '' and  "
            + "     #in($_parameter.model.includedUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end "
            + "#if($_parameter.orders && $_parameter.orders.size()>0) "
            + "  #repeat($_parameter.orders $orderModel \" , \" \" order by  \" ) "
            + "    $check.column(${orderModel.orderBy}) $check.order(${orderModel.order}) "
            + "  #end "
            + "#end ")
    List<PodPO> listPodsByMultiKeyAndId(@Param("accountId") String accountId,
                                        @Param("model") PodListModel podListModel,
                                        @Param("orders") List<OrderModel> orders,
                                        @Param("queryList") List<PodFilterQueryModel> queryList,
                                        @Param("id") BigInteger id);

    @Select("select pod_id, pod_uuid, name, status, sub_status, v_cpu, memory, eip_uuid, public_ip,"
            + " bandwidth_in_mbps, eip_status,"
            + " cce_uuid, internal_ip, internal_ipv6, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, cpt1, product_type, gpu_type, gpu_count,"
            + " resource_uuid, task_status, nfs, empty_dir, config_file, flex_volume, created_time, updated_time, "
            + " application, enable_log, charge_source, charge_account_id, conditions, preempt_status, cpu_type, "
            + " delay_release_duration_minute, delay_release_succeeded, resource_version, bci_resource_version, "
            + " ds_containers_version, ds_containers_count, ds_containers_synced_to_k8s FROM t_pod_v2 "
            + "#where() "
            + "  user_id = @{accountId} and order_id != '' and status != \'unknown\'"
            + "  and deleted = 0 and status != \'deleted\' and id >= @{id}"
            + "  #if($_parameter.queryExactMatchList && $_parameter.queryExactMatchList.size() > 0) "
            + "    #repeat($_parameter.queryExactMatchList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) = @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.queryFuzzyMatchList && $_parameter.queryFuzzyMatchList.size() > 0) "
            + "    #repeat($_parameter.queryFuzzyMatchList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) like @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.model.includedUuids && $_parameter.model.includedUuids.size() > 0)"
            + "   and pod_uuid != '' and  "
            + "     #in($_parameter.model.includedUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end "
            + "#if($_parameter.orders && $_parameter.orders.size()>0) "
            + "  #repeat($_parameter.orders $orderModel \" , \" \" order by  \" ) "
            + "    $check.column(${orderModel.orderBy}) $check.order(${orderModel.order}) "
            + "  #end "
            + "#end ")
    List<PodPO> listPodsByMultiKeyAndIdWithMatchList(@Param("accountId") String accountId,
                                        @Param("model") PodListModel podListModel,
                                        @Param("orders") List<OrderModel> orders,
                                        @Param("queryExactMatchList") List<PodFilterQueryModel> queryExactMatchList,
                                        @Param("queryFuzzyMatchList") List<PodFilterQueryModel> queryFuzzyMatchList,
                                        @Param("id") BigInteger id);

    @Select("select pod_id, pod_uuid, name, status, sub_status, v_cpu, memory, public_ip,"
            + " cce_uuid, internal_ip, internal_ipv6, restart_policy,"
            + " order_id, tags, user_id, zone_id, cpt1, product_type, gpu_type, gpu_count,"
            + " resource_uuid, task_status, created_time, updated_time, "
            + " application, enable_log, charge_source, charge_account_id, conditions, preempt_status, cpu_type, "
            + " delay_release_duration_minute, delay_release_succeeded, resource_version, bci_resource_version, "
            + " ds_containers_version, ds_containers_count, ds_containers_synced_to_k8s FROM t_pod_v2 "
            + "#where() "
            + "  user_id = @{accountId} and order_id != '' and status != \'unknown\'"
            + "  and deleted = 0 and status != \'deleted\' and id >= @{id}"
            + "  #if($_parameter.queryExactMatchList && $_parameter.queryExactMatchList.size() > 0) "
            + "    #repeat($_parameter.queryExactMatchList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) = @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.queryFuzzyMatchList && $_parameter.queryFuzzyMatchList.size() > 0) "
            + "    #repeat($_parameter.queryFuzzyMatchList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) like @{query.value} "
            + "    #end "
            + "  #end "
            + "#end "
            + " limit @{queryLimit} ")
    List<PodPO> listPodsLightByMultiKeyAndId(@Param("accountId") String accountId,
                                        @Param("model") PodListModel podListModel,
                                        @Param("queryExactMatchList") List<PodFilterQueryModel> queryExactMatchList,
                                        @Param("queryFuzzyMatchList") List<PodFilterQueryModel> queryFuzzyMatchList,
                                        @Param("id") BigInteger id, @Param("queryLimit") int queryLimit);

    @Select(" #set($pattern = '%' + $_parameter.keyword + '%')"
            + " select pod_id, pod_uuid, name, status, sub_status, v_cpu, memory, eip_uuid, public_ip,"
            + " cce_uuid, internal_ip, internal_ipv6, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, cpt1, product_type, gpu_type, gpu_count,"
            + " resource_uuid, task_status, nfs, empty_dir, config_file, flex_volume, pfs, bos, host_path, created_time, updated_time,"
            + " application, enable_log, charge_source, charge_account_id, conditions, preempt_status, cpu_type,"
            + " delay_release_duration_minute, delay_release_succeeded, resource_version, bci_resource_version,"
            + " ds_containers_version, ds_containers_count, ds_containers_synced_to_k8s FROM t_pod_v2 WHERE "
            + " user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\'"
            + " and order_id != '' and updated_time >= @{updatedTime} "
            + " #if($_parameter.keywordType)"
            + "    AND $check.column($_parameter.keywordType) LIKE @{pattern, jdbcType=VARCHAR}"
            + " #end")
    List<PodPO> listPodsByUpdatedTime(@Param("accountId") String accountId,
                                      @Param("updatedTime") Timestamp updatedTime,
                                      @Param("keywordType") String keywordType,
                                      @Param("keyword") String keyword);

    @Select(" #set($pattern = '%' + $_parameter.keyword + '%')"
            + " select pod_id, pod_uuid, name, status, sub_status, v_cpu, memory, eip_uuid, public_ip,"
            + " cce_uuid, internal_ip, internal_ipv6, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, cpt1,"
            + " resource_uuid, task_status, nfs, empty_dir, config_file, flex_volume, pfs, bos, host_path, created_time,"
            + " updated_time,  gpu_type, gpu_count"
            + " application, enable_log, charge_source, charge_account_id, conditions, cpu_type,"
            + " delay_release_duration_minute, delay_release_succeeded, resource_version, bci_resource_version,"
            + " ds_containers_version, ds_containers_count, ds_containers_synced_to_k8s FROM t_pod_v2 WHERE "
            + " user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\'"
            + " and order_id != '' "
            + " #if($_parameter.ids && $_parameter.ids.size() > 0)"
            + "     and  "
            + "     #in($_parameter.ids $shortId \" pod_id \" ) "
            + "         @{shortId}"
            + "     #end"
            + " #end"
            + " #if($_parameter.keywordType)"
            + "    AND $check.column($_parameter.keywordType) LIKE @{pattern, jdbcType=VARCHAR}"
            + " #end")
    List<PodPO> listPods(@Param("accountId") String accountId,
                         @Param("ids") List<String> ids,
                         @Param("keywordType") String keywordType,
                         @Param("keyword") String keyword);

    @Select(QUERY_POD + " where p.ds_containers_version > 0 and p.ds_containers_synced_to_k8s = 0 and"
                      + " status in ('Pending', 'Running') and p.deleted = 0 limit 100")
    List<PodPO> listPodsWhichNotSyncDsContainersToK8S();

    @Select("select count(*) from t_pod_v2 "
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' "
            + "  and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.queryList && $_parameter.queryList.size() > 0) "
            + "    #repeat($_parameter.queryList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) like @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.model.includedUuids && $_parameter.model.includedUuids.size() > 0)"
            + "   and pod_uuid != '' and  "
            + "     #in($_parameter.model.includedUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end ")
    int queryPodCountByMultiKey(@Param("accountId") String accountId,
                                @Param("model") PodListModel cinderListModel,
                                @Param("queryList") List<PodFilterQueryModel> queryList);


    @Select(QUERY_POD_DETAIL + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.status !=\'deleted\'"
            + " and p.pod_id = @{podId}")
    PodPO getPodDetailById(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL + " where p.user_id= @{accountId} and p.status !=\'unknown\' "
            + " and p.pod_id = @{podId}")
    PodPO getPodDetailByIdForAllStatus(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL_LIGHT + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.status !=\'deleted\'"
            + " and p.pod_id = @{podId}")
    PodPO getPodDetailLightById(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL_LIGHT + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.pod_id = @{podId}")
    PodPO getPodDetailWithDeletedById(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL_LIGHT
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodPO> listPodsDetailLightByIds(@Param("accountId") String accountId, @Param("podIds") List<String> podIds);

    @Select(QUERY_POD_DETAIL + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.status !=\'deleted\'"
            + " and p.pod_uuid = @{podId}")
    PodPO getPodDetailByUuid(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL + " where p.user_id= @{accountId} and p.status !=\'unknown\' "
            + " and p.pod_uuid = @{podId}")
    PodPO getPodDetailByUuidForAllStatus(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL_LIGHT + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.status !=\'deleted\'"
            + " and p.pod_uuid = @{podId}")
    PodPO getPodDetailLightByUuid(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL_LIGHT + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.pod_uuid = @{podId}")
    PodPO getPodDetailWithDeletedByUuid(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select("select count(*) from t_pod_v2 where user_id = @{accountId} and deleted = 0 "
            + " and charge_source = 'user' and status = 'Pending'")
    int getPendingPodCountByUserId(@Param("accountId") String accountId);

    @Select("select count(*) from t_pod_v2 where user_id = @{accountId} and deleted = 0 "
            + " and resource_recycle_complete = 0")
    int getResourceRecycleIncompletePodCountByUserId(@Param("accountId") String accountId);

    @Select(QUERY_POD + " where p.status !=\'unknown\' and p.status !=\'deleted\' and p.pod_id = @{podId}")
    PodPO getPodById(@Param("podId") String podId);

    @Select(QUERY_POD + " where p.user_id= @{accountId} and p.status !=\'unknown\' and"
            + " p.status !=\'deleted\' and p.pod_id = @{podId}")
    PodPO getPodByUserIdAndPodId(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD + " where p.status !=\'unknown\' and p.status !=\'deleted\' and p.pod_uuid = @{podUuid}")
    PodPO getPodByUuid(@Param("podUuid") String podUuid);

    @Select(QUERY_POD + " where p.user_id= @{accountId} and p.status !=\'unknown\' and"
            + " p.status !=\'deleted\' and p.pod_uuid = @{podUuid}")
    PodPO getPodByUserIdAndUuid(@Param("accountId") String accountId, @Param("podUuid") String podUuid);

    @Select(QUERY_POD + " where p.user_id= @{accountId} and p.pod_id = @{podId}")
    PodPO getPodByIdIgnoreStatus(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD + " where p.user_id= @{accountId} and p.pod_uuid = @{podUuid}")
    PodPO getPodByUuidIgnoreStatus(@Param("accountId") String accountId, @Param("podUuid") String podUuid);

    @Select("select pod_uuid from t_pod_v2 where pod_id = @{podId}")
    String queryPodUuid(@Param("podId") String podId);

    @Select("select id from t_pod_v2 where pod_id = @{podId}")
    BigInteger queryID(@Param("podId") String podId);

    @Update("update t_pod_v2 set deleted = 1, status = 'deleted',"
            + " deleted_time = utc_timestamp() where deleted = 0 and user_id = @{accountId} and pod_id = @{podId}")
    void deletePodById(@Param("accountId") String accountId, @Param("podId") String podId);

    @Update("update t_pod_v2 set deleted = 1, status = 'deleted',"
            + " deleted_time = utc_timestamp() where deleted = 0 and user_id = @{accountId} and pod_uuid = @{podUuid}")
    void deletePodByUuid(@Param("accountId") String accountId, @Param("podUuid") String podUuid);

    @Update("update t_pod_v2 set updated_time = utc_timestamp(),"
            + " resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp},"
            + " resource_recycle_reason = @{podPO.resourceRecycleReason}"
            + " where deleted = 0 and user_id = @{podPO.userId} and pod_id = @{podPO.podId}"
            + " and resource_recycle_timestamp = 0")
    int markResourceRecyclePod(@Param("podPO") PodPO podPO);

    String INSERT_POD = "INSERT INTO t_pod_v2 ("
            + "pod_id, pod_uuid, name, status, sub_status, internal_status, v_cpu, memory,"
            + " eip_uuid, public_ip, bandwidth_in_mbps, eip_status,"
            + " cce_uuid, internal_ip, internal_ipv6, subnet_uuid, security_group_uuid, restart_policy, order_id, tags, description,"
            + " user_id,  zone_id, logical_zone, resource_uuid, task_status, nfs, empty_dir, config_file, flex_volume,"
            + " pfs, bos, host_path, pod_volumes, application, conditions, enable_log, charge_source, charge_account_id, " +
            "created_time, gpu_type,"
            + " gpu_count, product_type, preempt_status, cpu_type, delay_release_duration_minute,"
            + " delay_release_succeeded, zone_subnets, is_tidal, cpt1, extra, ds_containers_version, ds_containers_count,"
            + " ds_containers_synced_to_k8s, client_token)";

    @Insert(INSERT_POD + " values "
            + "#repeat($_parameter.podPOs $podPO \",\")"
            + "(@{podPO.podId}, @{podPO.podUuid}, @{podPO.name}, "
            + " @{podPO.status},  @{podPO.subStatus}, @{podPO.internalStatus}, "
            + " @{podPO.vCpu}, @{podPO.memory}, @{podPO.eipUuid}, @{podPO.publicIp}, "
            + " @{podPO.bandwidthInMbps}, @{podPO.eipStatus}, @{podPO.cceUuid}, @{podPO.internalIp}, @{podPO.internalIPv6},"
            + " @{podPO.subnetUuid}, @{podPO.securityGroupUuid},"
            + " @{podPO.restartPolicy}, @{podPO.orderId}, @{podPO.tags}, @{podPO.description},"
            + " @{podPO.userId}, @{podPO.zoneId}, @{podPO.logicalZone}, @{podPO.resourceUuid}, @{podPO.taskStatus}, @{podPO.nfs},"
            + " @{podPO.emptyDir}, @{podPO.configFile}, @{podPO.flexVolume}, @{podPO.pfs}, @{podPO.bos},"
            + " @{podPO.hostPath}, @{podPO.podVolumes},"
            + " @{podPO.application}, @{podPO.conditions}, @{podPO.enableLog}, @{podPO.chargeSource}, @{podPO.chargeAccountId},"
            + " utc_timestamp(), @{podPO.gpuType}, @{podPO.gpuCount}, @{podPO.productType}, @{podPO.preemptStatus},"
            + " @{podPO.cpuType}, @{podPO.delayReleaseDurationMinute}, @{podPO.delayReleaseSucceeded},"
            + " @{podPO.zoneSubnets}, @{podPO.isTidal}, @{podPO.cpt1}, @{podPO.extra},"
            + " @{podPO.dsContainersVersion}, @{podPO.dsContainersCount}, @{podPO.dsContainersSyncedToK8S}, @{podPO.clientToken})"
            + "#end")
    void batchInsertPods(@Param("podPOs") List<PodPO> podPOList);

    @Select("select pod_uuid from t_pod_v2 where user_id = @{accountId} and deleted = 0 "
            + " and status in ('Pending', 'Running')")
    List<String> listAllPodUuids(@Param("accountId") String accountId);

    @Select("select count(*) from t_pod_v2 where user_id = @{accountId} and deleted = 0 "
            + " and charge_source = 'user' and status in ('Pending', 'Running')")
    int getCreatedPodInQuota(@Param("accountId") String accountId);

    @Select(QUERY_POD
            + " where p.user_id= @{accountId} and p.status != \'unknown\' and p.deleted = 0 and p.status !=\'deleted\'"
            + " and p.order_id= @{orderId}")
    List<PodPO> listByOrderId(@Param("accountId") String accountId, @Param("orderId") String orderId);

    @Select(QUERY_POD
            + " where p.user_id= @{accountId} and p.order_id= @{orderId}")
    List<PodPO> listByOrderIdIgnoreStatus(@Param("accountId") String accountId, @Param("orderId") String orderId);
    
    @Select(QUERY_POD
            + " where p.preempt_status = @{preemptStatus} "
            + " and p.deleted = 0 "
            + " and p.status != 'Succeeded' "
            + " and p.product_type = @{produceType} "
            + " and p.bcc_instance_id != '' "
            + " and p.preempt_status_update_time <= @{beforeTime} " )
    List<PodPO> listBidRunningPods(@Param("preemptStatus") String preemptStatus,
                                    @Param("produceType") String produceType,
                                    @Param("beforeTime") Timestamp beforeTime);

    @Select(QUERY_POD
            + "#where() "
            + "  deleted = 0 "
            + "  and preempt_status = @{beforePreemptStatus} "
            + "  #if($_parameter.willBePreemptedInstanceIds && $_parameter.willBePreemptedInstanceIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.willBePreemptedInstanceIds $willBePreemptedInstanceId \" bcc_instance_id \" ) "
            + "         @{willBePreemptedInstanceId}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodPO> listPreemptBidPods(@Param("willBePreemptedInstanceIds") List<String> willBePreemptedInstanceIds,
                                   @Param("beforePreemptStatus") String beforePreemptStatus);

    @Update("update t_pod_v2 set preempt_status = @{afterPreemptStatus}, preempt_status_update_time = now() "
            + "#where() "
            + "  deleted = 0 "
            + "  and preempt_status = @{beforePreemptStatus} "
            + "  #if($_parameter.willBePreemptedInstanceIds && $_parameter.willBePreemptedInstanceIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.willBePreemptedInstanceIds $willBePreemptedInstanceId \" bcc_instance_id \" ) "
            + "         @{willBePreemptedInstanceId}"
            + "     #end"
            + "  #end "
            + "#end ")
    void updatePreemptedStatusByInstanceId(
            @Param("willBePreemptedInstanceIds") List<String> willBePreemptedInstanceIds,
            @Param("beforePreemptStatus") String beforePreemptStatus,
            @Param("afterPreemptStatus") String afterPreemptStatus);

    @Select(QUERY_POD + " where  p.status = @{status} and p.deleted = 0")
    List<PodPO> listByStatus(@Param("status") String status);

    @Select(QUERY_POD + " where  p.internal_status = @{internalStatus} and p.deleted = 0 and order_id != ''")
    List<PodPO> listByInternalStatus(@Param("internalStatus") String internalStatus);

    @Select(QUERY_POD + " where  p.deleted = 0 and p.status !=\'deleted\'")
    List<PodPO> listAllPods();

    @Select(QUERY_POD + " where p.deleted = 0 and p.status !=\'deleted\' "
            + " and preempt_status = @{preemptStatus} "
            + " and preempt_status_update_time <= @{beforeTime}")
    List<PodPO> listToBePreemptBidPods(@Param("preemptStatus") String preemptStatus,
                                       @Param("beforeTime") Timestamp beforeTime);

    @Update("update t_pod_v2 set status = @{status}, preempt_status = @{preemptStatus}, "
            + " preempt_status_update_time = now() "
            + "#where() "
            + "  deleted = 0 and status !=\'deleted\'"
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end "
            + "  #end "
            + "#end ")
    void batchUpdatePreemptedPod(
            @Param("podIds") List<String> podIds,
            @Param("status") String status,
            @Param("preemptStatus") String preemptStatus
    );

    @Update("update t_pod_v2 set ds_containers_synced_to_k8s = 1, updated_time = utc_timestamp(), "
         + " bci_resource_version = @{newBciResourceVersion} "
         + " where user_id = @{userId} and pod_id = @{podId} "
         + " and bci_resource_version = @{oldBciResourceVersion}")
    void markDsConatinersSynced(@Param("userId") String userId, @Param("podId") String podId, 
                                @Param("newBciResourceVersion") long newBciResourceVersion,
                                @Param("oldBciResourceVersion") long oldBciResourceVersion);

    @Select(QUERY_POD + " where p.deleted = 0 and p.status !=\'deleted\' "
            + " and p.status = @{status} "
            + " and p.preempt_status = @{preemptStatus} "
            + " and p.preempt_status_update_time <= @{beforeTime} ")
    List<PodPO> listPreemptedBidPods(@Param("status") String status,
                                     @Param("preemptStatus") String preemptStatus,
                                     @Param("beforeTime") Timestamp beforeTime);

    @Update("update t_pod_v2 set status = @{podPO.status}, pod_uuid = @{podPO.podUuid},"
            + " conditions = @{podPO.conditions}, extra =  @{podPO.extra}, updated_time = utc_timestamp(),"
            + " nfs = @{podPO.nfs}, empty_dir = @{podPO.emptyDir}, config_file = @{podPO.configFile}, "
            + " flex_volume = @{podPO.flexVolume}, pfs = @{podPO.pfs}, pod_volumes = @{podPO.podVolumes}, "
            + " bos = @{podPO.bos}, host_path = @{podPO.hostPath}, "
            + " internal_ip = @{podPO.internalIp}, internal_ipv6 = @{podPO.internalIPv6}, resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp}, "
            + " resource_recycle_reason = @{podPO.resourceRecycleReason}, zone_id = @{podPO.zoneId}, "
            + " ds_containers_version = @{podPO.dsContainersVersion}, "
            + " ds_containers_count = @{podPO.dsContainersCount}, "
            + " ds_containers_synced_to_k8s = @{podPO.dsContainersSyncedToK8S}, " 
            + " bci_resource_version = @{podPO.bciResourceVersion}, "
            + " subnet_uuid = @{podPO.subnetUuid} "
            + " where pod_id = @{podPO.podId} and deleted = 0 and preempt_status != 'PREEMPTED'")
    void updatePod(@Param("podPO") PodPO podPO);

    @Update("update t_pod_v2 set extra =  @{podPO.extra} where pod_id = @{podPO.podId} and user_id = @{podPO.userId}")
    void updatePodExtras(@Param("podPO") PodPO podPO);

    @Update("update t_pod_v2 set pod_uuid = @{podPO.podUuid} where pod_id = @{podPO.podId} and deleted = 0"
            + " and user_id = @{podPO.userId}")
    void updatePodUuid(@Param("podPO") PodPO podPO);

    @Update("update t_pod_v2 set client_token = @{podPO.clientToken} where pod_id = @{podPO.podId} and deleted = 0"
            + " and user_id = @{podPO.userId}")
    void updatePodClientToken(@Param("podPO") PodPO podPO);

    @Update("update t_pod_v2 set internal_status = @{podPO.internalStatus},"
            + " pod_volumes = @{podPO.podVolumes}, updated_time = utc_timestamp(),"
            + " internal_ip = @{podPO.internalIp}, internal_ipv6 = @{podPO.internalIPv6}, pod_uuid = @{podPO.podUuid}, resource_uuid = @{podPO.resourceUuid},"
            + " node_name = @{podPO.nodeName}, bcc_instance_id = @{podPO.bccInstanceId}"
            + " where pod_id = @{podPO.podId} and deleted = 0 and internal_status != 'synced'")
    void updateOrderInfo(@Param("podPO") PodPO podPO);

    @Update("update t_pod_v2 set created_bls_tasks_id = @{createdBLSTasksID}, updated_time = utc_timestamp()"
            + " where pod_id = @{podPO.podId} and deleted = 0")
    void updateCreatedBLSTasksID(@Param("podPO") PodPO podPO, @Param("createdBLSTasksID") String createdBLSTasksID);

    @Update("update t_pod_v2 set order_id = @{orderId}, updated_time = utc_timestamp() "
            + "#where() "
            + "  user_id = @{accountId} "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    void updateOrderId(@Param("podIds") List<String> podIds, @Param("accountId") String accountId,
                       @Param("orderId") String orderId);

    @Select("select max(updated_time) from t_pod_v2 where deleted != 1 and status != \'Pending\'")
    Timestamp updateSince();

    @Select(QUERY_POD + " where p.user_id = @{accountId} and p.deleted = 0")
    List<PodPO> listPodByAccount(@Param("accountId") String accountId);

    @Select(QUERY_POD + " where (p.user_id = @{accountId} or p.charge_account_id = @{accountId}) and "
            + "p.deleted = 0 and cpt1 = 1")
    List<PodPO> listCpt1PodByAccount(@Param("accountId") String accountId);

    @Select(QUERY_POD
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodPO> listPodByPodIds(@Param("podIds") List<String> podIds, @Param("accountId") String accountId);

    @Select(QUERY_POD
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.podUuids && $_parameter.podUuids.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodPO> listPodByPodUuids(@Param("podUuids") List<String> podIds, @Param("accountId") String accountId);

    @MapKey("subnet_uuid")
    @Select("select subnet_uuid, count(*) as num from t_pod_v2 where "
            + "  user_id = @{accountId} and order_id != '' "
            + "  and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.subnetIds && $_parameter.subnetIds.size() > 0)"
            + "     and  "
            + "     #in($_parameter.subnetIds $subnetId \" subnet_uuid \" ) "
            + "         @{subnetId}"
            + "     #end"
            + "  #end "
            + "  group by subnet_uuid")
    Map<String, Map<String, Long>> countPodWithSubnetId(@Param("accountId") String accountId,
                                                        @Param("subnetIds") List<String> subnetIds);

    @Select("select count(*) from t_pod_v2 p"
            + " where p.status != \'unknown\' and p.deleted = 0 and p.status !=\'deleted\'"
            + " and p.order_id= @{orderId}")
    int getPodCountByOrderId(@Param("orderId") String orderId);

    @Select("select count(*) from t_pod_v2 p"
            + " where p.order_id= @{orderId}")
    int getPodCountByOrderidIgnoreStatus(@Param("orderId") String orderId);

    @Select(QUERY_POD_DELETE + " where p.resource_recycle_timestamp != 0 and p.resource_recycle_complete = 0")
    List<PodPO> getResourceRecyclePod();

    @Update("update t_pod_v2 set eip_status = 1, updated_time = utc_timestamp()"
            + " where user_id = @{accountId} and pod_id = @{podId} and public_ip = @{eipIp}")
    void deleteEip(@Param("accountId") String accountId, @Param("podId") String podId, @Param("eipIp") String eipIp);

    @Update("update t_pod_v2 set public_ip = @{eipIp}, bandwidth_in_mbps = @{bandwidth}, eip_status = 0, "
            + " updated_time = utc_timestamp() where user_id = @{accountId} and pod_id = @{podId}")
    void bindEip(@Param("accountId") String accountId, @Param("podId") String podId, @Param("eipIp") String eipIp, 
                 @Param("bandwidth") int bandwidth);

    @Update("update t_pod_v2 set public_ip = @{eipIp}, eip_status = @{eipStatus}, updated_time = utc_timestamp() "
            + " where user_id = @{accountId} and pod_id = @{podId}")
    void unbindEip(@Param("accountId") String accountId, @Param("podId") String podId, @Param("eipIp") String eipIp,
                @Param("eipStatus") int eipStatus);

    @Select(QUERY_EIP
        + " where p.deleted = 0 and p.status != \'deleted\' and p.status != \'unknown\' and p.order_id != \'\'"
        + " and p.public_ip != \'\' and (p.eip_status = 0 or p.eip_status = 3)")
    List<PodPO> getEipOfActivePod();

    @Update("update t_pod_v2 set bandwidth_in_mbps = @{podPO.bandwidthInMbps},"
            + " eip_actual_status = @{podPO.eipActualStatus}, updated_time = utc_timestamp()"
            + " where user_id = @{podPO.userId} and deleted = 0 and public_ip != \'\' and pod_id = @{podPO.podId}")
    void updateEipInfo(@Param("podPO") PodPO podPO);

    @Update("update t_pod_v2 set eip_id = @{podPO.eipId}, eip_route_type = @{podPO.eipRouteType},"
            + " eip_pay_method = @{podPO.eipPayMethod}, bandwidth_in_mbps = @{podPO.bandwidthInMbps},"
            + " eip_actual_status = @{podPO.eipActualStatus}, updated_time = utc_timestamp()"
            + " where user_id = @{podPO.userId} and deleted = 0 and public_ip != \'\' and pod_id = @{podPO.podId}")
    void updateNewEipInfo(@Param("podPO") PodPO podPO);

    @Update("update t_pod_v2 set eip_actual_status = \'\', updated_time = utc_timestamp()"
            + " where user_id = @{podPO.userId} and deleted = 0 and public_ip != \'\' and pod_id = @{podPO.podId}")
    void updateUnbindedEipInfo(@Param("podPO") PodPO podPO);

    @Select("select p.pod_id, p.internal_ip FROM t_pod_v2 p where p.user_id = @{accountId} and p.public_ip = @{eipIp}"
    + " and p.eip_status = @{eipStatus}"
    + " and p.status != \'unknown\' and p.deleted = 0 and p.status !=\'deleted\' and order_id != \'\'")
    List<PodPO> getPodsByEip(@Param("accountId") String accountId, @Param("eipIp") String eipIp, 
                        @Param("eipStatus") int eipStatus);

    @Select(QUERY_POD + " where p.user_id = @{podPO.userId} and p.name = @{podPO.name} and p.cce_uuid = @{podPO" +
            ".cceUuid}"
            + " and p.client_token = @{podPO.clientToken}")
    PodPO podIdempotentCheck(@Param("podPO") PodPO podPO);

    @Select(QUERY_EIP
    + " where p.id > @{startId} and p.user_id = @{accountId} and p.deleted = 0 and p.public_ip != \'\' and p" +
            ".resource_recycle_complete = 0 "
    + " and (p.eip_status = 0 or p.eip_status = 3) order by p.id asc limit @{step}")
    List<PodPOWithId> getActiveEipByUser(@Param("accountId") String accountId, @Param("startId") long startId, 
                @Param("step") int step);

    @Select("select pod_uuid, pod_id from t_pod_v2 where (deleted_time = '1971-01-01 08:00:01' or deleted_time >= " +
            "@{startTime}) "
            + "and user_id = @{userId} and cpt1 = 1 ")
    List<PodPO> getCpcTestPodUuidByDeletedTime(@Param("startTime")Timestamp startTime, @Param("userId")String userId);

    @Select(QUERY_POD_BRIEF
            + "#where() "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodPO> getPodsByPodIds(@Param("podIds") List<String> podIds);

    @Update("update t_pod_v2 set conditions = @{conditions}, updated_time = utc_timestamp() "
            + " where user_id = @{userId} and pod_id = @{podId}")
    int updatePodConditionsByPodId(
            @Param("userId") String userId,
            @Param("podId") String podId,
            @Param("conditions") String conditions);

    @Insert(INSERT_POD + " values "
            + "(@{podPO.podId}, @{podPO.podUuid},"
            + " @{podPO.name}, @{podPO.status}, @{podPO.subStatus}, @{podPO.internalStatus},"
            + " @{podPO.vCpu}, @{podPO.memory}, @{podPO.eipUuid}, @{podPO.publicIp},"
            + " @{podPO.bandwidthInMbps}, @{podPO.eipStatus}, @{podPO.cceUuid}, @{podPO.internalIp}, @{podPO.internalIPv6},"
            + " @{podPO.subnetUuid}, @{podPO.securityGroupUuid},"
            + " @{podPO.restartPolicy}, @{podPO.orderId}, @{podPO.tags}, @{podPO.description},"
            + " @{podPO.userId}, @{podPO.zoneId}, @{podPO.logicalZone}, @{podPO.resourceUuid}, @{podPO.taskStatus}, @{podPO.nfs},"
            + " @{podPO.emptyDir}, @{podPO.configFile}, @{podPO.flexVolume}, @{podPO.pfs}, @{podPO.bos},"
            + " @{podPO.hostPath}, @{podPO.podVolumes},"
            + " @{podPO.application}, @{podPO.conditions}, @{podPO.enableLog}, @{podPO.chargeSource}, @{podPO.chargeAccountId},"
            + " utc_timestamp(), @{podPO.gpuType}, @{podPO.gpuCount}, @{podPO.productType}, @{podPO.preemptStatus},"
            + " @{podPO.cpuType}, @{podPO.delayReleaseDurationMinute}, @{podPO.delayReleaseSucceeded},"
            + " @{podPO.zoneSubnets}, @{podPO.isTidal}, @{podPO.cpt1}, @{podPO.extra}, @{podPO.dsContainersVersion},"
            + " @{podPO.dsContainersCount}, @{podPO.dsContainersSyncedToK8S}, @{podPO.clientToken})")
    int stateMachineCreatePod(@Param("podPO") PodPO podPO,
                              @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set status = @{podPO.status}, sub_status = @{podPO.subStatus},"
            + " updated_time = utc_timestamp(),"
            + " resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp},"
            + " resource_recycle_reason = @{podPO.resourceRecycleReason},"
            + " resource_version = @{podPO.resourceVersion},"
            + " bci_resource_version = @{podPO.bciResourceVersion}"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId}"
            + " and resource_version = @{context.resourceVersion}"
            + " and bci_resource_version = @{context.bciResourceVersion}")
    int stateMachineUpdatePod(@Param("podPO") PodPO podPO,
                              @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set deleted = 1, status = @{podPO.status}, sub_status = @{podPO.subStatus},"
            + " updated_time = utc_timestamp(), deleted_time = utc_timestamp(), commit_deleted_time = utc_timestamp(),"
            + " resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp},"
            + " resource_recycle_reason = @{podPO.resourceRecycleReason},"
            + " resource_version = @{podPO.resourceVersion},"
            + " bci_resource_version = @{podPO.bciResourceVersion}"
            + " where deleted = 0 and user_id = @{podPO.userId} and pod_id = @{podPO.podId}")
    int stateMachineDeletePod(@Param("podPO") PodPO podPO,
                              @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set resource_version = @{podPO.resourceVersion},"
            + " bci_resource_version = @{podPO.bciResourceVersion},"
            + " updated_time = utc_timestamp()"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId}"
            + " and bci_resource_version = @{context.bciResourceVersion}")
    int stateMachineK8SPodDeletedByUser(@Param("podPO") PodPO podPO,
                                        @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set status = @{podPO.status},"
            + " resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp},"
            + " resource_recycle_reason = @{podPO.resourceRecycleReason},"
            + " resource_version = @{podPO.resourceVersion},"
            + " bci_resource_version = @{podPO.bciResourceVersion},"
            + " updated_time = utc_timestamp()"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId}"
            + " and bci_resource_version = @{context.bciResourceVersion}")
    int stateMachineK8SPodDeletedByK8SUnexpectedly(@Param("podPO") PodPO podPO,
                                                   @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set status = @{podPO.status}, sub_status = @{podPO.subStatus},"
            + " updated_time = utc_timestamp(),"
            + " resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp},"
            + " resource_recycle_reason = @{podPO.resourceRecycleReason},"
            + " resource_version = @{podPO.resourceVersion},"
            + " bci_resource_version = @{podPO.bciResourceVersion}"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId}"
            + " and resource_version = @{context.resourceVersion}"
            + " and bci_resource_version = @{context.bciResourceVersion}")
    int stateMachineK8SPodEvicted(@Param("podPO") PodPO podPO,
                                  @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set resource_recycle_complete = @{podPO.resourceRecycleComplete},"
            + " resource_recycle_complete_timestamp = @{podPO.resourceRecycleCompleteTimestamp},"
            + " sub_status = @{podPO.subStatus},"
            + " updated_time = utc_timestamp(),"
            + " bci_resource_version = @{podPO.bciResourceVersion}"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId}")
    int stateMachineResourceRecycleComplete(@Param("podPO") PodPO podPO,
                                            @Param("context") StateMachinePodDaoContext context);

    // ENI_CREATED_FAIL & JOB_POD_COMPLETE 场景，podPO 的 resource_recycle_timestamp 会被 PodContainerSyncServiceV2 更新，pod 被回收
    // 这样订单超时，这种情况也需要在这里将 internal_status 更新为 synced，避免用户删除pod前一直查询billing，去除更新语句中 resource_recycle_timestamp 为 0 的条件
    @Update("update t_pod_v2 set resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp},"
            + " resource_recycle_reason = @{podPO.resourceRecycleReason},"
            + " internal_status = @{podPO.internalStatus},"
            + " status = @{podPO.status},"
            + " sub_status = @{podPO.subStatus},"
            + " bci_resource_version = @{podPO.bciResourceVersion},"
            + " updated_time = utc_timestamp()"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId}"
            + " and bci_resource_version = @{context.bciResourceVersion}")
    int stateMachineOrderFailed(@Param("podPO") PodPO podPO,
                                @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp},"
            + " resource_recycle_reason = @{podPO.resourceRecycleReason},"
            + " internal_status = @{podPO.internalStatus},"
            + " status = @{podPO.status},"
            + " sub_status = @{podPO.subStatus},"
            + " bci_resource_version = @{podPO.bciResourceVersion},"
            + " updated_time = utc_timestamp()"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId}"
            + " and resource_recycle_timestamp = 0"
            + " and bci_resource_version = @{context.bciResourceVersion}")
    int stateMachineK8SPodNoResourceAvailable(@Param("podPO") PodPO podPO,
                                        @Param("context") StateMachinePodDaoContext context);

    @Update("update t_pod_v2 set status = @{podPO.status} , pod_uuid = @{podPO.podUuid},"
            + " conditions = @{podPO.conditions}, updated_time = utc_timestamp(),"
            + " internal_ip = @{podPO.internalIp}, internal_ipv6 = @{podPO.internalIPv6}, resource_recycle_timestamp = @{podPO.resourceRecycleTimestamp}, "
            + " resource_recycle_reason = @{podPO.resourceRecycleReason}, zone_id = @{podPO.zoneId}, logical_zone = @{podPO.logicalZone}, "
            + " bci_resource_version = @{podPO.bciResourceVersion}, "
            + " subnet_uuid = @{podPO.subnetUuid}, resource_version = @{podPO.resourceVersion}"
            + " where user_id = @{podPO.userId} and pod_id = @{podPO.podId} "
            + " and deleted = 0 and preempt_status != 'PREEMPTED'"
            + " and resource_version >= @{context.resourceVersion}"
            + " and bci_resource_version = @{context.bciResourceVersion}")
    int stateMachineK8SPodEventStatus(@Param("podPO") PodPO podPO,
                                      @Param("context") StateMachinePodDaoContext context);
}
