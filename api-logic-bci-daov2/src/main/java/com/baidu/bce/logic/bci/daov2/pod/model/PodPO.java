package com.baidu.bce.logic.bci.daov2.pod.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.bci.dao.pod.model.PodPOForBcm;
import com.baidu.bce.logic.bci.daov2.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PodPO implements Cloneable, PodPOForBcm {

    private String name = "";
    private String podId = "";
    private String podUuid = "";
    private int podIndex = 0;
    private String status = "";
    private String subStatus = "";
    private String internalStatus = "";
    private int delayReleaseDurationMinute = 0;
    private boolean delayReleaseSucceeded = false;
    @JsonProperty(value = "vCpu")
    private float vCpu = 0;
    private float memory = 0;
    private String cpuType = "";
    private String gpuType = "";
    private float gpuCount = 0;
    private String productType = "";
    private String eipUuid = "";
    private String eipId = "";
    private String publicIp = "";
    private String eipRouteType = "";
    private String eipPayMethod = "";
    private String eipActualStatus = ""; // pod绑定eip的实际状态: "binded", ""
    @JsonIgnore
    private int eipStatus = 0; // EIP用户期望状态: 0 使用中，1 已删除，2 已解绑, 3 用户指定的eip使用中, 4 用户指定的eip已解绑 
    @JsonIgnore
    private int bandwidthInMbps = 0;
    private String cceUuid = "";
    private String internalIp = "";
    private String internalIPv6 = "";
    private String securityGroupUuid;
    private String restartPolicy = "";
    @JsonIgnore
    private String tags = "";    // 存储label
    private String orderId = "";
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    private String description = "";
    private String region = "";
    private String userId = "";
    private String resourceUuid = "";
    private String taskStatus = "";
    @JsonIgnore
    private String nfs = "";
    @JsonIgnore
    private String emptyDir = "";
    @JsonIgnore
    private String configFile = "";
    @JsonIgnore
    private String flexVolume = "";
    @JsonIgnore
    private String pfs = "";

    @JsonIgnore
    private String bos = "";
    @JsonIgnore
    private String hostPath = "";
    @JsonIgnore
    private String podVolumes = "";
    @JsonIgnore
    private String cephFS = "";

    private int deleted = 0;

    private long resourceRecycleTimestamp = 0;

    private String resourceRecycleReason = "";

    private int resourceRecycleComplete = 0;

    private long resourceRecycleCompleteTimestamp = 0;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp commitDeletedTime;

    // K8S Pod资源版本号
    private long resourceVersion = 0;
    // BCI Pod资源版本号
    private long bciResourceVersion = 0;
    @JsonIgnore
    private int enableLog = 0;
    @JsonProperty(value = "tags")
    private List<Tag> podTags;   // tag服务的标签
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String subnetUuid = "";
    private String zoneId = "";
    private String logicalZone = "";
    private String subnetType = "";
    private String eipGroupId = "";
    private List<Label> labels;
    private String zoneSubnets = "";
    @JsonIgnore
    private Boolean cpt1 = false;
    // 标记是否为v2类型的pod
    private boolean v2 = true;
    // dsContainer版本，仅在有dsContainer的pod中有效，
    // 不包含dsContainer的pod中，该值一直为0
    // 包含dsContainer的pod中，每次涉及到ds容器变更，该值都会累加1
    private long dsContainersVersion = 0;
    // ds容器的数量，只在bci控制面同步更新pod时用到
    // 数据库主从延迟，导致podPO已经更新，但是containerPO还未创建，就会导致更新到底层k8s集群的容器与数据库预期不一致。
    // 为了解决该问题，在podPO中记录一下dsContainersCount，如果podPO记录的ds容器数量和containerPO表中查询的不一致，暂不处理，因为可能是主从延迟。
    private long dsContainersCount = 0;
    // 记录ds容器是否已经同步到bci底层k8s集群
    private boolean dsContainersSyncedToK8S = true;

    /**
     * 业务
     */
    private String application;

    private boolean pushLog = false;

    private String chargeSource; // charge source ， 计费源, 普通模式值为user，统一计费为各服务名

    private String chargeAccountId = ""; // 当存在账号代付时，为代付账号，即平台账户id

    // 新增字段，存储 pod.status.conditions
    @JsonIgnore
    private String conditions;
    @JsonProperty(value = "created_bls_tasks_id")
    private String createdBLSTasksID;
    @JsonIgnore
    private String nodeName = ""; // pod对应的node
    @JsonIgnore
    private String bccInstanceId = ""; // node对应的bcc的instanceid 

    private String preemptStatus = "RUNNING"; // 抢占类型的实例，被抢占的状态，RUNNING运行期，
                                                // TOBEPREEMPTED即将被抢占；PREEMPTED被抢占

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT)
    private Timestamp preemptStatusUpdateTime; // 记录抢占相关操作时间

    private String extra; // 存储额外信息,必须是json格式,避免覆盖

    private boolean isTidal; // 是否是潮汐pod

    private String clientToken = ""; // 创建Pod对应的clientToken

    public float getvCpu() {
        return vCpu;
    }

    public void setvCpu(float vCpu) {
        this.vCpu = vCpu;
    }

    public int getIntCpt1Mode() {
        return getCpt1() ? 1 : 0;
    }

    public String getRealChargeAccountId() {
        if (StringUtils.isNotEmpty(chargeAccountId)) {
            return chargeAccountId; // 代付平台账号
        }
        return userId; // 用户账号
    }
}