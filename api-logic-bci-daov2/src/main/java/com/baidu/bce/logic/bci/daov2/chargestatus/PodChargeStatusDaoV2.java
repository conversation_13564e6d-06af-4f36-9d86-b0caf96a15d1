package com.baidu.bce.logic.bci.daov2.chargestatus;

import com.baidu.bce.logic.bci.daov2.chargestatus.mapper.PodChargeStatusMapperV2;
import com.baidu.bce.logic.bci.daov2.chargestatus.model.PodChargeStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Repository
public class PodChargeStatusDaoV2 {

    @Autowired
    PodChargeStatusMapperV2 podChargeStatusMapper;

    /**
     * 特殊的id，这条记录只用来存储 since 时间点
     * */
    public static final String SINCE_PLACEHOLDER = "since-placeholder";

    public int insert(PodChargeStatus podChargeStatus) {
        return podChargeStatusMapper.insert(podChargeStatus);
    }

    // 仅用于 syncPodInBuild，规避重复插入
    public int insertWhenPodUnsync(PodChargeStatus podChargeStatus) {
        return podChargeStatusMapper.insertWhenPodUnsync(podChargeStatus);
    }

    public Map<String, LinkedList<PodChargeStatus>> listCpcChargesByTime(
            Timestamp lastChargeTime, Timestamp chargeTime) {
        List<PodChargeStatus> podChargeStatuses =
            podChargeStatusMapper.listCpcChargesByTime(lastChargeTime, chargeTime, SINCE_PLACEHOLDER);
        Map<String, LinkedList<PodChargeStatus>> map = new HashMap<>();
        for (PodChargeStatus podChargeStatus : podChargeStatuses) {
            if (!map.containsKey(podChargeStatus.getPodUuid())) {
                map.put(podChargeStatus.getPodUuid(), new LinkedList<PodChargeStatus>());
            }
            map.get(podChargeStatus.getPodUuid()).add(podChargeStatus);
        }
        return map;
    }

    public Timestamp getSinceTime() {
        PodChargeStatus status = podChargeStatusMapper.getSinceTime(SINCE_PLACEHOLDER);
        if (status == null) {
            return null;
        }
        return status.getCreatedTime();
    }

    public void updateSinceTime(Timestamp nextSinceTime) {
        podChargeStatusMapper.updateSinceTime(nextSinceTime, SINCE_PLACEHOLDER);
    }

    public long getMaxResourceVersion(String uuid, long resourceVersion) {
        Long maxVersion = podChargeStatusMapper.getMaxResourceVersion(uuid, resourceVersion);
        if (null == maxVersion) {
            return 0;
        }
        return maxVersion;
    }

    public PodChargeStatus queryByResourceVersion(String uuid, long resourceVersion) {
        return podChargeStatusMapper.queryByResourceVersion(uuid, resourceVersion);
    }

    public void updateCpt1SyncStateById(long id, long resourceVersion, String uuid, int cpt1SyncState) {
        Timestamp updateTime = new Timestamp(new Date().getTime());
        podChargeStatusMapper.updateCpt1SyncStateById(id, resourceVersion, uuid, cpt1SyncState, updateTime);
    }

    public List<PodChargeStatus> listNeedToSyncCpt1Status() {
        List<PodChargeStatus> podChargeStatuses =
                podChargeStatusMapper.listNeedToSyncCpt1Status();
        return podChargeStatuses;
    }

    public String getChargeStatusWhichResourceVersionIsNewest(String uuid) {
        String chargeStatuses = podChargeStatusMapper.getChargeStatusWhichResourceVersionIsNewest(uuid);
        return chargeStatuses;
    }

    public String getChargeStatusWhichCreatedTimeIsNewest(String uuid) {
        String chargeStatuses = podChargeStatusMapper.getChargeStatusWhichCreatedTimeIsNewest(uuid);
        return chargeStatuses;
    }
}
