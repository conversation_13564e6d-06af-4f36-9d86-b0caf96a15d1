package com.baidu.bce.logic.bci.daov2.chargerecord;

import com.baidu.bce.logic.bci.daov2.chargerecord.mapper.PodChargeRecordMapperV2;
import com.baidu.bce.logic.bci.daov2.chargerecord.model.PodChargeRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.Date;

@Repository
public class PodChargeRecordDaoV2 {

    @Autowired
    PodChargeRecordMapperV2 podChargeRecordMapper;

    public Boolean tryToLockOneLine(Date lockDate, String lockId, Long id, int overTime) {
        return podChargeRecordMapper.tryToLockOneLine(lockDate, lockId, id, overTime, lockDate) == 1;
    }

    public int insert(PodChargeRecord podChargeRecord) {
        return podChargeRecordMapper.insert(podChargeRecord);
    }

    public PodChargeRecord getNeedToPush() {
        return podChargeRecordMapper.getNeedToPush();
    }

    public int updateSucc(Long id) {
        return podChargeRecordMapper.updateSucc(id);
    }

    public Boolean unLock(Long id) {
        return podChargeRecordMapper.unLock(id) == 1;
    }

    public int delete(Long id) {
        return podChargeRecordMapper.delete(id);
    }

    public PodChargeRecord getLastPodChargeRecord() {
        return podChargeRecordMapper.getLastPodChargeRecord();
    }

    public int deleteBefore(Timestamp time) {
        return podChargeRecordMapper.deleteBefore(time);
    }
}
