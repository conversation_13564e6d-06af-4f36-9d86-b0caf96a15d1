package com.baidu.bce.logic.bci.daov2.pod.util;

import com.baidu.bce.logic.core.exception.CommonExceptions;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class PodDBHelper {
    private static final String SQL_SPLIT = ",";
    public static final Map<String, String> FIELD_MAP = new HashMap<>();

    private static final Set<String> QUERY_TYPE_SET;

    public static final Map<String, String> FIELD_EXACT_MATCH_MAP = new HashMap<>();

    static {
        FIELD_MAP.put("podId", "pod_id");
        FIELD_MAP.put("cceId", "cce_uuid");
        FIELD_MAP.put("createTime", "created_time");
        FIELD_MAP.put("name", "name");
        FIELD_MAP.put("status", "status");
        FIELD_MAP.put("tags", "tags");
        FIELD_MAP.put("restartPolicy", "restart_policy");
        FIELD_MAP.put("internalIp", "internal_ip");
        FIELD_MAP.put("zoneId", "zone_id");
        FIELD_MAP.put("vCpu", "v_cpu");
        FIELD_MAP.put("memory", "memory");
        FIELD_MAP.put("gpuType", "gpu_type");
        FIELD_MAP.put("gpuCount", "gpu_count");
        FIELD_MAP.put("cpt1", "cpt1");

        QUERY_TYPE_SET = new HashSet<>(Arrays.asList("name", "restartPolicy", "podId", "status"));
    }

    static {
        FIELD_EXACT_MATCH_MAP.put("cceId", "cce_uuid");
    }

    /**
     * 通过request中的orderBy字段获取数据库的orderBy字符串
     *
     * @param orderByField request中的字段
     * @return 数据库的orderBy字符串
     */
    public static String parseOrderBy(String orderByField) {
        String field = FIELD_MAP.get(orderByField);
        if (field == null) {
            throw new CommonExceptions.RequestInvalidException();
        }
        return field;
    }


    public static String toSerial(String volumeUuid) {
        String serial = "";
        if (StringUtils.isNotBlank(volumeUuid) && volumeUuid.length() > 20) {
            serial = volumeUuid.substring(0, 20);
        }
        return serial;
    }

    /**
     * keyword类型是否合法
     *
     * @param keywordType keyword类型
     * @return 返回true表示合法
     */
    public static boolean isKeywordTypeValid(String keywordType) {
        return QUERY_TYPE_SET.contains(keywordType);
    }

    /**
     * sql  :1:2:3:4:   --->>    int[] {1,2,3,4}
     *
     * @param line
     * @return
     */
    private static int[] toIntArray(String line) {
        String[] nums = line.split(SQL_SPLIT);
        int[] result = new int[nums.length - 1];
        for (int i = 1; i < nums.length; i++) {
            result[i - 1] = Integer.parseInt(nums[i]);
        }
        return result;
    }

}
