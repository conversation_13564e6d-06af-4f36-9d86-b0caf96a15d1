package com.baidu.bce.logic.bci.daov2.common.util;

import com.baidu.bce.logic.bci.daov2.pod.model.PodPO;
import com.baidu.bce.logical.tag.sdk.common.TagComparator;
import com.baidu.bce.logical.tag.sdk.model.Tag;

import java.util.List;

public class PodPOComparator extends TagComparator<PodPO> {

    public PodPOComparator(boolean isAscend) {
        super(isAscend);
    }

    @Override
    public List<Tag> getO1Tags(PodPO o1) {
        return o1.getPodTags();
    }

    @Override
    public List<Tag> getO2Tags(PodPO o2) {
        return o2.getPodTags();
    }

}
