package com.baidu.bce.logic.bci.daov2.reservedinstance.mapper;

import java.util.List;

import com.baidu.bce.logic.bci.daov2.reservedinstance.model.ReservedInstanceSpec;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface ReservedInstanceSpecMapper {
    @Select("SELECT * from t_reserved_instance_spec where deleted = 0")
    List<ReservedInstanceSpec> listReservedInstanceSpecs();

    @Select("SELECT * from t_reserved_instance_spec where deleted = 0 and name = @{spec}")
    ReservedInstanceSpec getSpecBySpecName(@Param("spec") String spec);
}
