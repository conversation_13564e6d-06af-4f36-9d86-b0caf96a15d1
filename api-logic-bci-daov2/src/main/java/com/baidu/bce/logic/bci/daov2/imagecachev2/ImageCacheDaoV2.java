package com.baidu.bce.logic.bci.daov2.imagecachev2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Repository;

import com.baidu.bce.logic.bci.daov2.imagecachev2.model.ImageCachePO;
import com.baidu.bce.logic.bci.daov2.imagecachev2.mapper.ImageCacheV2Mapper;

import java.util.List;

@Repository("imageCacheV2")
public class ImageCacheDaoV2 {
    @Autowired
    private ImageCacheV2Mapper imageCacheV2Mapper;

    public ImageCachePO getImageCacheById(String imageCacheId, String accountId) {
        return imageCacheV2Mapper.getImageCacheById(imageCacheId, accountId);
    }

    public void insertImageCachePO(ImageCachePO imageCachePO) throws DataAccessException {
        imageCacheV2Mapper.insertImageCachePO(imageCachePO);
    }

    public List<ImageCachePO> getImageCacheByStatus(ImageCachePO imageCachePO) {
        return imageCacheV2Mapper.getImageCacheByStatus(imageCachePO);
    }

    public ImageCachePO getImageCacheByName(String imageCacheName, String accountId) {
        return imageCacheV2Mapper.getImageCacheByName(imageCacheName, accountId);
    }

    public void updateImageCache(ImageCachePO imageCachePO) {
        imageCacheV2Mapper.updateImageCache(imageCachePO);
    }

    public List<ImageCachePO> listImageCachePOs(String accountId, Long limit, Long offset) {
        return imageCacheV2Mapper.listImageCachePOs(accountId, limit, offset);
    }

    public List<ImageCachePO> listAllValidImageCachesByAccountId(String accountId) {
        return imageCacheV2Mapper.listAllValidImageCachesByAccountId(accountId);
    }

    public ImageCachePO getLeastRecentlyUsedImageCache(String accountId) {
        return imageCacheV2Mapper.getLeastRecentlyUsedImageCache(accountId);
    }

    public ImageCachePO getAllImageCacheById(String imageCacheId, String accountId) {
        return imageCacheV2Mapper.getAllImageCacheById(imageCacheId, accountId);
    }

    public void updateImageAccScanDoneByName(String imageAccName, String cpuTypes) {
        imageCacheV2Mapper.updateImageAccScanDoneByName(imageAccName, cpuTypes);
    }

    public List<ImageCachePO> listAllSuccessImageAccAndNotScanDone() {
        return imageCacheV2Mapper.listAllSuccessImageAccAndNotScanDone();
    }

    public List<ImageCachePO> listAllSuccessImageCacheAndNotPredeploy(List<String> userIds) {
        return imageCacheV2Mapper.listAllSuccessImageCacheAndNotPredeploy(userIds);
    }

    public int updateImageCachePredeployCompleted(ImageCachePO imageCachePO) {
        return imageCacheV2Mapper.updateImageCachePredeployCompleted(imageCachePO);
    }
}   