package com.baidu.bce.logic.bci.daov2.webshell;

import com.baidu.bce.logic.bci.daov2.webshell.mapper.WebshellMapper;
import com.baidu.bce.logic.bci.daov2.webshell.model.WebshellPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository("webshellDao")
public class WebshellDao {

    @Autowired
    private WebshellMapper webshellMapper;

    public void insert(WebshellPO webshellPO) {
        webshellMapper.insert(webshellPO);
    }

    public WebshellPO queryByToken(String token) {
        return webshellMapper.getByToken(token);
    }

    public int lockWebShellToken(String token, int version) {
        return webshellMapper.lockWebshellToken(token, version);
    }
}
