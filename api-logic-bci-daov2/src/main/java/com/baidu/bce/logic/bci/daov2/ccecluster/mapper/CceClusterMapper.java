package com.baidu.bce.logic.bci.daov2.ccecluster.mapper;

import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CceClusterMapper {
    String INSERT_CCE_CLUSTER = "insert into t_cce_cluster (name, cce_id, cce_kube_config, description, "
            + "created_time, updated_time)";

    String QUERY_CCE_CLUSTER = "SELECT id, name, cce_id, cce_kube_config, is_default, description, "
            + "created_time, updated_time FROM t_cce_cluster ";


    @Insert(INSERT_CCE_CLUSTER + " values "
            + "(@{cceCluster.name}, @{cceCluster.cceId}, @{cceCluster.cceKubeConfig}, @{cceCluster.description}, "
            + "@{cceCluster.createdTime}, @{cceCluster.updatedTime})")
    void insert(@Param("cceCluster") CceCluster cceCluster);

    @Select(QUERY_CCE_CLUSTER + " where cce_id = @{cceId}")
    CceCluster getCceClusterByCceId(@Param("cceId") String cceId);

    @Select(QUERY_CCE_CLUSTER )
    List<CceCluster> getAllCceClusters();

    @Select(QUERY_CCE_CLUSTER  + " where is_default = 1")
    List<CceCluster> getDefaultCceClusters();

    @Delete("delete from t_cce_cluster where cce_id = @{cceId} ")
    int delete(@Param("cceId") String cceId);


}