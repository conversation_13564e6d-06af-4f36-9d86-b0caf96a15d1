package com.baidu.bce.logic.bci.daov2.imagedetailv2.mapper;

import com.baidu.bce.logic.bci.daov2.imagedetailv2.model.ImageDetailPO;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.dao.DataAccessException;
import java.util.List;

public interface ImageDetailV2Mapper {
    @Select("select * from t_image_detail " 
        + "where origin_image_address = @{imageAddress} and origin_image_version= @{imageTag}"
        + "and account_id=@{accountId} and deleted=false")
    List<ImageDetailPO> getImageDetailByOriginImageAddressAndTag(@Param("imageAddress") String imageAddress, 
        @Param("imageTag") String imageTag, @Param("accountId") String accountId);

    @Select("select *from t_image_detail " 
        + "where origin_image_address = @{imageAddress} and account_id=@{accountId} and deleted=false")
    List<ImageDetailPO> getImageDetailByOriginImageAddress(@Param("imageAddress") String imageAddress, 
        @Param("accountId") String accountId);

    @Insert("insert into t_image_detail (account_id,image_cache_id,image_cache_type,"
        + "origin_image_address,origin_image_version"
        + ",acce_image_address,acce_image_version,deleted) values (@{imageDetailPO.accountId},"
        + "@{imageDetailPO.imageCacheId},@{imageDetailPO.imageCacheType}"
        + ",@{imageDetailPO.originImageAddress},@{imageDetailPO.originImageVersion},@{imageDetailPO.acceImageAddress}"
        +",@{imageDetailPO.acceImageVersion},@{imageDetailPO.deleted})")
    void insertImageDetailPO(@Param("imageDetailPO") ImageDetailPO imageDetailPO) throws DataAccessException;

    @Select("select * from t_image_detail where account_id=@{accountId} and "
        + "origin_image_address=@{imageAddress} and origin_image_version=@{imageTag} and "
        + " acce_image_version is not NULL and acce_image_version != \"\" and deleted=false")
    List<ImageDetailPO> getImageDetailByImageCacheTypeAndImageAddressTag(
        @Param("imageAddress") String imageAddress, @Param("imageTag") String imageTag, 
        @Param("accountId") String accountId);

    @Select("select * from t_image_detail where account_id=@{accountId} and "
            + "origin_image_address=@{imageAddress} and origin_image_version=@{imageTag} and "
            + " acce_image_version is not NULL and acce_image_version != \"\" and deleted=false order by id desc")
    List<ImageDetailPO> getImageDetailByImageAddressTagOrderByIdDesc(
            @Param("imageAddress") String imageAddress, @Param("imageTag") String imageTag,
            @Param("accountId") String accountId);

    @Select("select * from t_image_detail where image_cache_id=@{imageCacheId} and deleted=false")
    List<ImageDetailPO> getImageDetailsByImageCacheId(@Param("imageCacheId") String imageCacheId);

    @Update("update t_image_detail set acce_image_address=@{imageDetailPO.acceImageAddress},"
       + "acce_image_version=@{imageDetailPO.acceImageVersion},"
       + "deleted=@{imageDetailPO.deleted},updated_at=@{imageDetailPO.updatedAt},"
       + "deleted_at=@{imageDetailPO.deletedAt},"
       + "image_cache_id=@{imageDetailPO.imageCacheId} "
       + " where id=@{imageDetailPO.id}")
    void updateImageAccDetail(@Param("imageDetailPO") ImageDetailPO imageDetailPO);

    @Select("select * from t_image_detail " 
        + "where origin_image_address = @{imageAddress} and origin_image_version= @{imageTag}"
        + "and account_id=@{accountId} and deleted=false and image_cache_type='stargz'")
    List<ImageDetailPO> getCcrImageDetailByOriginImageAddressAndTag(@Param("imageAddress") String imageAddress, 
        @Param("imageTag") String imageTag, @Param("accountId") String accountId);

    @Select("select * from t_image_detail where image_cache_id=@{imageCacheId} and deleted=0")
    List<ImageDetailPO> getImageDetailsByImageCacheIdForQuery(@Param("imageCacheId") String imageCacheId);
    
}