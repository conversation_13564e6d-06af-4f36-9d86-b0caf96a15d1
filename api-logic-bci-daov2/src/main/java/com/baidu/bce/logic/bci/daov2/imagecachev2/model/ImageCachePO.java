package com.baidu.bce.logic.bci.daov2.imagecachev2.model;

import com.baidu.bce.internalsdk.core.BceConstant;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.experimental.Accessors;
import java.sql.Timestamp;

@Data
@Accessors(chain = true)
public class ImageCachePO implements Comparable<ImageCachePO> {
    private long id;
    private String imageCacheName = "";
    private String imageCacheId = "";
    private String accountId;
    private int temporaryStorageSize;
    private int retentionDay;
    private String imageSecrets = "";
    private String status = "";
    private int progress;
    private String cdsSnapShotId = "";
    private String imageCacheOwner = "";
    private String imageCacheType = "";
    private Boolean deleted;

    private String subnetId ="";
    private String securityGroupId = "";
    private String zoneSubnets = "";
    private int needEip;
    private int enableEni;
    private String eliminationStrategy = "";
    private float suitability;
    private String tcPodName;

    // for amd透明化
    private String cpuTypes;
    private int scanDone;
    
    // 默认是utc timeStamp
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedAt;

    private int imageCachePredeploy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp imageCachePredeployTime;

    private  String imageCachePredeployDaemonsetName;

    public int compareTo(ImageCachePO o) {
        if (this.suitability > o.suitability) {
            return 1;
        }  else if (this.suitability < o.suitability) {
            return -1;
        } else {
            if (this.createdAt.compareTo(createdAt) > 0) {
                return 1;
            } else if (this.createdAt.compareTo(createdAt) < 0) {
                return -1;
            } else {
                if (this.temporaryStorageSize < o.temporaryStorageSize) {
                    return 1;
                } else if (this.temporaryStorageSize < o.temporaryStorageSize) {
                    return -1;
                } else {
                    return 0;
                }
            }
        }
    }
}