package com.baidu.bce.logic.bci.daov2.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by huping on 2019-07-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WebShell {
    // 容器组id
    private String podId;
    // 容器名称
    private String containerName;
    // 是否分配tty
    private Boolean tty = true;
    // 是否开启stdin
    private Boolean stdin = true;
    // 是否开启stdout
    private Boolean stdout = true;
    // 是否开启stderr
    private Boolean stderr = false;
    // ["sh"]
    private List<String> command = new ArrayList<>();
}
