package com.baidu.bce.logic.bci.daov2.ccecluster.model;

import com.baidu.bce.internalsdk.core.BceConstant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CceCluster implements Cloneable {
    private long id;
    private String name = "";
    private String cceId = "";

    private String cceKubeConfig = "";
    private int isDefault = 0;
    private String description = "";

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;


    @JsonIgnore
    private int deleted = 0;

    public CceCluster() {
    }

    public CceCluster(String name, String cceId, String cceKubeConfig, String description) {
        Timestamp currentTime = new Timestamp(new Date().getTime());

        this.name = name;
        this.cceId = cceId;
        this.cceKubeConfig = cceKubeConfig;
        this.description = description;
        this.createdTime = currentTime;
        this.updatedTime = currentTime;
    }

}