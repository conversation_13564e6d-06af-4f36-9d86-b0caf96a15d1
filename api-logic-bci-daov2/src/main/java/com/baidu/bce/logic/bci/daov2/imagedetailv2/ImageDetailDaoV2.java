package com.baidu.bce.logic.bci.daov2.imagedetailv2;

import com.baidu.bce.logic.bci.daov2.imagedetailv2.mapper.ImageDetailV2Mapper;
import com.baidu.bce.logic.bci.daov2.imagedetailv2.model.ImageDetailPO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ImageDetailDaoV2 {
    @Autowired
    private ImageDetailV2Mapper imageDetailV2Mapper;

    public List<ImageDetailPO> getImageDetailByOriginImageAddressAndTag(
        String originImageAddress, String originImageTag, String accountId) {
        return imageDetailV2Mapper.getImageDetailByOriginImageAddressAndTag(
            originImageAddress, originImageTag, accountId);
    }

    public List<ImageDetailPO> getImageDetailByOriginImageAddress(String originImageAddress, String accountId) {
        return imageDetailV2Mapper.getImageDetailByOriginImageAddress(originImageAddress, accountId);
    }

    public void insertImageDetailPO(ImageDetailPO imageDetailPO) throws DataAccessException {
        imageDetailV2Mapper.insertImageDetailPO(imageDetailPO);
    }

    public List<ImageDetailPO> getImageDetailByImageCacheTypeAndImageAddressTag(
        String imageAddress, String tag, String accountId) {
        return imageDetailV2Mapper.getImageDetailByImageCacheTypeAndImageAddressTag(
            imageAddress, tag, accountId);
    }

    public List<ImageDetailPO> getImageDetailByImageAddressTagOrderByIdDesc(
            String imageAddress, String tag, String accountId) {
        return imageDetailV2Mapper.getImageDetailByImageAddressTagOrderByIdDesc(
                imageAddress, tag, accountId);
    }

    public List<ImageDetailPO> getImageDetailsByImageCacheId(String imageCacheId) {
        return imageDetailV2Mapper.getImageDetailsByImageCacheId(imageCacheId);
    }

    public void updateImageDetail(ImageDetailPO imageDetailPO) {
        imageDetailV2Mapper.updateImageAccDetail(imageDetailPO);
    }

    public List<ImageDetailPO> getCcrImageDetailByOriginImageAddressAndTag(
        String originImageAddress, String originImageTag, String accountId) {
        return imageDetailV2Mapper.getCcrImageDetailByOriginImageAddressAndTag(
            originImageAddress, originImageTag, accountId);
    }

    public List<ImageDetailPO> getImageDetailsByImageCacheIdForQuery(String imageCacheId) {
        return imageDetailV2Mapper.getImageDetailsByImageCacheIdForQuery(imageCacheId);
    }
}