package com.baidu.bce.logic.bci.daov2.ccecluster;

import com.baidu.bce.logic.bci.daov2.ccecluster.mapper.CceClusterMapper;
import com.baidu.bce.logic.bci.daov2.ccecluster.model.CceCluster;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class CceClusterDao {

    @Autowired
    private CceClusterMapper cceClusterMapper;


    public void insert(CceCluster cceCluster) {
        cceClusterMapper.insert(cceCluster);
    }

    public void delete(String cceId) {
        cceClusterMapper.delete(cceId);
    }

    public CceCluster getCceClusterByCceId(String cceId) {
        return cceClusterMapper.getCceClusterByCceId(cceId);
    }
    public List<CceCluster> getDefaultCceClusters() {
        return cceClusterMapper.getDefaultCceClusters();
    }
    public List<CceCluster> getAllCceClusters() {
        return cceClusterMapper.getAllCceClusters();
    }
}