package com.baidu.bce.logic.bci.service.charge.service;

import com.baidu.bce.logic.bci.dao.chargerecord.PodChargeRecordDao;
import com.baidu.bce.logic.bci.dao.chargerecord.model.PodChargeRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Service
public class PodPushTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodPushTaskService.class);

    private static final int DELAY_MINUTE = -30;

    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd-HH-mm");

    private static final Timestamp INIT_LAST_CHARGE_TIME = new Timestamp(31536001000L);
    private static final long PUSH_OVER_TIME = 30 * 60;
    private static final long SEVEN_DAY = 15 * 24 * 60 * 60 * 1000L;

    @Autowired
    private PodChargeRecordDao podChargeRecordDao;

    public void deleteRecord() {
        podChargeRecordDao.deleteBefore(new Timestamp(new Date().getTime() - SEVEN_DAY));
    }

    public void createPushTask() {
        PodChargeRecord podChargeRecord = podChargeRecordDao.getNeedToPush();
        if (podChargeRecord == null ) {
            PodChargeRecord newTask;
            PodChargeRecord lastPodChargeRecord = podChargeRecordDao.getLastPodChargeRecord();
            if (lastPodChargeRecord == null) {
                newTask = new PodChargeRecord();
                newTask.setMsgId(getMsgId());
                newTask.setChargeTime(getChargeTime());
                newTask.setLastChargeTime(INIT_LAST_CHARGE_TIME);
            } else {
                newTask = createPushTask(lastPodChargeRecord.getChargeTime(), lastPodChargeRecord.getMsgId());
            }

            if (newTask != null) {
                podChargeRecordDao.insert(newTask);
            }
        } else if (new Date().getTime() - podChargeRecord.getChargeTime().getTime() > PUSH_OVER_TIME) {
            LOGGER.warn("Charge push over time");
        }
    }

    private PodChargeRecord createPushTask(Timestamp chargeTime, String msgId) {
        long interval = new Date().getTime() - chargeTime.getTime();
        int delta = (int) (interval / (1000 * 60));

        if (delta == 0) {
            return null;
        }

        Calendar newChargeTime = Calendar.getInstance();
        newChargeTime.setTime(chargeTime);
        newChargeTime.add(Calendar.MINUTE, delta);

        Date msgIdDate;
        try {
            msgIdDate = formatter.parse(msgId);
        } catch (ParseException e) {
            LOGGER.error("Parse msgId error");
            return null;
        }

        Calendar newMsgId = Calendar.getInstance();
        newMsgId.setTime(msgIdDate);
        newMsgId.add(Calendar.MINUTE, delta);

        PodChargeRecord podChargeRecord = new PodChargeRecord();
        podChargeRecord.setLastChargeTime(chargeTime);
        podChargeRecord.setChargeTime(new Timestamp(newChargeTime.getTime().getTime()));
        podChargeRecord.setMsgId(formatter.format(newMsgId.getTime()));

        return podChargeRecord;
    }

    private String getMsgId() {
        return formatter.format(new Date());
    }

    private Timestamp getChargeTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, DELAY_MINUTE);
        return new Timestamp(calendar.getTime().getTime());
    }
}
