package com.baidu.bce.logic.bci.service.model;

import com.baidu.bce.internalsdk.eipv2.model.EipForCreate;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class EipPurchaseRequest extends EipForCreate implements IOrderItem {
    private String serviceType = "EIP";


    @Override
    public String getServiceType() {
        return serviceType;
    }

    @Override
    public void setServiceType(String serviceType) {

    }
}
