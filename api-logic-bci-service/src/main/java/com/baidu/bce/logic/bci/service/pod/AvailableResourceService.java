package com.baidu.bce.logic.bci.service.pod;

import com.baidu.bce.internalsdk.bci.model.AvailableResourceRequest;
import com.baidu.bce.internalsdk.bci.model.AvailableResourceResponse;
import com.baidu.bce.internalsdk.bci.model.CreateResourceUnitRequest;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.common.CommonUtils;
import com.baidu.bce.logic.bci.service.common.service.BciAsyncService;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.model.AvailableResourceForm;
import com.baidu.bce.logic.bci.service.model.AvailableResourceVo;
import com.baidu.bce.logic.bci.service.model.ResourceUnitForm;
import com.baidu.bce.logic.bci.service.model.ResourceUnitVo;
import com.baidu.bce.logic.bci.service.util.PodConfiguration;
import com.baidu.bce.logic.bci.service.util.PodValidator;
import com.baidu.bce.logic.bci.service.util.Validator;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;


@Service
public class AvailableResourceService {

    public static final Logger LOGGER = LoggerFactory.getLogger(AvailableResourceService.class);

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    @Autowired
    private Validator validator;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private BciAsyncService bciAsyncService;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    protected RegionConfiguration regionConfiguration;


    public AvailableResourceVo queryAvailableResource(AvailableResourceForm request) {
        String accountId = LogicUserService.getAccountId();
        AvailableResourceRequest internalRequest = new AvailableResourceRequest();
        BeanUtils.copyProperties(request, internalRequest);
        AvailableResourceResponse response = logicPodClientFactory.createPodClientByAccountId(accountId)
                .queryBCIAvailableResource(internalRequest);

        AvailableResourceVo resourceVo = new AvailableResourceVo();
        List<AvailableResourceVo.Result> results = new LinkedList<>();
        for (AvailableResourceResponse.Result result : response.getResults()) {
            AvailableResourceVo.Result voResult = new AvailableResourceVo.Result();
            voResult.setCollectionAt(result.getCollectionAt());
            voResult.setUpdatedAt(result.getUpdatedAt());
            voResult.setResourceUnitId(result.getResourceUnitId());
            voResult.setAvailableNumber(result.getAvailableNumber());
            results.add(voResult);
        }
        resourceVo.setResults(results);
        return resourceVo;
    }

    public ResourceUnitVo createResourceUnit(ResourceUnitForm form) {
        String accountId = LogicUserService.getAccountId();
        LOGGER.debug("form: {}", form);

        // 校验 cpu memory 套餐白名单可以绕过
        if (commonUtils.checkWhiteList(LogicalConstant.VALIDATE_CPU_MEM, regionConfiguration.getCurrentRegion(),
                accountId)) {
            podValidator.validateCpuAndMemoryInWhiteList("", form.getCpu().floatValue(),
                    form.getMemory().floatValue());
        } else {
            podValidator.validateCpuAndMemory("", form.getCpu().floatValue(), form.getMemory().floatValue());
        }
        form.setMemory(form.getMemory() * 1024);

        // 获取用户的 physical zone
        String physicalZone = logicPodClientFactory.createExternalSubnetClient(accountId)
                .getPhysicalZoneBySubnetId(form.getSubnetId());

        // 获取用户的group
//        ResourceGroupRequest groupRequest = new ResourceGroupRequest();
//        groupRequest.setPhysicalZone(physicalZone);
//        groupRequest.setUserId(accountId);
//        ResourceGroupResponse groupResponse = logicPodClientFactory.createPodClient(accountId)
//                .queryResourceGroup(groupRequest);
//        if (StringUtils.isEmpty(groupResponse.getGroupId())) {
//            throw new PodExceptions.ResourceGroupException();
//        }

        CreateResourceUnitRequest internalRequest = new CreateResourceUnitRequest();
        CreateResourceUnitRequest.ResourceUnit resourceUnit = new CreateResourceUnitRequest.ResourceUnit();
        // resourceUnits
        resourceUnit.setAvailableZone(physicalZone);
        resourceUnit.setCpu(form.getCpu().intValue());
        resourceUnit.setMemory(form.getMemory().intValue());
        // BCI 目前没有用tag，可以不传机型，但是也保留配置项，后续使用
        if (StringUtils.isNotEmpty(podConfiguration.getPodInstanceTypes())) {
            resourceUnit.setRequiredTags(podConfiguration.getPodInstanceTypeList());
        }

        String unitId = "BCI-" + physicalZone + "-"
                + resourceUnit.getGroupId() + "-" + resourceUnit.getCpu().toString()
                + "-" + resourceUnit.getMemory().toString();

        if (StringUtils.isNotEmpty(podConfiguration.getPodInstanceTypes())) {
            unitId = unitId + "-" + podConfiguration.getPodInstanceTypes().replaceAll(",", "-");
        }
        resourceUnit.setId(unitId);

        internalRequest.setResourceUnit(resourceUnit);

        LOGGER.debug("internal request: {}", internalRequest);
        logicPodClientFactory.createPodClientByAccountId(accountId)
                .createResourceUnit(internalRequest);
        ResourceUnitVo result = new ResourceUnitVo();
        result.setResourceUnitId(resourceUnit.getId());
        return result;
    }
}
