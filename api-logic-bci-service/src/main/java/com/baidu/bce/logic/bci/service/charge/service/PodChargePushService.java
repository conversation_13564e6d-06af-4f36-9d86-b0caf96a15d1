package com.baidu.bce.logic.bci.service.charge.service;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.billing.proxy.model.v1.LegacyChargeDataRequest;
import com.baidu.bce.billing.proxy.model.v1.LegacyDimension;
import com.baidu.bce.billing.proxy.model.v1.LegacyFlavor;
import com.baidu.bce.billing.proxy.model.v1.LegacyFlavorSet;
import com.baidu.bce.billing.proxy.model.v1.LegacyMetricData;
import com.baidu.bce.billing.proxy.model.v1.LegacyStatisticValues;
import com.baidu.bce.billing.proxy.model.v1.LegacyUserChargeData;
import com.baidu.bce.billing.proxy.service.v1.LegacyProxyService;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.Account;
import com.baidu.bce.logic.bci.dao.chargerecord.PodChargeRecordDao;
import com.baidu.bce.logic.bci.dao.chargerecord.model.PodChargeRecord;
import com.baidu.bce.logic.bci.dao.chargestatus.PodChargeStatusDao;
import com.baidu.bce.logic.bci.dao.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.common.service.BciChargeAsyncService;
import com.baidu.bce.logic.bci.service.constant.ChargeStatus;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.interceptor.ResourceAccountConfig;
import com.baidu.bce.logic.bci.service.model.PodCDSVolumeType;
import com.baidu.bce.logic.bci.service.util.JsonUtil;
import com.baidu.bce.logic.bci.service.util.PodConfiguration;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class PodChargePushService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodChargePushService.class);

    @Autowired
    private PodChargeRecordDao podChargeRecordDao;

    @Autowired
    private PodChargeStatusDao podChargeStatusDao;

    @Autowired
    private PodDao podDao;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private BciChargeAsyncService bciChargeAsyncService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private LogicPodClientFactory bciClientFactory;

    @Autowired
    private ResourceAccountConfig resourceAccountConfig;

    @Autowired
    private PodConfiguration podConfiguration;

    @Value("${bcc.billing.batch.max.size:100}")
    private int batchMaxSize;

    private static final String INSTANCE_ID = "instanceId";
    private static final String REGION = "region";
    private static final String TIME_UNIT = "SECOND";
    private static final String METRIC_NAME = "CPC_flavor";

    private static final int RETRY_TIMES = 3;
    private static final int LOCK_OVER_TIME = 300;
    private static final Timestamp INIT_LAST_CHARGE_TIME = new Timestamp(31536001000L);

    public void pushChargeTask() {
        PodChargeRecord podChargeRecord = podChargeRecordDao.getNeedToPush();
        if (podChargeRecord == null) {
            return;
        }
        Boolean locked = false;
        try {
            String lockId = UUID.randomUUID().toString();
            locked = podChargeRecordDao.tryToLockOneLine(new Date(), lockId,
                    podChargeRecord.getId(), LOCK_OVER_TIME);
            if (!locked) {
                return;
            }
            boolean isSucc = pushCharge(podChargeRecord.getLastChargeTime(), podChargeRecord.getChargeTime(),
                    podChargeRecord.getMsgId());

            if (isSucc) {
                podChargeRecordDao.updateSucc(podChargeRecord.getId());
                locked = false;
            }
        } catch (Exception e) {
                LOGGER.warn("Push charge error, time : {}, exception: {}", podChargeRecord.getChargeTime(), e);
        } finally {
            if (locked) {
                int index = 0;
                Boolean unLock = false;
                while (index < RETRY_TIMES) {
                    try {
                        unLock = podChargeRecordDao.unLock(podChargeRecord.getId());
                        if (unLock) {
                            break;
                        } else {
                            Thread.sleep(3000);
                        }
                    } catch (Exception e) {
                        LOGGER.warn("Try unLock PodChargeRecord failed! retry : {}", index);
                    }
                    index ++;
                }
                if (!unLock) {
                    LOGGER.error("Try unLock PodChargeRecord failed!");
                }
            }
        }
    }

    private boolean pushCharge(Timestamp lastChargeTime, Timestamp chargeTime, String msgId) {
        Map<String, LinkedList<PodChargeStatus>> podChargeStatusMap =
                podChargeStatusDao.listByTime(lastChargeTime, chargeTime);
        long usage;
        Map<PodPO, Long> podUsage = new HashMap<>();
        for (String podUuid : podChargeStatusMap.keySet()) {
            PodPO podPO = podDao.getPodById(podUuid);
            if (podPO == null || StringUtils.isEmpty(podPO.getResourceUuid())) {
                continue;
            }
            usage = getUsageForPod(podChargeStatusMap.get(podUuid), lastChargeTime,
                    chargeTime, podPO);
            podUsage.put(podPO, usage);
        }

        List<PodPO> podPOS = podDao.listAllPods();
        for (PodPO podPO : podPOS) {
            if (podChargeStatusMap.keySet().contains(podPO.getPodUuid())
                    || StringUtils.isEmpty(podPO.getResourceUuid())) {
                continue;
            }
            if (PodConstants.NO_CHARGE.equalsIgnoreCase(ChargeStatus.getStatus(podPO.getStatus().toLowerCase()))) {
                usage = 0;
            } else {
                // 这里的问题是，推送任务的时间段是早于当前时间的，当前时间pod可能已经不计费了，但是之前是计费的，
                // 与之对应的，会有重复计费的情况，不过误差都在1分钟之内
                usage = chargeTime.getTime() -
                        (lastChargeTime.equals(INIT_LAST_CHARGE_TIME) ?
                                podPO.getCreatedTime() : lastChargeTime).getTime();
            }
            podUsage.put(podPO, usage);
        }
        List<LegacyUserChargeData> legacyUserChargeDataList = generateLegacyChargeDataRequest(
                podUsage, msgId, chargeTime);

        return charge(legacyUserChargeDataList);
    }

    // 根据 status 里的计费状态，掐头（计费status：去掉大于record.lastChargeTime的时间 ），
    // 去尾（不计费的status，去掉小于 record.chargeTime 的时间），
    // 计算出 record.lastChargeTime 至 record.chargeTime 时间段内的计费时间
    // 这里的计算有几个问题：1.如果有多个计费状态，那 noChargeTime 的计算方法，最终只会计算最后一个计费状态的时间；
    // 2.如果第一个chargeStatus 的时间since时间> lastChargeTime，这段时间的pod其实是pending的，没计算到；
    // 如果 < lastChargeTime, 则重复计算。
    private long getUsageForPod(LinkedList<PodChargeStatus> podChargeStatuses,
                                Timestamp lastChargeTime, Timestamp chargeTime, PodPO podPO) {
        if (CollectionUtils.isEmpty(podChargeStatuses)) {
            return 0;
        }
        lastChargeTime = lastChargeTime.equals(INIT_LAST_CHARGE_TIME) ? podPO.getCreatedTime() : lastChargeTime;
        long noChargeTime = 0;
        long previous = lastChargeTime.getTime();
        PodChargeStatus endPodChargeStatus = new PodChargeStatus();
        for (PodChargeStatus podChargeStatus : podChargeStatuses) {
            if (PodConstants.CHARGE.equalsIgnoreCase(podChargeStatus.getChargeState())) {
                noChargeTime += podChargeStatus.getCreatedTime().getTime() - previous;
            }
            previous = podChargeStatus.getCreatedTime().getTime();
            endPodChargeStatus = podChargeStatus;
        }
        if (PodConstants.NO_CHARGE.equalsIgnoreCase(endPodChargeStatus.getChargeState())) {
            noChargeTime += chargeTime.getTime() - previous;
        }

        return chargeTime.getTime() - lastChargeTime.getTime() - noChargeTime ;
    }

    private List<LegacyUserChargeData> generateLegacyChargeDataRequest(Map<PodPO, Long> podUsage,
                                                                       String msgId, Timestamp chargeTime) {
        List<LegacyUserChargeData> legacyUserChargeDataList = new ArrayList<>();

        // 获取所有资源账号的accountID
        Map<String, String> raMap = getRAIDMap();

        Map<String, List<PodPO>> userPodsMap = new HashMap<>();
        for (PodPO podPO : podUsage.keySet()) {
            String userID = podPO.getUserId();
            // 非用户计费的资源，需要根据订单的账号来推送计费信息
            if (!LogicalConstant.CHARGE_SOURCE_USER.equalsIgnoreCase(podPO.getChargeSource())) {
                if (!raMap.containsKey(podPO.getChargeSource())) {
                    LOGGER.error("push charge error: cannot find this resource account {}", podPO.getChargeSource());
                    break;
                }
                userID = raMap.get(podPO.getChargeSource());
            }

            if (!userPodsMap.containsKey(userID)) {
                userPodsMap.put(userID, new ArrayList<PodPO>());
            }
            userPodsMap.get(userID).add(podPO);
        }

        for (String accountId : userPodsMap.keySet()) {
            LegacyUserChargeData legacyUserChargeData = new LegacyUserChargeData();
            legacyUserChargeData.setScope("BCE_BCI");
            legacyUserChargeData.setMsgId(msgId + "-" + accountId);
            legacyUserChargeData.setUserId(accountId);

            List<LegacyMetricData> legacyMetricDataList = generateMetricDatas(userPodsMap.get(accountId),
                    chargeTime, podUsage);
            legacyUserChargeData.setMetricData(legacyMetricDataList);
            LOGGER.debug("metric data: {}", JsonUtil.toJSON(legacyUserChargeData));

            legacyUserChargeDataList.add(legacyUserChargeData);
        }
        return legacyUserChargeDataList;
    }

    private List<LegacyMetricData> generateMetricDatas(List<PodPO> podPOS, Timestamp chargeTime,
                                                       Map<PodPO, Long> podUsage) {
        List<LegacyMetricData> metricDataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(podPOS)) {
            return metricDataList;
        }

        for (PodPO podPO : podPOS) {
            LegacyMetricData legacyMetricData = new LegacyMetricData();

            LegacyDimension instanceId = new LegacyDimension();
            instanceId.setName(INSTANCE_ID);
            instanceId.setValue(podPO.getPodUuid());

            LegacyDimension region = new LegacyDimension();
            region.setName(REGION);
            region.setValue(regionConfiguration.getCurrentRegion());
            List<LegacyDimension> dimensions = Arrays.asList(instanceId, region);

            legacyMetricData.setDimensions(dimensions);
            legacyMetricData.setMetricName(METRIC_NAME);

            LegacyStatisticValues legacyStatisticValues = new LegacyStatisticValues();
            legacyStatisticValues.setSum(new BigDecimal(podUsage.get(podPO) / 1000));
            legacyStatisticValues.setUnit(TIME_UNIT);

            legacyMetricData.setStatisticValues(legacyStatisticValues);
            legacyMetricData.setTimestamp(chargeTime.getTime() / 1000);
            legacyMetricData.setTag(false);

            LegacyFlavorSet legacyFlavorSet = new LegacyFlavorSet();
            List<LegacyFlavor> flavors = new ArrayList<>();
            LegacyFlavor flavor = new LegacyFlavor();

            flavor.setName(LogicalConstant.CPU);
            flavor.setValue("1");
            flavor.setScale(new BigDecimal(String.valueOf(podPO.getvCpu())));
            flavors.add(flavor);

            flavor = new LegacyFlavor();
            flavor.setName(LogicalConstant.MEMORY);
            flavor.setValue("1");
            flavor.setScale(new BigDecimal(String.valueOf(podPO.getMemory())));
            flavors.add(flavor);

            // disk
            int diskSize = getEmptyDirScale(podPO.getPodVolumes());
            if (diskSize != 0) {
                flavor = new LegacyFlavor();
                flavor.setName(LogicalConstant.DISK);
                flavor.setValue("1");
                flavor.setScale(new BigDecimal(diskSize));
                flavors.add(flavor);
            }

            legacyFlavorSet.setFlavor(flavors);

            legacyMetricData.setFlavor(legacyFlavorSet);

            metricDataList.add(legacyMetricData);
        }

        return metricDataList;
    }

    private Boolean charge(List<LegacyUserChargeData> legacyUserChargeDataList) {
        if (CollectionUtils.isEmpty(legacyUserChargeDataList)) {
            return true;
        }
        int left = legacyUserChargeDataList.size();
        int batch = 0;
        List<LegacyChargeDataRequest> legacyChargeDataRequests = new ArrayList<>();
        while (left > batchMaxSize) {  // billing批量最大支持100
            LegacyChargeDataRequest legacyChargeDataRequest = new LegacyChargeDataRequest();
            legacyChargeDataRequest.setUserChargeDatas(
                    legacyUserChargeDataList.subList(batch * batchMaxSize, (batch + 1) * batchMaxSize));
            legacyChargeDataRequests.add(legacyChargeDataRequest);
            batch++;
            left -= batchMaxSize;
        }

        LegacyChargeDataRequest legacyChargeDataRequest = new LegacyChargeDataRequest();
        legacyChargeDataRequest.setUserChargeDatas(
                legacyUserChargeDataList.subList(batch * batchMaxSize, batch * batchMaxSize + left));
        legacyChargeDataRequests.add(legacyChargeDataRequest);

        try {
            asyncExecutorService.initAsyncWorkMap();

            for (LegacyChargeDataRequest request : legacyChargeDataRequests) {
                bciChargeAsyncService.bciCharge(request);
            }

            for (LegacyChargeDataRequest request : legacyChargeDataRequests) {
                LegacyProxyService.EmptyResponse response = (LegacyProxyService.EmptyResponse) asyncExecutorService.
                        getAsyncResult(WorkKeyUtil.genWorkKey("bciChargeAsync",
                                Arrays.asList((Object) request)));
                if (response == null) {
                    return false;
                }
            }
        } catch (Exception e) {
            LOGGER.error("sync work exception!");
            return false;
        } finally {
            asyncExecutorService.destroyAsyncWorkMap();
        }
        return true;
    }

    /**
     * getRAIDMap return resource accountID map :
     * cce -> 02fa0a988caf43a9a5c0270310347581,
     * bsc -> 2a504dd6a8344952b10835451d2d2ebb
     * @return
     */
    public Map<String, String> getRAIDMap() {
        Map<String, String> resourceAccountIDMap = new HashMap<>();

        IAMClient iamClient = bciClientFactory.createIAMClientWithConsoleToken();

        for (Map.Entry<String, String> entry : resourceAccountConfig.resourceAccountMapping().entrySet()) {
            Account account = new Account();
            try {
                account = iamClient.accountsGet(entry.getValue());
            } catch (BceException e) {
                LOGGER.debug("failed to get account {} : {}", entry.getValue(), e);
            } catch (Exception e) {
                LOGGER.debug("failed to get account {} : {}", entry.getValue(), e);
            }

            if (account == null) {
                break;
            }
            LOGGER.debug("resourceAccount : {}, name : {}", entry.getKey(), account.getName());
            if (account.getName().equalsIgnoreCase(entry.getValue())) {
                resourceAccountIDMap.put(entry.getKey().toLowerCase(), account.getDomainId());
            }
        }
        return resourceAccountIDMap;
    }

    private int getEmptyDirScale(String podVolumes) {
        int diskSize = 0;
        if (StringUtils.isNotEmpty(podVolumes)) {
            List<ServersResponse.ServerBciCds> volumeList = JsonUtil.toList(podVolumes,
                    ServersResponse.ServerBciCds.class);
            if (CollectionUtils.isEmpty(volumeList)) {
                return diskSize;
            }
            for (ServersResponse.ServerBciCds podVolume : volumeList) {
                if (PodCDSVolumeType.EMPTYDIR.getType().equalsIgnoreCase(podVolume.getType())) {
                    diskSize += Integer.parseInt(podVolume.getSize());
                }
                if (PodCDSVolumeType.ROOTFS.getType().equalsIgnoreCase(podVolume.getType())) {
                    diskSize += Integer.parseInt(podVolume.getSize());
                }
            }
        }

        diskSize = diskSize - podConfiguration.getFreeChargeSizeInGB();

        return diskSize > 0 ? diskSize : 0;
    }
}
