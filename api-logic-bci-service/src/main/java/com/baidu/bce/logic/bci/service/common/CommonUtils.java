package com.baidu.bce.logic.bci.service.common;

import com.baidu.bce.logic.bci.dao.container.ContainerDao;
import com.baidu.bce.logic.bci.dao.container.model.ContainerPO;
import com.baidu.bce.logic.bci.dao.imagecache.ImageCacheDao;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.model.PodListRequest;
import com.baidu.bce.logic.bci.service.util.JsonUtil;
import com.baidu.bce.logic.bci.service.util.permission.PermissionExceptionUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.utils.UUIDUtil;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import com.baidu.bce.user.settings.sdk.model.FeatureAclRequest;
import com.baidu.bce.user.settings.sdk.model.FeatureAclResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;


@Component
public class CommonUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonUtils.class);

    @Autowired
    private PodDao podDao;

    @Autowired
    private ContainerDao containerDao;

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    @Autowired
    private ImageCacheDao imageCacheDao;

    /**
     * 判断用户输入是否全部为短ID, 如是则将原ID SET 替换为对应的UUID SET
     *
     * @param instanceIdSet 原ID集合(若原ID全部为短ID,则执行此方法后会被全部替换为长ID)
     * @return
     */
    public void shortIdCheck(Set<String> instanceIdSet) {

        Set<String> idSetForCheck = new HashSet<>();
        Map<String, String> podMap = podDao.podIdMap(getAccountId());

        for (String originId : instanceIdSet) {
            if (podMap.containsKey(originId)) {
                idSetForCheck.add(podMap.get(originId));
            } else {
                LOGGER.debug("permission vertify deny! resouce {} not exist", originId);
                throw new PermissionExceptionUtil.PermissionDenyException();
            }
        }

        instanceIdSet.clear();
        instanceIdSet.addAll(idSetForCheck);
    }

    public String createExternalId(String idPrefix) {
        int retryCount = 0;
        while (retryCount < 10) {
            try {
                String shortId = UUIDUtil.generateShortUuid();
                String uuid = podDao.queryPodUuid(shortId);
                if (StringUtils.isEmpty(uuid)) {
                    return idPrefix + "-" + shortId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate external_id in database.");
    }

    public List<String> filterByTag(PodListRequest podListRequest) {

        List<String> podUuids = new ArrayList<>();
        if (podListRequest == null || !podListRequest.getFilterMap().containsKey("tag")) {
            return null;
        }

        String[] tagKeyValues = podListRequest.getFilterMap().get("tag").split("__");
        if (tagKeyValues.length < 1) {
            throw new CommonExceptions.RequestInvalidException("tag filter is invalid");
        }

        if (StringUtils.isBlank(tagKeyValues[0])) {
            return podUuids;
        }

        try {
            LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
            FullTagListRequest tagRequest = new FullTagListRequest();
            tagRequest.setServiceTypes(Arrays.asList(PodConstants.SERVICE_TYPE));
            tagRequest.setTagKey(tagKeyValues[0]);
            if (tagKeyValues.length < 2) {
                tagRequest.setTagValue("");
            } else {
                tagRequest.setTagValue(tagKeyValues[1]);
            }

            TagAssociationFulls tagFulls = tagClient.listFullTags(tagRequest);
            for (TagAssociationFull tagFull : tagFulls.getTagAssociationFulls()) {
                if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(tagFull.getServiceType())) {
                    podUuids.add(tagFull.getResourceUuid());
                }
            }
        } catch (Exception e) {
            // 降级tag服务
            LOGGER.error("filter by tag error: {}", e);
        }
        return podUuids;
    }


    @Transactional(rollbackFor = CommonExceptions.InternalServerErrorException.class)
    public List<String> savePodCreate2DB(List<PodPO> pods, List<ContainerPO> containers) {
        if (CollectionUtils.isEmpty(pods) || CollectionUtils.isEmpty(containers)) {
            LOGGER.error("savePodCreate2DB: pod or container is null");
            throw new CommonExceptions.InternalServerErrorException();
        }
        List<String> podIds = new ArrayList<>();
        for (PodPO podPO : pods) {
            podIds.add(podPO.getPodId());
        }
        try {
            podDao.batchInsertPods(pods);
            LOGGER.debug("[bce logical bci] Update containers :", JsonUtil.toJSON(containers));
            containerDao.batchInsert(containers);
        } catch (Exception e) {
            LOGGER.error("[bce logical bci] Update logical failed, operation is {}", "[create bci] ", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        return podIds;
    }

    public boolean checkWhiteList(String featureType, String region, String accountId) {
        UserSettingsClient client = logicPodClientFactory.createUserSettingsClient(accountId);
        FeatureAclRequest request = new FeatureAclRequest();
        request.setFeatureType(featureType);
        request.setAclType("AccountId");
        request.setAclName(accountId);
        request.setRegion(region);
        FeatureAclResponse response = client.isInFeatureAcl(request);
        return response.getIsExist();
    }


    public String createTaskId() {
        int retryCount = 0;
        while (retryCount < 10) {

            String id = UUID.randomUUID().toString();
            int count = imageCacheDao.getTaskById(id);
            if (count == 0) {
                return id;
            }
            retryCount++;
        }
        throw new DuplicateKeyException("Duplicate external_id in database.");
    }
}
