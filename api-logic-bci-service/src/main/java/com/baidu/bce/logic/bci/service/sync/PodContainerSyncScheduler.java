package com.baidu.bce.logic.bci.service.sync;

import com.baidu.bce.logic.bci.service.sync.service.PodContainerSyncService;
import com.baidu.bce.logic.bci.service.sync.service.SyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@EnableScheduling
@Configuration
@Profile("default")
public class PodContainerSyncScheduler extends SyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodContainerSyncScheduler.class);

    private static final int FIX_DELAY_ONE = 20000;

    @Autowired
    private PodContainerSyncService podContainerSyncService;

    // 同步容器状态， 使用max(updated_time) 作为since 获取全量的更新结果，把podInfo里的since设置为pod的updated_time.
    // 因为非pending的pod，其updated_time肯定是 nova返回的 since，所以取max(updated_time)没问题，不需要担心会有时间gap。
    @Scheduled(fixedDelay = FIX_DELAY_ONE)
    public void runScheduledTask() {
        podContainerSyncService.syncPodContainer();
    }

}
