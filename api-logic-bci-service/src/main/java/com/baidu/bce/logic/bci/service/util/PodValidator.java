package com.baidu.bce.logic.bci.service.util;

import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.BelongSameVpcRequest;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.constant.PodVolumeType;
import com.baidu.bce.logic.bci.service.constant.WhiteListKey;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.bci.service.exception.VpcExceptions;
import com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.bci.service.interceptor.ResourceAccountSetting;
import com.baidu.bce.logic.bci.service.model.BaseVolume;
import com.baidu.bce.logic.bci.service.model.BciQuota;
import com.baidu.bce.logic.bci.service.model.ConfigFile;
import com.baidu.bce.logic.bci.service.model.ContainerPurchase;
import com.baidu.bce.logic.bci.service.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.EmptyDir;
import com.baidu.bce.logic.bci.service.model.IOrderItem;
import com.baidu.bce.logic.bci.service.model.ImageRegistrySecret;
import com.baidu.bce.logic.bci.service.model.Nfs;
import com.baidu.bce.logic.bci.service.model.PodCDSVolumeType;
import com.baidu.bce.logic.bci.service.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.Volume;
import com.baidu.bce.logic.bci.service.model.VolumeMounts;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service
public class PodValidator {
    private static final Logger LOGGER = LoggerFactory.getLogger(PodValidator.class);

    public static final String PATTERN_NAME;
    public static final String SEARCH_VALUE;

    static {
        PATTERN_NAME = "^(?!^\\d)(?!^[-_/.])[\u4e00-\u9fa5a-zA-Z\\d-_/.]{1,256}$";
        SEARCH_VALUE = "[\u4e00-\u9fa5a-zA-Z\\d-_/.]{1,256}";
    }

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    @Autowired
    private PodConfiguration podConfiguration;

    @Value("${bce.enable.debug:false}")
    private boolean debug;

    public void validateCreateBCIServiceType(BaseCreateOrderRequestVo<IOrderItem> request) {
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : request.getItems()) {
            if (!"BCI".equalsIgnoreCase(item.getConfig().getServiceType())
                    && !ServiceType.EIP.name().equalsIgnoreCase(item.getConfig().getServiceType())) {
                throw new PodExceptions.RequestInvalidException(String.format("Not support serviceType:%s",
                        item.getConfig().getServiceType()));
            }
        }
    }


    private static void checkValue(String name, int value, int max) {
        if (value <= 0 || value > max) {
            String message =
                    name + " max value is " + max + ", " + "you set value is " + value + ", please check the parameter";
            throw new CommonExceptions.RequestInvalidException(message);
        }
    }

    public void validateAutoRenewTime(String renewTimeUnit, int renewTime) {
        if (renewTime < 0) {
            throw new PodExceptions.InvalidAutoRenewTimeException();
        } else if (renewTime == 0) {
            // 0表示不开通自动续费
            return;
        }
        if (PodConstants.RENEW_TIME_UNIT_MONTH.equalsIgnoreCase(renewTimeUnit)) {
            //
            checkValue("autoRenewTime", renewTime, PodConstants.RENEW_MAX_MONTH);
        } else if (PodConstants.RENEW_TIME_UNIT_YEAR.equalsIgnoreCase(renewTimeUnit)) {
            checkValue("autoRenewTime", renewTime, PodConstants.RENEW_MAX_YEAR);
        } else {
            throw new PodExceptions.InvalidAutoRenewTimeUnitException();
        }
    }

    public void validateEipBandwidthInMbps(EipPurchaseRequest eipPurchaseRequest) {
        int bandwidthInMbps = eipPurchaseRequest.getBandwidthInMbps();
        if (LogicalConstant.EipSubProductType.BAND_WIDTH.equalsIgnoreCase(eipPurchaseRequest.getSubProductType())
                && 200 < bandwidthInMbps) {
            throw new CommonExceptions.RequestInvalidException("invalidate bandwidth parameter.");
        }
        if (LogicalConstant.EipSubProductType.NETRAFFIC.equalsIgnoreCase(eipPurchaseRequest.getSubProductType())
                && 1000 < bandwidthInMbps) {
            throw new CommonExceptions.RequestInvalidException("invalidate bandwidth parameter.");
        }
    }

    public void validateEipBlackList(List<String> whiteList) {
        if (whiteList.contains(WhiteListKey.EIP_BLACK_LIST)) {
            throw new PodExceptions.EipOperationDeniedException();
        }
    }

    public void validateBciParameters(PodPurchaseRequest podPurchaseRequest, BciQuota bciQuota) {
        validateCreateBciParams(podPurchaseRequest);
        // 统一计费的资源不计入用户的quota，因为created pod 的数量不好统计，暂时不做quota限制。
        if (!ResourceAccountSetting.isUnifiedCharge()) {
            validateQuotaLimit(podPurchaseRequest, bciQuota);
        }
    }

    public void validateRepository(PodPurchaseRequest podPurchaseRequest) {
        if (podPurchaseRequest == null || podPurchaseRequest.getContainerPurchases() == null) {
            return;
        }
        for (ContainerPurchase container : podPurchaseRequest.getContainerPurchases()) {
            String imageAddress = container.getImageAddress();
            // 校验是否为合法地址
            if (!Pattern.matches(podConfiguration.getImageRepoPattern(), imageAddress)) {
                throw new PodExceptions.ImageAddressException(imageAddress);
            }
        }
    }

    public void validateImageRegistrySecret(PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(podPurchaseRequest.getImageRegistrySecrets())) {
            return;
        }
        String patternStr = "(http|https):\\/\\/([\\w.]+\\/?)\\S*";
        Pattern pattern = Pattern.compile(patternStr);
        Map<String, ImageRegistrySecret> imageRegistrySecretMap = new HashMap<>();
        for (ImageRegistrySecret registrySecret : podPurchaseRequest.getImageRegistrySecrets()) {
            if (!pattern.matcher(registrySecret.getServer()).matches()) {
                throw new PodExceptions.ImageRegistryException();
            }
            String address = registrySecret.getServer().replace("http://", "")
                    .replace("https://", "");
            if (address.endsWith("/")) {
                address = address.substring(0, address.lastIndexOf("/"));
            }
            imageRegistrySecretMap.put(address, registrySecret);
        }
        LOGGER.debug("ImageRegistrySecretMap adress : {}", imageRegistrySecretMap.keySet());

        boolean isImageRegistry = false;
        for (ContainerPurchase container : podPurchaseRequest.getContainerPurchases()) {
            String repository = container.getImageAddress();
            int sublen = repository.lastIndexOf("/");
            // docker hub 只传 image name，这里做下兼容
            if (sublen == -1) {
                continue;
            }
            repository = repository.substring(0, sublen);
            LOGGER.debug("Image repository : {}", repository);
            if (imageRegistrySecretMap.keySet().contains(repository)) {
                isImageRegistry = true;
                break;
            }
        }

        if (!isImageRegistry) {
            throw new PodExceptions.ImageRegistryException();
        }
    }

    private void validateCreateBciParams(PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(podPurchaseRequest.getContainerPurchases())) {
            throw new PodExceptions.RequestInvalidException();
        }
        if (StringUtils.isNotEmpty(podPurchaseRequest.getName()) &&
                !PodUtils.validaterName(podPurchaseRequest.getName())) {
            LOGGER.error("NameInvalidException,the pod name {} is invalidate", podPurchaseRequest.getName());
            throw new PodExceptions.NameInvalidException();
        }
        if (!Payment.isPostpay(podPurchaseRequest.getProductType())) {
            LOGGER.warn("validateProductType failed!");
            throw new CommonExceptions.RequestInvalidException();
        }

        Volume volume = podPurchaseRequest.getVolume();
        if (volume == null ){
            LOGGER.warn("validate volume failed!");
            throw new CommonExceptions.RequestInvalidException();
        }

        List<BaseVolume> volumes = new ArrayList<>();
        volumes.addAll(volume.getNfs());
        volumes.addAll(volume.getEmptyDir());
        volumes.addAll(volume.getConfigFile());
        Map<String, BaseVolume> volumeMap = new HashMap<>();
        for (BaseVolume baseVolume : volumes) {
            if (volumeMap.containsKey(baseVolume.getName())) {
                throw new PodExceptions.VolumeNameInvalidException(baseVolume.getName());
            }
            volumeMap.put(baseVolume.getName(), baseVolume);
        }

        // 校验 name
        int rootfsNum = 0;
        if (CollectionUtils.isNotEmpty(volume.getPodVolumes())) {
            // volume ids
            rootfsNum = 1;
            for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
                if (volumeMap.containsKey(podVolume.getName())) {
                    throw new PodExceptions.VolumeNameInvalidException(podVolume.getName());
                }
                volumeMap.put(podVolume.getName(), new BaseVolume());
            }
        }

        List<VolumeMounts> volumeMounts = new LinkedList<>();
        // 校验 volumeMounts 和 volumes：必须能互相对应
        for (ContainerPurchase container : podPurchaseRequest.getContainerPurchases()) {
            if (container != null && CollectionUtils.isNotEmpty(container.getVolumeMounts())) {
                volumeMounts.addAll(container.getVolumeMounts());
            }
        }

        Map<String, Boolean> mountHitVolume = new HashMap<>();
        for (VolumeMounts volumeMount : volumeMounts) {
            String mountName = volumeMount.getName();
            if (!volumeMap.containsKey(mountName)) {
                throw new PodExceptions.MountsNoVolumeException(mountName);
            }

            mountHitVolume.put(mountName, true);
        }

        // 按道理现在应该只有 rootfs；这里不校验用户传了rootfs mounts 的情况，其他地方会校验，所以不考虑 size == 0
        if (volumeMap.size() - mountHitVolume.size() > rootfsNum) {
            LOGGER.error("no mounts volume: {}", volumeMap.keySet().removeAll(mountHitVolume.keySet()));
            throw new PodExceptions.VolumeNoMountsException();
        }
    }

    private void validateQuotaLimit(PodPurchaseRequest podPurchaseRequest, BciQuota bciQuota) {
        if (bciQuota.getPodCreated() >= bciQuota.getPodTotal()) {
            throw new PodExceptions.ExceedLimitException();
        }
        Volume volume = podPurchaseRequest.getVolume();

        List<Nfs> nfs = volume.getNfs();
        List<EmptyDir> emptyDirs = volume.getEmptyDir();
        List<ConfigFile> configFile = volume.getConfigFile();

        if (nfs.size() > bciQuota.getNfsRatio()) {
            throw new PodExceptions.NfsQuotaExceededLimit();
        }
        if (emptyDirs.size() > bciQuota.getEmptyDirRatio()) {
            throw new PodExceptions.EmptyDirQuotaExceededLimit();
        }
        if (configFile.size() > bciQuota.getConfigFileRatio()) {
            throw new PodExceptions.ConfigFileQuotaExceededLimit();
        }

        Map<String, Integer> volumeMap = new HashMap<>();

        // 新配置和旧配置分开校验，不排除有用户两种方式都用，这种情况让用户改一下
        if (CollectionUtils.isNotEmpty(volume.getPodVolumes())) {
            for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
                String volumeType = podVolume.getType();

                if (!volumeMap.containsKey(volumeType)) {
                    volumeMap.put(volumeType, 1);
                    continue;
                }
                volumeMap.put(volumeType, volumeMap.get(volumeType) +1);
            }
        }

        String emptyDir = PodCDSVolumeType.EMPTYDIR.getType();
        String dataVolume = PodCDSVolumeType.DATA.getType();
        String rootfs = PodCDSVolumeType.ROOTFS.getType();

        // cds 和 本地盘不能混用
        if (CollectionUtils.isNotEmpty(emptyDirs) && volumeMap.containsKey(emptyDir)) {
            throw new PodExceptions.LocalDiskAndCDSMixed();
        }
        // emptyDir 在一起算
        if (volumeMap.containsKey(emptyDir) && (volumeMap.get(emptyDir) > bciQuota.getEmptyDirRatio())) {
            throw new PodExceptions.EmptyDirQuotaExceededLimit();
        }
        if (volumeMap.containsKey(dataVolume) && (volumeMap.get(dataVolume) > bciQuota.getDataVolumeQuota())) {
            throw new PodExceptions.DataVolumeQuotaExceededLimit();
        }
    }

    public void validateAndSetSubnetUuid(PodPurchaseRequest podPurchaseRequest, ZoneMapDetail zoneMapVo,
                                         boolean isCreateWithEip) {
        // 简化逻辑，子网和安全组，要不都传，要不都不传
        if (isDefaultSecurityGroupId(podPurchaseRequest.getSecurityGroupId())
                && StringUtils.isEmpty(podPurchaseRequest.getSubnetId())
                && StringUtils.isEmpty(podPurchaseRequest.getSubnetUuid())) {
            // 使用默认值
            podPurchaseRequest.setSubnetUuid(zoneMapVo.getSubnetUuid());
        } else if (!isDefaultSecurityGroupId(podPurchaseRequest.getSecurityGroupId())
                && (StringUtils.isNotEmpty(podPurchaseRequest.getSubnetId())
                || StringUtils.isNotEmpty(podPurchaseRequest.getSubnetUuid()))) {
            String subnetId = podPurchaseRequest.getSubnetId();
            if (StringUtils.isEmpty(subnetId)) {
                subnetId = podPurchaseRequest.getSubnetUuid();
            }
            SubnetVo subnetVo = getSubnet(subnetId);
            podPurchaseRequest.setSubnetUuid(subnetVo.getSubnetUuid());
            podPurchaseRequest.setSubnetId(subnetVo.getSubnetId());
            validateSubnetSecurityGroupInSameVpc(podPurchaseRequest.getSecurityGroupId(),
                    podPurchaseRequest.getSubnetUuid());
        } else {
            throw new CommonExceptions.RequestInvalidException("subnetId and securityGroupId should be added");
        }

        SubnetVo subnetVo = getSubnetWithIpUsage(podPurchaseRequest.getSubnetUuid());
        if (isCreateWithEip && SubnetVo.SubnetType.BCC_NAT.getId() == subnetVo.getSubnetType()) {
            throw new PodExceptions.RequestInvalidException("BCI with NAT subnet can not bind eip");
        }
        if (StringUtils.isNotEmpty(subnetVo.getAz())
                && !subnetVo.getAz().equalsIgnoreCase(zoneMapVo.getLogicalZone())) {
            throw new PodExceptions.RequestInvalidException("The subnet is not belong to the logical zone.");
        }

        // 子网内IP容量判断
        if (subnetVo.getTotalIps() >= 0 && subnetVo.getUsedIps() >= 0) {
            if (subnetVo.getTotalIps() - subnetVo.getUsedIps() < podPurchaseRequest.getPurchaseNum()) {
                throw new VpcExceptions.IpInSubnetNotEnoughExceptions();
            }
        }
    }

    public SubnetVo getSubnetWithIpUsage(String subnetUuid) {
        SubnetVo subnetVo = null;
        try {
            ExternalSubnetClient subnetClient = logicPodClientFactory.createExternalSubnetClient(getAccountId());
            subnetVo = subnetClient.findSubnetWithIpUsage(subnetUuid);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        if (subnetVo == null) {
            throw new CommonExceptions.ResourceNotExistException();
        }

        return subnetVo;
    }

    private boolean isDefaultSecurityGroupId(String sgId) {
        return StringUtils.isEmpty(sgId) || "default".equals(sgId);
    }

    public SubnetVo getSubnet(String subnetUuid) {
        SubnetVo subnetVo = null;
        try {
            ExternalSubnetClient subnetClient = logicPodClientFactory.createExternalSubnetClient(getAccountId());
            subnetVo = subnetClient.findBySubnetId(subnetUuid);
        } catch (Exception e) {
            LogicPodExceptionHandler.handle(e);
        }
        if (subnetVo == null) {
            throw new CommonExceptions.ResourceNotExistException();
        }

        return subnetVo;
    }

    public void validateSubnetSecurityGroupInSameVpc(String securityGroupId, String subnetId) {
        boolean same;
        BelongSameVpcRequest belongSameVpcRequest = new BelongSameVpcRequest();
        belongSameVpcRequest.setSecurityGroupId(securityGroupId);
        belongSameVpcRequest.setSubnetId(subnetId);
        try {
            same = logicPodClientFactory.
                    createExternalVpcClient(getAccountId()).validateBelongSameVpc(belongSameVpcRequest);
        } catch (Exception e) {
            LOGGER.error("validateBelongSameVpc error with belongSameVpcRequest = {}, error = {}",
                    belongSameVpcRequest, e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        if (!same) {
            throw new CommonExceptions.RequestInvalidException("securityGroup and subnet must be in same vpc");
        }
    }

    /**
     * 验证搜索的值是否合法
     *
     * @param value 搜索值
     * @return 返回true代表合法
     */
    public static boolean validateSearchId(String value) {
        Pattern pattern = Pattern.compile(SEARCH_VALUE);
        return StringUtils.isEmpty(value) || pattern.matcher(value).matches();
    }

    /**
     * 验证搜索的值是否合法
     *
     * @param filterMap 过滤条件
     * @return 返回true代表过滤条件合法
     */
    public static boolean validateSearchId(Map<String, String> filterMap) {
        for (Map.Entry<String, String> entry : filterMap.entrySet()) {
            if ("tag".equalsIgnoreCase(entry.getKey())) {
                continue;
            }
            if (!validateSearchId(entry.getValue())) {
                return false;
            }
        }
        return true;
    }

    public void validateCpuAndMemory(String name, Float cpu, Float memory) {
        if (podConfiguration.getContainerCpu() == null) {
            return;
        }
        LOGGER.debug("container cpu and memory setting, cpu:{}, minRatio:{}, maxRatio:{}, stepLength:{}",
                podConfiguration.getContainerCpu(), podConfiguration.getMinRatio(),
                podConfiguration.getMaxRatio(), podConfiguration.getStepLength());
        LOGGER.debug("cpu:{}", cpu.toString());
        if (!podConfiguration.getContainerCpu().contains(cpu.toString())) {
            throw new PodExceptions.ContainerCPUInvalidException(name);
        } else {
            Float ratio = memory / cpu;
            if (ratio < podConfiguration.getMinRatio()
                    || ratio > podConfiguration.getMaxRatio()
                    || memory % podConfiguration.getStepLength() != 0) {
                throw new PodExceptions.ContainerCPUMemoryRatioInvalidException(name);
            }
        }
    }

    public void validateCpuAndMemoryInWhiteList(String name, Float cpu, Float memory) {
        if (podConfiguration.getContainerCpu() == null) {
            return;
        }
        LOGGER.debug("container cpu and memory setting, cpu:{}, minRatio:{}, maxRatio:{}, stepLength:{}",
                podConfiguration.getContainerCpu(), podConfiguration.getMinRatio(),
                podConfiguration.getMaxRatio(), podConfiguration.getStepLength());
        LOGGER.debug("cpu:{}", cpu.toString());
        if (!podConfiguration.getContainerCpuInWhiteList().contains(cpu.toString())) {
            throw new PodExceptions.ContainerCPUInvalidException(name);
        } else {
            Float ratio = memory / cpu;
            if (ratio < podConfiguration.getMinRatio()
                    || ratio > podConfiguration.getMaxRatio()
                    || memory % podConfiguration.getStepLength() != 0) {
                throw new PodExceptions.ContainerCPUMemoryRatioInvalidException(name);
            }
        }
    }

    public void validateMountVolumeType(ContainerPurchase containerPurchase, Volume volume) {
        Map<String, String> volumeMap = new HashMap<>();
        for (Nfs nfs : volume.getNfs()) {
            if (volumeMap.containsKey(nfs.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            volumeMap.put(nfs.getName(), PodVolumeType.NFS.getType());
        }
        for (EmptyDir emptyDir : volume.getEmptyDir()) {
            if (volumeMap.containsKey(emptyDir.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            volumeMap.put(emptyDir.getName(), PodVolumeType.EMPTY_DIR.getType());
        }
        for (ConfigFile configFile : volume.getConfigFile()) {
            if (volumeMap.containsKey(configFile.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            volumeMap.put(configFile.getName(), PodVolumeType.CONFIG_FILE.getType());
        }

        if (CollectionUtils.isNotEmpty(volume.getPodVolumes())) {
            for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
                volumeMap.put(podVolume.getName(), podVolume.getType());
            }
        }

        List<VolumeMounts> volumeMountsList = containerPurchase.getVolumeMounts();
        Map<String, Boolean> mountPathValidator = new HashMap<>();
        for (VolumeMounts volumeMount : volumeMountsList) {
            String mountPath = volumeMount.getMountPath();
            String mountName = volumeMount.getName();
            String mountType = volumeMount.getType();
            if (volumeMap.get(mountName) == null) {
                throw new PodExceptions.PodContainerMountInvalidException();
            }
            if (StringUtils.isNotEmpty(mountType) &&
                    !mountType.equals(volumeMap.get(mountName))) {
                throw new PodExceptions.PodContainerMountTypeInvalidException();
            }
            if (mountPathValidator.containsKey(mountPath)) {
                throw new PodExceptions.MountPathDuplicated(mountPath);
            }
            mountPathValidator.put(mountPath, true);
            volumeMount.setType(volumeMap.get(mountName));
        }
    }
}
