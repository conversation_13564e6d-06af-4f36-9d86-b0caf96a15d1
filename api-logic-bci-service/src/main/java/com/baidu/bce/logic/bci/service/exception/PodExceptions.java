package com.baidu.bce.logic.bci.service.exception;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

public class PodExceptions extends CommonExceptions {

    public static class ResourceAndPONotEqualException extends BceException {
        public ResourceAndPONotEqualException() {
            super("order resource in billing and PO in logic db not equal", HttpStatus.ERROR_INPUT_INVALID,
                    "Order.ResourceAndPONotEqualException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * 无效的自动续费时长 400
     */
    public static class InvalidAutoRenewTimeException extends BceException {
        public InvalidAutoRenewTimeException() {
            super("Instance autoRenew time is invalid.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidAutoRenewTimeException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidZoneException extends BceException {
        public InvalidZoneException(String zone) {
            super("Resource in " + zone + " has sold out", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidZoneException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class JsonTransforException extends BceException {
        public JsonTransforException() {
            super("Order extra to json failed.", HttpStatus.ERROR_INPUT_INVALID, "InternalException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * eip超出额度 413
     */
    public static class EipQuotaExceedLimitException extends BceException {
        public EipQuotaExceedLimitException() {
            super("The number of eip will exceed the limit.",
                    HttpStatus.ERROR_TOO_MANY, "Instance.EipQuotaLimitExceeded");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * 无效的自动续费单元 400
     */
    public static class InvalidAutoRenewTimeUnitException extends BceException {
        public InvalidAutoRenewTimeUnitException() {
            super("Instance autoRenew time unit is invalid.", HttpStatus.ERROR_INPUT_INVALID,
                    "Instance.InvalidAutoRenewTimeUnitException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidateZoneException extends BceException {
        public InvalidateZoneException() {
            super("Invalidate zone name.", HttpStatus.ERROR_INPUT_INVALID, "Pod.InvalidateZoneException");
            setRequestId(LogicUserService.getRequestId());
        }
    }


    public static class PermissionDenyException extends BceException {
        public PermissionDenyException() {
            super("Permission denied. Please contact the administrator.", HttpStatus.ERROR_OPERATION_DENY);
        }
    }

    /**
     * cds磁盘超额 413
     */
    public static class ExceedLimitException extends BceException {
        public ExceedLimitException() {
            super("Number of pod exceeds limit.", HttpStatus.ERROR_TOO_MANY, "Pod.PodQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    /**
     * 容器组id不允许为空 400
     */
    public static class PodIdIsEmptyException extends BceException {
        public PodIdIsEmptyException() {
            super("Pod id must be provided.", HttpStatus.ERROR_INPUT_INVALID, "Pod.PodIdIsEmpty");
            setRequestId(LogicUserService.getRequestId());
        }
    }


    public static class EipOperationDeniedException extends BceException {
        public EipOperationDeniedException() {
            super("Account has no permission to bind EIP.", HttpStatus.ERROR_OPERATION_DENY,
                    "Pod.EipOperationDenied");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class NameInvalidException extends BceException {
        public NameInvalidException() {
            super("Pod name is invalid.", HttpStatus.ERROR_INPUT_INVALID, "Pod.NameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ImageRegistryException extends BceException {
        public ImageRegistryException() {
            super("Pod ImageRegistry is invalid.", HttpStatus.ERROR_INPUT_INVALID, "Pod.ImageRegistryInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ImageAddressException extends BceException {
        public ImageAddressException(String address) {
            super("Pod ImageAddress [" + address + "] is invalid. Only images from CCR/CCE/DockerHub are supported.",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.ImageAddressInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class NfsQuotaExceededLimit extends BceException {
        public NfsQuotaExceededLimit() {
            super("Nfs size exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.NfsQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class LocalDiskAndCDSMixed extends BceException {
        public LocalDiskAndCDSMixed() {
            super("Can not mix up local disk and CDS.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.LocalDiskAndCDSMixed");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class RootFSVolumeExceededLimit extends BceException {
        public RootFSVolumeExceededLimit() {
            super("There can only be one rootfs.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.RootFSVolumeExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class EmptyDirQuotaExceededLimit extends BceException {
        public EmptyDirQuotaExceededLimit() {
            super("EmptyDir volume exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.EmptyDirQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ConfigFileQuotaExceededLimit extends BceException {
        public ConfigFileQuotaExceededLimit() {
            super("PodConfigFile size exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.PodQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class DataVolumeQuotaExceededLimit extends BceException {
        public DataVolumeQuotaExceededLimit() {
            super("Data volume exceeds total capacity quota.", HttpStatus.ERROR_TOO_MANY,
                    "Pod.DataVolumeQuotaExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidVolumeSize extends BceException {
        public InvalidVolumeSize() {
            super("Invalid volume size.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidVolumeSize");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodCreatedFailed extends BceException {
        public PodCreatedFailed() {
            super("Fail to create pod", HttpStatus.ERROR_TOO_MANY,
                    "Pod.PodCreatedFailed");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeNameInvalidException extends BceException {
        public VolumeNameInvalidException(String name) {
            super("More than one volumes named " + name, HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeNameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class MountsNoVolumeException extends BceException {
        public MountsNoVolumeException(String name) {
            super("Mounts (" + name + ") does not match any volume", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeMountsInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeNoMountsException extends BceException {
        public VolumeNoMountsException() {
            super("Some volumes has no matched mount (no need for rootfs)", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.PodVolumesInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ResourceNotExistException extends BceException {
        public ResourceNotExistException() {
            super("The specified object is not found or resource do not exist.",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "NoSuchObject");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerCPUMemoryRatioInvalidException extends BceException {
        public ContainerCPUMemoryRatioInvalidException(String name) {
            super("Unsupported resource requirements for container " + name + ". Please consult the doc at " +
                    "https://cloud.baidu.com/doc/CCE/s/Jk9cxgekd#%E9%85%8D%E7%BD%AEcpu%E5%92%8Cmemory%E8%B5%84%E6%BA%90"
                    , HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerCPUMemoryRatioInvalidException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerCPUInvalidException extends BceException {
        public ContainerCPUInvalidException(String name) {
            super("Unsupported resource requirements for container " + name + ". Please consult the doc at " +
                    "https://cloud.baidu.com/doc/CCE/s/Jk9cxgekd#%E9%85%8D%E7%BD%AEcpu%E5%92%8Cmemory%E8%B5%84%E6%BA%90"
                    , HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerCPUInvalidException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodContainerMountTypeInvalidException extends BceException {
        public PodContainerMountTypeInvalidException() {
            super("Volume mount type invalid", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.PodContainerMountTypeInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PodContainerMountInvalidException extends BceException {
        public PodContainerMountInvalidException() {
            super("Volume mount name invalid", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.PodContainerMountNameInvalid");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class UserPriceConfigurationNotExist extends BceException {
        public UserPriceConfigurationNotExist() {
            super("User's price configuration for postpay does not exist.", HttpStatus.ERROR_INPUT_INVALID,
                    "Bbc.UserPriceConfigurationNotExist");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class OperationNotAvailable extends BceException {
        public OperationNotAvailable() {
            super("Operation not available. Please confirm your permission.", HttpStatus.ERROR_OPERATION_NOT_AVAILABLE);
        }
    }

    public static class GetServerFailed extends BceException {
        public GetServerFailed() {
            super("GetServer failed", 500);
        }
    }

    public static class CreateImageCacheFailed extends BceException {
        public CreateImageCacheFailed() {
            super("CreateImageCache failed", 500);
        }
    }

    public static class InvalidDiskSize extends BceException {
        public InvalidDiskSize() {
            super("Invalid disk size for resource unit.", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidDiskSize");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ResourceGroupException extends BceException {
        public ResourceGroupException() {
            super("resource group is empty.", HttpStatus.ERROR_COMPUTER,
                    "Pod.ResourceGroupException");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class LeakagePodInfoIsEmptyException extends BceException {
        public LeakagePodInfoIsEmptyException() {
            super("Pod info must be provided.", HttpStatus.ERROR_INPUT_INVALID, "Pod.LeakagePodInfoIsEmpty");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ContainerNumExceededLimit extends BceException {
        public ContainerNumExceededLimit(Integer number, Integer limit) {
            super("Container number: " + number + ", exceeds limit: " + limit, HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.ContainerNumExceededLimit");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeNotFound extends BceException {
        public VolumeNotFound() {
            super("Volume(s) not found", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeNotFound");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class UnsupportedCDSPayment extends BceException {
        public UnsupportedCDSPayment(String payment) {
            super("Unsupported data volume payment: " + payment, HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.UnsupportedCDSPayment");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeNotAvailable extends BceException {
        public VolumeNotAvailable() {
            super("Volume(s) not available", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.VolumeNotAvailable");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class InvalidVolumeSource extends BceException {
        public InvalidVolumeSource() {
            super("Only DATA volume can use existed cds", HttpStatus.ERROR_INPUT_INVALID,
                    "Pod.InvalidVolumeSource");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class VolumeInvalidZone extends BceException {
        public VolumeInvalidZone(String volumeZone, String podZone) {
            super("Volume(s) zone: " + volumeZone + " is different with pod zone: " + podZone,
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.VolumeInvalidZone");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class MountPathDuplicated extends BceException {
        public MountPathDuplicated(String mountPath) {
            super("Invalid mount path: " + mountPath + ", duplicated",
                    HttpStatus.ERROR_INPUT_INVALID, "Pod.MountPathDuplicated");
            setRequestId(LogicUserService.getRequestId());
        }
    }

}
