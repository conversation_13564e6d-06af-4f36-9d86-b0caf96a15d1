package com.baidu.bce.logic.bci.service.constant;

public enum BciStatus {

    PENDING("pending", "Pending"),
    RUNNING("running", "Running"),
    FAILED("failed", "Failed"),
    SUCCEED("succeeded", "Succeeded"),
    UNKNOWN("unknown", "Unknown"),
    DELETED("deleted", "Deleted"),
    CRASHED("crashed", "Crashed"),
    UNUSUAL_ORDER("unusualOrder", "UnusualOrder"),
    DELETING("deleting", "Deleted"),
    EXPIRED("expired", "Expired");

    private String name;
    private String status;


    BciStatus(String name, String status) {
        this.name = name;
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public String getStatus() {
        return status;
    }

    public static String getStatus(String name) {
        for (BciStatus bciStatus : values()) {
            if (bciStatus.getName().equalsIgnoreCase(name)) {
                return bciStatus.getStatus();
            }
        }
        return name;
    }
}
