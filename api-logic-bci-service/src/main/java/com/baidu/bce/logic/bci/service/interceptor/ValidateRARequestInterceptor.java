package com.baidu.bce.logic.bci.service.interceptor;

import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.Account;
import com.baidu.bce.internalsdk.iam.model.Accounts;
import com.baidu.bce.internalsdk.iam.model.SignatureValidator;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.exception.BackendExceptions;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.plat.webframework.iam.service.IAMService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;


// validate resource account request
public class ValidateRARequestInterceptor extends HandlerInterceptorAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(ValidateRARequestInterceptor.class);

    private static long expirationPeriodInSeconds = 30 * 60 * 1000;
    private static long fiveMinute = 5 * 60 * 1000;

    private LogicPodClientFactory bciClientFactory;

    private ResourceAccountConfig resourceAccountConfig;

    private IAMService iamService;

    public ValidateRARequestInterceptor(LogicPodClientFactory bciClientFactory, ResourceAccountConfig config,
                                        IAMService iamService) {
        this.bciClientFactory = bciClientFactory;
        this.resourceAccountConfig = config;
        this.iamService = iamService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {

        String application = request.getHeader(LogicalConstant.CHARGE_APPLICATION);
        String authorization = request.getHeader(LogicalConstant.CHARGE_AUTHORIZATION);

        if (StringUtils.isEmpty(application) && StringUtils.isEmpty(authorization)) {
            ResourceAccountSetting.setUnifiedCharge(Boolean.FALSE);
            return true;
        }

        if (StringUtils.isNotEmpty(application) && StringUtils.isEmpty(authorization)) {
            throw new BackendExceptions.MissChargeAuthorizationException();
        }

        if (StringUtils.isNotEmpty(authorization) && StringUtils.isEmpty(application)) {
            throw new BackendExceptions.MissApplicationException();
        }

        // 验签
        String accountId = getResourceAccountID(request, authorization);

        IAMClient iamClient = bciClientFactory.createIAMClientWithConsoleToken();
        Accounts accounts = iamClient.accountsGetByDomainId(accountId);
        if (accounts == null || CollectionUtils.isEmpty(accounts.getList())) {
            throw new BackendExceptions.ResourceAccountException();
        }

        boolean validated = false;
        for (Account account : accounts.getList()) {
            LOGGER.debug("application : {}, name : {}", application, account.getName());
            if (account.getName().equalsIgnoreCase(
                    resourceAccountConfig.resourceAccountMapping().get(application.toLowerCase()))) {
                validated = true;
                break;
            }
        }

        if (!validated) {
            throw new BackendExceptions.ResourceAccountException();
        }

        /**
         * resourceAccountId放入AccountSetting，后续要用直接取
         */
        ResourceAccountSetting.setApplication(application);
        ResourceAccountSetting.setResourceAccountId(accountId);
        ResourceAccountSetting.setUnifiedCharge(true);
        LOGGER.debug("ResourceSetting applicatiopn : {}, accountID: {}, charge: {}",
                ResourceAccountSetting.getApplication(), ResourceAccountSetting.getResourceAccountId(),
                ResourceAccountSetting.isUnifiedCharge());
        return true;
    }

    private String getResourceAccountID(HttpServletRequest request, String authorization) {
        Token userToken;
        try {
            if (StringUtils.isNotBlank(authorization)) {
                userToken = iamService.tokensGetByAuthorization(getSignatureValidator(request));
                String userTokenStr = new ObjectMapper().writeValueAsString(userToken);
                LOGGER.debug("signature validated, get user token: {}", userTokenStr);
            } else {
                LOGGER.debug("can't find authentication info");
                throw new BceException("Authentication failed", 403, "AccessDeny");
            }
        } catch (Exception ex) {
            LOGGER.debug("Authentication failed", ex);
            throw new BceException("Authentication failed", 403, "AccessDeny");
        }
        if (null == userToken || StringUtils.isEmpty(userToken.getAccountId())) {
            LOGGER.debug("UserToken / accountID is null");
            throw new BceException("Authentication failed", 403, "AccessDeny");
        }
        return userToken.getAccountId();
    }

    private SignatureValidator getSignatureValidator(HttpServletRequest request)
            throws UnsupportedEncodingException {

        String authorization = request.getHeader(LogicalConstant.CHARGE_AUTHORIZATION);
        String method = request.getMethod();
        String uri = URLDecoder.decode(request.getRequestURI(), "UTF-8");

        Map<String, String> signatureHeaders = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String header = headerNames.nextElement();
            signatureHeaders.put(header, request.getHeader(header));
//            if (header.equalsIgnoreCase(SignatureValidator.SECURITY_TOKEN_HEADER)) {
//                securityToken = request.getHeader(header);
//            }
        }

        Map<String, Object> parameters = new HashMap<>();
        for (Map.Entry<String, String[]> entry : request.getParameterMap().entrySet()) {
            if (entry.getValue().length == 1) {
                parameters.put(entry.getKey(), entry.getValue()[0]);
            } else {
                parameters.put(entry.getKey(), Arrays.asList(entry.getValue()));
            }
//            if (entry.getKey().equalsIgnoreCase(SignatureValidator.SECURITY_TOKEN_QUERYSTRING)) {
//                securityToken = entry.getValue()[0];
//            }
        }

        return new SignatureValidator(authorization, method, uri, signatureHeaders, parameters, null);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        LOGGER.info("Remove ResourceAccountSetting afterCompletion");
        ResourceAccountSetting.clear();
    }
}
