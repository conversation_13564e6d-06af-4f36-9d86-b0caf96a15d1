package com.baidu.bce.logic.bci.service.constant;

public class PodConstants {

    public static final String FROM_API = "api";
    public static final String FROM_CONSOLE = "console";

    public static final String RENEW_TIME_UNIT_MONTH = "month";
    public static final String RENEW_TIME_UNIT_YEAR = "year";
    public static final int RENEW_MAX_MONTH = 9;
    public static final int RENEW_MAX_YEAR = 3;



    public static final String NFS = "nfs";
    public static final String EMPTYDIR = "empty_dir";
    public static final String CONFIGFILE = "config_file";

    public static final String SERVICE_TYPE = "BCI";

    public static final String WEBSHELL = "webshell";

    public static final String CHAREG_SUCC = "succ";

    public static final String CHARGE = "charge";
    public static final String NO_CHARGE = "noCharge";

    public static final String DELETED = "deleted";

    /**
     * 普1  kvm
     * 普2  HM-CPU-v4_e5_2680
     * 普3  HM-CPU-Gold_6148
     * 普4  HM-CPU-Gold_6271
     */

    public static final String INSTANCE_TYPE_G1 = "kvm";
    public static final String INSTANCE_TYPE_G2 = "HM-CPU-v4_e5_2680";
    public static final String INSTANCE_TYPE_G3 = "HM-CPU-Gold_6148";
    public static final String INSTANCE_TYPE_G4 = "HM-CPU-Gold_6271";
}
