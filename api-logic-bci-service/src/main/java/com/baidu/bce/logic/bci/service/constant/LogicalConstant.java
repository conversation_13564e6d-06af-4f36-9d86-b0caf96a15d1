package com.baidu.bce.logic.bci.service.constant;

import java.util.HashMap;
import java.util.Map;

public class LogicalConstant {

    public static final String PHYSICAL_ZONE = "physicalZone";
    public static final String LOGICAL_ZONE = "logicalZone";

    public static final String PRODUCT_TYPE_PREPAY = "prepay";
    public static final String PRODUCT_TYPE_POSTPAY = "postpay";

    public static final String API_POD_PREFIX = "p";
    public static final String CONTAINER_GROUP = "pod";

    public static final String SUBNET_ID = "subnetId";
    public static final String CPU = "CpuRunTime";
    public static final String MEMORY = "RamRunTime";
    public static final String DISK = "EmptyDir";

    public static final String IP_VERSION_4 = "4";
    public static final String IP_VERSION_6 = "6";

    public static final int DIVIDE_SCALE = 7;

    public static final String DEFAULT = "default";
    public static final String BILLING_SERVICE_NAME = "billing";

    public static final Map<String, String> POD_STATUS_MAP = new HashMap<>();
    public static final Map<String, String> POD_RESTART_POLICY_MAP = new HashMap<>();

    public static final String RESOURCE_ACCOUNT_ID = "resource-account-id";
    public static final String CHARGE_APPLICATION = "charge-application";
    public static final String RESOURCE_ACCOUNT_ACCESSKEY = "resource-account-accesskey";
    public static final String CHARGE_AUTHORIZATION = "charge-authorization";
    public static final String CHARGE_SOURCE_USER = "user";
    public static final String CHARGE_SOURCE_BSC = "bsc";

    public static final String LABEL_ACCOUNT_ID = "AccountID";
    public static final String LABEL_POD_ID = "podId";
    public static final String LABEL_ORDER_ID = "orderId";
    public static final String LABEL_INSTANCE_TYPE = "instanceType";

    // white list feature type
    public static final String VALIDATE_CPU_MEM = "validateCPUMemory";
    public static final String ROUND_UP_CPU_MEM = "roundUpCPUMemory";


    static {
        POD_STATUS_MAP.put(BciStatus.PENDING.getStatus().toUpperCase(), "创建中");
        POD_STATUS_MAP.put(BciStatus.FAILED.getStatus().toUpperCase(), "失败");
        POD_STATUS_MAP.put(BciStatus.RUNNING.getStatus().toUpperCase(), "运行中");
        POD_STATUS_MAP.put(BciStatus.SUCCEED.getStatus().toUpperCase(), "成功");
        POD_STATUS_MAP.put(BciStatus.UNKNOWN.getStatus().toUpperCase(), "未知");

        POD_RESTART_POLICY_MAP.put("ALWAYS", "总是重启");
        POD_RESTART_POLICY_MAP.put("ONFAILURE", "失败重启");
        POD_RESTART_POLICY_MAP.put("NEVER", "从不重启");
    }

    public static class EipSubProductType {
        // console
        public static final String BAND_WIDTH = "bandwidth";
        public static final String NETRAFFIC = "netraffic";

        // BANDWIDTH_PREPAID：预付费按带宽结算
        // TRAFFIC_POSTPAID_BY_HOUR：流量按小时后付费
        // BANDWIDTH_POSTPAID_BY_HOUR：带宽按小时后付费
        public static final String BANDWIDTH_PREPAID = "BANDWIDTH_PREPAID";
        public static final String TRAFFIC_POSTPAID_BY_HOUR = "TRAFFIC_POSTPAID_BY_HOUR";
        public static final String BANDWIDTH_POSTPAID_BY_HOUR = "BANDWIDTH_POSTPAID_BY_HOUR";
    }
}
