package com.baidu.bce.logic.bci.service.sync.service;

import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeRollbackDbRequest;
import com.baidu.bce.internalsdk.bci.model.PodStatus;
import com.baidu.bce.internalsdk.bci.model.PodStatusResponse;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.bci.model.TransactionDetailResponse;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.BindShortIdRequest;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceIds;
import com.baidu.bce.internalsdk.order.model.ResourceInstanceIdentity;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.common.service.LogicalResourceService;
import com.baidu.bce.logic.bci.service.constant.BciStatus;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.model.BciOrderExtra;
import com.baidu.bce.logic.bci.service.model.Volume;
import com.baidu.bce.logic.bci.service.util.JsonUtil;
import com.baidu.bce.logic.bci.service.util.PodConfiguration;
import com.baidu.bce.logic.bci.service.util.PodUtils;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class PodInBuildSyncService extends SyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodInBuildSyncService.class);

    private static final int RETRY_NUM = 3;

    private static final String TRANSACTION_STATUS_SUCCESS = "succ";
    private static final String TRANSACTION_STATUS_ERROR = "error";
    private static final String TRANSACTION_STATUS_OPERATING = "operating";
    private static final String TRANSACTION_STATUS_DELETED = "deleted";
    private static final String TRANSACTION_STATUS_UNKNOWN = "unknown";

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    @Qualifier("theadPoolTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private LogicalResourceService logicalResourceService;

    private static final ConcurrentHashMap<String, Boolean> managePod = new ConcurrentHashMap<>();

    public void syncPodInBuild() {
        Map<String, List<PodPO>> pendingPodMap = getInSpecifiedStatusPodsMap(BciStatus.PENDING.getStatus());
        if (pendingPodMap != null && !pendingPodMap.isEmpty()) {
            for (String orderId : pendingPodMap.keySet()) {
                threadPoolTaskExecutor.execute(new SyncOrder(orderId, pendingPodMap.get(orderId)));
            }
        }
    }

    protected static boolean checkAndAddPod(String orderID) {
        if (managePod.containsKey(orderID)) {
            return false;
        } else {
            managePod.put(orderID, true);
        }
        return true;
    }

    protected static void removePod(String orderID) {
        if (managePod.containsKey(orderID)) {
            managePod.remove(orderID);
        }
    }

    private Map<String, List<PodPO>> getInSpecifiedStatusPodsMap(String status) {
        List<PodPO> pods = podDao.listByStatus(status);
        if (CollectionUtils.isEmpty(pods)) {
            return null;
        }
        Map<String, List<PodPO>> result = new HashMap<>();
        for (PodPO podPO : pods) {
            // resourceID不为空，说明订单已经成功了，跳过
            if (StringUtils.isNotEmpty(podPO.getResourceUuid())) {
                continue;
            }
            if (!result.containsKey(podPO.getOrderId())) {
                result.put(podPO.getOrderId(), new ArrayList<PodPO>());
            }
            result.get(podPO.getOrderId()).add(podPO);
        }
        return result;
    }

    class SyncOrder implements Runnable {
        private String orderId;
        private List<PodPO> podPOList;

        public SyncOrder(String orderId, List<PodPO> podPOList) {
            this.orderId = orderId;
            this.podPOList = podPOList;
        }

        @Override
        public void run() {
            BceInternalRequest.setThreadRequestId(PodUtils.createLongUuid());
            synchronizeBuildStatusPodData(orderId, podPOList);
        }

        private void synchronizeBuildStatusPodData(String orderId, List<PodPO> podPOS) {
            OrderClient orderClient = logicPodClientFactory.createOrderClient();
            ResourceClient resourceClient = logicPodClientFactory.createResourceClient();
            if (CollectionUtils.isEmpty(podPOS)) {
                LOGGER.debug("synchronizeBuildStatusPodData empty pod list");
                return;
            }
            String accountID = podPOS.get(0).getUserId();
            Order bciOrder;
            try {
                if (StringUtils.isEmpty(orderId)) {
                    // orderID 是空
                    LOGGER.debug("orderID is empty");
                    return;
                }
                bciOrder = orderClient.get(orderId);
                if (bciOrder == null) {
                    LOGGER.error("sync order error: Query order return null, order id is {}", orderId);
                    return;
                }
            } catch (Exception e) {
                LOGGER.error("sync order error, order {}, exception: {}", orderId, e);
                return;
            }

            OrderStatus orderStatus = bciOrder.getStatus();
            LOGGER.error("order {} detail is {}", orderId, JsonUtil.toJSON(bciOrder));
            // 创建失败，删除本地资源：将所有订单id为orderId的资源都删除，包括pod和container
            if (orderStatus == OrderStatus.CREATE_FAILED || orderStatus == OrderStatus.REFUND_SUCC
                    || orderStatus == OrderStatus.REFUND_FAILED || orderStatus == OrderStatus.EXPIRED
                    || orderStatus == OrderStatus.CANCELLED) {
                LOGGER.error("order {} status is {}", orderId, orderStatus);
                deletePodAndContainers(accountID, orderId);
            } else {
                LOGGER.error("order {} is normal, status is {}", orderId, orderStatus);
            }

            if (orderStatus == OrderStatus.CREATED) {
                try {
                    updatePodStatus(bciOrder, resourceClient, podPOS);
                    LOGGER.debug("BCI_LIFECYCLE_SYNC_ORDER_CREATED order is created, order:{}", orderId);
                } catch (Exception e) {
                    LOGGER.error("update status failed after query order, order: {}, exception is {}",
                            bciOrder.getId(), e);

                    // 如果订单一直同步不成功，这里会根据创建时间进行超时处理
                    long current = System.currentTimeMillis();
                    if ((current - podPOS.get(0).getCreatedTime().getTime()) >
                            podConfiguration.getOrderReadyTimeout() * 1000L) {
                        // 设置为 unusualOrder
                        for (PodPO pod : podPOS) {
                            pod.setStatus(BciStatus.UNUSUAL_ORDER.getName());
                        }

                        // 订单更新失败，且超时，更新 pod status 为 failed
                        podDao.batchUpdateStatus(podPOS);
                    }
                }
            }
        }

        private void deletePodAndContainers(String accountId, String orderId) {
            try {
                PodClient podClient = logicPodClientFactory.createPodClient(accountId);
                BCCClient bccClient = logicPodClientFactory.createBCCClient(accountId);
                try {
                    TransactionDetailResponse response = podClient.showTransaction(orderId);
                    // 如果是返回 null ，抛异常，避免漏删
                    LOGGER.debug("BCI_LIFECYCLE_ROLLBACK_ORDER rollback transaction {}, status : {}",
                            orderId, response.getStatus());

                    if (response.getStatus().equalsIgnoreCase(TRANSACTION_STATUS_OPERATING) ||
                            response.getStatus().equalsIgnoreCase(TRANSACTION_STATUS_UNKNOWN)) {
                        return;
                    }

                    if (response.getStatus().equalsIgnoreCase(TRANSACTION_STATUS_SUCCESS) ||
                            response.getStatus().equalsIgnoreCase(TRANSACTION_STATUS_ERROR)) {
                        deletePodFromNova(accountId, response);
                    } else if (!response.getStatus().equalsIgnoreCase(TRANSACTION_STATUS_DELETED)) {
                        // 不在以上五种状态之中，直接返回
                        return;
                    }
                } catch (BceInternalResponseException ex) {
                    LOGGER.debug("Query pod create status, orderId: {}, exception: {}", orderId, ex);
                    if (ex.getHttpStatus() == 404 && ex.getCode().equalsIgnoreCase("NoSuchObject")) {
                        LOGGER.debug("cannot found this transaction {}", orderId);
                    } else {
                        throw ex;
                    }
                }

                List<PodPO> podPOS = podDao.listByOrderId(accountId, orderId);
                for (PodPO podPO : podPOS) {
                    // 回滚volume attach状态
                    bccClient.rollbackVolume(getVolumeRollbackRequest(podPO));
                    podDao.deletePod(accountId, podPO.getPodId());
                    containerDao.deleteContainers(accountId, podPO.getPodId());
                }
            } catch (Exception e) {
                LOGGER.debug("Rollback Pod error, exception: {}", e);
            }
        }

        private void updatePodStatus(Order order, ResourceClient resourceClient,
                                     List<PodPO> podPOS) {
            if (CollectionUtils.isEmpty(podPOS)) {
                LOGGER.debug("updatePodStatus empty pod list");
                return;
            }
            String accountID = podPOS.get(0).getUserId();

            List<String> resourceIds = order.getResourceIds();
            if (resourceIds == null || resourceIds.isEmpty()) {
                return;
            }
            List<Resource> podResource = getPodResources(resourceIds, resourceClient);

            Map<String, ServersResponse> podMap = getPodUuidMap(accountID, order.getUuid());
            List<BindShortIdRequest.Item> items = Lists.newArrayList();

            // 使用 updated_time 作为since，由于入库的 updated_time == created_time , get(0) ： 同订单下的pod时间一样，
            // 跟nova确认过，since接口跟account信息无关，所以这里用order的accountID，没有问题
            // ContainerManager -> nova transaction success -> orderClient 的订单状态为CREATED,
            // 所以这里直接更新pod为running，不需要再请求 since 接口。把 syncPod 方法里的计费入库提到这里做。

            // Map<String, PodStatus> podStatusMap = queryPodStatus(order.getAccountId(), podPOS.get(0).getUpdatedTime());
            for (int i = 0, size = podResource.size(); i < size; i++) {
                Resource resource = podResource.get(i);
                ServersResponse podDetail = podMap.get(resource.getName());

                podPOS.get(i).setPodUuid(podDetail.getId());
                podPOS.get(i).setInternalIp(podDetail.getFixip());
                podPOS.get(i).setResourceUuid(resourceIds.get(i));
                podPOS.get(i).setPodVolumes(JsonUtil.toJSON(podDetail.getBciCds()));

                chargeStatus(podPOS.get(i));

                // 为了避免类似 主从同步 导致的 pod 状态被重置的问题，必须在这里 更新 pod 为 running. 这里状态需要在charge之后更新
                podPOS.get(i).setStatus(BciStatus.getStatus("running"));
                // 因为 since 不再依赖 pod 的 updated_time 字段，这里就不更新

//            syncPod(podStatusMap.get(podDetail.getId()), podPOS.get(i));
//            podStatusMap.remove(podDetail.getId());

                items.add(BindShortIdRequest.Item.builder()
                        .resource(ResourceInstanceIdentity.builder()
                                .region(regionConfiguration.getCurrentRegion())
                                .serviceType("BCI")
                                .name(podDetail.getId())
                                .build())
                        .shortId(podPOS.get(i).getPodId())
                        .build());
            }
            bindShotId(BindShortIdRequest.builder().items(items).build());
            assignTagAfterCreated(podPOS, order);
            podDao.batchUpdateStatus(podPOS);

            // 上面的是把订单里的pod同步，since接口返回的是全量数据，剩余的pod也要更新。不然后面container用max(updated_time)会漏掉
            // 这里的全量pod更新，影响性能，注释掉，container 状态由syncPodContainer保证。
            //        if (podStatusMap.size() > 0) {  //  更新状态发生变化的POD
            //            syncPod(new ArrayList<>(podStatusMap.values()));
            //        }
        }

        private Map<String, ServersResponse> getPodUuidMap(String accountId, String orderId) {
            TransactionDetailResponse transaction = queryCreateServerProcessByTransactionId(accountId, orderId);
            Map<String, ServersResponse> result = new HashMap<>();
            for (ServersResponse pod : transaction.getLstServer()) {
                result.put(pod.getId(), pod);
            }
            return result;
        }

        private void bindShotId(BindShortIdRequest request) {
            int count = RETRY_NUM;
            while (count-- > 0) {
                try {
                    logicPodClientFactory.createResourceClientV2().bindShortId(request);
                    break;
                } catch (BceInternalResponseException ex) {
                    LOGGER.warn("bind Order Resource error, retry {},exception is {}", count, ex);
                }
            }
        }

        private void assignTagAfterCreated(List<PodPO> podPOs, Order order) {
            try {
                BciOrderExtra orderExtra = PodUtils.getOrderExtra(
                        PodUtils.getExtraByServiceType(order, PodConstants.SERVICE_TYPE));
                List<Tag> tags = orderExtra.getTags();
                if (CollectionUtils.isNotEmpty(tags)) {
                    assignTag(podPOs, tags, podPOs.get(0).getUserId());
                }
            } catch (Exception e) {
                LOGGER.error("assignTagAfterCreated error={}", e);
            }
        }

        /**
         * 创建时统一加标签
         *
         * @param podPOS pods
         * @param tags   标签
         */
        private void assignTag(List<PodPO> podPOS, List<Tag> tags, String accountId) {
            List<AssignResource> assignResources = new ArrayList<>();
            for (PodPO podPO : podPOS) {
                AssignResource bccAssignResource = new AssignResource();
                bccAssignResource.setRegion(regionConfiguration.getCurrentRegion());
                bccAssignResource.setResourceId(podPO.getPodId());
                bccAssignResource.setResourceUuid(podPO.getPodUuid());
                bccAssignResource.setServiceType(PodConstants.SERVICE_TYPE);
                bccAssignResource.setTags(tags);
                assignResources.add(bccAssignResource);
            }

            try {
                LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(accountId);
                CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                createAndAssignTagRequest.setResources(assignResources);
                tagClient.createAndAssignTag(createAndAssignTagRequest);
            } catch (BceException bce) {
                throw new BceInternalResponseException(bce.getMessage(), bce.getHttpStatus(), bce.getCode());
            }
        }

        private TransactionDetailResponse queryCreateServerProcessByTransactionId(String accountId, String orderId) {
            int count = RETRY_NUM;
            PodClient podClient = logicPodClientFactory.createPodClient(accountId);
            TransactionDetailResponse transactionDetail = null;
            while (count-- > 0) {
                try {
                    transactionDetail = podClient.showTransaction(orderId);
                    if (transactionDetail != null) {
                        break;
                    }
                } catch (Exception ex) {
                    LOGGER.error("Query pod create status, retry {}, orderId: {}, exception: {}", count, orderId, ex);
                }
            }
            return transactionDetail;
        }

        private Map<String, PodStatus> queryPodStatus(String accountId, Timestamp since) {
            PodClient podClient = logicPodClientFactory.createPodClientByAccountId(accountId);

            PodStatusResponse podStatusResponse = new PodStatusResponse();
            Map<String, PodStatus> podStatusMap = new HashMap<>();
            try {
                podStatusResponse = podClient.getPodStatus(since);
            } catch (Exception e) {
                LOGGER.error("PodInBuildSyncScheduler : Fail to query pod_status", e);
            }
            for (PodStatus podStatus : podStatusResponse.getPodsInfo()) {
                podStatusMap.put(podStatus.getPodUuid(), podStatus);
            }

            return podStatusMap;
        }

        private List<Resource> getPodResources(List<String> resourceUuids, ResourceClient resourceClient) {

            List<Resource> podResource = new ArrayList<>();
            ResourceIds resourceIds = new ResourceIds();
            resourceIds.setResourceIds(resourceUuids);
            Resources resources = new Resources();
            int tryTime = 3;
            while (tryTime-- > 0) {
                try {
                    resources = resourceClient.getResourcesByIds(resourceIds);
                    if (resources.size() == resourceUuids.size()) {
                        break;
                    } else {  // 可能主从同步导致查询不到，之后重试
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            LOGGER.debug("getResourcesByIds,exception:{}", e);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.debug("getResourcesByIds.exception:{},resourceUuids:{}",
                            e.getMessage(), Arrays.toString(resourceUuids.toArray()));
                    throw e;
                }
            }
            for (Resource resource : resources) {
                if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(resource.getServiceType())) {
                    podResource.add(resource);
                }
            }
            return podResource;
        }

        private void deletePodFromNova(String accountId, TransactionDetailResponse response) {
            try {
                if (CollectionUtils.isEmpty(response.getLstServer())) {
                    LOGGER.debug("pod list is empty");
                    return;
                }
                for (ServersResponse pod : response.getLstServer()) {
                    logicPodClientFactory.createPodClientByAccountId(accountId).deletePod(pod.getId());
                }
            } catch (BceInternalResponseException e) {
                if (e.getHttpStatus() != 404) {
                    throw e;
                }
            }
        }

        private AttachVolumeRollbackDbRequest getVolumeRollbackRequest(PodPO podPO) {
            AttachVolumeRollbackDbRequest rollbackDbRequest = new AttachVolumeRollbackDbRequest();
            List<Volume.PodVolume> podVolumes = new LinkedList<>();
            podVolumes = JsonUtil.toList(podPO.getPodVolumes(), Volume.PodVolume.class);
            if (CollectionUtils.isEmpty(podVolumes)) {
                return null;
            }
            List<String> volumeIds = new LinkedList<>();
            for (Volume.PodVolume podVolume : podVolumes) {
                if (podVolume != null && podVolume.getVolumeSource() != null
                        && podVolume.getVolumeSource().getCds() != null
                        && StringUtils.isNotEmpty(podVolume.getVolumeSource().getCds().getUuid())) {
                    volumeIds.add(podVolume.getVolumeSource().getCds().getUuid());
                }
            }
            if (CollectionUtils.isEmpty(volumeIds)) {
                return null;
            }

            rollbackDbRequest.setInstanceUuid(podPO.getPodId());
            rollbackDbRequest.setDiskIds(volumeIds);

            return rollbackDbRequest;
        }
    }


}
