/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.bci.service.util;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Data
@Component("podConfiguration")
public class PodConfiguration {

    @Value("${pod.delete.status:running,failed,succeeded,unusualorder,crashed}")
    private String delete;

    @Value("${pod.restart.policy:always,onfailure,never}")
    private String restartPolicy;

    @Value("${pod.container.containerCpu:0.25,0.5,1.0,1.5,2.0,4.0}")
    private String containerCpu;

    @Value("${pod.container.whitelist.containerCpu:0.25,0.5,1.0,1.5,2.0,4.0,8.0}")
    private String containerCpuInWhiteList;

    @Value("${pod.container.cpu.memory.min.ratio:2.0}")
    private Float minRatio;

    @Value("${pod.container.cpu.memory.max.ratio:4.0}")
    private Float maxRatio;

    @Value("${pod.container.memory.step.length:0.5}")
    private Float stepLength;

    @Value("${pod.volume.mount.type:NFS,EmptyDir,PodConfigFile}")
    private String volumeMountTypes;

    @Value("${pod.instance.type:}")
    private String podInstanceTypes;

    // 从下单开始算，同步到订单的超时报警时间，默认10分钟，单位：秒
    @Value("${order.ready.timeout:600}")
    private Integer orderReadyTimeout;

    @Value("${pod.image.repo.pattern:^(((hub|registry)\\.baidubce\\.com(/[^/]*)+)/|)([^/]+/)?[^/]+$}")
    private String imageRepoPattern;

    @Value("${pod.container.max.num:15}")
    private Integer maxContainerNum;

    @Value("${pod.root.size.gb:3}")
    private Integer rootSizeInGB;

    @Value("${free.charge.size.gb:13}")
    private Integer freeChargeSizeInGB;

    @Value("${cds.volume.size.gb.min:1}")
    private Integer cdsVolumeSizeMin;

    @Value("${cds.volume.size.gb.max:2000}")
    private Integer cdsVolumeSizeMax;

    public List<String> getPodInstanceTypeList() {
        return Arrays.asList(podInstanceTypes.split(","));
    }

}
