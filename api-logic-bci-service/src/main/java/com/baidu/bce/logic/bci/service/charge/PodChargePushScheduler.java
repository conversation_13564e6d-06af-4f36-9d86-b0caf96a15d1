package com.baidu.bce.logic.bci.service.charge;

import com.baidu.bce.logic.bci.service.charge.service.PodChargePushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@EnableScheduling
@Configuration
@Profile("default")
public class PodChargePushScheduler {

    @Autowired
    private PodChargePushService podChargePushService;

    @Scheduled(cron = "0 0/1 * * * ? ")
    public void runScheduledTask() {
        podChargePushService.pushChargeTask();
    }
}
