package com.baidu.bce.logic.bci.service.common.service;

import com.baidu.bce.billing.resourcemanager.model.ResourceIdentify;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.request.DeleteRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.ExtendedGetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.GetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("logicalResourceService")
public class LogicalResourceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogicalResourceService.class);

    @Autowired
    LogicPodClientFactory logicPodClientFactory;

    @Autowired
    RegionConfiguration regionConfiguration;

    @Value("${bcc.billing.deleteResourceV2:true}")
    boolean deleteResourceV2;

    /**
     * 根据对象id，查询对象在billing中对应的resource
     *
     * @param serviceType
     * @param objectId
     * @return
     */
    public Resource queryResource(ServiceType serviceType, String objectId) {
        ResourceClient resourceClient = logicPodClientFactory.createResourceClient();
        ExtendedGetResourcesRequest extendedGetResourcesRequest = new ExtendedGetResourcesRequest();
        extendedGetResourcesRequest.setName(objectId);
        extendedGetResourcesRequest.setServiceType(serviceType.getName());
        extendedGetResourcesRequest.setRegion(regionConfiguration.getCurrentRegion());
        Resources resources = resourceClient.queryList(extendedGetResourcesRequest);
        if (resources == null || resources.isEmpty()) {
            throw new PodExceptions.ResourceNotExistException();
        }
        return resources.get(0);
    }



    /**
     * 从Billing的Resource表中删除
     */
    public void deleteResourceByName(String accountId, String name, String serviceType) {

        LOGGER.debug("deleteResourceByName accountId: {}, name: {}, serviceType:{}, deleteResourceV2:{}",
                accountId, name, serviceType, deleteResourceV2);
        
        if (deleteResourceV2) {
            LOGGER.debug("delete with resourceV2");
            deleteResourceByNameV2(accountId, name, serviceType);
            return;
        }

        ResourceClient resourceClient = logicPodClientFactory.createResourceClient();

        try {
            Resource resource = getResource(accountId, name, serviceType);
            resourceClient.delete(resource.getUuid());
        } catch (BceInternalResponseException e) {
            if (e.getHttpStatus() != 404) {
                LOGGER.error("[bce logical bci] Delete resource by name failed, name is {}, exception is {}", name, e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
    }

    public void deleteResourceByNameV2(String accountId, String name, String serviceType) {
        ChargeResourceService chargeResourceService =
                logicPodClientFactory.createChargeResourceService(accountId);
        ResourceIdentify resourceIdentify = new ResourceIdentify();
        resourceIdentify.setServiceType(serviceType);
        resourceIdentify.setName(name);
        resourceIdentify.setRegion(regionConfiguration.getCurrentRegion());
        DeleteRequest deleteRequest = new DeleteRequest();
        deleteRequest.setIdentify(resourceIdentify);
        deleteRequest.setAccountId(accountId);

        try {
            chargeResourceService.destroy(deleteRequest);
        } catch (BceInternalResponseException e) {
            if (e.getHttpStatus() != 404) {
                LOGGER.debug("LogicalResourceService.deleteResourceByName caught exception: {}", e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
    }

    public Resource getResource(String accountId, String name, String serviceType) {
        ResourceClient resourceClient = logicPodClientFactory.createResourceClient();
        GetResourcesRequest getResourcesRequest = new GetResourcesRequest();
        getResourcesRequest.setAccountId(accountId);
        getResourcesRequest.setName(name);
        getResourcesRequest.setRegion(regionConfiguration.getCurrentRegion());
        getResourcesRequest.setServiceType(serviceType);

        Resource resource = null;
        Resources resources = resourceClient.list(getResourcesRequest);
        if (resources != null && resources.size() > 0) {
            for (Resource resourceTmp : resources) {
                if (resourceTmp.getName().contains(name) && resourceTmp.getStatus() != ResourceStatus.DESTROYED) {
                    resource = resourceTmp;
                    break;
                }
            }
        }

        if (null == resource) {
            LOGGER.warn("instance resource in not exist");
            throw new PodExceptions.ResourceNotExistException();
        }

        return resource;
    }

}
