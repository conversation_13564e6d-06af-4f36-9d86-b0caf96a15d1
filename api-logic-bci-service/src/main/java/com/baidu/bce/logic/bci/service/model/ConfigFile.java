package com.baidu.bce.logic.bci.service.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ConfigFile extends BaseVolume{

    private List<ConfigFileDetail> configFiles;

    public List<ConfigFileDetail> getConfigFiles() {
        return configFiles;
    }

    public void setConfigFiles(List<ConfigFileDetail> configFiles) {
        this.configFiles = configFiles;
    }

}
