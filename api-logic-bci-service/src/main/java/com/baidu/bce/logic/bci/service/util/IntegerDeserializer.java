package com.baidu.bce.logic.bci.service.util;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

public class IntegerDeserializer extends StdDeserializer<Integer> {

    public IntegerDeserializer() {
        super(Integer.class);
    }

    @Override
    public Integer deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        Integer value = Integer.parseInt(p.getValueAsString());
        return value;
    }
}
