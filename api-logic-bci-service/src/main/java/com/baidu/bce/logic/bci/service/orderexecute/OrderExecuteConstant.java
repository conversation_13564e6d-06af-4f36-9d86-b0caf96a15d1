package com.baidu.bce.logic.bci.service.orderexecute;

public class OrderExecuteConstant {

    public static final String REGION_PAIRS = "bj=北京;gz=广州;hk=香港;hk02=香港2区;su=苏州;fsh=上海金融云;hkg=香港;"
            + "hb-fsg=华北度小满金融专区;bd=华北保定;fwh=金融华中武汉";

    public static final String PRODUCT_TYPE_POSTPAY = "postpay";
    public static final String PRODUCT_TYPE_PREPAY = "prepay";

    public static final String AUTO_RENEW_TIME_KEY = "autoRenewTime";
    public static final String AUTO_RENEW_TIME_UNIT_KEY = "autoRenewTimeUnit";
    public static final String RENEW_TIME_UNIT_MONTH = "month";
    public static final String RENEW_TIME_UNIT_YEAR = "year";
    public static int renewMaxYear = 3;
    public static int renewMaxMonth = 9;

    public static final String ORDER_TIME_OUT = "Create Order Timeout";
    public static final String ORDER_CREATING_TIME_OUT = "Check In Creating Status Order: Create Resource spent too "
            + "long.";
    public static final String ORDER_EXCEPTION = "Order Occurred Exception";

    public static class BciFlavorItemKey {
        public static final String CPU = "CpuRunTime";
        public static final String MEMORY = "RamRunTime";
        public static final String DISK = "disk";
        public static final String PHYSICAL_ZONE = "physicalZone";
        public static final String SUBNET_ID = "subnetId";
    }
}
