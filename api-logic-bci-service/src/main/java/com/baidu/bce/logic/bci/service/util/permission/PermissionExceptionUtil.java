package com.baidu.bce.logic.bci.service.util.permission;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

public class PermissionExceptionUtil {

    /**
     * 无权限 401
     */
    public static class PermissionDenyException extends BceException {
        public PermissionDenyException() {
            super("bci permission deny.",
                    HttpStatus.ERROR_PERMISSION_DENY, "PermissionDeny");
            setRequestId(LogicUserService.getRequestId());
        }
    }
}
