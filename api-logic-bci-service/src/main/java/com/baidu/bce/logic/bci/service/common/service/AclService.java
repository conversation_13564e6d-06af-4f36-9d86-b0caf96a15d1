package com.baidu.bce.logic.bci.service.common.service;

import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.WhiteListKey;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.user.settings.sdk.model.FeatureTypeListRequest;
import com.baidu.bce.user.settings.sdk.model.FeatureTypeListResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component("aclService")
public class AclService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclService.class);

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    /**
     * 查询用户拥有的白名单列表
     *
     * @return
     */
    public List<String> getAclList() {
        try {
            FeatureTypeListRequest request = new FeatureTypeListRequest();
            request.setAclName(getAccountId());
            request.setAclType("AccountId");
            FeatureTypeListResponse response =
                    logicPodClientFactory.createUserSettingsClient(getAccountId()).showFeatureTypes(request);
            return response.getFeatureTypes();
        } catch (Exception e) {
            LOGGER.error("inBccNewFlavorWhiteList error!", e);
        }
        return new ArrayList<>();
    }

    public String getAccountId() {
        return LogicUserService.getAccountId();
    }
}

