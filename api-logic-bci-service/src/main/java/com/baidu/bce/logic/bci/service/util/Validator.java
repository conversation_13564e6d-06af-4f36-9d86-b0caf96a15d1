package com.baidu.bce.logic.bci.service.util;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.BccEncrypt;
import com.baidu.bce.internalsdk.bci.model.QueryVolumesRequest;
import com.baidu.bce.internalsdk.bci.model.QueryVolumesResponse;
import com.baidu.bce.internalsdk.bci.model.VolumeVO;
import com.baidu.bce.internalsdk.bci.model.iam.AttachStatus;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthority;
import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthorityList;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.common.CommonUtils;
import com.baidu.bce.logic.bci.service.common.service.AclService;
import com.baidu.bce.logic.bci.service.common.service.BciAsyncService;
import com.baidu.bce.logic.bci.service.common.service.LogicalQuotaService;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.bci.service.exception.TagExceptions;
import com.baidu.bce.logic.bci.service.model.BciQuota;
import com.baidu.bce.logic.bci.service.model.ConfigFile;
import com.baidu.bce.logic.bci.service.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.service.model.ContainerPurchase;
import com.baidu.bce.logic.bci.service.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.IOrderItem;
import com.baidu.bce.logic.bci.service.model.PodCDSVolumeType;
import com.baidu.bce.logic.bci.service.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.ValidatedItem;
import com.baidu.bce.logic.bci.service.model.Volume;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.request.CreateTagsRequest;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service
public class Validator {
    private static final Logger LOGGER = LoggerFactory.getLogger(Validator.class);

    @Autowired
    private AclService aclService;

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private BciAsyncService bciAsyncService;

    @Autowired
    private LogicalQuotaService logicalQuotaService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private RegionConfiguration regionConfiguration;

    private static final String CPU = "cpu";
    private static final String MEMORY = "memory";

    public ValidatedItem validate(BaseCreateOrderRequestVo<IOrderItem> request, String from,
                                  String accessKey) {
        ValidatedItem validatedItem = new ValidatedItem().setAccessKey(accessKey).setFrom(from)
                .setWhiteList(aclService.getAclList());

        formCreateRequest(request, validatedItem);
        // podValidator.validateImageRegistrySecret(validatedItem.getPodPurchaseRequest());
        podValidator.validateRepository(validatedItem.getPodPurchaseRequest());

        BciQuota bciQuota = logicalQuotaService.getBciQuota();
        podValidator.validateAndSetSubnetUuid(validatedItem.getPodPurchaseRequest(), validatedItem.getZoneMap(),
                validatedItem.getEipPurchaseRequest() != null);
        bciAsyncService.validateBciParameters(validatedItem.getPodPurchaseRequest(), bciQuota);

        bciAsyncService.getZoneResourceDetail();

        asyncExecutorService.getAsyncResult(WorkKeyUtil.genWorkKey("validateBciParameters",
                Arrays.asList(validatedItem.getPodPurchaseRequest(), bciQuota)));

        ZoneAndAuthorityList zoneAndAuthorityList = (ZoneAndAuthorityList) asyncExecutorService.getAsyncResult(
                WorkKeyUtil.genWorkKey("getZoneResourceDetail", new LinkedList<>()));
        for (ZoneAndAuthority zoneAndAuthority : zoneAndAuthorityList.getZoneAndAuthorities()) {
            if (zoneAndAuthority.getZone().equalsIgnoreCase(validatedItem.getLogicalZone())) {
                if (!zoneAndAuthority.isEnableBci()) {
                    throw new PodExceptions.InvalidZoneException(validatedItem.getLogicalZone());
                }
            }
        }

        if (validatedItem.getEipPurchaseRequest() != null) {
            // 购买时开通自动续费校验
            podValidator.validateAutoRenewTime(validatedItem.getEipPurchaseRequest().getAutoRenewTimeUnit(),
                    validatedItem.getEipPurchaseRequest().getAutoRenewTime());
            podValidator.validateEipBandwidthInMbps(validatedItem.getEipPurchaseRequest());
            podValidator.validateEipBlackList(validatedItem.getWhiteList());
        }

        return validatedItem;
    }

    private void formCreateRequest(@NotNull BaseCreateOrderRequestVo<IOrderItem> request, ValidatedItem validatedItem) {
        PodPurchaseRequest podPurchaseRequest = null;
        EipPurchaseRequest eipPurchaseRequest = null;

        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : request.getItems()) {
            switch (item.getConfig().getServiceType().toUpperCase()) {
                case "BCI":
                    podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
                    validatedItem.setPodPurchaseRequest(podPurchaseRequest)
                            .setLogicalZone(podPurchaseRequest.getLogicalZone())
                            .setBciPurchaseOrder(item.getPurchaseOrder())
                            .setBciPaymentModel(item.getPaymentMethod());
                    break;
                case "EIP":
                    eipPurchaseRequest = (EipPurchaseRequest) item.getConfig();
                    validatedItem.setEipPurchaseRequest(eipPurchaseRequest)
                            .setEipPaymentModel(item.getPaymentMethod())
                            .setEipPurchaseOrder(item.getPurchaseOrder());
                    break;
                default:
                    throw new PodExceptions.RequestInvalidException(String.format("Not support serviceType:%s",
                            item.getConfig().getServiceType()));

            }
        }
        if (podPurchaseRequest == null) {
            throw new PodExceptions.RequestInvalidException("bciItem is empty.");
        }

        if (PodConstants.FROM_CONSOLE.equals(validatedItem.getFrom())) {
            base64ForConfigFile(podPurchaseRequest.getVolume().getConfigFile());
        }

        if (CollectionUtils.isNotEmpty(podPurchaseRequest.getTags())) {
            CreateTagsRequest tagRequest = new CreateTagsRequest();
            tagRequest.setTags(podPurchaseRequest.getTags());
            checkTags(tagRequest);
        }

        if (!podConfiguration.getRestartPolicy().contains(podPurchaseRequest.getRestartPolicy().toLowerCase())) {
            throw new PodExceptions.RequestInvalidException();
        }
        validatedItem.setZoneMap(getZone(podPurchaseRequest.getLogicalZone()));
        podPurchaseRequest.setZoneId(validatedItem.getZoneMap().getZoneId());
        podPurchaseRequest.setEncryptedPhysicalZone(BccEncrypt.encrypt(validatedItem.getZoneMap().getPhysicalZone()));

        Map<String, Float> cpuMemory = getCpuMemory(podPurchaseRequest.getContainerPurchases());
        podPurchaseRequest.setCpu(cpuMemory.get(CPU));
        podPurchaseRequest.setMemory(cpuMemory.get(MEMORY));

        fillVolume(podPurchaseRequest);
    }

    // fillVolume 填充volume相关字段
    private void fillVolume(PodPurchaseRequest podPurchaseRequest) {
        Volume volume = podPurchaseRequest.getVolume();
        if (volume == null || CollectionUtils.isEmpty(podPurchaseRequest.getContainerPurchases())) {
            throw new PodExceptions.RequestInvalidException();
        }

        if (CollectionUtils.isEmpty(volume.getPodVolumes())) {
            return;
        }

        // rootfs 默认配置
        ContainerPurchase firstContainer = podPurchaseRequest.getContainerPurchases().get(0);
        Volume.PodVolume rootVolume = new Volume.PodVolume();
        rootVolume.setType(PodCDSVolumeType.ROOTFS.getType());
        rootVolume.setName(firstContainer.getName());
        rootVolume.setSizeInGB(podConfiguration.getRootSizeInGB());
        rootVolume.setVolumeSource(new Volume.VolumeSource());

        int rootFSNum = 0;
        Map<String, Volume.VolumeSource> volumeIdMap = new HashMap<>();
        // 后端 emptyDir是一块盘，所以要加起来做校验
        int emptyTotalSize = 0;
        for (Volume.PodVolume podVolume : volume.getPodVolumes()) {
            if (PodCDSVolumeType.ROOTFS.getType().equalsIgnoreCase(podVolume.getType())) {
                rootFSNum++;
            }

            validatePodVolumeSize(podVolume);

            if (PodCDSVolumeType.EMPTYDIR.getType().equalsIgnoreCase(podVolume.getType())) {
                emptyTotalSize += podVolume.getSizeInGB();
            }

            accumulatedPurchaseSize(podPurchaseRequest, podVolume);

            Volume.VolumeSource volumeSource = podVolume.getVolumeSource();
            if (volumeSource != null &&
                    volumeSource.getCds() != null && StringUtils.isNotEmpty(volumeSource.getCds().getUuid())) {
                if (!PodCDSVolumeType.DATA.getType().equalsIgnoreCase(podVolume.getType())) {
                    throw new PodExceptions.InvalidVolumeSource();
                }

                volumeIdMap.put(volumeSource.getCds().getUuid(), volumeSource);
            }
        }

        Volume.PodVolume emptyTotal = new Volume.PodVolume();
        emptyTotal.setType(PodCDSVolumeType.EMPTYDIR.getType());
        emptyTotal.setSizeInGB(emptyTotalSize);
        validatePodVolumeSize(emptyTotal);

        // 校验已有磁盘的可用区跟pod可用区是否一致, 状态是否available
        validateVolumeZone(volumeIdMap, podPurchaseRequest);

        switch (rootFSNum) {
            case 0:
                podPurchaseRequest.getVolume().getPodVolumes().add(rootVolume);
                podPurchaseRequest.setCdsPurchaseSize(podPurchaseRequest.getCdsPurchaseSize()
                        + podConfiguration.getRootSizeInGB());
                break;
            case 1:
                break;
            default:
                throw new PodExceptions.RootFSVolumeExceededLimit();
        }

        // 扣除免费的额度
        float purchaseSize = podPurchaseRequest.getCdsPurchaseSize() - podConfiguration.getFreeChargeSizeInGB();
        if (purchaseSize < 0) {
            purchaseSize = 0;
        }
        podPurchaseRequest.setCdsPurchaseSize(purchaseSize);
    }

    private void base64ForConfigFile(List<ConfigFile> configFiles) {
        if (CollectionUtils.isEmpty(configFiles)) {
            return;
        }

        for (ConfigFile configFile : configFiles) {
            List<ConfigFileDetail> configFileDetails = configFile.getConfigFiles();
            base64Encode(configFileDetails);
        }
    }

    private void base64Encode(List<ConfigFileDetail> configFileDetails) {
        if (CollectionUtils.isEmpty(configFileDetails)) {
            return;
        }
        for (ConfigFileDetail detail : configFileDetails) {
            detail.setFile(Base64.encodeBase64String(detail.getFile().getBytes()));
        }
    }

    private void checkTags(CreateTagsRequest request) {
        List<String> tagKeyList = new ArrayList<>();
        Set<String> tagKeySet = new HashSet<>();

        for (Tag tag : request.getTags()) {
            if (tag.getTagKey() != null) {
                tagKeyList.add(tag.getTagKey());
                tagKeySet.add(tag.getTagKey());
            }
        }
        if (tagKeyList.size() != tagKeySet.size()) {
            throw new TagExceptions.TagKeyDuplicateException();
        }

        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
        tagClient.checkTags(request);
    }

    public ZoneMapDetail getZone(String logicalZone) {
        ZoneMapDetail zoneMapDetail = null;
        try {
            ZoneClient zoneClient = logicPodClientFactory.createZoneClient(getAccountId());
            zoneMapDetail = zoneClient.createZoneByLogicalZone(logicalZone);
        } catch (Exception e) {
            LOGGER.info("getZone from logical zone error = {}", e);
            throw new PodExceptions.InvalidateZoneException();
        }

        if (zoneMapDetail == null) {
            throw new PodExceptions.InvalidateZoneException();
        }

        return zoneMapDetail;
    }

    private Map<String, Float> getCpuMemory(List<ContainerPurchase> containers) {
        if (CollectionUtils.isEmpty(containers)) {
            throw new PodExceptions.RequestInvalidException();
        }
        float cpu = 0.00F;
        float memory = 0.00F;
        for (ContainerPurchase container : containers) {
            cpu = cpu + container.getCpu();
            memory = memory + container.getMemory();
        }
        Map<String, Float> cpuMemory = new HashMap<>();
        // 白名单，判断是否向上取整
        String instanceType = "";
        if (commonUtils.checkWhiteList(LogicalConstant.ROUND_UP_CPU_MEM, regionConfiguration.getCurrentRegion(),
                getAccountId())) {
            if (memory >= 2.00F) {
                memory = (float) Math.ceil(memory);
                cpu = (float) Math.ceil(cpu);
                instanceType = PodConstants.INSTANCE_TYPE_G4;
            }
        }
        // 非普四，需要校验容器数量
        if (!PodConstants.INSTANCE_TYPE_G4.equals(instanceType)) {
            checkContainerNum(containers);
        }
        cpuMemory.put(CPU, cpu);
        cpuMemory.put(MEMORY, memory);

        return cpuMemory;
    }

    private void checkContainerNum(List<ContainerPurchase> containers) {
        if (containers.size() > podConfiguration.getMaxContainerNum()) {
            throw new PodExceptions.ContainerNumExceededLimit(containers.size(),
                    podConfiguration.getMaxContainerNum());
        }
    }

    private void validatePodVolumeSize(Volume.PodVolume podVolume) {
        // 数据盘目前只支持已有盘，不检查
        if (PodCDSVolumeType.DATA.getType().equals(podVolume.getType())) {
            return;
        }
        if (podVolume.getSizeInGB() == null
                || podVolume.getSizeInGB().compareTo(podConfiguration.getCdsVolumeSizeMin()) == -1
                || podVolume.getSizeInGB().compareTo(podConfiguration.getCdsVolumeSizeMax()) == 1) {
            throw new PodExceptions.InvalidVolumeSize();
        }
    }

    private void accumulatedPurchaseSize(PodPurchaseRequest podPurchaseRequest, Volume.PodVolume podVolume) {
        // 数据盘目前只支持已有盘，不累加计费
        if (PodCDSVolumeType.DATA.getType().equals(podVolume.getType())) {
            return;
        }

        podPurchaseRequest.setCdsPurchaseSize(podPurchaseRequest.getCdsPurchaseSize()
                + podVolume.getSizeInGB().floatValue());
    }

    private void validateVolumeZone(Map<String, Volume.VolumeSource> volumeIdMap,
                                    PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(volumeIdMap.keySet())) {
            return;
        }
        List<String> volumeIds = new ArrayList<>(volumeIdMap.keySet());
        String podZone = podPurchaseRequest.getLogicalZone();
        if (StringUtils.isEmpty(podZone)) {
            throw new PodExceptions.InvalidZoneException(podZone);
        }
        BCCClient bccClient = logicPodClientFactory.createBCCClient(getAccountId());
        QueryVolumesResponse response = bccClient.getVolumes(new QueryVolumesRequest().setDiskIds(volumeIds));
        if (response == null || response.getVolumeVOS() == null) {
            throw new CommonExceptions.InternalServerErrorException();
        }
        if (response.getVolumeVOS().size() != volumeIds.size()) {
            throw new PodExceptions.VolumeNotFound();
        }
        for (VolumeVO volumeVO : response.getVolumeVOS()) {
            if (!"postpay".equalsIgnoreCase(volumeVO.getProductType())) {
                throw new PodExceptions.UnsupportedCDSPayment(volumeVO.getProductType());
            }
            if (!AttachStatus.available.toString().equalsIgnoreCase(volumeVO.getStatus())) {
                throw new PodExceptions.VolumeNotAvailable();
            }
            if (!podZone.equalsIgnoreCase(volumeVO.getLogicalZone())) {
                throw new PodExceptions.VolumeInvalidZone(volumeVO.getLogicalZone(), podZone);
            }
            String mapKey = "";

            if (volumeIdMap.containsKey(volumeVO.getVolumeId())) {
                mapKey = volumeVO.getVolumeId();
            }
            if (volumeIdMap.containsKey(volumeVO.getVolumeUuid())) {
                mapKey = volumeVO.getVolumeUuid();
            }

            volumeIdMap.get(mapKey).getCds().setUuid(volumeVO.getVolumeUuid());
        }
    }
}
