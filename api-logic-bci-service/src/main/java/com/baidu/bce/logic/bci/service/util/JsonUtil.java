package com.baidu.bce.logic.bci.service.util;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

public class JsonUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonUtil.class);

    private static final ObjectMapper MAPPER = new ObjectMapper();

    public static String toJSON(Object obj) {
        StringWriter writer = new StringWriter();
        try {
            MAPPER.writeValue(writer, obj);
        } catch (JsonGenerationException e) {
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return writer.toString();
    }

    public static <T> T fromJSON(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            return MAPPER.readValue(json, clazz);
        } catch (JsonParseException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (IOException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> toList(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json)) {
            return new ArrayList<>();
        }
        try {
            JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
            return MAPPER.readValue(json, javaType);
        } catch (JsonParseException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        } catch (IOException e) {
            LOGGER.warn("json = {}", json);
            throw new RuntimeException(e);
        }
    }
}
