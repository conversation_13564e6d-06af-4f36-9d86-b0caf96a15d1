package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Volume {

    private List<Nfs> nfs;
    private List<EmptyDir> emptyDir;
    private List<ConfigFile> configFile;
    private List<PodVolume> podVolumes;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PodVolume {
        private String name;
        private String type; // data, empty_dir，rootfs
        private VolumeSource volumeSource;
        private Integer sizeInGB;
        @JsonProperty(value = "fs")
        private FS fs;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class FS {
        @JsonProperty(value = "fs_type")
        private String fsType = "ext4";
        @JsonProperty(value = "mount_flags")
        private List<String> mountFlags;
        @JsonProperty(value = "force_format")
        private Boolean forceFormat = false;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VolumeSource {
        private CDS cds;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CDS {
        private String uuid = "";
        private String name;
        private String type; // 磁盘类型
    }

    @Data
    public static class Local {

    }

    public List<Nfs> getNfs() {
        if (nfs == null) {
            nfs = new ArrayList<>();
        }
        return nfs;
    }

    public void setNfs(List<Nfs> nfs) {
        this.nfs = nfs;
    }

    public List<EmptyDir> getEmptyDir() {
        if (emptyDir == null) {
            emptyDir = new ArrayList<>();
        }
        return emptyDir;
    }

    public void setEmptyDir(List<EmptyDir> emptyDir) {
        this.emptyDir = emptyDir;
    }

    public List<ConfigFile> getConfigFile() {
        if (configFile == null) {
            configFile = new ArrayList<>();
        }
        return configFile;
    }

    public void setConfigFile(List<ConfigFile> configFile) {
        this.configFile = configFile;
    }
}
