package com.baidu.bce.logic.bci.service.interceptor;

import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.plat.webframework.iam.service.IAMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

@Configuration
public class ValidateRARequestInterceptorConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private LogicPodClientFactory bciClientFactory;

    @Autowired
    private ResourceAccountConfig config;

    @Autowired
    private IAMService iamService;

    /**
     * 对于创建，删除
     * 若是PaaS方调用，需要校验资源账号
     * @param registry
     */
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ValidateRARequestInterceptor(bciClientFactory, config, iamService))
                .addPathPatterns(
                        "/api/logical/bci/v1/pod/create",
                        "/api/logical/bci/v1/pod/list",
                        "/api/logical/bci/v1/pod/delete",
                        "/api/logical/bci/v1/pod/leakage/delete",
                        "/v1/pod/create",
                        "/v1/pod/list",
                        "/v1/pod/delete");
    }
}
