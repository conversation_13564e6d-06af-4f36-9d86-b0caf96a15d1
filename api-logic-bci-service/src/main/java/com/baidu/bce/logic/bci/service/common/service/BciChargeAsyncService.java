package com.baidu.bce.logic.bci.service.common.service;

import com.baidu.bce.asyncwork.sdk.asyncaop.BceAsyncWork;
import com.baidu.bce.asyncwork.sdk.model.Level;
import com.baidu.bce.billing.proxy.model.v1.LegacyChargeDataRequest;
import com.baidu.bce.billing.proxy.service.v1.LegacyProxyService;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BciChargeAsyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BciChargeAsyncService.class);

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;


    @BceAsyncWork(name = "bciChargeAsync", timeoutMilliSeconds = 15000, level = Level.MIDDLE)
    public LegacyProxyService.EmptyResponse bciCharge(LegacyChargeDataRequest legacyChargeDataRequest) {
        return logicPodClientFactory.createLegacyProxyService().charge(legacyChargeDataRequest);
    }
}
