package com.baidu.bce.logic.bci.service.util;

import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.PodEventPO;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.dao.common.model.Label;
import com.baidu.bce.logic.bci.dao.container.model.ContainerPO;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.common.ApiUtil;
import com.baidu.bce.logic.bci.service.model.BciOrderExtra;
import com.baidu.bce.logic.bci.service.model.ConfigFile;
import com.baidu.bce.logic.bci.service.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.service.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.service.model.ContainerPreviousState;
import com.baidu.bce.logic.bci.service.model.EmptyDir;
import com.baidu.bce.logic.bci.service.model.Environment;
import com.baidu.bce.logic.bci.service.model.Nfs;
import com.baidu.bce.logic.bci.service.model.PodDetail;
import com.baidu.bce.logic.bci.service.model.PodListRequest;
import com.baidu.bce.logic.bci.service.model.Port;
import com.baidu.bce.logic.bci.service.model.VolumeMounts;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PodUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodUtils.class);

    public static final String PATTERN_NAME;
    public static final long FIVE_MINUTES_MILLISECOND = 1000 * 60 * 5;
    private static final String PATTERN_PASSWORD;
    public static final String SEARCH_VALUE;
    public static final String BCI_ORDER_KEY = "bci";
    public static final String BCI_ORDER_ITEM_KEY_PREFIX = BCI_ORDER_KEY + "-";

    static {
        PATTERN_NAME = "^(?!^[-_/.])[\u4e00-\u9fa5a-zA-Z\\d-_/.]{2,256}$";
        PATTERN_PASSWORD = "^(?![0-9]+$)(?![a-zA-Z]+$)(?![!@#$%^*()]+$)[0-9A-Za-z!@#$%^*()]{8,32}$";
        SEARCH_VALUE = "[\u4e00-\u9fa5a-zA-Z\\d-_/.]{1,65}";
    }

    public static boolean validaterName(String name) {
        Pattern pattern = Pattern.compile(PATTERN_NAME);
        return pattern.matcher(name).matches();
    }

    public static <T> List<T> subList(List<T> list, int pageNo, int pageSize) {
        if (CollectionUtils.isEmpty(list) || list.size() < pageSize) {
            return list;
        }
        List<T> listPage = new ArrayList<>();
        for (int i = pageSize * (pageNo - 1); i < list.size() && i < pageNo * pageSize; i ++) {
            listPage.add(list.get(i));
        }
        return listPage;
    }

    public static PodDetail convertPodDetailShow(PodPO podPO, List<ContainerPO> containers) {
        PodDetail podDetail = convertBean(podPO, PodDetail.class);

        podDetail.setNfs(JsonUtil.toList(podPO.getNfs(), Nfs.class));
        podDetail.setEmptyDir(JsonUtil.toList(podPO.getEmptyDir(), EmptyDir.class));
        podDetail.setPodVolumes(JsonUtil.toList(podPO.getPodVolumes(), ServersResponse.ServerBciCds.class));
        podDetail.setConfigFile(base64Decode(podPO.getConfigFile()));
        podDetail.setLabels(JsonUtil.toList(podPO.getTags(), Label.class));
        podDetail.setCpu(podPO.getvCpu());
        podDetail.setPushLog(podPO.getEnableLog() == 1);

        List<PodDetail.ContainerDetail> containerDetails = new ArrayList<>();
        for (ContainerPO container : containers) {
            PodDetail.ContainerDetail containerDetail = convertBean(container, PodDetail.ContainerDetail.class);
            PodDetail.ContainerStatus status = new PodDetail.ContainerStatus();
            status.setPreviousState(JsonUtil.fromJSON(container.getPreviousState(), ContainerPreviousState.class));
            status.setCurrentState(JsonUtil.fromJSON(container.getCurrentState(), ContainerCurrentState.class));
            status.setRestartCount(container.getRestartCount());

            containerDetail.setStatus(status);
            containerDetail.setArgs(JsonUtil.toList(container.getArgs(), String.class));
            containerDetail.setCommands(JsonUtil.toList(container.getCommands(), String.class));
            containerDetail.setPorts(JsonUtil.toList(container.getPorts(), Port.class));
            containerDetail.setVolumeMounts(JsonUtil.toList(container.getVolumeMounts(), VolumeMounts.class));
            containerDetail.setEnvs(JsonUtil.toList(container.getEnvs(), Environment.class));
            containerDetails.add(containerDetail);
        }
        podDetail.setContainers(containerDetails);
        return podDetail;
    }

    /**
     * 将一个对象转换为另一个对象
     * @param <T1> 要转换的对象
     * @param <T2> 转换后的类
     * @param orimodel 要转换的对象
     * @param castClass 转换后的类
     * @return 转换后的对象
     */
    public static <T1, T2> T2 convertBean(T1 orimodel, Class<T2> castClass) {
        T2 returnModel = null;
        try {
            returnModel = castClass.newInstance();
        } catch (Exception e) {
            throw new RuntimeException("创建" + castClass.getName() + "对象失败");
        }
        List<Field> fieldList = new ArrayList<>(); // 要转换的字段集合
        while (castClass != null && // 循环获取要转换的字段,包括父类的字段
                !castClass.getName().toLowerCase().equals("java.lang.object")) {
            fieldList.addAll(Arrays.asList(castClass.getDeclaredFields()));
            castClass = (Class<T2>) castClass.getSuperclass(); // 得到父类,然后赋给自己
        }
        for (Field field : fieldList) {
            PropertyDescriptor getpd = null;
            PropertyDescriptor setpd = null;
            try {
                getpd = new PropertyDescriptor(field.getName(), orimodel.getClass());
                setpd = new PropertyDescriptor(field.getName(), returnModel.getClass());
            } catch (Exception e) {
                continue;
            }
            try {
                Method getMethod = getpd.getReadMethod();
                Object transValue = getMethod.invoke(orimodel);
                Method setMethod = setpd.getWriteMethod();
                setMethod.invoke(returnModel, transValue);
            } catch (Exception e) {
                LOGGER.warn("skip property " + field.getName());
            }
        }
        return returnModel;
    }

    public static List<ConfigFile> base64Decode(String json) {
        List<ConfigFile> configFiles = JsonUtil.toList(json, ConfigFile.class);
        if (CollectionUtils.isEmpty(configFiles)) {
            return null;
        }
        for (ConfigFile configFile : configFiles) {
            if (CollectionUtils.isEmpty(configFile.getConfigFiles())) {
                continue;
            }
            for (ConfigFileDetail configFileDetail : configFile.getConfigFiles()) {
                if (StringUtils.isNotEmpty(configFileDetail.getFile())) {
                    configFileDetail.setFile(new String(Base64.decodeBase64(configFileDetail.getFile())));
                }
            }
        }
        return configFiles;
    }

    public static final <T> List<T> equalFilter(List<T> list, String propertyName,
                                                Object propertyValue, boolean filterWithNull) {
        List<T> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        boolean valueIsNull = propertyValue == null;
        if (!filterWithNull && valueIsNull) {
            return result;
        }
        for (T t : list) {
            Object value = ApiUtil.getValue(t, propertyName);
            if (valueIsNull) {
                if (value == null) {
                    result.add(t);
                }
            } else {
                if (propertyValue.equals(value)) {
                    result.add(t);
                }
            }
        }
        return result;
    }

    public static final <T> List<T> likeFilter(List<T> list, String propertyName, String propertyValue) {
        List<T> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        if (propertyValue == null) {
            return list;
        }
        String regex = ".*" + escapeSpecialLetter(propertyValue) + ".*";
        Pattern p = Pattern.compile(regex);
        for (T t : list) {
            String value = (String) ApiUtil.getValue(t, propertyName);
            if (value != null) {
                Matcher m = p.matcher(value);
                if (m.matches()) {
                    result.add(t);
                }
            }
        }
        return result;
    }

    /**
     * 转义正则特殊字符 （$()*+.[]?\^{},|）
     *
     * @param keyword
     *
     * @return
     */
    public static final String escapeSpecialLetter(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return keyword;
        }

        StringBuilder builder = new StringBuilder(keyword);

        /**
         * 对\的转义需先做
         */
        String[] specialLetters = {"\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|"};
        for (String letter : specialLetters) {
            if (keyword.contains(letter)) {
                keyword = keyword.replace(letter, "\\" + letter);
            }
        }
        return keyword;
    }

    public static final <T> List<T> generalLikeFilter(List<T> list, String propertyValue) {
        List<T> result = new ArrayList<>();
        Set<T> resSet = new HashSet<T>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        if (propertyValue == null) {
            return list;
        }
        Class clazz = list.get(0).getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // 为了避免统计单测覆盖率的时候往里面注入的新的字段
            if (!field.isSynthetic()) {
                String propertyName = field.getName();
                resSet.addAll(likeFilter(list, propertyName, propertyValue));
            }
        }

        result.addAll(resSet);
        return result;
    }

    public static  <T> T convertRequestModel(PodListRequest podListRequest, Class<T> clazz) {
        // keywordType/keyword 统一到filter
        if (StringUtils.isNotEmpty(podListRequest.getKeywordType())) {
            if (null == podListRequest.getFilterMap()) {
                podListRequest.setFilterMap(new HashMap<String, String>());
            }
            podListRequest.getFilterMap().put(podListRequest.getKeywordType(), podListRequest.getKeyword());
        }

        return PodUtils.convertBean(podListRequest, clazz);
    }

    /**
     * 为返回值对象添加分页信息：page类型
     *
     * @param listRequest list请求
     * @return response
     */
    public static <T> LogicPageResultResponse<T> initEdpPageResultResponse(PodListRequest listRequest) {
        LogicPageResultResponse<T> pageResultResponse = new LogicPageResultResponse<>();
        OrderModel orderModel = listRequest.getOrders() == null ? new OrderModel() : listRequest.getOrders().get(0);
        pageResultResponse.setOrder(orderModel.getOrder());
        pageResultResponse.setOrderBy(orderModel.getOrderBy());
        pageResultResponse.setPageNo(listRequest.getPageNo());
        pageResultResponse.setPageSize(listRequest.getPageSize());
        return pageResultResponse;
    }

    public static <T> LogicPageResultResponse<T> getEmptyResponse(LogicPageResultResponse<T> resultResponse) {
        resultResponse.setTotalCount(0);
        resultResponse.setResult(new ArrayList<T>());
        return resultResponse;
    }

    public static List<String> getTags(ImageTags imageTags) {
        List<String> userImageTags = new ArrayList<>();
        List<ImageTags.Tags> tags = imageTags.getTags();
        for (ImageTags.Tags tag : tags) {
            userImageTags.add(tag.getName());
        }
        return userImageTags;
    }

    public static void quickSort(ArrayList<PodEventPO> list, int left, int right, boolean flag) {
        if (left >= right) {
            return;
        }
        int i = left;
        int j = right;
        PodEventPO podEventPO = list.get(left);

        if (flag) {
            while (i < j) {
                while (i < j && podEventPO.getEventTime().compareTo(list.get(j).getEventTime()) > 0) {
                    j--;
                }
                list.set(i, list.get(j));
                while (i < j && podEventPO.getEventTime().compareTo(list.get(i).getEventTime()) <= 0) {
                    i++;
                }
                list.set(j, list.get(i));
            }
        } else {
            while (i < j) {
                while (i < j && podEventPO.getEventTime().compareTo(list.get(j).getEventTime()) <= 0) {
                    j--;
                }
                list.set(i, list.get(j));
                while (i < j && podEventPO.getEventTime().compareTo(list.get(i).getEventTime()) > 0) {
                    i++;
                }
                list.set(j, list.get(i));
            }
        }
        list.set(i, podEventPO);

        quickSort(list, left, i - 1, flag);
        quickSort(list, i + 1, right, flag);
    }

    /**
     * 从orderItem中解析podId
     * @param orderItem
     * @return String
     */
    public static String parsePodIdFromOrderItem(Order.Item orderItem) {
        // key:bci-p-xaw7cc61
        // return: p-xaw7cc61
        String key = orderItem.getKey();
        if (StringUtils.isEmpty(key)) {
            LOGGER.error("key is empty, orderItem:{}", orderItem);
            return null;
        }
        // 首先判断key是否以ORDER_ITEM_KEY_PREFIX(bci-)开头
        // 如果是,则返回之后的字符串
        // 如果不是,则返回空null
        if (key.startsWith(BCI_ORDER_ITEM_KEY_PREFIX)) {
            return key.substring(BCI_ORDER_ITEM_KEY_PREFIX.length());
        } else {
            return null;
        }
    }

    public static BciOrderExtra getOrderExtra(String extra) throws IOException {
        BciOrderExtra orderExtra = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            orderExtra = objectMapper.readValue(extra, BciOrderExtra.class);
        } catch (IOException e) {
            LOGGER.error("订单extra解析失败");
            throw e;
        }
        return orderExtra;
    }

    public static String getExtraByServiceType(Order order, String serviceType) {

        if (StringUtils.isEmpty(serviceType)) {
            return "";
        }
        for (Order.Item item : order.getItems()) {
            if (serviceType.equalsIgnoreCase(item.getServiceType())) {
                return item.getExtra();
            }
        }
        return "";
    }

    public static String createLongUuid() {
        return UUID.randomUUID().toString();
    }
}
