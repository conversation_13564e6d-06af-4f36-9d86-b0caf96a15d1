package com.baidu.bce.logic.bci.service.model;

import com.baidu.bce.logic.bci.dao.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PodPurchaseRequest implements IOrderItem{

    private String name;
    private String restartPolicy = "";
    private String cceId = "";
    private String vpcId = "";
    private String vpcUuid = "";
    private String subnetId = "";
    private String subnetUuid = "";
    private String subProductType;
    @JsonProperty(value = "volumes")
    private Volume volume;
    @JsonProperty(value = "containers")
    private List<ContainerPurchase> containerPurchases;
    @JsonProperty(value = "imageRegistrySecret")
    private List<ImageRegistrySecret> imageRegistrySecrets;
    private String securityGroupId;
    private List<Tag> tags;
    private int purchaseNum = 1;
    private String logicalZone = "";
    private String zoneId = "";
    private float cpu;
    private float memory;
    private float cdsPurchaseSize = 0;
    private String serviceType = "BCI";
    private String productType;
    /**
     * 加密后的 物理zone
     */
    private String encryptedPhysicalZone;

    private String orderUuid;
    private String annotations;

    private List<Label> labels;

    private boolean enableLog = false;

    /**
     * 业务
     */
    private String application = "default";

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

}
