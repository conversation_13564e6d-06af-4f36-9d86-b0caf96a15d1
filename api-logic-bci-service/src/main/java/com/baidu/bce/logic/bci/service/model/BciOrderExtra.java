package com.baidu.bce.logic.bci.service.model;

import com.baidu.bce.logic.bci.dao.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import lombok.Data;

import java.util.List;

@Data
public class BciOrderExtra {
    private String restartPolicy;
    private Volume volume;
    private List<ContainerPurchase> containers;
    private List<ImageRegistrySecret> imageRegistrySecrets;
    private List<Tag> tags;
    private String logicalZone;
    private String zoneId;
    private String name;
    private String securityGroupId;
    private String annotations;
    private String physicalZone;
    private String subnetUuid;
    private List<Label> labels;
    private boolean enableLog;
    // 当统一计费时，会在extra中加用户的accountID
    private String extraAccountID;
    private String chargeSource;
}
