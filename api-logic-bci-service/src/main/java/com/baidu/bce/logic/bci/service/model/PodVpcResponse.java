package com.baidu.bce.logic.bci.service.model;

import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import lombok.Data;

@Data
public class PodVpcResponse {
    private SimpleSecurityGroupVO securityGroupVO;
    private VpcVo vpc;
    private SubnetVo subnet;
}
