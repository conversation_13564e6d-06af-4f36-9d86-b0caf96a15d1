package com.baidu.bce.logic.bci.service.pod;

import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.bci.dao.chargestatus.PodChargeStatusDao;
import com.baidu.bce.logic.bci.dao.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.dao.container.ContainerDao;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.BciStatus;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.DeleteTagAssociationRequest;
import com.baidu.bce.logical.tag.sdk.model.Resource;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.Date;

import static com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate;


public abstract class BaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseService.class);

    @Autowired
    protected PodDao podDao;

    @Autowired
    protected ContainerDao containerDao;

    @Autowired
    protected PodChargeStatusDao podChargeStatusDao;

    @Autowired
    protected LogicPodClientFactory logicPodClientFactory;

    @Autowired
    protected RegionConfiguration regionConfiguration;

    protected void simplyDeletePod(PodPO podPO) {
        try {
            podDao.deletePod(podPO.getUserId(), podPO.getPodUuid());
            containerDao.deleteContainers(getAccountId(), podPO.getPodId());
            unBindTags(podPO);
        } catch (Exception e) {

        }
    }

    protected PodPO getPodPOByPodId(String podId) {
        if (StringUtils.isBlank(podId)) {
            throw new CommonExceptions.RequestInvalidException();
        }
        PodPO podPO = podDao.getPodDetail(getAccountId(), podId);
        if (podPO == null) {
            LOGGER.debug("get podPO return null, uuid is {}", podId);
            throw new CommonExceptions.ResourceNotExistException();
        }
        return podPO;
    }

    protected PodPO getByPodId(String podId) {
        if (StringUtils.isBlank(podId)) {
            throw new CommonExceptions.RequestInvalidException();
        }
        PodPO podPO = podDao.getPodById(podId);
        if (podPO == null) {
            LOGGER.debug("get podPO return null, uuid is {}", podId);
            throw new CommonExceptions.ResourceNotExistException();
        }
        return podPO;
    }

    public void unBindTags(PodPO podPO) {

        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
        DeleteTagAssociationRequest request = new DeleteTagAssociationRequest();
        Resource resource = new Resource();
        resource.setRegion(regionConfiguration.getCurrentRegion());
        resource.setResourceId(podPO.getPodId());
        resource.setResourceUuid(podPO.getPodUuid());
        resource.setServiceType(PodConstants.SERVICE_TYPE);
        request.setResource(resource);

        tagClient.deleteTagAssociation(request);
    }

    public void unBindTags(String podId, String podUuid) {

        LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
        DeleteTagAssociationRequest request = new DeleteTagAssociationRequest();
        Resource resource = new Resource();
        resource.setRegion(regionConfiguration.getCurrentRegion());
        resource.setResourceId(podId);
        resource.setResourceUuid(podUuid);
        resource.setServiceType(PodConstants.SERVICE_TYPE);
        request.setResource(resource);

        tagClient.deleteTagAssociation(request);
    }

    public String getAccountId() {
        return LogicUserService.getAccountId();
    }


    protected void deletePodFromNova(String accountID, String uuid) {
        try {
            PodClient podClient = logicPodClientFactory.createPodClientByAccountId(accountID);
            podClient.deletePod(uuid);
        } catch (BceInternalResponseException e) {
            if (e.getHttpStatus() != 404) {
                LOGGER.error("Delete from backend failed, exception is {}", e);
                throwPermissionDeniedExceptionIfAppropriate(e);
                throw new PodExceptions.InternalServerErrorException();
            }
        }
    }

    protected void deletePodRecord(PodPO podPO) {
        PodChargeStatus podChargeStauts = new PodChargeStatus();
        podChargeStauts.setPodUuid(podPO.getPodUuid());
        podChargeStauts.setPreviousState(podPO.getStatus());
        podChargeStauts.setCurrentState(BciStatus.DELETED.getName());
        podChargeStauts.setChargeState(PodConstants.NO_CHARGE);
        podChargeStauts.setCreatedTime(new Timestamp(new Date().getTime()));

        podChargeStatusDao.insert(podChargeStauts);
    }
}
