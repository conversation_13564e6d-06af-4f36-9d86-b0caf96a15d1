package com.baidu.bce.logic.bci.service.sync.service;

import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.model.PodStatusResponse;
import com.baidu.bce.logic.bci.dao.chargestatus.PodChargeStatusDao;
import com.baidu.bce.logic.bci.dao.chargestatus.model.PodChargeStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.sql.Timestamp;

@Service
public class PodContainerSyncService extends SyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodContainerSyncService.class);
    private static final long TEN_MIN = 10  * 60 * 1000L;
    private static final long HALF_DAY = 12 * 60 * 60 * 1000L;

    public void syncPodContainer() {
        // 将这个since 入库，全部 sync 完成后，才更新since
        Timestamp since = podChargeStatusDao.getSinceTime();
        if (since == null) {
            since = podDao.updateSince();
            // 初始化 since 记录
            PodChargeStatus podChargeStatus = new PodChargeStatus();
            podChargeStatus.setPodUuid(PodChargeStatusDao.SINCE_PLACEHOLDER);
            podChargeStatus.setPreviousState("");
            podChargeStatus.setCurrentState("");
            podChargeStatus.setChargeState("");
            podChargeStatus.setCreatedTime(since);
            podChargeStatusDao.insert(podChargeStatus);
        }
        PodClient podClient = createPodClient();
        PodStatusResponse podStatus = null;
        try {
            // 这里跟since 一样，是utc时间
            Timestamp nextSince = new Timestamp(new Date().getTime());
            LOGGER.debug("start sync pod status since={}", since);
            if (nextSince.getTime() - since.getTime() > HALF_DAY) {
                LOGGER.debug("SINCE TIME TOO LONG");
            }

            podStatus = podClient.getPodStatus(since);

            LOGGER.debug("pods_info={}", podStatus.getPodsInfo());

            boolean updateSince = syncPod(podStatus.getPodsInfo());

            // 存在pod没有更新，不更新 since 时间
            if (!updateSince) {
                nextSince = since;
            } else {
                podChargeStatusDao.updateSinceTime(nextSince);
            }

            LOGGER.debug("end of sync pod status nextSince={}", nextSince);
        } catch (Exception ex) {
            LOGGER.warn("fail to query pod_status", ex);
        }
    }

    private PodClient createPodClient() {
        return logicPodClientFactory.createAdminPodClient();
    }
}
