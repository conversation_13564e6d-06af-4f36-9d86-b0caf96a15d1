package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class ContainerPurchase {
    private String name = "";
    private String imageName = "";
    private String imageVersion = "";
    private String imageAddress = "";
    private float memory;
    private float cpu;
    private String workingDir = "";
    private String imagePullPolicy = "";
    private List<String> commands;
    private List<String> args;
    private List<VolumeMounts> volumeMounts;
    private List<Port> ports;
    private List<Environment> envs;
}
