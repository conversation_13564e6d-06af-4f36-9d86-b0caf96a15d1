package com.baidu.bce.logic.bci.service.sync;

import com.baidu.bce.logic.bci.service.sync.service.PodInBuildSyncService;
import com.baidu.bce.logic.bci.service.sync.service.SyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@EnableScheduling
@Configuration
@Profile("default")
public class PodInBuildSyncScheduler extends SyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodInBuildSyncScheduler.class);

    private static final int FIX_DELAY_ONE = 3000;

    @Autowired
    private PodInBuildSyncService podInBuildSyncService;

    // 同步pod状态, 使用min(updated_time) 作为since 获取全量的更新结果，把podInfo里的since设置为pod的updated_time.
    @Scheduled(fixedDelay = FIX_DELAY_ONE)
    public void runScheduledTask() {
        podInBuildSyncService.syncPodInBuild();
    }
}
