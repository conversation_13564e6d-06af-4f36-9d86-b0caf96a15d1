package com.baidu.bce.logic.bci.service.common;

import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.request.ListRequest;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.bci.service.constant.PodConstants.FROM_API;

@Component
public class ApiUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiUtil.class);
    private static final String API_LOGICAL_ZONE_RESPONSE_PATTERN = "^zone[0-9a-zA-Z]+$";
    private static final Pattern LOGICAL_ZONE_RESPONSE_PATTERN = Pattern.compile(API_LOGICAL_ZONE_RESPONSE_PATTERN);
    private static final String API_LOGICAL_ZONE_REQUEST_PATTERN = "^[a-zA-Z]+-[0-9a-zA-Z\\-]+-[0-9a-zA-Z]+$";
    private static final Pattern LOGICAL_ZONE_REQUEST_PATTERN = Pattern.compile(API_LOGICAL_ZONE_REQUEST_PATTERN);

    @Autowired
    private RegionConfiguration regionConfiguration;

    public String apiZoneToLogicZone(String from, String logicZone) {
        if (!FROM_API.equalsIgnoreCase(from)) {
            return null;
        }
        if (logicZone == null || !LOGICAL_ZONE_REQUEST_PATTERN.matcher(logicZone).matches()
                || !StringUtils.containsIgnoreCase(logicZone, regionConfiguration.getCurrentRegion())) {
            throw new CommonExceptions.RequestInvalidException("zone is invalid.");
        }
        return "zone" + StringUtils.substringAfterLast(logicZone, "-").toUpperCase();
    }

    public static Object getValue(Object o, String propertyName) {
        Object result = null;
        try {
            result = PropertyUtils.getProperty(o, propertyName);
        } catch (IllegalAccessException e) {
            LOGGER.error("IllegalAccessException", e);
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            LOGGER.error("InvocationTargetException", e);
            throw new RuntimeException(e);
        } catch (NoSuchMethodException e) {
            if (propertyName.startsWith("this$")) {
                LOGGER.warn("the property name is {}", propertyName);
                // 内部类的情况
                return null;
            }

            String methodName = getMethodName(propertyName, "get");
            for (Class<?> clazz = o.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
                try {
                    Method method = clazz.getDeclaredMethod(methodName);
                    method.setAccessible(true);
                    return method.invoke(o);
                } catch (NoSuchMethodException e1) {
                } catch (InvocationTargetException e1) {
                    LOGGER.warn(e1.getMessage());
                } catch (IllegalAccessException e1) {
                    LOGGER.warn(e1.getMessage());
                }
            }
            throw new RuntimeException(e);
        }

        return result;
    }

    private static String getMethodName(String propertyName, String prefix) {
        StringBuilder sb = new StringBuilder(prefix);
        sb.append(propertyName.toUpperCase().charAt(0));
        sb.append(propertyName.substring(1));
        return sb.toString();
    }

    public void convertListRequest(String from, ListRequest request) {
        if (!FROM_API.equalsIgnoreCase(from)) {
            return;
        }

        String logicZone = apiZoneToLogicZone(from, request.getLogicZone());
        if (StringUtils.isNotEmpty(logicZone)) {
            request.setLogicZone(logicZone);
        }

    }
}
