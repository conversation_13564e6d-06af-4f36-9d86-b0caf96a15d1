package com.baidu.bce.logic.bci.service.util.permission;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.SignatureValidator;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.logic.bci.service.common.CommonUtils;
import com.baidu.bce.logic.bci.service.model.BCIBatchPermissionRequest;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Aspect
@Component
@ConditionalOnExpression("${bce.logical.permissionvertify.enabled:false}")
@Order(Integer.MAX_VALUE - 100)
public class PermissionAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionAspect.class);

    @Autowired
    private IamLogicService iamLogicService;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private CommonUtils commonUtils;

    /**
     * 定义拦截规则：拦截com.baidu.bce包下面的所有类中，
     * 有@RequestMapping注解。
     * 且有@PermissionVertify注解的方法。
     */
    @Pointcut("execution(* com.baidu.bce..*(..)) "
            + "&& @annotation(org.springframework.web.bind.annotation.RequestMapping) "
            + "&& @annotation(com.baidu.bce.logic.bci.service.util.permission.PermissionVertify) ")
    public void permissionVertifyPointcut() {

    }

    /**
     * 对于参数，如果要指定资源，必须在参数上添加IdPermission注解，无论是字符串还是对象；
     * 对象中的属性，要在资源id的字段上加上IdPermission注解，并指定服务号和权限列表
     *
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    @Around("permissionVertifyPointcut()")
    public Object permissionVertifyInterceptor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        boolean permissionVertifySuccess = true;
        Set<String> instanceIdSet = new HashSet<>();
        String securityToken = null;
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                    .currentRequestAttributes()).getRequest();

            securityToken = request.getHeader("x-bce-security-token");
        } catch (Exception e) {
            LOGGER.debug("get request from context failed: {}", e);
        }

        LOGGER.debug("1. PermissionAspect: handle annotation and vertify permission, securityToken: {}", securityToken);
        try {
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            Method method = methodSignature.getMethod();

            // 从注解中获取『类型』
            PermissionVertify permissionVertifyAnnotation = method.getAnnotation(PermissionVertify.class);
            if (permissionVertifyAnnotation == null) {
                throw new Exception("PermissionAspect not valid annotation");
            }
            String operationType = permissionVertifyAnnotation.type();
            String[] serviceIds = permissionVertifyAnnotation.service(); // 服务ID eg. [bce:bcc,bce:dcc]
            String[] permissions = permissionVertifyAnnotation.permission();
            if (StringUtils.isNotBlank(operationType)
                    && PermissionConstant.BILLING_TYPE.equalsIgnoreCase(operationType)
                    && permissions.length > 0
                    && PermissionConstant.BCI_CONTROL.equals(permissions[0])) {
                instanceIdSet.add("*");
            } else {
                instanceIdSet = handleMethodParameters(proceedingJoinPoint);
                commonUtils.shortIdCheck(instanceIdSet);
            }
            permissionVertifySuccess = permissionVertifySuccess(instanceIdSet, serviceIds, permissions, securityToken);
        } catch (Exception e) {
            LOGGER.debug("1. PermissionAspect error! error message is {}", e);
        }

        if (!permissionVertifySuccess) {
            LOGGER.debug("permission vertify deny! resouce={}", instanceIdSet);
            throw new PermissionExceptionUtil.PermissionDenyException();
        }


        Object result = null;
        Object[] originalArgs = proceedingJoinPoint.getArgs();

        // 正常处理方法
        LOGGER.debug("2. PermissionAspect: execute method");
        try {
            result = proceedingJoinPoint.proceed(originalArgs);
        } catch (Exception e) {
            throw e;
        }

        return result;
    }

    /**
     * 去IAM进行权限验证
     *
     * @param objectIdSet
     * @param serviceIds
     * @param permissions
     * @return 通过返回true， 异常或拒绝返回false
     */
    private boolean permissionVertifySuccess(Set<String> objectIdSet, String[] serviceIds,
                                             String[] permissions, String securityToken)
            throws UnsupportedEncodingException {
        // IAM权限验证所需上下文信息
        String region = regionConfiguration.getCurrentRegion();
        if (StringUtils.isBlank(region)) {
            region = "bj";
        }
        String accountId = LogicUserService.getAccountId();
        String userId = LogicUserService.getUserId();
        LOGGER.debug("VertifyInfo: objectIdSet={}, serviceIds={}, permissions={}, region={}, accountId={}, userId={}",
                objectIdSet, serviceIds, permissions, region, accountId, userId);

        if (CollectionUtils.isEmpty(objectIdSet)) {
            LOGGER.debug("no resource id need to vertify.");
            return true;
        }

        IAMClient iamClient = createIamClient(region);
        String authToken = iamLogicService.getConsoleToken().getId();
        iamClient.setxAuthToken(authToken);
        LOGGER.debug("authToken={}", authToken);


        List<BCIBatchPermissionRequest> batchPermissionRequests = new ArrayList<>();
        for (String serviceId : serviceIds) {
            BCIBatchPermissionRequest batchPermissionRequest = new BCIBatchPermissionRequest();
            List<BatchPermissionRequest.Request> requests = new ArrayList<>();
            for (String objectId : objectIdSet) {
                BatchPermissionRequest.Request request = new BatchPermissionRequest.Request();
                request.setPermission(Arrays.asList(permissions));
                request.setRegion(region);
                request.setResourceOwner(accountId);
                request.setResource(Arrays.asList(PermissionConstant.TYPE_PREFIX_POD + objectId));
                request.setService(serviceId);
                requests.add(request);
            }
            batchPermissionRequest.setVerifyList(requests);
            batchPermissionRequest.setSecurityToken(securityToken);
            batchPermissionRequests.add(batchPermissionRequest);
        }

        boolean vertifySuccess = false;
        for (BatchPermissionRequest batchPermissionRequest : batchPermissionRequests) {
            if ((vertifySuccess = isVertifySuccess(iamClient, userId, batchPermissionRequest))) {
                break;
            }
        }

        return vertifySuccess;

    }

    private boolean isVertifySuccess(IAMClient iamClient, String userId,
                                     BatchPermissionRequest batchPermissionRequest) {
        LOGGER.debug("isVertifySuccess: userId={}, batchPermissionRequest={}", userId, batchPermissionRequest);
        BatchVerifyResults verifyResults = null;
        try {
            verifyResults = iamClient.batchVerify(userId, batchPermissionRequest);
        } catch (Exception e) {
            LOGGER.debug("authAndVerify error! ", e.getMessage());
            return false;
        }

        if (verifyResults == null || CollectionUtils.isEmpty(verifyResults.getVerifyResults())) {
            LOGGER.debug("vertify result null.");
            return false;
        }

        List<BatchVerifyResults.Result> results = verifyResults.getVerifyResults();
        if (CollectionUtils.isEmpty(results)) {
            return false;
        }

        for (BatchVerifyResults.Result result : results) {
            if (result == null || CollectionUtils.isEmpty(result.getResult())) {
                return false;
            }
            for (VerifyResult subResult : result.getResult()) {
                if (subResult == null) {
                    return false;
                }
                if (!PermissionConstant.ALLOW_PERMISSION.equals(subResult.getEffect())) {
                    LOGGER.debug("permission not allow. result = {}", subResult);
                    return false;
                }
            }
        }

        LOGGER.debug("vertify success!");
        return true;
    }

    /**
     * 处理方法参数
     * 只处理带@IdPermission注解的参数
     *
     * @param proceedingJoinPoint
     */
    public Set<String> handleMethodParameters(ProceedingJoinPoint proceedingJoinPoint)
            throws IllegalAccessException, ClassNotFoundException {
        Set<String> resourceIdSet = new HashSet<>();

        Object[] args = proceedingJoinPoint.getArgs();  // 获取所有的参数
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations(); // 获取所有的注解
        Type[] parameterType = method.getGenericParameterTypes();  // 获取所有参数的类型，与args对应

        for (int argIndex = 0; argIndex < args.length; argIndex++) { // 对于任意一个参数
            for (Annotation annotation : parameterAnnotations[argIndex]) { // 对于选定的参数的任意一个注解
                if (!(annotation instanceof IdPermission)) {
                    continue;
                }

                // 只处理带@IdPermission注解的参数
                handleParameter(parameterType[argIndex], args[argIndex], resourceIdSet);
            }
        }

        return resourceIdSet;
    }

    /**
     * 处理带@IdPermission注解的参数
     * 此参数类型只支持：基本类型、字符串、List、Set、Map、自定义Class类型
     *
     * @param paramType
     * @param obj
     * @param resourceIdSet
     * @throws IllegalAccessException
     * @throws ClassNotFoundException
     */
    public void handleParameter(Type paramType, Object obj, Set<String> resourceIdSet)
            throws IllegalAccessException, ClassNotFoundException {
        LOGGER.debug("handleParameter: paramType={}, resourceIdSet={}", paramType, resourceIdSet);
        if (paramType instanceof ParameterizedType) { // 泛型参数：List、Set、Map、自定义泛型
            String paramRawType = ((Class) ((ParameterizedType) paramType).getRawType()).getName();
            if (StringUtils.isBlank(paramRawType)) {
                return;
            }

            if (paramRawType.startsWith(PermissionConstant.INTERAL_CLASS_PREFIX)) { // 自定义泛型类型参数
                handleParameterClassValue(Class.forName(paramRawType), obj, resourceIdSet);
                return;
            }

            Class<?> paramClazz = Class.forName(paramRawType);
            if (checkType(paramClazz, Map.class)) { // Map类型
                Set<String> tmpResourceIdSet = new HashSet<>();
                List<Object> objectList = new ArrayList<>();
                processMapParameter(obj, tmpResourceIdSet, objectList);

                if (CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                    resourceIdSet.addAll(tmpResourceIdSet);
                }
                if (CollectionUtils.isEmpty(objectList)) {
                    for (Object value : objectList) {
                        handleParameter(value.getClass(), value, resourceIdSet);
                    }
                }
            } else if (checkType(paramClazz, Collection.class)) { // 集合类型 List、Set
                Set<String> tmpResourceIdSet = new HashSet<>();
                List<Object> objectList = new ArrayList<>();
                processCollectionParameter(obj, tmpResourceIdSet, objectList);

                if (CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                    resourceIdSet.addAll(tmpResourceIdSet);
                }
                if (CollectionUtils.isEmpty(objectList)) {
                    for (Object value : objectList) {
                        handleParameter(value.getClass(), value, resourceIdSet);
                    }
                }
            } else {
                LOGGER.debug("not valid parameter type. {}", paramType);
            }

        } else if (isPrimitiveType((Class) paramType)) { // 基本类型、字符串
            LOGGER.debug("the type of parameter is primitive, " + paramType);
            resourceIdSet.addAll(processResources(obj.toString()));
        } else if (((Class) paramType).getName().startsWith(PermissionConstant.INTERAL_CLASS_PREFIX)) { // 自定义类型
            LOGGER.debug("the type of parameter is user-defined, " + paramType);
            handleParameterClassValue((Class) paramType, obj, resourceIdSet);
        }
    }

    /**
     * 方法参数为自定义类型时，依次进行字段判断，找到有@IdPermission注解的变量
     *
     * @param clazz
     * @param obj
     * @param valueSet
     * @throws IllegalAccessException
     */
    public void handleParameterClassValue(Class<?> clazz, Object obj, Set<String> valueSet)
            throws IllegalAccessException, ClassNotFoundException {
        LOGGER.debug("handleParameterClassValue : clazz={}, valueSet={}", clazz, valueSet);
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object fieldValue = field.get(obj);
            Class fieldClazz = field.getType();
            if (fieldValue != null) {
                fieldClazz = fieldValue.getClass(); // 方便泛型处理
            }
            LOGGER.debug("handleParameterClassValue fieldName={}, fieldValue={}, fieldClazz={}",
                    fieldName, fieldValue, fieldClazz);

            Annotation annotation = field.getAnnotation(IdPermission.class);
            if (annotation instanceof IdPermission) {
                if (isPrimitiveType(fieldClazz)) { // 基本类型、字符串
                    valueSet.addAll(processResources((String) fieldValue));
                } else if (checkType(fieldClazz, Collection.class)) {  // List、Set类型
                    Set<String> tmpResourceIdSet = new HashSet<>();
                    List<Object> objectList = new ArrayList<>();
                    processCollectionParameter(fieldValue, tmpResourceIdSet, objectList);

                    // @IdPermission注解只能注解在基础变量上
                    if (CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                        valueSet.addAll(tmpResourceIdSet);
                    }
                } else if (checkType(fieldClazz, Map.class)) { // Map类型
                    Set<String> tmpResourceIdSet = new HashSet<>();
                    List<Object> objectList = new ArrayList<>();
                    processMapParameter(fieldValue, tmpResourceIdSet, objectList);

                    // @IdPermission注解只能注解在基础变量上
                    if (CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                        valueSet.addAll(tmpResourceIdSet);
                    }
                }
                continue;
            }


            if (isPrimitiveType(fieldClazz)) { // 基本类型和字符串
                ;
            } else if (checkType(fieldClazz, Collection.class)) { // List、Set类型
                Set<String> tmpResourceIdSet = new HashSet<>();
                List<Object> objectList = new ArrayList<>();
                processCollectionParameter(fieldValue, tmpResourceIdSet, objectList);

                // 基本类型没有@IdPermission注解不在处理，只处理对象类型
                if (CollectionUtils.isNotEmpty(objectList)) {
                    for (Object value : objectList) {
                        handleParameterClassValue(value.getClass(), value, valueSet);  // 递归调用，查找IdPermission注解
                    }
                }
            } else if (checkType(fieldClazz, Map.class)) { // Map类型
                Set<String> tmpResourceIdSet = new HashSet<>();
                List<Object> objectList = new ArrayList<>();
                processMapParameter(fieldValue, tmpResourceIdSet, objectList);

                // 基本类型没有@IdPermission注解不在处理，只处理对象类型
                if (CollectionUtils.isNotEmpty(objectList)) {
                    for (Object value : objectList) {
                        handleParameterClassValue(value.getClass(), value, valueSet);
                    }
                }
            } else if (fieldClazz.getName().startsWith(PermissionConstant.INTERAL_CLASS_PREFIX)) { // 百度内部自定义类型
                if (fieldClazz.isEnum()) { // 自定义枚举类型不处理。
                    continue;
                }
                handleParameterClassValue(fieldClazz, fieldValue, valueSet); // 递归调用，查找IdPermission注解
            }
        }
    }

    /**
     * 处理 Map类型的变量
     *
     * @param srcObj
     * @param primitiveSet 泛型中基本类型集合
     * @param objList      泛型中对象类型集合
     * @throws ClassNotFoundException
     * @throws IllegalAccessException
     */
    private void processMapParameter(Object srcObj, Set<String> primitiveSet, List<Object> objList)
            throws ClassNotFoundException, IllegalAccessException {
        Map<?, ?> mapValue = (Map<?, ?>) srcObj;
        if (mapValue != null) {
            LOGGER.debug("map keySet={}, valueSet={}", mapValue.keySet(), mapValue.values());
            for (Object value : mapValue.values()) {
                Class valueClazz = value.getClass();

                if (isPrimitiveType(valueClazz)) {
                    primitiveSet.addAll(processResources(value.toString()));
                } else {
                    objList.add(value);
                }
            }
        }
    }

    /**
     * 处理 集合类型的变量
     *
     * @param srcObj
     * @param primitiveSet 泛型中基本类型集合
     * @param objList      泛型中对象类型集合
     * @return
     * @throws ClassNotFoundException
     * @throws IllegalAccessException
     */
    private void processCollectionParameter(Object srcObj, Set<String> primitiveSet, List<Object> objList)
            throws ClassNotFoundException, IllegalAccessException {
        Collection<Object> values = (Collection<Object>) srcObj;
        if (values != null) {
            LOGGER.debug("list values={}", values);
            for (Object value : values) {
                Class valueClazz = value.getClass();

                if (isPrimitiveType(valueClazz)) {
                    primitiveSet.addAll(processResources(value.toString()));
                } else {
                    objList.add(value);
                }
            }
        }
    }

    /**
     * 防止多个id通过逗号分隔拼成字符串
     * 处理逗号分隔的资源id
     *
     * @param resourceIdStr
     * @return
     */
    private List<String> processResources(String resourceIdStr) {
        List<String> resourceIds = new ArrayList<>();
        if (StringUtils.isBlank(resourceIdStr)) {
            return resourceIds;
        }

        if (!resourceIdStr.contains(PermissionConstant.COMMA_SEPARATOR)) {
            resourceIds.add(resourceIdStr);
        } else {
            resourceIds.addAll(Arrays.asList(resourceIdStr.split(PermissionConstant.COMMA_SEPARATOR)));
        }

        return resourceIds;
    }

    /**
     * 判断paramClazz是否是destClazz的子类或相同
     *
     * @param paramClazz
     * @param destClazz
     * @return
     */
    private boolean checkType(Class paramClazz, Class destClazz) {
        boolean result = false;
        if (paramClazz == null || destClazz == null) {
            return result;
        }

        if (destClazz.isAssignableFrom(paramClazz)) {
            result = true;
        }

        return result;
    }

    /**
     * 判断是否是基本类型（包含包装类型））
     *
     * @param paramClazz
     * @return
     */
    private boolean isPrimitiveType(Class<?> paramClazz) {
        if (paramClazz.isPrimitive() || paramClazz.getName().startsWith("java.lang")) {
            return true;
        }
        return false;
    }

    public IAMClient createIamClient(String region) {
        String endpoint = EndpointManager.getInstance().getRegion(region).getEndpoint(IAMClient.SERVICE_NAME);
        IAMClient iamClient = new IAMClient(endpoint);
        iamClient.setSubuserEnabled(true);
        return iamClient;
    }

    private SignatureValidator getSignatureValidator(HttpServletRequest request)
            throws UnsupportedEncodingException {
        LOGGER.debug("PermissionAspect : getSignatureValidator start");
        String securityToken = null;

        String authorization = request.getHeader(BceConstant.AUTHORIZATION);
        String method = request.getMethod();
        String uri = URLDecoder.decode(request.getRequestURI(), "UTF-8");

        Map<String, String> signatureHeaders = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String header = headerNames.nextElement();
            signatureHeaders.put(header, request.getHeader(header));
            if (header.equalsIgnoreCase(SignatureValidator.SECURITY_TOKEN_HEADER)) {
                securityToken = request.getHeader(header);
            }
        }

        Map<String, Object> parameters = new HashMap<>();
        for (Map.Entry<String, String[]> entry : request.getParameterMap().entrySet()) {
            if (entry.getValue().length == 1) {
                parameters.put(entry.getKey(), entry.getValue()[0]);
            } else {
                parameters.put(entry.getKey(), Arrays.asList(entry.getValue()));
            }
            if (entry.getKey().equalsIgnoreCase(SignatureValidator.SECURITY_TOKEN_QUERYSTRING)) {
                securityToken = entry.getValue()[0];
            }
        }

        LOGGER.debug("PermissionAspect : getSignatureValidator success.");
        return new SignatureValidator(authorization, method, uri, signatureHeaders, parameters, securityToken);
    }

}