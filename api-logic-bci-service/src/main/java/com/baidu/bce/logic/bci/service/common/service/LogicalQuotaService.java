package com.baidu.bce.logic.bci.service.common.service;

import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.QuotaKeys;
import com.baidu.bce.logic.bci.service.model.BciQuota;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import com.baidu.bce.user.settings.sdk.model.QuotaBatchRequest;
import com.baidu.bce.user.settings.sdk.model.QuotaRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

@Service
public class LogicalQuotaService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogicalQuotaService.class);

    @Value("${bci.pod.sum.quota:10}")
    protected int podSumQuota;

    @Value("${bci.volume.quota:5}")
    protected int volumeRatio;

    @Value("${bci.nfs.quota:5}")
    protected int nfsRatio;

    @Value("${bci.emptyDir.quota:5}")
    protected int emptyDirRatio;

    @Value("${bci.configFile.quota:5}")
    protected int configFileRatio;

    @Value("${bci.port.quota:5}")
    protected int portRatio;

    @Value("${bci.env.quota:5}")
    protected int envRatio;

    @Value("${bci.datavolume.quota:5}")
    protected int dataVolumeQuota;

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    @Autowired
    private PodDao podDao;

    private String getAccountId() {
        return LogicUserService.getAccountId();
    }

    public BciQuota getBciQuota() {
        int podSumQuotaNum = podSumQuota;
        int volumeRatioNum = volumeRatio;
        int nfsRatioNum = nfsRatio;
        int emptyDirRatioNum = emptyDirRatio;
        int configFileRatioNum = configFileRatio;
        int portRatioNum = portRatio;
        int envRatioNum = envRatio;
        int dataVolumeNum = dataVolumeQuota;
        try {
            LOGGER.info("query quota from resource, userId {}", getAccountId());
            QuotaBatchRequest quotaRequest = new QuotaBatchRequest();
            quotaRequest.setQuotaTypes(new ArrayList<>(Arrays.asList(QuotaKeys.POD,
                    QuotaKeys.VOLUME_RATIO, QuotaKeys.NFS_RATIO, QuotaKeys.EMPTY_DIR_RATIO, QuotaKeys.CONFIG_FILE_RATIO,
                    QuotaKeys.PORT_RATIO, QuotaKeys.ENV_RATIO)));
            quotaRequest.setUserType(QuotaRequest.UserType.AccountId);
            quotaRequest.setUserValue(getAccountId());
            UserSettingsClient userSettingsClient = logicPodClientFactory.createUserSettingsClient(getAccountId());
            Map<String, String> quotaMap = userSettingsClient.getBatchQuota(quotaRequest).getQuotaType2quota();
            if (quotaMap.containsKey(QuotaKeys.POD)) {
                podSumQuotaNum = Integer.parseInt(quotaMap.get(QuotaKeys.POD));
            }
            if (quotaMap.containsKey(QuotaKeys.VOLUME_RATIO)) {
                volumeRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.VOLUME_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.NFS_RATIO)) {
                nfsRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.NFS_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.EMPTY_DIR_RATIO)) {
                emptyDirRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.EMPTY_DIR_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.CONFIG_FILE_RATIO)) {
                configFileRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.CONFIG_FILE_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.DATA_VOLUME_QUOTA)) {
                dataVolumeNum = Integer.parseInt(quotaMap.get(QuotaKeys.DATA_VOLUME_QUOTA));
            }
            if (quotaMap.containsKey(QuotaKeys.PORT_RATIO)) {
                portRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.PORT_RATIO));
            }
            if (quotaMap.containsKey(QuotaKeys.ENV_RATIO)) {
                envRatioNum = Integer.parseInt(quotaMap.get(QuotaKeys.ENV_RATIO));
            }
        } catch (Exception ex) {
            LOGGER.error("get quota messge from resource error: {}", ex);
        }

        // 统一计费的pod暂时不计入任何账户的 quota
        int podNum = podDao.getCreatedPodInQuota(getAccountId());

        BciQuota bciQuota = new BciQuota();
        bciQuota.setPodTotal(podSumQuotaNum);
        bciQuota.setVolumeRatio(volumeRatioNum);
        bciQuota.setEmptyDirRatio(emptyDirRatioNum);
        bciQuota.setNfsRatio(nfsRatioNum);
        bciQuota.setConfigFileRatio(configFileRatioNum);
        bciQuota.setEnvRatio(envRatioNum);
        bciQuota.setPodCreated(podNum);
        bciQuota.setPortRatio(portRatioNum);
        bciQuota.setDataVolumeQuota(dataVolumeNum);

        return bciQuota;
    }
}
