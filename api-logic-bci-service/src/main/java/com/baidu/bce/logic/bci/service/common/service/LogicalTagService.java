package com.baidu.bce.logic.bci.service.common.service;

import com.baidu.bce.logic.bci.dao.common.model.Label;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.util.JsonUtil;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service
public class LogicalTagService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogicalTagService.class);

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    /**
     * 获取以resourceUuid为key的tag列表Map
     * 报错降级
     * @param resourceUuids 资源id
     * @return 以resourceUuid为key的tag列表Map
     */
    private Map<String, List<Tag>> queryTagsByResourceUuids(List<String> resourceUuids) {
        Map<String, List<Tag>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(resourceUuids)) {
            return result;
        }

        try {
            FullTagListRequest request = new FullTagListRequest();
            request.setResourceUuids(resourceUuids);
            LogicalTagClient tagClient = logicPodClientFactory.createLogicalTagClient(getAccountId());
            TagAssociationFulls response = tagClient.listFullTags(request);
            for (TagAssociationFull tagFull : response.getTagAssociationFulls()) {
                String resourceUuid = tagFull.getResourceUuid();
                Tag tag = new Tag();
                tag.setTagKey(tagFull.getTagKey());
                tag.setTagValue(tagFull.getTagValue());
                if (result.containsKey(resourceUuid)) {
                    result.get(resourceUuid).add(tag);
                } else {
                    List<Tag> temp = new ArrayList<>();
                    temp.add(tag);
                    result.put(resourceUuid, temp);
                }
            }
        } catch (Exception e) {
            LOGGER.error("queryTagsByResourceUuids error:{}", e);
        }
        return result;
    }

    public void addTagInfoToPodPO(List<PodPO> podPOS) {
        List<String> podUuids = new ArrayList<>();
        for (PodPO pod : podPOS) {
            podUuids.add(pod.getPodUuid());
        }
        Map<String, List<Tag>> tagMap = queryTagsByResourceUuids(podUuids);
        for (PodPO pod : podPOS) {
            pod.setPodTags(tagMap.get(pod.getPodUuid()));
            pod.setLabels(JsonUtil.toList(pod.getTags(), Label.class));
        }
    }
}
