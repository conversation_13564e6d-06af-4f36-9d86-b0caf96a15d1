package com.baidu.bce.logic.bci.service.sync.service;

import com.baidu.bce.internalsdk.bci.model.PodStatus;
import com.baidu.bce.logic.bci.dao.chargestatus.PodChargeStatusDao;
import com.baidu.bce.logic.bci.dao.chargestatus.model.PodChargeStatus;
import com.baidu.bce.logic.bci.dao.container.ContainerDao;
import com.baidu.bce.logic.bci.dao.container.model.ContainerPO;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.BciStatus;
import com.baidu.bce.logic.bci.service.constant.ChargeStatus;
import com.baidu.bce.logic.bci.service.model.ContainerCurrentState;
import com.baidu.bce.logic.bci.service.model.ContainerPreviousState;
import com.baidu.bce.logic.bci.service.util.JsonUtil;
import com.baidu.bce.logic.bci.service.util.PodUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class SyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SyncService.class);

    @Autowired
    protected PodDao podDao;

    @Autowired
    protected ContainerDao containerDao;

    @Autowired
    protected PodChargeStatusDao podChargeStatusDao;

    @Autowired
    protected LogicPodClientFactory logicPodClientFactory;

    private static final Long TIME_SECOND = 10000000000L;
    private static final Long UTCTime = 8 * 60 * 60 * 1000L;

    protected boolean syncPod(List<PodStatus> podStatusList) {
        boolean updateSince = true;
        if (CollectionUtils.isEmpty(podStatusList)) {
            updateSince = false;
            LOGGER.debug("since info is empty, skip sync-pod");
            return updateSince;
        }
        // 这里 since 接口可能返回一些不在 console 端记录的pod，所以以数据库为准，进行循环
        // 这样也可以通过 resource ID、podUUID 判断订单是否已经created
        // 减少读库操作，性能也更好一些
        List<PodPO> podPOList = podDao.listAllPods();
        for (PodPO podPO : podPOList) {
            // 订单循环还没更新podUUID，为了保证下次能继续查询，不更新 since；同时不阻塞其他pod
            if (podPO.getStatus().equalsIgnoreCase(BciStatus.PENDING.getStatus()) &&
                    ( podPO.getPodId().equalsIgnoreCase(podPO.getPodUuid()) ||
                    StringUtils.isEmpty(podPO.getResourceUuid()) )) {
                LOGGER.debug("order {} is still creating", podPO.getOrderId());
                updateSince = false;
                continue;
            }
            try {
                for (PodStatus podStatus : podStatusList) {
                    if (podPO.getPodUuid().equalsIgnoreCase(podStatus.getPodUuid())) {
                        chargeStatus(podStatus, podPO);

                        // status 和 updateTime 都相等，说明之前更新过，这里跳过
                        // TODO 如果pod在同一秒内有两次状态变化，这里就有可能漏掉最新的状态。
                        //  而且这个变化，可能是 container 的变化，无法从pod的内容判断出来。
                        //  这里也可以改造成并发任务可以降低同步时延，但是没法解决数据库重复写的问题。
                        if (podPO.getStatus().equalsIgnoreCase(BciStatus.getStatus(podStatus.getStatus())) &&
                                podPO.getUpdatedTime().equals(getUpdateTime(podStatus.getSince()))) {
                            LOGGER.debug("pod: {} already sync, skip this pod", podPO.getPodId());
                            break;
                        }
                        LOGGER.debug("BCI_LIFECYCLE_SYNC_POD order:{}", podPO.getOrderId());
                        LOGGER.debug("podId: {}, pod time : {}, status time : {}, fromStatus: {}, toStatus: {}",
                                podPO.getPodId(),
                                podPO.getUpdatedTime(), getUpdateTime(podStatus.getSince()),
                                podPO.getStatus(), BciStatus.getStatus(podStatus.getStatus()));
                        podPO.setStatus(BciStatus.getStatus(podStatus.getStatus()));
                        // todo since 接口时间都是GMT时间，这里时间加8小时处理了，但是入库还是 GMT 时间，暂时还没搞明白
                        podPO.setUpdatedTime(getUpdateTime(podStatus.getSince()));
                        syncContainer(podPO.getPodId(), podStatus.getPodUuid(), podStatus);
                        podDao.updateSinceInfo(podPO);
                        break;
                    }
                }
            } catch (Exception e) {
                // 这里加argus报警
                LOGGER.debug("failed to sync pod status from nova");
                LOGGER.debug("exception detail: {}", e);
                // catch异常是为了不影响sync其他pod，但是updateSince时间还是保持不变
                updateSince = false;
            }
        }

        return updateSince;
    }

    protected void syncPod(PodStatus podStatus, PodPO podPO) {
        if (podStatus == null || podPO == null) {
            return;
        }
        chargeStatus(podStatus, podPO);

        podPO.setStatus(BciStatus.getStatus(podStatus.getStatus()));
        podPO.setUpdatedTime(getUpdateTime(podStatus.getSince()));
        syncContainer(podPO.getPodId(), podPO.getPodUuid(), podStatus);
    }

    protected void syncContainer(String id, String uuid, PodStatus podStatus) {
        List<ContainerPO> containers = containerDao.listByPodId(uuid);
        if (CollectionUtils.isEmpty(containers)) {
            LOGGER.debug("cannot find container by id: {}, try: {}", uuid, id);
            containers = containerDao.listByPodId(id);
        }
        Map<String, PodStatus.ContainerStatus> containerMap = new HashMap<>();
        if (CollectionUtils.isEmpty(podStatus.getContainerStatus())) {
            LOGGER.error("container status response is null, containerStatus: {}", podStatus.getContainerStatus());
            return;
        }
        for (PodStatus.ContainerStatus containerStatus : podStatus.getContainerStatus()) {
            containerMap.put(containerStatus.getName(), containerStatus);
        }
        for (ContainerPO container : containers) {
            if (containerMap.containsKey(container.getName())) {
                PodStatus.ContainerStatus containerStatus = containerMap.get(container.getName());
                container.setPreviousState(JsonUtil.toJSON(
                        PodUtils.convertBean(containerStatus.getPreviousState(), ContainerPreviousState.class)));
                container.setCurrentState(JsonUtil.toJSON(
                        PodUtils.convertBean(containerStatus.getCurrentState(), ContainerCurrentState.class)));
                container.setRestartCount(containerStatus.getRestartCount());
                container.setContainerUuid(containerStatus.getContainerId());
                container.setPodUuid(podStatus.getPodUuid());
                container.setUpdatedTime(new Timestamp(new Date().getTime()));
            }
        }
        containerDao.batchUpdate(containers);
    }

    private Timestamp getUpdateTime(long since) {
        if (since < TIME_SECOND) {
            since = since * 1000;
        }
        return new Timestamp(Long.parseLong(String.valueOf(since + UTCTime)));
    }

    private void chargeStatus(PodStatus podStatus, PodPO podPO) {
        if (podPO.getStatus().equalsIgnoreCase(podStatus.getStatus())) {
            return;
        }
        String currentChargeStatus = ChargeStatus.getStatus(podStatus.getStatus().toLowerCase());
        PodChargeStatus podChargeStatus = new PodChargeStatus();
        podChargeStatus.setPodUuid(podStatus.getPodUuid());
        podChargeStatus.setPreviousState(podPO.getStatus());
        podChargeStatus.setCurrentState(podStatus.getStatus());
        podChargeStatus.setChargeState(currentChargeStatus);
        podChargeStatus.setCreatedTime(getUpdateTime(podStatus.getSince()));
        // todo 虽然设置了时间，但是sql里是current time，后面改计费的时候再看。。。
        podChargeStatusDao.insert(podChargeStatus);
    }

    public void chargeStatus(PodPO podPO) {
        String currentChargeStatus = ChargeStatus.getStatus("running");
        PodChargeStatus podChargeStatus = new PodChargeStatus();
        podChargeStatus.setPodUuid(podPO.getPodUuid());
        podChargeStatus.setPreviousState(podPO.getStatus());
        podChargeStatus.setCurrentState("Running");
        podChargeStatus.setChargeState(currentChargeStatus);
        podChargeStatus.setCreatedTime(podPO.getCreatedTime());
        // todo 虽然设置了时间，但是sql里是current time，后面改计费的时候再看。。。
        podChargeStatusDao.insert(podChargeStatus);
    }
}
