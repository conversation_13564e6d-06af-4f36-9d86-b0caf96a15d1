package com.baidu.bce.logic.bci.service.interceptor;

import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class ActivateRoleForUserInterceptor extends HandlerInterceptorAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivateRoleForUserInterceptor.class);

    private LogicPodClientFactory logicPodClientFactory;
    private String roleName;
    private String policyId;
    private String podServiceAccountId;

    public ActivateRoleForUserInterceptor(LogicPodClientFactory logicPodClientFactory,
                                          String roleName, String policyId, String podServiceAccountId) {
        this.logicPodClientFactory = logicPodClientFactory;
        this.roleName = roleName;
        this.policyId = policyId;
        this.podServiceAccountId = podServiceAccountId;
    }

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
        IAMClient iamClient = logicPodClientFactory.createIAMClientWithConsoleToken();
        iamClient.stsServiceRoleActivate(roleName, logicPodClientFactory.getAccountId(), policyId, podServiceAccountId);
        LOGGER.debug("activate role for user, rolename:{}, accountId:{}, policyId:{}, asServiceAccountId:{}",
                roleName, logicPodClientFactory.getAccountId(), policyId, podServiceAccountId);
        return true;
    }
}
