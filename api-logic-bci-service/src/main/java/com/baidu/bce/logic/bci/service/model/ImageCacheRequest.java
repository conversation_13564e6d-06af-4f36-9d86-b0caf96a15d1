package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ImageCacheRequest {
    @NotBlank(message = "image cannot be null")
    private String image;
    private ImageRegistrySecret imageSecret;
    private String availableZone = "";
}
