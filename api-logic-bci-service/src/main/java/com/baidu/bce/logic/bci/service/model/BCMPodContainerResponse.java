package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BCMPodContainerResponse {
    private String orderBy = "";
    private String order = "";
    private int pageNo = 1;
    private int pageSize = 0;
    private int totalCount = 0;
    protected List<PodContainerForBCM> result;
}
