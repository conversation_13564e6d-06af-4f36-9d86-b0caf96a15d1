package com.baidu.bce.logic.bci.service.common.service;

import com.baidu.bce.asyncwork.sdk.asyncaop.BceAsyncWork;
import com.baidu.bce.asyncwork.sdk.model.Level;
import com.baidu.bce.internalsdk.bci.CceImageClient;
import com.baidu.bce.internalsdk.bci.ContainerManagerClient;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.model.CNImageCacheRequest;
import com.baidu.bce.internalsdk.bci.model.ImageCacheTaskStatus;
import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.zone.model.ZoneAndAuthorityList;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.service.model.BciQuota;
import com.baidu.bce.logic.bci.service.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.service.util.PodValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class BciAsyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BciAsyncService.class);

    @Autowired
    private LogicalPodEipService logicalPodEipService;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private LogicalZoneResourceService logicalZoneResourceService;

    @BceAsyncWork(name = "getEipInstanceMapAsync", level = Level.MIDDLE)
    public Map<String, EipInstance> getEipInstanceMapAsync() {
        return logicalPodEipService.getBciEipMap();
    }

    @BceAsyncWork(name = "validateBciParameters", level = Level.MIDDLE)
    public Boolean validateBciParameters(PodPurchaseRequest podPurchaseRequest, BciQuota bciQuota) {
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
        return true;
    }

    @BceAsyncWork(name = "validateAndSetSubnetUuid", level = Level.MIDDLE)
    public Boolean validateAndSetSubnetUuid(PodPurchaseRequest podPurchaseRequest, ZoneMapDetail zoneMapVo,
                                            boolean isCreateWithEip) {
        podValidator.validateAndSetSubnetUuid(podPurchaseRequest, zoneMapVo, isCreateWithEip);
        return true;
    }

    @BceAsyncWork(name = "getZoneResourceDetail", level = Level.MIDDLE)
    public ZoneAndAuthorityList getZoneResourceDetail() {
        return logicalZoneResourceService.listZoneResourceV2();
    }

    @BceAsyncWork(name = "getImageTags", level = Level.MIDDLE)
    public ImageTags getImageTags(UserImage image, CceImageClient cceImageClient) {
        return cceImageClient.listImageVersions(image.getNamespace(), image.getName());
    }

    @BceAsyncWork(name = "pushLog", level = Level.MIDDLE)
    public Boolean pushLog(String podUuid, PodClient podClient) {
        podClient.pushLog(podUuid);
        return Boolean.TRUE;
    }

    @BceAsyncWork(name = "createImageCache", level = Level.MIDDLE)
    public void createImageCache(CNImageCacheRequest request, ContainerManagerClient client) {
        client.createImageCache(request);
    }


    @BceAsyncWork(name = "queryImageCache", level = Level.MIDDLE)
    public ImageCacheTaskStatus queryImageCache(String taskId, ContainerManagerClient client) {
        return client.getImageCache(taskId);
    }
}
