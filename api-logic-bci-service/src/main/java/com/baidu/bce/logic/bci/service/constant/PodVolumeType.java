package com.baidu.bce.logic.bci.service.constant;

public enum PodVolumeType {

    NFS(1,"NFS"),
    EMPTY_DIR(2,"EmptyDir"),
    CONFIG_FILE(3, "ConfigFile");

    private int id;
    private String type;

    PodVolumeType(int id, String type) {
        this.id = id;
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public String getType() {
        return type;
    }

    public static String getStatus(int id) {
        for (PodVolumeType podStatus : values()) {
            if (podStatus.getId() == id) {
                return podStatus.getType();
            }
        }
        return null;
    }
}
