package com.baidu.bce.logic.bci.service.log;

import com.baidu.bce.logic.bci.service.model.OrderUuidResults;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 实例操作相关日志关键词
 * 可根据一下关键词进行日志查询：
 * InstanceOperation-instanceId
 * InstanceOperation-instanceUuid
 * InstanceOperation-orderUuid
 * Created by huangshanqi on 2017/9/27.
 */
public class LogUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogUtil.class);

    public static void operationLog(Object content, BaseOperation operation, String uuid) {
        operationLog(content, operation, Arrays.asList(uuid));
    }

    public static void operationLog(Object content, BaseOperation operation, Collection<String> instanceIds) {
        LOGGER.info(generateInstanceOperationLogString(operation, instanceIds));
        LOGGER.info("{} operation={}", operation, content);
    }

    public static void operationLog(Object content, BaseOperation operation, Collection<String> instanceIds,
                                    String orderUuid) {
        operationLog(content, operation, instanceIds, Arrays.asList(orderUuid));
    }

    public static void operationLog(Object content, BaseOperation operation, Collection<String> instanceIds,
                                    Collection<String> orderUuids) {
        List<String> uuids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(instanceIds)) {
            uuids.addAll(instanceIds);
        }
        if (CollectionUtils.isNotEmpty(orderUuids)) {
            uuids.addAll(orderUuids);
        }
        LOGGER.info(generateInstanceOperationLogString(operation, uuids));
        LOGGER.info("{} operation={}", operation, content);
    }

    private static String generateInstanceOperationLogString(BaseOperation operation, Collection<String> keyIds ) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            for (String keyId : keyIds) {
                stringBuilder.append(operation.toString()).append("-").append(keyId).append(",");
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        } catch (Exception e) {
            return stringBuilder.toString();
        }
        return stringBuilder.toString();
    }

    public static List<String> getOrderResultsIds(OrderUuidResults orderUuidResults) {
        List<String> ids = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(orderUuidResults)) {
                for (OrderUuidResult orderUuidResult : orderUuidResults) {
                    ids.add(orderUuidResult.getOrderId());
                }
            }
        } catch (Exception e) {
            LOGGER.error("getResizeVolumeIds", e);
        }
        return ids;
    }
}
