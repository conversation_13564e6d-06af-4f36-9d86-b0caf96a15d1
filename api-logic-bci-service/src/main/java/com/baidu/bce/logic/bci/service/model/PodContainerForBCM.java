package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PodContainerForBCM implements Cloneable {
    private String podId = "";
    private String podUuid = "";
    private String status = "";
    private List<Container> containers;

    @Data
    public static class Container {
        private String id;
        private String containerName;
    }
}
