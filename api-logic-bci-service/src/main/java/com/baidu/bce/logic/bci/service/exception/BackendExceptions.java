package com.baidu.bce.logic.bci.service.exception;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

public class BackendExceptions {

    public static class RequestLockException extends BceException {
        public RequestLockException() {
            super("resource is in task");
        }
    }

    public static class ResourceInUseException extends BceException {
        public ResourceInUseException(BceInternalResponseException exception) {
            super("resource is in use");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class RequestInvalidException extends BceException {
        public RequestInvalidException(BceInternalResponseException exception) {
            super("request is invalid");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PermissionDenyException extends BceException {
        public PermissionDenyException(BceInternalResponseException exception) {
            super("permission deny");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ResourceNotExistException extends BceException {
        public ResourceNotExistException(BceInternalResponseException exception) {
            super("resource is not existed");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class OperationTypeErrorException extends BceException {
        public OperationTypeErrorException(BceInternalResponseException exception) {
            super("operation type is incorrect");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class OperationErrorException extends BceException {
        public OperationErrorException(BceInternalResponseException exception) {
            super("operation is incorrect");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ExceedLimitException extends BceException {
        public ExceedLimitException(BceInternalResponseException exception) {
            super("request is exceed limit");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class BCCInternalException extends BceException {
        public BCCInternalException(BceInternalResponseException exception) {
            super("bcc service internal exception occurs, please retry later");
            setHttpStatus(exception.getHttpStatus());
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class OrderPayFailedException extends BceException {
        public OrderPayFailedException() {
            super("payment failed, please check remaining balance", HttpStatus.ERROR_OPERATION_DENY, "PaymentFailed");
        }
    }

    public static class MissResourceAccountIdException extends BceException {
        public MissResourceAccountIdException() {
            super("The resource account id is missing",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "MissResourceAccountId");
        }
    }

    public static class MissChargeAuthorizationException extends BceException {
        public MissChargeAuthorizationException() {
            super("The charge authorization id is missing",
                    HttpStatus.ERROR_INPUT_INVALID,
                    "MissChargeAuthorization");
        }
    }

    public static class MissApplicationException extends BceException {
        public MissApplicationException() {
            super("The application is missing", HttpStatus.ERROR_INPUT_INVALID,
                    "MissResourceAccountId");
        }
    }

    public static class ResourceAccountException extends BceException {
        public ResourceAccountException() {
            super("Resource account id does not match paas-application ", HttpStatus.ERROR_INPUT_INVALID,
                    "ResourceAccount");
        }
    }

    public static class DecryptionException extends BceException {
        public DecryptionException() {
            super("Invalid encryption code found for the cipher.", HttpStatus.ERROR_INPUT_INVALID,
                    "Decryption");
        }
    }

    public static class ResourceAccountIdDecryptTimeOutException extends BceException {
        public ResourceAccountIdDecryptTimeOutException() {
            super("Resource account id decrypt time out.", HttpStatus.ERROR_INPUT_INVALID,
                    "Instance.ResourceAccountIdTimeOutException");
        }
    }
}
