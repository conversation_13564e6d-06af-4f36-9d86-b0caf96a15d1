package com.baidu.bce.logic.bci.service.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfiguration {
    @Value("${thread.pool.core.size:100}")
    private Integer threadPoolCoreSize;
    @Value("${thread.pool.max.size:350}")
    private Integer threadPoolMaxSize;
    @Value("${thread.pool.queue.capacity:50000}")
    private Integer threadPoolQueueCapacity;

    @Bean(name = "theadPoolTaskExecutor")
    public ThreadPoolTaskExecutor theadPoolTaskExecutor() {
        ThreadPoolTaskExecutor t = new ThreadPoolTaskExecutor();
        t.setCorePoolSize(threadPoolCoreSize);
        t.setMaxPoolSize(threadPoolMaxSize);
        t.setQueueCapacity(threadPoolQueueCapacity);
        t.setKeepAliveSeconds(60);
        return t;
    }
}
