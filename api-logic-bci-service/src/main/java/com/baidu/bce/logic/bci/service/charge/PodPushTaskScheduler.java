package com.baidu.bce.logic.bci.service.charge;

import com.baidu.bce.logic.bci.service.charge.service.PodPushTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@EnableScheduling
@Configuration
@Profile("default")
public class PodPushTaskScheduler {

    @Autowired
    private PodPushTaskService podPushTaskService;

    @Scheduled(cron = "0 0 0 1/1 * ? ")
    public void deleteRecord() {
        podPushTaskService.deleteRecord();
    }

    @Scheduled(cron = "0/20 * * * * ? ")
    public void runScheduledTask() {
        podPushTaskService.createPushTask();
    }

}
