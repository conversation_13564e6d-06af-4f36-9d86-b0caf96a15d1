package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "serviceType", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = PodPurchaseRequest.class, name = "BCI"),
        @JsonSubTypes.Type(value = EipPurchaseRequest.class, name = "EIP")})
public interface IOrderItem {

    String getServiceType();

    void setServiceType(String serviceType);

    String getProductType();

    void setProductType(String productType);
}
