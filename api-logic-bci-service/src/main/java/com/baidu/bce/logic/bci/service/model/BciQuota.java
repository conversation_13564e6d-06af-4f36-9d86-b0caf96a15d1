package com.baidu.bce.logic.bci.service.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BciQuota {

    private int podTotal;
    private int podCreated;
    private int volumeRatio;
    private int nfsRatio;
    private int emptyDirRatio;
    private int configFileRatio;
    private int dataVolumeQuota = 5;
    private int envRatio;
    private int portRatio;
}
