package com.baidu.bce.logic.bci.service.common.service;

import com.baidu.bce.internalsdk.eip.EipLogicalClient;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.eip.model.EipListResponse;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;

@Service
public class LogicalPodEipService {

    public static final Logger LOGGER = LoggerFactory.getLogger(LogicalPodEipService.class);

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    public Map<String, EipInstance> getBciEipMap() {
        return getEipMap(PodConstants.SERVICE_TYPE);
    }

    public Map<String, EipInstance> getEipMap(String serviceType) {
        List<EipInstance> eipInstances = getBackendEips(null, null, null, serviceType);
        Map<String, EipInstance> eipMap = new HashMap<>();
        for (EipInstance eipInstance : eipInstances) {
            eipMap.put(eipInstance.getInstanceLongId(), eipInstance);
        }
        return eipMap;
    }

    public List<EipInstance> getBackendEips(String eipStatus, String eipName, String eip, String serviceName) {
        List<EipInstance> eipInstances = null;
        EipLogicalClient eipLogicalClient = logicPodClientFactory.createEipClient(getAccountId());
        EipListResponse eipListResponse = eipLogicalClient.listEip("", eipStatus, serviceName,
                "", "", 1000);
        if (eipListResponse == null || CollectionUtils.isEmpty(eipListResponse.getEipList())) {
            return new LinkedList<>();
        }
        eipInstances = eipListResponse.getEipList();
        for (Iterator<EipInstance> iter = eipInstances.iterator(); iter.hasNext(); ) {
            EipInstance eipInstance = iter.next();
            if (StringUtils.isNotBlank(eipName) && !eipInstance.getName().contains(eipName)) {
                iter.remove();
                continue;
            }
            if (StringUtils.isNotBlank(eip) && !eipInstance.getEip().contains(eip)) {
                iter.remove();
                continue;
            }
        }
        return eipInstances;
    }
}
