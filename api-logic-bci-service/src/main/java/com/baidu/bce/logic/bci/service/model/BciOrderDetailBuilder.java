package com.baidu.bce.logic.bci.service.model;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.PriceType;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.bci.daov2.podextrav2.PodExtraDaoV2;
import com.baidu.bce.logic.bci.daov2.podextrav2.model.PodExtraPO;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.util.PodUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static com.baidu.bce.logic.bci.service.util.PodUtils.BCI_ORDER_ITEM_KEY_PREFIX;

@Service
@Data
public class BciOrderDetailBuilder extends BciAbstractDetailBuilder {

    public static final Logger LOGGER = LoggerFactory.getLogger(BciOrderDetailBuilder.class);

    @Autowired
    private PodExtraDaoV2 podExtraDaoV2;

    private BciOrderExtra orderExtra = new BciOrderExtra();

    public boolean initOrderExtra(Order order) {
        this.order = order;
        return getOrderExtra();
    }

    @Override
    protected String serviceType() {
        return "BCI;BCI__EIP";
    }

    public boolean getOrderExtra() {
        try {
            Order.Item orderItem = order.getItems().get(0);
            String extra = orderItem.getExtra();
            ObjectMapper objectMapper = new ObjectMapper();
            String orderItemKey = orderItem.getKey();
            if (StringUtils.isNotEmpty(orderItemKey)) {
                if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType()) &&
                        orderItem.getKey().startsWith(BCI_ORDER_ITEM_KEY_PREFIX)) { // 去除eip的order item
                    String podId = PodUtils.parsePodIdFromOrderItem(orderItem);
                    if (StringUtils.isNotEmpty(podId)) {
                        PodExtraPO podExtraPO = podExtraDaoV2.getPodExtraByPodIdIgnoreStatus(podId);
                        if (podExtraPO != null && !StringUtils.isEmpty(podExtraPO.getOrderExtra())) {
                            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                            orderExtra = objectMapper.readValue(podExtraPO.getOrderExtra(), BciOrderExtra.class);
                            orderItem.setExtra(podExtraPO.getOrderExtra());
                            return true;
                        }
                    }
                }
            }
            orderExtra = objectMapper.readValue(extra, BciOrderExtra.class);
        } catch (IOException e) {
            LOGGER.error("订单extra解析失败 exception:", e);
            return false;
        }
        return true;
    }

    @Override
    protected List<String> chargeType(Order.Item item) {
        String type = item.getProductType().equalsIgnoreCase("postpay") ? PriceType.CPT1.name() : PriceType.CPT2.name();
        return Arrays.asList(type);
    }

    @Override
    protected List<String> configuration(Order.Item item) {
        return new LinkedList<>();
    }

    @Override
    protected String itemServiceType() {
        return ServiceType.BBC.name();
    }

    /**
     * 显示可用区域 (从Item的Flavor里取出logicalZone)
     */
    @Override
    protected String logicalZone(Order.Item item) {
        return orderExtra.getLogicalZone();
    }
}

