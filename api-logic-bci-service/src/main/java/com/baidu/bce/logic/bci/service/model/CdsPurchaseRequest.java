package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class CdsPurchaseRequest {
    private List<DiskConfig> diskConfigs;

    private boolean autoRenew   ;
    private String autoRenewTimeUnit = "month";
    private int autoRenewTime = 0;
    private Integer month;
    private Integer purchaseLength;
    private String productType;

    @Data
    public static class DiskConfig {
        private Integer sizeInGB;
        private String snapshotId;
        private String storageType;
        private String name;
        private String podVolumeType;
    }
}
