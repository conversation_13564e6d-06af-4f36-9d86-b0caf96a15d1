package com.baidu.bce.logic.bci.service.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
public class ContainerPreviousState {
    private String state;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Date containerStartTime;
    private int exitCode;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Date containerFinishTime;
    private String detailStatus;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getContainerStartTime() {
        return containerStartTime;
    }

    public void setContainerStartTime(Date containerStartTime) {
        this.containerStartTime = containerStartTime;
    }

    public int getExitCode() {
        return exitCode;
    }

    public void setExitCode(int exitCode) {
        this.exitCode = exitCode;
    }

    public Date getContainerFinishTime() {
        return containerFinishTime;
    }

    public void setContainerFinishTime(Date containerFinishTime) {
        this.containerFinishTime = containerFinishTime;
    }

    public String getDetailStatus() {
        return detailStatus;
    }

    public void setDetailStatus(String detailStatus) {
        this.detailStatus = detailStatus;
    }
}
