package com.baidu.bce.logic.bci.service.exception;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

/**
 * VPC异常
 */

public class VpcExceptions extends CommonExceptions {
    public static class NetworkInfoErrorExceptions extends BceException {
        public NetworkInfoErrorExceptions() {
            super("vpcId or subnetId info error", HttpStatus.ERROR_INPUT_INVALID, "Vpc.NetworkParamError");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class NewFixIpNotInSubnetExceptions extends BceException {
        public NewFixIpNotInSubnetExceptions() {
            super("new private ip is not in the subnet.", HttpStatus.ERROR_INPUT_INVALID, "Vpc.NewFixIpNotInSubnet");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class IpInSubnetNotEnoughExceptions extends BceException {
        public IpInSubnetNotEnoughExceptions() {
            super("The ip left in the subnet is not enouth for this create",
                    HttpStatus.ERROR_INPUT_INVALID, "Vpc.IpInSubnetNotEnoughExceptions");
            setRequestId(LogicUserService.getRequestId());
        }
    }
}
