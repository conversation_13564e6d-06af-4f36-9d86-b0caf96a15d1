package com.baidu.bce.logic.bci.service.pod;

import com.baidu.bce.externalsdk.logical.network.common.model.OrderDetailRequest;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.PricingClientV3;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.bci.service.model.BciOrderDetailBuilder;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.pricing.service.model.common.Flavor;
import com.baidu.bce.pricing.service.model.common.FlavorItem;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

import static com.baidu.bce.logic.core.user.LogicUserService.getAccountId;


@Service
public class PodOrderService {

    public static final Logger LOGGER = LoggerFactory.getLogger(PodOrderService.class);

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;
    @Autowired
    private  BciOrderDetailBuilder bciOrderDetailBuilder;

    @Autowired
    private RegionConfiguration regionConfiguration;

    public Order getBciOrderDetail(OrderDetailRequest request) {
        if (org.apache.commons.lang.StringUtils.isEmpty(request.getUuid())) {
            throw new PodExceptions.RequestInvalidException();
        }

        Order order = getOrderByUuid(request.getUuid());
        bciOrderDetailBuilder.initOrderExtra(order);
        order = bciOrderDetailBuilder.build(request.getKey());

        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setGroupingUsed(false);
        numberFormat.setMaximumFractionDigits(7);
        PricingClientV3 client = logicPodClientFactory.createGlobalPriceClient();
        for (Order.Item item : order.getItems()) {
            if (LogicalConstant.PRODUCT_TYPE_POSTPAY.equalsIgnoreCase(item.getProductType())) {
                int purchaseNum = item.getCount();
                BigDecimal totalPrice = getPrice(client, item.getFlavor());
                if (purchaseNum > 1) {
                    totalPrice = totalPrice.divide(new BigDecimal(purchaseNum), LogicalConstant.DIVIDE_SCALE,
                            BigDecimal.ROUND_HALF_UP);
                }
                item.setUnitPrice(totalPrice);
                String unitPriceShow = "￥" + numberFormat.format(item.getUnitPrice()) + "/秒/台";

                item.setUnitPriceShow(unitPriceShow);
            }
        }

        return order;
    }

    /**
     * 从后端查询订单
     *
     * @param orderUuid
     * @return
     */
    public Order getOrderByUuid(String orderUuid) {
        Order order = null;
        try {
            OrderClient orderClient = logicPodClientFactory.createOrderClient();
            if (StringUtils.isEmpty(orderUuid)) {
                // orderID 是空
                LOGGER.debug("orderID is empty");
                return order;
            }
            order = orderClient.get(orderUuid);
        } catch (Exception e) {
            LOGGER.info("getOrderByUuid error with orderUuid = {}, error = {}", orderUuid, e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        if (!getAccountId().equals(order.getAccountId())) {
            throw new CommonExceptions.ResourceNotExistException();
        }

        return order;
    }

    /**
     * 查询定价
     */
    private BigDecimal getPrice(PricingClientV3 client, com.baidu.bce.internalsdk.order.model.Flavor flavor) {
        BigDecimal totalPrice;

        BigDecimal cpu = new BigDecimal("0");
        BigDecimal memory = new BigDecimal("0");
        for (com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem flavorItem : flavor) {
            if (LogicalConstant.CPU.equalsIgnoreCase(flavorItem.getName())) {
                cpu = flavorItem.getScale();
            }

            if (LogicalConstant.MEMORY.equalsIgnoreCase(flavorItem.getName())) {
                memory = flavorItem.getScale();
            }
        }
        try {
            Flavor priceFlavor = new Flavor();
            List<FlavorItem> flavorItems = new ArrayList<>();

            FlavorItem item;
            item = new FlavorItem();
            item.setName(LogicalConstant.CPU);
            item.setValue("1");
            item.setScale(cpu);
            flavorItems.add(item);

            item = new FlavorItem();
            item.setName(LogicalConstant.MEMORY);
            item.setValue("1");
            item.setScale(memory);
            flavorItems.add(item);

            priceFlavor.setFlavorItems(flavorItems);

            totalPrice = client.getCpcFlavorPrice(regionConfiguration.getCurrentRegion(), PodConstants.SERVICE_TYPE,
                    DateTime.now(), "CPC_flavor", priceFlavor, new BigDecimal(1), 1, getAccountId());
        } catch (Exception e) {
            throw new PodExceptions.UserPriceConfigurationNotExist();
        }
        return totalPrice;
    }
}
