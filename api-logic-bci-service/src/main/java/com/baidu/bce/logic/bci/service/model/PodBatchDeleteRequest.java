package com.baidu.bce.logic.bci.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Accessors(chain = true)
public class PodBatchDeleteRequest {
    private List<DeletePod> deletePods;
    private Boolean relatedReleaseFlag = Boolean.FALSE;

}
