package com.baidu.bce.logic.bci.service.sync;

import com.baidu.bce.logic.bci.service.sync.service.PodRecycleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@EnableScheduling
@Configuration
@Profile("default")
public class PodRecycleScheduler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodRecycleScheduler.class);

    private static final int FIX_DELAY_ONE = 20000;

    @Autowired
    private PodRecycleService podRecycleService;

    @Scheduled(fixedDelay = FIX_DELAY_ONE)
    public void runScheduledTask() {
        podRecycleService.recyclePodContainer();
    }

}
