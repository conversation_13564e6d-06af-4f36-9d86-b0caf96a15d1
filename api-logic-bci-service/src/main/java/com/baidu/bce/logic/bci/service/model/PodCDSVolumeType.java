package com.baidu.bce.logic.bci.service.model;

public enum PodCDSVolumeType {

    ROOTFS(1,"rootfs"),
    EMPTYDIR(2,"empty_dir"),
    DATA(3, "data");

    private int id;
    private String type;

    PodCDSVolumeType(int id, String type) {
        this.id = id;
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public String getType() {
        return type;
    }

    public static String getStatus(int id) {
        for (PodCDSVolumeType podStatus : values()) {
            if (podStatus.getId() == id) {
                return podStatus.getType();
            }
        }
        return null;
    }
}
