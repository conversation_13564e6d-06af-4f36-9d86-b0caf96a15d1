package com.baidu.bce.logic.bci.service.interceptor;

public class ResourceAccountSetting {
    private static ThreadLocal<String> application = new ThreadLocal<String>();
    private static ThreadLocal<String> resourceAccountId = new ThreadLocal<String>();
    // 统一计费
    private static ThreadLocal<Boolean> unifiedCharge = new ThreadLocal<Boolean>();

    public static String getApplication() {
        return application.get();
    }

    public static void setApplication(String application) {
        ResourceAccountSetting.application.set(application);
    }

    public static String getResourceAccountId() {
        return resourceAccountId.get();
    }

    public static void setResourceAccountId(String resourceAccountId) {
        ResourceAccountSetting.resourceAccountId.set(resourceAccountId);
    }

    public static Boolean isUnifiedCharge() {
        Boolean fromPass = unifiedCharge.get();
        if (fromPass == null) {
            return false;
        }
        return fromPass;
    }

    public static void setUnifiedCharge(Boolean fromPaas) {
        ResourceAccountSetting.unifiedCharge.set(fromPaas);
    }

    public static void clear() {
        application.remove();
        resourceAccountId.remove();
        unifiedCharge.remove();
    }
}
