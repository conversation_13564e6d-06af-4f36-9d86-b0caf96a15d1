package com.baidu.bce.logic.bci.service.orderexecute;

import com.baidu.bce.internalsdk.bci.BccEncrypt;
import com.baidu.bce.internalsdk.bci.EipTimeOutV2Client;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.constant.ImageConstant;
import com.baidu.bce.internalsdk.bci.model.Auth;
import com.baidu.bce.internalsdk.bci.model.BciVolume;
import com.baidu.bce.internalsdk.bci.model.Container;
import com.baidu.bce.internalsdk.bci.model.CreateServersResponse;
import com.baidu.bce.internalsdk.bci.model.EipResponse;
import com.baidu.bce.internalsdk.bci.model.Image;
import com.baidu.bce.internalsdk.bci.model.MapEntry;
import com.baidu.bce.internalsdk.bci.model.OsSchedulerHints;
import com.baidu.bce.internalsdk.bci.model.PodConfigFile;
import com.baidu.bce.internalsdk.bci.model.SecurityGroup;
import com.baidu.bce.internalsdk.bci.model.ServerFlavor;
import com.baidu.bce.internalsdk.bci.model.ServerForCreate;
import com.baidu.bce.internalsdk.bci.model.Servers;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.bci.model.TransactionDetailResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.eipv2.model.EipBind;
import com.baidu.bce.internalsdk.eipv2.model.EipInstanceCreateModel;
import com.baidu.bce.internalsdk.eipv2.model.EipOrderExtra;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.ResourceMapping;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.logic.bci.dao.common.model.Label;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.common.CommonUtils;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.constant.PodVolumeType;
import com.baidu.bce.logic.bci.service.model.BciOrderExtra;
import com.baidu.bce.logic.bci.service.model.ConfigFile;
import com.baidu.bce.logic.bci.service.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.service.model.ContainerPurchase;
import com.baidu.bce.logic.bci.service.model.EmptyDir;
import com.baidu.bce.logic.bci.service.model.Environment;
import com.baidu.bce.logic.bci.service.model.ImageRegistrySecret;
import com.baidu.bce.logic.bci.service.model.Nfs;
import com.baidu.bce.logic.bci.service.model.OrderFailedContentVar;
import com.baidu.bce.logic.bci.service.model.OrderSuccessContentVar;
import com.baidu.bce.logic.bci.service.model.PodCDSVolumeType;
import com.baidu.bce.logic.bci.service.model.Port;
import com.baidu.bce.logic.bci.service.model.Volume;
import com.baidu.bce.logic.bci.service.model.VolumeMounts;
import com.baidu.bce.logic.bci.service.util.PodUtils;
import com.baidu.bce.logic.core.constants.RegionConstant;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.order.executor.sdk.model.FaultTrace;
import com.baidu.bce.order.executor.sdk.model.MessageCenterModel;
import com.baidu.bce.order.executor.sdk.model.ReceiverType;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

@Service
public class PodNewOrderExecutorServiceV1 {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodNewOrderExecutorServiceV1.class);

    private static final String BCI_SOURCE = "bci";
    private static final int SIXTY_MINUTES = 600;
    private static final int RETRY_NUM = 3;
    private static final String POD = "POD";
    private static final String ACTION = "create";
    private static final String ORDER_KEY = "bci";

    private static final String ERROR_STATUS = "error";
    private static final String SUCCESS_TATUS = "succ";
    private static final String OPERATING_STATUS = "operating";
    private static final String CALLBACK_ERROR = "callback_error";
    private static final String CREATE_POD_SUCC_NO_PACKAGE = "create pod success, no package id, orderId is {}";

    private static final String ORDER_TIME_OUT = "Create Order Timeout";
    private static final String ORDER_CREATING_TIME_OUT = "Check In Creating Status Order: Create Resource spent too "
            + "long.";
    private static final String ORDER_EXCEPTION = "Order Occurred Exception";

    @Value("${sms.create.fail.tpl.id:Tpl_2ef56822-636c-4c7d-9a0c-919925b5dd8a}")
    private String smsCreateFailId;

    @Value("${sms.bci.create.success.tpl.id:Tpl_acc9ccfc-e3ae-4472-a0e4-001faa6da57b}")
    private String smsCreateSuccessId;

    @Value("${pod.emptyDir.size:10}")
    private int emptyDirSize;

    @Value("${pod.container.size:3}")
    private int containerSize;

    @Value("${eip_createTimeOut:25000}")
    private Integer eipCreateTimeOut;

    // 从下单开始算，创建server超时报警时间，单位：秒
    @Value("${server.create.timeout:300}")
    private Integer serverCreateTimeout;

    @Autowired
    private LogicPodClientFactory logicPodClientFactory;

    @Autowired
    private PodDao podDao;

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private RegionConfiguration regionConfiguration;


    public ExecutionResult execute(OrderClient orderClient, ResourceClient resourceClient, Order order) {
        LOGGER.debug("servicev1 order callback execute is called,orderid is:{}", order.getUuid());
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        // 获取 extra 里的用户accountID
        String accountID = "";
        try {
            accountID = getRealAccountID(order);
        } catch (IOException e) {
            LOGGER.debug("getExtraAccount failed: {}", e);
            return executionResult;
        }

        try {
            List<PodPO> pods = podDao.listByOrderId(accountID, order.getUuid());
            if (CollectionUtils.isEmpty(pods)) {
                LOGGER.error("failed to fetch pod by orderId: {}, accountId: {}", order.getUuid(), accountID);
                return executionResult;
            }
        } catch (Exception e) {
            LOGGER.error("failed to fetch pod by orderId : {}, ex:{}", order.getUuid(), e);
            return executionResult;
        }

        try {
            PodClient podClient = logicPodClientFactory.createPodClient(accountID);
            ServerForCreate serverForCreate = encapsulateServerModelForCreate(order, ACTION);
            CreateServersResponse createServersResponse = podClient.createServers(serverForCreate);
            if (createServersResponse != null) {
                LOGGER.debug("BCI_LIFECYCLE_CREATE_SERVER user: {} order is {}",
                        accountID, createServersResponse.getTranscationId());
                updateOrderToCreatingStatus(orderClient, order);
            } else {
                throw new BceException("createServersResponse is null");
            }
        } catch (BceInternalResponseException e) {
            LOGGER.error("create pod failed, exception is {}", e);
            if (e.getCode().equalsIgnoreCase("DuplicateTransactionId")) {
                LOGGER.error("BCI_LIFECYCLE_CREATE_SERVER DuplicateTransactionId user: {} order is {}",
                        accountID, order.getUuid());
                updateOrderToCreatingStatus(orderClient, order);
            }
        } catch (Exception e) {
            LOGGER.error("BCI_LIFECYCLE_CREATE_SERVER create pod failed, order is {}, exception is {}",
                    order.getUuid(), e);
            updateOrderToFailedStatus(orderClient, order, executionResult, "[POD Backend]" + ORDER_EXCEPTION,
                    e.toString());
        }
        return executionResult;
    }


    public ExecutionResult check(OrderClient orderClient, ResourceClient resourceClient, Order order) {
        LOGGER.debug("servicev1 order callback check is called,orderid is:{}", order.getUuid());

        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        // 获取 extra 里的用户accountID
        String accountID = "";
        try {
            accountID = getRealAccountID(order);
        } catch (IOException e) {
            LOGGER.debug("getExtraAccount failed: {}", e);
            return executionResult;
        }

        try {
            List<PodPO> pods = podDao.listByOrderId(accountID, order.getUuid());
            if (CollectionUtils.isEmpty(pods)) {
                LOGGER.error("failed to fetch pod by orderId: {}, accountId: {}", order.getUuid(), accountID);
                return executionResult;
            }
        } catch (Exception e) {
            LOGGER.error("failed to fetch pod by orderId : {}, ex:{}", order.getUuid(), e);
            return executionResult;
        }

        TransactionDetailResponse transactionDetail = null;
        transactionDetail = queryCreateServerProcessByTransactionId(accountID, order.getUuid());
        if (transactionDetail != null) {
            updateOrderByServerCreateStatus(accountID, orderClient, order, transactionDetail, executionResult);
        }

        if (isOrderTimeout(order)) {
            LOGGER.error("Create pod server spent time too long, orderId is {}", order.getUuid());
            updateOrderToFailedStatus(orderClient, order, executionResult, "[BCC Backend] " + ORDER_TIME_OUT,
                    ORDER_CREATING_TIME_OUT);
        }
        return executionResult;
    }


    /**
     * 根据容器组创建进度，对订单做相应处理
     *
     * @param response
     * @param order
     */
    private void updateOrderByServerCreateStatus(String accountID, OrderClient client, Order order,
                                                 TransactionDetailResponse response,
                                                 ExecutionResult executionResult) {
        long timeConsuming = (System.currentTimeMillis() - order.getCreateTime().getTime()) / 1000L;
        switch (response.getStatus()) {
            case SUCCESS_TATUS:
                LOGGER.debug("BCI_LIFECYCLE_SERVER_CREATED nova transaction success, orderID: {}, " +
                        "consuming time: {}s", response.getTransactionId(), timeConsuming);
                createRelatedObjectAndUpdateOrderToCreated(accountID, client, order, response, executionResult);
                break;
            case OPERATING_STATUS:
                break;
            case ERROR_STATUS: // fall through to case CALLBACK_ERROR
            case CALLBACK_ERROR:
                updateOrderToFailedStatus(client, order, executionResult,
                        "[BCI Backend] Query POD create progress " + "from BCI Logical failed ",
                        "BCI Logical return is " + response.getErrMsg());
                break;
            default:
                LOGGER.warn("Server Create status is unknown, orderId is {}", order.getUuid());
                break;
        }
        // 超过 5分钟，就会报警
        if (timeConsuming > serverCreateTimeout) {
            LOGGER.error("BCI_LIFECYCLE_CREATE_SERVER over time: {}s, orderID: {}", timeConsuming, order.getUuid());
        }
    }

    private void createRelatedObjectAndUpdateOrderToCreated(String accountID, OrderClient orderClient, Order order,
                                                            TransactionDetailResponse transactionDetail,
                                                            ExecutionResult executionResult) {
        List<Order.Item> eipItems = getItemsByServiceType(order, ServiceType.EIP.name());
        if (CollectionUtils.isEmpty(eipItems)) {
            updateBccOrderToCreated(orderClient, order, transactionDetail, null, executionResult);
            LOGGER.info(CREATE_POD_SUCC_NO_PACKAGE, order.getUuid());
            return;
        }

        String serverOrderId = order.getUuid();
        Order.Item eipOrderItem = eipItems.get(0);

        EipTimeOutV2Client eipV2Client = logicPodClientFactory.createEipTimeOutClientV2(accountID, eipCreateTimeOut);
        EipBind eipBind = getEipBindFromServerIds(transactionDetail.getLstServer()); // new EipBind();

        LOGGER.debug("Create pod success, start bind eip, order is {}, account is {}", serverOrderId, accountID);
        EipOrderExtra eipExtra = null;
        String strEipExtra = eipOrderItem.getExtra();
        try {
            if (strEipExtra.contains("eipExtra")) {
                strEipExtra = getValueFromExtra(strEipExtra, "eipExtra");
            } else {
                // 支持12月大促 解析前去掉campaignDesc
                String[] strArrExtra = strEipExtra.split(";");
                strEipExtra = (strArrExtra.length == 2) ? strArrExtra[1] : strArrExtra[0];
            }
            eipExtra = new ObjectMapper().readValue(strEipExtra, EipOrderExtra.class);
        } catch (Exception ex) {
            LOGGER.error("Parse eip extra error, order is {}, exception is {}", serverOrderId, ex);
            updateOrderToFailedStatus(orderClient, order, executionResult,
                    "Parse eip extra error", "exception is" + ex);
            return;
        }

        try {
            EipResponse response = createEip(serverOrderId, eipExtra, eipOrderItem.getRegion(),
                    eipOrderItem.getCount(), eipBind, eipV2Client, getChargeSource(order));
            if (order.getAccountId().equalsIgnoreCase(accountID)) {
                updateBccOrderToCreated(orderClient, order, transactionDetail, response.getEips(), executionResult);
            } else {
                // 对于统一计费的资源，不需要添加EIP的信息，不然会报错：OrderExceptions.ResourceDuplicateKeyException ，怀疑是
                // EIP 那边已经做了订单处理。
                updateBccOrderToCreated(orderClient, order, transactionDetail, null, executionResult);
            }
        } catch (Exception e) {
            LOGGER.error("Bind eip to pod error, order is {}, exception is {}", serverOrderId, e);
            updateOrderToFailedStatus(orderClient, order, executionResult,
                    "Bind eip to pod error", "exception is" + e);
        }
    }

    /**
     * 更新创建bci订单为CREATED
     *
     * @param orderClient
     * @param transactionDetail
     * @return
     */
    private void updateBccOrderToCreated(OrderClient orderClient, Order order,
                                         TransactionDetailResponse transactionDetail,
                                         List<EipResponse.SingleEipResponse> eipBindQueryResponses,
                                         ExecutionResult executionResult) {
        List<ResourceMapping> resourceMappingList = new ArrayList<>();

        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(OrderStatus.CREATED);

        if (CollectionUtils.isNotEmpty(eipBindQueryResponses)) {
            for (EipResponse.SingleEipResponse eipBindResponse : eipBindQueryResponses) {
                ResourceMapping resourceMapping = new ResourceMapping();
                resourceMapping.setKey("eip1");
                resourceMapping.setStatus(ResourceStatus.RUNNING);
                resourceMapping.setId(eipBindResponse.getEncryptId());
                resourceMapping.setShortId(eipBindResponse.getAllocationId());
                resourceMappingList.add(resourceMapping);
            }
        }

        for (ServersResponse serversResponse : transactionDetail.getLstServer()) {
            ResourceMapping resourceMapping = new ResourceMapping();
            resourceMapping.setKey(ORDER_KEY);
            resourceMapping.setStatus(ResourceStatus.RUNNING);
            resourceMapping.setId(serversResponse.getId());
            resourceMappingList.add(resourceMapping);
        }
        updateOrderRequest.setResources(resourceMappingList);

        int count = 3;
        boolean updateSucceed = false;
        String exMessage = "";
        while (count-- > 0) {
            try {
                orderClient.update(transactionDetail.getTransactionId(), updateOrderRequest);
                updateSucceed = true;
                break;
            } catch (BceInternalResponseException ex) {
                LOGGER.error("Update bcc order status error, retry {}, orderId is {}, exception is {}", count,
                        transactionDetail.getTransactionId(), ex);
                exMessage = ex.getMessage();
            }
        }

        if (updateSucceed) {
            LOGGER.debug("BCI_LIFECYCLE_PUT_ORDER_CREATED update order to created, order:{}",
                    transactionDetail.getTransactionId());
            sendServerCreatedMsg(transactionDetail.getLstServer(), order, executionResult);
        } else {
            updateOrderToFailedStatus(orderClient, order, executionResult, "Update Order Failed", exMessage);
        }
    }

    /**
     * 发送手机短信
     *
     * @param serversResponses
     * @param order
     * @param executionResult
     */
    private void sendServerCreatedMsg(List<ServersResponse> serversResponses, Order order,
                                      ExecutionResult executionResult) {
        try {
            BciOrderExtra orderExtra = PodUtils.getOrderExtra(
                    PodUtils.getExtraByServiceType(order, PodConstants.SERVICE_TYPE));
            StringBuilder userName = new StringBuilder("root");
            String region = getRegionDesc(order.getItems().get(0).getRegion());
            if (StringUtils.isNotEmpty(region)) {
                userName.append("，地域：").append(region);
            }
            userName.append("，可用区：").append(region).append("-")
                    .append(orderExtra.getLogicalZone().replaceAll("zone", ""));
            userName.append("。其他实例信息详见控制台");

            OrderSuccessContentVar createContentVar = new OrderSuccessContentVar();
            createContentVar.setCount(String.valueOf(serversResponses.size()));
            createContentVar.setServiceType("BCI");
            createContentVar.setInstance(getInstanceInfo(serversResponses.get(0)));
            createContentVar.setUsername(userName.toString());
            createContentVar.setExtraInfo("");

            executionResult.appendMessageCenterModel(new MessageCenterModel(smsCreateSuccessId,
                    new ObjectMapper().writeValueAsString(createContentVar), order.getAccountId(),
                    ReceiverType.UserId, "create sucess"));
            executionResult.setExecutionStatus(ExecutionStatus.SUCCESS);
        } catch (Exception e) {
            LOGGER.error("send message error, orderId is {}, exception is {}", order.getUuid(), e);
        }
    }

    private String getInstanceInfo(ServersResponse serversResponse) {

        StringBuilder instanceInfo = new StringBuilder("");

        String network = "私网IP:" + serversResponse.getFixip();
        instanceInfo.append("容器组").append("名称：").append(serversResponse.getName());
        instanceInfo.append(",").append(network);

        return instanceInfo.toString();
    }

    /**
     * 获取region的中文描述
     *
     * @param region
     * @return
     */
    private String getRegionDesc(String region) {
        for (RegionConstant regionConstant : RegionConstant.values()) {
            if (regionConstant.getName().equalsIgnoreCase(region)) {
                return regionConstant.getChineseName();
            }
        }
        return region;
    }

    /**
     * 虚机查询创建进度
     *
     * @param accountId
     * @param orderId
     * @return
     */
    private TransactionDetailResponse queryCreateServerProcessByTransactionId(String accountId, String orderId) {
        int count = RETRY_NUM;
        PodClient podClient = logicPodClientFactory.createPodClient(accountId);
        TransactionDetailResponse transactionDetail = null;
        while (count-- > 0) {
            try {
                transactionDetail = podClient.showTransaction(orderId);
                if (transactionDetail != null) {
                    break;
                }
            } catch (Exception ex) {
                LOGGER.error("Query pod create status, retry {}, orderId: {}, exception: {}", count, orderId, ex);
            }
        }
        return transactionDetail;
    }

    /**
     * 判断是否超时
     *
     * @param order
     * @return
     */
    private boolean isOrderTimeout(Order order) {
        Date baseTime = order.getUpdateTime();

        Calendar retryEndTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        retryEndTime.setTime(baseTime);
        retryEndTime.add(Calendar.MINUTE, SIXTY_MINUTES);
        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        return now.after(retryEndTime);
    }

    /**
     * 不用订单
     *
     * @param action
     * @return
     * @throws IOException
     */
    private ServerForCreate encapsulateServerModelForCreate(Order order, String action) throws IOException {

        ServerForCreate serverForCreate = new ServerForCreate();
        serverForCreate.setSource(BCI_SOURCE);
        serverForCreate.setAction(action);
        serverForCreate.setType(POD);
        serverForCreate.setTransactionId(order.getUuid());

        Servers servers = null;
        List<Servers> lstServers = new ArrayList<>();
        String physicalZone = null;
        for (Order.Item orderItem : order.getItems()) {
            if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) {
                servers = new Servers();
                servers.setCount(orderItem.getCount());
                servers.setCreateFloatingIp(true);
                physicalZone = parseExtraField(orderItem.getExtra(), servers);
                MapEntry mapEntry = new MapEntry();
                mapEntry.setName(LogicalConstant.LABEL_ORDER_ID);
                mapEntry.setValue(order.getUuid());
                servers.getLabels().add(mapEntry);
                parseFlavors(servers, orderItem);
                servers.getMetadata().put("payment", orderItem.getProductType());
                servers.setOsSchedulerHints(parseHints(physicalZone, servers, getRealAccountID(order)));
                servers.setImageRef("");
                lstServers.add(servers);
            }
        }

        serverForCreate.setList(lstServers);
        if (StringUtils.isNotEmpty(physicalZone)) {
            serverForCreate.setAvailableZone(physicalZone);
        }

        return serverForCreate;
    }

    private OsSchedulerHints parseHints(String physicalZone, Servers servers, String accountId) {
        ServerFlavor flavor = servers.getServerFlavor();
        OsSchedulerHints osSchedulerHints = new OsSchedulerHints();
        List<String> schedulerTags = new ArrayList<>();
        schedulerTags.add(physicalZone);
        // 白名单，判断是否依据内存指定机型tag
        if (commonUtils.checkWhiteList(LogicalConstant.ROUND_UP_CPU_MEM, regionConfiguration.getCurrentRegion(),
                accountId) && flavor.getMemory() >= 2 * 1024) {
            schedulerTags.add(PodConstants.INSTANCE_TYPE_G4);
            osSchedulerHints.setEnableDpdk(true);
            // 增加 机型 label
            MapEntry entry = new MapEntry();
            entry.setName(LogicalConstant.LABEL_INSTANCE_TYPE);
            entry.setValue("G4");
            servers.getLabels().add(entry);
        } else {
            // schedulerTags.add(PodConstants.INSTANCE_TYPE_G1);
            // nova（lixiong02）要求普一、普三只传普三tag
            schedulerTags.add(PodConstants.INSTANCE_TYPE_G3);
        }
        osSchedulerHints.setSchedulerTags(schedulerTags);
        return osSchedulerHints;
    }

    private String parseExtraField(String extra, Servers servers)
            throws IOException {
        BciOrderExtra orderExtra = PodUtils.getOrderExtra(extra);
        List<Container> containers = new ArrayList<>();
        Volume volume = orderExtra.getVolume();
        for (ContainerPurchase containerPurchase : orderExtra.getContainers()) {
            Container container = new Container();
            container.setArgs(containerPurchase.getArgs());
            container.setCommand(containerPurchase.getCommands());
            container.setCpu(containerPurchase.getCpu());
            container.setMemory((int) (containerPurchase.getMemory() * 1024));

            Image image = new Image();
            Auth auth = new Auth();
            String imageName = parseAuth(orderExtra.getImageRegistrySecrets(), auth, containerPurchase);
            image.setName(imageName);
            image.setVersion(containerPurchase.getImageVersion());
            image.setAuth(auth);
            container.setImage(image);

            container.setEnvs(parseEnvs(containerPurchase.getEnvs()));
            container.setName(containerPurchase.getName());
            container.setWorkingDir(containerPurchase.getWorkingDir());
            container.setMounts(parseMounts(containerPurchase.getVolumeMounts(), volume));
            container.setPorts(parsePorts(containerPurchase.getPorts()));

            containers.add(container);
        }

        List<SecurityGroup> securityGroups = new ArrayList<>();
        SecurityGroup securityGroup = new SecurityGroup();
        securityGroup.setName(orderExtra.getSecurityGroupId());
        securityGroups.add(securityGroup);
        servers.setSecurityGroups(securityGroups);

        servers.setNfs(parseNfs(volume.getNfs()));
        servers.setEmptyDir(parseEmptyDir(volume.getEmptyDir()));
        servers.setConfigFile(parseConfigFile(volume.getConfigFile()));
        servers.setBciVolumes(parseBciVolumes(volume.getPodVolumes(), false));
        servers.setBciDataVolumes(parseBciVolumes(volume.getPodVolumes(), true));
        servers.setName(orderExtra.getName());
        servers.setContainers(containers);
        servers.setRestartPolicy(orderExtra.getRestartPolicy().toUpperCase());
        servers.setLabels(parselabels(orderExtra.getLabels()));
        servers.setSubnetId(orderExtra.getSubnetUuid());
        servers.setPushLog(orderExtra.isEnableLog());

        if (StringUtils.isNotEmpty(orderExtra.getAnnotations())) {
            if (servers.getMetadata() == null) {
                servers.setMetadata(new HashMap<String, String>());
            }
            servers.getMetadata().put("annotations", orderExtra.getAnnotations());
        }

        return BccEncrypt.decrypt(orderExtra.getPhysicalZone());
    }

    /**
     * 转换参数，han接口的data类型volume是单独的字段，这里做拆分处理，也会把多余的emptyDir去掉
     *
     * @param podVolumes
     * @param wantDataVolume
     * @return
     */
    private List<BciVolume> parseBciVolumes(List<Volume.PodVolume> podVolumes, boolean wantDataVolume) {
        List<BciVolume> bciVolumes = new LinkedList<>();
        if (CollectionUtils.isEmpty(podVolumes)) {
            return bciVolumes;
        }
        BciVolume emptyDirVolume = null;
        for (Volume.PodVolume podVolume : podVolumes) {
            boolean isDataVolume = PodCDSVolumeType.DATA.getType()
                    .equalsIgnoreCase(podVolume.getType());
            if (wantDataVolume ^ isDataVolume) {
                continue;
            }

            BciVolume bciVolume = new BciVolume();
            bciVolume.setFs(new BciVolume.FS());
            bciVolume.setName(podVolume.getName());
            bciVolume.setType(podVolume.getType());
            bciVolume.setSize(podVolume.getSizeInGB());

            // fs
            BciVolume.FS destFS = new BciVolume.FS();
            if (podVolume.getFs() != null) {
                Volume.FS sourceFS = podVolume.getFs();
                destFS.setForceFormat(sourceFS.getForceFormat());
                destFS.setMountFlags(sourceFS.getMountFlags());
                destFS.setFsType(sourceFS.getFsType());
            }
            bciVolume.setFs(destFS);

            BciVolume.VolumeSource destVS = new BciVolume.VolumeSource();
            BciVolume.Cds destCDS = new BciVolume.Cds();
            destVS.setCds(destCDS);
            bciVolume.setVolumeSource(destVS);

            if (podVolume.getVolumeSource() != null && podVolume.getVolumeSource().getCds() != null) {
                // 磁盘类型
                bciVolume.setVolumeType(podVolume.getVolumeSource().getCds().getType());
                // 磁盘uuid
                bciVolume.getVolumeSource().getCds().setUuid(podVolume.getVolumeSource().getCds().getUuid());
            }

            if (PodCDSVolumeType.EMPTYDIR.getType().equalsIgnoreCase(podVolume.getType())) {
                if (emptyDirVolume == null) {
                    emptyDirVolume = bciVolume;
                    continue;
                }

                emptyDirVolume.setSize(emptyDirVolume.getSize() + bciVolume.getSize());
                continue;
            }

            bciVolumes.add(bciVolume);
        }
        if (emptyDirVolume != null) {
            bciVolumes.add(emptyDirVolume);
        }

        return bciVolumes;
    }

    private List<String> parseNfs(List<Nfs> nfsList) {
        List<String> serversNfs = new ArrayList<>();
        for (Nfs nfs : nfsList) {
            String nfsPath = PodConstants.NFS + ":/" + nfs.getServer() + ":" + nfs.getPath();
            serversNfs.add(nfsPath);
        }
        return serversNfs;
    }

    private List<String> parseEmptyDir(List<EmptyDir> emptyDirList) {
        List<String> emptyDirs = new ArrayList<>();
        for (EmptyDir emptyDir : emptyDirList) {
            emptyDirs.add(emptyDir.getName());
        }
        return emptyDirs;
    }

    private List<MapEntry> parselabels(List<Label> createLabels) {
        if (CollectionUtils.isEmpty(createLabels)) {
            return null;
        }
        List<MapEntry> labels = new ArrayList<>();
        for (Label label : createLabels) {
            MapEntry mapEntry = new MapEntry();
            mapEntry.setName(label.getLabelKey());
            mapEntry.setValue(label.getLabelValue());
            labels.add(mapEntry);
        }
        return labels;
    }

    private List<PodConfigFile> parseConfigFile(List<ConfigFile> configFiles) {
        if (CollectionUtils.isEmpty(configFiles)) {
            return null;
        }

        List<PodConfigFile> serversConfigs = new ArrayList<>();
        for (ConfigFile configFile : configFiles) {
            PodConfigFile file = new PodConfigFile();
            file.setName(configFile.getName());
            List<MapEntry> configFileMaps = new ArrayList<>();
            for (ConfigFileDetail detail : configFile.getConfigFiles()) {
                MapEntry mapEntry = new MapEntry();
                mapEntry.setName(detail.getPath());
                mapEntry.setValue(detail.getFile());
                configFileMaps.add(mapEntry);
            }
            file.setConfigItem(configFileMaps);
            serversConfigs.add(file);
        }
        return serversConfigs;
    }

    private String parseAuth(List<ImageRegistrySecret> imageRegistrySecrets,
                             Auth auth, ContainerPurchase containerPurchase) {
        if (CollectionUtils.isNotEmpty(imageRegistrySecrets)) {
            for (ImageRegistrySecret imageRegistrySecret : imageRegistrySecrets) {
                if (isAddressEquals(imageRegistrySecret.getServer(), containerPurchase.getImageAddress())) {
                    auth.setName(imageRegistrySecret.getUserName());
                    auth.setPassword(imageRegistrySecret.getPassword());
                    auth.setServerAddress(imageRegistrySecret.getServer());
                }
            }
        }
        if (containerPurchase.getImageAddress().equalsIgnoreCase(containerPurchase.getImageName())) {
            return ImageConstant.DOCKER_IMAGE_IO + containerPurchase.getImageName();
        } else {
            return containerPurchase.getImageAddress();
        }
    }

    private List<MapEntry> parseEnvs(List<Environment> environments) {
        if (CollectionUtils.isEmpty(environments)) {
            return null;
        }
        List<MapEntry> envs = new ArrayList<>();
        for (Environment environment : environments) {
            MapEntry mapEntry = new MapEntry();
            mapEntry.setName(environment.getKey());
            mapEntry.setValue(environment.getValue());
            envs.add(mapEntry);
        }
        return envs;
    }

    private List<Container.Mount> parseMounts(List<VolumeMounts> volumeMounts, Volume volume) {
        if (CollectionUtils.isEmpty(volumeMounts)) {
            return null;
        }
        Map<String, Nfs> nfsMap = new HashMap<>();
        Map<String, EmptyDir> emptyDirMap = new HashMap<>();
        Map<String, ConfigFile> configFileMap = new HashMap<>();
        Map<String, Volume.PodVolume> emptyDirVolumeMap = new HashMap<>();
        Map<String, Volume.PodVolume> dataVolumeMap = new HashMap<>();
        for (Nfs nfs : volume.getNfs()) {
            if (nfsMap.containsKey(nfs.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            nfsMap.put(nfs.getName(), nfs);
        }
        for (EmptyDir emptyDir : volume.getEmptyDir()) {
            if (emptyDirMap.containsKey(emptyDir.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            emptyDirMap.put(emptyDir.getName(), emptyDir);
        }
        for (ConfigFile configFile : volume.getConfigFile()) {
            if (configFileMap.containsKey(configFile.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }
            configFileMap.put(configFile.getName(), configFile);
        }
        emptyDirVolumeMap = parsePodVolumeMap(PodCDSVolumeType.EMPTYDIR.getType(), volume.getPodVolumes());
        dataVolumeMap = parsePodVolumeMap(PodCDSVolumeType.DATA.getType(), volume.getPodVolumes());

        List<Container.Mount> mounts = new ArrayList<>();
        for (VolumeMounts volumeMount : volumeMounts) {
            Container.Mount mount = new Container.Mount();
            // rootfs 不设置 mounts
            if (PodCDSVolumeType.ROOTFS.getType().equals(volumeMount.getType())) {
                continue;
            }
            if (nfsMap.containsKey(volumeMount.getName())
                    || PodVolumeType.NFS.getType().equals(volumeMount.getType())) {
                Nfs nfs = nfsMap.get(volumeMount.getName());
                mount.setHostPath(PodConstants.NFS + ":/" + nfs.getServer() + ":" + nfs.getPath());
            }
            if (emptyDirMap.containsKey(volumeMount.getName())
                    || PodVolumeType.EMPTY_DIR.getType().equals(volumeMount.getType())) {
                mount.setHostPath(PodConstants.EMPTYDIR + ":" + volumeMount.getName());
            }
            if (configFileMap.containsKey(volumeMount.getName())
                    || PodVolumeType.CONFIG_FILE.getType().equals(volumeMount.getType())) {
                mount.setHostPath(PodConstants.CONFIGFILE + ":" + volumeMount.getName());
            }
            if (emptyDirVolumeMap.containsKey(volumeMount.getName())
                    || PodCDSVolumeType.EMPTYDIR.getType().equals(volumeMount.getType())) {
                mount.setHostPath(PodCDSVolumeType.EMPTYDIR.getType() + ":" + volumeMount.getName());
            }
            if (dataVolumeMap.containsKey(volumeMount.getName())
                    || PodCDSVolumeType.DATA.getType().equals(volumeMount.getType())) {
                mount.setHostPath(PodCDSVolumeType.DATA.getType() + ":" + volumeMount.getName());
            }
            mount.setReadOnly(volumeMount.getReadOnly());
            mount.setContainerPath(volumeMount.getMountPath());

            mounts.add(mount);
        }
        return mounts;
    }

    private Map<String, Volume.PodVolume> parsePodVolumeMap(String volumeType, List<Volume.PodVolume> podVolumes) {
        Map<String, Volume.PodVolume> volumeMap = new HashMap<>();
        if (CollectionUtils.isEmpty(podVolumes)) {
            return volumeMap;
        }
        for (Volume.PodVolume podVolume : podVolumes) {
            if (!volumeType.equals(podVolume.getType())) {
                continue;
            }
            if (volumeMap.containsKey(podVolume.getName())) {
                throw new CommonExceptions.RequestInvalidException();
            }

            volumeMap.put(podVolume.getName(), podVolume);
        }

        return volumeMap;
    }

    private List<Container.Port> parsePorts(List<Port> ports) {
        if (CollectionUtils.isEmpty(ports)) {
            return null;
        }
        List<Container.Port> novaPorts = new ArrayList<>();
        for (Port port : ports) {
            Container.Port novaPort = new Container.Port();
            novaPort.setPort(port.getPort());
            novaPort.setProtocol(port.getProtocol());
            novaPorts.add(novaPort);
        }
        return novaPorts;
    }

    private Boolean isAddressEquals(String repository, String address) {
        if (repository.endsWith("/")) {
            repository = repository.substring(0, repository.lastIndexOf("/"));
        }
        String addressWithSchema = repository.replace("http://", "").replace("https://", "");
        return addressWithSchema.equals(address.substring(0, address.lastIndexOf("/")));
    }

    private void parseFlavors(Servers servers, Order.Item item) {
        ServerFlavor serverFlavor = servers.getServerFlavor();
        if (null == serverFlavor) {
            serverFlavor = new ServerFlavor();
        }

        for (Object flavor : item.getFlavor()) {
            Flavor.FlavorItem flavorItem = (Flavor.FlavorItem) flavor;
            if (flavorItem.getScale() == null) {
                continue;
            }
            String flavorItemName = flavorItem.getName();
            BigDecimal scale = flavorItem.getScale();
            if (OrderExecuteConstant.BciFlavorItemKey.CPU.equals(flavorItemName)) {
                serverFlavor.setCpu((int) Math.ceil(scale.floatValue()));
            } else if (OrderExecuteConstant.BciFlavorItemKey.MEMORY.equals(flavorItemName)) {
                serverFlavor.setMemory((int) (1024 * scale.floatValue()));
            }
        }
        int weight = 0;
        if (servers.getEmptyDir().size() > 0) {
            weight = 1;
        }
        serverFlavor.setDisk(weight * emptyDirSize + containerSize);

        if (servers.getMetadata() == null) {
            servers.setMetadata(new HashMap<String, String>());
        }
        servers.getMetadata().put("key", item.getKey());

        servers.setServerFlavor(serverFlavor);
    }

    /**
     * 更新订单为创建中
     *
     * @param client
     * @param order
     * @return
     */
    private boolean updateOrderToCreatingStatus(OrderClient client, Order order) {
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(OrderStatus.CREATING);
        return bestEffortUpdateOrder(client, order.getUuid(), updateOrderRequest);
    }

    /**
     * 更新订单为创建失败 & 发送创建失败的短信
     *
     * @param orderClient
     * @param order
     */
    private void updateOrderToFailedStatus(OrderClient orderClient, Order order, ExecutionResult executionResult,
                                           String summary, String detail) {
        LOGGER.error("BCI_LIFECYCLE_SERVER_FAILED, order is: " + order.getUuid());

        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
        bestEffortUpdateOrder(orderClient, order.getUuid(), updateOrderRequest);

        try {
            OrderFailedContentVar failedContentVar = new OrderFailedContentVar();
            failedContentVar.setOrderId(order.getUuid());
            failedContentVar.setServiceType(PodConstants.SERVICE_TYPE);

            String contentVar = new ObjectMapper().writeValueAsString(failedContentVar);
            executionResult.appendMessageCenterModel(new MessageCenterModel(smsCreateFailId,
                    contentVar, order.getAccountId(), ReceiverType.UserId, "create failed"));
            executionResult.setExecutionStatus(ExecutionStatus.FAILURE);
        } catch (Exception e) {
            LOGGER.warn("send mail failed, exception is {}", e);
        }

        FaultTrace faultTrace = new FaultTrace(summary, detail);
        executionResult.setFaultTrace(faultTrace);
    }

    /**
     * 更新订单，若失败重试三次
     *
     * @param client
     * @param orderId
     * @param updateOrderRequest
     */
    private boolean bestEffortUpdateOrder(OrderClient client, String orderId, UpdateOrderRequest updateOrderRequest) {
        int count = RETRY_NUM;
        boolean flag = false;
        while (count-- > 0) {
            try {
                client.update(orderId, updateOrderRequest);
                flag = true;
                break;
            } catch (Exception ex) {
                LOGGER.error("Update order status error, retry {}, orderId is {}, exception is {}", count, orderId, ex);
            }
        }
        return flag;
    }

    private List<Order.Item> getItemsByServiceType(Order serverOrder, String serviceType) {
        List<Order.Item> items = new ArrayList<>();
        for (Order.Item item : serverOrder.getItems()) {
            if (item.getServiceType().equalsIgnoreCase(serviceType)) {
                items.add(item);
            }
        }
        return items;
    }

    /**
     * 封装EIPBind对象
     *
     * @param servers
     * @return
     */
    private EipBind getEipBindFromServerIds(List<ServersResponse> servers) {
        List<String> serverIds = new ArrayList<>();
        for (ServersResponse serversResponse : servers) {
            serverIds.add(serversResponse.getId());
        }

        EipBind eipBind = new EipBind();
        eipBind.setInstanceType(PodConstants.SERVICE_TYPE);
        eipBind.setInstanceIdList(serverIds);
        return eipBind;
    }

    private static String getValueFromExtra(String extra, String key) {
        String value = null;
        if (StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(extra) && extra.contains(key)) {
            for (String keyValue : extra.split(";")) {
                String[] keyValueArray = keyValue.split(":", 2);
                if (keyValueArray.length == 2 && key.equalsIgnoreCase(keyValueArray[0])) {
                    value = keyValueArray[1];
                    break;
                }
            }
        }
        return value;
    }

    private EipResponse createEip(String orderId, EipOrderExtra eipExtra, String region, int count,
                                  EipBind eipBind, EipTimeOutV2Client eipV2Client, String chargeSource) {
        EipInstanceCreateModel eipInstanceCreateModel = new EipInstanceCreateModel();
        eipInstanceCreateModel.setName(eipExtra.getName());
        eipInstanceCreateModel.setArea(region);
        eipInstanceCreateModel.setBandwidthInMbps(eipExtra.getBandWidth());
        eipInstanceCreateModel.setCount(count);
        eipInstanceCreateModel.setOrderId(orderId);
        if (eipBind != null) {
            eipInstanceCreateModel.setBind(eipBind);
        }
        if (StringUtils.isNotEmpty(chargeSource)) {
            eipInstanceCreateModel.setSource(chargeSource);
        }
        return eipV2Client.createEipWithLongTimeOut(eipInstanceCreateModel);
    }

    // 获取真实 accountID, 对于转移计费，order里的accountID是资源账号ID，用户ID需要从订单extra信息里获取
    private String getRealAccountID(Order order) throws IOException {
        String accountID = order.getAccountId();
        for (Order.Item orderItem : order.getItems()) {
            if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) {
                String extra = orderItem.getExtra();
                BciOrderExtra orderExtra = PodUtils.getOrderExtra(extra);
                if (StringUtils.isNotEmpty(orderExtra.getExtraAccountID())) {
                    accountID = orderExtra.getExtraAccountID();
                    LOGGER.debug("getExtraAccountID : {}", accountID);
                    break;
                }
            }
        }

        return accountID;
    }

    private String getChargeSource(Order order) throws IOException {
        String chargeSource = "";
        for (Order.Item orderItem : order.getItems()) {
            if (PodConstants.SERVICE_TYPE.equalsIgnoreCase(orderItem.getServiceType())) {
                String extra = orderItem.getExtra();
                BciOrderExtra orderExtra = PodUtils.getOrderExtra(extra);
                if (StringUtils.isNotEmpty(orderExtra.getChargeSource())) {
                    chargeSource = orderExtra.getChargeSource();
                    LOGGER.debug("getChargeSource : {}", chargeSource);
                    break;
                }
            }
        }

        return chargeSource;
    }

    public boolean isV1Order(String orderId) {
        return podDao.getPodCountByOrderid(orderId) > 0;
    }
}