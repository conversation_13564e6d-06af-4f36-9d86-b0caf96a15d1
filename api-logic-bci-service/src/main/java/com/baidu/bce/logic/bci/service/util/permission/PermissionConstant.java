package com.baidu.bce.logic.bci.service.util.permission;

public class PermissionConstant {
    public static final String COMMA_SEPARATOR = ",";
    public static final String DEFAULT_CREATE_RESOURCE = "*";
    public static final String ALLOW_PERMISSION = "ALLOW";
    public static final String INTERAL_CLASS_PREFIX = "com.baidu";

    public static final String SERVICE_BCI = "bce:bci";

    public static final String TYPE_PREFIX_POD = "pod/";

    public static final String VM_OPERATE = "VM_OPERATE";
    public static final String VM_READ = "VM_READ";
    public static final String BCI_OPERATE = "OPERATE";
    public static final String BCI_READ = "READ";
    public static final String BCI_CONTROL = "CONTROL";
    public static final String BILLING_TYPE = "BILLING_TYPE";
    public static final String OPERATE = "OPERATE";
    public static final String READ = "READ";

}
