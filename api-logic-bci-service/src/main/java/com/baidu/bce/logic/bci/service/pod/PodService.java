package com.baidu.bce.logic.bci.service.pod;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.asyncwork.sdk.work.WorkKeyUtil;
import com.baidu.bce.externalsdk.logical.network.securitygroup.SecurityGroupClient;
import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SecurityGroupSimpleInstancesVO;
import com.baidu.bce.externalsdk.logical.network.securitygroup.model.SimpleSecurityGroupVO;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.request.SubnetMapRequest;
import com.baidu.bce.externalsdk.logical.network.subnet.model.response.SubnetMapResponse;
import com.baidu.bce.internalsdk.bci.BCCClient;
import com.baidu.bce.internalsdk.bci.CCRClient;
import com.baidu.bce.internalsdk.bci.CceImageClient;
import com.baidu.bce.internalsdk.bci.CloudTrailClient;
import com.baidu.bce.internalsdk.bci.DockerHubClient;
import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.constant.ImageConstant;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeRollbackDbRequest;
import com.baidu.bce.internalsdk.bci.model.AttachVolumeUpdateDbRequest;
import com.baidu.bce.internalsdk.bci.model.BindEipToBciRequest;
import com.baidu.bce.internalsdk.bci.model.CCRImage;
import com.baidu.bce.internalsdk.bci.model.CCRImageResponse;
import com.baidu.bce.internalsdk.bci.model.CceOfficialImage;
import com.baidu.bce.internalsdk.bci.model.DockerHubImage;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageResponse;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageTag;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageTagResponse;
import com.baidu.bce.internalsdk.bci.model.ExecArgs;
import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.OfficialImage;
import com.baidu.bce.internalsdk.bci.model.OfficialImageResponse;
import com.baidu.bce.internalsdk.bci.model.PodEventPO;
import com.baidu.bce.internalsdk.bci.model.Server;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.internalsdk.bci.model.UserImageResponse;
import com.baidu.bce.internalsdk.bci.model.WebShellRequest;
import com.baidu.bce.internalsdk.bci.model.WebshellResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderCreateRequest;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.logic.bci.dao.common.model.Label;
import com.baidu.bce.logic.bci.dao.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.dao.common.model.PodListModel;
import com.baidu.bce.logic.bci.dao.common.model.WebShell;
import com.baidu.bce.logic.bci.dao.common.util.PodPOComparator;
import com.baidu.bce.logic.bci.dao.container.model.ContainerPO;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.common.CommonUtils;
import com.baidu.bce.logic.bci.service.common.service.AclService;
import com.baidu.bce.logic.bci.service.common.service.BciAsyncService;
import com.baidu.bce.logic.bci.service.common.service.LogicalQuotaService;
import com.baidu.bce.logic.bci.service.common.service.LogicalResourceService;
import com.baidu.bce.logic.bci.service.common.service.LogicalTagService;
import com.baidu.bce.logic.bci.service.constant.BciStatus;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.constant.PodConstants;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.bci.service.interceptor.ResourceAccountSetting;
import com.baidu.bce.logic.bci.service.model.BciCreateResponse;
import com.baidu.bce.logic.bci.service.model.BciOrderExtra;
import com.baidu.bce.logic.bci.service.model.BciQuota;
import com.baidu.bce.logic.bci.service.model.ConfigFile;
import com.baidu.bce.logic.bci.service.model.ConfigFileDetail;
import com.baidu.bce.logic.bci.service.model.ContainerPurchase;
import com.baidu.bce.logic.bci.service.model.DeletePod;
import com.baidu.bce.logic.bci.service.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.IOrderItem;
import com.baidu.bce.logic.bci.service.model.LeakagePodDeleteRequest;
import com.baidu.bce.logic.bci.service.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.service.model.PodDetail;
import com.baidu.bce.logic.bci.service.model.PodListRequest;
import com.baidu.bce.logic.bci.service.model.PodNumberRequest;
import com.baidu.bce.logic.bci.service.model.PodNumberResponse;
import com.baidu.bce.logic.bci.service.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.PodPushLogRequest;
import com.baidu.bce.logic.bci.service.model.PodVpcResponse;
import com.baidu.bce.logic.bci.service.model.ValidatedItem;
import com.baidu.bce.logic.bci.service.model.Volume;
import com.baidu.bce.logic.bci.service.util.JsonUtil;
import com.baidu.bce.logic.bci.service.util.PodConfiguration;
import com.baidu.bce.logic.bci.service.util.PodUtils;
import com.baidu.bce.logic.bci.service.util.PodValidator;
import com.baidu.bce.logic.bci.service.util.Validator;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.utils.BeanCopyUtil;
import com.baidu.bce.plat.cloudtrail.model.Event;
import com.baidu.bce.plat.cloudtrail.model.QueryEventsRequest;
import com.baidu.bce.plat.cloudtrail.model.ResourceInfo;
import com.baidu.bce.plat.cloudtrail.model.TrailPageResponse;
import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.FlavorItem;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.hsqldb.lib.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate;


@Service
public class PodService extends BaseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PodService.class);
    private static final String QUERY_LOGIC_FAILED = "[Query failed] ";
    private static final String LOG_DELETE_PREFIX = "[delete bci] ";
    private static final String LOG_CREATE_PREFIX = "[create bci] ";
    private static final String LOG_LIST_PAGE_PREFIX = "[list pod page] ";
    private static final String ORDER_KEY = "bci";
    private static final String APPLICATION_DEFAULT = "default";
    private static final String APPLICATION_INNER = "inner";

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private LogicalTagService logicalTagService;

    @Autowired
    private PodConfiguration podConfiguration;

    @Autowired
    private AclService aclService;

    @Autowired
    private LogicalQuotaService logicalQuotaService;

    @Autowired
    private LogicalResourceService logicalResourceService;

    @Autowired
    private BciAsyncService bciAsyncService;

    @Autowired
    private AsyncExecutorService asyncExecutorService;

    @Autowired
    private Validator validator;

    @Value("${order.timeout.millis:45000}")
    private int orderTimeoutMillis;

    private static final Long TIME_SECOND = 10000000000L;
    private static final Long UTCTime = 8 * 60 * 60 * 1000L;

    public LogicPageResultResponse<PodPO> listPodsWithPageByMultiKey(PodListRequest podListRequest) {
        PodListModel podListModel = PodUtils.convertRequestModel(podListRequest, PodListModel.class);
        LogicPageResultResponse<PodPO> resultResponse = PodUtils.initEdpPageResultResponse(podListRequest);

        if (null != podListRequest.getFilterMap()) {
            if (!PodValidator.validateSearchId(podListRequest.getFilterMap())) {
                // 不合法的搜索关键字字符，直接返回空结果
                LOGGER.debug(LOG_LIST_PAGE_PREFIX + "search value contains invalid char : {}",
                        podListRequest.getFilterMap().values());
                return new LogicPageResultResponse<>();
            }

            List<String> podUuids = commonUtils.filterByTag(podListRequest);
            if (podUuids != null && podUuids.size() == 0) {
                return PodUtils.getEmptyResponse(resultResponse);
            } else if (CollectionUtils.isNotEmpty(podListModel.getIncludedUuids())) {
                Set<String> oriIdSet = new HashSet<>(podListModel.getIncludedUuids());
                oriIdSet.retainAll(podUuids);
                podListModel.setIncludedUuids(new ArrayList<>(oriIdSet));
            } else {
                podListModel.setIncludedUuids(podUuids);
            }
        }

        Map<String, String> filterMap = podListModel.getFilterMap();
        List<PodFilterQueryModel> queryList = new ArrayList<>();
        if (filterMap != null && filterMap.size() > 0) {
            Set<Map.Entry<String, String>> keywordTypeSet = filterMap.entrySet();
            for (Map.Entry<String, String> keywordTypeEntry : keywordTypeSet) {
                String keyword = keywordTypeEntry.getValue();
                String keywordType = keywordTypeEntry.getKey();
                if (StringUtils.isNotEmpty(keyword)) {
                    queryList.add(new PodFilterQueryModel(keywordType, keyword));
                }
            }
        }
        podListModel.setFilterMap(podListRequest.getFilterMap());

        List<PodPO> podPOS;
        try {
            List<PodPO> allPodPOS = podDao.listPodsByMultiKey(getAccountId(), podListModel, queryList);

            if (ResourceAccountSetting.isUnifiedCharge()) {
                // 统一计费，匹配charge source
                podPOS = filterPodByChargeSource(allPodPOS, ResourceAccountSetting.getApplication().toLowerCase());
            } else {
                // 根据charge source 过滤列表, 不展示统一计费的资源
                podPOS = filterPodByChargeSource(allPodPOS, LogicalConstant.CHARGE_SOURCE_USER);
            }

            // paging
            List<PodPO> pagePodList = paging(podPOS, podListRequest.getPageNo(), podListRequest.getPageSize());

            resultResponse.setTotalCount(podPOS.size());
            // 封装标签信息
            logicalTagService.addTagInfoToPodPO(pagePodList);
            // 根据标签排序
            if ("tag".equalsIgnoreCase(podListRequest.getTagOrderBy())) {
                if ("desc".equalsIgnoreCase(podListRequest.getTagOrder())) {
                    Collections.sort(pagePodList, new PodPOComparator(false));
                } else {
                    Collections.sort(pagePodList, new PodPOComparator(true));
                }
                resultResponse.setOrderBy("tag");
                resultResponse.setOrder(podListRequest.getTagOrder());
            }
            // 封装网络信息
            wrapNetworkForPod(pagePodList);

            resultResponse.setResult(pagePodList);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    private List<PodPO> paging(List<PodPO> podPOList, Integer pageNo, Integer pageSize) {
        int totalCount = podPOList.size();
        List<PodPO> pagePodList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = Math.max((pageNo - 1) * pageSize, 0);
            int end = Math.min(start + pageSize, totalCount);
            for (int i = start; i < end; i++) {
                pagePodList.add(podPOList.get(i));
            }
        } else {
            pagePodList = podPOList;
        }

        return pagePodList;
    }

    private void convertApplicationVo(List<PodPO> podPOS) {
        for (PodPO podPO : podPOS) {
            if (!APPLICATION_DEFAULT.equals(podPO.getApplication())) {
                podPO.setApplication(APPLICATION_INNER);
            }
            podPO.setPushLog(podPO.getEnableLog() == 1);
        }
    }

    public LogicPageResultResponse<PodPO> listPodsByUpdatedTime(String keywordType, String keyword,
                                                                long since) {
        if (since < TIME_SECOND) {
            since = since * 1000;
        }
        Timestamp updatedTime = new Timestamp(Long.parseLong(String.valueOf(since + UTCTime)));

        LOGGER.debug("list bci by since {}, updated time {}", since, updatedTime);
        LogicPageResultResponse<PodPO> resultResponse = new LogicPageResultResponse<>();

        List<PodFilterQueryModel> queryList = new ArrayList<>();

        List<PodPO> podPOS;
        try {
            List<PodPO> allPodPOS = podDao.listPodsByUpdatedTime(getAccountId(), updatedTime, keywordType, keyword);

            if (ResourceAccountSetting.isUnifiedCharge()) {
                // 统一计费，匹配charge source
                podPOS = filterPodByChargeSource(allPodPOS, ResourceAccountSetting.getApplication().toLowerCase());
            } else {
                // 根据charge source 过滤列表, 不展示统一计费的资源
                podPOS = filterPodByChargeSource(allPodPOS, LogicalConstant.CHARGE_SOURCE_USER);
            }

            resultResponse.setTotalCount(podPOS.size());
            // 封装标签信息
            logicalTagService.addTagInfoToPodPO(podPOS);

            // 封装网络信息
            wrapNetworkForPod(podPOS);

            resultResponse.setResult(podPOS);
        } catch (Exception e) {
            LOGGER.error(QUERY_LOGIC_FAILED, LOG_LIST_PAGE_PREFIX, e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return resultResponse;
    }

    public List<PodPO> getPods(List<String> podIds) {
        if (CollectionUtils.isEmpty(podIds)) {
            throw new CommonExceptions.RequestInvalidException();
        }

        PodListModel podListModel = new PodListModel();
        podListModel.setIncludedUuids(podIds);

        List<PodPO> podPOS = null;
        try {
            podPOS = podDao.listPodsByMultiKey(getAccountId(), podListModel, new ArrayList<PodFilterQueryModel>());
        } catch (Exception e) {
            LOGGER.error("[bce logical bci] Query logical failed, operation "
                    + "is {}, exception is {}", "[query server]", e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        return podPOS;
    }

    public void deletePod(PodBatchDeleteRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getDeletePods())) {
            LOGGER.warn(LOG_DELETE_PREFIX + "delete pods is empty");
            throw new PodExceptions.PodIdIsEmptyException();
        }

        bciAsyncService.getEipInstanceMapAsync();
        Map<String, EipInstance> eipInstanceMap = (Map<String, EipInstance>) asyncExecutorService.getAsyncResult(
                WorkKeyUtil.genWorkKey("getEipInstanceMapAsync", new LinkedList<Object>()));

        List<DeletePod> deletePods = request.getDeletePods();
        for (DeletePod deletePod : deletePods) {
            deletePodAndEip(deletePod.getPodId(), request.getRelatedReleaseFlag(), eipInstanceMap);
        }
    }

    public void pushLog(PodPushLogRequest request) {
        if (CollectionUtils.isEmpty(request.getPodIds())) {
            throw new PodExceptions.RequestInvalidException();
        }
        String accountId = getAccountId();
        List<PodPO> pods = podDao.listPodPOByIds(request.getPodIds(), accountId);
        PodClient podClient = logicPodClientFactory.createPodClientByAccountId(accountId);
        for (PodPO podPO : pods) {
            bciAsyncService.pushLog(podPO.getPodUuid(), podClient);
        }
        for (PodPO podPO : pods) {
            Boolean isPushLog = (Boolean) asyncExecutorService.getAsyncResult(WorkKeyUtil.genWorkKey("pushLog",
                    Arrays.asList(podPO.getPodUuid(), podClient)));
            podPO.setEnableLog(isPushLog ? 1 : 0);
        }

        try {
            podDao.enableLog(pods, accountId);
        } catch (Exception e) {
            throw new PodExceptions.InternalServerErrorException();
        }
    }

    private void deletePodAndEip(String podId, boolean relatedReleaseFlag,
                                 Map<String, EipInstance> eipInstanceMap) {
        PodPO podPO = getPodPOByPodId(podId);
        if (!podConfiguration.getDelete().contains(podPO.getStatus().toLowerCase())) {
            throw new CommonExceptions.ResourceInTaskException();
        }

        if (relatedReleaseFlag) {
            forceReleaseBindEip(eipInstanceMap, podPO.getPodUuid());
        } else {
            EipInstance eipInstance = eipInstanceMap.get(podPO.getPodUuid());
            if (eipInstance != null) {
                unBindEipFromPod(eipInstance.getEip());
            }
        }

        String accountID = getAccountId();
        if (ResourceAccountSetting.isUnifiedCharge()) {
            accountID = ResourceAccountSetting.getResourceAccountId();
        }
        // billing 的接口，得用创建订单的accountID
        logicalResourceService.deleteResourceByName(accountID, podPO.getPodUuid(), PodConstants.SERVICE_TYPE);
        // nova是用户ID创建的，用用户的ID
        deletePodFromNova(getAccountId(), podPO.getPodUuid());

        try {
            deletePodRecord(podPO);
            unBindTags(podPO);
            podDao.deletePod(podPO.getUserId(), podPO.getPodUuid());
            containerDao.deleteContainers(getAccountId(), podPO.getPodId());
        } catch (Exception e) {
            LOGGER.error(LOG_DELETE_PREFIX + "delete from logical failed, exception is {}", e);
            throwPermissionDeniedExceptionIfAppropriate(e);
            throw new PodExceptions.InternalServerErrorException();
        }
    }


    private void forceReleaseBindEip(Map<String, EipInstance> eipMap, String podId) {
        String accountId = getAccountId();
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(accountId);
        EipInstance eipInstance = eipMap.get(podId);
        if (eipInstance != null && !"shared".equalsIgnoreCase(eipInstance.getEipType())) {
            try {
                logicEipClient.forceReleaseEip(eipInstance.getEip());
            } catch (BceInternalResponseException e) {
                if ("PrepayEip".equalsIgnoreCase(e.getCode())) {
                    LOGGER.warn("Prepay EIP delete operation is not available and eipId:{}", eipInstance.getEip());
                    unBindEipFromPod(eipInstance.getEip());
                } else {
                    throw e;
                }
            }
        }
    }

    public PodDetail podDetail(String podId) {

        PodPO podPO = getPodPOByPodId(podId);
        List<ContainerPO> containers = containerDao.listByPodId(podPO.getPodUuid());
        if (CollectionUtils.isEmpty(containers)) {
            containers = containerDao.listByPodId(podPO.getPodId());
        }
        if (CollectionUtils.isEmpty(containers)) {
            throw new CommonExceptions.ResourceNotExistException();
        }
        logicalTagService.addTagInfoToPodPO(Collections.singletonList(podPO));
        wrapEipForPod(Collections.singletonList(podPO));
        PodDetail podDetail = PodUtils.convertPodDetailShow(podPO, containers);
        try {
            List<String> subnetIds = new ArrayList<>();
            subnetIds.add(podPO.getSubnetUuid());
            SubnetMapRequest subnetMapRequest = new SubnetMapRequest();
            subnetMapRequest.setSubnetIds(subnetIds);
            subnetMapRequest.setAttachVpc(true);
            SubnetMapResponse subnetMapResponse =
                    logicPodClientFactory.createExternalSubnetClient(getAccountId()).getSubnetMap(subnetMapRequest);

            SimpleSecurityGroupVO securityGroupVO = getSecurityGroup(podPO.getSecurityGroupUuid());
            podDetail.setSecurityGroup(securityGroupVO);
            podDetail.setRegion(regionConfiguration.getCurrentRegion());
            podDetail.setSubnet(subnetMapResponse.getSubnetMap().get(podPO.getSubnetUuid()));
            podDetail.setVpc(subnetMapResponse.getVpcMap().get(podDetail.getSubnet().getVpcId()));
            podDetail.setSubnetType(SubnetVo.SubnetType.findById(podDetail.getSubnet().getSubnetType()).getName());
        } catch (BceInternalResponseException e) {
            LOGGER.error("VPC server response exception:{}", e);
        }

        // 如果application不是"default"则为内部业务
        if (!APPLICATION_DEFAULT.equals(podDetail.getApplication())) {
            podDetail.setApplication(APPLICATION_INNER);
        }
        return podDetail;
    }

    public PodVpcResponse getVpcAndSubnetByPodId(String podId) {
        PodPO podPO = getPodPOByPodId(podId);
        List<String> subnetIds = new ArrayList<>();
        subnetIds.add(podPO.getSubnetUuid());
        SubnetMapRequest subnetMapRequest = new SubnetMapRequest();
        subnetMapRequest.setSubnetIds(subnetIds);
        subnetMapRequest.setAttachVpc(true);
        SubnetMapResponse subnetMapResponse =
                logicPodClientFactory.createExternalSubnetClient("").getSubnetMap(subnetMapRequest);

        SimpleSecurityGroupVO securityGroupVO = getSecurityGroup(podPO.getSecurityGroupUuid());

        PodVpcResponse response = new PodVpcResponse();
        response.setSecurityGroupVO(securityGroupVO);
        response.setSubnet(subnetMapResponse.getSubnetMap().get(podPO.getSubnetUuid()));
        response.setVpc(subnetMapResponse.getVpcMap().get(response.getSubnet().getVpcId()));
        return response;
    }

    public byte[] downloadConfigFile(String podId, String name, String path) {
        PodPO podPO = getPodPOByPodId(podId);
        String config = podPO.getConfigFile();
        String file = "";
        List<ConfigFile> configFiles = PodUtils.base64Decode(config);
        for (ConfigFile configFile : configFiles) {
            if (!configFile.getName().equals(name)) {
                continue;
            }
            for (ConfigFileDetail configFileDetail : configFile.getConfigFiles()) {
                if (configFileDetail.getPath().equals(path)) {
                    file = configFileDetail.getFile();
                    break;
                }
            }
        }
        byte[] bom4utf8 = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        byte[] bytes = file.getBytes();
        byte[] outputBytes = new byte[bom4utf8.length + bytes.length];
        System.arraycopy(bom4utf8, 0, outputBytes, 0, bom4utf8.length);
        System.arraycopy(bytes, 0, outputBytes, 3, bytes.length);
        return outputBytes;
    }

    public byte[] download(PodListRequest podListRequest) {

        StringBuffer csv = new StringBuffer("ID, 名称, 状态, CPU(核), " +
                "内存(GB), 公网IP, 公网带宽, 内网IP, 重启策略, 创建时间" + "\r\n");

        List<PodPO> result = (List<PodPO>) listPodsWithPageByMultiKey(podListRequest).getResult();
        LOGGER.debug("pod downLoad size is {}", result.size());
        if (StringUtils.isNotBlank(podListRequest.getKeywordType())) {
            result = PodUtils.likeFilter(result, podListRequest.getKeywordType(),
                    podListRequest.getKeyword());
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (PodPO podPO : result) {
            csv.append(podPO.getPodId()).append(",");
            csv.append(podPO.getName()).append(",");
            csv.append(LogicalConstant.POD_STATUS_MAP.get(podPO.getStatus().toUpperCase())).append(",");
            csv.append(podPO.getvCpu()).append(",");
            csv.append(podPO.getMemory()).append(",");
            csv.append(podPO.getPublicIp()).append(",");
            csv.append(podPO.getBandwidthInMbps()).append(",");
            csv.append(podPO.getInternalIp()).append(",");
            csv.append(LogicalConstant.POD_RESTART_POLICY_MAP.get(podPO.getRestartPolicy().toUpperCase())).append(",");
            csv.append(formatter.format(podPO.getCreatedTime())).append(",");
            csv.append("\r\n");
        }

        byte[] bom4utf8 = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        byte[] bytes = csv.toString().getBytes();
        byte[] outputBytes = new byte[bom4utf8.length + bytes.length];
        System.arraycopy(bom4utf8, 0, outputBytes, 0, bom4utf8.length);
        System.arraycopy(bytes, 0, outputBytes, 3, bytes.length);
        return outputBytes;
    }

    public BceInternalResponse logDownload(String podID, String containerName, Integer limitBytes,
                                           Integer tailLines, String sinceTime, Integer sinceSeconds,
                                           Boolean timestamps, Boolean previous, Boolean follow) {
        String host = "";
        PodPO podDetail = checkPod(podID);
        String podUUID = podDetail.getPodUuid();
        try {
            // 调Nova接口
            Server server = logicPodClientFactory.createAdminPodClient().getServer(podUUID);
            host = server.getHost();
        } catch (BceInternalResponseException e) {
            LOGGER.debug("PodClient.getServer failed : {}", e);
            if (e.getHttpStatus() == 404) {
                throw new PodExceptions.ResourceNotExistException();
            }
            throw new PodExceptions.GetServerFailed();
        }
        if (StringUtils.isEmpty(host)) {
            LOGGER.debug("server host is empty");
            throw new PodExceptions.GetServerFailed();
        }
        // cn 接口
        return logicPodClientFactory.createContainerManagerClient(host, getAccountId())
                .getPodLog(podUUID, containerName, limitBytes, tailLines, sinceTime,
                        sinceSeconds, timestamps, previous, follow);
    }

    public LogicPageResultResponse<CCRImage> listCCRImage(String keyword, String keywordType, String order,
                                                          String orderBy, Integer pageNo, Integer pageSize) {
        LogicPageResultResponse<CCRImage> response = new LogicPageResultResponse<>();

        CCRClient ccrClient = logicPodClientFactory.createCCRClient(getAccountId());
        CCRImageResponse ccrImageResponse = new CCRImageResponse();

        try {
            ccrImageResponse = ccrClient.listUserImage();
        } catch (Exception e) {
            LOGGER.debug("Fail to list user image: {}", e);
        }

        List<CCRImage> imageList = ccrImageResponse.getResult();

        Comparator<CCRImage> comparator = CCRImage.getComparator(orderBy);
        if ("desc".equalsIgnoreCase(order)) {
            Collections.sort(imageList, Collections.reverseOrder(comparator));
        } else {
            Collections.sort(imageList, comparator);
        }

        int totalCount = imageList.size();
        List<CCRImage> resultList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = (pageNo - 1) * pageSize;
            start = start < 0 ? 0 : start;
            int end = start + pageSize > totalCount ? totalCount : start + pageSize;
            for (int i = start; i < end; i++) {
                resultList.add(imageList.get(i));
            }
        } else {
            resultList = imageList;
        }
        if (pageSize == null) {
            pageSize = 1000;
        }
        if (pageNo == null) {
            pageNo = 1;
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(totalCount);
        response.setResult(resultList);
        return response;
    }

    public LogicPageResultResponse<UserImage> listUserImage(String keyword, String keywordType, String order,
                                                            String orderBy, Integer pageNo, Integer pageSize) {
        LogicPageResultResponse<UserImage> response = new LogicPageResultResponse<>();

        CceImageClient cceImageClient = logicPodClientFactory.createCceImageClient(getAccountId());
        UserImageResponse userImageResponse = new UserImageResponse();

        try {
            userImageResponse = cceImageClient.listUserImage();
        } catch (Exception e) {
            LOGGER.debug("Fail to list user image");
        }

        List<UserImage> imageList = userImageResponse.getRepositories();
        LOGGER.info("begin to get image tag, begin time: {}", new Date().toString());
        Iterator<UserImage> imageIterator = imageList.iterator();
        while (imageIterator.hasNext()) {
            UserImage image = imageIterator.next();
            if ("name".equalsIgnoreCase(keywordType) && image.getName().contains(keyword)) {
                bciAsyncService.getImageTags(image, cceImageClient);
            } else {
                imageIterator.remove();
            }
        }

        for (UserImage image : imageList) {
            ImageTags imageTags = (ImageTags) asyncExecutorService.getAsyncResult(WorkKeyUtil.genWorkKey("getImageTags",
                    Arrays.asList(image, cceImageClient)));
            image.setTags(PodUtils.getTags(imageTags));
            image.setAddress(ImageConstant.USER_IMAGE_URL + image.getNamespace() + "/" + image.getName());
        }
        LOGGER.info("finish to get image tag, finish time: {}", new Date().toString());

        Comparator<UserImage> comparator = UserImage.getComparator(orderBy);
        if ("desc".equalsIgnoreCase(order)) {
            Collections.sort(imageList, Collections.reverseOrder(comparator));
        } else {
            Collections.sort(imageList, comparator);
        }

        int totalCount = imageList.size();
        List<UserImage> resultList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = (pageNo - 1) * pageSize;
            start = start < 0 ? 0 : start;
            int end = start + pageSize > totalCount ? totalCount : start + pageSize;
            for (int i = start; i < end; i++) {
                resultList.add(imageList.get(i));
            }
        } else {
            resultList = imageList;
        }
        if (pageSize == null) {
            pageSize = 1000;
        }
        if (pageNo == null) {
            pageNo = 1;
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(totalCount);
        response.setResult(resultList);
        return response;
    }

    public LogicPageResultResponse<OfficialImage> listOfficialImage(String keyword, String keywordType, String order,
                                                                    String orderBy, Integer pageNo, Integer pageSize) {
        LogicPageResultResponse<OfficialImage> response = new LogicPageResultResponse<>();

        OfficialImageResponse officialImageResponse = logicPodClientFactory.createCceImageClient(getAccountId())
                .listOfficialImage(keyword, keywordType);
        List<CceOfficialImage> imageList = officialImageResponse.getImages();

        List<CceOfficialImage> officialImages = new LinkedList<>();
        if (StringUtils.isNotBlank(keywordType) && StringUtils.isNotBlank(keyword)) {
            // 目前只支持按镜像名搜索
            if ("name".equals(keywordType)) {
                for (CceOfficialImage imageInfo : imageList) {
                    if (StringUtils.isNotBlank(imageInfo.getRepository())
                            && imageInfo.getRepository().contains(keyword)) {
                        officialImages.add(imageInfo);
                    }
                }
            }
        } else {
            officialImages = imageList;
        }

        Map<String, OfficialImage> imageMap = new HashMap<>();
        for (CceOfficialImage image : officialImages) {
            if (imageMap.containsKey(image.getRepository())) {
                OfficialImage officialImage = imageMap.get(image.getRepository());
                officialImage.getTags().add(image.getTag());
            } else {
                OfficialImage oImage = new OfficialImage();
                oImage.setName(image.getRepository());
                oImage.setDescription(image.getDescription());
                oImage.getTags().add(image.getTag());
                oImage.setAddress(image.getAddress().substring(0, image.getAddress().lastIndexOf(":")));
                oImage.setIcon(image.getIcon());
                imageMap.put(image.getRepository(), oImage);
            }
        }

        List<OfficialImage> images = new ArrayList<>(imageMap.values());

        Comparator<OfficialImage> comparator = OfficialImage.getComparator(orderBy);
        if ("desc".equalsIgnoreCase(order)) {
            Collections.sort(images, Collections.reverseOrder(comparator));
        } else {
            Collections.sort(images, comparator);
        }

        int totalCount = images.size();
        List<OfficialImage> resultList = new LinkedList<>();
        if (pageNo != null && pageSize != null) {
            int start = (pageNo - 1) * pageSize;
            start = start < 0 ? 0 : start;
            int end = start + pageSize > totalCount ? totalCount : start + pageSize;
            for (int i = start; i < end; i++) {
                resultList.add(images.get(i));
            }
        } else {
            resultList = images;
        }
        if (pageSize == null) {
            pageSize = 1000;
        }
        if (pageNo == null) {
            pageNo = 1;
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(totalCount);
        response.setResult(resultList);
        return response;
    }

    public LogicPageResultResponse<DockerHubImage> listDockerHubImages(String keyword, String keywordType, String order,
                                                                       String orderBy, Integer pageNo,
                                                                       Integer pageSize) {

        LogicPageResultResponse<DockerHubImage> response = new LogicPageResultResponse<>();
        DockerHubImageResponse dockerImage = null;

        List<DockerHubImage> imagesByKeyword = new ArrayList<>();
        if (StringUtils.isNotEmpty(keyword) || "name".equalsIgnoreCase(keywordType)) {
            dockerImage = getImages(pageNo, 1000);
            for (DockerHubImage dockerHubImage : dockerImage.getResults()) {
                if (dockerHubImage.getName().contains(keyword)) {
                    imagesByKeyword.add(dockerHubImage);
                }
            }
        } else {
            dockerImage = getImages(pageNo, pageSize);
        }

        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(imagesByKeyword.size() > 0 ? imagesByKeyword.size() : dockerImage.getCount());
        response.setResult(imagesByKeyword.size() > 0 ? PodUtils.subList(imagesByKeyword, pageNo, pageSize)
                : dockerImage.getResults());
        return response;
    }

    public LogicPageResultResponse<String> listDockerHubImageTags(String name, Integer pageNo, Integer pageSize) {

        LogicPageResultResponse<String> response = new LogicPageResultResponse<>();
        DockerHubImageTagResponse dockerImageTags = logicPodClientFactory.createDockerHubrClient()
                .listDockerHubImageTag(name, pageNo, pageSize);

        List<String> tags = new ArrayList<>();
        for (DockerHubImageTag tag : dockerImageTags.getResults()) {
            tags.add(tag.getName());
        }

        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(dockerImageTags.getCount());
        response.setResult(tags);
        return response;
    }

    public void bindEipToPod(String eip, String podId) {
        PodPO podPO = podDao.getPodDetail(getAccountId(), podId);
        if (podPO == null) {
            throw new CommonExceptions.RequestInvalidException();
        }

        BindEipToBciRequest bindEipToBccRequest = new BindEipToBciRequest();
        bindEipToBccRequest.setInstanceType(PodConstants.SERVICE_TYPE);
        bindEipToBccRequest.setInstanceId(podPO.getPodUuid());

        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
        logicEipClient.bindEip(eip, bindEipToBccRequest);
    }

    public void unBindEipFromPod(String eip) {
        LogicEipClient logicEipClient = logicPodClientFactory.createLogicEipClient(getAccountId());
        logicEipClient.unbindEip(eip);
    }

    public BciQuota getBciQuota(Boolean needGlobalQuota) {
        return logicalQuotaService.getBciQuota();
    }

    public BciCreateResponse createPod(BaseCreateOrderRequestVo<IOrderItem> request, String from) {

        long beginTime = new Date().getTime();
        ValidatedItem validatedItem = validator.validate(request, from, null);
        LOGGER.info("validate param : {}", new Date().getTime() - beginTime);

        List<CreateNewTypeOrderItem> createNewTypeOrderRequestItems = new ArrayList<>();

        List<PodPO> pods = new ArrayList<>();
        List<ContainerPO> containers = new ArrayList<>();

        createNewTypeOrderRequestItems.addAll(handleInstanceCreateBci(validatedItem, pods, containers));

        createNewTypeOrderRequestItems.addAll(handleBciCreateEip(validatedItem));

        List<String> instanceIds = commonUtils.savePodCreate2DB(pods, containers);

        LOGGER.debug("create server,createNewTypeOrderRequestItems  is  {}", createNewTypeOrderRequestItems);

        BCCClient bccClient = logicPodClientFactory.createBCCClient(getAccountId());

        CreateOrderRequest<CreateNewTypeOrderItem> createNewTypeOrderRequest = new CreateOrderRequest<>();
        createNewTypeOrderRequest.setOrderType(OrderType.NEW.name());
        createNewTypeOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createNewTypeOrderRequest.setTotal(request.getTotal());
        createNewTypeOrderRequest.setTicketId(request.getTicketId());
        createNewTypeOrderRequest.setPaymentMethod(request.getPaymentMethod());

        createNewTypeOrderRequest.setItems(createNewTypeOrderRequestItems);
        OrderUuidResult orderUuidResult = new OrderUuidResult();
        AttachVolumeUpdateDbRequest attachVolumeRequest = getAttachVolumeRequest(pods,
                validatedItem.getPodPurchaseRequest());
        try {
            // attach volume 抢占
            if (attachVolumeRequest != null) {
                bccClient.attachVolume(attachVolumeRequest);
            }
            orderUuidResult = submitCreateOrderToServiceCatalog(createNewTypeOrderRequest, LOG_CREATE_PREFIX);
        } catch (Exception e) {
            LOGGER.error("failed to create new order, update status for pod : {}", instanceIds);
            for (PodPO pod : pods) {
                podDao.deletePod(getAccountId(), pod.getPodId());
            }
            if (attachVolumeRequest != null) {
                // 回滚volume状态
                LOGGER.error("rollback volume attachment");
                AttachVolumeRollbackDbRequest rollbackDbRequest = new AttachVolumeRollbackDbRequest();
                rollbackDbRequest.setDiskIds(attachVolumeRequest.getDiskIds());
                rollbackDbRequest.setInstanceUuid(attachVolumeRequest.getInstanceUuid());
                bccClient.rollbackVolume(rollbackDbRequest);
            }

            throwPermissionDeniedExceptionIfAppropriate(e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        podDao.updateOrderId(instanceIds, getAccountId(), orderUuidResult.getOrderId());

        BciCreateResponse bciCreateResponse = new BciCreateResponse();
        bciCreateResponse.setOrderId(orderUuidResult.getOrderId());
        bciCreateResponse.setPodIds(instanceIds);
        return bciCreateResponse;
    }

    /**
     * 统一提交创建order请求到ServiceCatalog
     *
     * @param createOrderRequest
     * @param logPrefix
     * @return
     */
    public OrderUuidResult submitCreateOrderToServiceCatalog(CreateOrderRequest createOrderRequest, String logPrefix) {
        OrderUuidResult result = null;
        try {
            ServiceCatalogOrderClient orderClient = getServiceCatalogOrderClient(logPrefix);
            orderClient.setReadTimeout(orderTimeoutMillis);
            result = orderClient.createOrder(createOrderRequest);

            if (StringUtils.isEmpty(result.getOrderId())) {
                LOGGER.error(logPrefix + "create order failed, order id is null");
                throw new CommonExceptions.InternalServerErrorException();
            }
        } catch (Exception e) {
            LOGGER.error(logPrefix + "revoke billing interface failed, exception is {}", e);
            throw e;
        }
        return result;
    }

    /**
     * 创建访问订单的Client
     *
     * @param prefix
     * @return
     */
    protected ServiceCatalogOrderClient getServiceCatalogOrderClient(String prefix) {
        ServiceCatalogOrderClient serviceCatalogOrderClient = null;
        String accountID = getAccountId();
        if (ResourceAccountSetting.isUnifiedCharge()) {
            accountID = ResourceAccountSetting.getResourceAccountId();
        }
        try {
            serviceCatalogOrderClient = logicPodClientFactory.createServiceCatalogOrderClient(accountID);
        } catch (Exception e) {
            LOGGER.warn(prefix + "create serviceCatalogOrderClient failed, exception is {}", e);
            throw new CommonExceptions.InternalServerErrorException();
        }

        if (serviceCatalogOrderClient == null) {
            LOGGER.info(prefix + "serviceCatalogOrderClient is null");
            throw new CommonExceptions.InternalServerErrorException();
        }
        return serviceCatalogOrderClient;
    }

    private List<CreateNewTypeOrderItem> handleInstanceCreateBci(ValidatedItem validatedItem, List<PodPO> pods,
                                                                 List<ContainerPO> containers) {
        List<CreateNewTypeOrderItem> createNewTypeOrderItems = new ArrayList<>();
        PodPurchaseRequest podPurchaseRequest = validatedItem.getPodPurchaseRequest();
        LOGGER.debug("BciPurchaseRequest={}", podPurchaseRequest);

        generatePodAndContainerPO(pods, podPurchaseRequest, containers);

        int purchaseCount = podPurchaseRequest.getPurchaseNum();

        OrderCreateRequest orderCreate = parseInstancePurchaseToOrder(podPurchaseRequest);
        Order.Item orderItem = orderCreate.getItems().iterator().next();

        CreateNewTypeOrderItem bciCreateNewTypeOrderItem = new CreateNewTypeOrderItem();
        bciCreateNewTypeOrderItem.setPaymentMethod(validatedItem.getBciPaymentModel());
        bciCreateNewTypeOrderItem.setPurchaseOrder(validatedItem.getBciPurchaseOrder());
        bciCreateNewTypeOrderItem.setProductType(podPurchaseRequest.getProductType().toLowerCase());
        bciCreateNewTypeOrderItem.setServiceType(podPurchaseRequest.getServiceType().toUpperCase());
        bciCreateNewTypeOrderItem.setSubProductType(orderCreate.getSubProductType());
        bciCreateNewTypeOrderItem.setExtra(generateExtra(podPurchaseRequest, pods.get(0).getPodId()));
        Set<FlavorItem> flavorItems = BeanCopyUtil.copySetCollection(new HashSet<Object>(orderItem.getFlavor()));
        bciCreateNewTypeOrderItem.setFlavor(new LinkedHashSet<FlavorItem>(flavorItems));
        bciCreateNewTypeOrderItem.setKey(orderItem.getKey());
        bciCreateNewTypeOrderItem.setTime(orderItem.getTime());
        bciCreateNewTypeOrderItem.setTimeUnit(orderItem.getTimeUnit());
        bciCreateNewTypeOrderItem.setReleaseTime(orderCreate.getReleaseTime());
        bciCreateNewTypeOrderItem.setCount(purchaseCount);
        createNewTypeOrderItems.add(bciCreateNewTypeOrderItem);

        return createNewTypeOrderItems;
    }

    private String generateExtra(PodPurchaseRequest podPurchaseRequest, String podId) {

        BciOrderExtra bciOrderExtra = new BciOrderExtra();
        bciOrderExtra.setContainers(podPurchaseRequest.getContainerPurchases());
        bciOrderExtra.setImageRegistrySecrets(podPurchaseRequest.getImageRegistrySecrets());
        bciOrderExtra.setVolume(podPurchaseRequest.getVolume());
        bciOrderExtra.setRestartPolicy(podPurchaseRequest.getRestartPolicy());
        bciOrderExtra.setTags(podPurchaseRequest.getTags());
        bciOrderExtra.setLogicalZone(podPurchaseRequest.getLogicalZone());
        bciOrderExtra.setZoneId(podPurchaseRequest.getZoneId());
        bciOrderExtra.setName(podPurchaseRequest.getName());
        bciOrderExtra.setAnnotations(podPurchaseRequest.getAnnotations());
        bciOrderExtra.setSubnetUuid(podPurchaseRequest.getSubnetUuid());
        bciOrderExtra.setPhysicalZone(podPurchaseRequest.getEncryptedPhysicalZone());
        bciOrderExtra.setLabels(podPurchaseRequest.getLabels());
        bciOrderExtra.setEnableLog(podPurchaseRequest.isEnableLog());
        String groupId = podPurchaseRequest.getSecurityGroupId();
        if (!isDefaultSecurityGroupId(groupId)) {
            SimpleSecurityGroupVO securityGroupVO = getSecurityGroup(groupId);
            groupId = securityGroupVO.getUuid();
        }
        bciOrderExtra.setSecurityGroupId(groupId);
        bciOrderExtra.setExtraAccountID("");
        bciOrderExtra.setChargeSource("");
        // 如果是统一计费，需要在extra中设置用户的accountID、chargeSource
        if (ResourceAccountSetting.isUnifiedCharge()) {
            bciOrderExtra.setExtraAccountID(getAccountId());
            bciOrderExtra.setChargeSource(ResourceAccountSetting.getApplication().toLowerCase());
        }
        checkOrderLabels(bciOrderExtra);
        // 新增 pod 短ID label
        Label label = new Label();
        label.setLabelKey(LogicalConstant.LABEL_POD_ID);
        label.setLabelValue(podId);
        bciOrderExtra.getLabels().add(label);

        String extra = "";
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            extra = objectMapper.writeValueAsString(bciOrderExtra);
        } catch (IOException e) {
            throw new PodExceptions.JsonTransforException();
        }
        return extra;
    }

    private SimpleSecurityGroupVO getSecurityGroup(String securityGroupId) {
        SecurityGroupClient securityGroupClient = logicPodClientFactory.createSecurityGroupClient(getAccountId());
        List<String> securityGroupIds = new ArrayList<>();
        securityGroupIds.add(securityGroupId);
        SecurityGroupSimpleInstancesVO securityGroupSimpleInstancesVO = securityGroupClient
                .getSimpleSecurityGroupListByIds(securityGroupIds);
        if (securityGroupSimpleInstancesVO == null
                || CollectionUtils.isEmpty(securityGroupSimpleInstancesVO.getList())) {
            throw new CommonExceptions.RequestInvalidException();
        }
        for (SimpleSecurityGroupVO securityGroupVO : securityGroupSimpleInstancesVO.getList()) {
            if (securityGroupId.equalsIgnoreCase(securityGroupVO.getSecurityGroupId())
                    || securityGroupId.equalsIgnoreCase(securityGroupVO.getUuid())) {
                return securityGroupVO;
            }
        }
        throw new CommonExceptions.RequestInvalidException();
    }

    private boolean isDefaultSecurityGroupId(String sgId) {
        return org.apache.commons.lang.StringUtils.isEmpty(sgId) || "default".equals(sgId);
    }

    private OrderCreateRequest parseInstancePurchaseToOrder(PodPurchaseRequest podPurchaseRequest) {
        LOGGER.debug("create bci, parameters is {}", podPurchaseRequest);

        OrderCreateRequest orderCreateRequest = new OrderCreateRequest();
        orderCreateRequest.setOrderUuid(podPurchaseRequest.getOrderUuid());

        encapsulateOrderBasicInfo(podPurchaseRequest.getProductType(), orderCreateRequest);
        encapsulateOrderItems(podPurchaseRequest, orderCreateRequest);

        String accountID = getAccountId();

        orderCreateRequest.setUserId(getAccountId());
        orderCreateRequest.setAccountId(getAccountId());

        return orderCreateRequest;
    }

    private void encapsulateOrderBasicInfo(String productType, OrderCreateRequest orderCreateRequest) {
        if (Payment.isPostpay(productType)) {
            orderCreateRequest = orderCreateRequest.withNeedConfirm(false);
        }

        orderCreateRequest.setUserId(getAccountId());
        orderCreateRequest.setAccountId(getAccountId());
        orderCreateRequest.setServiceType(PodConstants.SERVICE_TYPE);
        orderCreateRequest.setProductType(productType);
    }

    private void encapsulateOrderItems(PodPurchaseRequest podPurchaseRequest,
                                       OrderCreateRequest orderCreateRequest) {
        List<Order.Item> items = new ArrayList<>();
        Order.Item item = new Order.Item();
        item.setRegion(regionConfiguration.getCurrentRegion());
        item.setCount(podPurchaseRequest.getPurchaseNum());
        item.setKey(ORDER_KEY);

        item.setFlavor(generateOrderFlavor(podPurchaseRequest));

        items.add(item);
        orderCreateRequest.setItems(items);
    }

    private Flavor generateOrderFlavor(PodPurchaseRequest podPurchaseRequest) {
        float vcpu = podPurchaseRequest.getCpu();
        float memory = podPurchaseRequest.getMemory();
        float cdsPurchaseSize = podPurchaseRequest.getCdsPurchaseSize();

        Flavor flavor = new Flavor();

        Flavor.FlavorItem item0 = new Flavor.FlavorItem();
        item0.setName(LogicalConstant.CPU);
        item0.setValue("1");
        item0.setScale(new BigDecimal(String.valueOf(vcpu)));
        flavor.add(item0);

        Flavor.FlavorItem item1 = new Flavor.FlavorItem();
        item1.setName(LogicalConstant.MEMORY);
        item1.setValue("1");
        item1.setScale(new BigDecimal(String.valueOf(memory)));
        flavor.add(item1);

        if (cdsPurchaseSize > 0) {
            Flavor.FlavorItem item2 = new Flavor.FlavorItem();
            item2.setName(LogicalConstant.DISK);
            item2.setValue("1");
            item2.setScale(new BigDecimal(String.valueOf(cdsPurchaseSize)));
            flavor.add(item2);
        }

        return flavor;
    }

    private void generatePodAndContainerPO(List<PodPO> podPOS,
                                           PodPurchaseRequest podPurchaseRequest,
                                           List<ContainerPO> containers) {
        for (int i = 0, n = podPurchaseRequest.getPurchaseNum(); i < n; i++) {
            PodPO podPO = initPodPO(podPurchaseRequest);
            generateInstanceCreateContainerPO(podPurchaseRequest, containers, podPO.getPodId());
            podPOS.add(podPO);
        }
    }

    private void generateInstanceCreateContainerPO(PodPurchaseRequest podPurchaseRequest,
                                                   List<ContainerPO> containers,
                                                   String podId) {
        for (ContainerPurchase containerPurchase : podPurchaseRequest.getContainerPurchases()) {
            // 套餐白名单可以绕过
            if (commonUtils.checkWhiteList(LogicalConstant.VALIDATE_CPU_MEM, regionConfiguration.getCurrentRegion(),
                    getAccountId())) {
                podValidator.validateCpuAndMemoryInWhiteList(containerPurchase.getName(), containerPurchase.getCpu(),
                        containerPurchase.getMemory());
            } else {
                podValidator.validateCpuAndMemory(containerPurchase.getName(), containerPurchase.getCpu(),
                        containerPurchase.getMemory());
            }

            podValidator.validateMountVolumeType(containerPurchase, podPurchaseRequest.getVolume());
            if (StringUtils.isNotEmpty(containerPurchase.getName()) &&
                    !PodUtils.validaterName(containerPurchase.getName())) {
                LOGGER.error("NameInvalidException,the container name {} is invalidate", containerPurchase.getName());
                throw new PodExceptions.NameInvalidException();
            }
            ContainerPO containerPO = new ContainerPO();
            containerPO.setPodUuid(podId);
            containerPO.setName(containerPurchase.getName());
            containerPO.setArgs(containerPurchase.getArgs() == null ? "" : JsonUtil.toJSON(
                    containerPurchase.getArgs()));
            containerPO.setCommands(containerPurchase.getCommands() == null ? "" : JsonUtil.toJSON(
                    containerPurchase.getCommands()));
            containerPO.setCpu(containerPurchase.getCpu());
            containerPO.setImageName(containerPurchase.getImageName());
            containerPO.setImageVersion(containerPurchase.getImageVersion());
            containerPO.setImageAddress(containerPurchase.getImageAddress());
            containerPO.setMemory(containerPurchase.getMemory());
            containerPO.setCreatedTime(new Timestamp(new Date().getTime()));
            containerPO.setUpdatedTime(new Timestamp(new Date().getTime()));
            containerPO.setEnvs(containerPurchase.getEnvs() == null ? "" :
                    JsonUtil.toJSON(containerPurchase.getEnvs()));
            containerPO.setImagePullPolicy(containerPurchase.getImagePullPolicy());
            containerPO.setUserId(getAccountId());
            containerPO.setPorts(containerPurchase.getPorts() == null ? "" :
                    JsonUtil.toJSON(containerPurchase.getPorts()));
            containerPO.setVolumeMounts(containerPurchase.getVolumeMounts() == null ? "" :
                    JsonUtil.toJSON(containerPurchase.getVolumeMounts()));
            containerPO.setWorkingDir(containerPurchase.getWorkingDir());
            containers.add(containerPO);
        }
    }

    public List<CreateNewTypeOrderItem> handleBciCreateEip(ValidatedItem validatedItem) {
        List<CreateNewTypeOrderItem> createNewTypeOrderItems = new ArrayList<>();
        if (null == validatedItem.getEipPurchaseRequest()) {
            return createNewTypeOrderItems;
        }

        EipPurchaseRequest eipPurchaseRequest = validatedItem.getEipPurchaseRequest();

        LOGGER.info("EipNewOrderItem={}", eipPurchaseRequest);

        OrderCreateRequest orderCreate = null;
        try {

            orderCreate = logicPodClientFactory.createLogicEipClient(getAccountId())
                    .getEipCreateRequestForLogicalBCC(eipPurchaseRequest);
        } catch (BceInternalResponseException e) {
            LOGGER.info("getEipCreateRequestForLogicalBCC error with: error =", e);
            if ("ExceedLimit".equalsIgnoreCase(e.getCode())) {
                throw new PodExceptions.EipQuotaExceedLimitException();
            }
            throw new CommonExceptions.RequestInvalidException();
        } catch (Exception e) {
            LOGGER.info("getEipCreateRequestForLogicalBCC error with: error =", e);
            throw new CommonExceptions.RequestInvalidException();
        }
        Order.Item orderItem = orderCreate.getItems().iterator().next();
        orderItem.setExtra("eipExtra:" + orderItem.getExtra());
        // 是否开启自动续费
        String autoRenewTimeUnitStr = "autoRenewTimeUnit:" + eipPurchaseRequest.getAutoRenewTimeUnit();
        String autoRenewTimeStr = "autoRenewTime:" + eipPurchaseRequest.getAutoRenewTime();
        if (orderItem.getExtra() == null) {
            orderItem.setExtra("");
        }
        orderItem.setExtra(autoRenewTimeUnitStr + ";" + autoRenewTimeStr + ";" + orderItem.getExtra());

        CreateNewTypeOrderItem createNewTypeOrderRequestItem = new CreateNewTypeOrderItem();
        createNewTypeOrderRequestItem.setPaymentMethod(validatedItem.getEipPaymentModel());
        createNewTypeOrderRequestItem.setPurchaseOrder(validatedItem.getEipPurchaseOrder());
        createNewTypeOrderRequestItem.setProductType(eipPurchaseRequest.getProductType().toLowerCase());
        createNewTypeOrderRequestItem.setServiceType(eipPurchaseRequest.getServiceType().toUpperCase());
        createNewTypeOrderRequestItem.setSubProductType(orderCreate.getSubProductType());
        createNewTypeOrderRequestItem.setCount(orderItem.getCount());
        createNewTypeOrderRequestItem.setExtra(orderItem.getExtra());
        Set<FlavorItem> flavorItems = BeanCopyUtil.copySetCollection(new HashSet<Object>(orderItem.getFlavor()));
        createNewTypeOrderRequestItem.setFlavor(new LinkedHashSet<FlavorItem>(flavorItems));
        createNewTypeOrderRequestItem.setKey(orderItem.getKey());
        createNewTypeOrderRequestItem.setTime(orderItem.getTime());
        createNewTypeOrderRequestItem.setTimeUnit(orderItem.getTimeUnit());

        createNewTypeOrderItems.add(createNewTypeOrderRequestItem);
        return createNewTypeOrderItems;
    }


    private PodPO initPodPO(PodPurchaseRequest podPurchaseRequest) {
        String shortId =
                commonUtils.createExternalId(LogicalConstant.API_POD_PREFIX);
        Volume volume = podPurchaseRequest.getVolume();

        PodPO podPO = new PodPO();
        podPO.setCceUuid(podPurchaseRequest.getCceId());
        podPO.setMemory(podPurchaseRequest.getMemory());
        podPO.setvCpu(podPurchaseRequest.getCpu());
        podPO.setRestartPolicy(podPurchaseRequest.getRestartPolicy());
        podPO.setName(podPurchaseRequest.getName());
        podPO.setPodId(shortId);
        podPO.setPodUuid(shortId);
        podPO.setStatus(BciStatus.PENDING.getStatus());
        podPO.setConfigFile(JsonUtil.toJSON(volume.getConfigFile()));
        podPO.setNfs(JsonUtil.toJSON(volume.getNfs()));
        podPO.setEmptyDir(JsonUtil.toJSON(volume.getEmptyDir()));
        podPO.setPodVolumes(JsonUtil.toJSON(volume.getPodVolumes()));
        podPO.setTags(JsonUtil.toJSON(podPurchaseRequest.getLabels()));
        podPO.setUserId(getAccountId());
        podPO.setSubnetUuid(podPurchaseRequest.getSubnetUuid());
        podPO.setSecurityGroupUuid(podPurchaseRequest.getSecurityGroupId());
        podPO.setZoneId(podPurchaseRequest.getZoneId());
        podPO.setCreatedTime(new Timestamp(new Date().getTime()));
        podPO.setUpdatedTime(new Timestamp(new Date().getTime()));
        podPO.setApplication(podPurchaseRequest.getApplication());
        podPO.setEnableLog(podPurchaseRequest.isEnableLog() ? 1 : 0);
        // 统一计费
        if (ResourceAccountSetting.isUnifiedCharge()) {
            podPO.setChargeSource(ResourceAccountSetting.getApplication().toLowerCase());
        } else {
            podPO.setChargeSource(LogicalConstant.CHARGE_SOURCE_USER);
        }

        return podPO;
    }

    private void base64Encode(List<ConfigFileDetail> configFileDetails) {
        if (CollectionUtils.isEmpty(configFileDetails)) {
            return;
        }
        for (ConfigFileDetail detail : configFileDetails) {
            detail.setFile(Base64.encodeBase64String(detail.getFile().getBytes()));
        }
    }

    private List<String> wrapEipForPod(List<PodPO> podPOS) {
        List<String> subnetIds = new ArrayList<>();
        try {
            Map<String, EipInstance> eipInstanceMap = new HashMap<>();
            bciAsyncService.getEipInstanceMapAsync();
            eipInstanceMap = (Map<String, EipInstance>) asyncExecutorService.getAsyncResult(
                    WorkKeyUtil.genWorkKey("getEipInstanceMapAsync", new LinkedList<Object>()));

            for (PodPO podPO : podPOS) {
                podPO.setRegion(regionConfiguration.getCurrentRegion());
                subnetIds.add(podPO.getSubnetUuid());
                if (eipInstanceMap.containsKey(podPO.getPodUuid())) {
                    podPO.setEipUuid(eipInstanceMap.get(podPO.getPodUuid()).getEipId());
                    podPO.setPublicIp(eipInstanceMap.get(podPO.getPodUuid()).getEip());
                    podPO.setEipGroupId(eipInstanceMap.get(podPO.getPodUuid()).getShareGroupId());
                    podPO.setBandwidthInMbps(eipInstanceMap.get(podPO.getPodUuid()).getBandwidthInMbps());
                }
            }
        } catch (Exception e) {
            // 服务降级
            LOGGER.error("unknow exception!", e);
        }
        return subnetIds;
    }

    private void wrapNetworkForPod(List<PodPO> podPOS) {
        try {
            List<String> subnetIds = wrapEipForPod(podPOS);
            SubnetMapRequest subnetMapRequest = new SubnetMapRequest();
            subnetMapRequest.setAttachVpc(true);
            subnetMapRequest.setSubnetIds(subnetIds);
            SubnetMapResponse subnetMapResponse =
                    logicPodClientFactory.createExternalSubnetClient(getAccountId()).findSubnetMap(subnetMapRequest);
            Map<String, SubnetVo> subnetMap = subnetMapResponse.getSubnetMap();
            for (PodPO podPO : podPOS) {
                if (subnetMap.containsKey(podPO.getSubnetUuid())) {
                    SubnetVo subnetVo = subnetMap.get(podPO.getSubnetUuid());
                    podPO.setSubnetUuid(subnetVo.getSubnetUuid());
                    podPO.setSubnetType(SubnetVo.SubnetType.findById(subnetVo.getSubnetType()).getName());
                } else {
                    LOGGER.warn("can not query subnetVo: subnetUUid = " + podPO.getSubnetUuid());
                }
                if (!APPLICATION_DEFAULT.equals(podPO.getApplication())) { // 转换application，内部用户返回"inner"
                    podPO.setApplication(APPLICATION_INNER);
                }
                podPO.setPushLog(podPO.getEnableLog() == 1);
            }
        } catch (Exception e) {
            // 服务降级
            LOGGER.error("unknow exception!", e);
        }
    }

    private DockerHubImageResponse getImages(int pageNo, int pageSize) {
        DockerHubClient dockerHubrClient = logicPodClientFactory.createDockerHubrClient();
        DockerHubImageResponse dockerImage = new DockerHubImageResponse();
        try {
            dockerImage = dockerHubrClient.listDockerHubImage(pageNo, pageSize);
        } catch (Exception e) {
            LOGGER.debug("Fail to list docker hub image");
        }
        return dockerImage;
    }

    /**
     * 2019-11-05 减少日志输出
     *
     * @param podListRequest
     * @return
     */
    public LogicPageResultResponse<PodEventPO> listPodEventsWithPageByMultiKey(PodListRequest podListRequest) {
        PodListModel podListModel = PodUtils.convertRequestModel(podListRequest, PodListModel.class);
        LogicPageResultResponse<PodEventPO> resultResponse = PodUtils.initEdpPageResultResponse(podListRequest);

        QueryEventsRequest request = consQueryEventsRequest(podListModel.getPageNo(), podListModel.getPageSize(),
                Integer.parseInt(podListModel.getFilterMap().get("day")));
        CloudTrailClient cloudTrailClient = logicPodClientFactory.createCloudTrailClient();
        TrailPageResponse trailPageResponse = cloudTrailClient.queryEvents(request);

        ArrayList<PodEventPO> podEventPOList = new ArrayList<>();

        if (trailPageResponse != null && CollectionUtils.isNotEmpty(trailPageResponse.getData())) {
            String podUuid = podDao.queryPodUuid(podListModel.getFilterMap().get("podId"));
            List<Event> eventList = trailPageResponse.getData();
            for (Event event : eventList) {
                List<ResourceInfo> resources = event.getResources();
                for (ResourceInfo resourceInfo : resources) {// list的index都是0
                    if ("Pod".equalsIgnoreCase(resourceInfo.getResourceType())
                            && podUuid.equalsIgnoreCase(resourceInfo.getResourceId())) {
                        PodEventPO podEventPO = convertEventPo(event);
                        podEventPOList.add(podEventPO);
                    }
                }
            }
        }
        // 排序
        boolean flag = true;
        if ("asc".equalsIgnoreCase(podListModel.getOrders().get(0).getOrder())) {
            flag = false;
        }
        PodUtils.quickSort(podEventPOList, 0, podEventPOList.size() - 1, flag);

        resultResponse.setResult(podEventPOList);
        resultResponse.setTotalCount(podEventPOList.size());
        LOGGER.info("podEventPOList data:{}, size:{}", podEventPOList, podEventPOList.size());

        return resultResponse;
    }


    public QueryEventsRequest consQueryEventsRequest(int pageNo, int pageSize, int day) {
        QueryEventsRequest request = new QueryEventsRequest();
        request.setDomainId(getAccountId());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize > 10000 ? pageSize : 10000);
        Date end = new Date();
        Date start = new Date();
        long timestamp = System.currentTimeMillis() - day * 24 * 60 * 60 * 1000L;
        start.setTime(timestamp);
        request.setStartTime(start);
        request.setEndTime(end);

        return request;
    }

    private PodEventPO convertEventPo(Event event) {
        PodEventPO eventPO = new PodEventPO();
        eventPO.setEventName(event.getEventName());
        eventPO.setEventType(event.getEventDetail().getAdditionalEventData().get("bciEventType"));
        eventPO.setDescription(event.getDescription());
        eventPO.setEventTime(event.getEventTime());

        return eventPO;
    }

    public String getWebshellUrl(WebShell webshell) {
        String podId = webshell.getPodId();
        // 如果是内部产品，封禁webshell，抛出异常
        PodPO podPO = getPodPOByPodId(podId);
        if (podPO != null && !APPLICATION_DEFAULT.equals(podPO.getApplication())) {
            throw new PodExceptions.OperationNotAvailable();
        }
        Boolean tty = webshell.getTty();
        Boolean stdin = webshell.getStdin();
        String containerName = webshell.getContainerName();
        List<String> command = webshell.getCommand();
        if (!StringUtil.isEmpty(podId) && podId.startsWith("p-")) {
            podId = podDao.queryPodUuid(podId); // 短id 转化为长ID
        }
        String containerId = containerDao.queryContainerUuid(podId, containerName);
        LOGGER.info("webshell param:podId is {} , tty is {} , stdin is {} , command is {} , containerId is {}",
                podId, tty, stdin, command, containerId);
        ExecArgs args = new ExecArgs(containerId, tty, stdin, command);
        WebShellRequest request = new WebShellRequest(PodConstants.WEBSHELL, args);
        WebshellResponse response = logicPodClientFactory.createPodClientByAccountId(getAccountId())
                .getWebUrl(podId, request);
        return response.getUrl();
    }

    private PodPO checkPod(String podID) {
        if (StringUtils.isEmpty(podID)) {
            throw new PodExceptions.PodIdIsEmptyException();
        }
        PodPO podDetail = podDao.getPodDetail(getAccountId(), podID);
        if (null == podDetail) {
            throw new PodExceptions.ResourceNotExistException();
        }
        return podDetail;
    }

    private List<PodPO> filterPodByChargeSource(List<PodPO> pods, String chargeSource) {
        List<PodPO> filteredPods = new LinkedList<>();
        for (PodPO podPO : pods) {
            if (podPO.getChargeSource().equalsIgnoreCase(chargeSource)) {
                filteredPods.add(podPO);
            }
        }
        return filteredPods;
    }

    private void checkOrderLabels(BciOrderExtra bciOrderExtra) {
        List<Label> labels = bciOrderExtra.getLabels();
        if (CollectionUtils.isEmpty(labels)) {
            labels = new LinkedList<>();
        }

        String accountID = getAccountId();
        boolean existed = false;
        for (Label label : labels) {
            if (LogicalConstant.LABEL_ACCOUNT_ID.equals(label.getLabelKey())) {
                label.setLabelValue(accountID);
                existed = true;
            }
        }
        if (!existed) {
            Label label = new Label();
            label.setLabelKey(LogicalConstant.LABEL_ACCOUNT_ID);
            label.setLabelValue(accountID);
            labels.add(label);
        }

        bciOrderExtra.setLabels(labels);
    }

    public void deleteLeakagePod(LeakagePodDeleteRequest request) {
        if (request == null || StringUtils.isEmpty(request.getPodId())
                || StringUtils.isEmpty(request.getPodUuid())) {
            LOGGER.warn(LOG_DELETE_PREFIX + "pods info is empty");
            throw new PodExceptions.LeakagePodInfoIsEmptyException();
        }

        bciAsyncService.getEipInstanceMapAsync();
        Map<String, EipInstance> eipInstanceMap = (Map<String, EipInstance>) asyncExecutorService.getAsyncResult(
                WorkKeyUtil.genWorkKey("getEipInstanceMapAsync", new LinkedList<Object>()));

        deleteLeakagePodAndEip(request.getPodId(), request.getPodUuid(), request.getRelatedReleaseFlag(),
                eipInstanceMap);
    }

    private void deleteLeakagePodAndEip(String podId, String podUuid, boolean relatedReleaseFlag,
                                        Map<String, EipInstance> eipInstanceMap) {

        if (relatedReleaseFlag) {
            forceReleaseBindEip(eipInstanceMap, podUuid);
        } else {
            EipInstance eipInstance = eipInstanceMap.get(podUuid);
            if (eipInstance != null) {
                unBindEipFromPod(eipInstance.getEip());
            }
        }

        String accountID = getAccountId();
        if (ResourceAccountSetting.isUnifiedCharge()) {
            accountID = ResourceAccountSetting.getResourceAccountId();
        }
        // billing 的接口，得用创建订单的accountID
        logicalResourceService.deleteResourceByName(accountID, podUuid, PodConstants.SERVICE_TYPE);
        // nova是用户ID创建的，用用户的ID
        deletePodFromNova(getAccountId(), podUuid);

        unBindTags(podId, podUuid);
    }

    public PodNumberResponse getPodNumberBySubnetId(PodNumberRequest request) {
        PodNumberResponse response = new PodNumberResponse();
        List<PodNumberResponse.PodNumber> podNumbers = new LinkedList<>();
        if (CollectionUtils.isEmpty(request.getSubnetIds())) {
            return response;
        }

        Map<String, Map<String, Long>> numbers = podDao.countPodWithSubnetId(getAccountId(), request.getSubnetIds());
        for (Map.Entry<String, Map<String, Long>> entry: numbers.entrySet()) {
            PodNumberResponse.PodNumber podNumber = new PodNumberResponse.PodNumber();
            podNumber.setSubnetId(entry.getKey());
            podNumber.setBciNum(entry.getValue().get("num").intValue());
            podNumbers.add(podNumber);
        }
        response.setList(podNumbers);
        return response;
    }

    public AttachVolumeUpdateDbRequest getAttachVolumeRequest(List<PodPO> pods,
                                                              PodPurchaseRequest podPurchaseRequest) {
        if (CollectionUtils.isEmpty(podPurchaseRequest.getVolume().getPodVolumes())) {
            return null;
        }
        AttachVolumeUpdateDbRequest attachVolumeRequest = new AttachVolumeUpdateDbRequest();
        PodPO pod = pods.get(0);
        List<Volume.PodVolume> podVolumes = podPurchaseRequest.getVolume().getPodVolumes();

        attachVolumeRequest.setInstanceUuid(pod.getPodId());
        List<String> volumeIds = new LinkedList<>();
        for (Volume.PodVolume podVolume : podVolumes) {
            if (podVolume != null && podVolume.getVolumeSource() != null
                    && podVolume.getVolumeSource().getCds() != null
                    && StringUtils.isNotEmpty(podVolume.getVolumeSource().getCds().getUuid())) {
                volumeIds.add(podVolume.getVolumeSource().getCds().getUuid());
            }
        }
        if (CollectionUtils.isEmpty(volumeIds)) {
            return null;
        }
        attachVolumeRequest.setDiskIds(volumeIds);

        return attachVolumeRequest;
    }
}
