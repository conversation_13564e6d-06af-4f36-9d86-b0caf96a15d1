package com.baidu.bce.logic.bci.service.model;

import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bcc.sdk.model.flavor.ZoneResourceDetail;
import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

@Data
@Accessors(chain = true)
public class ValidatedItem {
    private PodPurchaseRequest podPurchaseRequest;
    private EipPurchaseRequest eipPurchaseRequest;

    private String from;
    private String productType;
    private String logicalZone;
    private String accessKey;
    private List<String> whiteList;

    private Set<PaymentModel> bciPaymentModel;
    private int bciPurchaseOrder;
    private Set<PaymentModel> eipPaymentModel;
    private int eipPurchaseOrder;
    private ZoneMapDetail zoneMap = new ZoneMapDetail();
    private ZoneResourceDetail zoneResourceDetail;
}
