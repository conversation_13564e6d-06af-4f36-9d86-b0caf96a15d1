INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtUI4D', 'ffcdfa25-6edc-4b7c-943d-d5225050086a', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', 'ecgttrscxrzrryrtvsxz', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtfwef', 'ffcdfa25-6edc-4b7c-943d-d52250500few', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtfwef', 'ffcdfa25-6edc-4b7c-943d-d52250500123', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtfvevf', 'ffcdfa25-6edc-4b7c-943d-d522505012e1e', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod_v2`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-4WRtfvevf-v2', 'ffcdfa25-6edc-4b7c-943d-d522505012e1e-v2', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_container`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d5225050086a', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);

INSERT INTO `t_container_v2`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d522505012e1e-v2', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);


INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-delete', 'ffcdfa25-6edc-4b7c-943d-d52250qqqqqq', 'test-event', 'running', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');


INSERT INTO `t_container`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d52250500123', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);

INSERT INTO `t_container`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d522505012e1e', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);


INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-pending', 'ffcdfa25-6edc-4b7c-943d-d5fwwf', 'test-event', 'Pending', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');

INSERT INTO `t_pod`(`pod_id`, `pod_uuid`,`name`, `status`, `v_cpu`, `memory`, `eip_uuid`, `public_ip`, `cce_uuid`, `internal_ip`,`subnet_uuid`,`security_group_uuid`, `restart_policy`, `order_id`, `tags`, `description`, `user_id`, `zone_id`, `resource_uuid`, `task_status`, `nfs`, `empty_dir`, `config_file`, `created_time`, `updated_time`, `deleted_time`, `deleted`, `internal_ipv6`)
VALUES ('p-3reeev3', 'ffcdfa25-6edc-4b7c-943d-4tgsfe', 'test-event', 'Pending', 1, 1, '', '', '', '*************', '9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1', 'g-2ya3sszj7km2', 'Always','b5e691ab-6b7f-4263-a171-761b6c2a3e5d', '[]', 'description', 'bb45087dee674fcaa21d75b53a35f7fc','z-ejQwNt8E', '', '', '[{"name":"nfs","server":"cfs.baidu.com","path":"/data/container","readOnly":true}]', '[{"name":"emptydir"}]', '[{"name":"config","configFiles":[{"path":"pod/config","file":"在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]', '2019-04-15 14:12:54', '2019-04-15 14:12:34', '2019-04-01 14:12:39', 0, '');


INSERT INTO `t_container`(`pod_uuid`, `name`, `container_uuid`, `image_name`, `image_version`, `image_address`, `cpu`, `memory`, `working_dir`, `image_pull_policy`, `commands`, `args`, `ports`, `volume_mounts`, `envs`, `user_id`, `previous_state`, `current_state`, `restart_count`, `created_time`, `updated_time`, `deleted_time`, `deleted`)
VALUES ('ffcdfa25-6edc-4b7c-943d-d5225011111', 'container01', '16151de01cefd87e78322709ac42b12e20254439d0679d1282ac05645cab2235', 'paddlecloud-job', 'container01', '', 0.25, 0.50, 'working_dir', '', '["cmd"]', '["args"]', '[{"port":8080,"protocol":"TCP"}]', '[{"mountPath":"/usr","readOnly":false,"name":"nfs"}]', '[{"labelKey":"envs","labelValue":"envs"}]', 'bb45087dee674fcaa21d75b53a35f7fc', '{"state":"Failed","containerStartTime":"2019-05-14T16:33:48Z","exitCode":255,"containerFinishTime":"2019-05-14T16:33:58Z","detailStatus":"container is dead"}', '{"state":"Running","containerStartTime":"2019-05-14T16:34:08Z","detailStatus":"container is running"}', 1, '2019-04-29 14:39:37', '2019-04-29 14:39:37', '1971-01-01 08:00:01', 0);

INSERT INTO `t_pod_charge_record`(`id`, `msg_id`, `push_time`, `charge_time`, `last_charge_time`, `lock_id`, `lock_time`, `push_status`)
VALUES (96095, '2019-10-22-00-28', '2019-10-22 00:00:00', '2019-10-21 23:58:40', '2019-10-21 23:57:40', '', '1971-01-01 08:00:01', '');


insert into t_zone(zone_id,account_id,logical_zone,physical_zone,subnet_id,type) values ('z-ejQwNt8E','be2cadc4547f4b3d93d63e830a33c3ec', 'zoneA', 'AZONE-nmg02',    '023db2e4-0f83-43ac-a690-dc6b3b10f5b2','available');
insert into t_zone(zone_id,account_id,logical_zone,physical_zone,subnet_id,type) values ('z-5ye4Vxad','be2cadc4547f4b3d93d63e830a33c3ec', 'zoneB', 'AZONE-dbl',      '9d9ea609-e8db-44b5-b32a-0b1872674023','available');
insert into t_zone(zone_id,account_id,logical_zone,physical_zone,subnet_id,type) values ('z-vvfgnIkW','be2cadc4547f4b3d93d63e830a33c3ec', 'zoneC', 'AZONE-sandboxc', '59927d0d-53dd-4156-840b-e3e889b8b99a','available');
