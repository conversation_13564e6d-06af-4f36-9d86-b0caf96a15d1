{"items": [{"config": {"productType": "postpay", "region": "bj", "restartPolicy": "Always", "vpcId": "ed82e80f-8e2a-40e3-804c-96d5eb040a53", "logicalZone": "zoneC", "securityGroupId": "g-2ya3sszj7km2", "subnetId": "9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1", "name": "param", "vpcUuid": "ed82e80f-8e2a-40e3-804c-96d5eb040a53", "subnetUuid": "9dbd8d71-1dee-4d9e-a78a-b2038f0fd2f1", "volumes": {"nfs": [{"name": "nfs", "server": "cfs.baidu.com", "path": "/data/container", "readOnly": true}], "emptyDir": [{"name": "emptydir"}], "configFile": [{"name": "config", "configFiles": [{"path": "pod/config", "file": "在平台组Java架构体系中，bce-plat-web-framework处于最基础位置"}]}]}, "containers": [{"name": "container01", "cpu": 0.25, "memory": 0.5, "imageName": "busybox", "imageAddress": "hub.baidubce.com/liqilong/busybox", "imageVersion": "musl", "envs": [{"key": "java_home", "value": "/usr/local/java"}], "ports": [{"protocol": "TCP", "port": "8090"}], "workingDir": "working", "commands": ["sleep"], "args": ["200"], "volumeMounts": [{"readOnly": false, "name": "nfs", "mountPath": "/usr/local/share"}]}], "imageRegistrySecret": [{"server": "http://hub.baidubce.com/liqilong/", "userName": "", "password": ""}], "tags": [{"tagKey": "默认项目", "tagValue": "tag"}], "serviceType": "BCI"}, "paymentMethod": []}], "paymentMethod": []}