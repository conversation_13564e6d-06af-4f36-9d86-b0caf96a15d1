CREATE TABLE `t_pod` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_id` varchar(255) NOT NULL,
  `pod_uuid` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `status` varchar(40) NOT NULL,
  `sub_status` varchar(64) NOT NULL DEFAULT '' COMMENT 'sub status',
  `v_cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `eip_uuid` varchar(255) NOT NULL,
  `eip_id` varchar(40) NOT NULL DEFAULT '',
  `public_ip` varchar(100) NOT NULL,
  `bandwidth_in_mbps` int(11) NOT NULL DEFAULT 0,
  `eip_route_type` varchar(40) NOT NULL DEFAULT '',
  `eip_pay_method` varchar(40) NOT NULL DEFAULT '',
  `eip_actual_status` varchar(40) NOT NULL DEFAULT '',
  `eip_status` int(11) NOT NULL DEFAULT 0,
  `cce_uuid` varchar(255) NOT NULL,
  `internal_ip` varchar(100) NOT NULL,
  `subnet_uuid` varchar(100) NOT NULL,
  `security_group_uuid` varchar(1000) NOT NULL,
  `restart_policy` varchar(40) NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `tags` varchar(1000) NOT NULL,
  `description` varchar(1000) NOT NULL,
  `user_id` varchar(255) NOT NULL,
  `zone_id` varchar(255) NOT NULL,
  `resource_uuid` varchar(1000) NOT NULL,
  `task_status` varchar(40) NOT NULL,
  `nfs` text NOT NULL,
  `empty_dir` text NOT NULL,
  `config_file` text NOT NULL,
  `pod_volumes` varchar(1000) NOT NULL,
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(11) NOT NULL,
  `enable_log` int(11) NOT NULL,
  `application` varchar(255) NOT NULL,
  `charge_source` varchar(32) NOT NULL DEFAULT 'user',
  `internal_ipv6` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT='pod信息表';

CREATE TABLE `t_container` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_uuid` varchar(255) NOT NULL DEFAULT '',
  `name` varchar(40) NOT NULL DEFAULT '',
  `container_uuid` varchar(255) NOT NULL DEFAULT '' COMMENT '容器底层id',
  `image_name` varchar(40) NOT NULL DEFAULT '',
  `image_version` varchar(40) NOT NULL DEFAULT '',
  `image_address` varchar(40) NOT NULL DEFAULT '',
  `cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `working_dir` varchar(255) NOT NULL DEFAULT '',
  `image_pull_policy` varchar(255) NOT NULL DEFAULT '',
  `commands` varchar(255) NOT NULL DEFAULT '',
  `args` varchar(255) NOT NULL DEFAULT '',
  `ports` text NOT NULL DEFAULT '',
  `volume_mounts` text NOT NULL DEFAULT '',
  `envs` text NOT NULL DEFAULT '',
  `user_id` varchar(255) NOT NULL DEFAULT '',
  `previous_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器上一个状态',
  `current_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器当前状态',
  `restart_count` int(11) NOT NULL DEFAULT '0' COMMENT '容器重启次数',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) COMMENT='container信息表';

CREATE TABLE `t_pod_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_id` varchar(255) NOT NULL,
  `pod_uuid` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `status` varchar(40) NOT NULL,
  `sub_status` varchar(64) NOT NULL DEFAULT '' COMMENT 'sub status',
  `v_cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `gpu_type` varchar(255) DEFAULT '',
  `gpu_count` float DEFAULT '0.0',
  `product_type` varchar(255) NOT NULL,
  `eip_uuid` varchar(255) NOT NULL,
  `public_ip` varchar(100) NOT NULL,
  `bandwidth_in_mbps` int(11) NOT NULL DEFAULT 0,
  `eip_status` int(11) NOT NULL DEFAULT 0,
  `cce_uuid` varchar(255) NOT NULL,
  `internal_ip` varchar(100) NOT NULL,
  `subnet_uuid` varchar(100) NOT NULL,
  `security_group_uuid` varchar(1000) NOT NULL,
  `restart_policy` varchar(40) NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `tags` text,
  `description` varchar(1000) NOT NULL,
  `user_id` varchar(255) NOT NULL,
  `zone_id` varchar(255) NOT NULL,
  `resource_uuid` varchar(1000) NOT NULL,
  `task_status` varchar(40) NOT NULL,
  `nfs` text NOT NULL,
  `empty_dir` text NOT NULL,
  `config_file` text NOT NULL,
  `flex_volume` text,
  `pfs` text,
  `bos` text,
  `host_path` text,
  `pod_volumes` varchar(10000) NOT NULL,
  `zone_subnets` text,
  `created_bls_tasks_id` varchar(1024) NOT NULL DEFAULT '' COMMENT '创建的BLS任务id',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(11) NOT NULL DEFAULT 0,
  `enable_log` int(11) NOT NULL DEFAULT '0',
  `application` varchar(255) NOT NULL,
  `charge_source` varchar(32) NOT NULL DEFAULT 'user',
  `charge_account_id` varchar(255) NOT NULL DEFAULT '',
  `conditions` text NOT NULL DEFAULT '' COMMENT 'pod conditions 字段',
  `node_name` varchar(40) DEFAULT '',
  `bcc_instance_id` varchar(40) DEFAULT '',
  `preempt_status` varchar(40) DEFAULT '',
  `preempt_status_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '抢占状态变更时间',
  `extra` text,
  `cpu_type` varchar(255) DEFAULT '',
  `resource_recycle_timestamp` bigint(20) NOT NULL DEFAULT 0 COMMENT '开始资源回收时间',
  `resource_recycle_reason` varchar(40) DEFAULT '',
  `resource_recycle_complete` int(11) NOT NULL DEFAULT 0,
  `resource_recycle_complete_timestamp` bigint(20) NOT NULL DEFAULT 0 COMMENT '完成资源回收时间',
  `delay_release_duration_minute` int(11) NOT NULL DEFAULT 0,
  `delay_release_succeeded` boolean NOT NULL DEFAULT 0,
  `resource_version` bigint(20) NOT NULL DEFAULT 0 COMMENT 'k8s Pod资源版本号',
  `bci_resource_version` bigint(20) NOT NULL DEFAULT 0 COMMENT 'bci Pod资源版本号',
  `cpt1` tinyint(1) DEFAULT 0 COMMENT 'CPT1计费',
  `ds_containers_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '每次有ds容器发生变化，ds容器版本加一',
  `ds_containers_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '记录ds容器数量',
  `ds_containers_synced_to_k8s` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'ds容器是否已经同步到K8S',
  `internal_ipv6` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT='pod信息表';

CREATE TABLE `t_container_v2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pod_uuid` varchar(255) NOT NULL DEFAULT '',
  `name` varchar(40) NOT NULL DEFAULT '',
  `container_type` varchar(40) NOT NULL DEFAULT 'workload' COMMENT '容器类型: init、workload、ds-workload',
  `container_uuid` varchar(255) NOT NULL DEFAULT '' COMMENT '容器底层id',
  `image_name` varchar(40) NOT NULL DEFAULT '',
  `image_version` varchar(40) NOT NULL DEFAULT '',
  `image_address` varchar(255) NOT NULL DEFAULT '',
  `image_id` varchar(255) DEFAULT '',
  `cpu` float NOT NULL DEFAULT '0.0',
  `memory` float NOT NULL DEFAULT '0.0',
  `gpu_type` varchar(255) DEFAULT '',
  `gpu_count` float DEFAULT '0.0',
  `working_dir` varchar(255) NOT NULL DEFAULT '',
  `image_pull_policy` varchar(255) NOT NULL DEFAULT '',
  `commands` varchar(255) NOT NULL DEFAULT '',
  `args` text NOT NULL DEFAULT '',
  `ports` text NOT NULL DEFAULT '',
  `volume_mounts` text NOT NULL DEFAULT '',
  `envs` text NOT NULL DEFAULT '',
  `liveness_probe` text NOT NULL DEFAULT '' COMMENT '容器是否存活探针',
  `readiness_probe` text NOT NULL DEFAULT '' COMMENT '容器是否就绪探针',
  `startup_probe` text NOT NULL DEFAULT '' COMMENT '容器是否启动成功探针',
  `extend_field` text NOT NULL DEFAULT '' COMMENT '扩展资源字段',
  `user_id` varchar(255) NOT NULL DEFAULT '',
  `previous_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器上一个状态',
  `current_state` varchar(1000) NOT NULL DEFAULT '' COMMENT '容器当前状态',
  `restart_count` int(11) NOT NULL DEFAULT '0' COMMENT '容器重启次数',
  `ready` varchar(25) NOT NULL DEFAULT '' COMMENT '容器状态',
  `started` varchar(25) NOT NULL DEFAULT '' COMMENT '容器是否启动成功',
  `lifecycle` text COMMENT '容器生命周期',
  `stdin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否打开标准输入流',
  `stdin_once` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否单次开启标准输入流',
  `tty` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启终端',
  `security_context` text COMMENT '容器安全上下文',
  `ds_container_version` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前ds容器的版本号',
  `extra` text NOT NULL DEFAULT '' COMMENT '额外信息',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '删除时间',
  `deleted` int(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) COMMENT='container信息表';

CREATE TABLE `t_zone` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键（自然主键）',
  `zone_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户zone的uuid',
  `account_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户id',
  `logical_zone` varchar(32) NOT NULL DEFAULT '' COMMENT '逻辑可用区',
  `physical_zone` varchar(32) NOT NULL DEFAULT '' COMMENT '物理可用区',
  `subnet_id` varchar(64) NOT NULL DEFAULT '' COMMENT '默认子网的ID',
  `type` varchar(32) NOT NULL DEFAULT 'available' COMMENT '映射类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `zone_id` (`zone_id`),
  UNIQUE KEY `user_logical` (`account_id`,`logical_zone`)
);

CREATE TABLE `t_pod_charge_status` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
`pod_uuid` varchar(255) NOT NULL,
`previous_state` varchar(40) NOT NULL,
`current_state` varchar(40) NOT NULL,
`charge_state` varchar(40) NOT NULL,
`created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
PRIMARY KEY (`id`),
UNIQUE KEY `pod_uuid` (`pod_uuid`,`charge_state`,`created_time`)
) ;

CREATE TABLE `t_pod_charge_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `msg_id` varchar(255) NOT NULL,
  `push_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '推送时间',
  `charge_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '计费截止时间',
  `last_charge_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01',
  `lock_id` varchar(255) NOT NULL,
  `lock_time` timestamp NOT NULL DEFAULT '1971-01-01 08:00:01' COMMENT '加锁时间',
  `push_status` varchar(40) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `msg_id` (`msg_id`)
) ;



CREATE ALIAS IF NOT EXISTS UTC_TIMESTAMP FOR "com.baidu.bce.logic.bci.service.util.FunctionsMySQL.utcTimestamp";