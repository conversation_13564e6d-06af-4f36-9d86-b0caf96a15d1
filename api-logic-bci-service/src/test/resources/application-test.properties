server-host=qasandbox.bcetest.baidu.com
server.port=8784

bce.logical.region=bj
usersettings.default.region=bj
region.defaultRegion=bj
region.currentRegion=bj

swagger.start:false
swagger.app.docs: http://${server-host}:${server.port}

error_page.404:/bcc/404.html

#=================== login & access ===================#
login.url:https://login.bcetest.baidu.com/?redirect={referredUrl}
cookie.domain:.bcetest.baidu.com
login.cookie.md5.key:bcetest
login.urls.not.need.auth:/bcc/index.html;/bcc/asset/**;/bcc/dep/**;/bcc/esl.js;/swagger/**;/api-docs;/api-docs/**;/bcc/version.txt
login.urls.need.auth:/**
passport.appid=1240
passport.session.endpoint:http://***********:7801
uc.app.hypervisorId:285
uc.server:************:8880

iam.access.failed.jump.url:https://${server-host}/iam/access?redirect={referredUrl}
iam.console.username=console
iam.console.password=console
iam.access.paths:/**
iam.access.exclude.paths:/api-docs;/api-docs/**
iam.sts.rolename=BceServiceRole_bci
pod.sts.policy.id=9b468c32be2e405ba60380e2d2389ee0
pod.sts.service.account.id=c3dfeaf0a5234f4fa11f995bfc1005d7

iam.csrf.paths:/api/**
iam.csrf.exclude.paths:/api/bcc/instance/vnc;/api-docs;/api-docs/**;/api/bcc/order/confirm;/api/bcc/cds/order/confirm
iam.csrf.is.need.check.valid=true
iam.permission.is.need.check.valid=true
#======================================================#

#=================== logging config ===================#
logging.requestId_urlPattern:/bcc/;/api/*
logging.has_console_appender: true

logging.has_web_debug_appender: true
logging.web_debug_path: /bcc/debug
logging.web_debug.level: INFO

### logback rolling log, uncomment to open appender ###
logging.info_log_file_path:  ../log/info/bce-console-bcc.info.log
logging.error_log_file_path: ../log/error/bce-console-bcc.error.log
logging.warn_log_file_path:  ../log/warn/bce-console-bcc.warn.log
logging.debug_log_file_path: ../log/debug/bce-console-bcc.debug.log

### access log, uncomment to open appender ###
logging.access_debug_uri_prefix:/api
logging.access_log_file_path: ../log/access/bce-console-bcc.access.log
logging.access_debug_log_file_path: ../log/access_debug/bce-console-bcc.access_debug.log
#======================================================#

#=================== show floating ip ===================#
bcc.list.whetherShowFloatingIp=true
#======================================================#

#=================== sms ===================#
sms.bcc.rebuild.tpl.hypervisorId=smsTpl:0a09f75624f741fdb2b57ac620961afd
#======================================================#

#=================== finance ===================#
finance.account.type.available:100
finance.account.type.freeze:101
#======================================================#

#=================== bcc quota ===================#
bcc.prepay.quota:100
bcc.postpay.quota:20
cds.to.bcc.ratio:5
template.quota:20
snapshot.to.cds.ratio:8
#======================================================#

institution_id=1
secret_code=d21taUQ0N0tNZ0o2RllvbElObkV6VzFZdFhGd1dK
secret_code_institution_id_1=mUphTrGslz6JZtgAj0eqEKRBI9oxa4bi1M3wHcLF
secret_code_institution_id_2=d21taUQ0N0tNZ0o2RllvbElObkV6VzFZdFhGd1dK

finance.pay.url.prefix=http://cp01-testing-fengkong04.cp01.baidu.com:8118/process/pay_order_bce
finance.pay.pay_order.forward_url.prefix=https://${server-host}/billing/#/order/success
finance.pay.pay_order.feedback_url.prefix=http://nmg02-bce-test6.nmg02.baidu.com:8003/orderPurchaseCallback
finance.pay.recharge.forward_url=https://${server-host}/billing/#/account/history

#=================== trail config ===================#
trail.event.enable:true
trail.event.table_name:event
trail.event.agent_data_dir:../../../tidedb/agent/bce-console/

usersettings.region.enable:true
endpoint.default.regionName:bj

bae.apis_host:openbaev3.offlinea.bae.baidu.com
qualify.is.need.check.valid:true

#================ passport modify ================#
passport.passgateEndpoint:http://***********:8300/passgate
passport.app.username:bceplat
passport.app.password:bceplat

order.operate.enable:true

#================ global quota ================#
instance.postpay.gz.quota=2000
instance.postpay.bj.quota=1000

#============== new logical white user ============#
bce.logical.full.flow:true
bce.logical.white.enable:true
bce.logical.white.user:1a58d71202a14c4fbd077f9d027d5b1a,c254400af22947c3a3a1bea7ee1dee1e,\
  c3b1fab46b2a448a9d326931dbef06fa,c30d8e9de5204ca6a13c548d7f8acacf,\
  f1f62daf2ea34a7ba67b8aae9e398ef3,8a8fc7ed05eb45a7811c2c33e02b2587,36327b8052cc4a46afc0b13ba35907fc,\
  e91992a18cc74eb5859a97f95f363a30,5b785d8cb32c418e8a4a61cdb85e50a8

#================log monitor=================#
monitor.latencyRecorder.enable:true

database.isEmbedded:false
db.logical.bcc.url:***********************************************************************************
db.logical.bcc.username:root
db.logical.bcc.password:123456
#==================== database config begin ====================#
database.enable:false
database.mybatis.enable:true
database.logic.enable:true
#database.isEmbedded:false
database.logic.url:***********************************************************************************
database.logic.username:root
database.logic.password:123456
#==================== database config end ====================#

spring.velocity.checkTemplateLocation=false

bce.asyncwork.enable:true
