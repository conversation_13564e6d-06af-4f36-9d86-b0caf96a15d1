package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import org.springframework.stereotype.Component;

import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.TRANSACTION_ID;

@Component
public class ServiceCatalogOrderClientMock extends ServiceCatalogOrderClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public ServiceCatalogOrderClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public OrderUuidResult createOrder(CreateOrderRequest request) {
        OrderUuidResult orderUuidResult = new OrderUuidResult();
        orderUuidResult.setOrderId(TRANSACTION_ID);
        return orderUuidResult;
    }
}
