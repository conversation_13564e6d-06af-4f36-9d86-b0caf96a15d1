package com.baidu.bce.logic.bci.service;

import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.bci.service.constant.LogicalConstant;
import com.baidu.bce.logic.bci.service.constant.WhiteListKey;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.bci.service.model.BciQuota;
import com.baidu.bce.logic.bci.service.model.ContainerPurchase;
import com.baidu.bce.logic.bci.service.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.IOrderItem;
import com.baidu.bce.logic.bci.service.model.ImageRegistrySecret;
import com.baidu.bce.logic.bci.service.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import com.baidu.bce.logic.bci.service.util.PodValidator;
import com.baidu.bce.logic.bci.service.util.TestConfiguration;
import com.baidu.bce.logic.bci.service.util.TestUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodValidatorTest {

    private static final String CREATE_POD_LOCATION = "classpath*:create_pod.json";

    private BaseCreateOrderRequestVo<IOrderItem> request;

    @Autowired
    private PodValidator podValidator;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        testUtil.setUp();
        request = testUtil.orderRequest(
                databaseUtil.getResource(CREATE_POD_LOCATION).getInputStream(), IOrderItem.class);
    }

    @Test
    public void validateCreateBCIServiceTypeTest() {
        podValidator.validateCreateBCIServiceType(request);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void validateCreateBCIServiceTypeExceptionTest() {
        request.getItems().get(0).getConfig().setServiceType("BCC");
        podValidator.validateCreateBCIServiceType(request);
    }

    @Test(expected = PodExceptions.ImageRegistryException.class)
    public void validateImageSecretTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.getImageRegistrySecrets().get(0).setServer("httw:/evce.cwv");
        podValidator.validateImageRegistrySecret(podPurchaseRequest);
    }

    @Test
    public void validateImageRegistrySecretTest() {
        podValidator.validateImageRegistrySecret((PodPurchaseRequest) request.getItems().get(0).getConfig());
    }

    @Test
    public void validateRepositoryTest() {
        podValidator.validateRepository((PodPurchaseRequest) request.getItems().get(0).getConfig());
    }

    @Test(expected = PodExceptions.ImageRegistryException.class)
    public void validateImageRegistryTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        for (ContainerPurchase containerPurchase : podPurchaseRequest.getContainerPurchases()) {
            containerPurchase.setImageAddress("fwee.r3g5ht/fdq");
        }
        podValidator.validateImageRegistrySecret((PodPurchaseRequest) request.getItems().get(0).getConfig());
    }

    @Test
    public void validateNullImageRegistrySecretTest() {
        podValidator.validateImageRegistrySecret(
                ((PodPurchaseRequest) request.getItems().get(0).getConfig())
                        .setImageRegistrySecrets(new ArrayList<ImageRegistrySecret>()));
    }

    @Test
    public void validateAutoRenewTimeMouthTest() {
        podValidator.validateAutoRenewTime("month", 1);
    }

    @Test
    public void validateAutoRenewTimeYearTest() {
        podValidator.validateAutoRenewTime("year", 1);
    }

    @Test(expected = PodExceptions.InvalidAutoRenewTimeException.class)
    public void validateAutoRenewTimeInvalidExceptionTest() {
        podValidator.validateAutoRenewTime("year", -1);
    }

    @Test(expected = PodExceptions.InvalidAutoRenewTimeUnitException.class)
    public void validateTimeUnitInvalidExceptionTest() {
        podValidator.validateAutoRenewTime("day", 1);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void validateTimeUnitExceptionTest() {
        podValidator.validateAutoRenewTime("month", 13);
    }

    @Test
    public void validateEipBandwidthInMbpsTest() {
        EipPurchaseRequest request = new EipPurchaseRequest();
        request.setSubProductType(LogicalConstant.EipSubProductType.BAND_WIDTH);
        request.setBandwidthInMbps(150);
        podValidator.validateEipBandwidthInMbps(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void validateEipBandwidthInMbpsBAND_WIDTHExceptionTest() {
        EipPurchaseRequest request = new EipPurchaseRequest();
        request.setSubProductType(LogicalConstant.EipSubProductType.BAND_WIDTH);
        request.setBandwidthInMbps(250);
        podValidator.validateEipBandwidthInMbps(request);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void validateEipBandwidthInMbpsNETRAFFICExceptionTest() {
        EipPurchaseRequest request = new EipPurchaseRequest();
        request.setSubProductType(LogicalConstant.EipSubProductType.NETRAFFIC);
        request.setBandwidthInMbps(1250);
        podValidator.validateEipBandwidthInMbps(request);
    }

    @Test(expected = PodExceptions.EipOperationDeniedException.class)
    public void validateEipBlackListTest() {
        podValidator.validateEipBlackList(Arrays.asList(WhiteListKey.EIP_BLACK_LIST));
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void validateBciParametersTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setContainerPurchases(null);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = PodExceptions.NameInvalidException.class)
    public void validateBciParametersNameTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setName("efo&%$#!@_-ID");
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void validateBciParametersPrepayTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setProductType("prepay");
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void validateBciParametersVolumeTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podPurchaseRequest.setVolume(null);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.ExceedLimitException.class)
    public void validateBciParametersCreatedQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(1);
        bciQuota.setEmptyDirRatio(1);
        bciQuota.setEnvRatio(1);
        bciQuota.setNfsRatio(1);
        bciQuota.setPodCreated(3);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.NfsQuotaExceededLimit.class)
    public void validateBciParametersNfsQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(2);
        bciQuota.setEmptyDirRatio(2);
        bciQuota.setEnvRatio(2);
        bciQuota.setNfsRatio(0);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.ConfigFileQuotaExceededLimit.class)
    public void validateBciParametersConfQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(0);
        bciQuota.setEmptyDirRatio(2);
        bciQuota.setEnvRatio(2);
        bciQuota.setNfsRatio(2);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    //@Test(expected = PodExceptions.EmptyDirQuotaExceededLimit.class)
    public void validateBciParametersEmptyQuotaTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        BciQuota bciQuota = new BciQuota();
        bciQuota.setConfigFileRatio(2);
        bciQuota.setEmptyDirRatio(0);
        bciQuota.setEnvRatio(2);
        bciQuota.setNfsRatio(2);
        bciQuota.setPodCreated(0);
        bciQuota.setPodTotal(2);
        bciQuota.setPortRatio(1);
        bciQuota.setVolumeRatio(3);
        podValidator.validateBciParameters(podPurchaseRequest, bciQuota);
    }

    @Test(expected = CommonExceptions.ResourceNotExistException.class)
    public void validateAndSetSubnetUuidTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.setSecurityGroupId("default");
        podPurchaseRequest.setSubnetId(null);
        podPurchaseRequest.setSubnetUuid(null);
        ZoneMapDetail zoneMapDetail = new ZoneMapDetail();
        zoneMapDetail.setSubnetUuid("evevevfsveveewfwfwfw");
        podValidator.validateAndSetSubnetUuid(podPurchaseRequest, new ZoneMapDetail(), false);
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void validateAndSetSubnetTest() {
        PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) request.getItems().get(0).getConfig();
        podPurchaseRequest.setSecurityGroupId("effwfscs");
        podPurchaseRequest.setSubnetId(null);
        podPurchaseRequest.setSubnetUuid(null);
        podValidator.validateAndSetSubnetUuid(podPurchaseRequest, new ZoneMapDetail(), false);
    }

    @Test(expected = CommonExceptions.ResourceNotExistException.class)
    public void getSubnetWithIpUsage() {
        podValidator.getSubnetWithIpUsage(null);
    }
}
