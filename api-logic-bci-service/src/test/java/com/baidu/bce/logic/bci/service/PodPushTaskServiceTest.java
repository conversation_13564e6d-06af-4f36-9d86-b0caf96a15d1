package com.baidu.bce.logic.bci.service;


import com.baidu.bce.logic.bci.dao.chargerecord.PodChargeRecordDao;
import com.baidu.bce.logic.bci.service.charge.service.PodPushTaskService;
import com.baidu.bce.logic.bci.service.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.service.util.TestConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodPushTaskServiceTest {

    @Autowired
    private PodPushTaskService podPushTaskService;

    private PodChargeRecordDao podChargeRecordDao;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
        podChargeRecordDao = PowerMockito.mock(PodChargeRecordDao.class);
    }

    @Test
    public void deleteRecordTest() {
        podPushTaskService.deleteRecord();
    }

    @Test
    public void createPushTaskTest() {
        podPushTaskService.createPushTask();
    }
}
