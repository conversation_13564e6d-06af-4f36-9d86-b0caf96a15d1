package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.logic.bci.dao.chargestatus.PodChargeStatusDao;
import com.baidu.bce.logic.bci.dao.chargestatus.model.PodChargeStatus;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;

public class PodChargeStatusDaoMock extends PodChargeStatusDao {

    public Map<String, LinkedList<PodChargeStatus>> listByTime(Timestamp lastChargeTime, Timestamp chargeTime) {
        Map<String, LinkedList<PodChargeStatus>> map = new HashMap<>();
        LinkedList<PodChargeStatus> list = new LinkedList<>();
        PodChargeStatus podChargeStatus = new PodChargeStatus();
        podChargeStatus.setChargeState("onCharge");
        podChargeStatus.setCurrentState("failed");
        podChargeStatus.setPreviousState("running");
        podChargeStatus.setCreatedTime(new Timestamp(31536001000L));
        list.add(podChargeStatus);
        map.put("ffcdfa25-6edc-4b7c-943d-d5225050086a", list);
        return map;
    }
}
