package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.eip.EipLogicalClient;
import com.baidu.bce.internalsdk.eip.model.EipInstance;
import com.baidu.bce.internalsdk.eip.model.EipListResponse;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class EipLogicalClientMock extends EipLogicalClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public EipLogicalClientMock() {
        super(null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public EipListResponse listEip(String eip, String status, String instanceType,
                                   String instanceId, String marker, Integer maxKeys) {
        if (throwException != null) {
            throw throwException;
        }

        EipListResponse response = new EipListResponse();
        List<EipInstance> eipInstances = new ArrayList<>();
        EipInstance eipInstance = new EipInstance();
        eipInstance.setEip("**********");
        eipInstance.setInstanceLongId("eip-long-id");
        eipInstances.add(eipInstance);
        response.setEipList(eipInstances);

        return response;
    }

}
