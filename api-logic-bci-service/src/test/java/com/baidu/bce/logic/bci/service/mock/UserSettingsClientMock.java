package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.logic.bci.service.constant.QuotaKeys;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import com.baidu.bce.user.settings.sdk.model.FeatureAclRequest;
import com.baidu.bce.user.settings.sdk.model.FeatureAclResponse;
import com.baidu.bce.user.settings.sdk.model.FeatureTypeListRequest;
import com.baidu.bce.user.settings.sdk.model.FeatureTypeListResponse;
import com.baidu.bce.user.settings.sdk.model.QuotaBatchRequest;
import com.baidu.bce.user.settings.sdk.model.QuotaBatchResponse;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Component
public class UserSettingsClientMock extends UserSettingsClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public UserSettingsClientMock() {
        super(null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public FeatureTypeListResponse showFeatureTypes(FeatureTypeListRequest request) {
        if (throwException != null) {
            throw throwException;
        }
        FeatureTypeListResponse featureTypeListResponse = new FeatureTypeListResponse();
        featureTypeListResponse.setFeatureTypes(Collections.singletonList("whitelist"));

        return featureTypeListResponse;
    }

    public QuotaBatchResponse getBatchQuota(QuotaBatchRequest request) {
        if (throwException != null) {
            throw throwException;
        }
        Map<String, String> quotaMap = new HashMap<>();
        quotaMap.put(QuotaKeys.POD, "5");
        quotaMap.put(QuotaKeys.CONFIG_FILE_RATIO, "5");
        quotaMap.put(QuotaKeys.EMPTY_DIR_RATIO, "5");
        quotaMap.put(QuotaKeys.ENV_RATIO, "5");
        quotaMap.put(QuotaKeys.NFS_RATIO, "5");
        quotaMap.put(QuotaKeys.PORT_RATIO, "5");
        quotaMap.put(QuotaKeys.VOLUME_RATIO, "5");

        QuotaBatchResponse response = new QuotaBatchResponse();
        response.setQuotaType2quota(quotaMap);
        return response;
    }

    public FeatureAclResponse isInFeatureAcl(FeatureAclRequest request) {
        FeatureAclResponse response = new FeatureAclResponse();
        response.setIsExist(false);
        return response;
    }
}
