package com.baidu.bce.logic.bci.service.util;

import com.baidu.bce.logic.bci.dao.container.ContainerDao;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.service.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.service.mock.ResettableMock;
import com.baidu.bce.logic.bci.service.pod.PodService;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.aop.framework.Advised;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;

import static org.powermock.api.mockito.PowerMockito.when;

@Component
@PrepareForTest({PodService.class})
public class TestUtil {

    @Autowired
    private DatabaseUtil databaseUtil;

    @Autowired
    private PodService podService;

    @Autowired
    private PodDao podDao;

    @Autowired
    private ContainerDao containerDao;

    @Autowired
    private ResettableMock[] resettableMocks;

    private static final ObjectMapper MAPPER = new ObjectMapper();

    public void setUp() throws IllegalAccessException, SQLException {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
        for (ResettableMock resettableMock : resettableMocks) {
            resettableMock.reset();
        }
    }

    public static <T> T getTargetObject(Object proxy) throws Exception {
        if ((AopUtils.isJdkDynamicProxy(proxy))) {
            return (T) getTargetObject(((Advised) proxy).getTargetSource().getTarget());
        }
        return (T) proxy;
    }

    public <T> BaseCreateOrderRequestVo<T> orderRequest(InputStream inputStream, Class<T> clazz) throws IOException {
        if (inputStream == null) {
            return null;
        }
        JavaType javaType = MAPPER.getTypeFactory().constructParametricType(BaseCreateOrderRequestVo.class, clazz);
        return MAPPER.readValue(inputStream, javaType);
    }

    public static <T> T fromJson(InputStream inputStream, Class<T> clazz) throws IOException {
        return MAPPER.readValue(inputStream, clazz);
    }
}
