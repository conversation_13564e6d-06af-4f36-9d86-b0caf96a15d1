package com.baidu.bce.logic.bci.service;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.trail.EventBuilder;
import com.baidu.bce.logic.bci.service.exception.BackendExceptions;
import com.baidu.bce.logic.bci.service.exception.handler.LogicPodExceptionHandler;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.dao.CleanupFailureDataAccessException;
import org.springframework.dao.DataAccessException;


@RunWith(PowerMockRunner.class)
public class LogicPodExceptionHandlerTest {

    private static final String ERROR_CODE_RESOURCE_IN_TASK = "ResourceInTaskException";
    private static final String ERROR_CODE_ORDER_PAY_FAILED = "NotEnoughBalanceForPayOrder";
    private static final String INSUFFICIENT_BALANCE = "InsufficientBalance";

    @Test(expected = BackendExceptions.RequestLockException.class)
    public void handleRequestLockExceptionTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setCode(ERROR_CODE_RESOURCE_IN_TASK);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BackendExceptions.OrderPayFailedException.class)
    public void handleRequestOrderPayFailedTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setCode(ERROR_CODE_ORDER_PAY_FAILED);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = CommonExceptions.InsufficientBalanceException.class)
    public void handleRequestInsufficientTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setCode(INSUFFICIENT_BALANCE);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BackendExceptions.RequestInvalidException.class)
    public void handleRequestRequestInvalidTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(400);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BackendExceptions.PermissionDenyException.class)
    public void handleRequestPermissionDenyTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(401);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BackendExceptions.ResourceNotExistException.class)
    public void handleRequestResourceNotExistTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(404);
        LogicPodExceptionHandler.handle(exception);
    }


    @Test(expected = BackendExceptions.OperationTypeErrorException.class)
    public void handleRequestOperationTypeTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(405);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BackendExceptions.OperationErrorException.class)
    public void handleRequestOperationErrorTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(409);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BackendExceptions.ExceedLimitException.class)
    public void handleRequestExceedLimitTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(413);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = CommonExceptions.InternalServerErrorException.class)
    public void handleRequestInternalServerErrorTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(500);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = CommonExceptions.InternalServerErrorException.class)
    public void handleRequestTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(505);
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BceException.class)
    public void handleRequestExceptionTest() {
        BceException exception = new BceException("");
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = CommonExceptions.InternalServerErrorException.class)
    public void handleRequestDataAccessExceptionTest() {
        DataAccessException exception = new CleanupFailureDataAccessException("", new Throwable());
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = CommonExceptions.InternalServerErrorException.class)
    public void handleRequestDefaultExceptionTest() {
        Exception exception = new Exception("");
        LogicPodExceptionHandler.handle(exception);
    }

    @Test(expected = BackendExceptions.RequestLockException.class)
    public void handleEventExceptionTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setCode(ERROR_CODE_RESOURCE_IN_TASK);
        EventBuilder eventBuilder = new EventBuilder();
        LogicPodExceptionHandler.handle(eventBuilder, exception);
    }

    @Test(expected = BceException.class)
    public void handleEventBceExceptionTest() {
        BceException exception = new BceException("");
        EventBuilder eventBuilder = new EventBuilder();
        LogicPodExceptionHandler.handle(eventBuilder, exception);
    }

    @Test(expected = CommonExceptions.InternalServerErrorException.class)
    public void handleDataAccessExceptionTest() {
        DataAccessException exception = new CleanupFailureDataAccessException("", new Throwable());
        EventBuilder eventBuilder = new EventBuilder();
        LogicPodExceptionHandler.handle(eventBuilder, exception);
    }

    @Test(expected = CommonExceptions.InternalServerErrorException.class)
    public void handleEventDefaultTest() {
        Exception exception = new Exception("");
        EventBuilder eventBuilder = new EventBuilder();
        LogicPodExceptionHandler.handle(eventBuilder, exception);
    }

    @Test(expected = CommonExceptions.OperationDeniedException.class)
    public void throwPermissionDeniedExceptionDenyTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(403);
        LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate(exception);
    }

    @Test(expected = CommonExceptions.ResourceInTaskException.class)
    public void throwPermissionDeniedExceptionInTaskTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setHttpStatus(409);
        LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate(exception);
    }

    @Test(expected = CommonExceptions.PaymentFailedException.class)
    public void throwPermissionDeniedExceptionPaymentFailedTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setCode(ERROR_CODE_ORDER_PAY_FAILED);
        LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate(exception);
    }

    @Test(expected = CommonExceptions.InsufficientBalanceException.class)
    public void throwPermissionDeniedExceptionInsufficientTest() {
        BceInternalResponseException exception = new BceInternalResponseException("");
        exception.setCode(INSUFFICIENT_BALANCE);
        LogicPodExceptionHandler.throwPermissionDeniedExceptionIfAppropriate(exception);
    }
}
