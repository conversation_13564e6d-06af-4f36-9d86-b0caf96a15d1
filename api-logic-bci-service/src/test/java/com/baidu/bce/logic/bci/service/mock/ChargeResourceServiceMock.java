package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.billing.resourcemanager.model.ResourceUseStatusInfo;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.request.DeleteRequest;
import com.baidu.bce.billing.resourcemanager.service.request.UseStatusBatchQueryRequest;
import com.baidu.bce.billing.resourcemanager.service.request.UseStatusRequest;
import com.baidu.bce.billing.resourcemanager.service.response.CheckResponse;
import com.baidu.bce.billing.resourcemanager.service.response.Response;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ChargeResourceServiceMock implements ChargeResourceService {
    @Override
    public Response destroy(DeleteRequest deleteRequest) {
        return null;
    }

    @Override
    public List<String> getUsingServiceList(String s) {
        return null;
    }

    @Override
    public Response pause(UseStatusRequest useStatusRequest) {
        return null;
    }

    @Override
    public Response start(UseStatusRequest useStatusRequest) {
        return null;
    }

    @Override
    public CheckResponse allowChangeUseStatus(UseStatusRequest useStatusRequest) {
        return null;
    }

    @Override
    public List<ResourceUseStatusInfo> getUseStatusList(UseStatusBatchQueryRequest useStatusBatchQueryRequest) {
        return null;
    }
}
