package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.BelongSameVpcRequest;
import org.springframework.stereotype.Component;

@Component
public class ExternalVpcClientMock extends ExternalVpcClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public ExternalVpcClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public boolean validateBelongSameVpc(BelongSameVpcRequest request) {
        return true;
    }
}
