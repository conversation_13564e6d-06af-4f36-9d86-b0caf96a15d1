package com.baidu.bce.logic.bci.service;

import com.baidu.bce.internalsdk.trail.util.JsonUtil;
import com.baidu.bce.logic.bci.dao.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.dao.common.model.PodListModel;
import com.baidu.bce.logic.bci.dao.pod.PodDao;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import com.baidu.bce.logic.bci.service.util.TestConfiguration;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.ACCOUNT_ID;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.POD_ID;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.POD_UUID;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.TRANSACTION_ID;

@SuppressWarnings("SpringJavaAutowiringInspection")
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
public class PodDaoTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(PodDaoTest.class);

    @Autowired
    DatabaseUtil databaseUtil;

    @Autowired
    PodDao podDao;

    String accountId = "account_id";
    String orderId = "order_id";
    String podId = "pod_id";
    String podUuid = "pod_uuid";
    String status = "error";

    PodListModel listModel;

    @Before
    public void resetDatabase() throws Exception {
//        databaseUtil.resetDatabase();

        String str = "{\"keywordType\":\"podId\",\"keyword\":\"pod_id\",\"pageNo\":1,\"pageSize\":10," +
                "\"orders\":[{\"order\":\"desc\",\"orderBy\":\"createTime\"}],\"marker\":null,\"maxKeys\":1000," +
                "\"filterMap\":{\"keywordType\":\"keyword\"},\"logicalZone\":null,\"includedUuids\":null," +
                "\"tagOrderBy\":null,\"tagOrder\":null}";
        listModel = JsonUtil.fromJson(str, PodListModel.class);
    }

    @Test
    public void listPodTest() {
        Assert.assertTrue(podDao.listAllPodUuids(ACCOUNT_ID).size() > 0);
    }

    @Test
    public void mapPodTest() {
        Map<String, String> map = podDao.podIdMap(ACCOUNT_ID);
        Assert.assertTrue(map.size() > 0);
    }


    @Test
    public void listPodByOrderTest() {
        List<PodPO> list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void updateStatusTest() {
        List<PodPO> list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
        PodPO podPO = list.get(0);
        podPO.setStatus(status);
        podPO.setInternalIp("************");
        podDao.batchUpdateStatus(list);
    }

    @Test
    public void insertTest() {
        PodPO podPO = new PodPO();
        podPO.setPodId(podId + "_insert");
        podPO.setUserId(ACCOUNT_ID);
        podPO.setPodUuid(POD_UUID);
        podPO.setStatus("Pending");
        podPO.setConfigFile("");
        List<PodPO> list = new ArrayList<>();
        list.add(podPO);
        podDao.batchInsertPods(list);

        List<String> uuids = podDao.listAllPodUuids(ACCOUNT_ID);
    }

    @Test
    public void getPodDetailTest() {
        PodPO podPO = podDao.getPodDetail(ACCOUNT_ID, POD_ID);
        Assert.assertTrue(podPO != null);
    }

    @Test
    public void listPodsByMultiKeyTest() {
        PodFilterQueryModel podFilterQueryModel = new PodFilterQueryModel("podId", "123");
        List<PodFilterQueryModel> list = new ArrayList<>();
        list.add(podFilterQueryModel);
        Assert.assertTrue(
                podDao.listPodsByMultiKey(ACCOUNT_ID, listModel,  list).size() ==  0);
    }

    @Test
    public void queryPodCountByMultiKeyTest() {

        PodFilterQueryModel podFilterQueryModel = new PodFilterQueryModel("podId", POD_ID);
        List<PodFilterQueryModel> list = new ArrayList<>();
        list.add(podFilterQueryModel);
        Assert.assertTrue(
                podDao.queryPodCountByMultiKey(ACCOUNT_ID, listModel,  list) > 0);
    }

    @Test
    public void queryPodUuidTest() {
        Assert.assertTrue(podDao.queryPodUuid(POD_ID) != null);
    }

    @Test
    public void deletePodTest() {
        podDao.deletePod(ACCOUNT_ID, POD_ID);
    }

    @Test
    public void listByStatusTest() {
        Assert.assertTrue(podDao.listByStatus("Pending").size() > 0);
    }

    @Test
    public void updateTest() {
        List<PodPO> list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
//        PodPO podPO = list.get(0);
//        podPO.setStatus(status);
//        podPO.setInternalIp("************");
//        podPO.setUpdatedTime(new Timestamp(1557264980L * 1000));
        podDao.batchUpdateStatus(list);

        list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
    }

    @Test
    public void updateSinceTest() {
        Timestamp timestamp = podDao.updateSince();
        LOGGER.info(timestamp.toString());
    }

    @Test
    public void pushLogTest() {
        List<PodPO> list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
        for (PodPO podPO : list) {
            podPO.setEnableLog(1);
        }
        podDao.enableLog(list, ACCOUNT_ID);

        list = podDao.listByOrderId(ACCOUNT_ID, TRANSACTION_ID);
        for (PodPO podPO : list) {
            Assert.assertTrue(podPO.getEnableLog() == 1);
        }
    }
}
