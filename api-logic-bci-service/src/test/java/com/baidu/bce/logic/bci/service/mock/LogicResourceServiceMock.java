package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.logic.bci.service.common.service.LogicalResourceService;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class LogicResourceServiceMock extends LogicalResourceService implements ResettableMock {
    private boolean throwException = false;

    @Autowired
    private DatabaseUtil databaseUtil;

    private Map<String, Resource> resourceMap = new HashMap<>();

    public void setThrowException(boolean throwException) {
        this.throwException = throwException;
    }


    public void addResource(String uuid, Resource resource) {
        resourceMap.put(uuid, resource);
    }

    @Override
    public void reset() {
        resourceMap.clear();
    }


//    @Override
//    public Resource getResource(String accountId, String name, String serviceType) {
//        assertEquals(PodConstants.SERVICE_TYPE, serviceType);
//        Resource resource = resourceMap.get(name);
//        if (resource == null) {
//            String podUuid;
//            try {
//                podUuid = databaseUtil.getPodMapper().queryPodUuid(name);
//                if (podUuid != null) {
//                    return resourceMap.get(podUuid);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        return resource;
//    }

    public Resource removeResource(String resourceUuid) {
        return resourceMap.remove(resourceUuid);
    }
}
