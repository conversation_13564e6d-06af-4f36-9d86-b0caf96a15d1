package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.bci.CceImageClient;
import com.baidu.bce.internalsdk.bci.model.CceOfficialImage;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageResponse;
import com.baidu.bce.internalsdk.bci.model.ImageTags;
import com.baidu.bce.internalsdk.bci.model.OfficialImageResponse;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.internalsdk.bci.model.UserImageResponse;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class CceImageClientMock extends CceImageClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public CceImageClientMock() {
        super(null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public UserImageResponse listUserImage(String keyword, String keywordType) {

        return new UserImageResponse().setRepositories(Arrays.asList(
                new UserImage().setId(1).setAddress("feffewfw").setName("image")
        ));
    }

    public OfficialImageResponse listOfficialImage(String keyword, String keywordType) {

        return new OfficialImageResponse().setImages(Arrays.asList(
                new CceOfficialImage().setRepository("baidu")
                        .setAddress("baidu.image.com/repository/baidu:latest").setTag("latest"),
                new CceOfficialImage().setRepository("baidu")
                        .setAddress("baidu.image.com/repository/baidu:5.5").setTag("5.5")
        ));
    }

    public DockerHubImageResponse listDockerHubImage(String keyword, String keywordType) {
        return new DockerHubImageResponse();
    }

    public ImageTags listImageVersions(String namespace, String repository) {
        return new ImageTags().setRepository("image").setTags(Arrays.asList(
                new ImageTags.Tags().setName("latest").setDigest("dd")
        ));
    }
}
