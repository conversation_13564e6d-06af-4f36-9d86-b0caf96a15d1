package com.baidu.bce.logic.bci.service;

import com.baidu.bce.logic.bci.service.pod.BCMPodService;
import com.baidu.bce.logic.bci.service.constant.ServiceTestConstants;
import com.baidu.bce.logic.bci.service.util.TestConfiguration;
import com.baidu.bce.logic.bci.service.model.BCMListPodRequest;
import com.baidu.bce.logic.bci.service.model.BCMListPodResponse;
import com.baidu.bce.logic.bci.service.model.BCMPodContainerResponse;
import com.baidu.bce.logic.bci.service.model.PodForBCM;
import com.baidu.bce.logic.bci.service.model.PodContainerForBCM;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class BCMPodServiceTest {

    @Autowired
    private BCMPodService bcmPodService;


    @Before
    public void setUp() {
        PowerMockito.mockStatic(LogicUserService.class);
        when(LogicUserService.getAccountId()).thenReturn(ServiceTestConstants.ACCOUNT_ID);
    }

    @Test
    public void listPodsTest() {
        BCMListPodRequest podListRequest = new BCMListPodRequest();
        podListRequest.setKeywordType("keywordType");
        podListRequest.setKeyword("keyword");
        podListRequest.setPageNo(1);
        podListRequest.setPageSize(10);
        BCMListPodResponse resp = bcmPodService.listPods(podListRequest);
        Assert.assertTrue(resp.getResult().size() == 8);
        Assert.assertTrue(resp.getTotalCount() == 8);

        for (PodForBCM pod : resp.getResult()) {
            Assert.assertTrue(pod.getName() != "");
            Assert.assertTrue(pod.getPodId() != "");
            Assert.assertTrue(pod.getPodUuid() != "");
            Assert.assertTrue(pod.getStatus() != "");
            Assert.assertTrue(pod.getPublicIp() == "");
            Assert.assertTrue(pod.getInternalIp() != "");
        }
    }

    @Test
    public void listPodsTestWithPaging() {
        BCMListPodRequest podListRequest = new BCMListPodRequest();
        podListRequest.setKeywordType("keywordType");
        podListRequest.setKeyword("keyword");
        podListRequest.setPageNo(2);
        podListRequest.setPageSize(5);
        BCMListPodResponse resp = bcmPodService.listPods(podListRequest);
        Assert.assertTrue(resp.getResult().size() == 3);
        Assert.assertTrue(resp.getTotalCount() == 8);
    }

    @Test
    public void listPodContainerTest() {
        BCMListPodRequest podListRequest = new BCMListPodRequest();
        podListRequest.setKeywordType("keywordType");
        podListRequest.setKeyword("keyword");
        podListRequest.setPageNo(1);
        podListRequest.setPageSize(10);
        BCMPodContainerResponse resp = bcmPodService.listPodContainer(podListRequest);
        Assert.assertTrue(resp.getResult().size() == 8);
        Assert.assertTrue(resp.getTotalCount() == 8);

        int containerCount = 0;
        for (PodContainerForBCM pod : resp.getResult()) {
            Assert.assertTrue(pod.getPodId() != null);
            Assert.assertTrue(pod.getPodUuid() != null);
            Assert.assertTrue(pod.getStatus() != null);
            containerCount += pod.getContainers().size();
        }
        Assert.assertTrue(containerCount == 4);
    }
}