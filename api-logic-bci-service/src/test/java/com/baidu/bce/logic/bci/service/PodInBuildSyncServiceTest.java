package com.baidu.bce.logic.bci.service;


import com.baidu.bce.logic.bci.service.sync.service.PodInBuildSyncService;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import com.baidu.bce.logic.bci.service.util.TestConfiguration;
import com.baidu.bce.logic.bci.service.util.TestUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import endpoint.EndpointManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.sql.SQLException;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class, EndpointManager.class})
public class PodInBuildSyncServiceTest {

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Autowired
    private PodInBuildSyncService podInBuildSyncService;

    @Before
    public void setUp() throws IllegalAccessException, SQLException {
        testUtil.setUp();
    }

    @Test
    public void schedulerTest() {
        podInBuildSyncService.syncPodInBuild();
    }
}
