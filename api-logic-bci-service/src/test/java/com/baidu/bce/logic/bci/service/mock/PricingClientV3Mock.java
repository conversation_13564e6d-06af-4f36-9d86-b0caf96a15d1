package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.order.PricingClientV3;
import com.baidu.bce.pricing.service.model.common.Flavor;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class PricingClientV3Mock extends PricingClientV3 implements ThrowExceptionMock {

    public PricingClientV3Mock() {
        super("", "", "");
    }

    public BigDecimal getCpcFlavor(String region, String serviceType, DateTime queryTime,
                                   String chargeItem, Flavor flavor, String amount, int count) {
        return new BigDecimal("0.012");
    }

    public BigDecimal getCpcFlavorPrice(String region, String serviceType, DateTime queryTime,
            String chargeItem, Flavor flavor, BigDecimal amount, int count, String accountId) {
        return new BigDecimal("0.012");
    }

    @Override
    public void setThrowException(RuntimeException throwException) {

    }
}
