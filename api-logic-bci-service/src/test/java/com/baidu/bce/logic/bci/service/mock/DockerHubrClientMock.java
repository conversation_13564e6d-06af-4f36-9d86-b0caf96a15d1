package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.bci.DockerHubClient;
import com.baidu.bce.internalsdk.bci.model.DockerHubImage;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageResponse;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageTag;
import com.baidu.bce.internalsdk.bci.model.DockerHubImageTagResponse;
import org.springframework.stereotype.Component;

import java.util.Collections;


@Component
public class DockerHubrClientMock extends DockerHubClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public DockerHubrClientMock() {
        super();
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public DockerHubImageResponse listDockerHubImage(int page, int pageSize) {
        if (throwException != null) {
            throw throwException;
        }
        return new DockerHubImageResponse().setResults(Collections.singletonList(
                new DockerHubImage().setName("mysql")));
    }

    public DockerHubImageTagResponse listDockerHubImageTag(String name, int page, int pageSize) {
        if (throwException != null) {
            throw throwException;
        }
        return new DockerHubImageTagResponse().setResults(Collections.singletonList(
                new DockerHubImageTag().setName("latest")));
    }
}
