package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import com.baidu.bce.logic.bci.service.util.TestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class OrderClientMock extends OrderClient implements ThrowExceptionMock {

    @Autowired
    DatabaseUtil databaseUtil;

    private RuntimeException throwException;

    public OrderClientMock() {
        super(null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public Order get(String uuid) {
        Order order = null;
        try {
            order = TestUtil.fromJson(
                    databaseUtil.getResource("classpath*:order.json").getInputStream(), Order.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return order;
    }

    public Order update(String uuid, UpdateOrderRequest request) {
        return new Order();
    }
}
