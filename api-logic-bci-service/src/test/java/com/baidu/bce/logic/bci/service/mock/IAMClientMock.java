package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.iam.IAMClient;
import org.springframework.stereotype.Component;


@Component
public class IAMClientMock extends IAMClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public IAMClientMock() {
        super(null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    @Override
    public void stsServiceRoleActivate(String roleName, String accountId, String policyId, String serviceId) {
        if (throwException != null) {
            throw throwException;
        }
    }
}
