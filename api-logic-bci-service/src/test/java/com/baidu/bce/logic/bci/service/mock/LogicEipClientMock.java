package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.bci.LogicEipClient;
import com.baidu.bce.internalsdk.bci.model.BindEipToBciRequest;
import com.baidu.bce.internalsdk.eipv2.model.EipForCreate;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderCreateRequest;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class LogicEipClientMock extends LogicEipClient implements ThrowExceptionMock {

    private RuntimeException throwException;

    public LogicEipClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public void forceReleaseEip(String eip) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public void bindEip(String eip, BindEipToBciRequest bindEipRequest) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public void unbindEip(String eip) {
        if (throwException != null) {
            throw throwException;
        }
    }

    public OrderCreateRequest getEipCreateRequestForLogicalBCC(EipForCreate eipForCreate) {
        OrderCreateRequest orderCreateRequest = new OrderCreateRequest();
        orderCreateRequest.setOrderUuid(eipForCreate.getOrderUuid());
        List<Order.Item> items = new ArrayList<>();
        items.add(new Order.Item());
        orderCreateRequest.setItems(items);

        return orderCreateRequest;
    }
}
