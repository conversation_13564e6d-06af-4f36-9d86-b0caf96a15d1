package com.baidu.bce.logic.bci.service;


import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.bci.service.mock.OrderClientMock;
import com.baidu.bce.logic.bci.service.mock.ResourceClientMock;
import com.baidu.bce.logic.bci.service.orderexecute.PodNewOrderExecutorServiceV1;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import com.baidu.bce.logic.bci.service.util.TestConfiguration;
import com.baidu.bce.logic.bci.service.util.TestUtil;
import com.baidu.bce.logic.core.user.LogicUserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({LogicUserService.class})
public class PodNewOrderExecutorServiceTest {

    @Autowired
    PodNewOrderExecutorServiceV1 podNewOrderExecutorService;

    @Autowired
    DatabaseUtil databaseUtil;

    OrderClient orderClient;

    ResourceClient resourceClient;

    Order order;

    @Before
    public void init() throws IOException {
        orderClient = new OrderClientMock();
        resourceClient = new ResourceClientMock();
        order = TestUtil.fromJson(
                databaseUtil.getResource("classpath*:order.json").getInputStream(), Order.class);
    }

    @Test
    public void executeTest() {
        order.setUuid("response");
        podNewOrderExecutorService.execute(orderClient, resourceClient, order);
    }

    @Test
    public void executeTestBceException() {
        order.setUuid("null-response");
        podNewOrderExecutorService.execute(orderClient, resourceClient, order);
    }

    @Test
    public void checkSuccTest() {
        order.setUuid("succ");
        podNewOrderExecutorService.check(orderClient, resourceClient, order);
    }

    @Test
    public void checkErrorTest() {
        order.setUuid("error");
        podNewOrderExecutorService.check(orderClient, resourceClient, order);
    }
}
