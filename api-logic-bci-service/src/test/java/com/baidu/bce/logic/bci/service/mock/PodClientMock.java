package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.bci.PodClient;
import com.baidu.bce.internalsdk.bci.model.CreateServersResponse;
import com.baidu.bce.internalsdk.bci.model.PodStatusResponse;
import com.baidu.bce.internalsdk.bci.model.ServerForCreate;
import com.baidu.bce.internalsdk.bci.model.ServersResponse;
import com.baidu.bce.internalsdk.bci.model.TransactionDetailResponse;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import com.baidu.bce.logic.bci.service.util.TestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.ERROR_STATUS;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.POD_UUID;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.TRANSACTION_ID;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.TRANSACTION_STATUS;

@Component
public class PodClientMock extends PodClient implements ThrowExceptionMock {

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    private RuntimeException throwException;

    private static final String POD_INFO_LOCATION = "classpath*:pod_info.json";

    public PodClientMock() {
        super(null, null, null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public TransactionDetailResponse showTransaction(String transactionId) {
        if (throwException != null) {
            throw throwException;
        }
        TransactionDetailResponse response = new TransactionDetailResponse();

        ServersResponse server = new ServersResponse();
        server.setId(POD_UUID);
        response.setLstServer(Arrays.asList(server));

        if (TRANSACTION_STATUS.equalsIgnoreCase(transactionId)) {
            response.setStatus(TRANSACTION_STATUS);
        } else if (ERROR_STATUS.equalsIgnoreCase(transactionId)) {
            response.setStatus(ERROR_STATUS);
        }
        return response;
    }

    public CreateServersResponse createServers(ServerForCreate serverForCreate) {
        CreateServersResponse response = new CreateServersResponse();
        response.setTranscationId(TRANSACTION_ID);
        if ("response".equalsIgnoreCase(serverForCreate.getTransactionId())) {
            return response;
        } else if ("null-response".equalsIgnoreCase(serverForCreate.getTransactionId())) {
            return null;
        }

        return response;
    }

    public PodStatusResponse getPodStatus(Date date) {
        PodStatusResponse response = null ;
        try {
            response = testUtil.fromJson(
                    databaseUtil.getResource(POD_INFO_LOCATION).getInputStream(), PodStatusResponse.class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    public void deletePod(String podId) {
        if (throwException != null) {
            throw throwException;
        }
    }
}
