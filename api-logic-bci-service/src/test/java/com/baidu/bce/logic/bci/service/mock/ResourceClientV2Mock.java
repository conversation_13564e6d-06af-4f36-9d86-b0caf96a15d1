package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.order.ResourceClientV2;
import com.baidu.bce.internalsdk.order.model.BindShortIdRequest;
import org.springframework.stereotype.Component;

@Component
public class ResourceClientV2Mock extends ResourceClientV2 implements ThrowExceptionMock {

    private RuntimeException throwException;

    public ResourceClientV2Mock() {
        super(null, null);
    }

    @Override
    public void setThrowException(RuntimeException throwException) {
        this.throwException = throwException;
    }

    public void bindShortId(BindShortIdRequest request) {

    }
}
