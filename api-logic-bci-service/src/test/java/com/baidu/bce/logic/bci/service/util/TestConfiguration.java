package com.baidu.bce.logic.bci.service.util;

import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.logic.bci.dao.chargerecord.PodChargeRecordDao;
import com.baidu.bce.logic.bci.dao.chargestatus.PodChargeStatusDao;
import com.baidu.bce.logic.bci.service.LogicPodClientFactory;
import com.baidu.bce.logic.bci.service.common.service.LogicalResourceService;
import com.baidu.bce.logic.bci.service.common.service.LogicalZoneResourceService;
import com.baidu.bce.logic.bci.service.mock.AsyncExecutorServiceMock;
import com.baidu.bce.logic.bci.service.mock.LogicPodClientFactoryMock;
import com.baidu.bce.logic.bci.service.mock.LogicResourceServiceMock;
import com.baidu.bce.logic.bci.service.mock.LogicalZoneResourceServiceMock;
import com.baidu.bce.logic.bci.service.mock.PodChargeRecordDaoMock;
import com.baidu.bce.logic.bci.service.mock.PodChargeStatusDaoMock;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import endpoint.EndpointManager;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
@EnableAutoConfiguration
@PropertySource("classpath:application-test.properties")
@ComponentScan(
        basePackages = {"com.baidu.bce.*"},
        basePackageClasses = {RegionConfiguration.class},
        excludeFilters =
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = {
                        AsyncExecutorService.class,
                        LogicPodClientFactory.class,
                        LogicalResourceService.class
                })
)
public class TestConfiguration {
    private DatabaseUtil databaseUtil = new DatabaseUtil();

    public TestConfiguration() throws Exception {
        EndpointManager.setEndpoint("OrderV2", "");
    }

    @Bean
    @Primary
    public SqlSessionFactoryBean logicSqlSessionFactoryBean() {
        return databaseUtil.getSqlSessionFactoryBean();
    }

    @Bean(name = "logicalJdbcTemplate")
    @Primary
    public JdbcTemplate logicalJdbcTemplate() {
        return databaseUtil.getJdbcTemplate();
    }

    @Bean
    @Primary
    public org.springframework.boot.autoconfigure.web.ServerProperties server() {
        return new org.springframework.boot.autoconfigure.web.ServerProperties();
    }

    @Bean
    public DatabaseUtil databaseUtil() {
        return databaseUtil;
    }

    @Bean
    public LogicPodClientFactory logicPodClientFactory() {
        return new LogicPodClientFactoryMock();
    }

    @Bean
    public LogicalZoneResourceService logicalZoneResourceService() {
        return new LogicalZoneResourceServiceMock();
    }

    @Bean
    public AsyncExecutorService asyncExecutorService() {
        return new AsyncExecutorServiceMock();
    }

    @Bean
    public LogicalResourceService logicalResourceService() {
        return new LogicResourceServiceMock();
    }

    @Bean
    public ResourceLoader createResourceLoader() {
        return new DefaultResourceLoader();
    }

    @Bean
    public PodChargeRecordDao podChargeRecordDao() {
        return new PodChargeRecordDaoMock();
    }

    @Bean
    public PodChargeStatusDao podChargeStatusDao() {
        return new PodChargeStatusDaoMock();
    }
}
