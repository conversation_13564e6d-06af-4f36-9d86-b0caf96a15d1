package com.baidu.bce.logic.bci.service.mock;

import com.baidu.bce.internalsdk.bci.EipTimeOutV2Client;
import com.baidu.bce.internalsdk.bci.model.EipResponse;
import com.baidu.bce.internalsdk.eipv2.model.EipInstanceCreateModel;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Component
public class EipTimeOutV2ClientMock extends EipTimeOutV2Client {

    public EipTimeOutV2ClientMock() {
        super(null, null, null);
    }

    public EipResponse createEipWithLongTimeOut(EipInstanceCreateModel createModel) {
        EipResponse eipResponse = new EipResponse();
        EipResponse.SingleEipResponse eip = new EipResponse.SingleEipResponse();
        eip.setEip("*********");
        eipResponse.setEips(Collections.singletonList(eip));

        return eipResponse;
    }
}
