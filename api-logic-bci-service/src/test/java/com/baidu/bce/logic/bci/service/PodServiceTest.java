package com.baidu.bce.logic.bci.service;


import com.baidu.bce.asyncwork.sdk.service.AsyncExecutorService;
import com.baidu.bce.internalsdk.bci.model.DockerHubImage;
import com.baidu.bce.internalsdk.bci.model.OfficialImage;
import com.baidu.bce.internalsdk.bci.model.UserImage;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.service.exception.PodExceptions;
import com.baidu.bce.logic.bci.service.model.BciCreateResponse;
import com.baidu.bce.logic.bci.service.model.DeletePod;
import com.baidu.bce.logic.bci.service.model.EipPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.EmptyDir;
import com.baidu.bce.logic.bci.service.model.IOrderItem;
import com.baidu.bce.logic.bci.service.model.Nfs;
import com.baidu.bce.logic.bci.service.model.PodBatchDeleteRequest;
import com.baidu.bce.logic.bci.service.model.PodDetail;
import com.baidu.bce.logic.bci.service.model.PodListRequest;
import com.baidu.bce.logic.bci.service.model.PodPurchaseRequest;
import com.baidu.bce.logic.bci.service.model.Volume;
import com.baidu.bce.logic.bci.service.pod.PodService;
import com.baidu.bce.logic.bci.service.util.DatabaseUtil;
import com.baidu.bce.logic.bci.service.util.TestConfiguration;
import com.baidu.bce.logic.bci.service.util.TestUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.bci.service.constant.PodConstants.FROM_CONSOLE;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.DELETE_POD_ID;
import static com.baidu.bce.logic.bci.service.constant.ServiceTestConstants.POD_ID;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@PowerMockIgnore({"javax.management.*","javax.net.ssl.*"})
@PrepareForTest({LogicUserService.class})
public class PodServiceTest {

    protected static final Logger logger = LoggerFactory.getLogger(PodServiceTest.class);

    @Autowired
    private PodService podService;

    @Autowired
    private TestUtil testUtil;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Mock
    private AsyncExecutorService asyncExecutorService;


    @Rule
    public ExpectedException thrown = ExpectedException.none();

    private static final String CREATE_POD_LOCATION = "classpath*:create_pod.json";

    private BaseCreateOrderRequestVo<IOrderItem> request;

    @Before
    public void setUp() throws IllegalAccessException, SQLException, IOException {
        testUtil.setUp();
        request = testUtil.orderRequest(
                databaseUtil.getResource(CREATE_POD_LOCATION).getInputStream(), IOrderItem.class);
    }

    @Test
    public void downloadConfigFileTest() {
        podService.downloadConfigFile("ffcdfa25-6edc-4b7c-943d-d52250500123", "interceptor", "pod/interceptor");
    }

    @Test
    public void bindEipTest() {
        podService.bindEipToPod("**************", "ffcdfa25-6edc-4b7c-943d-d52250500123");
    }

    //@Test
    public void createPodTest() {
        BciCreateResponse response = podService.createPod(request, FROM_CONSOLE);
        Assert.assertNotNull(response.getOrderId());
    }

    //@Test
    public void createPodWithEipTest() {
        BaseCreateOrderRequestVo.Item<IOrderItem> item = new BaseCreateOrderRequestVo.Item<>();
        EipPurchaseRequest config = new EipPurchaseRequest();
        item.setConfig(config);

        config.setServiceType("EIP");
        config.setBandwidthInMbps(10);
        config.setName("eip");
        config.setCount(1);
        config.setProductType("postpay");

        request.getItems().add(item);
        BciCreateResponse response = podService.createPod(request, FROM_CONSOLE);
        Assert.assertNotNull(response.getOrderId());
    }

    @Test(expected = CommonExceptions.ResourceInTaskException.class)
    public void deletePodStatusTest() {
        PodBatchDeleteRequest request = new PodBatchDeleteRequest();
        List<DeletePod> deletePods = new ArrayList<>();
        DeletePod deletePod = new DeletePod();
        deletePod.setPodId("p-3reeev3");
        deletePods.add(deletePod);
        request.setDeletePods(deletePods);
        request.setRelatedReleaseFlag(true);
        podService.deletePod(request);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void createPodRequestInvalidExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        order.setItems(new ArrayList<BaseCreateOrderRequestVo.Item<IOrderItem>>());
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.RequestInvalidException.class)
    public void createPodRestartPolicyExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            podPurchaseRequest.setRestartPolicy("wrong_policy");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test(expected = PodExceptions.VolumeNameInvalidException.class)
    public void createPodVolumeNameExceptionTest() {
        BaseCreateOrderRequestVo<IOrderItem> order = request;
        List<BaseCreateOrderRequestVo.Item<IOrderItem>> items = order.getItems();
        for (BaseCreateOrderRequestVo.Item<IOrderItem> item : items) {
            PodPurchaseRequest podPurchaseRequest = (PodPurchaseRequest) item.getConfig();
            Volume volumes = podPurchaseRequest.getVolume();
            List<Nfs> nfs = volumes.getNfs();
            List<EmptyDir> emptyDirs = volumes.getEmptyDir();

            nfs.get(0).setName("same_name");
            emptyDirs.get(0).setName("same_name");
        }
        podService.createPod(order, FROM_CONSOLE);
    }

    @Test
    public void listPodTest() {

        PodListRequest listRequest =
                new PodListRequest("", "name", "desc",
                        "name", 1, 10, "");

        LogicPageResultResponse<PodPO> list = podService.listPodsWithPageByMultiKey(listRequest);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list.getResult()));
    }

    @Test
    public void listPodByTagTest() {
        PodListRequest listRequest =
                new PodListRequest("", "name", "desc",
                        "tag", 1, 10, "");
        Map<String, String> filter = new HashMap<>();
        filter.put("tag", "tt");
        listRequest.setFilterMap(filter);
        LogicPageResultResponse<PodPO> list = podService.listPodsWithPageByMultiKey(listRequest);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list.getResult()));
    }

    @Test
    public void listPodWithInvalidCharTest() {

        PodListRequest listRequest =
                new PodListRequest("", "name", "desc",
                        "name", 1, 10, "");
        Map<String, String> filter = new HashMap<>();
        filter.put("#@", "*^%");
        listRequest.setFilterMap(filter);
        LogicPageResultResponse<PodPO> list = podService.listPodsWithPageByMultiKey(listRequest);
        Assert.assertTrue(CollectionUtils.isEmpty(list.getResult()));
    }

    @Test
    public void listPodByUuid() {
        List<PodPO> podPOS = podService.getPods(Arrays.asList("ffcdfa25-6edc-4b7c-943d-d5225050086a"));
        Assert.assertTrue(CollectionUtils.isNotEmpty(podPOS));
    }

    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void listPodByNullUuid() {
        podService.getPods(null);
    }

    @Test
    public void listDockerHubImageTest() {
        LogicPageResultResponse<DockerHubImage> response = podService.listDockerHubImages("", "", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listDockerHubImageWithKeyWordTest() {
        LogicPageResultResponse<DockerHubImage> response = podService.listDockerHubImages("mysql", "name", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listDockerHubImageTagTest() {
        LogicPageResultResponse<String> response = podService.listDockerHubImageTags("mysql", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listUserImageTest() {
        LogicPageResultResponse<UserImage> response = podService.listUserImage("", "", "",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isEmpty(response.getResult()));
    }

    @Test
    public void listUserImageWithOrderTest() {
        LogicPageResultResponse<UserImage> response = podService.listUserImage("", "", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isEmpty(response.getResult()));
    }

    @Test
    public void listOfficialImageTest() {
        LogicPageResultResponse<OfficialImage> response = podService.listOfficialImage("baidu", "name", "",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listOfficialImageWithOrderTest() {
        LogicPageResultResponse<OfficialImage> response = podService.listOfficialImage("baidu", "name", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }

    @Test
    public void listOfficialImageWithNoKeyWordTest() {
        LogicPageResultResponse<OfficialImage> response = podService.listOfficialImage("", "", "desc",
                "name", 1, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getResult()));
    }


    @Test(expected = CommonExceptions.RequestInvalidException.class)
    public void bindEipRequestInvalidTest() {
        podService.bindEipToPod("**************", "dw");
    }

    @Test
    public void unBindEipTest() {
        podService.unBindEipFromPod(POD_ID);
    }

    @Test
    public void bciQuotaTest() {
        podService.getBciQuota(true);
    }


    @Test
    public void podDetailTest() {
        PodDetail podDetail = podService.podDetail(POD_ID);
        Assert.assertNotNull(podDetail);
    }

    @Test
    public void deletePodRelatedReleaseTest() {
        PodBatchDeleteRequest request = new PodBatchDeleteRequest();
        List<DeletePod> deletePods = new ArrayList<>();
        DeletePod deletePod = new DeletePod();
        deletePod.setPodId(DELETE_POD_ID);
        deletePods.add(deletePod);
        request.setDeletePods(deletePods);
        request.setRelatedReleaseFlag(true);

        podService.deletePod(request);
    }

    @Test
    public void deletePodTest() {
        PodBatchDeleteRequest request = new PodBatchDeleteRequest();
        List<DeletePod> deletePods = new ArrayList<>();
        DeletePod deletePod = new DeletePod();
        deletePod.setPodId("p-4WRtfvevf");
        deletePods.add(deletePod);
        request.setDeletePods(deletePods);
        request.setRelatedReleaseFlag(false);

        podService.deletePod(request);
    }

    @Test(expected = PodExceptions.PodIdIsEmptyException.class)
    public void deletePodIdIsEmptyTest() {
        podService.deletePod(null);
    }

    @Test
    public void downloadTest() {
        PodListRequest listRequest =
                new PodListRequest("keyword", "podId", "desc",
                        "name", 1, 10, "");
        podService.download(listRequest);
    }

}
