package com.baidu.bce.logic.bci.dao.chargerecord.mapper;

import com.baidu.bce.logic.bci.dao.chargerecord.model.PodChargeRecord;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.sql.Timestamp;
import java.util.Date;

public interface PodChargeRecordMapper {


    @Update("update t_pod_charge_record set lock_time = @{lockDate}, lock_id = @{lockId} "
            + " where id = @{id} and (lock_id = ''  or lock_time + " + "interval @{overTime} second <= @{now})")
    int tryToLockOneLine(@Param("lockDate") Date lockDate, @Param("lockId")String lockId, @Param("id")Long id,
                         @Param("overTime")int overTime, @Param("now") Date now);

    @Insert("insert into t_pod_charge_record (msg_id, charge_time,"
            + "last_charge_time, lock_id, lock_time, push_status) "
            + "select @{podChargeRecord.msgId,jdbcType=VARCHAR}, @{podChargeRecord.chargeTime,jdbcType=TIMESTAMP}, "
            + "@{podChargeRecord.lastChargeTime,jdbcType=TIMESTAMP}, @{podChargeRecord.lockId,jdbcType=VARCHAR}, "
            + "@{podChargeRecord.lockTime,jdbcType=TIMESTAMP}, @{podChargeRecord.pushStatus,jdbcType=VARCHAR} "
            + "from dual where not exists (select id from t_pod_charge_record where push_status = '')")
    int insert(@Param("podChargeRecord")PodChargeRecord podChargeRecord);

    @Select("select id, msg_id, push_time, charge_time, last_charge_time, lock_id, lock_time, push_status "
            + "from t_pod_charge_record where push_status != 'succ' "
            + "order by charge_time desc limit 1")
    PodChargeRecord getNeedToPush();


    @Update("update t_pod_charge_record set push_time = utc_timestamp(), push_status = 'succ' "
            + "where id = @{id}")
    int updateSucc(@Param("id")Long id);

    @Update("update t_pod_charge_record set lock_time = '1970-01-01 08:00:01',lock_id = '' "
            + "where id = @{id} ")
    int unLock(@Param("id")Long id);

    @Delete("delete from t_pod_charge_record where id = @{id} ")
    int delete(@Param("id") Long id);

    @Select("select id, msg_id, push_time, charge_time, last_charge_time, lock_id, lock_time, push_status "
            + "from t_pod_charge_record where push_status = 'succ' "
            + "order by charge_time desc limit 1")
    PodChargeRecord getLastPodChargeRecord();

    @Delete("delete from t_pod_charge_record where push_time < @{time} and push_status = 'succ'")
    int deleteBefore(@Param("time") Timestamp time);
}
