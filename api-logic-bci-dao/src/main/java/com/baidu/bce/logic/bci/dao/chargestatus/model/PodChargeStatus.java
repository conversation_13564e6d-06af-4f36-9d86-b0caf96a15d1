package com.baidu.bce.logic.bci.dao.chargestatus.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class PodChargeStatus {
    private long id;
    private String podUuid = "";
    private String previousState = "";
    private String currentState = "";
    private String chargeState = "";
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
}
