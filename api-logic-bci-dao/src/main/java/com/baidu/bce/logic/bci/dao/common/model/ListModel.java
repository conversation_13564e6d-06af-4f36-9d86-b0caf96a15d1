package com.baidu.bce.logic.bci.dao.common.model;

import com.baidu.bce.logic.core.request.OrderModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ListModel {

    /**
     * 关键字类型
     */
    private String keywordType;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 页码
     */
    private int pageNo;

    /**
     * 每页数据项数目
     */
    private int pageSize;

    /**
     * 多重排序
     */
    private List<OrderModel> orders;

    /**
     * 是一个系统生成的字符串，用来标记查询的起始位置
     */
    protected String marker;

    /**
     * 每页包含的最大数量，最大数量通常不超过1000。缺省值为1000
     */
    protected Integer maxKeys = 1000;

    /**
     * 多参数过滤条件
     */
    private Map<String, String> filterMap;

    /**
     * 逻辑zone名称
     */
    private String logicalZone;

}
