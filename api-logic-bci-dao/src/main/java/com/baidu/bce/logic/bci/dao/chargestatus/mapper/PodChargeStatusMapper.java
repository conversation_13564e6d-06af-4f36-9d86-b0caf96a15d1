package com.baidu.bce.logic.bci.dao.chargestatus.mapper;

import com.baidu.bce.logic.bci.dao.chargestatus.model.PodChargeStatus;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.sql.Timestamp;
import java.util.List;

public interface PodChargeStatusMapper {

    @Insert("INSERT INTO t_pod_charge_status(pod_uuid, previous_state, current_state, charge_state, created_time) "
            + "select @{podChargeStatus.podUuid,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.previousState,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.currentState,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.chargeState,jdbcType=VARCHAR}, "
            + "@{podChargeStatus.createdTime} "
            + "FROM DUAL WHERE NOT EXISTS( "
            + "SELECT id from t_pod_charge_status WHERE pod_uuid = @{podChargeStatus.podUuid,jdbcType=VARCHAR} "
            + "AND charge_state = @{podChargeStatus.chargeState,jdbcType=VARCHAR} "
            + "AND created_time = (SELECT MAX(created_time) FROM t_pod_charge_status "
            + "WHERE pod_uuid = @{podChargeStatus.podUuid,jdbcType=VARCHAR}))")
    int insert(@Param("podChargeStatus")PodChargeStatus podChargeStatus);


    @Select("select id, pod_uuid, previous_state, current_state, charge_state, created_time "
            + "from t_pod_charge_status where created_time >= @{lastChargeTime} and created_time < @{chargeTime} "
            + "and pod_uuid != @{sincePlaceHolder} "
            + "order by created_time asc")
    List<PodChargeStatus> listByTime(@Param("lastChargeTime")Timestamp lastChargeTime,
                                     @Param("chargeTime")Timestamp chargeTime,
                                     @Param("sincePlaceHolder")String sincePlaceHolder);

    @Select("select id, pod_uuid, previous_state, current_state, charge_state, created_time "
            + "from t_pod_charge_status where pod_uuid = @{sincePlaceHolder} "
            + "order by created_time desc limit 1" )
    PodChargeStatus getSinceTime(@Param("sincePlaceHolder")String sincePlaceHolder);

    @Update("update t_pod_charge_status set created_time = @{createdTime} "
            + " where pod_uuid = @{sincePlaceHolder}")
    void updateSinceTime(@Param("createdTime")Timestamp createdTime,
                         @Param("sincePlaceHolder")String sincePlaceHolder);
}
