package com.baidu.bce.logic.bci.dao.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by huping on 2019-07-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WebShell {
    private String podId ; // 容器组id
    private String containerName  ; // 容器名称
    private Integer terminalHeight = 0 ; // terminal高度
    private Integer terminalWeight = 0; // terminal宽度
    private Boolean tty = true ; // 是否分配tty
    private Boolean stdin = true; // 是否开启stdin
    private List<String> command = new ArrayList<>(); // ["sh"]
}
