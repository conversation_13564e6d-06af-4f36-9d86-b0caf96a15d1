package com.baidu.bce.logic.bci.dao.pod;

import com.baidu.bce.logic.bci.dao.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.dao.common.model.PodListModel;
import com.baidu.bce.logic.bci.dao.pod.mapper.PodMapper;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.bci.dao.pod.util.PodDBHelper;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.plat.webframework.database.SqlEscape;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository("podDao")
public class PodDao {

    @Autowired
    private PodMapper podMapper;

    private static final String POD_PREFIX = "p-";

    public List<PodPO> listPodsByMultiKey(String accountId, PodListModel podListModel,
                                          List<PodFilterQueryModel> queryList) {

        // Order by转换
        List<OrderModel> orderModels = podListModel.getOrders();
        // orderBy字段需要返回给fe，不能直接修改
        List<OrderModel> orderModelsTransformed = null;

        Iterator<PodFilterQueryModel> iterator = queryList.iterator();
        while (iterator.hasNext()) {
            PodFilterQueryModel item = iterator.next();
            if (PodDBHelper.FIELD_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_MAP.get(item.getName()));
                if (item.getValue() != null) {
                    item.setValue("%" + SqlEscape.escapeLikePattern(item.getValue()) + "%");
                }
            } else {
                iterator.remove();
            }
        }

        if (orderModels != null && !orderModels.isEmpty()) {
            orderModelsTransformed = new ArrayList<>(orderModels.size());
            for (OrderModel orderModel : orderModels) {
                OrderModel model = new OrderModel();
                model.setOrderBy(PodDBHelper.parseOrderBy(orderModel.getOrderBy()));
                model.setOrder(orderModel.getOrder());
                orderModelsTransformed.add(model);
            }
        }

        return podMapper.listPodsByMultiKey(accountId, podListModel, orderModelsTransformed, queryList);
    }

    public List<PodPO> listPods(String accountId, List<String> shortIds, String keywordType, String keyword) {
        if (PodDBHelper.FIELD_MAP.containsKey(keywordType)) {
            keywordType = PodDBHelper.FIELD_MAP.get(keywordType);
        } else {
            keywordType = null;
            keyword = null;
        }
        return podMapper.listPods(accountId, shortIds, keywordType, keyword);
    }

    public List<PodPO> listPodsByUpdatedTime(String accountId, Timestamp updatedTime,
                                             String keywordType, String keyword) {
        if (PodDBHelper.FIELD_MAP.containsKey(keywordType)) {
            keywordType = PodDBHelper.FIELD_MAP.get(keywordType);
        } else {
            keywordType = null;
            keyword = null;
        }
        return podMapper.listPodsByUpdatedTime(accountId, updatedTime, keywordType, keyword);
    }

    @Deprecated
    public int queryPodCountByMultiKey(String accountId, PodListModel podListModel,
                                       List<PodFilterQueryModel> queryList) {
        Iterator<PodFilterQueryModel> iterator = queryList.iterator();
        while (iterator.hasNext()) {
            PodFilterQueryModel item = iterator.next();
            if (PodDBHelper.FIELD_MAP.containsKey(item.getName())) {
                item.setName(PodDBHelper.FIELD_MAP.get(item.getName()));
                if (item.getValue() != null) {
                    item.setValue("%" + SqlEscape.escapeLikePattern(item.getValue()) + "%");
                }
            } else {
                iterator.remove();
            }
        }
        return podMapper.queryPodCountByMultiKey(accountId, podListModel, queryList);
    }

    public PodPO getPodDetail(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodDetailById(accountId, podId);
        } else {
            return podMapper.getPodDetailByUuid(accountId, podId);
        }
    }

    public PodPO getPodById(String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            return podMapper.getPodById(podId);
        } else {
            return podMapper.getPodByUuid(podId);
        }
    }

    public String queryPodUuid(String podId) {
        return podMapper.queryPodUuid(podId);
    }

    public void deletePod(String accountId, String podId) {
        if (StringUtils.startsWith(podId, POD_PREFIX)) {
            podMapper.deletePodById(accountId, podId);
        } else {
            podMapper.deletePodByUuid(accountId, podId);
        }
    }

    public void batchInsertPods(List<PodPO> podPOList) {
        podMapper.batchInsertPods(podPOList);
    }

    public List<String> listAllPodUuids(String accountId) {
        return podMapper.listAllPodUuids(accountId);
    }

    public int getCreatedPodInQuota(String accountId) {
        return podMapper.getCreatedPodInQuota(accountId);
    }

    public Map<String, String> podIdMap(String accountId) {
        List<PodPO> podPOList = podMapper.listPodByAccount(accountId);
        Map<String, String> map = new HashMap<>();
        for (PodPO podPO : podPOList) {
            map.put(podPO.getPodId(), podPO.getPodUuid());
        }
        return map;
    }

    public List<PodPO> listByOrderId(String accountId, String orderId) {
        return podMapper.listByOrderId(accountId, orderId);
    }

    @Transactional
    public void batchUpdateStatus(List<PodPO> podPOList) {
        for (PodPO podPO : podPOList) {
            podMapper.updateOrderInfo(podPO);
        }
    }

    public void updateOrderId(List<String> podIds, String accountId, String orderId) {
        podMapper.updateOrderId(podIds, accountId, orderId);
    }

    public List<PodPO> listByStatus(String status) {
        return podMapper.listByStatus(status);
    }

    @Transactional
    public List<PodPO> listAllPods() {
        // 目前只有推送计费（每隔1分钟）和同步pod、container信息（每隔20秒）时才会调用
        return podMapper.listAllPods();
    }

    public void updateSinceInfo(PodPO podPO) {
        podMapper.updateSinceInfo(podPO);
    }

    public Timestamp updateSince() {
        return podMapper.updateSince();
    }

    public void enableLog(List<PodPO> podPOS, String accountId) {
        podMapper.enableLog(podPOS, accountId);
    }

    public void disableLog(List<PodPO> podPOS, String accountId) {
        podMapper.disableLog(podPOS, accountId);
    }

    public List<PodPO> listPodPOByIds(List<String> podIds, String accountId) {
        List<PodPO> podsByIds = podMapper.listPodByPodIds(podIds, accountId);
        Set<PodPO> podSet = new HashSet<>(podsByIds);
        if (podIds.size() != podsByIds.size()) {
            List<PodPO> podsByuuids = podMapper.listPodByPodUuids(podIds, accountId);
            podSet.addAll(podsByuuids);
        }
        return new ArrayList<>(podSet);
    }

    public Map<String, Map<String, Long>> countPodWithSubnetId(String accountId, List<String> subnetIds) {
        return podMapper.countPodWithSubnetId(accountId, subnetIds);
    }

    public int getPodCountByOrderid( String orderId) {
        return podMapper.getPodCountByOrderid( orderId);
    }
}
