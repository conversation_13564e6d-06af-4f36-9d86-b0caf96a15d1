package com.baidu.bce.logic.bci.dao.container;

import com.baidu.bce.logic.bci.dao.container.mapper.ContainerMapper;
import com.baidu.bce.logic.bci.dao.container.model.ContainerPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("containerDao")
public class ContainerDao {

    @Autowired
    private ContainerMapper containerMapper;

    public void batchInsert(List<ContainerPO> containerPOS) {
        containerMapper.batchInsert(containerPOS);
    }

    public List<ContainerPO> listByPodId(String podId) {
        return containerMapper.listByPodId(podId);
    }

    public void batchUpdate(List<ContainerPO> containerPOS) {
        for (ContainerPO containerPO : containerPOS) {
            containerMapper.update(containerPO);
        }
    }

    public void deleteContainers(String accountId, String podId) {
        containerMapper.deleteContainer(accountId, podId);
    }

    public void updateContainersPodId(String podId, String podUuid) {
        containerMapper.updateContainersByPodId(podId, podUuid);
    }

    public String queryContainerUuid(String podUuid, String name) {
        return containerMapper.queryContainerUuid(podUuid, name);
    }

    public List<ContainerPO> listContainerByPod(String accountId, List<String> podIds) {
        return containerMapper.listPodContainerByPodIds(accountId, podIds);
    }
}
