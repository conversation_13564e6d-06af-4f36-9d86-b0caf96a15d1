package com.baidu.bce.logic.bci.dao.imagecache;

import com.baidu.bce.logic.bci.dao.imagecache.mapper.ImageCacheMapper;
import com.baidu.bce.logic.bci.dao.imagecache.model.ImageCachePO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("imageCacheDao")
public class ImageCacheDao {

    @Autowired
    ImageCacheMapper imageCacheMapper;

    public void insert(ImageCachePO imageCachePO) {
        imageCacheMapper.insert(imageCachePO);
    }

    public void updateTaskStatus(String taskId, String status) {
        imageCacheMapper.updateTaskStatus(taskId, status);
    }

    public List<ImageCachePO> queryTask(String taskId, String userId) {
        return imageCacheMapper.getTaskById(taskId, userId);
    }
    public int getTaskById(String taskId) {
        return imageCacheMapper.getByTaskId(taskId);
    }
}
