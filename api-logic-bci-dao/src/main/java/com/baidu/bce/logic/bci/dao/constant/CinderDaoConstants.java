package com.baidu.bce.logic.bci.dao.constant;

public class CinderDaoConstants {
    public static final String VOLUME_SYSTEM_PREFIX = "sys-disk-";
    public static final String VOLUME_EPHEMERAL_PREFIX = "local-disk-";

    public static class CinderStatus {
        public static final String AVAILABLE = "available";
        public static final String UNAVAILABLE = "unavailable";
        public static final String CREATING = "creating";
        public static final String IN_USE = "in-use";
        public static final String ATTACHING = "attaching";
        public static final String DETACHING = "detaching";
        public static final String ERROR_DELETING = "error-deleting";
        public static final String ERROR = "error";
        public static final String IN_SNAPSHOT = "in-snapshot";
        public static final String IN_ROLLBACK = "in-rollback";
        public static final String EXTENDING = "extending";
        public static final String EXPIRED = "expired";
    }

    public static final String TO_POSTPAY = "to_postpay";
    public static final String TO_PREPAY = "to_prepay";
}
