package com.baidu.bce.logic.bci.dao.container.mapper;

import com.baidu.bce.logic.bci.dao.container.model.ContainerPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ContainerMapper {

    String INSERT_CONTAINER = "insert into t_container (pod_uuid, name, container_uuid, image_name, image_version, "
            + "image_address, cpu, memory, working_dir, image_pull_policy, commands, args, ports, volume_mounts, envs, "
            + "user_id, previous_state, current_state, restart_count, created_time, updated_time)";

    @Insert(INSERT_CONTAINER + " values "
            + "#repeat($_parameter.containers $container \",\")"
            + "(@{container.podUuid}, @{container.name}, @{container.containerUuid}, @{container.imageName}, "
            + "@{container.imageVersion}, @{container.imageAddress}, @{container.cpu},@{container.memory}, "
            + "@{container.workingDir}, @{container.imagePullPolicy},@{container.commands}, @{container.args}, "
            + "@{container.ports}, @{container.volumeMounts}, @{container.envs}, @{container.userId}, "
            + "@{container.previousState}, @{container.currentState}, @{container.restartCount}, "
            + "@{container.createdTime}, @{container.updatedTime})"
            + "#end")
    void batchInsert(@Param("containers") List<ContainerPO> containers);

    String QUERY_CONTAINER = "SELECT id, pod_uuid, name, container_uuid, image_name, image_version, image_address, " +
            "cpu, memory, working_dir, image_pull_policy, commands, args, ports, volume_mounts, envs, user_id, " +
            "previous_state, current_state, restart_count, created_time, updated_time FROM t_container ";

    @Select(QUERY_CONTAINER + " where pod_uuid = @{podId} and deleted = 0")
    List<ContainerPO> listByPodId(@Param("podId") String podId);

    @Update("update t_container set pod_uuid = @{containerPO.podUuid}, updated_time = @{containerPO.updatedTime}, "
            + "previous_state = @{containerPO.previousState}, current_state = @{containerPO.currentState}, "
            + "restart_count = @{containerPO.restartCount}, container_uuid = @{containerPO.containerUuid} "
            + "where id = @{containerPO.id}")
    void update(@Param("containerPO") ContainerPO containerPO);

    @Update("update t_container set deleted = 1, deleted_time = UTC_TIMESTAMP() where deleted = 0 AND "
            + "user_id = @{accountId} and pod_uuid = @{podId}")
    void deleteContainer(@Param("accountId")String accountId, @Param("podId") String podId);

    @Update("update t_container set pod_uuid = @{podUuid} where pod_uuid = @{podId}")
    void updateContainersByPodId(@Param("podId")String podId, @Param("podUuid")String podUuid);

    @Select("select container_uuid from t_container where pod_uuid = @{podUuid} and name = @{name} LIMIT 1")
    String queryContainerUuid(@Param("podUuid")String podUuid, @Param("name")String name);


    @Select(  QUERY_CONTAINER
            + " where user_id = @{accountId}"
            + " #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "     and  "
            + "     #in($_parameter.podIds $podId \" pod_uuid \" ) "
            + "         @{podId}"
            + "     #end"
            + " #end ")
    List<ContainerPO> listPodContainerByPodIds(@Param("accountId") String accountId,
                                                @Param("podIds") List<String> podIds);
}
