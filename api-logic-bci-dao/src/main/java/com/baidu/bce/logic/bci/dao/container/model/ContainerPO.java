package com.baidu.bce.logic.bci.dao.container.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.sql.Timestamp;

public class ContainerPO implements ContainerPOForBcm {

    private long id;
    private String podUuid = "";
    private String name = "";
    private String containerUuid = "";
    private String imageName = "";
    private String imageVersion = "";
    private String imageAddress = "";
    private float cpu;
    private float memory;
    private String workingDir = "";
    private String imagePullPolicy = "";
    private String commands = "";
    private String args = "";
    private String ports = "";
    private String volumeMounts = "";
    private String envs = "";
    private String userId = "";
    private String previousState = "";
    private String currentState = "";
    private int restartCount = 0;
    private int deleted = 0;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getPodUuid() {
        return podUuid;
    }

    public void setPodUuid(String podUuid) {
        this.podUuid = podUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public float getCpu() {
        return cpu;
    }

    public void setCpu(float cpu) {
        this.cpu = cpu;
    }

    public float getMemory() {
        return memory;
    }

    public void setMemory(float memory) {
        this.memory = memory;
    }

    public String getWorkingDir() {
        return workingDir;
    }

    public void setWorkingDir(String workingDir) {
        this.workingDir = workingDir;
    }

    public String getImagePullPolicy() {
        return imagePullPolicy;
    }

    public void setImagePullPolicy(String imagePullPolicy) {
        this.imagePullPolicy = imagePullPolicy;
    }

    public String getCommands() {
        return commands;
    }

    public void setCommands(String commands) {
        this.commands = commands;
    }

    public String getArgs() {
        return args;
    }

    public void setArgs(String args) {
        this.args = args;
    }

    public String getPorts() {
        return ports;
    }

    public void setPorts(String ports) {
        this.ports = ports;
    }

    public String getVolumeMounts() {
        return volumeMounts;
    }

    public void setVolumeMounts(String volumeMounts) {
        this.volumeMounts = volumeMounts;
    }

    public String getEnvs() {
        return envs;
    }

    public void setEnvs(String envs) {
        this.envs = envs;
    }

    public Timestamp getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }

    public Timestamp getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public Timestamp getDeletedTime() {
        return deletedTime;
    }

    public void setDeletedTime(Timestamp deletedTime) {
        this.deletedTime = deletedTime;
    }

    public String getImageVersion() {
        return imageVersion;
    }

    public void setImageVersion(String imageVersion) {
        this.imageVersion = imageVersion;
    }

    public String getImageAddress() {
        return imageAddress;
    }

    public void setImageAddress(String imageAddress) {
        this.imageAddress = imageAddress;
    }

    public String getContainerUuid() {
        return containerUuid;
    }

    public void setContainerUuid(String containerUuid) {
        this.containerUuid = containerUuid;
    }

    public String getPreviousState() {
        return previousState;
    }

    public void setPreviousState(String previousState) {
        this.previousState = previousState;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public int getRestartCount() {
        return restartCount;
    }

    public void setRestartCount(int restartCount) {
        this.restartCount = restartCount;
    }

    @Override
    public String toString() {
        return "ContainerPO{"
                + "podUuid='" + podUuid + '\''
                + ", name='" + name + '\''
                + ", containerUuid='" + containerUuid + '\''
                + ", imageName='" + imageName + '\''
                + ", imageVersion='" + imageVersion + '\''
                + ", imageAddress='" + imageAddress + '\''
                + ", cpu=" + cpu
                + ", memory=" + memory
                + ", workingDir='" + workingDir + '\''
                + ", imagePullPolicy='" + imagePullPolicy + '\''
                + ", commands='" + commands + '\''
                + ", args='" + args + '\''
                + ", ports='" + ports + '\''
                + ", volumeMounts='" + volumeMounts + '\''
                + ", envs='" + envs + '\''
                + ", userId='" + userId + '\''
                + ", previousState='" + previousState + '\''
                + ", currentState='" + currentState + '\''
                + ", restartCount=" + restartCount
                + ", deleted=" + deleted
                + ", createdTime=" + createdTime
                + ", updatedTime=" + updatedTime
                + ", deletedTime=" + deletedTime
                + '}';
    }
}
