package com.baidu.bce.logic.bci.dao.chargestatus;

import com.baidu.bce.logic.bci.dao.chargestatus.mapper.PodChargeStatusMapper;
import com.baidu.bce.logic.bci.dao.chargestatus.model.PodChargeStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Repository("chargeStatusDao")
public class PodChargeStatusDao {

    @Autowired
    PodChargeStatusMapper podChargeStatusMapper;

    // 特殊的id，这条记录只用来存储 since 时间点
    public static final String SINCE_PLACEHOLDER = "since-placeholder";

    public int insert(PodChargeStatus podChargeStatus) {
        return podChargeStatusMapper.insert(podChargeStatus);
    }

    public Map<String, LinkedList<PodChargeStatus>> listByTime(Timestamp lastChargeTime, Timestamp chargeTime) {
        List<PodChargeStatus> podChargeStatuses = podChargeStatusMapper.listByTime(lastChargeTime, chargeTime,
                SINCE_PLACEHOLDER);
        Map<String, LinkedList<PodChargeStatus>> map = new HashMap<>();
        for (PodChargeStatus podChargeStatus : podChargeStatuses) {
            if (!map.containsKey(podChargeStatus.getPodUuid())) {
                map.put(podChargeStatus.getPodUuid(), new LinkedList<PodChargeStatus>());
            }
            map.get(podChargeStatus.getPodUuid()).add(podChargeStatus);
        }
        return map;
    }

    public Timestamp getSinceTime() {
        PodChargeStatus status = podChargeStatusMapper.getSinceTime(SINCE_PLACEHOLDER);
        if (status == null) {
            return null;
        }
        return status.getCreatedTime();
    }

    public void updateSinceTime(Timestamp nextSinceTime) {
        podChargeStatusMapper.updateSinceTime(nextSinceTime, SINCE_PLACEHOLDER);
    }
}
