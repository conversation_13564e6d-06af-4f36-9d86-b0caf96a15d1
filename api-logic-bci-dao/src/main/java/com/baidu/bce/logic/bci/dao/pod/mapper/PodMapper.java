package com.baidu.bce.logic.bci.dao.pod.mapper;

import com.baidu.bce.logic.bci.dao.common.model.PodFilterQueryModel;
import com.baidu.bce.logic.bci.dao.common.model.PodListModel;
import com.baidu.bce.logic.bci.dao.pod.model.PodPO;
import com.baidu.bce.logic.core.request.OrderModel;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public interface PodMapper {

    String QUERY_POD = "select p.pod_id, p.pod_uuid, p.name, p.status, p.v_cpu, p.memory, p.eip_uuid, p.public_ip,"
            + " p.cce_uuid, p.internal_ip, p.subnet_uuid, p.security_group_uuid, p.restart_policy,"
            + " p.order_id, p.tags, p.description, p.user_id, p.zone_id,"
            + " p.resource_uuid, p.task_status, p.nfs, p.empty_dir, p.config_file, p.pod_volumes, p.created_time,"
            + " p.updated_time, p.application, p.enable_log, p.charge_source"
            + " FROM t_pod p ";

    String QUERY_POD_DETAIL = "select p.pod_id, p.pod_uuid, p.name, p.status, p.v_cpu, p.memory, p.eip_uuid, "
            + " p.public_ip, p.cce_uuid, p.internal_ip, p.subnet_uuid, p.security_group_uuid, p.restart_policy,"
            + " p.order_id, p.tags, p.description, p.user_id, p.zone_id,"
            + " p.resource_uuid, p.task_status, p.nfs, p.empty_dir, p.config_file, p.pod_volumes, p.created_time, "
            + " p.updated_time, p.application, p.enable_log, p.charge_source"
            + " FROM t_pod p ";

    @Select("select pod_id, pod_uuid, name, status, v_cpu, memory, eip_uuid, public_ip,"
            + " cce_uuid, internal_ip, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, "
            + " resource_uuid, task_status, nfs, empty_dir, config_file, created_time, updated_time, "
            + " application, enable_log, charge_source"
            + " FROM t_pod "
            + "#where() "
            + "  user_id = @{accountId} and order_id != '' and status != \'unknown\'"
            + "  and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.queryList && $_parameter.queryList.size() > 0) "
            + "    #repeat($_parameter.queryList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) like @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.model.includedUuids && $_parameter.model.includedUuids.size() > 0)"
            + "   and pod_uuid != '' and  "
            + "     #in($_parameter.model.includedUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end "
            + "#if($_parameter.orders && $_parameter.orders.size()>0) "
            + "  #repeat($_parameter.orders $orderModel \" , \" \" order by  \" ) "
            + "    $check.column(${orderModel.orderBy}) $check.order(${orderModel.order}) "
            + "  #end "
            + "#end ")
    List<PodPO> listPodsByMultiKey(@Param("accountId") String accountId,
                                   @Param("model") PodListModel podListModel,
                                   @Param("orders") List<OrderModel> orders,
                                   @Param("queryList") List<PodFilterQueryModel> queryList);

    @Select(  " #set($pattern = '%' + $_parameter.keyword + '%')"
            + " select pod_id, pod_uuid, name, status, v_cpu, memory, eip_uuid, public_ip,"
            + " cce_uuid, internal_ip, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, "
            + " resource_uuid, task_status, nfs, empty_dir, config_file, created_time, updated_time, "
            + " application, enable_log, charge_source"
            + " FROM t_pod WHERE "
            + " user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\'"
            + " and order_id != '' and updated_time >= @{updatedTime} "
            + " #if($_parameter.keywordType)"
            + "    AND $check.column($_parameter.keywordType) LIKE @{pattern, jdbcType=VARCHAR}"
            + " #end")
    List<PodPO> listPodsByUpdatedTime(@Param("accountId") String accountId,
                                      @Param("updatedTime") Timestamp updatedTime,
                                      @Param("keywordType") String keywordType,
                                      @Param("keyword") String keyword);

    @Select(  " #set($pattern = '%' + $_parameter.keyword + '%')"
            + " select pod_id, pod_uuid, name, status, v_cpu, memory, eip_uuid, public_ip,"
            + " cce_uuid, internal_ip, subnet_uuid, security_group_uuid, restart_policy,"
            + " order_id, tags, description, user_id, zone_id, "
            + " resource_uuid, task_status, nfs, empty_dir, config_file, created_time, updated_time, "
            + " application, enable_log, charge_source"
            + " FROM t_pod WHERE "
            + " user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\'"
            + " and order_id != '' "
            + " #if($_parameter.ids && $_parameter.ids.size() > 0)"
            + "     and  "
            + "     #in($_parameter.ids $shortId \" pod_id \" ) "
            + "         @{shortId}"
            + "     #end"
            + " #end"
            + " #if($_parameter.keywordType)"
            + "    AND $check.column($_parameter.keywordType) LIKE @{pattern, jdbcType=VARCHAR}"
            + " #end")
    List<PodPO> listPods(@Param("accountId") String accountId,
                                      @Param("ids") List<String> ids,
                                      @Param("keywordType") String keywordType,
                                      @Param("keyword") String keyword);

    @Select("select count(*) from t_pod "
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' "
            + "  and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.queryList && $_parameter.queryList.size() > 0) "
            + "    #repeat($_parameter.queryList $query \" and \" \" and \" ) "
            + "      $check.column(${query.name}) like @{query.value} "
            + "    #end "
            + "  #end "
            + "  #if($_parameter.model.includedUuids && $_parameter.model.includedUuids.size() > 0)"
            + "   and pod_uuid != '' and  "
            + "     #in($_parameter.model.includedUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end ")
    int queryPodCountByMultiKey(@Param("accountId") String accountId,
                                @Param("model") PodListModel cinderListModel,
                                @Param("queryList") List<PodFilterQueryModel> queryList);


    @Select(QUERY_POD_DETAIL + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.status !=\'deleted\'"
            + " and p.pod_id = @{podId}")
    PodPO getPodDetailById(@Param("accountId") String accountId, @Param("podId") String podId);

    @Select(QUERY_POD_DETAIL + " where p.user_id= @{accountId} and p.status !=\'unknown\' and p.status !=\'deleted\'"
            + " and p.pod_uuid = @{podId}")
    PodPO getPodDetailByUuid(@Param("accountId") String accountId, @Param("podId") String podId);


    @Select(QUERY_POD + " where p.status !=\'unknown\' and p.status !=\'deleted\' and p.pod_id = @{podId}")
    PodPO getPodById(@Param("podId") String podId);

    @Select(QUERY_POD + " where p.status !=\'unknown\' and p.status !=\'deleted\' and p.pod_uuid = @{podUuid}")
    PodPO getPodByUuid(@Param("podUuid") String podUuid);

    @Select("select pod_uuid from t_pod where pod_id = @{podId}")
    String queryPodUuid(@Param("podId") String podId);

    @Update("update t_pod set deleted = 1, status = 'deleted',"
            + " deleted_time = utc_timestamp() where deleted = 0 and user_id = @{accountId} and pod_id = @{podId}")
    void deletePodById(@Param("accountId") String accountId, @Param("podId") String podId);

    @Update("update t_pod set deleted = 1, status = 'deleted',"
            + " deleted_time = utc_timestamp() where deleted = 0 and user_id = @{accountId} and pod_uuid = @{podUuid}")
    void deletePodByUuid(@Param("accountId") String accountId, @Param("podUuid") String podUuid);

    String INSERT_POD = "INSERT INTO t_pod (pod_id, pod_uuid, name, status, v_cpu, memory, eip_uuid, public_ip,"
            + " cce_uuid, internal_ip, subnet_uuid, security_group_uuid, restart_policy,order_id, tags, description,"
            + " user_id, zone_id, resource_uuid, task_status, nfs, empty_dir, config_file, pod_volumes, created_time, "
            + " updated_time, application, enable_log, charge_source)";

    @Insert(INSERT_POD + " values "
            + "#repeat($_parameter.podPOs $podPO \",\")"
            + "(@{podPO.podId}, @{podPO.podUuid}, @{podPO.name}, @{podPO.status}, @{podPO.vCpu}, "
            + " @{podPO.memory}, @{podPO.eipUuid}, @{podPO.publicIp}, @{podPO.cceUuid}, @{podPO.internalIp}, "
            + " @{podPO.subnetUuid}, @{podPO.securityGroupUuid},"
            + " @{podPO.restartPolicy}, @{podPO.orderId}, @{podPO.tags}, @{podPO.description},"
            + " @{podPO.userId}, @{podPO.zoneId}, @{podPO.resourceUuid}, @{podPO.taskStatus}, @{podPO.nfs}, "
            + " @{podPO.emptyDir}, @{podPO.configFile}, @{podPO.podVolumes}, @{podPO.createdTime}, "
            + " @{podPO.updatedTime}, @{podPO.application}, @{podPO.enableLog}, @{podPO.chargeSource}) "
            + "#end")
    void batchInsertPods(@Param("podPOs") List<PodPO> podPOList);

    @Select("select pod_uuid from t_pod where user_id = @{accountId} and deleted = 0 "
            + " and status in ('Pending', 'Running')")
    List<String> listAllPodUuids(@Param("accountId")String accountId);

    @Select("select count(*) from t_pod where user_id = @{accountId} and deleted = 0 "
            + " and charge_source = 'user' and status in ('Pending', 'Running')")
    int getCreatedPodInQuota(@Param("accountId")String accountId);

    @Select(QUERY_POD
            + " where p.user_id= @{accountId} and p.status != \'unknown\' and p.deleted = 0 and p.status !=\'deleted\'"
            + " and p.order_id= @{orderId}")
    List<PodPO> listByOrderId(@Param("accountId")String accountId, @Param("orderId")String orderId);

    @Select(QUERY_POD + " where  p.status = @{status} and p.deleted = 0")
    List<PodPO> listByStatus(@Param("status")String status);

    @Select(QUERY_POD + " where  p.deleted = 0 and p.status !=\'deleted\'")
    List<PodPO> listAllPods();

    @Update("update t_pod set status = @{podPO.status} , updated_time = @{podPO.updatedTime} "
            + " where pod_id = @{podPO.podId} and deleted = 0 and "
            + " (status != @{podPO.status} or updated_time != @{podPO.updatedTime})")
    void updateSinceInfo(@Param("podPO") PodPO podPO);

    @Update("update t_pod set status = @{podPO.status} , pod_volumes = @{podPO.podVolumes}, "
            + " internal_ip = @{podPO.internalIp}, pod_uuid = @{podPO.podUuid}, resource_uuid = @{podPO.resourceUuid}"
            + " where pod_id = @{podPO.podId} and status != @{podPO.status} and deleted = 0")
    void updateOrderInfo(@Param("podPO") PodPO podPO);

    @Update("update t_pod set order_id = @{orderId} "
            + "#where() "
            + "  user_id = @{accountId} "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    void updateOrderId(@Param("podIds")List<String> podIds, @Param("accountId")String accountId,
                       @Param("orderId")String orderId);

    @Update("update t_pod set enable_log = 1 "
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.podPOS && $_parameter.podPOS.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podPOS $podPO \" pod_id \" ) "
            + "         @{podPO.podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    void enableLog(@Param("podPOS")List<PodPO> podPOS, @Param("accountId")String accountId);

    @Update("update t_pod set enable_log = 0 "
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.podPOS && $_parameter.podPOS.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podPOS $podPO \" pod_id \" ) "
            + "         @{podPO.podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    void disableLog(@Param("podPOS")List<PodPO> podPOS, @Param("accountId")String accountId);

    @Select("select max(updated_time) from t_pod where deleted != 1 and status != \'Pending\'")
    Timestamp updateSince();

    @Select(QUERY_POD + " where p.user_id = @{accountId} and p.deleted = 0")
    List<PodPO> listPodByAccount(@Param("accountId")String accountId);

    @Select(QUERY_POD
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.podIds && $_parameter.podIds.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podIds $podId \" pod_id \" ) "
            + "         @{podId}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodPO> listPodByPodIds(@Param("podIds")List<String> podIds, @Param("accountId")String accountId);

    @Select(QUERY_POD
            + "#where() "
            + "  user_id = @{accountId} and status != \'unknown\' and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.podUuids && $_parameter.podUuids.size() > 0)"
            + "   and  "
            + "     #in($_parameter.podUuids $podUuid \" pod_uuid \" ) "
            + "         @{podUuid}"
            + "     #end"
            + "  #end "
            + "#end ")
    List<PodPO> listPodByPodUuids(@Param("podUuids")List<String> podIds, @Param("accountId")String accountId);

    @MapKey("subnet_uuid")
    @Select("select subnet_uuid, count(*) as num from t_pod where "
            + "  user_id = @{accountId} and order_id != '' "
            + "  and deleted = 0 and status != \'deleted\' "
            + "  #if($_parameter.subnetIds && $_parameter.subnetIds.size() > 0)"
            + "     and  "
            + "     #in($_parameter.subnetIds $subnetId \" subnet_uuid \" ) "
            + "         @{subnetId}"
            + "     #end"
            + "  #end "
            + "  group by subnet_uuid")
    Map<String, Map<String, Long>> countPodWithSubnetId(@Param("accountId") String accountId,
                                              @Param("subnetIds") List<String> subnetIds);

    @Select("select count(*) from t_pod p"
            + " where p.status != \'unknown\' and p.deleted = 0 and p.status !=\'deleted\'"
            + " and p.order_id= @{orderId}")
    int getPodCountByOrderid(@Param("orderId") String orderId);

}
