package com.baidu.bce.logic.bci.dao.pod.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.bci.dao.common.model.Label;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PodPO implements Cloneable, PodPOForBcm {

    private String name = "";
    private String podId = "";
    private String podUuid = "";
    private String status = "";
    @JsonProperty(value = "vCpu")
    private float vCpu = 0;
    private float memory = 0;
    private String eipUuid = "";
    private String publicIp = "";
    private int bandwidthInMbps = 0;
    private String cceUuid = "";
    private String internalIp = "";
    private String securityGroupUuid;
    private String restartPolicy = "";
    @JsonIgnore
    private String tags = "";    // 存储label
    private String orderId = "";
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    private String description = "";
    private String region = "";
    private String userId = "";
    private String resourceUuid = "";
    private String taskStatus = "";
    @JsonIgnore
    private String nfs = "";
    @JsonIgnore
    private String emptyDir = "";
    @JsonIgnore
    private String configFile = "";
    @JsonIgnore
    private String podVolumes = "";
    @JsonIgnore
    private int deleted = 0;
    @JsonIgnore
    private int enableLog = 0;
    @JsonProperty(value = "tags")
    private List<Tag> podTags;   // tag服务的标签
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String subnetUuid;
    private String zoneId = "";
    private String logicalZone = "";
    private String subnetType = "";
    private String eipGroupId = "";
    private List<Label> labels;

    /**
     * 业务
     */
    private String application;

    private boolean pushLog = false;

    private String chargeSource; // charge source ， 计费源, 普通模式值为user，统一计费为各服务名

    public float getvCpu() {
        return vCpu;
    }

    public void setvCpu(float vCpu) {
        this.vCpu = vCpu;
    }
}